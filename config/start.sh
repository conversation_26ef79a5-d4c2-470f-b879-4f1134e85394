#!/bin/bash
set -x
pushd /home/<USER>/
jar_name="start.jar"
function parase_json {
  if [[ "$PROJECT_NAME" == "default" ]]; then
    return
  fi
  result=$(cat build.json | jq '.apps[] | select(.name_cn == "'$PROJECT_NAME'")')
  if [[ -n "$result" ]]; then
    jar_name="$(cat build.json | jq -rc '.apps[] | select(.name_cn == "'$PROJECT_NAME'") | .name ')".jar
  else
    return
  fi
  if $(cat build.json | jq '.apps[] | select(.name_cn == "'$PROJECT_NAME'") | .opt.env.JAVA_OPTIONS != ""'); then
    export JAVA_OPTIONS=""$JAVA_OPTIONS" $(cat build.json | jq -r '.apps[] | select(.name == "'$PROJECT_NAME'") | .opt.env.JAVA_OPTIONS')"
  fi
}
if [ -n "$PROJECT_NAME" ]; then
  parase_json
else
  jar_name="start.jar"
fi
#exec java -cp "$jar_name" -Dloader.path=/home/<USER>/lib/ -DJM.LOG.PATH=/tmp -DJM.SNAPSHOT.PATH=/tmp -Duser.home=/tmp -Dspring.profiles.active=prod org.springframework.boot.loader.JarLauncher
exec java -cp "$jar_name" -DlibPath=/home/<USER>/lib/ -Dcrypto.ttl.workdir=/tmp/ttlAudit -DJM.LOG.PATH=/tmp -DJM.SNAPSHOT.PATH=/tmp -Duser.home=/tmp -Dspring.profiles.active=prod org.springframework.boot.loader.JarLauncher
