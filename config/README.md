## 配置结构

```text
{
  # 打包的时候只会打包打包“default-apps”配置的模块
  "default-apps": [
    "zs"
  ],
  "apps": [
    {
      # default模块为标准产品模块
      "name": "default",
      "description": "标准产品",
      "version": "1.0.0",
      "module": "web",
      # 没有配置opt时，则会使用全局的opt的配置
    },
    {
      # zs模块为定开示例模块
      "name": "zs",
      "description": "浙商银行",
      "version": "1.0.0-{git-xxx}",
      "module": "apps/app-zs",
      # 局部opt配置，这里的配置会覆盖全局中的opt配置
      "opt": {
        "env": {
          "JAVA_OPTS": "-Dlog4j.configurationFile=/tmp/conf/log4j2.xml"
        }
      }
    }
  ],
	# 全局的opt配置
  "opt": {
    "build": "mvn -U clean package -Pprod -Dmaven.test.skip=true -Dverbose -Dincludes=log4j:log4j ",
    "env": {
      "JAVA_OPTS": "-Dlog4j.configurationFile=conf/log4j2.xml"
    }
  }
}
```

## 内置环境变量

| 变量名称               | 示例值                    | 描述              |
|--------------------| ------------------------- | ----------------- |
| GIT_COMMIT_MESSAGE | 用户信息变更              | git的commit信息   |
| GIT_COMMIT         | 18ff97101e675ff863ae129fd | git的commitId信息 |
| GIT_TAG            | 1.2.3                     | git的tag信息      |
| giteeSourceBranch  | paradigm-api-dev          | git的分支信息     |
| giteeUserName      | wjz                       | git的作者信息     |
| giteeUserEmail     | <EMAIL>               | git的邮箱信息     |
| GIT_DATE           | 20240226190221            | git的提交时间     |
| BUILD_TIMESTAMP    | 1708945366                | build的时间戳     |
| BUILD_VERSION      | 1.0.0                     | build的版本号     |
| BUILD_DATE         | 2024-02-26                | build的日期       |
| BUILD_TIME         | 10:35:21                  | build的时间       |
| BUILD_HOST         | ************              | build的主机名     |
| BUILD_OS           | Linux                     | build的操作系统   |

