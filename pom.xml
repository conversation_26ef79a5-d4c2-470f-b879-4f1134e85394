<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <groupId>com.quanzhi.auditapiv2</groupId>
    <artifactId>auditapiv2</artifactId>
    <version>3.3.0-SNAPSHOT</version>
    <packaging>pom</packaging>

    <name>auditapiv2</name>
    <description>auditapiv2</description>

    <parent>
        <groupId>com.quanzhi</groupId>
        <artifactId>audit-parent</artifactId>
        <version>api-3.3.0-SNAPSHOT</version>
        <relativePath/>
    </parent>

    <properties>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
        <java.version>1.8</java.version>
        <mybatis-plus.version>3.4.1</mybatis-plus.version>
        <clickhouse.version>0.2.4</clickhouse.version>
        <druid.version>1.2.4</druid.version>
        <powermock.version>2.0.7</powermock.version>
        <audit.mix.version>3.1.0-SNAPSHOT</audit.mix.version>
    </properties>

    <dependencies>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-data-redis</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-aop</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>slf4j-log4j12</artifactId>
                    <groupId>org.slf4j</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.alibaba.boot</groupId>
            <artifactId>nacos-config-spring-boot-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.data</groupId>
            <artifactId>spring-data-mongodb</artifactId>
        </dependency>
        <dependency>
            <groupId>org.mongodb</groupId>
            <artifactId>mongodb-driver-sync</artifactId>
            <version>4.6.1</version>
        </dependency>

        <!--        <dependency>-->
        <!--            <groupId>javax.servlet</groupId>-->
        <!--            <artifactId>javax.servlet-api</artifactId>-->
        <!--            &lt;!&ndash;<scope>provided</scope>&ndash;&gt;-->
        <!--        </dependency>-->

        <!--        <dependency>-->
        <!--            <groupId>org.apache.dubbo</groupId>-->
        <!--            <artifactId>dubbo</artifactId>-->
        <!--        </dependency>-->

        <!--        <dependency>-->
        <!--            <groupId>org.apache.dubbo</groupId>-->
        <!--            <artifactId>dubbo-registry-nacos</artifactId>-->
        <!--            <exclusions>-->
        <!--                <exclusion>-->
        <!--                    <artifactId>simpleclient</artifactId>-->
        <!--                    <groupId>io.prometheus</groupId>-->
        <!--                </exclusion>-->
        <!--                <exclusion>-->
        <!--                    <groupId>log4j</groupId>-->
        <!--                    <artifactId>log4j</artifactId>-->
        <!--                </exclusion>-->
        <!--            </exclusions>-->
        <!--        </dependency>-->
        <!--        <dependency>-->
        <!--            <groupId>org.apache.dubbo</groupId>-->
        <!--            <artifactId>dubbo</artifactId>-->
        <!--        </dependency>-->

        <!--        <dependency>-->
        <!--            <groupId>org.apache.dubbo</groupId>-->
        <!--            <artifactId>dubbo-registry-nacos</artifactId>-->
        <!--            <exclusions>-->
        <!--                <exclusion>-->
        <!--                    <artifactId>simpleclient</artifactId>-->
        <!--                    <groupId>io.prometheus</groupId>-->
        <!--                </exclusion>-->
        <!--                <exclusion>-->
        <!--                    <groupId>log4j</groupId>-->
        <!--                    <artifactId>log4j</artifactId>-->
        <!--                </exclusion>-->
        <!--            </exclusions>-->
        <!--        </dependency>-->
        <!--        <dependency>-->
        <!--            <groupId>redis.clients</groupId>-->
        <!--            <artifactId>jedis</artifactId>-->
        <!--        </dependency>-->

        <dependency>
            <groupId>com.quanzhi</groupId>
            <artifactId>monitor-client</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>slf4j-log4j12</artifactId>
                    <groupId>org.slf4j</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>simpleclient</artifactId>
                    <groupId>io.prometheus</groupId>
                </exclusion>
                <exclusion>
                    <groupId>log4j</groupId>
                    <artifactId>log4j</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.quanzhi.plugincore-api</groupId>
            <artifactId>plugincore-core-service-auditapiv2</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>slf4j-log4j12</artifactId>
                    <groupId>org.slf4j</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.quanzhi</groupId>
            <artifactId>common</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>slf4j-log4j12</artifactId>
                    <groupId>org.slf4j</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>fastjson</artifactId>
                    <groupId>com.alibaba</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>commons-logging</artifactId>
                    <groupId>commons-logging</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>commons-lang</artifactId>
                    <groupId>commons-lang</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>commons-collections</artifactId>
                    <groupId>commons-collections</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>commons-beanutils</artifactId>
                    <groupId>commons-beanutils</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>spring-context-support</artifactId>
                    <groupId>com.alibaba.spring</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>nacos-client</artifactId>
                    <groupId>com.alibaba.nacos</groupId>
                </exclusion>
                <exclusion>
                    <groupId>org.mongodb</groupId>
                    <artifactId>mongo-java-driver</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>log4j</groupId>
                    <artifactId>log4j</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>commons-codec</groupId>
            <artifactId>commons-codec</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>log4j</groupId>
                    <artifactId>log4j</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>org.apache.shiro</groupId>
            <artifactId>shiro-spring</artifactId>
        </dependency>

        <dependency>
            <groupId>org.hibernate</groupId>
            <artifactId>hibernate-validator</artifactId>
        </dependency>

        <dependency>
            <groupId>commons-beanutils</groupId>
            <artifactId>commons-beanutils</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework.data</groupId>
            <artifactId>spring-data-commons</artifactId>
        </dependency>

        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-lang3</artifactId>
        </dependency>

        <dependency>
            <groupId>commons-io</groupId>
            <artifactId>commons-io</artifactId>
        </dependency>

        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>fastjson</artifactId>
        </dependency>

        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <optional>true</optional>
        </dependency>

        <dependency>
            <groupId>org.aspectj</groupId>
            <artifactId>aspectjrt</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
            <exclusions>
                <exclusion>
                    <groupId>org.junit.vintage</groupId>
                    <artifactId>junit-vintage-engine</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.quanzhi</groupId>
            <artifactId>audit-extract</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>commons-codec</artifactId>
                    <groupId>commons-codec</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>commons-io</artifactId>
                    <groupId>commons-io</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>guava</artifactId>
                    <groupId>com.google.guava</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>slf4j-log4j12</artifactId>
                    <groupId>org.slf4j</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>common</artifactId>
                    <groupId>com.quanzhi</groupId>
                </exclusion>
                <exclusion>
                    <groupId>com.quanzhi.metabase</groupId>
                    <artifactId>metabase-common-client</artifactId>
                </exclusion>
                <exclusion>
                    <artifactId>hyperscan</artifactId>
                    <groupId>com.quanzhi.hyperscan</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.quanzhi.dlp4j</groupId>
            <artifactId>dlp4j-core</artifactId>
        </dependency>

        <dependency>
            <groupId>com.quanzhi.metabase</groupId>
            <artifactId>metabase-common-client</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>fastjson</artifactId>
                    <groupId>com.alibaba</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>log4j</artifactId>
                    <groupId>log4j</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>commons-io</artifactId>
                    <groupId>commons-io</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>commons-codec</artifactId>
                    <groupId>commons-codec</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>guava</artifactId>
                    <groupId>com.google.guava</groupId>
                </exclusion>
                <exclusion>
                    <groupId>org.slf4j</groupId>
                    <artifactId>slf4j-log4j12</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>org.mapstruct</groupId>
            <artifactId>mapstruct</artifactId>
        </dependency>

        <dependency>
            <groupId>org.mapstruct</groupId>
            <artifactId>mapstruct-processor</artifactId>
        </dependency>

        <dependency>
            <groupId>org.jsoup</groupId>
            <artifactId>jsoup</artifactId>
        </dependency>

        <dependency>
            <groupId>cn.hutool</groupId>
            <artifactId>hutool-all</artifactId>
            <version>5.7.17</version>
            <exclusions>
                <exclusion>
                    <groupId>org.mongodb</groupId>
                    <artifactId>mongo-java-driver</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>io.prometheus</groupId>
            <artifactId>simpleclient_pushgateway</artifactId>
        </dependency>

        <dependency>
            <groupId>org.docx4j</groupId>
            <artifactId>docx4j</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>slf4j-log4j12</artifactId>
                    <groupId>org.slf4j</groupId>
                </exclusion>
                <exclusion>
                    <groupId>log4j</groupId>
                    <artifactId>log4j</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.apache.commons</groupId>
                    <artifactId>commons-compress</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>org.docx4j</groupId>
            <artifactId>docx4j-export-fo</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>org.apache.commons</groupId>
                    <artifactId>commons-compress</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.apache.pdfbox</groupId>
                    <artifactId>fontbox</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.quanzhi.operate</groupId>
            <artifactId>auditapiv2-operate</artifactId>
            <version>app-3.3.0-SNAPSHOT</version>
            <exclusions>
                <exclusion>
                    <groupId>org.apache.commons</groupId>
                    <artifactId>commons-text</artifactId>
                </exclusion>
                <exclusion>
                    <artifactId>log4j</artifactId>
                    <groupId>log4j</groupId>
                </exclusion>
                <exclusion>
                    <groupId>org.mongodb</groupId>
                    <artifactId>mongo-java-driver</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.mongodb</groupId>
                    <artifactId>mongodb-driver</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.springframework.data</groupId>
                    <artifactId>spring-data-mongodb</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.quanzhi.datataskcore</groupId>
            <artifactId>datataskcore-common-service-facade</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>spring-data-mongodb</artifactId>
                    <groupId>org.springframework.data</groupId>
                </exclusion>
                <exclusion>
                    <groupId>redis.clients</groupId>
                    <artifactId>jedis</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.quanzhi</groupId>
            <artifactId>encrypt-sdk</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>slf4j-log4j12</artifactId>
                    <groupId>org.slf4j</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.quanzhi</groupId>
            <artifactId>resource-fetcher-client</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>common</artifactId>
                    <groupId>com.quanzhi</groupId>
                </exclusion>
                <exclusion>
                    <groupId>org.mongodb</groupId>
                    <artifactId>mongo-java-driver</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.mongodb</groupId>
                    <artifactId>mongodb-driver</artifactId>
                </exclusion>
                <exclusion>
                    <artifactId>log4j</artifactId>
                    <groupId>log4j</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>slf4j-log4j12</artifactId>
                    <groupId>org.slf4j</groupId>
                </exclusion>
                <exclusion>
                    <groupId>com.quanzhi.metabase</groupId>
                    <artifactId>metabase-common-client</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.springframework.data</groupId>
                    <artifactId>spring-data-mongodb</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.quanzhi.audit.mix</groupId>
            <artifactId>audit-config-template</artifactId>
            <version>${audit.mix.version}</version>
        </dependency>
        <dependency>
            <groupId>com.quanzhi.uias</groupId>
            <artifactId>base-sdk-util</artifactId>
            <version>2.0.0-SNAPSHOT</version>
        </dependency>
    </dependencies>

    <dependencyManagement>
        <dependencies>
            <!-- sub projects -->
            <dependency>
                <groupId>com.quanzhi.auditapiv2</groupId>
                <artifactId>auditapiv2-core-model</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.quanzhi.auditapiv2</groupId>
                <artifactId>auditapiv2-core-trace</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.quanzhi.auditapiv2</groupId>
                <artifactId>auditapiv2-core-risk</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.quanzhi.auditapiv2</groupId>
                <artifactId>auditapiv2-common-risk</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.quanzhi.auditapiv2</groupId>
                <artifactId>auditapiv2-biz-schedule</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.quanzhi.auditapiv2</groupId>
                <artifactId>auditapiv2-biz-risk</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.quanzhi.auditapiv2</groupId>
                <artifactId>auditapiv2-common-service-integration</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.quanzhi.auditapiv2</groupId>
                <artifactId>auditapiv2-common-dal</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.quanzhi.auditapiv2</groupId>
                <artifactId>auditapiv2-common-util</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.quanzhi.auditapiv2</groupId>
                <artifactId>auditapiv2-common-monitor</artifactId>
                <version>${project.version}</version>
            </dependency>

            <dependency>
                <groupId>com.quanzhi.auditapiv2</groupId>
                <artifactId>auditapiv2-test</artifactId>
                <version>${project.version}</version>
            </dependency>

            <dependency>
                <groupId>com.quanzhi.auditapiv2</groupId>
                <artifactId>auditapiv2-report-manage</artifactId>
                <version>${project.version}</version>
            </dependency>

            <dependency>
                <groupId>com.quanzhi.auditapiv2</groupId>
                <artifactId>auditapiv2-encrypt-decrypt</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>ru.yandex.clickhouse</groupId>
                <artifactId>clickhouse-jdbc</artifactId>
                <version>${clickhouse.version}</version>
            </dependency>
            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>druid</artifactId>
                <version>${druid.version}</version>
            </dependency>
            <dependency>
                <groupId>org.powermock</groupId>
                <artifactId>powermock-module-junit4</artifactId>
                <version>${powermock.version}</version>
                <scope>test</scope>
            </dependency>
            <dependency>
                <groupId>org.powermock</groupId>
                <artifactId>powermock-api-mockito2</artifactId>
                <version>${powermock.version}</version>
                <scope>test</scope>
            </dependency>
            <dependency>
                <groupId>junit</groupId>
                <artifactId>junit</artifactId>
                <scope>test</scope>
                <version>4.13.1</version>
            </dependency>
            <dependency>
                <groupId>commons-fileupload</groupId>
                <artifactId>commons-fileupload</artifactId>
                <version>1.5</version>
            </dependency>
            <dependency>
                <groupId>com.quanzhi.auditapiv2</groupId>
                <artifactId>auditapiv2-open-api</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.quanzhi.auditapiv2</groupId>
                <artifactId>auditapiv2-report-api</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.quanzhi.auditapiv2</groupId>
                <artifactId>auditapiv2-common-report</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.quanzhi.auditapiv2</groupId>
                <artifactId>one-api</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>org.codehaus.janino</groupId>
                <artifactId>janino</artifactId>
                <version>3.1.6</version>
            </dependency>
            <!-- https://mvnrepository.com/artifact/org.codehaus.janino/commons-compiler -->
            <dependency>
                <groupId>org.codehaus.janino</groupId>
                <artifactId>commons-compiler</artifactId>
                <version>3.1.6</version>
            </dependency>
            <!-- https://mvnrepository.com/artifact/org.codehaus.groovy/groovy -->
            <dependency>
                <groupId>org.codehaus.groovy</groupId>
                <artifactId>groovy</artifactId>
                <version>3.0.9</version>
            </dependency>
            <dependency>
                <groupId>com.quanzhi.auditapiv2</groupId>
                <artifactId>auditapiv2-open-api-sdk</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.quanzhi.audit.mix</groupId>
                <artifactId>audit-mix-log</artifactId>
                <version>${audit.mix.version}</version>
            </dependency>
            <dependency>
                <groupId>com.quanzhi.audit.mix</groupId>
                <artifactId>audit-mix-authorization</artifactId>
                <version>${audit.mix.version}</version>
            </dependency>
            <dependency>
                <groupId>com.quanzhi.audit.mix</groupId>
                <artifactId>audit-mix-upload</artifactId>
                <version>${audit.mix.version}</version>
            </dependency>
            <dependency>
                <groupId>com.quanzhi.auditapiv2</groupId>
                <artifactId>auditapiv2-core-service</artifactId>
                <version>${project.version}</version>
            </dependency>

            <dependency>
                <groupId>com.quanzhi.audit.mix</groupId>
                <artifactId>audit-mix-plugin</artifactId>
                <version>3.3.0-SNAPSHOT</version>
            </dependency>

            <dependency>
                <groupId>com.quanzhi.audit.mix</groupId>
                <artifactId>audit-mix-match</artifactId>
                <version>${audit.mix.version}</version>
            </dependency>
            <dependency>
                <groupId>com.belerweb</groupId>
                <artifactId>pinyin4j</artifactId>
                <version>2.5.1</version>
            </dependency>
            <dependency>
                <groupId>org.apache.tika</groupId>
                <artifactId>tika-parsers</artifactId>
            </dependency>
            <dependency>
                <groupId>com.quanzhi.audit.mix</groupId>
                <artifactId>audit-mix-schedule</artifactId>
                <version>3.3.0-SNAPSHOT</version>
            </dependency>

            <dependency>
                <groupId>com.auth0</groupId>
                <artifactId>java-jwt</artifactId>
                <version>4.0.0</version>
            </dependency>
            <dependency>
                <groupId>com.fulmicoton</groupId>
                <artifactId>multiregexp</artifactId>
                <version>0.3</version>
            </dependency>
            <dependency>
                <groupId>dk.brics.automaton</groupId>
                <artifactId>automaton</artifactId>
                <version>1.11-8</version>
            </dependency>
            <dependency>
                <groupId>org.json</groupId>
                <artifactId>org.json</artifactId>
                <version>chargebee-1.0</version>
            </dependency>
            <dependency>
                <groupId>com.quanzhi</groupId>
                <artifactId>plugin-api</artifactId>
                <version>1.0.0-SNAPSHOT</version>
            </dependency>
            <dependency>
                <groupId>org.jeasy</groupId>
                <artifactId>easy-random-core</artifactId>
                <version>4.3.0</version>
                <scope>test</scope>
            </dependency>
            <dependency>
                <groupId>com.quanzhi.audit.mix</groupId>
                <artifactId>audit-hotswap</artifactId>
                <version>${audit.mix.version}</version>
            </dependency>
            <dependency>
                <groupId>com.github.xiaoymin</groupId>
                <artifactId>knife4j-spring-boot-starter</artifactId>
                <version>2.0.9</version>
            </dependency>
            <dependency>
                <groupId>com.quanzhi</groupId>
                <artifactId>awdb-core</artifactId>
                <version>0.0.5-SNAPSHOT</version>
            </dependency>
            <dependency>
                <groupId>com.quanzhi.audit.mix</groupId>
                <artifactId>audit-mix-tracer</artifactId>
                <version>${mix.version}</version>
            </dependency>
            <dependency>
                <groupId>com.quanzhi.audit.mix</groupId>
                <artifactId>audit-saferegex</artifactId>
                <version>${mix.version}</version>
            </dependency>
            <dependency>
                <groupId>org.openjdk.jmh</groupId>
                <artifactId>jmh-core</artifactId>
                <version>1.35</version>
            </dependency>
            <dependency>
                <groupId>org.openjdk.jmh</groupId>
                <artifactId>jmh-generator-annprocess</artifactId>
                <version>1.35</version>
            </dependency>
            <dependency>
                <groupId>com.quanzhi.auditapiv2</groupId>
                <artifactId>auditapiv2-web</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.quanzhi.login</groupId>
                <artifactId>login</artifactId>
                <version>0.0.3-SNAPSHOT</version>
            </dependency>
            <dependency>
                <groupId>com.quanzhi.mix</groupId>
                <artifactId>audit-api-trace</artifactId>
                <version>3.3.0-SNAPSHOT</version>
            </dependency>
            <dependency>
                <groupId>com.quanzhi.audit.mix</groupId>
                <artifactId>audit-magic-api</artifactId>
                <version>3.3.0-SNAPSHOT</version>
            </dependency>
            <dependency>
                <groupId>com.quanzhi.audit.mix</groupId>
                <artifactId>audit-permission</artifactId>
                <version>3.3.0-SNAPSHOT</version>
            </dependency>

            <dependency>
                <groupId>com.java3y.austin</groupId>
                <artifactId>austin-service-api</artifactId>
                <version>0.0.1-SNAPSHOT</version>
            </dependency>
        </dependencies>
    </dependencyManagement>


    <modules>
        <module>core/service</module>
        <module>core/model</module>
        <module>web</module>
        <module>test</module>
        <module>common/util</module>
        <module>common/service/integration</module>
        <module>common/dal</module>
        <module>biz/schedule</module>
        <module>common/monitor</module>
        <module>common/risk</module>
        <module>core/risk</module>
        <module>biz/risk</module>
        <module>common/open-api</module>
        <module>common/one-api</module>
        <module>common/report</module>
        <module>common/open-api-sdk</module>
        <module>core/event-trace</module>
        <module>core/encrypt-decrypt</module>
        <module>core/report</module>
        <module>common/report-api</module>
        <module>apps/app-dfhk</module>
        <module>apps/app-tyy</module>
        <module>apps/app-start</module>
        <module>apps/app-zryph</module>
        <module>apps/app-hnlt</module>
    </modules>

    <profiles>
        <profile>
            <!-- 本地开发环境 -->
            <id>dev</id>
            <properties>
                <env>dev</env>
            </properties>
            <activation>
                <!-- 设置默认激活这个配置 -->
                <activeByDefault>true</activeByDefault>
            </activation>
        </profile>
        <profile>
            <id>prod</id>
            <properties>
                <env>prod</env>
            </properties>
            <build>
                <plugins>
                    <plugin>
                        <artifactId>maven-jar-plugin</artifactId>
                        <configuration>
                            <archive>
                                <manifest>
                                    <addClasspath>true</addClasspath>
                                    <classpathPrefix>lib/</classpathPrefix>
                                    <useUniqueVersions>false</useUniqueVersions>
                                </manifest>
                            </archive>
                        </configuration>
                    </plugin>
                    <plugin>
                        <artifactId>maven-dependency-plugin</artifactId>
                        <executions>
                            <execution>
                                <id>copy</id>
                                <phase>package</phase>
                                <goals>
                                    <goal>copy-dependencies</goal>
                                </goals>
                                <configuration>
                                    <outputDirectory>${project.build.directory}/lib</outputDirectory>
                                    <excludeTransitive>false</excludeTransitive>
                                    <stripVersion>false</stripVersion>
                                    <includeScope>compile</includeScope>
                                </configuration>
                            </execution>
                        </executions>
                    </plugin>
<!--                    <plugin>-->
<!--                        <groupId>org.apache.maven.plugins</groupId>-->
<!--                        <artifactId>maven-compiler-plugin</artifactId>-->
<!--                        <version>3.8.1</version>-->
<!--                        <configuration>-->
<!--                            <source>1.8</source>-->
<!--                            <target>1.8</target>-->
<!--                            <compilerArgs>-->
<!--                                <arg>-parameters</arg>-->
<!--                            </compilerArgs>-->
<!--                        </configuration>-->
<!--                    </plugin>-->
                </plugins>
            </build>
        </profile>
        <profile>
            <id>deploy</id>
            <properties>
                <env>prod</env>
            </properties>
        </profile>
    </profiles>

    <repositories>
        <repository>
            <id>quanzhi</id>
            <url>http://192.168.0.92:8081/nexus/content/groups/public/</url>
        </repository>
    </repositories>

</project>
