package com.quanzhi.auditapiv2.ipTest;

import com.quanzhi.auditapiv2.common.util.utils.ServiceException;
import com.quanzhi.awdb_core.util.IpUtil;
import org.junit.Ignore;
import org.junit.Test;
import org.springframework.boot.test.context.SpringBootTest;

import java.util.ArrayList;
import java.util.List;

/**
 * @author: yangzixian
 * @date: 2023.09.26 10:46
 * @description:
 */
@SpringBootTest
@Ignore
public class testSave {

    private final String ipv6 = "^(?:(?:[a-fA-F0-9]{1,4}:){6}|::(?:[a-fA-F0-9]{1,4}:){5}|(?:[a-fA-F0-9]{1,4})?::(?:[a-fA-F0-9]{1,4}:){4}|(?:(?:[a-fA-F0-9]{1,4}:){0,1}[a-fA-F0-9]{1,4})?::(?:[a-fA-F0-9]{1,4}:){3}|(?:(?:[a-fA-F0-9]{1,4}:){0,2}[a-fA-F0-9]{1,4})?::(?:[a-fA-F0-9]{1,4}:){2}|(?:(?:[a-fA-F0-9]{1,4}:){0,3}[a-fA-F0-9]{1,4})?::[a-fA-F0-9]{1,4}:(?:[a-fA-F0-9]{1,4}:){1}|(?:(?:[a-fA-F0-9]{1,4}:){0,4}[a-fA-F0-9]{1,4})?::[a-fA-F0-9]{1,4}|(?:(?:[a-fA-F0-9]{1,4}:){0,5}[a-fA-F0-9]{1,4})?::[a-fA-F0-9]{1,4}|(?:(?:[a-fA-F0-9]{1,4}:){0,6}[a-fA-F0-9]{1,4})?::)$";

    private final String ipv4 = "(?:(?:1[0-9][0-9]\\.)|(?:2[0-4][0-9]\\.)|(?:25[0-5]\\.)|(?:[1-9][0-9]\\.)|(?:[0-9]\\.)){3}(?:(?:1[0-9][0-9])|(?:2[0-4][0-9])|(?:25[0-5])|(?:[1-9][0-9])|(?:[0-9]))";

    @Test
    public void testSave() {
        List<String> ipArraies = new ArrayList<>();
        ipArraies.add("*******-*******");
        ipArraies.add("*******/16-*******");
        ipArraies.add("*******-*******/16");
        ipArraies.add("1.1.1.*-2.2.2.*");
        ipArraies.add("1.1.1.*/16-2.2.2.*");
        ipArraies.add("1.1.1.*-2.2.2.*/16");
        ipArraies.add("2a4c:d3e7::8a9d:7cff:feb5:87ed:5ee9/32");
        ipArraies.add("2a4c:d3e7:5e1e:8a9d:7cff:feb5:87ed:5ee9-2a4c:d3e7:5e1e::7cff:feb5:87ed:5ee7");
        for (String ips : ipArraies) {
            String realIps = ips.replaceAll("\\*", "0");
            //ip段只接受ip-ip的格式
            if (realIps.contains("-")) {
                String[] ipArray = realIps.split("-");
                for (String ip : ipArray) {
                    //正则校验ip是否合法
                    if (!checkIp(ip)) {
                        System.out.println(ips + "   false1");
                    }
                }
            }

            //校验ipv6不能使用/64以外的子网掩码
            if (realIps.contains("/") && !realIps.contains("-")) {
                String[] ipArray = realIps.split("/");
                if (ipArray.length >= 2) {
                    String ip = ipArray[0];
                    String mask = ipArray[1];
                    if (IpUtil.isIPV6(ip) && Integer.valueOf(mask) != 64) {
                        System.out.println(ips + "   false2");
                    }
                } else {
                    System.out.println(ips + "   false3");
                }
            }
        }
    }

    private boolean checkIp(String ip) {
        if (ip.contains("*")) {
            ip = ip.replaceAll("\\*", "0");
        }
        if (ip.contains("/")) {
            ip = ip.substring(0, ip.indexOf("/"));
        }
        return IpUtil.isIPV6_IPV4(ip);
    }

}
