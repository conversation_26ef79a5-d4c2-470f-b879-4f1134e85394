package com.quanzhi.auditapiv2.biz;

import cn.hutool.core.date.DateField;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import com.quanzhi.auditapiv2.biz.risk.dto.RiskCountDto;
import com.quanzhi.auditapiv2.core.risk.dto.search.RiskSearchDto;
import org.apache.commons.lang.StringEscapeUtils;
import org.junit.Ignore;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.junit.runners.JUnit4;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * create at 2022/9/09 10:13 上午
 * @description:
 **/
@RunWith(JUnit4.class)
@Ignore
public class MainTest {

    /**
     * 测试去除转义
     */
    @Test
    public void testUnescapeHtml() {
        //初始化
        List<RiskCountDto> resultList = initTrendList(7);

        for (int i = 0; i < resultList.size(); i++) {
            String startDate = resultList.get(i).getDate();
            long startTime = com.quanzhi.auditapiv2.common.util.utils.DateUtil.getDateZeroTimestamp(com.quanzhi.auditapiv2.common.util.utils.DateUtil.stringToDate(startDate));
            long endTime = com.quanzhi.auditapiv2.common.util.utils.DateUtil.getDateLastTimestamp(com.quanzhi.auditapiv2.common.util.utils.DateUtil.stringToDate(startDate));
            RiskSearchDto riskSearchDto = new RiskSearchDto();
        }
    }


    private List<RiskCountDto> initTrendList(int beforeDay) {
        List<RiskCountDto> list = new ArrayList<>();
        DateTime startDate = DateUtil.offsetDay(new Date(), 0 - beforeDay);
        List<DateTime> dateTimes = DateUtil.rangeToList(startDate, new Date(), DateField.DAY_OF_WEEK);
        for (DateTime dateTime : dateTimes) {
            RiskCountDto riskCountDto = RiskCountDto.builder().date(DateUtil.date(dateTime).toString("yyyyMMdd")).highRiskNum(0L).midRiskNum(0L).lowRiskNum(0L).build();
            list.add(riskCountDto);
        }
        return list;
    }


}