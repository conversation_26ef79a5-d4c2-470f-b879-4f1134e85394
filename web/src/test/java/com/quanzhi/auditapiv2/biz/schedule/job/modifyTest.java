package com.quanzhi.auditapiv2.biz.schedule.job;

import com.quanzhi.audit.mix.schdule.infrastructure.spring.SpringScheduledTaskRegistrar;
import com.quanzhi.auditapiv2.core.trace.util.StringUtils;
import org.junit.Ignore;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.scheduling.TaskScheduler;
import org.springframework.scheduling.annotation.ScheduledAnnotationBeanPostProcessor;
import org.springframework.scheduling.annotation.SchedulingConfiguration;
import org.springframework.scheduling.config.CronTask;
import org.springframework.scheduling.config.ScheduledTask;
import org.springframework.scheduling.config.ScheduledTaskRegistrar;
import org.springframework.scheduling.support.CronTrigger;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;
import org.springframework.test.context.web.WebAppConfiguration;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.ScheduledFuture;

/**
 * @author: yangzixian
 * @date: 2023.09.06 17:55
 * @description:
 */
@RunWith(SpringJUnit4ClassRunner.class)
@WebAppConfiguration
@SpringBootTest
@ActiveProfiles("local")
@Ignore
public class modifyTest {

    @Autowired
    private TaskScheduler taskScheduler;

    @Autowired
    private SchedulingConfiguration postProcessor;

    @Test
    public void testModifyCron() throws InterruptedException {
        Set<ScheduledTask> scheduledTasks = postProcessor.scheduledAnnotationProcessor().getScheduledTasks();
        Map<String, Runnable> scheduleMap = new HashMap<>(scheduledTasks.size());
        for (ScheduledTask scheduledTask : scheduledTasks) {
            scheduleMap.put(scheduledTask.getTask().toString(), scheduledTask.getTask().getRunnable());
        }

        String executor = "TestJob";

        Map<String, ScheduledFuture<?>> scheduledManagerMap = new HashMap<>();
        for (String key : scheduleMap.keySet()) {
            System.out.println(key);
            String[] jobs = key.toUpperCase().split("\\.");
            System.out.println(jobs[jobs.length - 2]);
            if (key.toUpperCase().contains(executor.toUpperCase())) {
                ScheduledFuture<?> schedule = taskScheduler.schedule(scheduleMap.get(key), new CronTrigger("0 * * * * ?"));
                scheduledManagerMap.put(key, schedule);
            }
        }

        Thread.sleep(10000);

        for (String key : scheduledManagerMap.keySet()) {
            if (key.toUpperCase().contains(executor.toUpperCase())) {
                scheduledManagerMap.get(key).cancel(true);
                scheduledManagerMap.remove(key);
            }
        }
    }

}
