package com.quanzhi.auditapiv2.biz.schedule.job;

import com.alibaba.fastjson.JSON;
import org.apache.http.HttpEntity;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.conn.ssl.SSLConnectionSocketFactory;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;
import org.junit.Ignore;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;
import org.springframework.test.context.web.WebAppConfiguration;

import javax.net.ssl.SSLContext;
import javax.net.ssl.TrustManager;
import javax.net.ssl.X509TrustManager;
import java.security.KeyManagementException;
import java.security.NoSuchAlgorithmException;
import java.security.SecureRandom;
import java.security.cert.CertificateException;
import java.security.cert.X509Certificate;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2021/9/22 10:41 上午
 */
@RunWith(SpringJUnit4ClassRunner.class)
@SpringBootTest
@WebAppConfiguration
@ActiveProfiles("dev")
@Ignore
public class SysUserJobTest {

    @Test
    public void testJob() {

        try {

            CloseableHttpResponse response = null;

            //连接http接口
            CloseableHttpClient httpClient = createHttpClientTLS();
            HttpGet httpGet = new HttpGet("https://esrc.esgcc.com.cn/api/user/getApiUserList");
            //设置header信息
            //指定报文头【Content-type】、【User-Agent】
            httpGet.setHeader("content-type", "application/x-www-form-urlencoded; charset=utf-8");
            httpGet.setHeader("user-agent", "Mozilla/4.0 (compatible; MSIE 5.0; Windows NT; DigExt)");
            httpGet.setHeader("Referer", "https://esrc.esgcc.com.cn");
            //获取返回信息
            response = httpClient.execute(httpGet);
            HttpEntity entity = response.getEntity();
            String json = EntityUtils.toString(entity);

            Map<String, String> map = (Map<String, String>) JSON.parse(json);
            for (String key : map.keySet()) {

                System.out.println(key + ":" + map.get(key));
            }

        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    private static CloseableHttpClient createHttpClientTLS() throws NoSuchAlgorithmException, KeyManagementException {
        CloseableHttpClient httpClient = null;
        TrustManager[] trustManagers = trustAllHttpsCertificates();
        SSLContext sslContext = SSLContext.getInstance(SSLConnectionSocketFactory.TLS);
        sslContext.init(null, trustManagers, new SecureRandom());
        SSLConnectionSocketFactory sslsf = new SSLConnectionSocketFactory(
                sslContext,
                new String[]{"TLSv1.2"},
                null,
                SSLConnectionSocketFactory.BROWSER_COMPATIBLE_HOSTNAME_VERIFIER);
        httpClient = HttpClients.custom().setSSLSocketFactory(sslsf).build();
        return httpClient;
    }

    private static TrustManager[] trustAllHttpsCertificates() throws KeyManagementException {
        TrustManager[] trustManagers = new TrustManager[]{new X509TrustManager() {
            @Override
            public void checkClientTrusted(X509Certificate[] x509Certificates, String s) throws CertificateException {
            }

            @Override
            public void checkServerTrusted(X509Certificate[] x509Certificates, String s) throws CertificateException {
            }

            @Override
            public X509Certificate[] getAcceptedIssuers() {
                return new X509Certificate[]{};
            }
        }};
        return trustManagers;
    }
}
