package com.quanzhi.auditapiv2.biz.schedule.job;

import org.junit.Ignore;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;
import org.springframework.test.context.web.WebAppConfiguration;

@RunWith(SpringJUnit4ClassRunner.class)
@SpringBootTest
@WebAppConfiguration
@ActiveProfiles("dev")
@Ignore
public class IpInfoJobTest {

    @Autowired
    private IpInfoJob ipInfoJob;
    @Test
    public void execute(){
        ipInfoJob.execute();
    }
}
