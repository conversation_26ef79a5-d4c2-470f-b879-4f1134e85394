//package com.quanzhi.auditapiv2.biz;
//
//import com.mongodb.MongoClient;
//import com.mongodb.MongoClientOptions;
//import com.mongodb.MongoCredential;
//import com.mongodb.ServerAddress;
//import com.quanzhi.auditapiv2.core.risk.entity.RiskInfo;
//import org.junit.Test;
//import org.junit.runner.RunWith;
//import org.junit.runners.JUnit4;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.boot.test.context.SpringBootTest;
//import org.springframework.data.mongodb.core.MongoTemplate;
//import org.springframework.data.mongodb.core.aggregation.Aggregation;
//import org.springframework.data.mongodb.core.aggregation.AggregationResults;
//import org.springframework.data.mongodb.core.query.Criteria;
//
//import java.util.Map;
//
///**
// * @author: yangzixian
// * @date: 2023.10.19 10:11
// * @description:
// */
//@SpringBootTest
//public class RiskTest {
//
//    @Test
//    public void testGroup() {
//        MongoTemplate mongoTemplate = new MongoTemplate(new MongoClient(new ServerAddress("*************", 17017), MongoCredential.createCredential("audit", "audit", "123@Audit".toCharArray()), MongoClientOptions.builder().build()), "audit");
//        Aggregation aggregation = Aggregation.newAggregation(
////                Aggregation.match(Criteria.where("httpEvent.ip").gte(System.currentTimeMillis() - 3600 * 1000)),
//                Aggregation.group("entities",
//                        "channels",
//                        "policySnapshot._id",
//                        "description",
//                        "rspLabelList")
//        );
//        AggregationResults<RiskInfo> riskInfos = mongoTemplate.aggregate(aggregation, "riskInfo", RiskInfo.class);
//        for (RiskInfo riskInfo : riskInfos) {
//            System.out.println(riskInfo);
//        }
//    }
//
//}
