//package com.quanzhi.auditapiv2.biz.subscribeTest;
//
//import com.quanzhi.auditapiv2.common.dal.dao.IHttpApiDao;
//import com.quanzhi.auditapiv2.common.dal.dao.IHttpAppDao;
//import com.quanzhi.auditapiv2.core.risk.repository.IRiskInfoDao;
//import com.quanzhi.auditapiv2.core.service.SysUserService;
//import com.quanzhi.auditapiv2.core.service.manager.kafkaListener.ResourceChangedEventListener;
//import com.quanzhi.auditapiv2.core.service.manager.web.IApiWeaknessLogService;
//import com.quanzhi.auditapiv2.core.service.manager.web.IApiWeaknessService;
//import com.quanzhi.auditapiv2.core.service.manager.web.INotifyService;
//import com.quanzhi.auditapiv2.core.service.manager.web.IResourceChangeEventHandleService;
//import com.quanzhi.metabase.core.model.event.ResourceChangedEvent;
//import org.jeasy.random.EasyRandom;
//import org.jeasy.random.EasyRandomParameters;
//import org.junit.Before;
//import org.junit.Ignore;
//import org.junit.Test;
//import org.junit.runner.RunWith;
//import org.mockito.Mock;
//import org.mockito.junit.MockitoJUnitRunner;
//import org.springframework.context.ApplicationEventPublisher;
//
///**
// * <AUTHOR>
// * @date 2023/9/5 10:56 上午
// */
//@RunWith(MockitoJUnitRunner.class)
//@Ignore
//public class ApiAndWeakTest {
//
////    @Autowired
////    private ResourceChangedEventListener resourceChangedEventListener;
////
////    @Autowired
////    private HttpApiService httpApiService;
////
////    @Autowired
////    private ResourceChangeEventHandleServiceImpl resourceChangeEventHandleService;
////
////    private final String API_INFO = "{\"eventType\":\"NewResourceEvent\",\"payload\":{\"apiType\":2,\"appId\":\"64f4a67ae62c75396cc3f2f1\",\"appUri\":\"httpapp:rv.cn@QZKJMGC@64ef148b6554d375c18ec00a\",\"classifications\":[],\"deployDomains\":[\"局域网-其他\"],\"featureLabels\":[],\"host\":\"rv.cn\",\"id\":\"64f4a67ae62c75396cc3f2f2\",\"level\":\"非敏感\",\"lowClassifications\":[],\"lowReqDataLabels\":[],\"lowRspDataLabels\":[],\"methods\":[\"POST\"],\"newClassifications\":[],\"newDeployDomains\":[\"局域网-其他\"],\"newFeatureLabels\":[],\"newLowClassifications\":[],\"newLowReqDataLabels\":[],\"newLowRspDataLabels\":[],\"newReqContentTypes\":[\"x-www-form-urlencoded\"],\"newReqDataLabels\":[],\"newRspContentTypes\":[\"html\"],\"newRspDataLabels\":[],\"newVisitDomains\":[\"互联网-境内\"],\"parseConfigs\":{},\"recommend\":true,\"recommendFlag\":1,\"reqContentTypes\":[\"x-www-form-urlencoded\"],\"reqDataLabels\":[],\"restfulFlag\":1,\"rspContentTypes\":[\"html\"],\"rspDataLabels\":[],\"sampleId\":\"8C02741188214A0EADA3B20576099888\",\"sendTime\":1693755002852,\"terminals\":[\"PC\"],\"timestamp\":1692770856000,\"visitDomains\":[\"互联网-境内\"],\"whenFlag\":0},\"resourceType\":\"httpApi\",\"uri\":\"httpapi:http://rv.cn/hackable/uploads/4.php@QZKJMGC@64ef148b6554d375c18ec00a\"}";
////
////    private final String WEAK_INFO = "{\"eventType\":\"NewResourceEvent\",\"payload\":{\"earlyTimestamp\":1693816775025,\"id\":\"64f597c724dca57bc507bcdb\",\"lastOperator\":\"zixian\",\"level\":2,\"name\":\"任意短信发送\",\"state\":\"NEW\",\"timestamp\":1693816775025,\"type\":\"unrestricted_access_to_sensitive_business_flows\",\"updateTime\":1693816775029,\"weaknessId\":\"anysmssend\"},\"resourceType\":\"httpApiWeakness\",\"uri\":\"httpapi:http://06.cn/login\"}";
////
////    @Test
////    public void apiPushTest() throws Exception {
////        //转换推送对象
////        ResourceChangedEvent resourceChangedEvent = resourceChangedEventListener.convertResource(API_INFO);
////        //推送API
////        resourceChangeEventHandleService.handle(resourceChangedEvent);
////    }
////
////    @Test
////    public void weakPushTest() throws Exception {
////        //转换推送对象
////        ResourceChangedEvent resourceChangedEvent = resourceChangedEventListener.convertResource(WEAK_INFO);
////        //推送弱点
////        resourceChangeEventHandleService.handle(resourceChangedEvent);
////    }
//
//    private ResourceChangedEventListener resourceChangedEventListener;
//
//    @Mock
//    private IResourceChangeEventHandleService resourceChangeEventHandleService;
//
//    @Mock
//    private IApiWeaknessLogService apiWeaknessLogServiceImpl;
//
//    @Mock
//    private INotifyService notifyServiceImpl;
//
//    @Mock
//    private SysUserService sysUserServiceImpl;
//
//    @Mock
//    private IHttpAppDao httAppDao;
//
//    @Mock
//    private IHttpApiDao apiDao;
//
//    @Mock
//    private IRiskInfoDao riskInfoDaoImpl;
//
//    @Mock
//    private ApplicationEventPublisher applicationEventPublisher;
//
//    private EasyRandom generator;
//
//    @Before
//    public void setup() {
//        resourceChangedEventListener = new ResourceChangedEventListener(resourceChangeEventHandleService, apiWeaknessLogServiceImpl, notifyServiceImpl, sysUserServiceImpl, httAppDao, apiDao, riskInfoDaoImpl, applicationEventPublisher);
//        EasyRandomParameters easyRandomParameters = new EasyRandomParameters();
//        generator = new EasyRandom(easyRandomParameters);
//        easyRandomParameters.collectionSizeRange(1, 4);
//    }
//
//    /**
//     * 测试成功推送流程
//     */
//    @Test
//    public void test_send_success() {
//        ResourceChangedEvent resourceChangedEvent = generator.nextObject(ResourceChangedEvent.class);
//
//    }
//
//    /**
//     * 测试推送失败进行nack的流程
//     */
//    @Test
//    public void test_send_fail2Nack() {
//
//    }
//
//    /**
//     * 测试推送失败直至丢弃的流程
//     */
//    @Test
//    public void test_send_fail2Discard() {
//
//    }
//
//}
