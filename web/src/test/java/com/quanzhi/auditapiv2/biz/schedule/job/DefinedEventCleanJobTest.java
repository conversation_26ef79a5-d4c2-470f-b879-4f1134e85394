//package com.quanzhi.auditapiv2.biz.schedule.job;
//
//import com.alibaba.fastjson.JSON;
//import org.junit.Test;
//import org.junit.runner.RunWith;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.boot.test.context.SpringBootTest;
//import org.springframework.data.mongodb.core.MongoTemplate;
//import org.springframework.data.mongodb.core.query.Criteria;
//import org.springframework.data.mongodb.core.query.Query;
//import org.springframework.data.mongodb.core.query.Update;
//import org.springframework.test.context.ActiveProfiles;
//import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;
//import org.springframework.test.context.web.WebAppConfiguration;
//
//import java.util.HashMap;
//import java.util.Map;
//
///**
// * <AUTHOR>
// * @date 2021/9/22 10:41 上午
// */
//@RunWith(SpringJUnit4ClassRunner.class)
//@SpringBootTest
//@WebAppConfiguration
//@ActiveProfiles("local")
//public class DefinedEventCleanJobTest {
//    @Autowired
//    private DefinedEventCleanJob definedEventCleanJob;
//
//    @Test
//    public void testJob() {
//        Map<String, Object> map = new HashMap<>();
//        map.put("date", "2021-10-11");
//        definedEventCleanJob.execute(JSON.toJSONString(map));
//    }
//}
