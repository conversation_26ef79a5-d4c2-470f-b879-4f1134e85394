package com.quanzhi.auditapiv2.sso;

import com.quanzhi.auditapiv2.common.dal.dataobject.SysRole;
import com.quanzhi.auditapiv2.common.dal.entity.ConstantInfo;
import com.quanzhi.auditapiv2.core.service.impl.SysRoleServiceImpl;
import com.quanzhi.auditapiv2.core.service.sso.RequestServiceImpl;
import com.quanzhi.auditapiv2.core.service.sso.SsoServiceImpl;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

import java.io.IOException;

/**
 * @Author: K, 小康
 * @Date: 2024/01/11/下午3:32
 * @Description:
 */
@RunWith(MockitoJUnitRunner.class)
public class LtskSso {
    @Mock
    private SysRoleServiceImpl sysRoleService;
    private SsoServiceImpl ssoServiceImpl;
    @Mock
    private RequestServiceImpl requestService;
    @Before
    public void setup(){
        //ssoServiceImpl = new SsoServiceImpl(sysRoleService,requestService, zcService);
    }
    @Test
    public void test_isLogin() throws IOException {
        String header = ConstantInfo.Bearer+"3123";
        String response = "{\"code\":\"200\",\"msg\":\"\",\"data\":{\"user\":{\"id\":\"123\",\"username\":\"演示用户\",\"phone\":\"\"}}}";
        Mockito.when(requestService.sendRequest(Mockito.any())).thenReturn(response);
        SysRole sysRole = new SysRole();
        sysRole.setId("123");
        sysRole.setRoleName("安全管理员");
        Mockito.when(sysRoleService.getByRoleName(Mockito.anyString())).thenReturn(sysRole);
        Boolean login = ssoServiceImpl.login(header);
        System.out.println(login);
    }

}
