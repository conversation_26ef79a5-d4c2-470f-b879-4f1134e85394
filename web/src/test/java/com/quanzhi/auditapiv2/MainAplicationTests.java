package com.quanzhi.auditapiv2;

import com.alibaba.fastjson.JSON;
import com.quanzhi.audit_core.common.model.RiskInfoAgg;
import com.quanzhi.audit_core.resource.fetcher.client.utils.IpRegionalUtils;
import com.quanzhi.auditapiv2.biz.schedule.job.DailyFullDataSynchronizationJob;
import com.quanzhi.auditapiv2.biz.schedule.job.RiskCountryCountScheduleJob;
import com.quanzhi.auditapiv2.biz.schedule.job.RiskIndexCountJob;
import com.quanzhi.auditapiv2.common.dal.cache.impl.HttpApiInfoCacheRepositoryImpl;
import com.quanzhi.auditapiv2.common.dal.dao.IVisitTrendDao;
import com.quanzhi.auditapiv2.common.dal.dao.impl.HttpApiDaoImpl;
import com.quanzhi.auditapiv2.common.dal.dao.impl.HttpAppDaoImpl;
import com.quanzhi.auditapiv2.common.dal.dao.impl.UserDepartDaoImpl;
import com.quanzhi.auditapiv2.common.dal.dto.api.ApiStatistics;
import com.quanzhi.auditapiv2.common.dal.entity.SubscribeSyncConfig;
import com.quanzhi.auditapiv2.common.dal.entity.syslog.TcpSyslogClient;
import com.quanzhi.auditapiv2.common.dal.entity.syslog.UdpSyslogClient;
import com.quanzhi.auditapiv2.common.util.utils.ColorUtil;
import com.quanzhi.auditapiv2.core.service.subscribeOutput.SubscribeMsg2Syslog;
import com.quanzhi.auditapiv2.core.service.manager.kafkaListener.ResourceChangedEventListener;
import com.quanzhi.auditapiv2.core.service.manager.web.IHttpAppService;
import com.quanzhi.auditapiv2.core.service.manager.web.IResourceChangeEventHandleService;
import com.quanzhi.auditapiv2.core.service.manager.web.impl.AccParseConfigServiceImpl;
import com.quanzhi.auditapiv2.core.service.manager.web.impl.HttpApiService;
import com.quanzhi.auditapiv2.core.service.manager.web.securityPosture.ISecurityPostureService;
import com.quanzhi.metabase.core.model.http.HttpApiResource;
import org.junit.Ignore;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.List;
import java.util.stream.Collectors;

/**
 * @ClassName MainAplicationTests
 * @Description TODO
 * <AUTHOR>
 * @Date 2020/9/24 9:39
 **/
@SpringBootTest
@RunWith(SpringRunner.class)
@Ignore
public class MainAplicationTests {
    @Autowired
    @Qualifier("httpApiService")
    private HttpApiService httpApiService;

    @Autowired
    private HttpApiDaoImpl httpApiDao;

    @Autowired
    private HttpAppDaoImpl httpAppDao;

    @Autowired
    private IHttpAppService httpAppService;

    @Autowired
    private UserDepartDaoImpl userDepartDao;

    @Autowired
    private AccParseConfigServiceImpl accParseConfigService;

    @Autowired
    private HttpApiInfoCacheRepositoryImpl httpApiInfoCacheRepository;

    @Autowired
    private RiskIndexCountJob riskIndexCountJob;

    @Autowired
    private RiskCountryCountScheduleJob riskCountryCountScheduleJob;

   /* @Autowired
    private ThreatIpInfoCountJob threatIpInfoCountJob;*/

    @Autowired
    private SubscribeMsg2Syslog subscribeMsg2Syslog;

    @Autowired
    private IResourceChangeEventHandleService resourceChangeEventHandleService;

    @Autowired
    private ResourceChangedEventListener resourceChangedEventListener;

    @Autowired
    private MongoTemplate mongoTemplate;

    private String collectionName = "httpApi";

    @Autowired
    private ISecurityPostureService securityPostureService;

    @Autowired
    private IVisitTrendDao visitTrendDao;

    @Autowired
    private DailyFullDataSynchronizationJob notifyFullDailyJob;

    @Test
    public void testNotifyDaily() {
//        SubscribePolicyRule subscribePolicyRule = new SubscribePolicyRule();
//        subscribePolicyRule.setEnabled(true);
//        subscribePolicyRule.setType("WEAKNESS");
//        notifyFullDailyJob.useSyslog(subscribePolicyRule);
    }

    @Test
    public void testVisitTrend() {
        visitTrendDao.clearData();
    }

    @Test
    public void testColor() {
        System.out.println(ColorUtil.getRandColorCode());
    }

    @Test
    public void testApp() throws Exception {
        securityPostureService.getAppRelation();
    }

    @Test
    public void testKafka() {
        System.out.println(IpRegionalUtils.find("***************"));
        System.out.println(IpRegionalUtils.find("**************"));
    }

    @Test
    public void testTime() {
        long startTime = 1633881600000L;
        long endTime = 1633967999000L;
        //统计总API个数
        long totalApi = mongoTemplate.count(new Query().addCriteria(org.springframework.data.mongodb.core.query.Criteria
                .where("delFlag").is(false)
        ), collectionName);
        //统计高敏API个数
        long totalHighLevelApi = mongoTemplate.count(new Query().addCriteria(org.springframework.data.mongodb.core.query.Criteria
                .where("delFlag").is(false)
                .and("level").is("高敏感")
        ), collectionName);
        //统计互联网数据暴露API个数
        long totalInternetApi = mongoTemplate.count(new Query().addCriteria(org.springframework.data.mongodb.core.query.Criteria
                .where("delFlag").is(false)
                .and("classifications").regex("usedata_api")
        ), collectionName);
        //统计弱点API个数
        long totalWeaknessApi = mongoTemplate.count(new Query().addCriteria(org.springframework.data.mongodb.core.query.Criteria
                .where("delFlag").is(false)
                .and("briefWeaknesses.count").gt(0)
        ), collectionName);

        //获取今日新增api
        List<HttpApiResource> apis = mongoTemplate.find(new Query().addCriteria(org.springframework.data.mongodb.core.query.Criteria
                .where("delFlag").is(false)
                .and("discoverTime").gte(startTime).lte(endTime)
        ), HttpApiResource.class, collectionName);
        //统计今日新增API个数
        long todayApi = apis.size();
        //统计今日新增高敏API个数
        long todayHighLevelApi = apis.stream().filter(api -> "高敏感".equals(api.getLevel())).collect(Collectors.toList()).size();
        //统计今日新增互联网数据暴露API个数
        long todayInternetApi = apis.stream().filter(api -> api.getClassifications().contains("usedata_api")).collect(Collectors.toList()).size();
        //统计今日新增弱点API个数
        long todayWeaknessApi = apis.stream().filter(api -> api.getBriefWeaknesses() != null && api.getBriefWeaknesses().getCount() == 0).collect(Collectors.toList()).size();
        System.out.println(ApiStatistics.builder()
                //                .totalCount(totalApi)
                .todayApi(todayApi)
                //                .internetCount(totalInternetApi)
                .todayInternetApi(todayInternetApi)
                //                .highLevelCount(totalHighLevelApi)
                .todayHighLevelApi(todayHighLevelApi)
                //                .weaknessCount(totalWeaknessApi)
                .todayWeaknessApi(todayWeaknessApi).build().toString());
    }

    @Test
    public void testSyslog() {
        SubscribeSyncConfig subscribeSyncConfig = new SubscribeSyncConfig();
        subscribeSyncConfig.setIp("*************");
        subscribeSyncConfig.setPort("514");
        subscribeSyncConfig.setProtocol("TCP");

        RiskInfoAgg riskInfoAgg = new RiskInfoAgg();
        riskInfoAgg.setRiskId("1111");
        riskInfoAgg.setDepart("2222");

        try {
            switch (subscribeSyncConfig.getProtocol()) {
                case "TCP":
                    TcpSyslogClient tcpSyslogClient = new TcpSyslogClient(subscribeSyncConfig.getIp(), Integer.valueOf(subscribeSyncConfig.getPort()));
                    tcpSyslogClient.write(JSON.toJSONString(riskInfoAgg).toCharArray());
                    tcpSyslogClient.close();
                    break;
                case "UDP":
                    UdpSyslogClient udpSyslogClient = new UdpSyslogClient(subscribeSyncConfig.getIp(), Integer.valueOf(subscribeSyncConfig.getPort()));
                    udpSyslogClient.write(JSON.toJSONString(riskInfoAgg).toCharArray());
                    udpSyslogClient.close();
                    break;
            }
        } catch (Exception e) {
            System.out.println(e);
        }
    }

    @Test
    public void getRiskIndex() {
        riskIndexCountJob.doJob();
    }

    @Test
    public void getRiskCountry() {
        riskCountryCountScheduleJob.doJob();
    }

    /*@Test
    public void getThreatIp() {
        threatIpInfoCountJob.doJob();
    }*/

//    @Test
//    public void getinfomap(){
//        Map<String, Map<String, Long>> apiInfoMap = httpApiService.getApiInfoMap();
//        System.out.println(apiInfoMap);
//    }
//    @Test
//    public void putinfo(){
//        String key="name";
//        Map<String,Long> map=new HashMap<>();
//        map.put("wang",3L);
//        httpApiInfoCacheRepository.put(key,map);
//    }
//    @Test
//    public void getinfo(){
//        Map<String, Long> map = httpApiInfoCacheRepository.get("name");
//        System.out.println(map);
//    }
//    //新增接口解析配置
//    @Test
//    public void saveAccParseConfig() throws Exception {
//        ApiAccParseCriteriaDto apiAccParseCriteriaDto=new ApiAccParseCriteriaDto();
//        List<HttpApiResource.AccParseConfigs> userParseConfigsList=new ArrayList<>();
//        HttpApiResource.AccParseConfigs userParseConfigs=new HttpApiResource.AccParseConfigs();
//        userParseConfigs.setLocation(HttpLocationEnum.BODY);
//        userParseConfigs.setPath("$.userName");
//        userParseConfigs.setType(LabelConfigTypeEnum.HTML);
//        userParseConfigs.setUdf("");
//        userParseConfigsList.add(userParseConfigs);
//        apiAccParseCriteriaDto.setUri("httpapi:https://www.jsonp.com/resetData.php#GET");
//        apiAccParseCriteriaDto.setUserParseConfigs(userParseConfigsList);
//        httpApiDao.saveAccParseConfig(apiAccParseCriteriaDto,1);
//    }
//
//    @Test
//    public void countByUriAndIsParse() throws Exception {
//        List<String> tags=new ArrayList<>();
//        tags.add("accountParse");
//        System.out.println(httpAppDao.countByUriAndIsParse("httpapp:192.168.0.93",tags,2));
//    }
//
//    /**
//     * 分页查询应用解析配置测试
//     */
//    @Test
//    public void getAccParseAppList() throws Exception {
//        HttpAppCriteriaDto httpAppCriteriaDto=new HttpAppCriteriaDto();
//        httpAppCriteriaDto.setUri("httpapp:192.168.0.93");
//        System.out.println(httpAppService.getAccParseAppList(httpAppCriteriaDto));
//    }
//    @Test
//    public void saveUserDepart(){
//        UserDepart userDepart=new UserDepart();
//        userDepart.setStaffId("0081");
//        userDepart.setStaffDepart("研发部");
//        userDepart.setStaffNickName("傻逼");
//        userDepart.setStaffBankCard("1465675345wer");
//        userDepart.setStaffMobile("665895");
//        userDepart.setStaffIdCard("14545erreewqe234");
//        userDepart.setStaffEmail("<EMAIL>");
//        userDepart.setStaffChinese("sb");
//        userDepart.setStaffName("sba");
//        userDepart.setFirstDate("********");
//        userDepart.setLastDate("********");
//        String u="{'staffDepart':'研发部','staffNickName':'昵称','staffName':'姓名','staffChinese':'英文名','staffIdCard':'身份证','staffBankCard':'银行卡','staffEmail':'邮箱','staffId':'0083','staffMobile':'手机','firstDate':'********','lastDate':'********'}";
//        String uu="{\"staffId\":\"0082\",}";
//        System.out.println(userDepartDao.saveUserDepart(userDepart));
//    }
//    @Test
//    public void modify(){
//        AccParseConfigServiceImpl.AccParseConfig accParseConfig=new AccParseConfigServiceImpl.AccParseConfig();
//        List<String> strings=new ArrayList<>();
////        strings.add("*************");
////        strings.add("*************");
//        strings.add("************");
//        strings.add("*************");
//        accParseConfig.setIpExcludeLists(strings);
//        accParseConfig.setIsUaCheck(true);
//        accParseConfig.setIpAssociatedExpiry(0);
//        accParseConfig.setIsAccParseEnable(false);
//        accParseConfigService.modify(accParseConfig,1);
//    }
//
//    @Test
//    public void pullAccParseConfig() throws NacosException {
//        System.out.println(accParseConfigService.pullAccParseConfigs());
//    }
}
