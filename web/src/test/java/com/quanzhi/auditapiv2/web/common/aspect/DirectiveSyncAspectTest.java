package com.quanzhi.auditapiv2.web.common.aspect;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.quanzhi.auditapiv2.biz.risk.service.RiskInfoService;
import com.quanzhi.auditapiv2.biz.risk.service.ThreatInfoService;
import com.quanzhi.auditapiv2.biz.risk.service.ThreatIpService;
import com.quanzhi.auditapiv2.common.dal.dao.node.ClusterNodeDao;
import com.quanzhi.auditapiv2.common.dal.dto.DealWeaknessDto;
import com.quanzhi.auditapiv2.common.dal.entity.directive.Directive;
import com.quanzhi.auditapiv2.common.dal.entity.node.ClusterNode;
import com.quanzhi.auditapiv2.common.dal.entity.node.ClusterType;
import com.quanzhi.auditapiv2.common.util.dto.ResponseVo;
import com.quanzhi.auditapiv2.common.util.utils.DataUtil;
import com.quanzhi.auditapiv2.core.service.manager.utils.DirectiveSyncUtil;
import com.quanzhi.auditapiv2.core.service.manager.web.IApiWeaknessService;
import com.quanzhi.auditapiv2.core.service.manager.web.IHttpAppService;
import com.quanzhi.auditapiv2.core.service.manager.web.impl.HttpApiService;
import com.quanzhi.auditapiv2.web.common.annotation.DirectiveSync;
import com.quanzhi.auditapiv2.web.common.listener.DirectiveHandler;
import com.quanzhi.auditapiv2.web.common.utils.HttpContextUtils;
import com.quanzhi.auditapiv2.web.controller.ApiWeaknessController;
import com.quanzhi.auditapiv2.web.controller.risk.RiskCheckListController;
import com.quanzhi.auditapiv2.web.controller.risk.RiskPolicyController;
import com.quanzhi.dsl.DslEngine;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.reflect.MethodSignature;
import org.jeasy.random.EasyRandom;
import org.jeasy.random.EasyRandomParameters;
import org.jeasy.random.randomizers.range.IntegerRangeRandomizer;
import org.jeasy.random.randomizers.range.LongRangeRandomizer;
import org.junit.Before;
import org.junit.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.runner.Request;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.data.mongodb.core.FindAndModifyOptions;
import org.springframework.data.mongodb.core.MongoTemplate;

import javax.servlet.http.HttpServletRequest;
import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.Enumeration;
import java.util.List;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

/**
 * @Author: yangzx
 * @Date: 2024/7/25 11:59
 */
@RunWith(MockitoJUnitRunner.class)
public class DirectiveSyncAspectTest {

    private DirectiveSyncAspect directiveSyncAspect;

    @Mock
    private MongoTemplate mongoTemplate;

    @Mock
    private DirectiveSyncUtil directiveSyncUtil;

    @Mock
    private ClusterNodeDao clusterNodeDao;

    @Mock
    private HttpApiService httpApiService;

    @Mock
    private IApiWeaknessService apiWeaknessService;

    @Mock
    private IHttpAppService httpAppService;

    @Mock
    private DirectiveHandler directiveHandler;

    private EasyRandom generator;

    @Before
    public void setup() {
        directiveSyncAspect = new DirectiveSyncAspect(mongoTemplate, directiveSyncUtil, clusterNodeDao, httpApiService, apiWeaknessService, 8, httpAppService, directiveHandler);
        EasyRandomParameters easyRandomParameters = new EasyRandomParameters();
        generator = new EasyRandom(easyRandomParameters);
        easyRandomParameters.collectionSizeRange(1, 4);
        easyRandomParameters.randomize(Long.class, new LongRangeRandomizer(0L, 100L));
        easyRandomParameters.randomize(Integer.class, new IntegerRangeRandomizer(0, 100));
    }

    @Test
    public void testForward() throws Throwable {
        //返回主节点类型
        when(clusterNodeDao.getClusterType()).thenReturn(ClusterType.MASTER);

        //创建ProceedingJoinPoint的mock
        ProceedingJoinPoint joinPoint = mock(ProceedingJoinPoint.class);

        //注解
        Class<RiskCheckListController> riskCheckListControllerClass = RiskCheckListController.class;
        Method method = riskCheckListControllerClass.getMethod("markRisk", String.class, Boolean.class);
        MethodSignature methodSignature = mock(MethodSignature.class);
        when(joinPoint.getSignature()).thenReturn(methodSignature);
        when(methodSignature.getMethod()).thenReturn(method);

        //返回成功的调用结果
        ResponseVo responseVo = new ResponseVo();
        responseVo.setSuccess(true);
        when(joinPoint.proceed()).thenReturn(responseVo);

        //主节点+调用成功
        ResponseVo success = (ResponseVo) directiveSyncAspect.forward(joinPoint);
        assert success != null && success.isSuccess();

        //返回失败的调用结果
        responseVo.setSuccess(false);
        when(joinPoint.proceed()).thenReturn(responseVo);

        //主节点+调用失败
        ResponseVo fail = (ResponseVo) directiveSyncAspect.forward(joinPoint);
        assert fail != null && !fail.isSuccess();

        //返回子节点类型
        when(clusterNodeDao.getClusterType()).thenReturn(ClusterType.SLAVE);

        //子节点+调用失败
        ResponseVo forwardSlaveFail = (ResponseVo) directiveSyncAspect.forward(joinPoint);
        assert forwardSlaveFail != null && !forwardSlaveFail.isSuccess();

        //返回成功的调用结果
        responseVo.setSuccess(true);
        when(joinPoint.proceed()).thenReturn(responseVo);
        //子节点+调用成功
        ResponseVo forwardSlaveSuccess = (ResponseVo) directiveSyncAspect.forward(joinPoint);
        assert forwardSlaveSuccess != null && forwardSlaveSuccess.isSuccess();
    }

    /**
     * 测试requestBody的Post请求的各种情况
     */
    @Test
    public void testRequestBody2Post() throws Throwable {
        //创建ProceedingJoinPoint的mock
        ProceedingJoinPoint joinPoint = mock(ProceedingJoinPoint.class);
        //注解
        Class<ApiWeaknessController> apiWeaknessControllerClass = ApiWeaknessController.class;
        Method method = apiWeaknessControllerClass.getMethod("dealWeakness", DealWeaknessDto.class);
        DirectiveSync directiveSync = method.getAnnotation(DirectiveSync.class);
        //node
        ClusterNode node = generator.nextObject(ClusterNode.class);
        node.setUrl("http://*******");
        node.setIp("*******");
        node.setName("test");
        //request
        HttpServletRequest request = mock(HttpServletRequest.class);
        when(request.getContextPath()).thenReturn("/test");
        when(request.getServletPath()).thenReturn("/login");
        when(request.getMethod()).thenReturn("POST");
        //header
        Enumeration<String> headerNames = new Enumeration<String>() {
            @Override
            public boolean hasMoreElements() {
                return true;
            }

            @Override
            public String nextElement() {
                return "content-type";
            }
        };
        when(request.getHeaderNames()).thenReturn(headerNames);
        when(request.getHeader("content-type")).thenReturn("application/json;charset=UTF-8");
        //args
        DealWeaknessDto dealWeaknessDto = generator.nextObject(DealWeaknessDto.class);
        Object[] args = new Object[]{dealWeaknessDto};
        when(joinPoint.getArgs()).thenReturn(args);
        //method
        MethodSignature methodSignature = mock(MethodSignature.class);
        when(methodSignature.getMethod()).thenReturn(method);
        String[] parameterNames = new String[]{"dealWeaknessDto"};
        when(methodSignature.getParameterNames()).thenReturn(parameterNames);
        //调用
        JSONObject result = new JSONObject();
        result.put("success", true);
        when(directiveSyncUtil.sync(any())).thenReturn(result);
        //mongo
        Directive directive = new Directive();
        directive.setId("AAABBBCCCDDD");
        when(mongoTemplate.findAndModify(any(), any(), any(), any(), anyString())).thenReturn(directive);
        String send = directiveSyncAspect.send(joinPoint, request, directiveSync, node, methodSignature);
        //子节点调用成功，返回空字符串
        assert "".equals(send);
        //调用
        JSONObject result2 = new JSONObject();
        result.put("success", false);
        when(directiveSyncUtil.sync(any())).thenReturn(result2);
        String send2 = directiveSyncAspect.send(joinPoint, request, directiveSync, node, methodSignature);
        //子节点调用失败，返回子节点名称
        assert "test".equals(send2);
    }

    /**
     * 测试requestParam的Post请求的各种情况
     */
    @Test
    public void testRequestParam2Post() throws NoSuchMethodException {
        //创建ProceedingJoinPoint的mock
        ProceedingJoinPoint joinPoint = mock(ProceedingJoinPoint.class);
        //注解
        Class<ApiWeaknessController> apiWeaknessControllerClass = ApiWeaknessController.class;
        Method method = apiWeaknessControllerClass.getMethod("edit", String.class);
        DirectiveSync directiveSync = method.getAnnotation(DirectiveSync.class);
        //node
        ClusterNode node = generator.nextObject(ClusterNode.class);
        node.setUrl("http://*******");
        node.setIp("*******");
        node.setName("test");
        //request
        HttpServletRequest request = mock(HttpServletRequest.class);
        when(request.getContextPath()).thenReturn("/test");
        when(request.getServletPath()).thenReturn("/login");
        when(request.getMethod()).thenReturn("POST");
        //header
        Enumeration<String> headerNames = new Enumeration<String>() {
            @Override
            public boolean hasMoreElements() {
                return true;
            }

            @Override
            public String nextElement() {
                return "content-type";
            }
        };
        when(request.getHeaderNames()).thenReturn(headerNames);
        when(request.getHeader("content-type")).thenReturn("application/x-www-form-urlencoded");
        //args
        Object[] args = new Object[]{"{\"id\":\"ASDASDASDASD\"}"};
        when(joinPoint.getArgs()).thenReturn(args);
        //method
        MethodSignature methodSignature = mock(MethodSignature.class);
        when(methodSignature.getMethod()).thenReturn(method);
        String[] parameterNames = new String[]{"params"};
        when(methodSignature.getParameterNames()).thenReturn(parameterNames);
        //调用
        JSONObject result = new JSONObject();
        result.put("success", true);
        when(directiveSyncUtil.sync(any())).thenReturn(result);
        //mongo
        Directive directive = new Directive();
        directive.setId("AAABBBCCCDDD");
        when(mongoTemplate.findAndModify(any(), any(), any(), any(), anyString())).thenReturn(directive);
        String send = directiveSyncAspect.send(joinPoint, request, directiveSync, node, methodSignature);
        //子节点调用成功，返回空字符串
        assert "".equals(send);
        //调用
        JSONObject result2 = new JSONObject();
        result.put("success", false);
        when(directiveSyncUtil.sync(any())).thenReturn(result2);
        String send2 = directiveSyncAspect.send(joinPoint, request, directiveSync, node, methodSignature);
        //子节点调用失败，返回子节点名称
        assert "test".equals(send2);
    }

    /**
     * 测试Get请求的各种情况
     */
    @Test
    public void testGet() throws NoSuchMethodException {
        //创建ProceedingJoinPoint的mock
        ProceedingJoinPoint joinPoint = mock(ProceedingJoinPoint.class);
        //注解
        Class<RiskPolicyController> riskPolicyControllerClass = RiskPolicyController.class;
        Method method = riskPolicyControllerClass.getMethod("updateRiskPolicyState", String.class, Boolean.class);
        DirectiveSync directiveSync = method.getAnnotation(DirectiveSync.class);
        //node
        ClusterNode node = generator.nextObject(ClusterNode.class);
        node.setUrl("http://*******");
        node.setIp("*******");
        node.setName("test");
        //request
        HttpServletRequest request = mock(HttpServletRequest.class);
        when(request.getContextPath()).thenReturn("/test");
        when(request.getServletPath()).thenReturn("/login");
        when(request.getMethod()).thenReturn("GET");
        //header
        Enumeration<String> headerNames = new Enumeration<String>() {
            @Override
            public boolean hasMoreElements() {
                return false;
            }

            @Override
            public String nextElement() {
                return "";
            }
        };
        when(request.getHeaderNames()).thenReturn(headerNames);
        //args
        DealWeaknessDto dealWeaknessDto = generator.nextObject(DealWeaknessDto.class);
        Object[] args = new Object[]{"1", true};
        when(joinPoint.getArgs()).thenReturn(args);
        //method
        MethodSignature methodSignature = mock(MethodSignature.class);
        String[] parameterNames = new String[]{"policyId", "enable"};
        when(methodSignature.getParameterNames()).thenReturn(parameterNames);
        //调用
        JSONObject result = new JSONObject();
        result.put("success", true);
        when(directiveSyncUtil.sync(any())).thenReturn(result);
        //mongo
        Directive directive = new Directive();
        directive.setId("AAABBBCCCDDD");
        when(mongoTemplate.findAndModify(any(), any(), any(), any(), anyString())).thenReturn(directive);
        String send = directiveSyncAspect.send(joinPoint, request, directiveSync, node, methodSignature);
        //子节点调用成功，返回空字符串
        assert "".equals(send);
        //调用
        JSONObject result2 = new JSONObject();
        result.put("success", false);
        when(directiveSyncUtil.sync(any())).thenReturn(result2);
        String send2 = directiveSyncAspect.send(joinPoint, request, directiveSync, node, methodSignature);
        //子节点调用失败，返回子节点名称
        assert "test".equals(send2);
    }

}