package com.quanzhi.auditapiv2.gateway;

import com.quanzhi.auditapiv2.common.dal.dao.IGatewayConfigDao;
import com.quanzhi.auditapiv2.common.dal.entity.GatewayConfig;
import com.quanzhi.auditapiv2.common.util.utils.ByteAndBitTransUtil;
import com.quanzhi.auditapiv2.common.util.utils.IPAddressValidatorUtils;
import com.quanzhi.auditapiv2.core.service.SysLogService;
import com.quanzhi.auditapiv2.core.service.manager.web.IGatewayConfigSerivce;
import com.quanzhi.auditapiv2.core.service.manager.web.impl.gateway.GatewayManagerServiceImpl;
import com.quanzhi.auditapiv2.core.service.manager.web.impl.gateway.GatewaySSLInfoServiceImpl;
import com.quanzhi.metabase.core.model.http.HttpAppResource;
import org.jeasy.random.EasyRandom;
import org.jeasy.random.EasyRandomParameters;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @description
 * @date 2023/9/26 16:11
 */
@RunWith(MockitoJUnitRunner.class)
public class GatewayManagerTest {
    //网关配置service
    private GatewayManagerServiceImpl GatewayManagerServiceImpl;

    @Mock
    private IGatewayConfigDao gatewayConfigDaoImpl;

    @Mock
    private SysLogService sysLogService;

    @Mock
    private IGatewayConfigSerivce gatewayConfigService;

    @Mock
    private GatewaySSLInfoServiceImpl gatewaySSLInfoService;

    @Mock
    private GatewayConfig gatewayConfig;

    private EasyRandom generator;

    @Before
    public void setup(){
        GatewayManagerServiceImpl = new GatewayManagerServiceImpl(sysLogService, gatewayConfigDaoImpl,
                gatewayConfigService, gatewaySSLInfoService);
        EasyRandomParameters easyRandomParameters = new EasyRandomParameters();
        generator = new EasyRandom(easyRandomParameters);
        easyRandomParameters.collectionSizeRange(1, 4);
    }

    /**
     * 用户登录因为密码错误失败后，会保存用户登录状态。
     */
    @Test
    public void testSaveUserState_WhenLoginFailWhenPasswordError(){
        GatewayConfig gatewayConfig = generator.nextObject(GatewayConfig.class);
        Mockito.when(gatewayConfigDaoImpl.getById(Mockito.anyString())).thenReturn(gatewayConfig);
        String s = GatewayManagerServiceImpl.editGatewayName("123", "dean");
        System.out.println(s);
    }

    @Test
    public void testAssertions() {
        // 定义一些测试数据
        int num1 = 5;
        int num2 = 7;
        String text1 = "Hello";
        String text2 = "World";
        String nullString = null;

        // 使用断言验证相等性
        Assert.assertEquals(num1, 5);
        Assert.assertEquals(text1, "Hello");

        // 使用断言验证不相等性
        Assert.assertNotEquals(num1, num2);
        Assert.assertNotEquals(text1, text2);

        // 使用断言验证真假
        Assert.assertTrue(num1 < num2);
        Assert.assertFalse(num1 > num2);

        // 使用断言验证空值
        Assert.assertNull(nullString);
        Assert.assertNotNull(text1);

        // 使用断言验证异常
        try {
            int result = num1 / 0;
            Assert.fail("Expected ArithmeticException but got: " + result);
        } catch (ArithmeticException e) {
            // 预期的异常，不做任何操作
        }
    }

    @Test
    public void testByteAndBitTransUtil(){
        //String strVal = "0.43";
        //String strVal = "302.1";
        String strVal = "1128513.34";
        Long val = 0l;
        if (strVal.contains(".")) {
            // 去掉小数点并转换为 Long
            int decimalIndex = strVal.indexOf(".");
            if (decimalIndex >= 0) {
                strVal = strVal.substring(0, decimalIndex);
            }
            val = Long.parseLong(strVal);
        } else {
            // 如果没有小数点，直接转换为 Long
            val = Long.parseLong(strVal);
        }

        BigDecimal bigDecimal = ByteAndBitTransUtil.bytesOrBitUnit(val, ByteAndBitTransUtil.MBIT_SIZE);
        System.out.println(bigDecimal);

    }

    @Test
    public void testIPAddressValidatorUtils(){
        String ip = "*************";
        boolean validIP = IPAddressValidatorUtils.isValidIP(ip);
        System.out.println(validIP);
    }
}
