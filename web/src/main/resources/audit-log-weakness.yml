operations:
  - requestMapping: "/api/export/exportApiWeakness.do"
    module: "弱点"
    type: "导出"
    operation: "导出弱点清单"
    description: "导出弱点清单"
  - requestMapping: "/api/export/exportApiWeaknessReport.do"
    module: "弱点"
    type: "导出"
    operation: "导出弱点报告"
    description: "导出弱点报告"
  - requestMapping: "/api/apiWeakness/filter.do"
    module: "弱点"
    type: "处理"
    operation: "批量忽略弱点事件"
    description: "批量忽略\"${#apiCount}\"个API下的\"${#weaknessCount}\"个弱点事件，忽略内容为：${#name}"
    condition: "#state.toString().equals('IGNORED')"
  - requestMapping: "/api/weaknessRule/black.do"
    module: "弱点"
    type: "处理"
    operation: "弱点加入白名单"
    description: "${#name}"
    condition: "#name != null && #name.length() > 0"
  - requestMapping: "/api/apiWeakness/deal"
    module: "弱点"
    type: "处理"
    operation: "修改弱点状态"
    description: "将${#name}状态改为${#stateName} ${#option}"
  - requestMapping: "/api/apiWeakness/add"
    module: "弱点"
    type: "新增"
    operation: "手动新增弱点"
    description: "API\"${#weaknessAddDto.apiUri}\"手动新增\"${#weaknessAddDto.name}\"弱点"
  - requestMapping: "/api/apiWeakness/getSamples"
    module: "样例"
    type: "取消脱敏"
    operation: "取消脱敏"
    description: "取消脱敏查看API样例\"${#uri}\""
    condition: "#desensitize == false"
  - requestMapping: "/api/weaknessRule/add.do"
    module: "弱点规则"
    type: "新增"
    operation: "新增弱点规则"
    description: "新增弱点规则\"${#name}\""
