[{"apiUrl": "/v2/query/monitorLogs", "apiName": "查询监控事件列表", "postMode": "json", "backendRequestConfig": {"backendParamTemplateJSONPath": {"id": "API_HIGH_CK_EVENT_LIST", "actionFrontTransformPaths": [{"path": "$.repositoryOperateList[0].frontCriteriaList", "value": [{"property": "timestamp", "predicate": "LTE", "value": null, "valueType": "STRING"}, {"property": "timestamp", "predicate": "GTE", "value": null, "valueType": "STRING"}, {"property": "account", "predicate": "REGEX", "value": null}, {"property": "apiUrl", "predicate": "REGEX", "value": null}, {"property": "host", "predicate": "IN", "value": null}, {"property": "appFeatureLabels", "predicate": "HAS_ANY_CLICK_HOUSE", "value": null}, {"property": "apiLevel", "predicate": "IN", "value": null}, {"property": "file_fileDirection", "predicate": "IS", "value": null}, {"property": "file_fileLen", "predicate": "GTE", "value": null}, {"property": "file_fileLen", "predicate": "LTE", "value": null}, {"property": "ip", "predicate": "REGEX", "value": null}, {"property": "net_dstIp", "predicate": "REGEX", "value": null}, {"property": "originIpPosition_city", "predicate": "IS", "value": null}, {"property": "dstIpPosition_city", "predicate": "IS", "value": null}, {"property": "req<PERSON><PERSON><PERSON>", "predicate": "IN", "value": null}, {"property": "rspContentType", "predicate": "IN", "value": null}, {"property": "getOrPostValueString", "predicate": "REGEX", "value": null}, {"property": "rspContentLength", "predicate": "GTE", "value": null}, {"property": "rspContentLength", "predicate": "LTE", "value": null}, {"property": "ua", "predicate": "REGEX", "value": null}, {"property": "referer", "predicate": "REGEX", "value": null}, {"property": "reqDataLabelIds", "predicate": "HAS_ANY_CLICK_HOUSE", "value": null}, {"property": "rspDataLabelIds", "predicate": "HAS_ANY_CLICK_HOUSE", "value": null}, {"property": "classifications", "predicate": "HAS_ANY_CLICK_HOUSE", "value": null}, {"property": "uaType", "predicate": "IS", "value": null}, {"property": "file_fileType", "predicate": "IN", "value": null}, {"property": "primary", "predicate": "IS", "value": null}]}, {"path": "$.repositoryOperateList[0].sort", "value": [{"property": "timestamp", "order": "DESC"}]}, {"path": "$.repositoryOperateList[0].selectFields", "value": [{"selectField": "timestamp"}, {"selectField": "id"}, {"selectField": "net_srcIp_v4"}, {"selectField": "net_srcIp_v6"}, {"selectField": "net_dstIp_v4"}, {"selectField": "net_dstIp_v6"}, {"selectField": "account"}, {"selectField": "apiUrl"}, {"selectField": "classifications"}, {"selectField": "apiLevel"}, {"selectField": "host"}, {"selectField": "appUri"}, {"selectField": "reqContentType"}, {"selectField": "req<PERSON><PERSON><PERSON>"}, {"selectField": "uaType"}, {"selectField": "ua"}, {"selectField": "referer"}, {"selectField": "rspContentType"}, {"selectField": "rspContentLength"}, {"selectField": "rspStatus"}, {"selectField": "reqDataLabelIds"}, {"selectField": "rspDataLabelIds"}]}, {"path": "$.repositoryOperateList[0].page", "value": 1}, {"path": "$.repositoryOperateList[0].limit", "value": 10}]}, "backendParamTransformMap": {"startTimestamp": {"jsonPath": ["$.actionFrontTransformPaths[0].value[1].value"]}, "endTimestamp": {"jsonPath": ["$.actionFrontTransformPaths[0].value[0].value"]}, "account": {"jsonPath": ["$.actionFrontTransformPaths[0].value[2].value"]}, "apiUrl": {"jsonPath": ["$.actionFrontTransformPaths[0].value[3].value"]}, "host": {"jsonPath": ["$.actionFrontTransformPaths[0].value[4].value"]}, "appLabels": {"jsonPath": ["$.actionFrontTransformPaths[0].value[5].value"]}, "apiLevel": {"jsonPath": ["$.actionFrontTransformPaths[0].value[6].value"]}, "fileType": {"jsonPath": ["$.actionFrontTransformPaths[0].value[7].value"]}, "fileLenMin": {"jsonPath": ["$.actionFrontTransformPaths[0].value[8].value"]}, "fileLenMax": {"jsonPath": ["$.actionFrontTransformPaths[0].value[9].value"]}, "srcIp": {"jsonPath": ["$.actionFrontTransformPaths[0].value[10].value"]}, "dstIp": {"jsonPath": ["$.actionFrontTransformPaths[0].value[11].value"]}, "srcPosition": {"jsonPath": ["$.actionFrontTransformPaths[0].value[12].value"]}, "dstPosition": {"jsonPath": ["$.actionFrontTransformPaths[0].value[13].value"]}, "methods": {"jsonPath": ["$.actionFrontTransformPaths[0].value[14].value"]}, "rspContentTypes": {"jsonPath": ["$.actionFrontTransformPaths[0].value[15].value"]}, "reqArg": {"jsonPath": ["$.actionFrontTransformPaths[0].value[16].value"]}, "rspLengthMin": {"jsonPath": ["$.actionFrontTransformPaths[0].value[17].value"]}, "rspLengthMax": {"jsonPath": ["$.actionFrontTransformPaths[0].value[18].value"]}, "ua": {"jsonPath": ["$.actionFrontTransformPaths[0].value[19].value"]}, "referer": {"jsonPath": ["$.actionFrontTransformPaths[0].value[20].value"]}, "reqDataLabelIds": {"jsonPath": ["$.actionFrontTransformPaths[0].value[21].value"]}, "rspDataLabelIds": {"jsonPath": ["$.actionFrontTransformPaths[0].value[22].value"]}, "apiTypes": {"jsonPath": ["$.actionFrontTransformPaths[0].value[23].value"]}, "uaType": {"jsonPath": ["$.actionFrontTransformPaths[0].value[24].value"]}, "fileFormats": {"jsonPath": ["$.actionFrontTransformPaths[0].value[25].value"]}, "primary": {"jsonPath": ["$.actionFrontTransformPaths[0].value[26].value"]}, "sortType": {"jsonPath": ["$.actionFrontTransformPaths[1].value[0].order"]}, "limit": {"jsonPath": ["$.actionFrontTransformPaths[4].value"]}, "page": {"jsonPath": ["$.actionFrontTransformPaths[3].value"]}}}}, {"apiUrl": "/v2/count/monitorLogs", "apiName": "查询监控事件列表", "postMode": "json", "backendRequestConfig": {"backendParamTemplateJSONPath": {"id": "API_HIGH_CK_EVENT_COUNT", "actionFrontTransformPaths": [{"path": "$.repositoryOperateList[0].frontCriteriaList", "value": [{"property": "timestamp", "predicate": "LTE", "value": null, "valueType": "STRING"}, {"property": "timestamp", "predicate": "GTE", "value": null, "valueType": "STRING"}, {"property": "account", "predicate": "REGEX", "value": null}, {"property": "apiUrl", "predicate": "REGEX", "value": null}, {"property": "host", "predicate": "IN", "value": null}, {"property": "appFeatureLabels", "predicate": "HAS_ANY_CLICK_HOUSE", "value": null}, {"property": "apiLevel", "predicate": "IN", "value": null}, {"property": "file_fileDirection", "predicate": "IS", "value": null}, {"property": "file_fileLen", "predicate": "GTE", "value": null}, {"property": "file_fileLen", "predicate": "LTE", "value": null}, {"property": "ip", "predicate": "REGEX", "value": null}, {"property": "net_dstIp", "predicate": "REGEX", "value": null}, {"property": "originIpPosition_city", "predicate": "IS", "value": null}, {"property": "dstIpPosition_city", "predicate": "IS", "value": null}, {"property": "req<PERSON><PERSON><PERSON>", "predicate": "IN", "value": null}, {"property": "rspContentType", "predicate": "IN", "value": null}, {"property": "getOrPostValueString", "predicate": "REGEX", "value": null}, {"property": "rspContentLength", "predicate": "GTE", "value": null}, {"property": "rspContentLength", "predicate": "LTE", "value": null}, {"property": "ua", "predicate": "REGEX", "value": null}, {"property": "referer", "predicate": "REGEX", "value": null}, {"property": "reqDataLabelIds", "predicate": "HAS_ANY_CLICK_HOUSE", "value": null}, {"property": "rspDataLabelIds", "predicate": "HAS_ANY_CLICK_HOUSE", "value": null}, {"property": "classifications", "predicate": "HAS_ANY_CLICK_HOUSE", "value": null}, {"property": "uaType", "predicate": "IS", "value": null}, {"property": "file_fileType", "predicate": "IN", "value": null}, {"property": "primary", "predicate": "IS", "value": null}]}]}, "backendParamTransformMap": {"startTimestamp": {"jsonPath": ["$.actionFrontTransformPaths[0].value[1].value"]}, "endTimestamp": {"jsonPath": ["$.actionFrontTransformPaths[0].value[0].value"]}, "account": {"jsonPath": ["$.actionFrontTransformPaths[0].value[2].value"]}, "apiUrl": {"jsonPath": ["$.actionFrontTransformPaths[0].value[3].value"]}, "host": {"jsonPath": ["$.actionFrontTransformPaths[0].value[4].value"]}, "appLabels": {"jsonPath": ["$.actionFrontTransformPaths[0].value[5].value"]}, "apiLevel": {"jsonPath": ["$.actionFrontTransformPaths[0].value[6].value"]}, "fileType": {"jsonPath": ["$.actionFrontTransformPaths[0].value[7].value"]}, "fileLenMin": {"jsonPath": ["$.actionFrontTransformPaths[0].value[8].value"]}, "fileLenMax": {"jsonPath": ["$.actionFrontTransformPaths[0].value[9].value"]}, "srcIp": {"jsonPath": ["$.actionFrontTransformPaths[0].value[10].value"]}, "dstIp": {"jsonPath": ["$.actionFrontTransformPaths[0].value[11].value"]}, "srcPosition": {"jsonPath": ["$.actionFrontTransformPaths[0].value[12].value"]}, "dstPosition": {"jsonPath": ["$.actionFrontTransformPaths[0].value[13].value"]}, "methods": {"jsonPath": ["$.actionFrontTransformPaths[0].value[14].value"]}, "rspContentTypes": {"jsonPath": ["$.actionFrontTransformPaths[0].value[15].value"]}, "reqArg": {"jsonPath": ["$.actionFrontTransformPaths[0].value[16].value"]}, "rspLengthMin": {"jsonPath": ["$.actionFrontTransformPaths[0].value[17].value"]}, "rspLengthMax": {"jsonPath": ["$.actionFrontTransformPaths[0].value[18].value"]}, "ua": {"jsonPath": ["$.actionFrontTransformPaths[0].value[19].value"]}, "referer": {"jsonPath": ["$.actionFrontTransformPaths[0].value[20].value"]}, "reqDataLabelIds": {"jsonPath": ["$.actionFrontTransformPaths[0].value[21].value"]}, "rspDataLabelIds": {"jsonPath": ["$.actionFrontTransformPaths[0].value[22].value"]}, "apiTypes": {"jsonPath": ["$.actionFrontTransformPaths[0].value[23].value"]}, "uaType": {"jsonPath": ["$.actionFrontTransformPaths[0].value[24].value"]}, "fileFormats": {"jsonPath": ["$.actionFrontTransformPaths[0].value[25].value"]}, "primary": {"jsonPath": ["$.actionFrontTransformPaths[0].value[26].value"]}}}}, {"apiUrl": "/v2/get/monitorLog", "apiName": "查看事件样例", "postMode": "json", "backendRequestConfig": {"backendParamTemplateJSONPath": {"desensitize": true, "id": "CLICKHOUSE_CHECK_HTTP_EVENT", "actionFrontTransformPaths": [{"path": "$.repositoryOperateList[0].frontCriteriaList", "value": [{"property": "primary", "predicate": "IS", "value": null}]}, {"path": "$.repositoryOperateList[0].collectionName", "value": "http_defined_event"}, {"path": "$.repositoryOperateList[0].operateRsp.backend2FrontConvert.args", "value": [true]}]}, "backendParamTransformMap": {"primary": {"jsonPath": ["$.actionFrontTransformPaths[0].value[0].value"]}, "desensitize": {"jsonPath": ["$.desensitize", "$.actionFrontTransformPaths[2].value[0]"]}}}}, {"apiUrl": "/v2/query/dataLabelValue", "apiName": "查看事件敏感数据", "postMode": "json", "backendRequestConfig": {"backendParamTemplateJSONPath": {"id": "APP_AUDIT_2.1_CLICKHOUSE_EVENT_LABEL_VALUE", "actionFrontTransformPaths": [{"path": "$.repositoryOperateList[0].frontCriteriaList", "value": [{"property": "primary", "predicate": "IS", "value": null}]}, {"path": "$.repositoryOperateList[0].operateRsp.backend2FrontConvert.args", "value": [true]}, {"path": "$.repositoryOperateList[0].collectionName", "value": "http_defined_event"}]}, "backendParamTransformMap": {"primary": {"jsonPath": ["$.actionFrontTransformPaths[0].value[0].value"]}, "desensitize": {"jsonPath": ["$.actionFrontTransformPaths[1].value[0]"]}}}}, {"apiUrl": "/v1/create/clueTraceTaskFullLink", "apiName": "创建线索溯源任务", "postMode": "json", "backendRequestConfig": {"backendParamTemplateJSONPath": {"name": "", "traceDto": {"originalSensiDataValues": [], "endTimestamp": null, "startTimestamp": null}, "desc": "", "traceTaskTypeEnum": "SENSI_DATA_TRACE"}, "backendParamTransformMap": {"startTimestamp": {"jsonPath": ["$.traceDto.startTimestamp"]}, "endTimestamp": {"jsonPath": ["$.traceDto.endTimestamp"]}, "clues": {"jsonPath": ["$.traceDto.originalSensiDataValues"]}}}}, {"apiUrl": "/v1/get/clueTraceTask", "apiName": "获取任务状态", "postMode": "json", "backendRequestConfig": {"backendParamTemplateJSONPath": {"id": "AUDIT_V2_2.6.2_TRACE_TASK_RECORD_DETAIL", "actionFrontTransformPaths": [{"path": "$.repositoryOperateList[0].id", "value": null}]}, "backendParamTransformMap": {"clueTraceTaskId": {"jsonPath": ["$.actionFrontTransformPaths[0].value"]}}}}, {"apiUrl": "/v1/query/clueTraceEventsFullLink", "apiName": "获取溯源任务事件列表", "postMode": "json", "backendRequestConfig": {"backendParamTemplateJSONPath": {"id": "AUDIT_V2_TRACE_EVENT_LIST", "actionFrontTransformPaths": [{"path": "$.repositoryOperateList[0].collectionName", "value": "event_trace"}, {"path": "$.repositoryOperateList[1].collectionName", "value": "event_trace"}, {"path": "$.repositoryOperateList[0].sort", "value": [{"property": "primary", "order": "DESC"}]}, {"path": "$.repositoryOperateList[0].frontCriteriaList", "value": [{"property": "taskId", "predicate": "IS", "value": null}]}, {"path": "$.repositoryOperateList[0].page", "value": 1}, {"path": "$.repositoryOperateList[0].limit", "value": 5}, {"path": "$.repositoryOperateList[1].frontCriteriaList", "value": [{"property": "taskId", "predicate": "IS", "value": null}]}, {"path": "$.repositoryOperateList[0].selectFields", "value": [{"selectField": "*"}]}]}, "backendParamTransformMap": {"clueTraceTaskId": {"jsonPath": ["$.actionFrontTransformPaths[3].value[0].value", "$.actionFrontTransformPaths[6].value[0].value"]}, "page": {"jsonPath": ["$.actionFrontTransformPaths[4].value"]}, "limit": {"jsonPath": ["$.actionFrontTransformPaths[5].value"]}}}}, {"apiUrl": "/v1/create/subjectTraceTask", "apiName": "添加主体溯源任务", "postMode": "json", "backendRequestConfig": {"backendParamTemplateJSONPath": {"name": "", "traceDto": {"endTimestamp": null, "startTimestamp": null, "labels": null, "referers": null, "accounts": null, "ips": null, "apiUrls": null, "uas": null}, "desc": "", "traceTaskTypeEnum": "ENTITY_TRACE", "taskCron": {"time": null, "dayofMonth": null, "week": null}}, "backendParamTransformMap": {"time": {"jsonPath": ["$.taskCron.time"]}, "dayofMonth": {"jsonPath": ["$.taskCron.dayofMonth"]}, "week": {"jsonPath": ["$.taskCron.week"]}, "name": {"jsonPath": ["$.name"]}, "accounts": {"jsonPath": ["$.traceDto.accounts"]}, "ips": {"jsonPath": ["$.traceDto.ips"]}, "apiUris": {"jsonPath": ["$.traceDto.apiUrls"]}, "startTimestamp": {"jsonPath": ["$.traceDto.startTimestamp"]}, "endTimestamp": {"jsonPath": ["$.traceDto.endTimestamp"]}, "dataLabelIds": {"jsonPath": ["$.traceDto.labels"]}, "uas": {"jsonPath": ["$.traceDto.uas"]}, "referers": {"jsonPath": ["$.traceDto.referers"]}}}}, {"apiUrl": "/v1/query/subjectTraceEvents", "apiName": "查询主体溯源事件列表", "postMode": "json", "backendRequestConfig": {"backendParamTemplateJSONPath": {"id": "AUDIT_V2_TRACE_EVENT_LIST", "actionFrontTransformPaths": [{"path": "$.repositoryOperateList[0].sort", "value": [{"property": "timestamp", "order": "DESC"}]}, {"path": "$.repositoryOperateList[0].frontCriteriaList", "value": [{"property": "taskId", "predicate": "IS", "value": null}, {"property": "account", "predicate": "REGEX", "value": null, "regexPredicateAddPercentBoolean": true}, {"property": "ip", "predicate": "REGEX", "value": null, "regexPredicateAddPercentBoolean": true}, {"property": "apiUrl", "predicate": "REGEX", "value": null, "regexPredicateAddPercentBoolean": true}, {"property": "reqDataLabelIds", "predicate": "HAS_ANY_CLICK_HOUSE", "value": null}, {"property": "rspDataLabelIds", "predicate": "HAS_ANY_CLICK_HOUSE", "value": null}, {"property": "classifications", "predicate": "HAS_ANY_CLICK_HOUSE", "value": null}]}, {"path": "$.repositoryOperateList[0].page", "value": null}, {"path": "$.repositoryOperateList[0].limit", "value": null}, {"path": "$.repositoryOperateList[1].frontCriteriaList", "value": [{"property": "taskId", "predicate": "IS", "value": null}, {"property": "account", "predicate": "REGEX", "value": null, "regexPredicateAddPercentBoolean": true}, {"property": "ip", "predicate": "REGEX", "value": null, "regexPredicateAddPercentBoolean": true}, {"property": "apiUrl", "predicate": "REGEX", "value": null, "regexPredicateAddPercentBoolean": true}, {"property": "reqDataLabelIds", "predicate": "HAS_ANY_CLICK_HOUSE", "value": null}, {"property": "rspDataLabelIds", "predicate": "HAS_ANY_CLICK_HOUSE", "value": null}, {"property": "classifications", "predicate": "HAS_ANY_CLICK_HOUSE", "value": null}]}]}, "backendParamTransformMap": {"subjectTraceTaskId": {"jsonPath": ["$.actionFrontTransformPaths[1].value[0].value", "$.actionFrontTransformPaths[4].value[0].value"]}, "account": {"jsonPath": ["$.actionFrontTransformPaths[1].value[1].value", "$.actionFrontTransformPaths[4].value[1].value"]}, "ip": {"jsonPath": ["$.actionFrontTransformPaths[1].value[2].value", "$.actionFrontTransformPaths[4].value[2].value"]}, "apiUri": {"jsonPath": ["$.actionFrontTransformPaths[1].value[3].value", "$.actionFrontTransformPaths[4].value[3].value"]}, "reqDataLabelIds": {"jsonPath": ["$.actionFrontTransformPaths[1].value[4].value", "$.actionFrontTransformPaths[4].value[4].value"]}, "rspDataLabelIds": {"jsonPath": ["$.actionFrontTransformPaths[1].value[5].value", "$.actionFrontTransformPaths[4].value[5].value"]}, "apiTypes": {"jsonPath": ["$.actionFrontTransformPaths[1].value[6].value", "$.actionFrontTransformPaths[4].value[6].value"]}, "page": {"jsonPath": ["$.actionFrontTransformPaths[2].value"]}, "limit": {"jsonPath": ["$.actionFrontTransformPaths[3].value"]}}}}, {"apiUrl": "/v2/query/subjectTraceEventsFullLink", "apiName": "获取溯源任务事件列表", "postMode": "json", "backendRequestConfig": {"backendParamTemplateJSONPath": {"id": "AUDIT_V2_TRACE_EVENT_LIST", "actionFrontTransformPaths": [{"path": "$.repositoryOperateList[0].collectionName", "value": "event_trace"}, {"path": "$.repositoryOperateList[1].collectionName", "value": "event_trace"}, {"path": "$.repositoryOperateList[0].sort", "value": [{"property": "primary", "order": "DESC"}]}, {"path": "$.repositoryOperateList[0].frontCriteriaList", "value": [{"property": "taskId", "predicate": "IS", "value": null}, {"property": "account", "predicate": "REGEX", "value": null, "regexPredicateAddPercentBoolean": true}, {"property": "ip", "predicate": "REGEX", "value": null, "regexPredicateAddPercentBoolean": true}, {"property": "apiUrl", "predicate": "REGEX", "value": null, "regexPredicateAddPercentBoolean": true}, {"property": "reqDataLabelIds", "predicate": "HAS_ANY_CLICK_HOUSE", "value": null}, {"property": "rspDataLabelIds", "predicate": "HAS_ANY_CLICK_HOUSE", "value": null}, {"property": "classifications", "predicate": "HAS_ANY_CLICK_HOUSE", "value": null}, {"property": "host", "predicate": "IN", "value": null}]}, {"path": "$.repositoryOperateList[0].page", "value": 1}, {"path": "$.repositoryOperateList[0].limit", "value": 5}, {"path": "$.repositoryOperateList[1].frontCriteriaList", "value": [{"property": "taskId", "predicate": "IS", "value": null}, {"property": "account", "predicate": "REGEX", "value": null, "regexPredicateAddPercentBoolean": true}, {"property": "ip", "predicate": "REGEX", "value": null, "regexPredicateAddPercentBoolean": true}, {"property": "apiUrl", "predicate": "REGEX", "value": null, "regexPredicateAddPercentBoolean": true}, {"property": "reqDataLabelIds", "predicate": "HAS_ANY_CLICK_HOUSE", "value": null}, {"property": "rspDataLabelIds", "predicate": "HAS_ANY_CLICK_HOUSE", "value": null}, {"property": "classifications", "predicate": "HAS_ANY_CLICK_HOUSE", "value": null}, {"property": "host", "predicate": "IN", "value": null}]}, {"path": "$.repositoryOperateList[0].selectFields", "value": [{"selectField": "*"}]}]}, "backendParamTransformMap": {"account": {"jsonPath": ["$.actionFrontTransformPaths[3].value[1].value", "$.actionFrontTransformPaths[6].value[1].value"]}, "ip": {"jsonPath": ["$.actionFrontTransformPaths[3].value[2].value", "$.actionFrontTransformPaths[6].value[2].value"]}, "apiUri": {"jsonPath": ["$.actionFrontTransformPaths[3].value[3].value", "$.actionFrontTransformPaths[6].value[3].value"]}, "reqDataLabelIds": {"jsonPath": ["$.actionFrontTransformPaths[3].value[4].value", "$.actionFrontTransformPaths[6].value[4].value"]}, "rspDataLabelIds": {"jsonPath": ["$.actionFrontTransformPaths[3].value[5].value", "$.actionFrontTransformPaths[6].value[5].value"]}, "apiTypes": {"jsonPath": ["$.actionFrontTransformPaths[3].value[6].value", "$.actionFrontTransformPaths[6].value[6].value"]}, "host": {"jsonPath": ["$.actionFrontTransformPaths[3].value[7].value", "$.actionFrontTransformPaths[6].value[7].value"]}, "subjectTraceTaskId": {"jsonPath": ["$.actionFrontTransformPaths[3].value[0].value", "$.actionFrontTransformPaths[6].value[0].value"]}, "page": {"jsonPath": ["$.actionFrontTransformPaths[4].value"]}, "limit": {"jsonPath": ["$.actionFrontTransformPaths[5].value"]}}}}, {"apiUrl": "/v1/get/subjectTraceTask", "apiName": "获取任务状态", "postMode": "json", "backendRequestConfig": {"backendParamTemplateJSONPath": {"id": "AUDIT_V2_2.6.2_TRACE_TASK_RECORD_DETAIL", "actionFrontTransformPaths": [{"path": "$.repositoryOperateList[0].id", "value": null}]}, "backendParamTransformMap": {"subjectTraceTaskId": {"jsonPath": ["$.actionFrontTransformPaths[0].value"]}}}}]