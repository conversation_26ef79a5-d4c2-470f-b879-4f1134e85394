<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<?mso-application progid="Word.Document"?>
<w:wordDocument w:embeddedObjPresent="no" w:macrosPresent="no" w:ocxPresent="no" xml:space="preserve" xmlns:aml="http://schemas.microsoft.com/aml/2001/core" xmlns:dt="uuid:C2F41010-65B3-11d1-A29F-00AA00C14882" xmlns:o="urn:schemas-microsoft-com:office:office" xmlns:sl="http://schemas.microsoft.com/schemaLibrary/2003/core" xmlns:v="urn:schemas-microsoft-com:vml" xmlns:w="http://schemas.microsoft.com/office/word/2003/wordml" xmlns:w10="urn:schemas-microsoft-com:office:word" xmlns:wx="http://schemas.microsoft.com/office/word/2003/auxHint">
	<o:DocumentProperties>
		<o:Author>lijin</o:Author>
		<o:LastAuthor>qzkj_syd</o:LastAuthor>
		<o:Revision>1</o:Revision>
		<o:Created>2021-03-02T02:26:00Z</o:Created>
		<o:LastSaved>2021-04-22T16:59:20Z</o:LastSaved>
		<o:Pages>1</o:Pages>
		<o:Words>0</o:Words>
		<o:Characters>0</o:Characters>
		<o:Lines>0</o:Lines>
		<o:Paragraphs>0</o:Paragraphs>
		<o:CharactersWithSpaces>0</o:CharactersWithSpaces>
	</o:DocumentProperties>
	<o:CustomDocumentProperties>
		<o:KSOProductBuildVer dt:dt="string">2052-3.1.1.4956</o:KSOProductBuildVer>
	</o:CustomDocumentProperties>
	<w:fonts>
		<w:defaultFonts w:ascii="Times New Roman" w:cs="Times New Roman" w:fareast="宋体" w:h-ansi="Times New Roman"/>
		<w:font w:name="Times New Roman">
			<w:panose-1 w:val="02020503050405090304"/>
			<w:charset w:val="00"/>
			<w:family w:val="Auto"/>
			<w:pitch w:val="Default"/>
			<w:sig w:csb-0="400001BF" w:csb-1="DFF70000" w:usb-0="E0000AFF" w:usb-1="00007843" w:usb-2="00000001" w:usb-3="00000000"/>
		</w:font>
		<w:font w:name="宋体">
			<w:altName w:val="汉仪书宋二KW"/>
			<w:panose-1 w:val="00000000000000000000"/>
			<w:charset w:val="00"/>
			<w:family w:val="Auto"/>
			<w:pitch w:val="Default"/>
			<w:sig w:csb-0="00000000" w:csb-1="00000000" w:usb-0="00000000" w:usb-1="00000000" w:usb-2="00000000" w:usb-3="00000000"/>
		</w:font>
		<w:font w:name="DejaVu Sans">
			<w:altName w:val="苹方-简"/>
			<w:panose-1 w:val="02020603050405020304"/>
			<w:charset w:val="00"/>
			<w:family w:val="Roman"/>
			<w:pitch w:val="Default"/>
			<w:sig w:csb-0="000001FF" w:csb-1="00000000" w:usb-0="20007A87" w:usb-1="80000000" w:usb-2="00000008" w:usb-3="00000000"/>
		</w:font>
		<w:font w:name="方正书宋_GBK">
			<w:panose-1 w:val="02000000000000000000"/>
			<w:charset w:val="86"/>
			<w:family w:val="Auto"/>
			<w:pitch w:val="Default"/>
			<w:sig w:csb-0="00040001" w:csb-1="00000000" w:usb-0="A00002BF" w:usb-1="38CF7CFA" w:usb-2="00082016" w:usb-3="00000000"/>
		</w:font>
		<w:font w:name="方正黑体_GBK">
			<w:altName w:val="苹方-简"/>
			<w:panose-1 w:val="02000000000000000000"/>
			<w:charset w:val="00"/>
			<w:family w:val="Auto"/>
			<w:pitch w:val="Default"/>
			<w:sig w:csb-0="00040000" w:csb-1="00000000" w:usb-0="00000001" w:usb-1="08000000" w:usb-2="00000000" w:usb-3="00000000"/>
		</w:font>
		<w:font w:name="Cambria">
			<w:altName w:val="苹方-简"/>
			<w:panose-1 w:val="02040503050406030204"/>
			<w:charset w:val="00"/>
			<w:family w:val="Roman"/>
			<w:pitch w:val="Default"/>
			<w:sig w:csb-0="0000019F" w:csb-1="00000000" w:usb-0="00000000" w:usb-1="00000000" w:usb-2="00000000" w:usb-3="00000000"/>
		</w:font>
		<w:font w:name="Calibri">
			<w:altName w:val="Helvetica Neue"/>
			<w:panose-1 w:val="020F0502020204030204"/>
			<w:charset w:val="00"/>
			<w:family w:val="SWiss"/>
			<w:pitch w:val="Default"/>
			<w:sig w:csb-0="0000019F" w:csb-1="00000000" w:usb-0="00000000" w:usb-1="00000000" w:usb-2="00000001" w:usb-3="00000000"/>
		</w:font>
		<w:font w:name="汉仪书宋二KW">
			<w:panose-1 w:val="00020600040101010101"/>
			<w:charset w:val="86"/>
			<w:family w:val="Auto"/>
			<w:pitch w:val="Default"/>
			<w:sig w:csb-0="00040000" w:csb-1="00000000" w:usb-0="A00002BF" w:usb-1="18EF7CFA" w:usb-2="00000016" w:usb-3="00000000"/>
		</w:font>
		<w:font w:name="苹方-简">
			<w:panose-1 w:val="020B0400000000000000"/>
			<w:charset w:val="86"/>
			<w:family w:val="Auto"/>
			<w:pitch w:val="Default"/>
			<w:sig w:csb-0="00040001" w:csb-1="00000000" w:usb-0="A00002FF" w:usb-1="7ACFFDFB" w:usb-2="00000017" w:usb-3="00000000"/>
		</w:font>
		<w:font w:name="Helvetica Neue">
			<w:panose-1 w:val="02000503000000020004"/>
			<w:charset w:val="00"/>
			<w:family w:val="Auto"/>
			<w:pitch w:val="Default"/>
			<w:sig w:csb-0="00000000" w:csb-1="00000000" w:usb-0="E50002FF" w:usb-1="500079DB" w:usb-2="00000010" w:usb-3="00000000"/>
		</w:font>
		<w:font w:name="方正黑体_GBK">
			<w:altName w:val="苹方-简"/>
			<w:panose-1 w:val="03000509000000000000"/>
			<w:charset w:val="86"/>
			<w:family w:val="Auto"/>
			<w:pitch w:val="Default"/>
			<w:sig w:csb-0="00040000" w:csb-1="00000000" w:usb-0="00000000" w:usb-1="00000000" w:usb-2="00000000" w:usb-3="00000000"/>
		</w:font>
		<w:font w:name="PingFangSC-Regular">
			<w:panose-1 w:val="020B0400000000000000"/>
			<w:charset w:val="86"/>
			<w:family w:val="Auto"/>
			<w:pitch w:val="Default"/>
			<w:sig w:csb-0="00040001" w:csb-1="00000000" w:usb-0="A00002FF" w:usb-1="7ACFFDFB" w:usb-2="00000017" w:usb-3="00000000"/>
		</w:font>
		<w:font w:name="宋体-简">
			<w:panose-1 w:val="02010800040101010101"/>
			<w:charset w:val="86"/>
			<w:family w:val="Auto"/>
			<w:pitch w:val="Default"/>
			<w:sig w:csb-0="00040000" w:csb-1="00000000" w:usb-0="00000001" w:usb-1="080F0000" w:usb-2="00000000" w:usb-3="00000000"/>
		</w:font>
		<w:font w:name="PingFangHK">
			<w:altName w:val="苹方-简"/>
			<w:panose-1 w:val="00000000000000000000"/>
			<w:charset w:val="00"/>
			<w:family w:val="Auto"/>
			<w:pitch w:val="Default"/>
			<w:sig w:csb-0="00000000" w:csb-1="00000000" w:usb-0="00000000" w:usb-1="00000000" w:usb-2="00000000" w:usb-3="00000000"/>
		</w:font>
		<w:font w:name="Tahoma">
			<w:panose-1 w:val="020B0604030504040204"/>
			<w:charset w:val="00"/>
			<w:family w:val="Auto"/>
			<w:pitch w:val="Default"/>
			<w:sig w:csb-0="200101FF" w:csb-1="20280000" w:usb-0="E1002AFF" w:usb-1="C000605B" w:usb-2="00000029" w:usb-3="00000000"/>
		</w:font>
		<w:font w:name="Menlo">
			<w:panose-1 w:val="020B0609030804020204"/>
			<w:charset w:val="00"/>
			<w:family w:val="Auto"/>
			<w:pitch w:val="Default"/>
			<w:sig w:csb-0="600001DF" w:csb-1="FFDF0000" w:usb-0="E60022FF" w:usb-1="D200F9FB" w:usb-2="02000028" w:usb-3="00000000"/>
		</w:font>
	</w:fonts>
	<w:styles>
		<w:latentStyles w:defLockedState="off" w:latentStyleCount="260">
			<w:lsdException w:name="Normal"/>
			<w:lsdException w:name="heading 1"/>
			<w:lsdException w:name="heading 2"/>
			<w:lsdException w:name="heading 3"/>
			<w:lsdException w:name="heading 4"/>
			<w:lsdException w:name="heading 5"/>
			<w:lsdException w:name="heading 6"/>
			<w:lsdException w:name="heading 7"/>
			<w:lsdException w:name="heading 8"/>
			<w:lsdException w:name="heading 9"/>
			<w:lsdException w:name="index 1"/>
			<w:lsdException w:name="index 2"/>
			<w:lsdException w:name="index 3"/>
			<w:lsdException w:name="index 4"/>
			<w:lsdException w:name="index 5"/>
			<w:lsdException w:name="index 6"/>
			<w:lsdException w:name="index 7"/>
			<w:lsdException w:name="index 8"/>
			<w:lsdException w:name="index 9"/>
			<w:lsdException w:name="toc 1"/>
			<w:lsdException w:name="toc 2"/>
			<w:lsdException w:name="toc 3"/>
			<w:lsdException w:name="toc 4"/>
			<w:lsdException w:name="toc 5"/>
			<w:lsdException w:name="toc 6"/>
			<w:lsdException w:name="toc 7"/>
			<w:lsdException w:name="toc 8"/>
			<w:lsdException w:name="toc 9"/>
			<w:lsdException w:name="Normal Indent"/>
			<w:lsdException w:name="footnote text"/>
			<w:lsdException w:name="annotation text"/>
			<w:lsdException w:name="header"/>
			<w:lsdException w:name="footer"/>
			<w:lsdException w:name="index heading"/>
			<w:lsdException w:name="caption"/>
			<w:lsdException w:name="table of figures"/>
			<w:lsdException w:name="envelope address"/>
			<w:lsdException w:name="envelope return"/>
			<w:lsdException w:name="footnote reference"/>
			<w:lsdException w:name="annotation reference"/>
			<w:lsdException w:name="line number"/>
			<w:lsdException w:name="page number"/>
			<w:lsdException w:name="endnote reference"/>
			<w:lsdException w:name="endnote text"/>
			<w:lsdException w:name="table of authorities"/>
			<w:lsdException w:name="macro"/>
			<w:lsdException w:name="toa heading"/>
			<w:lsdException w:name="List"/>
			<w:lsdException w:name="List Bullet"/>
			<w:lsdException w:name="List Number"/>
			<w:lsdException w:name="List 2"/>
			<w:lsdException w:name="List 3"/>
			<w:lsdException w:name="List 4"/>
			<w:lsdException w:name="List 5"/>
			<w:lsdException w:name="List Bullet 2"/>
			<w:lsdException w:name="List Bullet 3"/>
			<w:lsdException w:name="List Bullet 4"/>
			<w:lsdException w:name="List Bullet 5"/>
			<w:lsdException w:name="List Number 2"/>
			<w:lsdException w:name="List Number 3"/>
			<w:lsdException w:name="List Number 4"/>
			<w:lsdException w:name="List Number 5"/>
			<w:lsdException w:name="Title"/>
			<w:lsdException w:name="Closing"/>
			<w:lsdException w:name="Signature"/>
			<w:lsdException w:name="Default Paragraph Font"/>
			<w:lsdException w:name="Body Text"/>
			<w:lsdException w:name="Body Text Indent"/>
			<w:lsdException w:name="List Continue"/>
			<w:lsdException w:name="List Continue 2"/>
			<w:lsdException w:name="List Continue 3"/>
			<w:lsdException w:name="List Continue 4"/>
			<w:lsdException w:name="List Continue 5"/>
			<w:lsdException w:name="Message Header"/>
			<w:lsdException w:name="Subtitle"/>
			<w:lsdException w:name="Salutation"/>
			<w:lsdException w:name="Date"/>
			<w:lsdException w:name="Body Text First Indent"/>
			<w:lsdException w:name="Body Text First Indent 2"/>
			<w:lsdException w:name="Note Heading"/>
			<w:lsdException w:name="Body Text 2"/>
			<w:lsdException w:name="Body Text 3"/>
			<w:lsdException w:name="Body Text Indent 2"/>
			<w:lsdException w:name="Body Text Indent 3"/>
			<w:lsdException w:name="Block Text"/>
			<w:lsdException w:name="Hyperlink"/>
			<w:lsdException w:name="FollowedHyperlink"/>
			<w:lsdException w:name="Strong"/>
			<w:lsdException w:name="Emphasis"/>
			<w:lsdException w:name="Document Map"/>
			<w:lsdException w:name="Plain Text"/>
			<w:lsdException w:name="E-mail Signature"/>
			<w:lsdException w:name="Normal (Web)"/>
			<w:lsdException w:name="HTML Acronym"/>
			<w:lsdException w:name="HTML Address"/>
			<w:lsdException w:name="HTML Cite"/>
			<w:lsdException w:name="HTML Code"/>
			<w:lsdException w:name="HTML Definition"/>
			<w:lsdException w:name="HTML Keyboard"/>
			<w:lsdException w:name="HTML Preformatted"/>
			<w:lsdException w:name="HTML Sample"/>
			<w:lsdException w:name="HTML Typewriter"/>
			<w:lsdException w:name="HTML Variable"/>
			<w:lsdException w:name="Normal Table"/>
			<w:lsdException w:name="annotation subject"/>
			<w:lsdException w:name="Table Simple 1"/>
			<w:lsdException w:name="Table Simple 2"/>
			<w:lsdException w:name="Table Simple 3"/>
			<w:lsdException w:name="Table Classic 1"/>
			<w:lsdException w:name="Table Classic 2"/>
			<w:lsdException w:name="Table Classic 3"/>
			<w:lsdException w:name="Table Classic 4"/>
			<w:lsdException w:name="Table Colorful 1"/>
			<w:lsdException w:name="Table Colorful 2"/>
			<w:lsdException w:name="Table Colorful 3"/>
			<w:lsdException w:name="Table Columns 1"/>
			<w:lsdException w:name="Table Columns 2"/>
			<w:lsdException w:name="Table Columns 3"/>
			<w:lsdException w:name="Table Columns 4"/>
			<w:lsdException w:name="Table Columns 5"/>
			<w:lsdException w:name="Table Grid 1"/>
			<w:lsdException w:name="Table Grid 2"/>
			<w:lsdException w:name="Table Grid 3"/>
			<w:lsdException w:name="Table Grid 4"/>
			<w:lsdException w:name="Table Grid 5"/>
			<w:lsdException w:name="Table Grid 6"/>
			<w:lsdException w:name="Table Grid 7"/>
			<w:lsdException w:name="Table Grid 8"/>
			<w:lsdException w:name="Table List 1"/>
			<w:lsdException w:name="Table List 2"/>
			<w:lsdException w:name="Table List 3"/>
			<w:lsdException w:name="Table List 4"/>
			<w:lsdException w:name="Table List 5"/>
			<w:lsdException w:name="Table List 6"/>
			<w:lsdException w:name="Table List 7"/>
			<w:lsdException w:name="Table List 8"/>
			<w:lsdException w:name="Table 3D effects 1"/>
			<w:lsdException w:name="Table 3D effects 2"/>
			<w:lsdException w:name="Table 3D effects 3"/>
			<w:lsdException w:name="Table Contemporary"/>
			<w:lsdException w:name="Table Elegant"/>
			<w:lsdException w:name="Table Professional"/>
			<w:lsdException w:name="Table Subtle 1"/>
			<w:lsdException w:name="Table Subtle 2"/>
			<w:lsdException w:name="Table Web 1"/>
			<w:lsdException w:name="Table Web 2"/>
			<w:lsdException w:name="Table Web 3"/>
			<w:lsdException w:name="Balloon Text"/>
			<w:lsdException w:name="Table Grid"/>
			<w:lsdException w:name="Table Theme"/>
			<w:lsdException w:name="Light Shading"/>
			<w:lsdException w:name="Light List"/>
			<w:lsdException w:name="Light Grid"/>
			<w:lsdException w:name="Medium Shading 1"/>
			<w:lsdException w:name="Medium Shading 2"/>
			<w:lsdException w:name="Medium List 1"/>
			<w:lsdException w:name="Medium List 2"/>
			<w:lsdException w:name="Medium Grid 1"/>
			<w:lsdException w:name="Medium Grid 2"/>
			<w:lsdException w:name="Medium Grid 3"/>
			<w:lsdException w:name="Dark List"/>
			<w:lsdException w:name="Colorful Shading"/>
			<w:lsdException w:name="Colorful List"/>
			<w:lsdException w:name="Colorful Grid"/>
			<w:lsdException w:name="Light Shading Accent 1"/>
			<w:lsdException w:name="Light List Accent 1"/>
			<w:lsdException w:name="Light Grid Accent 1"/>
			<w:lsdException w:name="Medium Shading 1 Accent 1"/>
			<w:lsdException w:name="Medium Shading 2 Accent 1"/>
			<w:lsdException w:name="Medium List 1 Accent 1"/>
			<w:lsdException w:name="Medium List 2 Accent 1"/>
			<w:lsdException w:name="Medium Grid 1 Accent 1"/>
			<w:lsdException w:name="Medium Grid 2 Accent 1"/>
			<w:lsdException w:name="Medium Grid 3 Accent 1"/>
			<w:lsdException w:name="Dark List Accent 1"/>
			<w:lsdException w:name="Colorful Shading Accent 1"/>
			<w:lsdException w:name="Colorful List Accent 1"/>
			<w:lsdException w:name="Colorful Grid Accent 1"/>
			<w:lsdException w:name="Light Shading Accent 2"/>
			<w:lsdException w:name="Light List Accent 2"/>
			<w:lsdException w:name="Light Grid Accent 2"/>
			<w:lsdException w:name="Medium Shading 1 Accent 2"/>
			<w:lsdException w:name="Medium Shading 2 Accent 2"/>
			<w:lsdException w:name="Medium List 1 Accent 2"/>
			<w:lsdException w:name="Medium List 2 Accent 2"/>
			<w:lsdException w:name="Medium Grid 1 Accent 2"/>
			<w:lsdException w:name="Medium Grid 2 Accent 2"/>
			<w:lsdException w:name="Medium Grid 3 Accent 2"/>
			<w:lsdException w:name="Dark List Accent 2"/>
			<w:lsdException w:name="Colorful Shading Accent 2"/>
			<w:lsdException w:name="Colorful List Accent 2"/>
			<w:lsdException w:name="Colorful Grid Accent 2"/>
			<w:lsdException w:name="Light Shading Accent 3"/>
			<w:lsdException w:name="Light List Accent 3"/>
			<w:lsdException w:name="Light Grid Accent 3"/>
			<w:lsdException w:name="Medium Shading 1 Accent 3"/>
			<w:lsdException w:name="Medium Shading 2 Accent 3"/>
			<w:lsdException w:name="Medium List 1 Accent 3"/>
			<w:lsdException w:name="Medium List 2 Accent 3"/>
			<w:lsdException w:name="Medium Grid 1 Accent 3"/>
			<w:lsdException w:name="Medium Grid 2 Accent 3"/>
			<w:lsdException w:name="Medium Grid 3 Accent 3"/>
			<w:lsdException w:name="Dark List Accent 3"/>
			<w:lsdException w:name="Colorful Shading Accent 3"/>
			<w:lsdException w:name="Colorful List Accent 3"/>
			<w:lsdException w:name="Colorful Grid Accent 3"/>
			<w:lsdException w:name="Light Shading Accent 4"/>
			<w:lsdException w:name="Light List Accent 4"/>
			<w:lsdException w:name="Light Grid Accent 4"/>
			<w:lsdException w:name="Medium Shading 1 Accent 4"/>
			<w:lsdException w:name="Medium Shading 2 Accent 4"/>
			<w:lsdException w:name="Medium List 1 Accent 4"/>
			<w:lsdException w:name="Medium List 2 Accent 4"/>
			<w:lsdException w:name="Medium Grid 1 Accent 4"/>
			<w:lsdException w:name="Medium Grid 2 Accent 4"/>
			<w:lsdException w:name="Medium Grid 3 Accent 4"/>
			<w:lsdException w:name="Dark List Accent 4"/>
			<w:lsdException w:name="Colorful Shading Accent 4"/>
			<w:lsdException w:name="Colorful List Accent 4"/>
			<w:lsdException w:name="Colorful Grid Accent 4"/>
			<w:lsdException w:name="Light Shading Accent 5"/>
			<w:lsdException w:name="Light List Accent 5"/>
			<w:lsdException w:name="Light Grid Accent 5"/>
			<w:lsdException w:name="Medium Shading 1 Accent 5"/>
			<w:lsdException w:name="Medium Shading 2 Accent 5"/>
			<w:lsdException w:name="Medium List 1 Accent 5"/>
			<w:lsdException w:name="Medium List 2 Accent 5"/>
			<w:lsdException w:name="Medium Grid 1 Accent 5"/>
			<w:lsdException w:name="Medium Grid 2 Accent 5"/>
			<w:lsdException w:name="Medium Grid 3 Accent 5"/>
			<w:lsdException w:name="Dark List Accent 5"/>
			<w:lsdException w:name="Colorful Shading Accent 5"/>
			<w:lsdException w:name="Colorful List Accent 5"/>
			<w:lsdException w:name="Colorful Grid Accent 5"/>
			<w:lsdException w:name="Light Shading Accent 6"/>
			<w:lsdException w:name="Light List Accent 6"/>
			<w:lsdException w:name="Light Grid Accent 6"/>
			<w:lsdException w:name="Medium Shading 1 Accent 6"/>
			<w:lsdException w:name="Medium Shading 2 Accent 6"/>
			<w:lsdException w:name="Medium List 1 Accent 6"/>
			<w:lsdException w:name="Medium List 2 Accent 6"/>
			<w:lsdException w:name="Medium Grid 1 Accent 6"/>
			<w:lsdException w:name="Medium Grid 2 Accent 6"/>
			<w:lsdException w:name="Medium Grid 3 Accent 6"/>
			<w:lsdException w:name="Dark List Accent 6"/>
			<w:lsdException w:name="Colorful Shading Accent 6"/>
			<w:lsdException w:name="Colorful List Accent 6"/>
			<w:lsdException w:name="Colorful Grid Accent 6"/>
		</w:latentStyles>
		<w:style w:default="on" w:styleId="a1" w:type="paragraph">
			<w:name w:val="Normal"/>
			<w:pPr>
				<w:widowControl w:val="off"/>
				<w:jc w:val="both"/>
			</w:pPr>
			<w:rPr>
				<w:rFonts w:ascii="Calibri" w:cs="Times New Roman" w:fareast="宋体" w:h-ansi="Calibri" w:hint="default"/>
				<w:kern w:val="2"/>
				<w:sz w:val="21"/>
				<w:sz-cs w:val="24"/>
				<w:lang w:fareast="ZH-CN" w:val="EN-US"/>
			</w:rPr>
		</w:style>
		<w:style w:default="on" w:styleId="a3" w:type="character">
			<w:name w:val="Default Paragraph Font"/>
			<w:semiHidden/>
		</w:style>
		<w:style w:default="on" w:styleId="a5" w:type="table">
			<w:name w:val="Normal Table"/>
			<w:semiHidden/>
			<w:tblPr>
				<w:tblCellMar>
					<w:top w:type="dxa" w:w="0"/>
					<w:left w:type="dxa" w:w="108"/>
					<w:bottom w:type="dxa" w:w="0"/>
					<w:right w:type="dxa" w:w="108"/>
				</w:tblCellMar>
			</w:tblPr>
		</w:style>
		<w:style w:styleId="a2" w:type="paragraph">
			<w:name w:val="HTML Preformatted"/>
			<w:basedOn w:val="a1"/>
			<w:pPr>
				<w:tabs>
					<w:tab w:pos="916" w:val="left"/>
					<w:tab w:pos="1832" w:val="left"/>
					<w:tab w:pos="2748" w:val="left"/>
					<w:tab w:pos="3664" w:val="left"/>
					<w:tab w:pos="4580" w:val="left"/>
					<w:tab w:pos="5496" w:val="left"/>
					<w:tab w:pos="6412" w:val="left"/>
					<w:tab w:pos="7328" w:val="left"/>
					<w:tab w:pos="8244" w:val="left"/>
					<w:tab w:pos="9160" w:val="left"/>
					<w:tab w:pos="10076" w:val="left"/>
					<w:tab w:pos="10992" w:val="left"/>
					<w:tab w:pos="11908" w:val="left"/>
					<w:tab w:pos="12824" w:val="left"/>
					<w:tab w:pos="13740" w:val="left"/>
					<w:tab w:pos="14656" w:val="left"/>
				</w:tabs>
				<w:jc w:val="left"/>
			</w:pPr>
			<w:rPr>
				<w:rFonts w:ascii="宋体" w:cs="宋体" w:fareast="宋体" w:h-ansi="宋体" w:hint="fareast"/>
				<w:kern w:val="0"/>
				<w:sz w:val="24"/>
				<w:sz-cs w:val="24"/>
				<w:lang w:bidi="AR-SA" w:fareast="ZH-CN" w:val="EN-US"/>
			</w:rPr>
		</w:style>
		<w:style w:styleId="a4" w:type="character">
			<w:name w:val="Hyperlink"/>
			<w:basedOn w:val="a3"/>
			<w:rPr>
				<w:color w:val="0000FF"/>
				<w:u w:val="single"/>
			</w:rPr>
		</w:style>
		<w:style w:styleId="a6" w:type="table">
			<w:name w:val="Table Grid"/>
			<w:basedOn w:val="a5"/>
			<w:pPr>
				<w:pStyle w:val="a5"/>
				<w:widowControl w:val="off"/>
				<w:jc w:val="both"/>
			</w:pPr>
			<w:tblPr>
				<w:tblBorders>
					<w:top w:color="auto" w:space="0" w:sz="4" w:val="single" wx:bdrwidth="10"/>
					<w:left w:color="auto" w:space="0" w:sz="4" w:val="single" wx:bdrwidth="10"/>
					<w:bottom w:color="auto" w:space="0" w:sz="4" w:val="single" wx:bdrwidth="10"/>
					<w:right w:color="auto" w:space="0" w:sz="4" w:val="single" wx:bdrwidth="10"/>
					<w:insideH w:color="auto" w:space="0" w:sz="4" w:val="single" wx:bdrwidth="10"/>
					<w:insideV w:color="auto" w:space="0" w:sz="4" w:val="single" wx:bdrwidth="10"/>
				</w:tblBorders>
			</w:tblPr>
		</w:style>
	</w:styles>
	<w:bgPict>
		<w:background/>
		<v:background id="_x0000_s1025">
			<v:fill focussize="0,0" on="f"/>
		</v:background>
	</w:bgPict>
	<w:docPr>
		<w:view w:val="print"/>
		<w:zoom w:percent="167"/>
		<w:characterSpacingControl w:val="CompressPunctuation"/>
		<w:documentProtection w:enforcement="off"/>
		<w:defaultTabStop w:val="420"/>
		<w:drawingGridVerticalSpacing w:val="156"/>
		<w:displayHorizontalDrawingGridEvery w:val="1"/>
		<w:displayVerticalDrawingGridEvery w:val="1"/>
		<w:compat>
			<w:adjustLineHeightInTable/>
			<w:doNotExpandShiftReturn/>
			<w:balanceSingleByteDoubleByteWidth/>
			<w:useFELayout/>
			<w:spaceForUL/>
			<w:breakWrappedTables/>
			<w:dontGrowAutofit/>
			<w:useFELayout/>
		</w:compat>
	</w:docPr>
	<w:body>

		<wx:sect>
			<w:p>
				<w:pPr>
					<w:rPr>
						<w:vertAlign w:val="baseline"/>
					</w:rPr>
				</w:pPr>
			</w:p>


			<#if hostList?? && (hostList?size >0)>

			<w:p>
				<w:pPr>
					<w:rPr>
						<w:vertAlign w:val="baseline"/>
					</w:rPr>
				</w:pPr>
				<w:r>
					<w:rPr>
						<w:vertAlign w:val="baseline"/>
					</w:rPr>
					<w:t/>
				</w:r>
				<w:r>
					<w:rPr>
						<w:rFonts w:hint="fareast"/>
						<w:sz w:val="44"/>
						<w:sz-cs w:val="44"/>
						<w:vertAlign w:val="baseline"/>
						<w:lang w:val="EN-US"/>
					</w:rPr>
					<w:t>应用列表</w:t>
				</w:r>
			</w:p>
			<w:p/>

			<w:tbl>
				<w:tblPr>
					<w:tblStyle w:val="a6"/>
					<w:tblW w:type="dxa" w:w="8521"/>
					<w:tblInd w:type="dxa" w:w="0"/>
					<w:tblBorders>
						<w:top w:color="auto" w:space="0" w:sz="4" w:val="single" wx:bdrwidth="10"/>
						<w:left w:color="auto" w:space="0" w:sz="4" w:val="single" wx:bdrwidth="10"/>
						<w:bottom w:color="auto" w:space="0" w:sz="4" w:val="single" wx:bdrwidth="10"/>
						<w:right w:color="auto" w:space="0" w:sz="4" w:val="single" wx:bdrwidth="10"/>
						<w:insideH w:color="auto" w:space="0" w:sz="4" w:val="single" wx:bdrwidth="10"/>
						<w:insideV w:color="auto" w:space="0" w:sz="4" w:val="single" wx:bdrwidth="10"/>
					</w:tblBorders>
					<w:tblCellMar>
						<w:top w:type="dxa" w:w="0"/>
						<w:left w:type="dxa" w:w="108"/>
						<w:bottom w:type="dxa" w:w="0"/>
						<w:right w:type="dxa" w:w="108"/>
					</w:tblCellMar>
				</w:tblPr>
				<w:tblGrid>
					<w:gridCol w:w="1459"/>
					<w:gridCol w:w="4349"/>
					<w:gridCol w:w="2713"/>
				</w:tblGrid>

				<#list hostList as hostInfo>
				<w:tr>
					<w:tblPrEx>
						<w:tblBorders>
							<w:top w:color="auto" w:space="0" w:sz="4" w:val="single" wx:bdrwidth="10"/>
							<w:left w:color="auto" w:space="0" w:sz="4" w:val="single" wx:bdrwidth="10"/>
							<w:bottom w:color="auto" w:space="0" w:sz="4" w:val="single" wx:bdrwidth="10"/>
							<w:right w:color="auto" w:space="0" w:sz="4" w:val="single" wx:bdrwidth="10"/>
							<w:insideH w:color="auto" w:space="0" w:sz="4" w:val="single" wx:bdrwidth="10"/>
							<w:insideV w:color="auto" w:space="0" w:sz="4" w:val="single" wx:bdrwidth="10"/>
						</w:tblBorders>
						<w:tblCellMar>
							<w:top w:type="dxa" w:w="0"/>
							<w:left w:type="dxa" w:w="108"/>
							<w:bottom w:type="dxa" w:w="0"/>
							<w:right w:type="dxa" w:w="108"/>
						</w:tblCellMar>
					</w:tblPrEx>
					<w:trPr>
						<w:trHeight w:h-rule="atLeast" w:val="3821"/>
					</w:trPr>
					<w:tc>
						<w:tcPr>
							<w:tcW w:type="dxa" w:w="1459"/>
							<w:shd w:color="auto" w:fill="auto" w:val="clear"/>
							<w:vAlign w:val="top"/>
						</w:tcPr>
						<w:p>
							<w:pPr>
								<w:keepNext w:val="off"/>
								<w:keepLines w:val="off"/>
								<w:widowControl/>
								<w:supressLineNumbers w:val="off"/>
								<w:jc w:val="both"/>
								<w:rPr>
									<w:sz w:val="21"/>
									<w:sz-cs w:val="21"/>
								</w:rPr>
							</w:pPr>
							<w:r>
								<w:rPr>
									<w:sz w:val="21"/>
									<w:sz-cs w:val="21"/>
								</w:rPr>
								<w:t>${hostInfo.host?html}</w:t>
							</w:r>
						</w:p>
						<w:p>
							<w:pPr>
								<w:jc w:val="both"/>
								<w:rPr>
									<w:sz w:val="21"/>
									<w:sz-cs w:val="21"/>
									<w:vertAlign w:val="baseline"/>
								</w:rPr>
							</w:pPr>
						</w:p>
					</w:tc>
					<w:tc>
						<w:tcPr>
							<w:tcW w:type="dxa" w:w="4349"/>
							<w:shd w:color="auto" w:fill="auto" w:val="clear"/>
							<w:vAlign w:val="top"/>
						</w:tcPr>
						<w:p>
							<w:pPr>
								<w:pStyle w:val="a2"/>
								<w:keepNext w:val="off"/>
								<w:keepLines w:val="off"/>
								<w:widowControl/>
								<w:supressLineNumbers w:val="off"/>
								<w:shd w:color="auto" w:fill="FFFFFF" w:val="clear"/>
								<w:rPr>
									<w:sz w:val="21"/>
									<w:sz-cs w:val="21"/>
									<w:vertAlign w:val="baseline"/>
								</w:rPr>
							</w:pPr>
							<w:r>
								<w:rPr>
									<w:sz w:val="21"/>
									<w:sz-cs w:val="21"/>
									<w:vertAlign w:val="baseline"/>
								</w:rPr>
								<w:t>接口数量：</w:t>
							</w:r>
							<w:r>
								<w:rPr>
									<w:sz w:val="21"/>
									<w:sz-cs w:val="21"/>
									<w:vertAlign w:val="baseline"/>
								</w:rPr>
								<w:t>${hostInfo.apiCount}</w:t>
							</w:r>
						</w:p>
						<w:p>
							<w:pPr>
								<w:jc w:val="both"/>
								<w:rPr>
									<w:sz w:val="21"/>
									<w:sz-cs w:val="21"/>
									<w:vertAlign w:val="baseline"/>
								</w:rPr>
							</w:pPr>
							<w:r>
								<w:rPr>
									<w:sz w:val="21"/>
									<w:sz-cs w:val="21"/>
									<w:vertAlign w:val="baseline"/>
								</w:rPr>
								<w:t>弱点总数：</w:t>
							</w:r>
							<w:r>
								<w:rPr>
									<w:sz w:val="21"/>
									<w:sz-cs w:val="21"/>
									<w:vertAlign w:val="baseline"/>
								</w:rPr>
								<w:t>${hostInfo.riskCount.total}</w:t>
							</w:r>
						</w:p>
						<w:p>
							<w:pPr>
								<w:jc w:val="both"/>
								<w:rPr>
									<w:sz w:val="21"/>
									<w:sz-cs w:val="21"/>
									<w:vertAlign w:val="baseline"/>
								</w:rPr>
							</w:pPr>
							<w:r>
								<w:rPr>
									<w:sz w:val="21"/>
									<w:sz-cs w:val="21"/>
									<w:vertAlign w:val="baseline"/>
								</w:rPr>
								<w:t>高危弱点数：</w:t>
							</w:r>
							<w:r>
								<w:rPr>
									<w:sz w:val="21"/>
									<w:sz-cs w:val="21"/>
									<w:vertAlign w:val="baseline"/>
								</w:rPr>
								<w:t>${hostInfo.riskCount.h}</w:t>
							</w:r>
						</w:p>
						<w:p>
							<w:pPr>
								<w:jc w:val="both"/>
								<w:rPr>
									<w:sz w:val="21"/>
									<w:sz-cs w:val="21"/>
									<w:vertAlign w:val="baseline"/>
								</w:rPr>
							</w:pPr>
							<w:r>
								<w:rPr>
									<w:sz w:val="21"/>
									<w:sz-cs w:val="21"/>
									<w:vertAlign w:val="baseline"/>
								</w:rPr>
								<w:t>中危弱点数：</w:t>
							</w:r>
							<w:r>
								<w:rPr>
									<w:sz w:val="21"/>
									<w:sz-cs w:val="21"/>
									<w:vertAlign w:val="baseline"/>
								</w:rPr>
								<w:t>${hostInfo.riskCount.m}</w:t>
							</w:r>

						</w:p>
						<w:p>
							<w:pPr>
								<w:jc w:val="both"/>
								<w:rPr>
									<w:sz w:val="21"/>
									<w:sz-cs w:val="21"/>
									<w:vertAlign w:val="baseline"/>
								</w:rPr>
							</w:pPr>
							<w:r>
								<w:rPr>
									<w:sz w:val="21"/>
									<w:sz-cs w:val="21"/>
									<w:vertAlign w:val="baseline"/>
								</w:rPr>
								<w:t>低危弱点数：</w:t>
							</w:r>
							<w:r>
								<w:rPr>
									<w:sz w:val="21"/>
									<w:sz-cs w:val="21"/>
									<w:vertAlign w:val="baseline"/>
								</w:rPr>
								<w:t>${hostInfo.riskCount.l}</w:t>
							</w:r>

						</w:p>
						<w:p>
							<w:pPr>
								<w:jc w:val="both"/>
								<w:rPr>
									<w:sz w:val="21"/>
									<w:sz-cs w:val="21"/>
									<w:vertAlign w:val="baseline"/>
								</w:rPr>
							</w:pPr>
							<w:r>
								<w:rPr>
									<w:sz w:val="21"/>
									<w:sz-cs w:val="21"/>
									<w:vertAlign w:val="baseline"/>
								</w:rPr>
								<w:t>敏感应用：</w:t>
							</w:r>
							<w:r>
								<w:rPr>
									<w:sz w:val="21"/>
									<w:sz-cs w:val="21"/>
									<w:vertAlign w:val="baseline"/>
								</w:rPr>
								<w:t>${hostInfo.monitorText}</w:t>
							</w:r>
						</w:p>
						<w:p>
							<w:pPr>
								<w:jc w:val="both"/>
								<w:rPr>
									<w:sz w:val="21"/>
									<w:sz-cs w:val="21"/>
									<w:vertAlign w:val="baseline"/>
								</w:rPr>
							</w:pPr>
							<w:r>
								<w:rPr>
									<w:sz w:val="21"/>
									<w:sz-cs w:val="21"/>
									<w:vertAlign w:val="baseline"/>
								</w:rPr>
								<w:t>访问域标签：</w:t>
							</w:r>
							<w:r>
								<w:rPr>
									<w:sz w:val="21"/>
									<w:sz-cs w:val="21"/>
									<w:vertAlign w:val="baseline"/>
								</w:rPr>
								<w:t></w:t>
							</w:r>
							<w:r>
								<w:rPr>
									<w:rFonts w:hint="default"/>
									<w:sz w:val="21"/>
									<w:sz-cs w:val="21"/>
									<w:vertAlign w:val="baseline"/>
								</w:rPr>
								<w:t>${hostInfo.srcIpLabel?join(",")}</w:t>
							</w:r>

						</w:p>
						<w:p>
							<w:pPr>
								<w:jc w:val="both"/>
								<w:rPr>
									<w:sz w:val="21"/>
									<w:sz-cs w:val="21"/>
									<w:vertAlign w:val="baseline"/>
								</w:rPr>
							</w:pPr>
							<w:r>
								<w:rPr>
									<w:sz w:val="21"/>
									<w:sz-cs w:val="21"/>
									<w:vertAlign w:val="baseline"/>
								</w:rPr>
								<w:t>终端标签：</w:t>
							</w:r>

							<w:r>
								<w:rPr>
									<w:rFonts w:hint="default"/>
									<w:sz w:val="21"/>
									<w:sz-cs w:val="21"/>
									<w:vertAlign w:val="baseline"/>
								</w:rPr>
								<w:t>${hostInfo.uaLabel?join(",")}</w:t>
							</w:r>

						</w:p>
						<w:p>
							<w:pPr>
								<w:pStyle w:val="a2"/>
								<w:keepNext w:val="off"/>
								<w:keepLines w:val="off"/>
								<w:widowControl/>
								<w:supressLineNumbers w:val="off"/>
								<w:shd w:color="auto" w:fill="FFFFFF" w:val="clear"/>
								<w:rPr>
									<w:sz w:val="21"/>
									<w:sz-cs w:val="21"/>
									<w:vertAlign w:val="baseline"/>
								</w:rPr>
							</w:pPr>
							<w:r>
								<w:rPr>
									<w:sz w:val="21"/>
									<w:sz-cs w:val="21"/>
									<w:vertAlign w:val="baseline"/>
								</w:rPr>
								<w:t>最大返回数据量：</w:t>
							</w:r>
							<w:r>
								<w:rPr>
									<w:sz w:val="21"/>
									<w:sz-cs w:val="21"/>
									<w:vertAlign w:val="baseline"/>
								</w:rPr>
								<w:t>${hostInfo.maxRspLabelValueCount}</w:t>
							</w:r>
						</w:p>
						<w:p>
							<w:pPr>
								<w:jc w:val="both"/>
								<w:rPr>
									<w:sz w:val="21"/>
									<w:sz-cs w:val="21"/>
									<w:vertAlign w:val="baseline"/>
								</w:rPr>
							</w:pPr>
							<w:r>
								<w:rPr>
									<w:sz w:val="21"/>
									<w:sz-cs w:val="21"/>
									<w:vertAlign w:val="baseline"/>
								</w:rPr>
								<w:t>最大返回标签个数：</w:t>
							</w:r>
							<w:r>
								<w:rPr>
									<w:sz w:val="21"/>
									<w:sz-cs w:val="21"/>
									<w:vertAlign w:val="baseline"/>
								</w:rPr>
								<w:t>${hostInfo.maxRspLabelSubCount}</w:t>
							</w:r>
						</w:p>
					</w:tc>
					<w:tc>
						<w:tcPr>
							<w:tcW w:type="dxa" w:w="2713"/>
							<w:shd w:color="auto" w:fill="auto" w:val="clear"/>
							<w:vAlign w:val="top"/>
						</w:tcPr>
						<w:p>
							<w:pPr>
								<w:keepNext w:val="off"/>
								<w:keepLines w:val="off"/>
								<w:widowControl/>
								<w:supressLineNumbers w:val="off"/>
								<w:jc w:val="both"/>
								<w:rPr>
									<w:sz w:val="21"/>
									<w:sz-cs w:val="21"/>
									<w:vertAlign w:val="baseline"/>
								</w:rPr>
							</w:pPr>
							<w:r>
								<w:rPr>
									<w:sz w:val="21"/>
									<w:sz-cs w:val="21"/>
									<w:vertAlign w:val="baseline"/>
								</w:rPr>
								<w:t>数据标签：</w:t>
							</w:r>

							<w:r>
								<w:rPr>
									<w:rFonts w:hint="default"/>
									<w:sz w:val="21"/>
									<w:sz-cs w:val="21"/>
									<w:vertAlign w:val="baseline"/>
								</w:rPr>
								<w:t>${hostInfo.dataLabels?join(",")}</w:t>
							</w:r>

						</w:p>
					</w:tc>
				</w:tr>
				 </#list>
			</w:tbl>

			</#if>

            <#if apiList?? && (apiList?size >0)>

			<w:p>
				<w:pPr>
					<w:rPr>
						<w:vertAlign w:val="baseline"/>
					</w:rPr>
				</w:pPr>
				<w:r>
					<w:rPr>
						<w:vertAlign w:val="baseline"/>
					</w:rPr>
					<w:t/>
				</w:r>
				<w:r>
					<w:rPr>
						<w:rFonts w:hint="fareast"/>
						<w:sz w:val="44"/>
						<w:sz-cs w:val="44"/>
						<w:vertAlign w:val="baseline"/>
						<w:lang w:val="EN-US"/>
					</w:rPr>
					<w:t>API列表</w:t>
				</w:r>
			</w:p>
			<w:p/>

			<w:tbl>
				<w:tblPr>
					<w:tblStyle w:val="a6"/>
					<w:tblW w:type="dxa" w:w="8521"/>
					<w:tblInd w:type="dxa" w:w="0"/>
					<w:tblBorders>
						<w:top w:color="auto" w:space="0" w:sz="4" w:val="single" wx:bdrwidth="10"/>
						<w:left w:color="auto" w:space="0" w:sz="4" w:val="single" wx:bdrwidth="10"/>
						<w:bottom w:color="auto" w:space="0" w:sz="4" w:val="single" wx:bdrwidth="10"/>
						<w:right w:color="auto" w:space="0" w:sz="4" w:val="single" wx:bdrwidth="10"/>
						<w:insideH w:color="auto" w:space="0" w:sz="4" w:val="single" wx:bdrwidth="10"/>
						<w:insideV w:color="auto" w:space="0" w:sz="4" w:val="single" wx:bdrwidth="10"/>
					</w:tblBorders>
					<w:tblCellMar>
						<w:top w:type="dxa" w:w="0"/>
						<w:left w:type="dxa" w:w="108"/>
						<w:bottom w:type="dxa" w:w="0"/>
						<w:right w:type="dxa" w:w="108"/>
					</w:tblCellMar>
				</w:tblPr>
				<w:tblGrid>
					<w:gridCol w:w="1459"/>
					<w:gridCol w:w="4349"/>
					<w:gridCol w:w="2713"/>
				</w:tblGrid>

				<#list apiList as apiInfo>
				<w:tr>
					<w:tblPrEx>
						<w:tblBorders>
							<w:top w:color="auto" w:space="0" w:sz="4" w:val="single" wx:bdrwidth="10"/>
							<w:left w:color="auto" w:space="0" w:sz="4" w:val="single" wx:bdrwidth="10"/>
							<w:bottom w:color="auto" w:space="0" w:sz="4" w:val="single" wx:bdrwidth="10"/>
							<w:right w:color="auto" w:space="0" w:sz="4" w:val="single" wx:bdrwidth="10"/>
							<w:insideH w:color="auto" w:space="0" w:sz="4" w:val="single" wx:bdrwidth="10"/>
							<w:insideV w:color="auto" w:space="0" w:sz="4" w:val="single" wx:bdrwidth="10"/>
						</w:tblBorders>
						<w:tblCellMar>
							<w:top w:type="dxa" w:w="0"/>
							<w:left w:type="dxa" w:w="108"/>
							<w:bottom w:type="dxa" w:w="0"/>
							<w:right w:type="dxa" w:w="108"/>
						</w:tblCellMar>
					</w:tblPrEx>
					<w:trPr>
						<w:trHeight w:h-rule="atLeast" w:val="3821"/>
					</w:trPr>
					<w:tc>
						<w:tcPr>
							<w:tcW w:type="dxa" w:w="1459"/>
							<w:shd w:color="auto" w:fill="auto" w:val="clear"/>
							<w:vAlign w:val="top"/>
						</w:tcPr>
						<w:p>
							<w:pPr>
								<w:keepNext w:val="off"/>
								<w:keepLines w:val="off"/>
								<w:widowControl/>
								<w:supressLineNumbers w:val="off"/>
								<w:jc w:val="both"/>
								<w:rPr>
									<w:sz w:val="21"/>
									<w:sz-cs w:val="21"/>
								</w:rPr>
							</w:pPr>
							<w:r>
								<w:rPr>
									<w:sz w:val="21"/>
									<w:sz-cs w:val="21"/>
								</w:rPr>
								<w:t>${apiInfo.apiUrl?html}</w:t>
							</w:r>
						</w:p>
						<w:p>
							<w:pPr>
								<w:jc w:val="both"/>
								<w:rPr>
									<w:sz w:val="21"/>
									<w:sz-cs w:val="21"/>
									<w:vertAlign w:val="baseline"/>
								</w:rPr>
							</w:pPr>
						</w:p>
					</w:tc>
					<w:tc>
						<w:tcPr>
							<w:tcW w:type="dxa" w:w="4349"/>
							<w:shd w:color="auto" w:fill="auto" w:val="clear"/>
							<w:vAlign w:val="top"/>
						</w:tcPr>
						<w:p>
							<w:pPr>
								<w:jc w:val="both"/>
								<w:rPr>
									<w:sz w:val="21"/>
									<w:sz-cs w:val="21"/>
									<w:vertAlign w:val="baseline"/>
								</w:rPr>
							</w:pPr>
							<w:r>
								<w:rPr>
									<w:rFonts w:hint="fareast"/>
									<w:sz w:val="21"/>
									<w:sz-cs w:val="21"/>
									<w:vertAlign w:val="baseline"/>
									<w:lang w:val="EN-US"/>
								</w:rPr>
								<w:t>访问域标签:</w:t>
							</w:r>

							<w:r>
								<w:rPr>
									<w:sz w:val="21"/>
									<w:sz-cs w:val="21"/>
									<w:vertAlign w:val="baseline"/>
								</w:rPr>
								<w:t>${apiInfo.visitDomains?join(",")}</w:t>
							</w:r>
						</w:p>
						<w:p>
							<w:pPr>
								<w:jc w:val="both"/>
								<w:rPr>
									<w:sz w:val="21"/>
									<w:sz-cs w:val="21"/>
									<w:vertAlign w:val="baseline"/>
								</w:rPr>
							</w:pPr>
							<w:r>
								<w:rPr>
									<w:sz w:val="21"/>
									<w:sz-cs w:val="21"/>
									<w:vertAlign w:val="baseline"/>
								</w:rPr>
								<w:t>终端标签：</w:t>
							</w:r>
							<w:r>
								<w:rPr>
									<w:sz w:val="21"/>
									<w:sz-cs w:val="21"/>
									<w:vertAlign w:val="baseline"/>
								</w:rPr>
								<#if apiInfo.termials?exists>
								<w:t>${apiInfo.termials?join(",")}</w:t>
								</#if>
							</w:r>
						</w:p>
						<w:p>
							<w:pPr>
								<w:jc w:val="both"/>
								<w:rPr>
									<w:sz w:val="21"/>
									<w:sz-cs w:val="21"/>
									<w:vertAlign w:val="baseline"/>
								</w:rPr>
							</w:pPr>
							<w:r>
								<w:rPr>
									<w:sz w:val="21"/>
									<w:sz-cs w:val="21"/>
									<w:vertAlign w:val="baseline"/>
								</w:rPr>
								<w:t>最大返回数据量：</w:t>
							</w:r>
							<w:r>
								<w:rPr>
									<w:sz w:val="21"/>
									<w:sz-cs w:val="21"/>
									<w:vertAlign w:val="baseline"/>
								</w:rPr>
								<w:t>${apiInfo.apiStat.maxReqLabelValueCount}</w:t>
							</w:r>
						</w:p>
						<w:p>
							<w:pPr>
								<w:jc w:val="both"/>
								<w:rPr>
									<w:sz w:val="21"/>
									<w:sz-cs w:val="21"/>
									<w:vertAlign w:val="baseline"/>
								</w:rPr>
							</w:pPr>
							<w:r>
								<w:rPr>
									<w:sz w:val="21"/>
									<w:sz-cs w:val="21"/>
									<w:vertAlign w:val="baseline"/>
								</w:rPr>
								<w:t>最大返回标签个数:</w:t>
							</w:r>
							<w:r>
								<w:rPr>
									<w:sz w:val="21"/>
									<w:sz-cs w:val="21"/>
									<w:vertAlign w:val="baseline"/>
								</w:rPr>
								<w:t>${apiInfo.apiStat.maxReqDupLabelCount}</w:t>
							</w:r>

						</w:p>


					</w:tc>
					<w:tc>
						<w:tcPr>
							<w:tcW w:type="dxa" w:w="2713"/>
							<w:shd w:color="auto" w:fill="auto" w:val="clear"/>
							<w:vAlign w:val="top"/>
						</w:tcPr>
						<w:p>
							<w:pPr>
								<w:keepNext w:val="off"/>
								<w:keepLines w:val="off"/>
								<w:widowControl/>
								<w:supressLineNumbers w:val="off"/>
								<w:jc w:val="both"/>
								<w:rPr>
									<w:sz w:val="21"/>
									<w:sz-cs w:val="21"/>
									<w:vertAlign w:val="baseline"/>
								</w:rPr>
							</w:pPr>
							<w:r>
								<w:rPr>
									<w:sz w:val="21"/>
									<w:sz-cs w:val="21"/>
									<w:vertAlign w:val="baseline"/>
								</w:rPr>
								<w:t>请求数据标签：</w:t>
							</w:r>

							<w:r>
								<w:rPr>
									<w:rFonts w:hint="default"/>
									<w:sz w:val="21"/>
									<w:sz-cs w:val="21"/>
									<w:vertAlign w:val="baseline"/>
								</w:rPr>
								<#if apiInfo.reqDataLabels?exists>
								<w:t>${apiInfo.reqDataLabels?join(",")}</w:t>
								</#if>

							</w:r>

						</w:p>
						<w:p>
							<w:pPr>
								<w:keepNext w:val="off"/>
								<w:keepLines w:val="off"/>
								<w:widowControl/>
								<w:supressLineNumbers w:val="off"/>
								<w:jc w:val="both"/>
								<w:rPr>
									<w:sz w:val="21"/>
									<w:sz-cs w:val="21"/>
									<w:vertAlign w:val="baseline"/>
								</w:rPr>
							</w:pPr>
							<w:r>
								<w:rPr>
									<w:sz w:val="21"/>
									<w:sz-cs w:val="21"/>
									<w:vertAlign w:val="baseline"/>
								</w:rPr>
								<w:t>返回数据标签：</w:t>
							</w:r>

							<w:r>
								<w:rPr>
									<w:rFonts w:hint="default"/>
									<w:sz w:val="21"/>
									<w:sz-cs w:val="21"/>
									<w:vertAlign w:val="baseline"/>
								</w:rPr>
								<w:t>${apiInfo.rspDataLabels?join(",")}</w:t>
							</w:r>

						</w:p>
					</w:tc>
				</w:tr>
				 </#list>
			</w:tbl>

            </#if>

            <#if weaknessList?? && (weaknessList?size >0)>

			<w:p>
				<w:pPr>
					<w:rPr>
						<w:vertAlign w:val="baseline"/>
					</w:rPr>
				</w:pPr>
				<w:r>
					<w:rPr>
						<w:vertAlign w:val="baseline"/>
					</w:rPr>
					<w:t/>
				</w:r>
				<w:r>
					<w:rPr>
						<w:rFonts w:hint="fareast"/>
						<w:sz w:val="44"/>
						<w:sz-cs w:val="44"/>
						<w:vertAlign w:val="baseline"/>
						<w:lang w:val="EN-US"/>
					</w:rPr>
					<w:t>弱点列表</w:t>
				</w:r>
			</w:p>
			<w:p/>

			<w:tbl>
				<w:tblPr>
					<w:tblStyle w:val="a6"/>
					<w:tblW w:type="dxa" w:w="8521"/>
					<w:tblInd w:type="dxa" w:w="0"/>
					<w:tblBorders>
						<w:top w:color="auto" w:space="0" w:sz="4" w:val="single" wx:bdrwidth="10"/>
						<w:left w:color="auto" w:space="0" w:sz="4" w:val="single" wx:bdrwidth="10"/>
						<w:bottom w:color="auto" w:space="0" w:sz="4" w:val="single" wx:bdrwidth="10"/>
						<w:right w:color="auto" w:space="0" w:sz="4" w:val="single" wx:bdrwidth="10"/>
						<w:insideH w:color="auto" w:space="0" w:sz="4" w:val="single" wx:bdrwidth="10"/>
						<w:insideV w:color="auto" w:space="0" w:sz="4" w:val="single" wx:bdrwidth="10"/>
					</w:tblBorders>
					<w:tblCellMar>
						<w:top w:type="dxa" w:w="0"/>
						<w:left w:type="dxa" w:w="108"/>
						<w:bottom w:type="dxa" w:w="0"/>
						<w:right w:type="dxa" w:w="108"/>
					</w:tblCellMar>
				</w:tblPr>
				<w:tblGrid>
					<w:gridCol w:w="1459"/>
					<w:gridCol w:w="4349"/>
					<w:gridCol w:w="2713"/>
				</w:tblGrid>

				<#list weaknessList as weaknessInfo>
				<w:tr>
					<w:tblPrEx>
						<w:tblBorders>
							<w:top w:color="auto" w:space="0" w:sz="4" w:val="single" wx:bdrwidth="10"/>
							<w:left w:color="auto" w:space="0" w:sz="4" w:val="single" wx:bdrwidth="10"/>
							<w:bottom w:color="auto" w:space="0" w:sz="4" w:val="single" wx:bdrwidth="10"/>
							<w:right w:color="auto" w:space="0" w:sz="4" w:val="single" wx:bdrwidth="10"/>
							<w:insideH w:color="auto" w:space="0" w:sz="4" w:val="single" wx:bdrwidth="10"/>
							<w:insideV w:color="auto" w:space="0" w:sz="4" w:val="single" wx:bdrwidth="10"/>
						</w:tblBorders>
						<w:tblCellMar>
							<w:top w:type="dxa" w:w="0"/>
							<w:left w:type="dxa" w:w="108"/>
							<w:bottom w:type="dxa" w:w="0"/>
							<w:right w:type="dxa" w:w="108"/>
						</w:tblCellMar>
					</w:tblPrEx>
					<w:trPr>
						<w:trHeight w:h-rule="atLeast" w:val="3821"/>
					</w:trPr>
					<w:tc>
						<w:tcPr>
							<w:tcW w:type="dxa" w:w="1459"/>
							<w:shd w:color="auto" w:fill="auto" w:val="clear"/>
							<w:vAlign w:val="top"/>
						</w:tcPr>
						<w:p>
							<w:pPr>
								<w:keepNext w:val="off"/>
								<w:keepLines w:val="off"/>
								<w:widowControl/>
								<w:supressLineNumbers w:val="off"/>
								<w:jc w:val="both"/>
								<w:rPr>
									<w:sz w:val="21"/>
									<w:sz-cs w:val="21"/>
								</w:rPr>
							</w:pPr>
							<w:r>
								<w:rPr>
									<w:sz w:val="21"/>
									<w:sz-cs w:val="21"/>
								</w:rPr>
								<w:t>${weaknessInfo.apiUrl?html}</w:t>
							</w:r>
						</w:p>
						<w:p>
							<w:pPr>
								<w:jc w:val="both"/>
								<w:rPr>
									<w:sz w:val="21"/>
									<w:sz-cs w:val="21"/>
									<w:vertAlign w:val="baseline"/>
								</w:rPr>
							</w:pPr>
						</w:p>
					</w:tc>
					<w:tc>
						<w:tcPr>
							<w:tcW w:type="dxa" w:w="4349"/>
							<w:shd w:color="auto" w:fill="auto" w:val="clear"/>
							<w:vAlign w:val="top"/>
						</w:tcPr>
						<w:p>
							<w:pPr>
								<w:pStyle w:val="a2"/>
								<w:keepNext w:val="off"/>
								<w:keepLines w:val="off"/>
								<w:widowControl/>
								<w:supressLineNumbers w:val="off"/>
								<w:shd w:color="auto" w:fill="FFFFFF" w:val="clear"/>
								<w:rPr>
									<w:sz w:val="21"/>
									<w:sz-cs w:val="21"/>
									<w:vertAlign w:val="baseline"/>
								</w:rPr>
							</w:pPr>
							<w:r>
								<w:rPr>
									<w:sz w:val="21"/>
									<w:sz-cs w:val="21"/>
									<w:vertAlign w:val="baseline"/>
								</w:rPr>
								<w:t>应用：</w:t>
							</w:r>
							<w:r>
								<w:rPr>
									<w:sz w:val="21"/>
									<w:sz-cs w:val="21"/>
									<w:vertAlign w:val="baseline"/>
								</w:rPr>
								<w:t>${weaknessInfo.host}</w:t>
							</w:r>
						</w:p>
						<w:p>
							<w:pPr>
								<w:jc w:val="both"/>
								<w:rPr>
									<w:sz w:val="21"/>
									<w:sz-cs w:val="21"/>
									<w:vertAlign w:val="baseline"/>
								</w:rPr>
							</w:pPr>
							<w:r>
								<w:rPr>
									<w:rFonts w:hint="fareast"/>
									<w:sz w:val="21"/>
									<w:sz-cs w:val="21"/>
									<w:vertAlign w:val="baseline"/>
									<w:lang w:val="EN-US"/>
								</w:rPr>
								<w:t>弱点名称:</w:t>
							</w:r>

							<w:r>
								<w:rPr>
									<w:sz w:val="21"/>
									<w:sz-cs w:val="21"/>
									<w:vertAlign w:val="baseline"/>
								</w:rPr>
								<w:t>${weaknessInfo.ruleName}</w:t>
							</w:r>
						</w:p>
						<w:p>
							<w:pPr>
								<w:jc w:val="both"/>
								<w:rPr>
									<w:sz w:val="21"/>
									<w:sz-cs w:val="21"/>
									<w:vertAlign w:val="baseline"/>
								</w:rPr>
							</w:pPr>
							<w:r>
								<w:rPr>
									<w:sz w:val="21"/>
									<w:sz-cs w:val="21"/>
									<w:vertAlign w:val="baseline"/>
								</w:rPr>
								<w:t>弱点状态:</w:t>
							</w:r>
							<w:r>
								<w:rPr>
									<w:sz w:val="21"/>
									<w:sz-cs w:val="21"/>
									<w:vertAlign w:val="baseline"/>
								</w:rPr>
								<w:t>${weaknessInfo.statusText}</w:t>
							</w:r>
						</w:p>
						<w:p>
							<w:pPr>
								<w:jc w:val="both"/>
								<w:rPr>
									<w:sz w:val="21"/>
									<w:sz-cs w:val="21"/>
									<w:vertAlign w:val="baseline"/>
								</w:rPr>
							</w:pPr>
							<w:r>
								<w:rPr>
									<w:sz w:val="21"/>
									<w:sz-cs w:val="21"/>
									<w:vertAlign w:val="baseline"/>
								</w:rPr>
								<w:t>弱点等级:</w:t>
							</w:r>
							<w:r>
								<w:rPr>
									<w:sz w:val="21"/>
									<w:sz-cs w:val="21"/>
									<w:vertAlign w:val="baseline"/>
								</w:rPr>
								<w:t>${weaknessInfo.levelText}</w:t>
							</w:r>
						</w:p>
					</w:tc>
					<w:tc>
						<w:tcPr>
							<w:tcW w:type="dxa" w:w="2713"/>
							<w:shd w:color="auto" w:fill="auto" w:val="clear"/>
							<w:vAlign w:val="top"/>
						</w:tcPr>
						<w:p>
							<w:pPr>
								<w:keepNext w:val="off"/>
								<w:keepLines w:val="off"/>
								<w:widowControl/>
								<w:supressLineNumbers w:val="off"/>
								<w:jc w:val="both"/>
								<w:rPr>
									<w:sz w:val="21"/>
									<w:sz-cs w:val="21"/>
									<w:vertAlign w:val="baseline"/>
								</w:rPr>
							</w:pPr>
							<w:r>
								<w:rPr>
									<w:sz w:val="21"/>
									<w:sz-cs w:val="21"/>
									<w:vertAlign w:val="baseline"/>
								</w:rPr>
								<w:t>弱点详情:</w:t>
							</w:r>

							<w:r>
								<w:rPr>
									<w:rFonts w:hint="default"/>
									<w:sz w:val="21"/>
									<w:sz-cs w:val="21"/>
									<w:vertAlign w:val="baseline"/>
								</w:rPr>
								<w:t>${weaknessInfo.desc?html}</w:t>
							</w:r>

						</w:p>

					</w:tc>
				</w:tr>
				 </#list>
			</w:tbl>
            </#if>


			<w:p/>
			<w:sectPr>
				<w:pgSz w:h="16838" w:w="11906"/>
				<w:pgMar w:bottom="1440" w:footer="992" w:gutter="0" w:header="851" w:left="1800" w:right="1800" w:top="1440"/>
				<w:cols w:space="425"/>
				<w:docGrid w:line-pitch="312" w:type="lines"/>
			</w:sectPr>
		</wx:sect>
	</w:body>
</w:wordDocument>
