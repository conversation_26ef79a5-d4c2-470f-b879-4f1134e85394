nacos.config.server-addr=nacos-server:8848
server.port=8080
spring.servlet.multipart.max-file-size=4096MB
spring.servlet.multipart.max-request-size=4096MB

logging.config=classpath:logback-prod.xml
user.home=$HOME

nacos.config.data-ids=auditapiv2.bootstrap.properties
nacos.config.type=properties
nacos.config.bootstrap.enable=true
nacos.config.group=auditapiv2
server.servlet.session.cookie.secure=true

# æ¯å¦åå§åæä»¶ç®å½
isAuditapiv2InitialPlugin=true
isHandlerInitialPlugin=false
isDiscoverInitialPlugin=false

# é£é©æ ·ä¾è¡¨æ¸çéå¼
riskSample.clean.num=1000000

# å¨çº¿IPææ¥åºå°å
//https://api.data-sec.com
online.ip.info.url=http://*************/api/onlineIpInfo/checkIp

# åçº§æå¡
upgrade.service.nacos.dataId=auditapiv2.bootstrap.properties
upgrade.service.nacos.groupId=auditapiv2

spring.mvc.static-path-pattern=/static/**
server.servlet.context-path=/audit-apiv2
audit.mix.schedule.app=auditapiv2



spring.datasource.url=**********************************************
spring.datasource.username=audit
spring.datasource.password=@ck
spring.datasource.driver-class-name=ru.yandex.clickhouse.ClickHouseDriver
server.servlet.encoding.charset=UTF-8
api.trace.executorPath=/trace/executors.yaml