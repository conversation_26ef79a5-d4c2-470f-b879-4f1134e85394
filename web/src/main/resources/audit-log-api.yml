operations:
  - requestMapping: "/api/httpApp/edit.do"
    conditions:
      - module: "应用"
        type: "编辑"
        operation: "编辑应用名称"
        description: "应用\"${#app.uri}\"名称改为\"${#app.name}\""
        condition: "#app.name != null && #app.name.length() > 0"
      - module: "应用"
        type: "编辑"
        operation: "关注应用"
        description: "关注应用\"${#app.uri}\""
        condition: "#app.followed != null && #app.followed == true"
      - module: "应用"
        type: "编辑"
        operation: "取消关注应用"
        description: "取消关注应用\"${#app.uri}\""
        condition: "#app.followed != null && #app.followed == false"
      - module: "应用"
        type: "编辑"
        operation: "编辑应用标签"
        description: "应用\"${#app.uri}\"标签改为\"${#app.featureLabelsValue}\""
        condition: "#app.featureLabelsValue != null && #app.featureLabelsValue.size() > 0"
      - module: "应用"
        type: "编辑"
        operation: "编辑应用等级"
        description: "应用\"${#app.uri}\"等级改为\"${#app.level}\""
        condition: "#app.level != null && #app.level.length() > 0"
  - requestMapping: "/api/httpApp/filter.do"
    module: "应用"
    type: "查看"
    operation: "应用加入黑名单"
    description: "应用\"${#app.host}\"加入黑名单"
  - requestMapping: "/api/export/exportApps.do"
    module: "应用"
    type: "导出"
    operation: "导出应用清单"
    description: "导出应用清单"
  - requestMapping: "/api/export/exportUrlStructures.do"
    module: "应用"
    type: "导出"
    operation: "导出应用结构"
    description: "导出应用结构"
  - requestMapping: "/api/sampleEvent/getSampleList.do"
    module: "样例"
    type: "取消脱敏"
    operation: "取消脱敏"
    description: "取消脱敏查看API样例\"${#uri}\""
    condition: "#desensitize == false"
  - requestMapping: "/api/httpApi/updateHttpApi.do"
    conditions:
      - module: "API"
        type: "编辑"
        operation: "编辑API标签"
        description: "将API\"${#api.uri}\"的标签改为\"${#labels}\""
        condition: "#labels != null"
      - module: "API"
        type: "编辑"
        operation: "编辑API敏感等级"
        description: "将API\"${#api.uri}\"的敏感等级改为\"${#api.level}\""
        condition: "#api.level != null"
      - module: "API"
        type: "编辑"
        operation: "编辑API名称"
        description: "将API\"${#api.uri}\"的名称改为\"${#api.name}\""
        condition: "#api.name != null"
  - requestMapping: "/api/httpApi/updateApiState"
    module: "API"
    type: "处理"
    operation: "处理API状态"
    description: "将API\"${#api.uri}\"确认状态改为\"${#api.state.toString().equals('CONFIRMED') ? '已确认' : '待确认'}\""
  - requestMapping: "/api/changeHost/change"
    module: "API"
    type: "编辑"
    operation: "对API进行应用变更"
    description: "${#logDesc}"
