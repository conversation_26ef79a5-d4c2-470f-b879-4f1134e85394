<!DOCTYPE html>
<html lang="zh-CN">
<head>
<meta charset="utf-8">
<meta http-equiv="X-UA-Compatible" content="IE=edge">
<meta name="viewport" content="width=device-width, initial-scale=1">
<meta name="description" content="">
<meta name="keywords" content="">
<title>API资产梳理报告</title>
<link rel="stylesheet" type="text/css" href="style.css">
<link rel="stylesheet" type="text/css" href="jquery.json-viewer.css">
<script src="js.js"></script>
<script src="jquery.json-viewer.js"></script>
</head>
<body style="font-size: 12px;">
<nav class="navbar navbar-default" style="margin-bottom: 10px;">
    <div class="container-fluid">
        <div class="navbar-header">
            <button type="button" class="navbar-toggle collapsed" data-toggle="collapse" data-target="#bs-example-navbar-collapse-1" aria-expanded="false">
                <span class="sr-only">Toggle navigation</span>
                <span class="icon-bar"></span>
                <span class="icon-bar"></span>
                <span class="icon-bar"></span>
            </button>
            <!--<a class="navbar-brand" href="http://qzkeji.com.cn/" target="_blank">全知科技</a>-->
        </div>
        <div class="collapse navbar-collapse" id="bs-example-navbar-collapse-1">
            <ul class="nav navbar-nav">
                <li class="active"><a href="javascript:void(0);">资产弱点</a></li>
            </ul>
        </div>
    </div>
</nav>
<div class="container-fluid">
    <ul class="nav nav-tabs" role="tablist">
        <li role="presentation" class="active"><a href="#summary" role="tab" data-toggle="tab" 
            aria-controls="summary" aria-expanded="true">弱点清单</a></li>
    </ul>
    <div class="tab-content" style="padding-top: 10px;">
        <div role="tabpanel" class="tab-pane fade active in" id="summary" aria-labelledby="summary-tab">
            <table class="table table-bordered riskListContent" style="border: 0;">
                <tr>
                    <th>接口</th>
                    <th>弱点名称</th>
                    <th>弱点状态</th>
                    <th>弱点等级</th>
                    <th>操作</th>
                </tr>
            </table>
        </div>
    </div>
</div>
<div class="modal fade" id="riskDetailModal" tabindex="-1" role="dialog" aria-labelledby="myModalLabel">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                <h4 class="modal-title" id="myModalLabel">弱点详情</h4>
            </div>
            <div class="modal-body">
                <form class="form-horizontal">
                    <div class="form-group">
                        <div class="col-sm-12">
                            <textarea class="form-control riskDetailContent" rows="15"></textarea>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-default" data-dismiss="modal">Close</button>
            </div>
        </div>
    </div>
</div>

<script type="text/javascript">
var data=undefined;
console.log(data);

function renderRiskDetailData(_id) {
    for (var riskInfo of data) {
        if (riskInfo['_id'] == _id) {
            $('.riskDetailContent').val(riskInfo['desc']);
            $('#riskDetailModal').modal();
            break;
        }
    }
}

function renderRiskListData(data) {
    if (!data) {
        return;
    }

    var content = '';
    for (var riskInfo of data) {
        var _id = riskInfo['_id'];
        var apiUrl = riskInfo['apiUrl'];
        var eventId = riskInfo['eventIdList'][0];
        var apiUrlMd5 = riskInfo['apiUrlMd5'];
        var apiDetailHref = '<a href="./detail/api_'+apiUrlMd5+'.html" target="_blank">'+apiUrl+'</a>';
        var eventDetailHref = '<a href="./detail/event_'+eventId+'.html" target="_blank">事件详情</a>';
        var riskDetailHref = '<a href="javascript:void(0)" onclick="renderRiskDetailData(\''+_id+'\')">弱点详情</a>';

        content += '<tr>';
        content += '<td>'+apiDetailHref+'</td>';
        content += '<td>'+riskInfo['ruleName']+'</td>';
        content += '<td>'+riskInfo['statusText']+'</td>';
        content += '<td>'+riskInfo['levelText']+'</td>';
        content += '<td style="width: 120px;">'+riskDetailHref+' '+eventDetailHref+'</td>';
        content += '</tr>';
    }
    $('.riskListContent').append(content);

}

$(document).ready(function(){
    renderRiskListData(data);
});

</script>
</body>
</html>
    