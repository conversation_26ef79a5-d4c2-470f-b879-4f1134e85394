<!DOCTYPE html>
<html lang="zh-CN">
<head>
<meta charset="utf-8">
<meta http-equiv="X-UA-Compatible" content="IE=edge">
<meta name="viewport" content="width=device-width, initial-scale=1">
<meta name="description" content="">
<meta name="keywords" content="">
<title>API资产梳理报告</title>
<link rel="stylesheet" type="text/css" href="style.css">
<link rel="stylesheet" type="text/css" href="jquery.json-viewer.css">
<script src="js.js"></script>
<script src="jquery.json-viewer.js"></script>
</head>
<body style="font-size: 12px;">
<nav class="navbar navbar-default" style="margin-bottom: 10px;">
    <div class="container-fluid">
        <div class="navbar-header">
            <button type="button" class="navbar-toggle collapsed" data-toggle="collapse" data-target="#bs-example-navbar-collapse-1" aria-expanded="false">
                <span class="sr-only">Toggle navigation</span>
                <span class="icon-bar"></span>
                <span class="icon-bar"></span>
                <span class="icon-bar"></span>
            </button>
            <!--<a class="navbar-brand" href="http://qzkeji.com.cn/" target="_blank">全知科技</a>-->
        </div>
        <div class="collapse navbar-collapse" id="bs-example-navbar-collapse-1">
            <ul class="nav navbar-nav">
                <li class="active"><a href="javascript:void(0);">接口资产</a></li>
            </ul>
        </div>
    </div>
</nav>
<div class="container-fluid">
    <ul class="nav nav-tabs" role="tablist">
        <li role="presentation" class="active"><a href="#summary" role="tab" data-toggle="tab"
            aria-controls="summary" aria-expanded="true">接口清单</a></li>
    </ul>
    <div class="tab-content" style="padding-top: 10px;">
        <div role="tabpanel" class="tab-pane fade active in" id="summary" aria-labelledby="summary-tab">
            <table class="table table-bordered apiListContent" style="border: 0;">
                <tr>
                    <th>接口URL</th>
                    <th>访问域标签</th>
                    <th>终端标签</th>
                    <th>最大返回数据量</th>
                    <th>请求数据标签</th>
                    <th>返回数据标签</th>
                    <th>操作</th>
                </tr>
            </table>
        </div>
    </div>
</div>

<script type="text/javascript">
var data=undefined;
console.log(data);

function renderApiListData(data) {
    if (!data) {
        return;
    }

    var content = '';
    var keyList = ['getDataLabels', 'postDataLabels', 'rspDataLabels'];
    for (var apiInfo of data) {
        var apiUrlMd5 = apiInfo['apiUrlMd5'];
        var apiHref = '<a href="./detail/api_'+ apiInfo['uriMd5'] +'.html" target="_blank">接口详情</a>';

        content += '<tr>';
        content += '<td>'+apiInfo['apiUrl']+'</td>';
        content += '<td>'+ (apiInfo['visitDomains'] || []).join(',')+'</td>';
        content += '<td>'+ (apiInfo['terminals'] || []).join(',')+'</td>';
        content += '<td>'+ (apiInfo['apiStat']['maxReqLabelValueCount'] || 0).toString()+'</td>';
        content += '<td>'+ (apiInfo['reqDataLabels'] || []).join(',')+'</td>';
        content += '<td>'+ (apiInfo['rspDataLabels'] || []).join(',')+'</td>';
        content += '<td>'+apiHref+'</td>';
        content += '</tr>';
    }
    $('.apiListContent').append(content);
}

$(document).ready(function(){
    renderApiListData(data);
});

</script>
</body>
</html>
