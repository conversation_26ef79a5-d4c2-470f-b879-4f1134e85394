<!DOCTYPE html>
<html lang="zh-CN">
<head>
<meta charset="utf-8">
<meta http-equiv="X-UA-Compatible" content="IE=edge">
<meta name="viewport" content="width=device-width, initial-scale=1">
<meta name="description" content="">
<meta name="keywords" content="">
<title>API资产梳理报告</title>
<link rel="stylesheet" type="text/css" href="style.css">
<link rel="stylesheet" type="text/css" href="jquery.json-viewer.css">
<script src="js.js"></script>
<script src="jquery.json-viewer.js"></script>
</head>
<body style="font-size: 12px;">
<nav class="navbar navbar-default" style="margin-bottom: 10px;">
    <div class="container-fluid">
        <div class="navbar-header">
            <button type="button" class="navbar-toggle collapsed" data-toggle="collapse" data-target="#bs-example-navbar-collapse-1" aria-expanded="false">
                <span class="sr-only">Toggle navigation</span>
                <span class="icon-bar"></span>
                <span class="icon-bar"></span>
                <span class="icon-bar"></span>
            </button>
            <!--<a class="navbar-brand" href="http://qzkeji.com.cn/" target="_blank">全知科技</a>-->
        </div>
        <div class="collapse navbar-collapse" id="bs-example-navbar-collapse-1">
            <ul class="nav navbar-nav">
                <li class="active"><a href="javascript:void(0);">应用资产</a></li>
            </ul>
        </div>
    </div>
</nav>
<div class="container-fluid">
    <ul class="nav nav-tabs" role="tablist">
        <li role="presentation" class="active"><a href="#summary" role="tab" data-toggle="tab"
            aria-controls="summary" aria-expanded="true">应用清单</a></li>
    </ul>
    <div class="tab-content" style="padding-top: 10px;">
        <div role="tabpanel" class="tab-pane fade active in" id="summary" aria-labelledby="summary-tab">
            <table class="table table-bordered hostListContent" style="border: 0;">
                <tr>
                    <th>应用</th>
                    <th>接口数量</th>
                    <th>弱点总数</th>
                    <th>高危弱点数</th>
                    <th>中危弱点数</th>
                    <th>低危弱点数</th>
                    <th>访问域标签</th>
                    <th>终端标签</th>
                    <th>最大返回数据量</th>
                    <th>最大返回标签个数</th>
                    <th>数据标签</th>
                    <th>操作</th>
                </tr>
            </table>
        </div>
    </div>
</div>

<script type="text/javascript">
var data=undefined;
console.log(data);

function renderHostListData(data) {
    if (!data) {
        return;
    }

    var content = '';
    var keyList = ['getDataLabels', 'postDataLabels', 'rspDataLabels'];
    for (var hostInfo of data) {
        var host = hostInfo['host'];
        var hostApiListHref = '<a href="./'+host+'/index_api_list.html" target="_blank">接口清单</a>';
        var hostRiskListHref = '<a href="./'+host+'/index_risk_list.html" target="_blank">弱点清单</a>';

        content += '<tr>';
        content += '<td>'+hostInfo['host']+'</td>';
        content += '<td>'+hostInfo['apiCount'].toString()+'</td>';
        content += '<td>'+hostInfo['riskCount']['total'].toString()+'</td>';
        content += '<td>'+hostInfo['riskCount']['h'].toString()+'</td>';
        content += '<td>'+hostInfo['riskCount']['m'].toString()+'</td>';
        content += '<td>'+hostInfo['riskCount']['l'].toString()+'</td>';
        content += '<td>'+hostInfo['srcIpLabel'].join(',')+'</td>';
        content += '<td>'+hostInfo['uaLabel'].join(',')+'</td>';
        content += '<td>'+hostInfo['maxRspLabelValueCount'].toString()+'</td>';
        content += '<td>'+hostInfo['maxRspLabelSubCount'].toString()+'</td>';
        content += '<td><div style="width: 200px;">'+ (hostInfo['dataLabels'] || []).join(',')+'</div></td>';
        content += '<td>'+hostApiListHref+' '+hostRiskListHref+'</td>';
        content += '</tr>';
    }
    $('.hostListContent').append(content);
}

$(document).ready(function(){
    renderHostListData(data);
});

</script>
</body>
</html>
