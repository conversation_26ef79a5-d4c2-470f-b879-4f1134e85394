<!DOCTYPE html>
<html lang="zh-CN">
<head>
<meta charset="utf-8">
<meta http-equiv="X-UA-Compatible" content="IE=edge">
<meta name="viewport" content="width=device-width, initial-scale=1">
<meta name="description" content="">
<meta name="keywords" content="">
<title>API资产梳理报告</title>
<link rel="stylesheet" type="text/css" href="style.css">
<link rel="stylesheet" type="text/css" href="jquery.json-viewer.css">
<script src="js.js"></script>
<script src="jquery.json-viewer.js"></script>
</head>
<body style="font-size: 12px;">
<nav class="navbar navbar-default" style="margin-bottom: 10px;">
    <div class="container-fluid">
        <div class="navbar-header">
            <button type="button" class="navbar-toggle collapsed" data-toggle="collapse" data-target="#bs-example-navbar-collapse-1" aria-expanded="false">
                <span class="sr-only">Toggle navigation</span>
                <span class="icon-bar"></span>
                <span class="icon-bar"></span>
                <span class="icon-bar"></span>
            </button>
            <!--<a class="navbar-brand" href="http://qzkeji.com.cn/" target="_blank">全知科技</a>-->
        </div>
        <div class="collapse navbar-collapse" id="bs-example-navbar-collapse-1">
            <ul class="nav navbar-nav">
                <li class="active"><a href="assetsReport.html">事件详情</a></li>
            </ul>
        </div>
    </div>
</nav>
<div class="container-fluid">
    <ul class="nav nav-tabs" role="tablist">
        <li role="presentation" class="active"><a href="#summary" role="tab" data-toggle="tab" 
            aria-controls="summary" aria-expanded="true">事件信息</a></li>
        <li role="presentation" class=""><a href="#reqHeader" role="tab" data-toggle="tab" 
            aria-controls="reqHeader" aria-expanded="false">请求头</a></li>
        <li role="presentation" class=""><a href="#getReqBody" role="tab" data-toggle="tab" 
            aria-controls="getReqBody" aria-expanded="false">GET请求内容</a></li>
        <li role="presentation" class=""><a href="#postReqBody" role="tab" data-toggle="tab" 
            aria-controls="postReqBody" aria-expanded="false">POST请求内容</a></li>
        <li role="presentation" class=""><a href="#reqBody" role="tab" data-toggle="tab" 
            aria-controls="reqBody" aria-expanded="false">请求内容</a></li>
        <li role="presentation" class=""><a href="#rspHeader" role="tab" data-toggle="tab" 
            aria-controls="rspHeader" aria-expanded="false">返回头</a></li>
        <li role="presentation" class=""><a href="#rspBody" role="tab" data-toggle="tab" 
            aria-controls="rspBody" aria-expanded="false">返回内容</a></li>
    </ul>
    <div class="tab-content" style="padding-top: 10px;">
        <div role="tabpanel" class="tab-pane fade active in" id="summary" aria-labelledby="summary-tab">
            <form class="form-horizontal">
                <div class="form-group">
                    <label class="col-sm-1 control-label" style="text-align: left;">请求URL</label>
                    <div class="col-sm-10">
                        <input type="text" class="form-control reqUrl" placeholder="">
                    </div>
                    <div class="col-sm-1">
                        <input type="text" class="form-control reqMethod" value="">
                    </div>
                </div>
            </form>
            <h5>请求基本信息</h5>
            <table class="table table-bordered">
                <tr>
                    <th style="width: 150px;">请求时间</th>
                    <td class="reqTime"></td>
                </tr>
                <tr>
                    <th style="width: 150px;">请求来源</th>
                    <td class="reqIp"></td>
                </tr>
            </table>
            <div class="labelValue"></div>
        </div>
        <div role="tabpanel" class="tab-pane fade reqHeader" id="reqHeader" aria-labelledby="reqHeader-tab">

        </div>
        <div role="tabpanel" class="tab-pane fade" id="getReqBody" aria-labelledby="getReqBody-tab">
            <form class="form-horizontal">
                <div class="form-group">
                    <div class="col-sm-12">
                        <textarea class="form-control getParams" rows="30"></textarea>
                    </div>
                </div>
            </form>
        </div>
        <div role="tabpanel" class="tab-pane fade" id="postReqBody" aria-labelledby="postReqBody-tab">
            <form class="form-horizontal">
                <div class="form-group">
                    <div class="col-sm-12">
                        <textarea class="form-control postParams" rows="30"></textarea>
                    </div>
                </div>
            </form>
        </div>
        <div role="tabpanel" class="tab-pane fade" id="reqBody" aria-labelledby="reqBody-tab">
            <form class="form-horizontal">
                <div class="form-group">
                    <div class="col-sm-12">
                        <textarea class="form-control reqBody" rows="30"></textarea>
                    </div>
                </div>
            </form>
        </div>
        <div role="tabpanel" class="tab-pane fade rspHeader" id="rspHeader" aria-labelledby="rspHeader-tab">

        </div>
        <div role="tabpanel" class="tab-pane fade" id="rspBody" aria-labelledby="rspBody-tab">
            <p style="width: 100%; text-align: right;">
                <button type="button" class="btn btn-primary btn-xs" id="rspBodyFormatBtnId">格式化</button>
                <button type="button" class="btn btn-primary btn-xs" id="rspBodyContentBtnId" style="display: none;">原始内容</button>
            </p>
            <form class="form-horizontal" id="rspBodyContentFormId">
                <div class="form-group">
                    <div class="col-sm-12">
                        <textarea class="form-control rspBody" rows="30" id="rspBodyContentId"></textarea>
                    </div>
                </div>
            </form>
            <pre id="rspBodyJsonId" style="display: none"></pre>
        </div>
    </div>
</div>

<script type="text/javascript">
var data=undefined;

function formatDate(date) {
    var date = new Date(date);
    var YY = date.getFullYear() + '-';
    var MM = (date.getMonth() + 1 < 10 ? '0' + (date.getMonth() + 1) : date.getMonth() + 1) + '-';
    var DD = (date.getDate() < 10 ? '0' + (date.getDate()) : date.getDate());
    var hh = (date.getHours() < 10 ? '0' + date.getHours() : date.getHours()) + ':';
    var mm = (date.getMinutes() < 10 ? '0' + date.getMinutes() : date.getMinutes()) + ':';
    var ss = (date.getSeconds() < 10 ? '0' + date.getSeconds() : date.getSeconds());
    return YY + MM + DD +" "+hh + mm + ss;
}

var HtmlUtil = {
    /*1.用浏览器内部转换器实现html编码（转义）*/
    htmlEncode:function (html){
        //1.首先动态创建一个容器标签元素，如DIV
        var temp = document.createElement ("div");
        //2.然后将要转换的字符串设置为这个元素的innerText或者textContent
        (temp.textContent != undefined ) ? (temp.textContent = html) : (temp.innerText = html);
        //3.最后返回这个元素的innerHTML，即得到经过HTML编码转换的字符串了
        var output = temp.innerHTML;
        temp = null;
        return output;
    },
    /*2.用浏览器内部转换器实现html解码（反转义）*/
    htmlDecode:function (text){
        //1.首先动态创建一个容器标签元素，如DIV
        var temp = document.createElement("div");
        //2.然后将要转换的字符串设置为这个元素的innerHTML(ie，火狐，google都支持)
        temp.innerHTML = text;
        //3.最后返回这个元素的innerText或者textContent，即得到经过HTML解码的字符串了。
        var output = temp.innerText || temp.textContent;
        temp = null;
        return output;
    },
    /*3.用正则表达式实现html编码（转义）*/
    htmlEncodeByRegExp:function (str){
         var temp = "";
         if(str.length == 0) return "";
         temp = str.replace(/&/g,"&");
         temp = temp.replace(/</g,"<");
         temp = temp.replace(/>/g,">");
         temp = temp.replace(/\s/g," ");
         temp = temp.replace(/\'/g,"'");
         temp = temp.replace(/\"/g,"\"");
         return temp;
    },
    /*4.用正则表达式实现html解码（反转义）*/
    htmlDecodeByRegExp:function (str){
         var temp = "";
         if(str.length == 0) return "";
         temp = str.replace(/&/g,"&");
         temp = temp.replace(/</g,"<");
         temp = temp.replace(/>/g,">");
         temp = temp.replace(/ /g," ");
         temp = temp.replace(/'/g,"\'");
         temp = temp.replace(/"/g,"\"");
         return temp;
    },
    /*5.用正则表达式实现html编码（转义）（另一种写法）*/
    html2Escape:function(sHtml) {
         return sHtml.replace(/[<>&"]/g,function(c){return {'<':'<','>':'>','&':'&','"':'"'}[c];});
    },
    /*6.用正则表达式实现html解码（反转义）（另一种写法）*/
    escape2Html:function (str) {
         var arrEntities={'lt':'<','gt':'>','nbsp':' ','amp':'&','quot':'"'};
         return str.replace(/&(lt|gt|nbsp|amp|quot);/ig,function(all,t){return arrEntities[t];});
    }
};

function renderEventData(data) {
    if (!data) {
        return;
    }

    // 渲染事件信息
    var url = data['req']['url'];
    $(".reqUrl").val(url);
    var method = data['req']['method'];
    $(".reqMethod").val(method);
    var reqTime = formatDate(data['meta']['tm'] * 1000);
    $(".reqTime").html(reqTime);
    var reqIp = data['ip'];
    var reqCountry = data['originIpPosition']['country'];
    var reqProvince = data['originIpPosition']['province'];
    var reqCity = data['originIpPosition']['city'];
    reqIp = reqIp + " ( "+reqCountry+" - "+reqProvince+" - "+reqCity+" )";
    $(".reqIp").html(reqIp);

    // 敏感内容渲染
    var labelValue = data['labelValue'];
    var labelValueContent = '';
    if (labelValue) {
        var locList = ['get', 'post', 'rsp'];
        var locDict = {'get': 'GET', 'post': 'POST', 'rsp': '返回'};
        for (var loc of locList) {
            if (!labelValue.hasOwnProperty(loc)) {
                continue;
            }

            labelValueContent += '<h5>'+locDict[loc]+'敏感内容</h5>';
            labelValueContent += '<table class="table table-bordered">';
            for (let key in labelValue[loc]) {
                var count = labelValue[loc][key].length;
                labelValueContent += '<tr>';
                labelValueContent += '<th style="width: 150px;">'+key+'</th>';
                labelValueContent += '<th style="width: 50px;"> '+count.toString()+' </th>';
                labelValueContent += '<td style="padding: 0 5px;"><input type="text" style="width: 100%; height: 32px; border: 0; font-size: 14px;" name="" value="'+labelValue[loc][key].join(',')+'"></td>';
                labelValueContent += '</tr>';
            }
            labelValueContent += '</table>';
        } 
    }
    $('.labelValue').html(labelValueContent);

    // 请求头渲染
    var reqHeader = data['req']['header'];
    var reqHeaderContent = '<h5>请求头信息</h5>';
    reqHeaderContent += '<table class="table table-bordered">';
    if (reqHeader) {
        for (let key in reqHeader) {
            reqHeaderContent += '<tr>';
            reqHeaderContent += '<th style="width: 120px;">'+key+'</th>';
            reqHeaderContent += '<td style="padding: 0 5px;"><input type="text" style="width: 100%; height: 32px; border: 0; font-size: 14px;" name="" value="'+reqHeader[key]+'"></td>';
            reqHeaderContent += '</tr>';
        }
    }
    reqHeaderContent += '</table>';
    $('.reqHeader').html(reqHeaderContent);

    // GET请求信息
    var getParams = '';
    if (data['req'].hasOwnProperty('args') && data['req']['args']) {
        getParams = JSON.stringify(data['req']['args']);
    }
    $('.getParams').val(getParams);

    // POST请求信息
    var postParams = '';
    if (data['req'].hasOwnProperty('args2') && data['req']['args2']) {
        postParams = JSON.stringify(data['req']['args2']);
    }
    $('.postParams').val(postParams);

    // 请求内容
    var reqBody = data['req']['body'];
    $('.reqBody').val(reqBody);

    // 返回头信息
    var rspHeader = undefined;
    var rspHeaderContent = '<h5>返回头信息</h5>';
    rspHeaderContent += '<table class="table table-bordered">';
    if (data.hasOwnProperty('rsp') && data['rsp'].hasOwnProperty('header')) {
        rspHeader = data['rsp']['header'];
    }
    if (rspHeader) {
        for (let key in rspHeader) {
            rspHeaderContent += '<tr>';
            rspHeaderContent += '<th style="width: 120px;">'+key+'</th>';
            rspHeaderContent += '<td style="padding: 0 5px;"><input type="text" style="width: 100%; height: 32px; border: 0; font-size: 14px;" name="" value="'+rspHeader[key]+'"></td>';
            rspHeaderContent += '</tr>';
        }
    }
    rspHeaderContent += '</table>';
    $('.rspHeader').html(rspHeaderContent);

    // 返回内容
    var rspBody = '';
    if (data.hasOwnProperty('rsp') && data['rsp'].hasOwnProperty('body')) {
        rspBody = data['rsp']['body'];
    }
    rspBody = HtmlUtil.htmlDecode(rspBody);
    $('.rspBody').val(rspBody);
    contentFormat('rspBody');
}

function getContentType(content) {
    if (!content || content.length <= 2) {
        return '';
    }
    var t = content.replace(/(^\s+)|(\s+$)/g,"");

    if (['{','['].indexOf(t.substr(0, 1)) >= 0 && ['}',']'].indexOf(t.substr(-1, 1)) >= 0) {
        return 'json';
    }

    t = t.toLowerCase();

    if (t.indexOf('html') > 0 && t.indexOf('body') > 0 && t.indexOf('title') > 0) {
        return 'html';
    }

    return '';
}

function contentFormat(moduleType) {
    $("#"+moduleType+"FormatBtnId").unbind('click').click(function() {
        var content = $('.' + moduleType).val();
        var contentType = getContentType(content);
        if (contentType == 'json') {
            try {
                var input = eval('(' + $("#rspBodyContentId").val() + ')');
            } catch (error) {
                var input = eval('({})');
            }
            var options = {
                collapsed: true,
                withQuotes: true
            };
            $('#'+moduleType+'ContentFormId').hide();
            $('#'+moduleType+'JsonId').jsonViewer(input, options);
            $('#'+moduleType+'JsonId').show();

            $('#'+moduleType+'FormatBtnId').hide();
            $('#'+moduleType+'ContentBtnId').show();
        } else if (contentType == 'html') {
            
        }
    });

    $("#"+moduleType+"ContentBtnId").unbind('click').click(function() {
        $('#'+moduleType+'ContentFormId').show();
        $('#'+moduleType+'JsonId').hide();
        $('#'+moduleType+'HtmlId').hide();
        $('#'+moduleType+'FormatBtnId').show();
        $('#'+moduleType+'ContentBtnId').hide();
    });
}

$(document).ready(function(){
    renderEventData(data);
});

</script>
</body>
</html>
    