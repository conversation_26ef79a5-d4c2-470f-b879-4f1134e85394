<!DOCTYPE html>
<html lang="zh-CN">
<head>
<meta charset="utf-8">
<meta http-equiv="X-UA-Compatible" content="IE=edge">
<meta name="viewport" content="width=device-width, initial-scale=1">
<meta name="description" content="">
<meta name="keywords" content="">
<title>API资产梳理报告</title>
<link rel="stylesheet" type="text/css" href="style.css">
<link rel="stylesheet" type="text/css" href="jquery.json-viewer.css">
<script src="js.js"></script>
<script src="jquery.json-viewer.js"></script>
</head>
<body style="font-size: 12px;">
<nav class="navbar navbar-default" style="margin-bottom: 10px;">
    <div class="container-fluid">
        <div class="navbar-header">
            <button type="button" class="navbar-toggle collapsed" data-toggle="collapse" data-target="#bs-example-navbar-collapse-1" aria-expanded="false">
                <span class="sr-only">Toggle navigation</span>
                <span class="icon-bar"></span>
                <span class="icon-bar"></span>
                <span class="icon-bar"></span>
            </button>
            <!--<a class="navbar-brand" href="http://qzkeji.com.cn/" target="_blank">全知科技</a>-->
        </div>
        <div class="collapse navbar-collapse" id="bs-example-navbar-collapse-1">
            <ul class="nav navbar-nav">
                <li class="active"><a href="javascript:void(0);">接口详情</a></li>
            </ul>
        </div>
    </div>
</nav>
<div class="container-fluid">
    <ul class="nav nav-tabs" role="tablist">
        <li role="presentation" class="active"><a href="#summary" role="tab" data-toggle="tab"
            aria-controls="summary" aria-expanded="true">接口信息</a></li>
        <li role="presentation" class=""><a href="#dataLabelGroup" role="tab" data-toggle="tab"
            aria-controls="dataLabelGroup" aria-expanded="true">数据标签组合 ( <span class="dataLabelGroupCount">0</span> ) </a></li>
    </ul>
    <div class="tab-content" style="padding-top: 10px;">
        <div role="tabpanel" class="tab-pane fade active in" id="summary" aria-labelledby="summary-tab">
            <h4>基本信息</h4>
            <table class="table table-bordered" style="border: 0;">
                <tr>
                    <th style="width: 150px; ">接口定义：</th>
                    <td style="padding: 0 5px;" colspan="2">
                        <input type="text" class="apiUrl" style="width: 100%; height: 32px; border: 0; font-size: 14px;" name="" value="">
                    </td>
                </tr>
                <tr>
                    <th style="width: 150px; ">接口标签：</th>
                    <td class="typeLabel" colspan="2">
                    </td>
                </tr>
                <tr>
                    <th style="width: 150px; ">请求标签：</th>
                    <td class="reqDataLabels" colspan="2">
                    </td>
                </tr>
                <tr>
                    <th style="width: 150px; ">返回标签：</th>
                    <td class="rspDataLabels" colspan="2">
                    </td>
                </tr>
                <tr>
                    <th style="width: 150px; ">访问域：</th>
                    <td class="srcIpLabel" colspan="2">
                    </td>
                </tr>
                <tr>
                    <th style="width: 150px; ">部署域：</th>
                    <td class="dstIpLabel" colspan="2">
                    </td>
                </tr>
                <tr>
                    <th style="width: 150px; ">访问终端：</th>
                    <td class="uaLabel" colspan="2">
                    </td>
                </tr>
            </table>
            <h4>数据样例<span class="dataLabelValueMaxEventId" style="font-size: 12px; font-weight: normal;"></span></h4>
            <div class="dataLabelValueMaxContent"></div>
        </div>
        <div role="tabpanel" class="tab-pane fade dataLabelGroup" id="dataLabelGroup" aria-labelledby="dataLabelGroup-tab">
            <h4>数据标签组合</h4>
            <table class="table table-bordered dataLabelGroupContent" style="border: 0;">
                <tr>
                    <th width="25%">GET数据标签</th>
                    <th width="25%">POST数据标签</th>
                    <th width="45%">返回数据标签</th>
                    <th width="5%">操作</th>
                </tr>
            </table>
        </div>

    </div>
</div>

<script type="text/javascript">
var data=undefined;
console.log(data);

function renderApiData(data) {
    if (!data) {
        return;
    }

    // 接口基本信息
    var apiUrl = data['apiUrl'];
    var typeLabel = data['featureLabelsValue'];
    var reqDataLabels = data['reqDataLabelsValue'] || [];
    var rspDataLabels = data['rspDataLabelsValue'] || [];
    var srcIpLabel = data['visitDomains'] || [];
    var dstIpLabel = data['deployDomains'] || [];
    var uaLabel = data['terminals'] || [];

    $('.apiUrl').val(apiUrl);
    $('.typeLabel').html(typeLabel);
    $('.reqDataLabels').html(reqDataLabels.join(','));
    $('.rspDataLabels').html(rspDataLabels.join(','));
    $('.srcIpLabel').html(srcIpLabel.join(','));
    $('.dstIpLabel').html(dstIpLabel.join(','));
    $('.uaLabel').html(uaLabel.join(','));

    // 接口数据样例
    var dataLabelValueMax = data['dataLabelValueMax'];
    if (dataLabelValueMax) {
        if (dataLabelValueMax.hasOwnProperty('eventId')) {
            var eventId = dataLabelValueMax['eventId'];
            $('.dataLabelValueMaxEventId').html(' ( <a href="event_'+eventId+'.html" target="_blank">事件ID：'+eventId+'</a> )');
        }
        var locList = ['get', 'post', 'rsp'];
        var locDict = {'get': 'GET请求', 'post': 'POST请求', 'rsp': '返回内容'};
        var content = '<table class="table table-bordered" style="border: 0;">';
        for (var loc of locList) {
            if (dataLabelValueMax.hasOwnProperty(loc)) {
                content += '<tr><th style="width: 150px; font-size: 14px;" colspan="3">'+locDict[loc]+'：</th></tr>';

                for (let key in dataLabelValueMax[loc]) {
                    var count = dataLabelValueMax[loc][key].length;
                    content += '<tr>';
                    content += '<th style="width: 150px;">'+key+'</th>';
                    content += '<th style="width: 50px;"> '+count.toString()+' </th>';
                    content += '<td style="padding: 0 5px;"><input type="text" style="width: 100%; height: 32px; border: 0; font-size: 14px;" name="" value="'+dataLabelValueMax[loc][key].join(',')+'"></td>';
                    content += '</tr>';
                }
            }
        }
        content += '</table>';
        $('.dataLabelValueMaxContent').html(content);
    }

    // 接口标签组合
    var dataLabelGroup = data['dataLabelGroup'];
    if (dataLabelGroup) {
        var content = '';
        var count = 0;
        for (let groupId in dataLabelGroup) {
            count += 1;
            var eventId = dataLabelGroup[groupId]['eventId'];
            var locList = ['get', 'post', 'rsp'];
            var locDict = {'get': '', 'post': '', 'rsp': ''};
            for (var loc of locList) {
                if (dataLabelGroup[groupId].hasOwnProperty(loc)) {
                    locDict[loc] = Object.keys(dataLabelGroup[groupId][loc]).join(',');
                }
            }

            content += '<tr>';
            content += '<td>'+locDict['get']+'</td>';
            content += '<td>'+locDict['post']+'</td>';
            content += '<td>'+locDict['rsp']+'</td>';
            content += '<td><a href="event_'+eventId+'.html" target="_blank">查看</a></td>';
            content += '</tr>';
        }
        $('.dataLabelGroupContent').append(content);
        $('.dataLabelGroupCount').html(count.toString());
    }
}

$(document).ready(function(){
    renderApiData(data);
});

</script>
</body>
</html>
