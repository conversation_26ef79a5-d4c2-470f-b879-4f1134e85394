operations:
  - requestMapping: "/api/export/export"
    conditions:
      - module: "API"
        type: "导出"
        operation: "导出API清单"
        description: "导出API清单"
        condition: "#taskType.equals('EXPORT_API')"
      - module: "风险"
        type: "导出"
        operation: "导出风险清单"
        description: "导出风险清单"
        condition: "#taskType.equals('EXPORT_RISK_INFO')"
      - module: "风险"
        type: "导出"
        operation: "导出威胁IP列表"
        description: "导出威胁IP列表"
        condition: "#taskType.equals('EXPORT_THREAT_IP')"
  - requestMapping: "/report/config/saveReportConfig"
    module: "报告"
    type: "新增"
    operation: "生成新报告"
    description: "生成新报告\"${#name}\""
  - requestMapping: "/report/detail/exportApiLifeItem"
    module: "报告"
    type: "导出"
    operation: "导出报告"
    description: "导出报告\"API生命周期清单\""
  - requestMapping: "/report/detail/exportAccountSafeItem"
    module: "报告"
    type: "导出"
    operation: "导出报告"
    description: "导出报告\"登录弱密码治理清单\""
  - requestMapping: "/report/detail/exportDataExposeDiffResult"
    module: "报告"
    type: "导出"
    operation: "导出报告"
    description: "导出报告\"数据暴露面治理对比结果清单\""
  - requestMapping: "/report/detail/exportAccountSafeDiffResult"
    module: "报告"
    type: "导出"
    operation: "导出报告"
    description: "导出报告\"登录弱密码治理对比清单\""
  - requestMapping: "/report/detail/exportDataRevealDiffResult"
    module: "报告"
    type: "导出"
    operation: "导出报告"
    description: "导出报告\"数据泄漏治理对比清单\""
  - requestMapping: "/api/commonAction/getActionByConfig.do"
    conditions:
      - module: "配置"
        type: "编辑"
        operation: "编辑风险规则"
        description: "编辑风险规则"
        condition: "#actionConfig.id.equals('API_HIGH_RISK_EDIT')"
  - requestMapping: "/api/commonAction/getActionByFrontConfig.do"
    conditions:
      - module: "报告"
        type: "删除"
        operation: "删除报告"
        description: "删除报告\"${#name}\""
        condition: "#actionConfigFrontTransform.id.equals('API_REPORT_TASK_DELETE')"
  - requestMapping: "/api/networkSegment/uploadExcel.do"
    module: "网段配置"
    type: "编辑"
    operation: "导入网段"
    description: "导入网段"
  - requestMapping: "/api/networkSegment/exportExcel.do"
    module: "网段配置"
    type: "导出"
    operation: "导出网段"
    description: "导出网段"
  - requestMapping: "/api/gatewayConfig/save.do"
    conditions:
      - module: "配置"
        type: "新增"
        operation: "新增网关"
        description: "新增网关\"${#ip}\""
        condition: "#id == null"
      - module: "配置"
        type: "编辑"
        operation: "编辑网关"
        description: "编辑网关\"${#ip}\""
        condition: "#id != null"
  - requestMapping: "/api/gatewayConfig/del.do"
    module: "配置"
    type: "删除"
    operation: "删除网关"
    description: "删除网关\"${#ip}\""
    condition: "#ip != null"
  - requestMapping: "/api/gatewayEventBlackWhiteList/add.do"
    conditions:
      - module: "配置"
        type: "新增"
        operation: "新增黑名单"
        description: "新增黑名单"
        condition: "#type == 0"
      - module: "配置"
        type: "新增"
        operation: "新增白名单"
        description: "新增白名单"
        condition: "#type == 1"
  - requestMapping: "/api/eventFilterPlugin/edit.do"
    conditions:
      - module: "配置"
        type: "编辑"
        operation: "关闭过滤规则"
        description: "关闭过滤规则\"${#name}\""
        condition: "#action != null && action.equals('disable')"
      - module: "配置"
        type: "编辑"
        operation: "打开过滤规则"
        description: "打开过滤规则\"${#name}\""
        condition: "#action != null && action.equals('enable')"
      - module: "配置"
        type: "编辑"
        operation: "编辑过滤规则"
        description: "编辑过滤规则\"${#name}\""
        condition: "1==1"
  - requestMapping: "/api/xxlJob/addTask.do"
    module: "配置"
    type: "新增"
    operation: "新增任务"
    description: "新增\"${#name}\"任务"
  - requestMapping: "/api/resourceChangedEventNacos/save.do"
    conditions:
      - module: "数据订阅"
        type: "新增"
        operation: "新增订阅"
        description: "新增订阅\"${#subscribePolicyRule.name}\""
        condition: "#subscribePolicyRule.id == null || #subscribePolicyRule.id.length() == 0"
      - module: "数据订阅"
        type: "编辑"
        operation: "编辑订阅"
        description: "编辑订阅\"${#subscribePolicyRule.name}\""
        condition: "#subscribePolicyRule.id != null && #subscribePolicyRule.id.length() > 0"
  - requestMapping: "/api/resourceChangedEventNacos/delete.do"
    module: "数据订阅"
    type: "删除"
    operation: "删除订阅"
    description: "删除订阅方式\"${#name}\""
    condition: "#name != null"
  - requestMapping: "/api/subscribeSyncConfig/save.do"
    conditions:
      - module: "数据订阅"
        type: "新增"
        operation: "新增数据订阅配置"
        description: "新增数据订阅配置\"${#subscribeSyncConfig.name}\""
        condition: "#subscribeSyncConfig.id == null || #subscribeSyncConfig.id.length() == 0"
      - module: "数据订阅"
        type: "编辑"
        operation: "编辑数据订阅配置"
        description: "编辑数据订阅配置\"${#subscribeSyncConfig.name}\""
        condition: "#subscribeSyncConfig.id != null && #subscribeSyncConfig.id.length() > 0"
  - requestMapping: "/api/subscribeSyncConfig/delete.do"
    module: "数据订阅"
    type: "删除"
    operation: "删除数据订阅配置"
    description: "删除数据订阅配置\"${#name}\""
    condition: "#name != null"
  - requestMapping: "/api/export/downloadResult.do"
    module: "数据导出管理"
    type: "导出"
    operation: "下载导出任务"
    description: "下载导出任务文件\"${#fn}\""
    condition: "#fn != null"
  - requestMapping: "/api/sysUpdate/upload.do"
    module: "系统升级"
    type: "导入"
    operation: "上传升级包"
    description: "上传升级包"
  - requestMapping: "/api/originIpResolve/save.do"
    conditions:
      - module: "IP解析配置"
        type: "编辑"
        operation: "编辑源IP解析方式"
        description: "编辑源IP解析方式"
  - requestMapping: "/api/resourceDefine/saveCompositeRule"
    conditions:
      - module: "资产定义"
        type: "新增"
        operation: "新增资产规则"
        description: "新增资产${#compositeRule.compositeType == 1 ? '合并' : '拆分'}规则\"${#compositeRule.rule}\""
        condition: "#compositeRule.isRepeal == null || #compositeRule.isRepeal==false"
      - module: "资产定义"
        type: "删除"
        operation: "删除资产规则"
        description: "删除资产${#compositeRule.compositeType == 1 ? '合并' : '拆分'}规则\"${#compositeRule.rule}\""
        condition: "#compositeRule.isRepeal == true"
  - requestMapping: "/api/ipDataLabel/saveIpLabel"
    conditions:
      - module: "IP标签"
        type: "新增"
        operation: "新增IP标签"
        description: "新增IP标签\"${#name}\""
  - requestMapping: "/api/featureLabel/save.do"
    conditions:
      - module: "API标签"
        type: "新增"
        operation: "新增API标签"
        description: "新增API标签\"${#featureLabel.name}\""
        condition: "#featureLabel.id == null || #featureLabel.id.equals('')"
      - module: "API标签"
        type: "编辑"
        operation: "编辑API标签"
        description: "编辑API标签\"${#featureLabel.name}\""
        condition: "#featureLabel.id != null && !#featureLabel.id.equals('')"
  - requestMapping: "/api/appFeatureLabel/save.do"
    conditions:
      - module: "应用标签"
        type: "新增"
        operation: "新增应用标签"
        description: "新增应用标签\"${#featureLabel.name}\""
        condition: "#featureLabel.id == null || #featureLabel.id.equals('')"
      - module: "应用标签"
        type: "编辑"
        operation: "编辑应用标签"
        description: "编辑应用标签\"${#featureLabel.name}\""
        condition: "#featureLabel.id != null && !#featureLabel.id.equals('')"
  - requestMapping: "/resource/level/save"
    module: "API敏感等级"
    type: "编辑"
    operation: "编辑敏感等级"
    description: "编辑\"${#resourceLevelStrategy.id}\"API等级"
  - requestMapping: "/api/emailConfig/save.do"
    module: "数据订阅"
    type: "编辑"
    operation: "编辑邮箱配置"
    description: "编辑邮件发件箱配置"
  - requestMapping: "/api/dataLabel/save.do"
    conditions:
      - module: "配置"
        type: "新增"
        operation: "新增数据标签"
        description: "新增数据标签\"${#name}\""
        condition: "#action.equals('ADD')"
      - module: "配置"
        type: "编辑"
        operation: "编辑数据标签"
        description: "编辑数据标签\"${#name}\""
        condition: "#action.equals('EDIT')"
  - requestMapping: "/api/dataLabel/batchDeleteDataLabel.do"
    module: "配置"
    type: "删除"
    operation: "删除数据标签"
    description: "删除数据标签 \"${#names}\""
  - requestMapping: "/api/weaknessRule/add.do"
    conditions:
      - module: "弱点规则"
        type: "新增"
        operation: "新增弱点规则"
        description: "新增弱点规则\"${#name}\""
        condition: "#action.equals('ADD')"
      - module: "弱点规则"
        type: "编辑"
        operation: "编辑弱点规则"
        description: "编辑弱点规则\"${#name}\""
        condition: "#action.equals('EDIT')"
  - requestMapping: "/api/nacosSingelKey/updateNacosSingleKeys.do"
    conditions:
      - module: "配置"
        type: "编辑"
        operation: "编辑留存策略和组件开关"
        description: "编辑留存策略和组件开关"
  - requestMapping: "/api/uaType/saveUaTypeDto.do"
    conditions:
      - module: "终端配置"
        type: "新增"
        operation: "新增终端类型"
        description: "新增终端类型\"${#uaTypeDto.uaType}\""
        condition: "#uaTypeDto.id == null"
      - module: "终端配置"
        type: "编辑"
        operation: "编辑终端类型"
        description: "编辑终端类型\"${#uaTypeDto.uaType}\""
        condition: "#uaTypeDto.id != null"
  - requestMapping: "/api/uaType/delete.do"
    module: "终端配置"
    type: "删除"
    operation: "删除终端类型"
    description: "删除终端类型\"${#name}\""
  - requestMapping: "/api/basicConfig/save.do"
    module: "系统配置"
    type: "编辑"
    operation: "编辑基础信息配置"
    description: "编辑基础信息配置"
  - requestMapping: "/api/bigScreenConfig/saveBigScreenConfig"
    module: "系统配置"
    type: "编辑"
    operation: "编辑大屏配置"
    description: "编辑大屏配置"
  - requestMapping: "/api/bigScreenConfig/saveBigScreenConfig"
    module: "配置"
    type: "编辑"
    operation: "编辑大屏配置"
    description: "编辑大屏配置"
  - requestMapping: "/api/pluginManage/uploadReport.do"
    module: "插件管理"
    type: "导入"
    operation: "上传插件"
    description: "更新插件库"
  - requestMapping: "/task/taskmanage/stopTask.do"
    module: "配置"
    type: "编辑"
    operation: "任务管理暂停任务"
    description: "任务管理暂停任务"
  - requestMapping: "/task/taskmanage/startTask.do"
    module: "配置"
    type: "编辑"
    operation: "任务管理开始任务"
    description: "任务管理开始任务"
  - requestMapping: "/api/login/checkLogin.do"
    conditions:
      - module: "登录"
        type: "登录"
        operation: "登录成功"
        description: "${#msg}"
        condition: "#loginCode == 0"
      - module: "登录"
        type: "登录"
        operation: "登录失败"
        description: "${#msg}"
        condition: "#loginCode == -1"
      - module: "登录"
        type: "登录"
        operation: "账号停用"
        description: "${#msg}"
        condition: "#loginCode == -2"
  - requestMapping: "/api/login/logout"
    module: "登录"
    type: "登录"
    operation: "退出登录"
    description: "${#userName} : 退出登录成功"
  - requestMapping: "/api/sysUser/edit.do"
    module: "用户"
    type: "编辑"
    operation: "修改个人信息"
    description: "编辑个人信息"
  - requestMapping: "/api/export/removeTask.do"
    module: "数据导出管理"
    type: "删除"
    operation: "删除导出任务"
    description: "删除导出任务\"任务创建时间${#date}，所属模块${#name}\""
  - requestMapping: "/api/sysLog/export.do"
    module: "配置"
    type: "导出"
    operation: "导出操作日志"
    description: "导出操作日志"
  - requestMapping: "/api/buildOrder/buildOrder"
    module: "建单"
    type: "建单"
    operation: "建单"
    description: "${#logDesc}"
  - requestMapping: "/api/ipDataLabel/deleteIpLabel"
    module: "配置"
    type: "删除"
    operation: "删除IP标签"
    description: "删除IP标签\"${#name}\""
  - requestMapping: "/api/resourceDefine/saveKeywordSplitRule"
    conditions:
      - module: "资产定义"
        type: "新增"
        operation: "新增关键词资产拆分"
        description: "新增关键词\"${#keywordSplitRuleDto.keyword}\"资产拆分"
        condition: "#keywordSplitRuleDto.delFlag == null || #keywordSplitRuleDto.delFlag == false"
      - module: "资产定义"
        type: "删除"
        operation: "删除关键词资产拆分"
        description: "删除关键词\"${#keywordSplitRuleDto.keyword}\"资产拆分"
        condition: "#keywordSplitRuleDto.delFlag == true"
  - requestMapping: "/api/resourceDefine/updateKeywordSplitRule"
    conditions:
      - module: "资产定义"
        type: "编辑"
        operation: "编辑关键词资产拆分"
        description: "编辑关键词\"${#keywordSplitRuleDto.keyword}\"资产拆分"
        condition: "#keywordSplitRuleDto.delFlag == null || #keywordSplitRuleDto.delFlag == false"
  - requestMapping: "/api/httpApp/importApp.do"
    module: "应用"
    type: "导入"
    operation: "对应用导入自定义字段"
    description: "对应用导入自定义字段"
  - requestMapping: "/api/notify/readAll.do"
    module: "通知管理"
    type: "处理"
    operation: "通知设为全部已读"
    description: "通知设为全部已读"
  - requestMapping: "/mdr/justForDesc"
    module: "系统监控"
    type: "编辑"
    operation: "系统监控"
    description: "此日志只是为了系统监控模块加入到日志筛选"
  - requestMapping: "/api/featureLabel/delete.do"
    module: "配置"
    type: "删除"
    operation: "删除API标签"
    description: "删除API标签 \"${#names}\""
  - requestMapping: "/api/appFeatureLabel/delete.do"
    module: "配置"
    type: "删除"
    operation: "删除应用标签"
    description: "删除应用标签 \"${#names}\""
