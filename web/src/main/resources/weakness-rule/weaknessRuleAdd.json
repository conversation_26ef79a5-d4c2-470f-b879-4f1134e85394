[{"id": "code_repo_expose", "name": "代码仓库暴露", "type": "improper_inventory_management", "level": 3, "mode": 0, "typeName": "资产管理失当", "description": "源代码代码仓库中的文件泄露，如.git/目录、.svn目录等，攻击者可能利用这些文件还原程序源代码。", "pluginPolicies": [{"enable": true}], "matchRule": {"ruleConditions": [{"seq": 1, "type": "API", "feature": "apiUrl", "featureType": "FEATURE", "operator": "STR_CONTAINS_IGNORE_CASE", "value": ".git", "valueType": "PRIMITIVE"}, {"seq": 2, "type": "MESSAGE", "feature": "rspStatus", "featureType": "FEATURE", "operator": "EQ", "value": "200", "valueType": "PRIMITIVE"}, {"seq": 3, "type": "API", "feature": "apiUrl", "featureType": "FEATURE", "operator": "STR_CONTAINS_IGNORE_CASE", "value": ".bzr", "valueType": "PRIMITIVE"}, {"seq": 4, "type": "API", "feature": "apiUrl", "featureType": "FEATURE", "operator": "STR_CONTAINS_IGNORE_CASE", "value": ".svn", "valueType": "PRIMITIVE"}, {"seq": 5, "type": "API", "feature": "apiUrl", "featureType": "FEATURE", "operator": "STR_CONTAINS_IGNORE_CASE", "value": ".hg", "valueType": "PRIMITIVE"}, {"seq": 6, "type": "API", "feature": "apiUrl", "featureType": "FEATURE", "operator": "STR_CONTAINS_IGNORE_CASE", "value": ".idea", "valueType": "PRIMITIVE"}, {"seq": 7, "type": "API", "feature": "apiUrl", "featureType": "FEATURE", "operator": "STR_CONTAINS_IGNORE_CASE", "value": ".ds_store", "valueType": "PRIMITIVE"}], "decision": {"logic": "(1|3|4|5|6|7)&2"}}, "scopeRule": {"ruleConditions": [], "decision": {"logic": ""}}, "enable": true}, {"id": "cookie_not_httpOnly", "name": "cookie没有设置为httpOnly", "type": "other", "level": 1, "mode": 0, "typeName": "Web安全缺陷", "description": "未设置 HttpOnly 的 Cookie 可被客户端脚本访问，易在跨站脚本攻击（XSS）中被窃取，进而导致会话劫持等安全问题。", "pluginPolicies": [{"enable": true}], "matchRule": {"ruleConditions": [{"seq": 1, "type": "API", "feature": "apiFeatureLabels", "featureType": "FEATURE", "operator": "CONTAINS_ANY", "value": ["login"], "valueType": "PRIMITIVE"}, {"seq": 2, "type": "MESSAGE", "feature": "<PERSON><PERSON><PERSON><PERSON>", "featureType": "FEATURE", "operator": "NOT_CONTAINS_VALUE", "value": "httpOnly", "valueType": "PRIMITIVE"}], "decision": {"logic": "1&2"}}, "scopeRule": {"ruleConditions": [], "decision": {"logic": ""}}, "enable": true}, {"id": "database_connection_leaked", "name": "数据库连接信息泄漏", "type": "other", "level": 3, "mode": 0, "typeName": "Web安全缺陷", "description": "接口返回数据中泄漏了不必要的数据库连接信息，可能导致被攻击者获取后，利用连接信息访问数据库。", "pluginPolicies": [{"enable": true}], "matchRule": {"ruleConditions": [{"seq": 1, "type": "MESSAGE", "feature": "rspStatus", "featureType": "FEATURE", "operator": "EQ", "value": "200", "valueType": "PRIMITIVE"}, {"seq": 2, "type": "MESSAGE", "feature": "rspDataLabelIds", "featureType": "FEATURE", "operator": "CONTAINS_ANY", "value": ["jdbcUrl"], "valueType": "PRIMITIVE"}, {"seq": 3, "type": "MESSAGE", "feature": "rspContentType", "featureType": "FEATURE", "operator": "EQ", "value": "json", "valueType": "PRIMITIVE"}], "decision": {"logic": "1&2&3"}}, "scopeRule": {"ruleConditions": [], "decision": {"logic": ""}}, "enable": true}, {"id": "ssrf_weakness", "name": "SSRF", "type": "server_side_request_forgery", "level": 3, "mode": 0, "typeName": "服务端请求伪造", "description": "SSRF漏洞允许攻击者在服务端执行恶意请求，攻击者可能会利用SSRF漏洞访问内部敏感文件、绕过防火墙、发起端口扫描或攻击内部系统等。", "pluginPolicies": [{"enable": true}], "matchRule": {"ruleConditions": [{"seq": 1, "type": "MESSAGE", "feature": "rspStatus", "featureType": "FEATURE", "operator": "EQ", "value": "200", "valueType": "PRIMITIVE"}, {"seq": 2, "type": "MESSAGE", "feature": "reqUrl", "featureType": "FEATURE", "operator": "REGEX", "value": ".*(url|link|src)=(http|file|gopher).*", "valueType": "PRIMITIVE"}], "decision": {"logic": "1&2"}}, "scopeRule": {"ruleConditions": [], "decision": {"logic": ""}}, "enable": true}, {"id": "cors_weakness", "name": "CORS", "type": "broken_authentication", "level": 3, "mode": 0, "typeName": "身份认证缺陷", "description": "CORS(跨源资源共享)，当允许凭据（如 Cookie、HTTP 认证信息等）时，不能使用通配符 \\\"*\\\" 作为允许的源。必须明确指定允许的源（如请求中的 Origin），否则可能导致恶意网站利用用户凭据进行跨站请求，从而产生安全风险。", "pluginPolicies": [{"enable": true}], "matchRule": {"ruleConditions": [{"seq": 1, "type": "API", "feature": "apiLevel", "featureType": "FEATURE", "operator": "IN", "value": ["低敏感", "中敏感", "高敏感"], "valueType": "PRIMITIVE"}, {"seq": 2, "type": "CUSTOM_ADD", "feature": "access_control_allow_origin", "featureType": "GENERAL", "operator": "EQ", "value": "*", "valueType": "PRIMITIVE"}, {"seq": 3, "type": "CUSTOM_ADD", "feature": "access_control_allow_credentials", "featureType": "GENERAL", "operator": "EQ", "value": "true", "valueType": "PRIMITIVE"}], "decision": {"logic": "1&2&3"}}, "scopeRule": {"ruleConditions": [], "decision": {"logic": ""}}, "enable": true}]