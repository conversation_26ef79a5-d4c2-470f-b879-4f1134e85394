operations:
  - requestMapping: "/api/sysUser/password.do"
    module: "用户"
    type: "编辑"
    operation: "修改密码"
    description: "修改密码"
  - requestMapping: "/api/sysUser/sysPassword.do"
    module: "用户"
    type: "编辑"
    operation: "系统管理员修改密码"
    description: "修改用户\"${#name}\"的密码"
  - requestMapping: "/api/sysUser/save.do"
    conditions:
      - module: "用户"
        type: "新增"
        operation: "新增账号"
        description: "新增账号\"${#name}\""
        condition: "#action.equals('ADD')"
  - requestMapping: "/api/sysUser/batchRemove.do"
    module: "用户"
    type: "删除"
    operation: "删除账号"
    description: "删除账号\"${#name}\""
  - requestMapping: "/api/sysUser/lock.do"
    module: "用户"
    type: "编辑"
    operation: "锁定账号"
    description: "锁定账号\"${#name}\""
  - requestMapping: "/api/sysUser/unlock.do"
    module: "用户"
    type: "编辑"
    operation: "解锁账号"
    description: "解锁账号\"${#name}\""
  - requestMapping: "/api/sysUser/edit.do"
    module: "用户"
    type: "编辑"
    operation: "编辑账号"
    description: "编辑账号\"${#name}\""
  - requestMapping: "/api/sysRole/save.do"
    module: "用户"
    type: "新增"
    operation: "新增角色"
    description: "新增角色\"${#name}\""
  - requestMapping: "/api/sysRole/edit.do"
    module: "用户"
    type: "编辑"
    operation: "编辑角色"
    description: "编辑角色\"${#name}\""
  - requestMapping: "/api/sysRole/batchRemove.do"
    module: "用户"
    type: "删除"
    operation: "删除角色"
    description: "删除角色\"${#name}\""
  - requestMapping: "/api/assetAuthorization/addAssetAuthorization.do"
    module: "用户"
    type: "新增"
    operation: "新增资产权限"
    description: "${#logText}"
  - requestMapping: "/api/assetAuthorization/editAssetAuthorization.do"
    module: "用户"
    type: "编辑"
    operation: "编辑资产权限"
    description: "${#logText}"
  - requestMapping: "/api/assetAuthorization/delAssetAuthorization.do"
    module: "用户"
    type: "删除"
    operation: "删除资产权限"
    description: "${#logText}"