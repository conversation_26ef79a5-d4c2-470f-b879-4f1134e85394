{"id": "tunnel-slave-data-1h", "sources": [{"type": "MongoDB", "collections": [{"name": "httpApp", "order": 1, "updateTimestampField": "createTime", "ext": {"autoCreateReadProgress": true}}, {"name": "httpApi", "order": 2, "updateTimestampField": "createTime", "ext": {"autoCreateReadProgress": true}}, {"name": "httpApiWeakness", "order": 3, "updateTimestampField": "createTime", "ext": {"autoCreateReadProgress": true}}, {"name": "riskSample", "order": 4, "updateTimestampField": "createTime", "batchSize": 10}, {"name": "riskInfo", "order": 5, "updateTimestampField": "firstTime", "ext": {"autoCreateReadProgress": true}}]}], "transforms": [{"type": "SlaveHttpAppMapper"}, {"type": "IdMapper"}, {"type": "WeaknessSlaveMapper"}, {"type": "RiskSlaveMapper"}, {"type": "SlaveAssetFilterRuleMapper"}, {"type": "SlaveHttpApiMapper"}], "sinks": [{"type": "QZOpenAPI"}], "env": {"periodSeconds": 3600}}