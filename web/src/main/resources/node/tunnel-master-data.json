{"id": "tunnel-master-data", "name": "同步主节点数据", "env": {"maxRandomDelaySeconds": 0}, "sources": [{"type": "QZOpenAPI"}], "transforms": [{"type": "IdMapper"}, {"type": "ClickhouseConfigMapper"}, {"type": "MongoCollectionMergeMapper", "params": {"collections": {"accountInfo": {"primaryFields": ["account"], "mappings": [{"key": "firstDate", "func": "MIN"}, {"key": "lastDate", "func": "MAX"}]}, "accountMonthInfo": {"primaryFields": ["account", "month"], "mappings": [{"key": "updateTime", "func": "CURRENT_TIMESTAMP"}, {"key": "dateStats", "transKeyFunc": "NODE_DATE_STAT", "func": "TARGET"}, {"key": "dateStats", "func": "RESET"}]}, "fileInfo": {"primaryFields": ["id"], "mappings": [{"key": "timestamp", "func": "MIN"}, {"key": "updateTime", "func": "MAX"}]}, "ipInfo": {"primaryFields": ["ip"]}, "ipMonthInfo": {"primaryFields": ["ip", "month"], "mappings": [{"key": "updateTime", "func": "CURRENT_TIMESTAMP"}, {"key": "dateStats", "transKeyFunc": "NODE_DATE_STAT", "func": "TARGET"}, {"key": "dateStats", "func": "RESET"}]}, "httpApiMonthStat": {"primaryFields": ["uri", "month"], "mappings": [{"key": "updateTime", "func": "CURRENT_TIMESTAMP"}, {"key": "dateStats", "transKeyFunc": "NODE_DATE_STAT", "func": "TARGET"}, {"key": "activeEventTimestamp", "func": "MAX"}, {"key": "dateStats", "func": "RESET"}]}, "httpAppMonthStat": {"primaryFields": ["uri", "month"], "mappings": [{"key": "updateTime", "func": "CURRENT_TIMESTAMP"}, {"key": "dateStats", "transKeyFunc": "NODE_DATE_STAT", "func": "TARGET"}, {"key": "dateStats", "func": "RESET"}]}}}}, {"type": "MasterConfigIdMapper"}, {"type": "MasterHttpAppMergeMapper"}, {"type": "MasterHttpApiMergeMapper"}, {"type": "MasterCompositeRuleMergeMapper"}, {"type": "NodeMetaMapper", "params": {"collections": ["accountInfo", "ipInfo", "fileInfo", "httpApp", "httpApi", "compositeRule"]}}, {"type": "VisitMapper"}, {"type": "MasterHttpApiStructMergeMapper"}, {"type": "WeaknessMasterMapper"}, {"type": "RiskMasterMapper"}, {"type": "DataInfoMasterMapper"}, {"type": "RiskSampleMasterMapper"}, {"type": "MasterAssetFilterRuleMergeMapper"}, {"type": "SpELMapper", "params": {"collections": {"accountInfo": {"mappings": [{"expr": "values.put('relatedIpDistinctCnt',values.get('relatedIpList') == null ? 0 : values.get('relatedIpList').size())"}]}, "ipInfo": {"mappings": [{"expr": "values.put('relatedAccountDistinctCnt',values.get('relatedAccountList') == null ? 0 : values.get('relatedAccountList').size())"}]}}}}], "sinks": [{"type": "MongoDB"}, {"type": "Kafka"}]}