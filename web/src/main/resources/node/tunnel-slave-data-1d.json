{"id": "tunnel-slave-data-1d", "sources": [{"type": "MongoDB", "collections": [{"name": "httpApi", "order": 1, "updateTimestampField": "updateTime"}, {"name": "httpApiStruct", "order": 2, "updateTimestampField": "updateTime"}, {"name": "dataLabelMonthInfo", "order": 2, "updateTimestampField": "updateTime"}, {"name": "httpApiWeakness", "order": 3, "updateTimestampField": "updateTime"}, {"name": "assetFilterRule", "order": 4, "updateTimestampField": "updateTime"}, {"name": "filterHttpEvent", "order": 5, "updateTimestampField": "createTime"}, {"name": "riskSample", "order": 6, "updateTimestampField": "createTime", "batchSize": 10}, {"name": "riskInfo", "order": 7, "updateTimestampField": "lastTime"}]}], "transforms": [{"type": "IdMapper"}, {"type": "WeaknessSlaveMapper"}, {"type": "RiskSlaveMapper"}, {"type": "SlaveAssetFilterRuleMapper"}, {"type": "SlaveHttpApiMapper"}, {"type": "DataInfoSlaveMapper"}], "sinks": [{"type": "QZOpenAPI"}], "env": {"cron": "0 30 2 * * ?"}}