[{"_id": "AUDIT_V2_2.6.2_OFF_ACCOUNT_ACCOUNT_BASIC_INFO", "name": "离职人员审计中账号基本信息", "repositoryOperateList": [{"repository": "MONGO", "collectionName": "accountInfo", "operateActionEnum": "FINDONE", "frontCriteriaList": [{"property": "account", "predicate": "IS", "value": ""}], "operateRsp": {"key": "accountInfo"}}], "rspKeyMap": {"accountInfo": "accountInfo"}}, {"_id": "AUDIT_V2_2.6.2_OFF_ACCOUNT_ACCOUNT_RISK_INFO", "name": "离职人员审计中账号风险信息", "repositoryOperateList": [{"repository": "MONGO", "collectionName": "riskInfo", "operateActionEnum": "QUERY", "frontCriteriaList": [{"property": "entities.value", "predicate": "IS", "value": ""}, {"property": "date", "predicate": "BETWEEN", "value": []}, {"property": "entities.type", "predicate": "IS", "value": "ACCOUNT"}, {"property": "state", "predicate": "IN", "value": [0, 2, 3]}], "metabaseGroupOperations": [{"groupFields": ["date"], "aggregateOptionList": [{"aggrType": "ADDTOSET", "aggrField": "policySnapshot.name", "aggrAsField": "policyNames"}, {"aggrType": "SUM", "aggrField": "rspDataDistinctCnt", "aggrAsField": "distinctSensiValueCnt"}, {"aggrType": "FIRST", "aggrField": "date", "aggrAsField": "date"}]}], "sort": [{"property": "date", "order": "ASC"}], "operateRsp": {"key": "groupList"}}], "rspKeyMap": {"accountRiskInfo": "groupList"}}, {"_id": "AUDIT_V2_2.6.2_OFF_ACCOUNT_ACCOUNT_END_INFO", "repositoryOperateList": [{"selectFields": [{"selectAsField": "distinctSensiValueCnt", "selectField": "length( groupUniqArrayArray(arrayFlatten(labelValue.value)))"}, {"selectAsField": "totalCount", "selectField": "count(*)"}], "operateActionEnum": "QUERY", "operateRsp": {"key": "totalInfo", "backend2FrontConvert": {"udf": "var a = JSON.parse(originalStr); originalStr = JSON.stringify( a[0] )"}}, "frontCriteriaList": [{"predicate": "IS", "property": "taskId", "value": ""}, {"predicate": "IS", "property": "account", "value": ""}], "extraInfo": {"jobType": "AGGTASK", "queryFields": ["distinctSensiValueCnt", "totalCount"]}, "repository": "CLICKHOUSE", "collectionName": "off_account"}, {"selectFields": [{"selectAsField": "totalCount", "selectField": "count(*)"}, {"selectAsField": "ipDistinctCnt", "selectField": "count(DISTINCT ip)"}, {"selectAsField": "uaDistinctCnt", "selectField": "count(DISTINCT ua)"}, {"selectAsField": "hostDistinctCnt", "selectField": "count(DISTINCT host)"}], "operateActionEnum": "QUERY", "operateRsp": {"key": "loginInfo", "backend2FrontConvert": {"udf": "var a = JSON.parse(originalStr); originalStr = JSON.stringify( a[0] )"}}, "frontCriteriaList": [{"predicate": "IS", "property": "taskId", "value": ""}, {"predicate": "IS", "property": "account", "value": ""}], "extraInfo": {"jobType": "AGGTASK", "queryFields": ["ipDistinctCnt", "totalCount", "uaDistinctCnt", "hostDistinctCnt"]}, "repository": "CLICKHOUSE", "collectionName": "off_account"}, {"selectFields": [{"selectAsField": "fileCnt", "selectField": "count( distinct file_sha256)"}, {"selectAsField": "fileDistinctSensiValueCnt", "selectField": "length( groupUniqArrayArray(arrayFlatten(labelValue.value)))"}], "operateActionEnum": "QUERY", "operateRsp": {"key": "fileInfo", "backend2FrontConvert": {"udf": "var a = JSON.parse(originalStr); originalStr = JSON.stringify( a[0] )"}}, "frontCriteriaList": [{"predicate": "IS", "property": "taskId", "value": ""}, {"predicate": "IS", "property": "account", "value": ""}, {"predicate": "IS", "property": "isFile", "value": true}, {"predicate": "IS", "property": "file_fileDirection", "value": "DOWNLOAD"}], "extraInfo": {"jobType": "AGGTASK", "queryFields": ["fileCnt", "fileDistinctSensiValueCnt"]}, "repository": "CLICKHOUSE", "collectionName": "off_account"}], "rspKeyMap": {"accountEndFileInfo": "fileInfo", "accountEndInfo": "totalInfo", "accountEndLoginInfo": "loginInfo"}, "name": "离职人员审计中账号总结信息"}, {"_id": "AUDIT_V2_2.6.2_OFF_ACCOUNT_CHART", "repositoryOperateList": [{"selectFields": [{"selectAsField": "date", "selectField": "date"}, {"selectAsField": "eventCnt", "selectField": "count(*)"}], "operateActionEnum": "QUERY", "operateRsp": {"key": "eventCntList"}, "frontCriteriaList": [{"predicate": "IS", "property": "taskId", "value": ""}, {"predicate": "IS", "property": "account", "value": ""}], "metabaseGroupOperations": [{"aggregateOptionList": [], "groupFields": ["date"]}], "extraInfo": {"jobType": "AGGTASK", "queryFields": []}, "repository": "CLICKHOUSE", "collectionName": "off_account"}, {"selectFields": [{"selectAsField": "date", "selectField": "date"}, {"selectAsField": "distinctSensiValueCnt", "selectField": "length( groupUniqArrayArray(arrayFlatten(labelValue.value)))"}], "operateActionEnum": "QUERY", "operateRsp": {"key": "sensiValueCntList"}, "frontCriteriaList": [{"predicate": "IS", "property": "taskId", "value": ""}, {"predicate": "IS", "property": "account", "value": ""}], "metabaseGroupOperations": [{"aggregateOptionList": [], "groupFields": ["date"]}], "extraInfo": {"jobType": "AGGTASK", "queryFields": []}, "repository": "CLICKHOUSE", "collectionName": "off_account"}, {"selectFields": [{"selectAsField": "fileCnt", "selectField": "count(distinct file_sha256)"}, {"selectAsField": "date", "selectField": "date"}], "operateActionEnum": "QUERY", "operateRsp": {"key": "downloadFile"}, "frontCriteriaList": [{"predicate": "IS", "property": "taskId", "value": ""}, {"predicate": "IS", "property": "account", "value": ""}, {"predicate": "IS", "property": "isFile", "value": true}, {"predicate": "IS", "property": "file_fileDirection", "value": "DOWNLOAD"}], "metabaseGroupOperations": [{"aggregateOptionList": [], "groupFields": ["date"]}], "extraInfo": {"jobType": "AGGTASK", "queryFields": []}, "repository": "CLICKHOUSE", "collectionName": "off_account"}], "rspKeyMap": {"eventDateChart": "eventCntList", "sensiValueDateChart": "sensiValueCntList", "downloadFileDateChart": "downloadFile"}, "name": "离职人员审计行为趋势聚合"}]