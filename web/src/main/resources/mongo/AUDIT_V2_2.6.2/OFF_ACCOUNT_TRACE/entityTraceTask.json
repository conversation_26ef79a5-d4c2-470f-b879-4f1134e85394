[{"_id": "AUDIT_V2_2.6.2_ENTITY_TRACE_DETAIL", "name": "主体溯源分析结果", "repositoryOperateList": [{"repository": "CLICKHOUSE", "collectionName": "event_trace", "operateActionEnum": "COUNT", "frontCriteriaList": [{"property": "taskId", "predicate": "IS", "value": "aaa"}], "extraInfo": {"jobType": "AGGTASK", "queryFields": ["COUNT"]}, "operateRsp": {"key": "totalCount", "backend2FrontConvert": {"udf": "var a = JSON.parse(originalStr); originalStr = a[0]['COUNT'].toString()", "udfRspType": "NUMBER"}}}, {"repository": "CLICKHOUSE", "collectionName": "event_trace", "operateActionEnum": "QUERY", "frontCriteriaList": [{"property": "taskId", "predicate": "IS", "value": "aaa"}], "selectFields": [{"selectField": "sum(length(arrayFlatten(labelValue.value)))", "selectAsField": "sensiValueCnt"}], "extraInfo": {"jobType": "AGGTASK", "queryFields": ["sensiValueCnt"]}, "operateRsp": {"key": "sensiValueCnt", "backend2FrontConvert": {"udf": "var a = JSON.parse(originalStr); originalStr = a[0]['sensiValueCnt'].toString()", "udfRspType": "NUMBER"}}}, {"repository": "CLICKHOUSE", "collectionName": "event_trace", "operateActionEnum": "QUERY", "frontCriteriaList": [{"property": "taskId", "predicate": "IS", "value": "aaa"}], "selectFields": [{"selectField": "length( groupUniqArrayArray(arrayFlatten(labelValue.value)) )", "selectAsField": "distinctSensiValueCnt"}], "extraInfo": {"jobType": "AGGTASK", "queryFields": ["distinctSensiValueCnt"]}, "operateRsp": {"key": "distinctSensiValueCnt", "backend2FrontConvert": {"udf": "var a = JSON.parse(originalStr); originalStr = a[0]['distinctSensiValueCnt'].toString()", "udfRspType": "NUMBER"}}}, {"repository": "CLICKHOUSE", "collectionName": "event_trace", "operateActionEnum": "QUERY", "selectFields": [{"selectField": "labelValue.label", "selectAsField": "labelKey"}, {"selectField": "length( groupUniqArrayArray( labelValue.value) )", "selectAsField": "labelCnt"}], "metabaseGroupOperations": [{"groupFields": ["labelValue.label"], "aggregateOptionList": []}], "childCollectionNameOperate": {"repository": "CLICKHOUSE", "collectionName": "future_event_trace ARRAY JOIN labelValue ", "operateActionEnum": "QUERY", "selectFields": [{"selectField": "labelValue.label"}, {"selectField": "labelValue.value"}], "frontCriteriaList": [{"property": "taskId", "predicate": "IS", "value": "aaa"}]}, "extraInfo": {"jobType": "AGGTASK", "queryFields": ["labelKey", "labelCnt"]}, "operateRsp": {"key": "distinctCnt"}}, {"repository": "CLICKHOUSE", "collectionName": "event_trace", "operateActionEnum": "QUERY", "selectFields": [{"selectField": "labelValue.label", "selectAsField": "labelKey"}, {"selectField": "groupUniqArrayArray( labelValue.value) ", "selectAsField": "labelDistinctValue"}], "metabaseGroupOperations": [{"groupFields": ["labelValue.label"], "aggregateOptionList": []}], "childCollectionNameOperate": {"repository": "CLICKHOUSE", "collectionName": "future_event_trace ARRAY JOIN labelValue ", "operateActionEnum": "QUERY", "selectFields": [{"selectField": "labelValue.label"}, {"selectField": "labelValue.value"}], "frontCriteriaList": [{"property": "taskId", "predicate": "IS", "value": "aaa"}]}, "extraInfo": {"jobType": "AGGTASK", "queryFields": ["labelKey", "labelDistinctValue"]}, "operateRsp": {"key": "distinctValue"}}], "rspKeyMap": {"totalCount": "totalCount", "sensiValueCnt": "sensiValueCnt", "distinctSensiValueCnt": "distinctSensiValueCnt", "distinctCnt": "distinctCnt", "distinctValue": "distinctValue"}}]