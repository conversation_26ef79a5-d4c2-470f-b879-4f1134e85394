[{"_id": "AUDIT_V2_2.6.2_SPECIAL_ACCOUNT_INFO", "name": "特权账号审计中登录趋势/特权接口访问次数/风险趋势", "repositoryOperateList": [{"repository": "CLICKHOUSE", "collectionName": "special_account", "operateActionEnum": "QUERY", "frontCriteriaList": [{"property": "taskId", "predicate": "IS", "value": ""}, {"property": "account", "predicate": "IS", "value": ""}, {"property": "classifications", "predicate": "HAS_ANY_CLICK_HOUSE", "value": ["login"]}], "selectFields": [{"selectField": "count(*)", "selectAsField": "eventDistinctCnt"}, {"selectField": "date", "selectAsField": "date"}], "metabaseGroupOperations": [{"groupFields": ["date"], "aggregateOptionList": []}], "extraInfo": {"jobType": "AGGTASK", "queryFields": []}, "operateRsp": {"key": "accountDateLoginEvent"}}, {"repository": "CLICKHOUSE", "collectionName": "special_account", "operateActionEnum": "QUERY", "frontCriteriaList": [{"property": "taskId", "predicate": "IS", "value": ""}, {"property": "account", "predicate": "IS", "value": ""}, {"property": "apiUrl", "predicate": "IN", "value": []}], "selectFields": [{"selectField": "count(*)", "selectAsField": "eventDistinctCnt"}, {"selectField": "date", "selectAsField": "date"}], "metabaseGroupOperations": [{"groupFields": ["date"], "aggregateOptionList": []}], "extraInfo": {"jobType": "AGGTASK", "queryFields": []}, "operateRsp": {"key": "accountDateSpecialApiEvent"}}, {"repository": "MONGO", "collectionName": "aggRiskInfo", "operateActionEnum": "QUERY", "frontCriteriaList": [{"property": "entities.value", "predicate": "IS", "value": ""}, {"property": "date", "predicate": "BETWEEN", "value": []}], "metabaseGroupOperations": [{"groupFields": ["date"], "aggregateOptionList": [{"aggrType": "COUNT", "aggrField": "count"}]}], "sort": [{"property": "_id", "order": "ASC"}], "operateRsp": {"key": "accountDateRiskInfoAggEvent", "backend2FrontConvert": {"udf": "var a=JSON.parse(originalStr);\nvar b = [];\nif(a && a.length>0) {\n    for(var i = 0;i < a.length;i++) {\n        if(a[i].hasOwnProperty(\"_id\")) {\n            var year = a[i]['_id'].substring(0,4);\n            var month = a[i]['_id'].substring(4,6);\n            var day = a[i]['_id'].substring(6);\n            var date = year + \"-\" + month + \"-\" + day;\n            b.push({\n                \"date\":date,\n                \"eventDistinctCnt\":a[i][\"count\"]\n            })\n        }\n    }\n}\noriginalStr = JSON.stringify(b)", "udfRspType": "JSON"}}}], "rspKeyMap": {"accountDateLoginEvent": "accountDateLoginEvent", "accountDateSpecialApiEvent": "accountDateSpecialApiEvent", "accountDateRiskInfoAggEvent": "accountDateRiskInfoAggEvent"}}, {"_id": "AUDIT_V2_2.6.2_SPECIAL_ACCOUNT_CNT", "name": "特权账号审计中登录次数/特权账号访问次数/风险数", "repositoryOperateList": [{"repository": "CLICKHOUSE", "collectionName": "special_account", "operateActionEnum": "QUERY", "frontCriteriaList": [{"property": "taskId", "predicate": "IS", "value": ""}, {"property": "account", "predicate": "IS", "value": ""}, {"property": "classifications", "predicate": "HAS_ANY_CLICK_HOUSE", "value": ["login"]}], "selectFields": [{"selectField": "count(*)", "selectAsField": "eventDistinctCnt"}], "extraInfo": {"jobType": "AGGTASK", "queryFields": []}, "operateRsp": {"key": "accountDateLoginEventCnt"}}, {"repository": "CLICKHOUSE", "collectionName": "special_account", "operateActionEnum": "QUERY", "frontCriteriaList": [{"property": "taskId", "predicate": "IS", "value": ""}, {"property": "account", "predicate": "IS", "value": ""}, {"property": "apiUrl", "predicate": "IN", "value": []}], "selectFields": [{"selectField": "count(*)", "selectAsField": "eventDistinctCnt"}], "extraInfo": {"jobType": "AGGTASK", "queryFields": []}, "operateRsp": {"key": "accountDateSpecialApiEventCnt"}}, {"repository": "MONGO", "collectionName": "aggRiskInfo", "operateActionEnum": "COUNT", "frontCriteriaList": [{"property": "entities.value", "predicate": "IS", "value": ""}, {"property": "date", "predicate": "BETWEEN", "value": []}], "operateRsp": {"key": "accountDateRiskInfoAggEventCnt"}}], "rspKeyMap": {"accountDateLoginEventCnt": "accountDateLoginEventCnt", "accountDateSpecialApiEventCnt": "accountDateSpecialApiEventCnt", "accountDateRiskInfoAggEventCnt": "accountDateRiskInfoAggEventCnt"}}]