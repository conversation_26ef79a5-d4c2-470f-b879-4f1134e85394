[{"_id": "AUDIT_V2_2.6.2_TRACE_TASK_RECORD_LIST", "name": "任务列表", "repositoryOperateList": [{"repository": "MONGO", "collectionName": "traceTaskRecord", "operateActionEnum": "QUERY", "frontCriteriaList": [{"property": "name", "predicate": "REGEX", "value": ""}, {"property": "traceTaskTypeEnum", "predicate": "IS", "value": "OFF_ACCOUNT"}], "page": 1, "limit": 10, "sort": [{"property": "createTime", "order": "DESC"}], "operateRsp": {"key": "rows"}}, {"repository": "MONGO", "collectionName": "traceTaskRecord", "operateActionEnum": "COUNT", "frontCriteriaList": [{"property": "name", "predicate": "REGEX", "value": ""}, {"property": "traceTaskTypeEnum", "predicate": "IS", "value": "OFF_ACCOUNT"}], "sort": [{"property": "createTime", "order": "DESC"}], "operateRsp": {"key": "count"}}], "rspKeyMap": {"rows": "rows", "totalCount": "count"}}, {"_id": "AUDIT_V2_2.6.2_TRACE_TASK_RECORD_DETAIL", "name": "单个任务详情", "repositoryOperateList": [{"repository": "MONGO", "collectionName": "traceTaskRecord", "operateActionEnum": "FINDONE", "id": "", "operateRsp": {"key": "taskDetail"}}], "rspKeyMap": {"taskDetail": "taskDetail"}}, {"_id": "AUDIT_V2_2.6.2_TRACE_TASK_TEMPLATE_DETAIL", "name": "单个模板详情", "repositoryOperateList": [{"repository": "MONGO", "collectionName": "traceTaskTemplate", "operateActionEnum": "FINDONE", "id": "", "operateRsp": {"key": "taskTemplateDetail"}}], "rspKeyMap": {"taskTemplateDetail": "taskTemplateDetail"}}, {"_id": "AUDIT_V2_2.6.2_TRACE_TASK_OFF_ACCOUNT_EXTRA_DETAIL", "name": "离职人员审计详情extra信息", "repositoryOperateList": [{"repository": "MONGO", "collectionName": "traceTaskRecordExtraInfo", "operateActionEnum": "FINDONE", "frontCriteriaList": [{"property": "taskId", "predicate": "IS", "value": ""}], "operateRsp": {"key": "taskExtraDetail"}}], "rspKeyMap": {"taskExtraDetail": "taskExtraDetail"}}, {"_id": "AUDIT_V2_2.6.2_TRACE_TASK_SPECIAL_ACCOUNT_EXTRA_DETAIL", "name": "特权账号审计详情extra信息", "repositoryOperateList": [{"repository": "MONGO", "collectionName": "traceTaskRecordExtraInfo", "operateActionEnum": "FINDONE", "frontCriteriaList": [{"property": "taskId", "predicate": "IS", "value": ""}], "operateRsp": {"key": "taskExtraDetail"}}], "rspKeyMap": {"taskExtraDetail": "taskExtraDetail"}}, {"_id": "AUDIT_V2_2.6.2_TRACE_TASK_ENTITY_EXTRA_DETAIL", "name": "主体溯源审计详情extra信息", "repositoryOperateList": [{"repository": "MONGO", "collectionName": "traceTaskRecordExtraInfo", "operateActionEnum": "FINDONE", "frontCriteriaList": [{"property": "taskId", "predicate": "IS", "value": ""}], "operateRsp": {"key": "taskExtraDetail"}}], "rspKeyMap": {"taskExtraDetail": "taskExtraDetail"}}]