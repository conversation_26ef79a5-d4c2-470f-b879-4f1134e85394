db.menuConfig.deleteMany({})
db.menuConfig.save({
    "code": "account_management",
    "name": "账号管理",
    "type": "MENU",
    "children": [
        {
            "code": "apiSysUser",
            "name": "账号相关",
            "type": "FUNCTION",
            "url": "/api/sysUser/*"
        }
    ],
    "delFlag": false
})
db.menuConfig.save({
    "code": "data_group_management",
    "name": "数据分组管理",
    "type": "MENU",
    "children": [
        {
            "code": "dataGroup",
            "name": "数据分组相关",
            "type": "FUNCTION",
            "url": "/api/dataGroup/*"
        }
    ],
    "delFlag": false
})
db.menuConfig.save({
    "code": "role_management",
    "name": "角色管理",
    "type": "MENU",
    "children": [
        {
            "code": "sysRole",
            "name": "角色相关",
            "type": "FUNCTION",
            "url": "/api/sysRole/*"
        }
    ],
    "delFlag": false
})
db.menuConfig.save({
    "code": "safe_config",
    "name": "安全配置",
    "type": "MENU",
    "children": [
        {
            "code": "safe_config_updateNacosSingleKeys",
            "name": "基础安全配置",
            "type": "FUNCTION",
            "action": "WRITE",
            "url": "/api/nacosSingelKey/updateNacosSingleKeys.do"
        },
        {
            "code": "blackAndWhiteList",
            "name": "访问IP列表",
            "type": "FUNCTION",
            "action": "WRITE",
            "url": "/ip/blackAndWhite/list"
        }
    ],
    "delFlag": false
})
db.menuConfig.save({
    "code": "audit_record",
    "name": "操作日志",
    "type": "MENU",
    "children": [
        {
            "code": "sysLog",
            "name": "操作日志相关",
            "type": "FUNCTION",
            "url": "/api/sysLog/*"
        }
    ],
    "delFlag": false
})
db.menuConfig.save({
    "code": "overview",
    "name": "概览",
    "type": "MENU",
    "children": [
        {
            "code": "apiHome",
            "name": "概览相关",
            "type": "FUNCTION",
            "url": "/api/home/<USER>"
        }
    ],
    "delFlag": false
})
db.menuConfig.save({
    "code": "api_list",
    "name": "API",
    "type": "MENU",
    "children": [
        {
            "code": "getHttpApis.do",
            "name": "查看API列表",
            "type": "FUNCTION",
            "url": "/api/httpApi/getHttpApis.do"
        },
        {
            "code": "/api/httpApi/apiGroup",
            "name": "API分组",
            "type": "FUNCTION",
            "url": "/api/httpApi/apiGroup"
        },
        {
            "code": "save_api_view",
            "name": "保存API视图",
            "type": "FUNCTION",
            "url": "/api/viewMenu/editViewMenu",
            "action": "WRITE",
            "spelExpression": "#viewMenu.moduleId.equals('ApiViewMenuProcess')"
        },
        {
            "code": "updateApiState",
            "name": "修改API确认状态",
            "type": "FUNCTION",
            "url": "/api/httpApi/updateApiState",
            "action": "WRITE"
        },
        {
            "code": "updateHttpApi.do",
            "name": "编辑API",
            "type": "FUNCTION",
            "url": "/api/httpApi/updateHttpApi.do",
            "action": "WRITE"
        },
        {
            "code": "exportHttpApi",
            "name": "导出API",
            "type": "FUNCTION",
            "url": "/api/export/export",
            "action": "WRITE",
            "spelExpression": "#requestBody[taskType].equals('EXPORT_API')"
        },
        {
            "code": "/api/httpApi/singleUpdateApiLogging",
            "name": "操作审计状态",
            "type": "FUNCTION",
            "url": "/api/httpApi/singleUpdateApiLogging",
            "action": "WRITE"
        },
        {
            "code": "/api/httpApi/batchUpdateApiLogging",
            "name": "批量操作审计状态",
            "type": "FUNCTION",
            "url": "/api/httpApi/batchUpdateApiLogging",
            "action": "WRITE"
        },
        {
            "code": "api/httpApi/dealApi",
            "name": "同步API",
            "type": "FUNCTION",
            "url": "/api/httpApi/dealApi",
            "action": "WRITE"
        },
        {
            "code": "api/httpApi/apiFilter",
            "name": "过滤API",
            "type": "FUNCTION",
            "url": "/api/httpApi/apiFilter",
            "action": "WRITE"
        }
    ],
    "delFlag": false
})
db.menuConfig.save({
    "code": "webapp",
    "name": "应用",
    "type": "MENU",
    "children": [
        {
            "code": "listData.do",
            "name": "查看应用列表",
            "type": "FUNCTION",
            "url": "/api/httpApp/listData.do"
        },
        {
            "code": "editApp",
            "name": "编辑应用",
            "type": "FUNCTION",
            "url": "/api/httpApp/edit.do",
            "action": "WRITE"
        },
        {
            "code": "exportApp",
            "name": "导出应用",
            "type": "FUNCTION",
            "url": "/api/export/export",
            "action": "WRITE",
            "spelExpression": "#requestBody[taskType].equals('EXPORT_APP')"
        },
        {
            "code": "/api/httpApp/appFilter",
            "name": "过滤APP",
            "type": "FUNCTION",
            "url": "/api/httpApp/appFilter",
            "action": "WRITE"
        },
        {
            "code": "/api/httpApp/importApp.do",
            "name": "导入自定义字段",
            "type": "FUNCTION",
            "url": "/api/httpApp/importApp.do",
            "action": "WRITE"
        },
        {
            "code": "save_app_view",
            "name": "保存应用视图",
            "type": "FUNCTION",
            "url": "/api/viewMenu/editViewMenu",
            "action": "WRITE",
            "spelExpression": "#viewMenu.moduleId.equals('AppViewMenuProcess')"
        },
        {
            "code": "/api/httpApp/dealApp",
            "name": "同步应用",
            "type": "FUNCTION",
            "url": "/api/httpApp/dealApp",
            "action": "WRITE"
        },
        {
            "code": "/api/httpApp/appMerge",
            "name": "合并应用",
            "type": "FUNCTION",
            "url": "/api/httpApp/appMerge",
            "action": "WRITE"
        }
    ],
    "delFlag": false
})
db.menuConfig.save({
    "code": "data",
    "name": "数据",
    "type": "MENU",
    "children": [
        {
            "code": "apiData",
            "name": "数据相关",
            "type": "FUNCTION",
            "url": "/api/data/*",
            "action": "WRITE"
        },
        {
            "code": "save_data_view",
            "name": "保存数据视图",
            "type": "FUNCTION",
            "url": "/api/viewMenu/editViewMenu",
            "action": "WRITE",
            "spelExpression": "#viewMenu.moduleId.equals('DataViewMenuProcess')"
        },
        {
            "code": "exportData",
            "name": "导出数据",
            "type": "FUNCTION",
            "url": "/api/export/export",
            "action": "WRITE",
            "spelExpression": "#requestBody[taskType].equals('EXPORT_DATA_MONTH_INFO')"
        }

    ],
    "delFlag": false
})
db.menuConfig.save({
    "code": "weakness",
    "name": "弱点",
    "type": "MENU",
    "children": [
        {
            "code": "apiWeakness",
            "name": "弱点相关",
            "type": "FUNCTION",
            "url": "/api/apiWeakness/*",
            "action": "WRITE"
        },
        {
            "code": "save_weakness_view",
            "name": "保存弱点视图",
            "type": "FUNCTION",
            "url": "/api/viewMenu/editViewMenu",
            "action": "WRITE",
            "spelExpression": "#viewMenu.moduleId.equals('WeaknessViewMenuProcess')"
        }
    ],
    "delFlag": false
})
db.menuConfig.save({
    "code": "risk",
    "name": "风险",
    "type": "MENU",
    "children": [
        {
            "code": "risk_list",
            "name": "风险",
            "type": "MENU",
            "children": [
                {
                    "code": "listAggRisk",
                    "name": "查询风险清单",
                    "type": "FUNCTION",
                    "url": "/api/aggRisk/listRiskInfo"
                },
                {
                    "code": "groupAggRisk",
                    "name": "分组风险清单",
                    "type": "FUNCTION",
                    "url": "/api/aggRisk/groupRiskInfo"
                }
            ]
        },
        {
            "code": "threat",
            "name": "威胁",
            "type": "MENU",
            "children": [
                {
                    "code": "listThreatInfo",
                    "name": "查询威胁清单",
                    "type": "FUNCTION",
                    "url": "/api/threatInfo/listThreatInfo"
                },
                {
                    "code": "getThreatInfoGroup",
                    "name": "分组威胁清单",
                    "type": "FUNCTION",
                    "url": "/api/threatInfo/getThreatInfoGroup"
                },
                {
                    "code": "getRiskNames",
                    "name": "风险事件枚举",
                    "type": "FUNCTION",
                    "url": "/api/threatInfo/getRiskNames"
                }
            ]
        },
        {
            "code": "exception",
            "name": "异常",
            "type": "MENU",
            "children": [
                {
                    "code": "listRiskInfo",
                    "name": "查询异常清单",
                    "type": "FUNCTION",
                    "url": "/api/riskV2/listRiskInfo"
                },
                {
                    "code": "groupRiskInfo",
                    "name": "分组异常清单",
                    "type": "FUNCTION",
                    "url": "/api/riskV2/groupRiskInfo"
                }
            ]
        }

    ],
    "delFlag": false
})
db.menuConfig.save({
    "code": "audit",
    "name": "审计",
    "type": "MENU",
    "children": [
        {
            "code": "analysis_ip",
            "name": "IP",
            "type": "MENU",
            "children": [
                {
                    "code": "ip_list",
                    "name": "IP列表",
                    "type": "FUNCTION",
                    "url": ""
                }
            ]
        },
        {
            "code": "analysis_account",
            "name": "账号",
            "type": "MENU",
            "children": [
                {
                    "code": "account_list",
                    "name": "账号列表",
                    "type": "FUNCTION",
                    "url": ""
                },
                {
                    "code": "account_parse",
                    "name": "账号解析配置列表",
                    "type": "FUNCTION",
                    "url": "/appParse/*"
                },
                {
                    "code": "account_parse_v2",
                    "name": "账号解析配置",
                    "type": "FUNCTION",
                    "url": "/apiParse/*"
                },
                {
                    "code": "account_parse_save",
                    "name": "保存解析配置",
                    "type": "FUNCTION",
                    "action": "WRITE",
                    "url": "/apiParse/saveParseConfigs"
                }
            ]
        },
        {
            "code": "analysis_file",
            "name": "文件",
            "type": "MENU",
            "children": [
                {
                    "code": "file_list",
                    "name": "账号列表",
                    "type": "FUNCTION",
                    "url": ""
                }
            ]
        },
        {
            "code": "analysis_trace",
            "name": "溯源",
            "type": "MENU",
            "children": []
        },
        {
            "code": "analysis_log",
            "name": "日志",
            "type": "MENU",
            "children": []
        }

    ],
    "delFlag": false
})
db.menuConfig.save({
    "code": "report",
    "name": "报告",
    "type": "MENU",
    "children": [
        {
            "code": "report_config",
            "name": "操作报表相关",
            "type": "FUNCTION",
            "url": "/api/report/*"
        }
    ],
    "delFlag": false
})
db.menuConfig.save({
    "code": "situation",
    "name": "态势",
    "type": "MENU",
    "children": [],
    "delFlag": false
})
db.menuConfig.save({
    "code": "control",
    "name": "管控",
    "type": "MENU",
    "children": [
        {
            "code": "control_upstream",
            "name": "上游",
            "type": "MENU",
            "children": []
        },
        {
            "code": "control_route",
            "name": "路由",
            "type": "MENU",
            "children": []
        },
        {
            "code": "control_cluster",
            "name": "集群",
            "type": "MENU",
            "children": []
        }
    ],
    "delFlag": false
})
db.menuConfig.save({
    "code": "setting_api",
    "name": "API配置",
    "type": "MENU",
    "children": [
        {
            "code": "api_settings_labels",
            "name": "API标签",
            "type": "MENU",
            "children": [
                {
                    "code": "/api/featureLabel/pageApiFeatureLabel",
                    "name": "标签列表",
                    "type": "FUNCTION",
                    "url": "/api/featureLabel/pageApiFeatureLabel"
                },
                {
                    "code": "",
                    "name": "保存标签",
                    "type": "FUNCTION",
                    "url": "/api/featureLabel/save.do"
                }
            ]
        },
        {
            "code": "api_settings_sensi_level",
            "name": "API敏感等级",
            "type": "MENU",
            "children": [
                {
                    "code": "/resource/level/list",
                    "name": "敏感等级列表",
                    "type": "FUNCTION",
                    "url": "/resource/level/list"
                },
                {
                    "code": "/resource/level/save",
                    "name": "保存敏感等级",
                    "type": "FUNCTION",
                    "url": "/resource/level/save",
                    "action":"WRITE"
                }
            ]
        },
        {
            "code": "api_settings_risk_level",
            "name": "API风险等级",
            "type": "MENU",
            "children": [
                {
                    "code": "apiRiskLevel_list",
                    "name": "API风险等级列表",
                    "type": "FUNCTION",
                    "url": "/api/riskLevel/getRiskLevels",
                    "spelExpression": "#riskLevel.levelType.equals('API')",
                },
                {
                    "code": "apiRiskLevel_save",
                    "name": "保存风险等级",
                    "type": "FUNCTION",
                    "url": "/api/riskLevel/saveRiskLevel",
                    "spelExpression": "#riskLevel.levelType.equals('API')",
                    "action":"WRITE"
                }
            ]
        },
        {
            "code": "api_settings_state",
            "name": "API状态",
            "type": "MENU",
            "children": [
                {
                    "code": "api_state_list",
                    "name": "API状态列表",
                    "type": "FUNCTION",
                    "url": "/api/assetLifeStateConfig/getByType",
                    "spelExpression": "#assetType.equals('API')",
                },
                {
                    "code": "api_state_save",
                    "name": "API状态保存",
                    "type": "FUNCTION",
                    "url": "/api/assetLifeStateConfig/save",
                    "spelExpression": "#assetLifeStateConfig.assetType.equals('API')",
                    "action":"WRITE"
                }
            ]
        },
        {
            "code": "api_settings_log",
            "name": "API审计策略",
            "type": "MENU",
            "children": [
                {
                    "code": "/api/moniorPolicy",
                    "name": "API审计策略",
                    "type": "FUNCTION",
                    "url": "/api/moniorPolicy/*",
                },
            ]
        }
    ],
    "delFlag": false
})
db.menuConfig.save({
    "code": "setting_app",
    "name": "应用配置",
    "type": "MENU",
    "children": [
        {
            "code": "app_settings_analysis",
            "name": "域名解析配置",
            "type": "MENU",
            "children": [
                {
                    "code": "app_host_analysis",
                    "name": "域名解析相关",
                    "type": "FUNCTION",
                    "url": "/api/hostAnalysisV2/*"
                }
            ]
        },
        {
            "code": "app_settings_labels",
            "name": "应用标签",
            "type": "MENU",
            "children": [
                {
                    "code": "app_feature",
                    "name": "应用标签相关",
                    "type": "FUNCTION",
                    "url": "/api/appFeatureLabel/*"
                }
            ]
        },
        {
            "code": "app_settings_state",
            "name": "应用状态",
            "type": "MENU",
            "children": [
                {
                    "code": "app_state_list",
                    "name": "APP状态列表",
                    "type": "FUNCTION",
                    "url": "/api/assetLifeStateConfig/getByType",
                    "spelExpression": "#assetType.equals('APP')",
                },
                {
                    "code": "app_state_save",
                    "name": "APP状态保存",
                    "type": "FUNCTION",
                    "url": "/api/assetLifeStateConfig/save",
                    "spelExpression": "#assetLifeStateConfig.assetType.equals('APP')",
                    "action":"WRITE"
                }
            ]
        }
    ],
    "delFlag": false
})
db.menuConfig.save({
    "code": "setting_assets",
    "name": "资产配置",
    "type": "MENU",
    "children": [
        {
            "code": "assets_filter",
            "name": "APIs过滤",
            "type": "MENU",
            "children": [
                {
                    "code": "api_filter",
                    "name": "资产过滤相关",
                    "type": "FUNCTION",
                    "url": "/api/resourceFilter/*"
                },
                {
                    "code": "smart_filter",
                    "name": "智能过滤相关",
                    "type": "FUNCTION",
                    "url": "/api/smartFilterRule/*"
                }
            ]
        },
        {
            "code": "assets_definition",
            "name": "资产定义",
            "type": "MENU",
            "children": [
                {
                    "code": "asset_resourceDefine",
                    "name": "资产定义相关",
                    "type": "FUNCTION",
                    "url": "/api/resourceDefine/*"
                }
            ]
        }
    ],
    "delFlag": false
})
db.menuConfig.save({
    "code": "setting_ip",
    "name": "IP配置",
    "type": "MENU",
    "children": [
        {
            "code": "ip_settings_analysis",
            "name": "IP解析配置",
            "type": "MENU",
            "children": [
                {
                    "code": "originIpResolve",
                    "name": "源IP解析相关",
                    "type": "FUNCTION",
                    "url": "/api/originIpResolve/*"
                },
                {
                    "code": "dstIpResolve",
                    "name": "目的IP解析相关",
                    "type": "FUNCTION",
                    "url": "/api/dstIpResolve/*"
                }
            ]
        },
        {
            "code": "ip_settings_risk_level",
            "name": "IP风险等级",
            "type": "MENU",
            "children": [
                {
                    "code": "ipRiskLevel_list",
                    "name": "API风险等级列表",
                    "type": "FUNCTION",
                    "url": "/api/riskLevel/getRiskLevels",
                    "spelExpression": "#riskLevel.levelType.equals('IP')",
                },
                {
                    "code": "ipRiskLevel_save",
                    "name": "保存风险等级",
                    "type": "FUNCTION",
                    "url": "/api/riskLevel/saveRiskLevel",
                    "spelExpression": "#riskLevel.levelType.equals('IP')",
                    "action":"WRITE"
                }
            ]
        },
        {
            "code": "network_segment",
            "name": "自由网段",
            "type": "MENU",
            "children": [
                {
                    "code": "network_segment",
                    "name": "自由网段列表",
                    "type": "FUNCTION",
                    "url": "/api/networkSegment/*"
                }
            ]
        },
        {
            "code": "ip_settings_database",
            "name": "IP库配置",
            "type": "MENU",
            "children": []
        }
    ],
    "delFlag": false
})
db.menuConfig.save({
    "code": "setting_account",
    "name": "账号配置",
    "type": "MENU",
    "children": [
        {
            "code": "account_settings_analysis",
            "name": "账号解析配置",
            "type": "MENU",
            "children": [
                {
                    "code": "appParse",
                    "name": "账号解析配置",
                    "type": "FUNCTION",
                    "url": "/appParse/*"
                }
            ]
        },
        {
            "code": "account_risk_level",
            "name": "账号风险等级",
            "type": "MENU",
            "children": [
                {
                    "code": "accountRiskLevel_list",
                    "name": "API风险等级列表",
                    "type": "FUNCTION",
                    "url": "/api/riskLevel/getRiskLevels",
                    "spelExpression": "#riskLevel.levelType.equals('ACCOUNT')",
                },
                {
                    "code": "accountRiskLevel_save",
                    "name": "保存风险等级",
                    "type": "FUNCTION",
                    "url": "/api/riskLevel/saveRiskLevel",
                    "spelExpression": "#riskLevel.levelType.equals('ACCOUNT')",
                    "action":"WRITE"
                }
            ]
        },
        {
            "code": "account_settings_state",
            "name": "账号状态",
            "type": "MENU",
            "children": [
                {
                    "code": "account_state_list",
                    "name": "APP状态列表",
                    "type": "FUNCTION",
                    "url": "/api/assetLifeStateConfig/getByType",
                    "spelExpression": "#assetType.equals('ACCOUNT')",
                },
                {
                    "code": "account_state_save",
                    "name": "APP状态保存",
                    "type": "FUNCTION",
                    "url": "/api/assetLifeStateConfig/save",
                    "spelExpression": "#assetLifeStateConfig.assetType.equals('ACCOUNT')",
                    "action":"WRITE"
                }
            ]
        },
        {
            "code": "account_settings_department_config",
            "name": "组织架构配置",
            "type": "MENU",
            "children": [
                {
                    "code": "userDepart",
                    "name": "组织架构配置",
                    "type": "FUNCTION",
                    "url": "/userDepart/*",
                }
            ]
        }
    ],
    "delFlag": false
})
db.menuConfig.save({
    "code": "setting_data",
    "name": "数据配置",
    "type": "MENU",
    "children": [
        {
            "code": "data_settings_labels",
            "name": "数据标签",
            "type": "MENU",
            "children": [
                {
                    "code": "/api/dataLabel/detail.do",
                    "name": "数据标签详情",
                    "type": "FUNCTION",
                    "url": "/api/dataLabel/detail.do"
                },
                {
                    "code": "/api/dataLabel/save",
                    "name": "保存数据标签",
                    "type": "FUNCTION",
                    "url": "/api/dataLabel/save"
                },
                {
                    "code": "/api/dataLabel/enableDataLabel.do",
                    "name": "停启用数据标签",
                    "type": "FUNCTION",
                    "url": "/api/dataLabel/enableDataLabel.do"
                },
                {
                    "code": "/api/dataLabel/batchOpts",
                    "name": "批量操作",
                    "type": "FUNCTION",
                    "url": "/api/dataLabel/batchOpts"
                },
                {
                    "code": "/api/dataLabel/list",
                    "name": "数据标签列表",
                    "type": "FUNCTION",
                    "url": "/api/dataLabel/list"
                },
                {
                    "code": "/api/dataLabel/statusSwitch",
                    "name": "状态开关",
                    "type": "FUNCTION",
                    "url": "/api/dataLabel/statusSwitch"
                },
                {
                    "code": "/api/dataLabel/addFilterRule",
                    "name": "添加过滤规则",
                    "type": "FUNCTION",
                    "url": "/api/dataLabel/addFilterRule"
                },
                {
                    "code": "/api/dataLabel/exportDataLabel",
                    "name": "导出数据标签",
                    "type": "FUNCTION",
                    "url": "/api/dataLabel/exportDataLabel"
                },
                {
                    "code": "/api/dataLabel/importDataLabel",
                    "name": "导入数据标签",
                    "type": "FUNCTION",
                    "url": "/api/dataLabel/importDataLabel"
                },
                {
                    "code": "/api/dataLabel/importExcel",
                    "name": "导入数据标签模板",
                    "type": "FUNCTION",
                    "url": "/api/dataLabel/importExcel"
                },
                {
                    "code": "/api/dataLabel/exportExcel",
                    "name": "导出数据标签模板",
                    "type": "FUNCTION",
                    "url": "/api/dataLabel/exportExcel"
                },
                {
                    "code": "/api/dataLabel/downloadErrorExcel",
                    "name": "下载导出失败的数据标签模板",
                    "type": "FUNCTION",
                    "url": "/api/dataLabel/downloadErrorExcel"
                }
            ]
        },
        {
            "code": "data_settings_sensi_level",
            "name": "数据敏感等级",
            "type": "MENU",
            "children": [
                {
                    "code": "/api/dataLabel/updateSensiLevel",
                    "name": "更新敏感等级",
                    "type": "FUNCTION",
                    "url": "/api/dataLabel/updateSensiLevel"
                },
                {
                    "code": "/api/dataLabel/getSensiLevel",
                    "name": "获取敏感等级",
                    "type": "FUNCTION",
                    "url": "/api/dataLabel/getSensiLevel"
                }
            ]
        },
        {
            "code": "data_settings_risk_level",
            "name": "数据风险等级",
            "type": "MENU",
            "children": [
                {
                    "code": "/api/riskLevel/saveRiskLevel",
                    "name": "保存风险等级配置",
                    "type": "FUNCTION",
                    "url": "/api/riskLevel/saveRiskLevel",
                    "spelExpression": "#riskLevel.levelType.equals('DATA')"
                }
            ]
        },
        {
            "code": "data_settings_template",
            "name": "数据标签模板",
            "type": "MENU",
            "children": [
                {
                    "code": "/api/dataLabelClassify/get",
                    "name": "获取数据标签模板",
                    "type": "FUNCTION",
                    "url": "/api/dataLabelClassify/get"
                },
                {
                    "code": "/api/dataLabelClassify/enable",
                    "name": "启用数据标签模板",
                    "type": "FUNCTION",
                    "url": "/api/dataLabelClassify/enable"
                },
                {
                    "code": "/api/dataLabelClassify/remove",
                    "name": "删除分类",
                    "type": "FUNCTION",
                    "url": "/api/dataLabelClassify/remove"
                },
                {
                    "code": "/api/dataLabelClassify/add",
                    "name": "新增分类",
                    "type": "FUNCTION",
                    "url": "/api/dataLabelClassify/add"
                }
            ]
        },
        {
            "code": "subscribe_configs",
            "name": "数据同步",
            "type": "MENU",
            "children": [
                {
                    "code": "resourceChangedEventNacos",
                    "name": "数据同步相关",
                    "type": "FUNCTION",
                    "url": "/api/resourceChangedEventNacos/*"
                }
            ]
        },
        {
            "code": "terminal_config",
            "name": "终端配置",
            "type": "MENU",
            "children": [
                {
                    "code": "uaType",
                    "name": "终端配置相关",
                    "type": "FUNCTION",
                    "url": "/api/uaType/*"
                }
            ]
        },
        {
            "code": "data_task",
            "name": "数据清理",
            "type": "MENU",
            "children": [
                {
                    "code": "xxlJob",
                    "name": "数据清理相关",
                    "type": "FUNCTION",
                    "url": "/api/xxlJob/*"
                }
            ]
        },
        {
            "code": "data_export",
            "name": "数据导出管理",
            "type": "MENU",
            "children": [
                {
                    "code": "commonExport",
                    "name": "数据导出相关",
                    "type": "FUNCTION",
                    "url": "/api/export/*"
                }
            ]
        },
        {
            "code": "file_settings_sensi_level",
            "name": "文件敏感等级",
            "type": "MENU",
            "children": [
                {
                    "code": "fileSensitive",
                    "name": "文件敏感等级相关",
                    "type": "FUNCTION",
                    "url": "/api/fileSensitive/*"
                }
            ]
        },
        {
            "code": "data_kafka",
            "name": "kafka数据接入",
            "type": "MENU",
            "children": []
        }

    ],
    "delFlag": false
})
db.menuConfig.save({
    "code": "setting_weakness",
    "name": "弱点规则",
    "type": "MENU",
    "children": [
        {
            "code": "weaknessRule",
            "name": "弱点规则",
            "type": "FUNCTION",
            "url":"/api/weaknessRule/*"
        }
    ],
    "delFlag": false
})
db.menuConfig.save({
    "code": "setting_risk",
    "name": "风险配置",
    "type": "MENU",
    "children": [
        {
            "code": "risk_settings_rule",
            "name": "异常规则",
            "type": "MENU",
            "children": []
        },
        {
            "code": "risk_settings_quota",
            "name": "异常指标",
            "type": "MENU",
            "children": []
        },
        {
            "code": "risk_settings_variable",
            "name": "变量管理",
            "type": "MENU",
            "children": []
        },
        {
            "code": "risk_control",
            "name": "风险管理",
            "type": "MENU",
            "children": []
        }
    ],
    "delFlag": false
})
db.menuConfig.save({
    "code": "setting_traffic",
    "name": "流量管理",
    "type": "MENU",
    "children": [
        {
            "code": "init_wizard",
            "name": "配置向导",
            "type": "MENU",
            "children": [
                {
                    "code": "gatewayConfig",
                    "name": "配置向导相关",
                    "type": "FUNCTION",
                    "url":"/api/gatewayConfig/*"
                }
            ]
        },
        {
            "code": "gateway_management",
            "name": "网关管理",
            "type": "MENU",
            "children": [
                {
                    "code": "gatewaySSL",
                    "name": "网关管理",
                    "type": "FUNCTION",
                    "url":"/api/gatewaySSL/*"
                }
            ]
        },
        {
            "code": "setting_plugin",
            "name": "插件管理",
            "type": "MENU",
            "children": [
                {
                    "code": "pluginManage",
                    "name": "插件管理",
                    "type": "FUNCTION",
                    "url":"/api/pluginManage/*"
                }
            ]
        },
        {
            "code": "operation_tools",
            "name": "运维工具",
            "type": "MENU",
            "children": [
                {
                    "code": "operationTools",
                    "name": "运维工具",
                    "type": "FUNCTION",
                    "url":"/api/tool/*"
                }
            ]
        },
        {
            "code": "document_management",
            "name": "文档管理",
            "type": "MENU",
            "children": [
                {
                    "code": "/openapi/saveKey.do",
                    "name": "新增授权",
                    "type": "FUNCTION",
                    "action":"WRITE",
                    "url":"/openapi/saveKey.do"
                },
                {
                    "code": "/openapi/delKey.do",
                    "name": "删除授权",
                    "type": "FUNCTION",
                    "action":"WRITE",
                    "url":"/openapi/delKey.do"
                },
                {
                    "code": "/openapi/listKey.do",
                    "name": "授权列表",
                    "type": "FUNCTION",
                    "url":"/openapi/listKey.do"
                }
            ]
        },
        {
            "code": "data_saved",
            "name": "留存策略",
            "type": "MENU",
            "children": [
                {
                    "code": "discoverSampleCount",
                    "name": "样例最少留存数量",
                    "type": "FUNCTION",
                    "action":"WRITE",
                    "url":"/api/nacosSingelKey/updateNacosSingleKeys.do",
                    "spelExpression": "#nacosSingleKeys[0].key.equals('discoverSampleCount')"
                },
                {
                    "code": "metabaseSnapshotCount",
                    "name": "样例留存周期",
                    "type": "FUNCTION",
                    "action":"WRITE",
                    "url":"/api/nacosSingelKey/updateNacosSingleKeys.do",
                    "spelExpression": "#nacosSingleKeys[0].key.equals('metabaseSnapshotCount')"
                },
                {
                    "code": "es_defined_event_date",
                    "name": "审计日志留存周期",
                    "type": "FUNCTION",
                    "action":"WRITE",
                    "url":"/api/nacosSingelKey/updateNacosSingleKeys.do",
                    "spelExpression": "#nacosSingleKeys[0].key.equals('es_defined_event_date')"
                },
                {
                    "code": "noSensiValueEventForwardEnable",
                    "name": "审计日志留存范围",
                    "type": "FUNCTION",
                    "action":"WRITE",
                    "url":"/api/nacosSingelKey/updateNacosSingleKeys.do",
                    "spelExpression": "#nacosSingleKeys[0].key.equals('noSensiValueEventForwardEnable')"
                }
            ]
        },
        {
            "code": "task_management",
            "name": "组件任务管理",
            "type": "MENU",
            "children": [
                {
                    "code": "schedule",
                    "name": "组件任务配置",
                    "type": "FUNCTION",
                    "url":"/api/schedule/*"
                }
            ]
        },
        {
            "code": "settings_multi_nodes",
            "name": "接入配置",
            "type": "MENU",
            "children": [
                {
                    "code": "node",
                    "name": "接入配置",
                    "type": "FUNCTION",
                    "url":"/api/node/*"
                }
            ]
        }
    ],
    "delFlag": false
})
db.menuConfig.save({
    "code": "setting_system_management",
    "name": "系统管理",
    "type": "MENU",
    "children": [
        {
            "code": "notice_record",
            "name": "通知管理",
            "type": "MENU",
            "children": [
                {
                    "code": "notify",
                    "name": "通知管理",
                    "type": "FUNCTION",
                    "url":"/api/notify/*"
                }
            ]
        },
        {
            "code": "system_authorization",
            "name": "系统授权",
            "type": "MENU",
            "children": [
                {
                    "code": "license",
                    "name": "系统授权",
                    "type": "FUNCTION",
                    "url":"/api/license/*"
                }
            ]
        },
        {
            "code": "settings_update",
            "name": "系统升级",
            "type": "MENU",
            "children": [
                {
                    "code": "sysUpdate",
                    "name": "系统升级",
                    "type": "FUNCTION",
                    "url":"/api/sysUpdate/*"
                }
            ]
        },
        {
            "code": "system_monitor",
            "name": "系统监控",
            "type": "MENU",
            "children": []
        },
        {
            "code": "system_config",
            "name": "系统配置",
            "type": "MENU",
            "children": [
                {
                    "code": "sysClean",
                    "name": "恢复出厂配置",
                    "type": "FUNCTION",
                    "url":"/api/sysClean/*"
                },
                {
                    "code": "basicConfig",
                    "name": "基础配置",
                    "type": "FUNCTION",
                    "url":"/api/basicConfig/*"
                },
                {
                    "code": "bigScreenConfig",
                    "name": "大屏配置",
                    "type": "FUNCTION",
                    "url":"/api/bigScreenConfig/*"
                }
            ]
        }
    ],
    "delFlag": false
})


