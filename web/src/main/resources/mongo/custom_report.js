var apiLifeCycleReportCount = db.scanTask.count({'code':'apiLifeCycleReport','reportType':'AMIS'});
if (apiLifeCycleReportCount == 0) {
    db.scanTask.save(
        {
            "code" : "apiLifeCycleReport",
            "name" : "API生命周期报告",
            "policy" : 0,
            "reportType" : "AMIS",
            "amisReportJson" : "{\"type\":\"page\",\"id\":\"u:82b84556c65f\",\"asideResizor\":false,\"pullRefresh\":{\"disabled\":true},\"body\":[{\"type\":\"container\",\"body\":[{\"type\":\"tpl\",\"tpl\":\"API生命周期报告\",\"inline\":true,\"wrapperComponent\":\"\",\"id\":\"u:7301240862fd\",\"themeCss\":{\"baseControlClassName\":{\"font:default\":{\"fontWeight\":\"500\",\"fontSize\":\"20px\"},\"padding-and-margin:default\":{}}}},{\"type\":\"tpl\",\"tpl\":\"${startDate} - ${endDate}\",\"inline\":true,\"wrapperComponent\":\"\",\"id\":\"u:b8ca5f3cb327\",\"themeCss\":{\"baseControlClassName\":{\"padding-and-margin:default\":{\"marginTop\":\"0.7142857142857143rem\",\"marginRight\":\"0.7142857142857143rem\",\"marginBottom\":\"0.7142857142857143rem\",\"marginLeft\":\"0.7142857142857143rem\"}}},\"editorSetting\":{\"mock\":{}}},{\"type\":\"button\",\"label\":\"导出报告\",\"onEvent\":{\"click\":{\"actions\":[{\"ignoreError\":false,\"actionType\":\"url\",\"args\":{\"url\":\"/audit-apiv2/report/detail/exportApiLifeItemByTask?taskId=${id}\"}}]}},\"id\":\"u:ef0e447819d3\",\"level\":\"primary\",\"wrapperCustomStyle\":{\"root\":{\"position\":\"absolute\",\"top\":\"0\",\"right\":\"0\"}}}],\"style\":{\"position\":\"relative\",\"display\":\"flex\",\"inset\":\"auto\",\"flexWrap\":\"nowrap\",\"flexDirection\":\"column\",\"alignItems\":\"center\"},\"size\":\"none\",\"wrapperBody\":false,\"id\":\"u:933c1c444f1c\",\"isFixedHeight\":false,\"isFixedWidth\":false},{\"type\":\"container\",\"body\":[{\"type\":\"service\",\"body\":[{\"type\":\"tpl\",\"tpl\":\"新增API ${totalCount} 个\",\"inline\":true,\"wrapperComponent\":\"\",\"id\":\"u:e1c34cb83bbd\",\"editorSetting\":{\"mock\":{}}}],\"id\":\"u:1ba82636f191\",\"dsType\":\"api\",\"api\":{\"url\":\"/audit-apiv2/report/detail/getApiLifeCycleItem\",\"method\":\"post\",\"requestAdaptor\":\"\",\"adaptor\":\"\",\"messages\":{},\"dataType\":\"form\",\"data\":{\"id\":\"${id}\",\"apiLifeType\":\"ADD_API\",\"page\":1,\"limit\":1}}},{\"id\":\"u:7da81642cf7b\",\"type\":\"crud2\",\"mode\":\"table2\",\"dsType\":\"api\",\"syncLocation\":true,\"primaryField\":\"id\",\"loadType\":\"pagination\",\"headerToolbar\":[],\"footerToolbar\":[{\"type\":\"flex\",\"direction\":\"row\",\"justify\":\"flex-start\",\"alignItems\":\"stretch\",\"style\":{\"position\":\"static\"},\"items\":[{\"type\":\"container\",\"align\":\"right\",\"body\":[{\"type\":\"pagination\",\"behavior\":\"Pagination\",\"layout\":[\"total\",\"perPage\",\"pager\"],\"perPage\":10,\"perPageAvailable\":[10,20,50,100],\"align\":\"right\",\"id\":\"u:e5ef7badbaad\",\"size\":\"\"}],\"wrapperBody\":false,\"style\":{\"flexGrow\":1,\"flex\":\"1 1 auto\",\"position\":\"static\",\"display\":\"flex\",\"flexBasis\":\"auto\",\"flexDirection\":\"row\",\"flexWrap\":\"nowrap\",\"alignItems\":\"stretch\",\"justifyContent\":\"flex-end\"},\"id\":\"u:ceb40830c520\"}],\"id\":\"u:b312794323a8\"}],\"columns\":[{\"type\":\"tpl\",\"title\":\"API\",\"name\":\"api\",\"id\":\"u:79eb42e42be3\",\"placeholder\":\"-\",\"tpl\":\"<p>${level === '高敏感' ? '<span style=\\\"color: rgb(232, 71, 56);\\\">高敏感</span>' :level === '中敏感' ? '<span style=\\\"color: rgb(212, 136, 6);\\\">中敏感</span>' :level === '低敏感' ? '<span style=\\\"color: rgb(209, 182, 59);\\\">低敏感</span>' :level === '非敏感' ? '<span style=\\\"color: rgb(94, 176, 35);\\\">非敏感</span>':'<span style=\\\"color: rgb(144, 147, 153);\\\">其他</span>'} ${apiUrl}</p>\",\"editorSetting\":{\"mock\":{\"tpl\":\"apiUrl\"}}},{\"type\":\"tpl\",\"title\":\"API格式\",\"name\":\"apiFormat\",\"id\":\"u:4ef5419025d5\",\"tpl\":\"\",\"placeholder\":\"-\",\"wrapperComponent\":\"\"},{\"type\":\"tpl\",\"title\":\"API标签\",\"name\":\"apiLabel\",\"id\":\"u:d0412d81cf9a\",\"placeholder\":\"-\",\"tpl\":\"\",\"wrapperComponent\":\"\"},{\"type\":\"tpl\",\"title\":\"访问域\",\"name\":\"visitDomain\",\"id\":\"u:1be0b28ddbd1\",\"tpl\":\"\",\"placeholder\":\"-\",\"wrapperComponent\":\"\"},{\"type\":\"tpl\",\"title\":\"响应数据标签\",\"name\":\"rspDataLabel\",\"id\":\"u:ef90d6358b60\",\"tpl\":\"\",\"placeholder\":\"-\",\"wrapperComponent\":\"\"},{\"type\":\"tpl\",\"title\":\"首次发现时间\",\"name\":\"discoverTime\",\"id\":\"u:74b168defb4d\",\"placeholder\":\"-\",\"tpl\":\"\",\"wrapperComponent\":\"\"},{\"type\":\"operation\",\"title\":\"操作\",\"buttons\":[{\"type\":\"api-sample\",\"uri\":\"${uri}\",\"id\":\"u:15c7e5adfe93\",\"url\":\"${apiUrl}\"}],\"id\":\"u:1c8e58d9697c\"}],\"editorSetting\":{\"mock\":{\"enable\":true,\"maxDisplayRows\":5}},\"placeholder\":\"暂无数据\",\"selectable\":false,\"showHeader\":true,\"wrapperCustomStyle\":{},\"rowClassNameExpr\":\"\",\"title\":null,\"api\":{\"url\":\"/audit-apiv2/report/detail/getApiLifeCycleItem\",\"method\":\"post\",\"requestAdaptor\":\"\",\"adaptor\":\"const items = response.data.rows.map(item => {\\n  const apiFormats = item.apiFormats || [];\\n  const classificationValue = item.classificationValue || [];\\n  const featureLabelsValue = item.featureLabelsValue || [];\\n  const visitDomains = item.visitDomains || [];\\n  const rspDataLabels = item.rspDataLabels || [];\\n\\n  return {\\n    uri: item.uri,\\n    level: item.level,\\n    apiUrl: item.apiUrl,\\n    apiFormat: apiFormats.join(','),\\n    apiLabel: classificationValue.concat(featureLabelsValue).join(','),\\n    visitDomain: visitDomains.join(','),\\n    rspDataLabel: rspDataLabels.join(','),\\n    discoverTime: moment(item.discoverTime).format('YYYY-MM-DD HH:mm:ss')\\n  }\\n})\\n\\nreturn {\\n  status: 0,\\n  msg: \\\"\\\",\\n  data: {\\n    items: items,\\n    total: response.data.totalCount\\n  }\\n}\",\"messages\":{},\"dataType\":\"form\",\"data\":{\"id\":\"${id}\",\"apiLifeType\":\"ADD_API\",\"page\":\"${page}\",\"limit\":\"${perPage}\"}}}],\"style\":{\"position\":\"relative\",\"display\":\"flex\",\"inset\":\"auto\",\"flexWrap\":\"nowrap\",\"flexDirection\":\"column\",\"alignItems\":\"stretch\"},\"size\":\"none\",\"wrapperBody\":false,\"id\":\"u:be840016b3bf\",\"isFixedHeight\":false,\"isFixedWidth\":false},{\"type\":\"container\",\"body\":[{\"type\":\"service\",\"body\":[{\"type\":\"tpl\",\"tpl\":\"活跃API ${totalCount} 个\",\"inline\":true,\"wrapperComponent\":\"\",\"id\":\"u:c8eae637269d\",\"editorSetting\":{\"mock\":{}}}],\"id\":\"u:c84f6a839c8c\",\"dsType\":\"api\",\"api\":{\"url\":\"/audit-apiv2/report/detail/getApiLifeCycleItem\",\"method\":\"post\",\"requestAdaptor\":\"\",\"adaptor\":\"\",\"messages\":{},\"dataType\":\"form\",\"data\":{\"id\":\"${id}\",\"apiLifeType\":\"ACTIVE_API\",\"page\":1,\"limit\":1}}},{\"id\":\"u:7396f03f6c9d\",\"type\":\"crud2\",\"mode\":\"table2\",\"dsType\":\"api\",\"syncLocation\":true,\"primaryField\":\"id\",\"loadType\":\"pagination\",\"headerToolbar\":[],\"footerToolbar\":[{\"type\":\"flex\",\"direction\":\"row\",\"justify\":\"flex-start\",\"alignItems\":\"stretch\",\"style\":{\"position\":\"static\"},\"items\":[{\"type\":\"container\",\"align\":\"right\",\"body\":[{\"type\":\"pagination\",\"behavior\":\"Pagination\",\"layout\":[\"total\",\"perPage\",\"pager\"],\"perPage\":10,\"perPageAvailable\":[10,20,50,100],\"align\":\"right\",\"id\":\"u:7f40b98f44f4\",\"size\":\"\"}],\"wrapperBody\":false,\"style\":{\"flexGrow\":1,\"flex\":\"1 1 auto\",\"position\":\"static\",\"display\":\"flex\",\"flexBasis\":\"auto\",\"flexDirection\":\"row\",\"flexWrap\":\"nowrap\",\"alignItems\":\"stretch\",\"justifyContent\":\"flex-end\"},\"id\":\"u:88ac65229112\"}],\"id\":\"u:dda2892b4cec\"}],\"columns\":[{\"type\":\"tpl\",\"title\":\"API\",\"name\":\"api\",\"id\":\"u:a90a7939cdeb\",\"placeholder\":\"-\",\"tpl\":\"<p>${level === '高敏感' ? '<span style=\\\"color: rgb(232, 71, 56);\\\">高敏感</span>' :level === '中敏感' ? '<span style=\\\"color: rgb(212, 136, 6);\\\">中敏感</span>' :level === '低敏感' ? '<span style=\\\"color: rgb(209, 182, 59);\\\">低敏感</span>' :level === '非敏感' ? '<span style=\\\"color: rgb(94, 176, 35);\\\">非敏感</span>':'<span style=\\\"color: rgb(144, 147, 153);\\\">其他</span>'} ${apiUrl}</p>\",\"editorSetting\":{\"mock\":{\"tpl\":\"apiUrl\"}}},{\"type\":\"tpl\",\"title\":\"API格式\",\"name\":\"apiFormat\",\"id\":\"u:1bb1b27cdbb7\",\"tpl\":\"\",\"placeholder\":\"-\",\"wrapperComponent\":\"\"},{\"type\":\"tpl\",\"title\":\"API标签\",\"name\":\"apiLabel\",\"id\":\"u:da6bcf2c4586\",\"placeholder\":\"-\",\"tpl\":\"\",\"wrapperComponent\":\"\"},{\"type\":\"tpl\",\"title\":\"访问域\",\"name\":\"visitDomain\",\"id\":\"u:136318fff0d8\",\"tpl\":\"\",\"placeholder\":\"-\",\"wrapperComponent\":\"\"},{\"type\":\"tpl\",\"title\":\"响应数据标签\",\"name\":\"rspDataLabel\",\"id\":\"u:66b716b74ddb\",\"tpl\":\"\",\"placeholder\":\"-\",\"wrapperComponent\":\"\"},{\"type\":\"tpl\",\"title\":\"最近活跃时间\",\"name\":\"activeTime\",\"id\":\"u:81aeadae5ddd\",\"placeholder\":\"-\",\"tpl\":\"\",\"wrapperComponent\":\"\"},{\"type\":\"operation\",\"title\":\"操作\",\"buttons\":[{\"type\":\"api-sample\",\"uri\":\"${uri}\",\"id\":\"u:75c0ff807e8b\",\"url\":\"${apiUrl}\"}],\"id\":\"u:8d55f37e8497\"}],\"editorSetting\":{\"mock\":{\"enable\":true,\"maxDisplayRows\":5}},\"placeholder\":\"暂无数据\",\"selectable\":false,\"showHeader\":true,\"wrapperCustomStyle\":{},\"rowClassNameExpr\":\"\",\"title\":null,\"api\":{\"url\":\"/audit-apiv2/report/detail/getApiLifeCycleItem\",\"method\":\"post\",\"requestAdaptor\":\"\",\"adaptor\":\"const items = response.data.rows.map(item => {\\n  const apiFormats = item.apiFormats || [];\\n  const classificationValue = item.classificationValue || [];\\n  const featureLabelsValue = item.featureLabelsValue || [];\\n  const visitDomains = item.visitDomains || [];\\n  const rspDataLabels = item.rspDataLabels || [];\\n\\n  return {\\n    uri: item.uri,\\n    level: item.level,\\n    apiUrl: item.apiUrl,\\n    apiFormat: apiFormats.join(','),\\n    apiLabel: classificationValue.concat(featureLabelsValue).join(','),\\n    visitDomain: visitDomains.join(','),\\n    rspDataLabel: rspDataLabels.join(','),\\n    activeTime: moment(item.activeTime).format('YYYY-MM-DD HH:mm:ss')\\n  }\\n})\\n\\nreturn {\\n  status: 0,\\n  msg: \\\"\\\",\\n  data: {\\n    items: items,\\n    total: response.data.totalCount\\n  }\\n}\",\"messages\":{},\"dataType\":\"form\",\"data\":{\"id\":\"${id}\",\"apiLifeType\":\"ACTIVE_API\",\"page\":\"${page}\",\"limit\":\"${perPage}\"}}}],\"style\":{\"position\":\"relative\",\"display\":\"flex\",\"inset\":\"auto\",\"flexWrap\":\"nowrap\",\"flexDirection\":\"column\",\"alignItems\":\"stretch\"},\"size\":\"none\",\"wrapperBody\":false,\"id\":\"u:ffc713f75589\",\"isFixedHeight\":false,\"isFixedWidth\":false,\"themeCss\":{\"baseControlClassName\":{\"padding-and-margin:default\":{\"marginTop\":\"20px\"}}}},{\"type\":\"container\",\"body\":[{\"type\":\"service\",\"body\":[{\"type\":\"tpl\",\"tpl\":\"失活API ${totalCount} 个\",\"inline\":true,\"wrapperComponent\":\"\",\"id\":\"u:79a8e946fb6d\",\"editorSetting\":{\"mock\":{}}}],\"id\":\"u:4635cf4fb7d9\",\"dsType\":\"api\",\"api\":{\"url\":\"/audit-apiv2/report/detail/getApiLifeCycleItem\",\"method\":\"post\",\"requestAdaptor\":\"\",\"adaptor\":\"\",\"messages\":{},\"dataType\":\"form\",\"data\":{\"id\":\"${id}\",\"apiLifeType\":\"INACTIVATION_API\",\"page\":1,\"limit\":1}}},{\"id\":\"u:49bde5229369\",\"type\":\"crud2\",\"mode\":\"table2\",\"dsType\":\"api\",\"syncLocation\":true,\"primaryField\":\"id\",\"loadType\":\"pagination\",\"headerToolbar\":[],\"footerToolbar\":[{\"type\":\"flex\",\"direction\":\"row\",\"justify\":\"flex-start\",\"alignItems\":\"stretch\",\"style\":{\"position\":\"static\"},\"items\":[{\"type\":\"container\",\"align\":\"right\",\"body\":[{\"type\":\"pagination\",\"behavior\":\"Pagination\",\"layout\":[\"total\",\"perPage\",\"pager\"],\"perPage\":10,\"perPageAvailable\":[10,20,50,100],\"align\":\"right\",\"id\":\"u:fff8ad559aa0\",\"size\":\"\"}],\"wrapperBody\":false,\"style\":{\"flexGrow\":1,\"flex\":\"1 1 auto\",\"position\":\"static\",\"display\":\"flex\",\"flexBasis\":\"auto\",\"flexDirection\":\"row\",\"flexWrap\":\"nowrap\",\"alignItems\":\"stretch\",\"justifyContent\":\"flex-end\"},\"id\":\"u:ac79abd4f87c\"}],\"id\":\"u:e254aee89566\"}],\"columns\":[{\"type\":\"tpl\",\"title\":\"API\",\"name\":\"api\",\"id\":\"u:f2fa599b6539\",\"placeholder\":\"-\",\"tpl\":\"<p>${level === '高敏感' ? '<span style=\\\"color: rgb(232, 71, 56);\\\">高敏感</span>' :level === '中敏感' ? '<span style=\\\"color: rgb(212, 136, 6);\\\">中敏感</span>' :level === '低敏感' ? '<span style=\\\"color: rgb(209, 182, 59);\\\">低敏感</span>' :level === '非敏感' ? '<span style=\\\"color: rgb(94, 176, 35);\\\">非敏感</span>':'<span style=\\\"color: rgb(144, 147, 153);\\\">其他</span>'} ${apiUrl}</p>\",\"editorSetting\":{\"mock\":{\"tpl\":\"apiUrl\"}}},{\"type\":\"tpl\",\"title\":\"API格式\",\"name\":\"apiFormat\",\"id\":\"u:738f0007ea85\",\"tpl\":\"\",\"placeholder\":\"-\",\"wrapperComponent\":\"\"},{\"type\":\"tpl\",\"title\":\"API标签\",\"name\":\"apiLabel\",\"id\":\"u:615e66e37478\",\"placeholder\":\"-\",\"tpl\":\"\",\"wrapperComponent\":\"\"},{\"type\":\"tpl\",\"title\":\"访问域\",\"name\":\"visitDomain\",\"id\":\"u:bc2edfdda1b7\",\"tpl\":\"\",\"placeholder\":\"-\",\"wrapperComponent\":\"\"},{\"type\":\"tpl\",\"title\":\"响应数据标签\",\"name\":\"rspDataLabel\",\"id\":\"u:75fecb6b06d6\",\"tpl\":\"\",\"placeholder\":\"-\",\"wrapperComponent\":\"\"},{\"type\":\"tpl\",\"title\":\"最近活跃时间\",\"name\":\"activeTime\",\"id\":\"u:b4ef186ad66a\",\"placeholder\":\"-\",\"tpl\":\"\",\"wrapperComponent\":\"\"},{\"type\":\"operation\",\"title\":\"操作\",\"buttons\":[{\"type\":\"api-sample\",\"uri\":\"${uri}\",\"id\":\"u:38318560c08e\",\"url\":\"${apiUrl}\"}],\"id\":\"u:d1df779575a4\"}],\"editorSetting\":{\"mock\":{\"enable\":true,\"maxDisplayRows\":5}},\"placeholder\":\"暂无数据\",\"selectable\":false,\"showHeader\":true,\"wrapperCustomStyle\":{},\"rowClassNameExpr\":\"\",\"title\":null,\"api\":{\"url\":\"/audit-apiv2/report/detail/getApiLifeCycleItem\",\"method\":\"post\",\"requestAdaptor\":\"\",\"adaptor\":\"const items = response.data.rows.map(item => {\\n  const apiFormats = item.apiFormats || [];\\n  const classificationValue = item.classificationValue || [];\\n  const featureLabelsValue = item.featureLabelsValue || [];\\n  const visitDomains = item.visitDomains || [];\\n  const rspDataLabels = item.rspDataLabels || [];\\n\\n  return {\\n    uri: item.uri,\\n    level: item.level,\\n    apiUrl: item.apiUrl,\\n    apiFormat: apiFormats.join(','),\\n    apiLabel: classificationValue.concat(featureLabelsValue).join(','),\\n    visitDomain: visitDomains.join(','),\\n    rspDataLabel: rspDataLabels.join(','),\\n    activeTime: moment(item.activeTime).format('YYYY-MM-DD HH:mm:ss')\\n  }\\n})\\n\\nreturn {\\n  status: 0,\\n  msg: \\\"\\\",\\n  data: {\\n    items: items,\\n    total: response.data.totalCount\\n  }\\n}\",\"messages\":{},\"dataType\":\"form\",\"data\":{\"id\":\"${id}\",\"apiLifeType\":\"INACTIVATION_API\",\"page\":\"${page}\",\"limit\":\"${perPage}\"}}}],\"style\":{\"position\":\"relative\",\"display\":\"flex\",\"inset\":\"auto\",\"flexWrap\":\"nowrap\",\"flexDirection\":\"column\",\"alignItems\":\"stretch\"},\"size\":\"none\",\"wrapperBody\":false,\"id\":\"u:e231fd27d70f\",\"isFixedHeight\":false,\"isFixedWidth\":false,\"themeCss\":{\"baseControlClassName\":{\"padding-and-margin:default\":{\"marginTop\":\"20px\"}}}},{\"type\":\"container\",\"body\":[{\"type\":\"service\",\"body\":[{\"type\":\"tpl\",\"tpl\":\"复活API ${totalCount} 个\",\"inline\":true,\"wrapperComponent\":\"\",\"id\":\"u:36dee3f8db15\",\"editorSetting\":{\"mock\":{}}}],\"id\":\"u:5b593e2e28cb\",\"dsType\":\"api\",\"api\":{\"url\":\"/audit-apiv2/report/detail/getApiLifeCycleItem\",\"method\":\"post\",\"requestAdaptor\":\"\",\"adaptor\":\"\",\"messages\":{},\"dataType\":\"form\",\"data\":{\"id\":\"${id}\",\"apiLifeType\":\"RESURRECTION_API\",\"page\":1,\"limit\":1}}},{\"id\":\"u:9e9fa4fc0a51\",\"type\":\"crud2\",\"mode\":\"table2\",\"dsType\":\"api\",\"syncLocation\":true,\"primaryField\":\"id\",\"loadType\":\"pagination\",\"headerToolbar\":[],\"footerToolbar\":[{\"type\":\"flex\",\"direction\":\"row\",\"justify\":\"flex-start\",\"alignItems\":\"stretch\",\"style\":{\"position\":\"static\"},\"items\":[{\"type\":\"container\",\"align\":\"right\",\"body\":[{\"type\":\"pagination\",\"behavior\":\"Pagination\",\"layout\":[\"total\",\"perPage\",\"pager\"],\"perPage\":10,\"perPageAvailable\":[10,20,50,100],\"align\":\"right\",\"id\":\"u:f0125a789b40\",\"size\":\"\"}],\"wrapperBody\":false,\"style\":{\"flexGrow\":1,\"flex\":\"1 1 auto\",\"position\":\"static\",\"display\":\"flex\",\"flexBasis\":\"auto\",\"flexDirection\":\"row\",\"flexWrap\":\"nowrap\",\"alignItems\":\"stretch\",\"justifyContent\":\"flex-end\"},\"id\":\"u:82b2a95027f0\"}],\"id\":\"u:f19c56f508aa\"}],\"columns\":[{\"type\":\"tpl\",\"title\":\"API\",\"name\":\"api\",\"id\":\"u:e0bef14408bd\",\"placeholder\":\"-\",\"tpl\":\"<p>${level === '高敏感' ? '<span style=\\\"color: rgb(232, 71, 56);\\\">高敏感</span>' :level === '中敏感' ? '<span style=\\\"color: rgb(212, 136, 6);\\\">中敏感</span>' :level === '低敏感' ? '<span style=\\\"color: rgb(209, 182, 59);\\\">低敏感</span>' :level === '非敏感' ? '<span style=\\\"color: rgb(94, 176, 35);\\\">非敏感</span>':'<span style=\\\"color: rgb(144, 147, 153);\\\">其他</span>'} ${apiUrl}</p>\",\"editorSetting\":{\"mock\":{\"tpl\":\"apiUrl\"}}},{\"type\":\"tpl\",\"title\":\"API格式\",\"name\":\"apiFormat\",\"id\":\"u:36f2b4530eee\",\"tpl\":\"\",\"placeholder\":\"-\",\"wrapperComponent\":\"\"},{\"type\":\"tpl\",\"title\":\"API标签\",\"name\":\"apiLabel\",\"id\":\"u:ebd7a24de9c1\",\"placeholder\":\"-\",\"tpl\":\"\",\"wrapperComponent\":\"\"},{\"type\":\"tpl\",\"title\":\"访问域\",\"name\":\"visitDomain\",\"id\":\"u:81b371df5bfb\",\"tpl\":\"\",\"placeholder\":\"-\",\"wrapperComponent\":\"\"},{\"type\":\"tpl\",\"title\":\"响应数据标签\",\"name\":\"rspDataLabel\",\"id\":\"u:092996788c86\",\"tpl\":\"\",\"placeholder\":\"-\",\"wrapperComponent\":\"\"},{\"type\":\"tpl\",\"title\":\"最近活跃时间\",\"name\":\"activeTime\",\"id\":\"u:501f1f0ca21e\",\"placeholder\":\"-\",\"tpl\":\"\",\"wrapperComponent\":\"\"},{\"type\":\"operation\",\"title\":\"操作\",\"buttons\":[{\"type\":\"api-sample\",\"uri\":\"${uri}\",\"id\":\"u:f0db33646050\",\"url\":\"${apiUrl}\"}],\"id\":\"u:f89f67d5a7be\"}],\"editorSetting\":{\"mock\":{\"enable\":true,\"maxDisplayRows\":5}},\"placeholder\":\"暂无数据\",\"selectable\":false,\"showHeader\":true,\"wrapperCustomStyle\":{},\"rowClassNameExpr\":\"\",\"title\":null,\"api\":{\"url\":\"/audit-apiv2/report/detail/getApiLifeCycleItem\",\"method\":\"post\",\"requestAdaptor\":\"\",\"adaptor\":\"const items = response.data.rows.map(item => {\\n  const apiFormats = item.apiFormats || [];\\n  const classificationValue = item.classificationValue || [];\\n  const featureLabelsValue = item.featureLabelsValue || [];\\n  const visitDomains = item.visitDomains || [];\\n  const rspDataLabels = item.rspDataLabels || [];\\n\\n  return {\\n    uri: item.uri,\\n    level: item.level,\\n    apiUrl: item.apiUrl,\\n    apiFormat: apiFormats.join(','),\\n    apiLabel: classificationValue.concat(featureLabelsValue).join(','),\\n    visitDomain: visitDomains.join(','),\\n    rspDataLabel: rspDataLabels.join(','),\\n    activeTime: moment(item.activeTime).format('YYYY-MM-DD HH:mm:ss')\\n  }\\n})\\n\\nreturn {\\n  status: 0,\\n  msg: \\\"\\\",\\n  data: {\\n    items: items,\\n    total: response.data.totalCount\\n  }\\n}\",\"messages\":{},\"dataType\":\"form\",\"data\":{\"id\":\"${id}\",\"apiLifeType\":\"RESURRECTION_API\",\"page\":\"${page}\",\"limit\":\"${perPage}\"}}}],\"style\":{\"position\":\"relative\",\"display\":\"flex\",\"inset\":\"auto\",\"flexWrap\":\"nowrap\",\"flexDirection\":\"column\",\"alignItems\":\"stretch\"},\"size\":\"none\",\"wrapperBody\":false,\"id\":\"u:086f46d85cb5\",\"isFixedHeight\":false,\"isFixedWidth\":false,\"themeCss\":{\"baseControlClassName\":{\"padding-and-margin:default\":{\"marginTop\":\"20px\"}}}}],\"toolbar\":[],\"data\":{\"startDate\":\"20241210\",\"endDate\":\"20241213\"},\"regions\":[\"body\"]}",
            "cron" : "",
            "params" : "{\"startTime\":\"2024-12-01\",\"endTime\":\"2024-12-26\"}",
            "status" : 1
        }
    )
}
var apiSafeOperationReportCount = db.scanTask.count({'code':'apiSafeOperationReport','reportType':'AMIS'});
if (apiSafeOperationReportCount == 0) {
    db.scanTask.save(
        {
            "code" : "apiSafeOperationReport",
            "name" : "API安全运营报告",
            "policy" : 0,
            "reportType" : "AMIS",
            "amisReportJson" :"{\"type\":\"page\",\"id\":\"u:82b84556c65f\",\"asideResizor\":false,\"pullRefresh\":{\"disabled\":true},\"body\":[{\"type\":\"container\",\"body\":[{\"type\":\"tpl\",\"tpl\":\"API安全运营报告\",\"inline\":true,\"wrapperComponent\":\"\",\"id\":\"u:7301240862fd\",\"themeCss\":{\"baseControlClassName\":{\"font:default\":{\"fontWeight\":\"500\",\"fontSize\":\"20px\"},\"padding-and-margin:default\":{}}},\"wrapperCustomStyle\":{\"color\":\"#333\"}},{\"type\":\"tpl\",\"tpl\":\"${startDate} - ${endDate}\",\"inline\":true,\"wrapperComponent\":\"\",\"id\":\"u:b8ca5f3cb327\",\"themeCss\":{\"baseControlClassName\":{\"padding-and-margin:default\":{\"marginTop\":\"0.7142857142857143rem\",\"marginRight\":\"0.7142857142857143rem\",\"marginBottom\":\"0.7142857142857143rem\",\"marginLeft\":\"0.7142857142857143rem\"}}},\"editorSetting\":{\"mock\":{}},\"visibleOn\":\"${dataSource !== 'ActiveScan'}\"},{\"type\":\"button\",\"label\":\"生成离线报告\",\"onEvent\":{\"click\":{\"actions\":[{\"ignoreError\":false,\"outputVar\":\"responseResult\",\"actionType\":\"ajax\",\"options\":{},\"api\":{\"url\":\"/audit-apiv2/report/detail/createApiSafeOperationReport\",\"method\":\"get\",\"requestAdaptor\":\"\",\"adaptor\":\"if(response.success) {\\n  alert('任务提交成功，稍后请刷新页面进行下载');\\n} else {\\n  alert(response.msg || '任务提价失败')\\n}\",\"messages\":{\"success\":\"\",\"failed\":\"\"},\"data\":{\"id\":\"${id}\"}}}]}},\"id\":\"u:ef0e447819d3\",\"level\":\"primary\",\"wrapperCustomStyle\":{\"root\":{\"position\":\"absolute\",\"top\":\"0\",\"right\":\"0\"}},\"visibleOn\":\"${!resultPath}\"},{\"type\":\"button\",\"label\":\"下载离线报告\",\"onEvent\":{\"click\":{\"actions\":[{\"ignoreError\":false,\"actionType\":\"url\",\"args\":{\"url\":\"/audit-apiv2/report/detail/exportApiSafeOperationReport?resultPath=${resultPath}\"}}]}},\"id\":\"u:c4cc3a3f2dd3\",\"level\":\"primary\",\"wrapperCustomStyle\":{\"root\":{\"position\":\"absolute\",\"top\":\"0\",\"right\":\"0\"}},\"visibleOn\":\"${resultPath}\"}],\"style\":{\"position\":\"relative\",\"display\":\"flex\",\"inset\":\"auto\",\"flexWrap\":\"nowrap\",\"flexDirection\":\"column\",\"alignItems\":\"center\"},\"size\":\"none\",\"wrapperBody\":false,\"id\":\"u:933c1c444f1c\",\"isFixedHeight\":false,\"isFixedWidth\":false},{\"type\":\"service\",\"body\":[{\"type\":\"tpl\",\"tpl\":\"基本信息\",\"inline\":true,\"wrapperComponent\":\"\",\"id\":\"u:ecf48e632252\",\"wrapperCustomStyle\":{\"font-size\":\"16px\",\"color\":\"#333\"}},{\"type\":\"container\",\"body\":[{\"type\":\"grid\",\"columns\":[{\"body\":[{\"type\":\"tpl\",\"tpl\":\"资产发现：\",\"inline\":true,\"wrapperComponent\":\"\",\"id\":\"u:163ee6d99c77\"}],\"id\":\"u:8d17013bb1b1\",\"md\":\"auto\"},{\"body\":[{\"type\":\"tpl\",\"tpl\":\"共发现web应用 ${basicInfo.totalAppNum || 0} 个，高敏感应用${basicInfo.highLevelAppNum || 0}个，互联网可访问应用 ${basicInfo.outerAppNum || 0} 个，自有应用 ${basicInfo.internalAppNum || 0} 个，弱点应用 ${basicInfo.weaknessAppNum || 0} 个${productLevel === '工具箱版' ? '' : `，风险应用 ${basicInfo.riskAppNum || 0} 个`}；\",\"inline\":false,\"wrapperComponent\":\"\",\"id\":\"u:f9fdb1b8757f\"},{\"type\":\"tpl\",\"tpl\":\"共发现API资产 ${basicInfo.totalApiNum || 0} 个，高敏感API ${basicInfo.highLevelApiNum || 0} 个，互联网暴露API ${basicInfo.outerApiNum || 0} 个，弱点API ${basicInfo.weaknessApiNum || 0} 个${productLevel === '工具箱版' ? ''  : `，风险API ${basicInfo.riskApiNum || 0} 个`}；\",\"inline\":true,\"wrapperComponent\":\"\",\"id\":\"u:90e6233d69c4\"}],\"id\":\"u:a8f228472077\"}],\"id\":\"u:a2876396fbef\",\"themeCss\":{\"baseControlClassName\":{\"padding-and-margin:default\":{\"marginTop\":\"10px\"}}},\"visibleOn\":\"${dataSource !== 'ActiveScan'}\"},{\"type\":\"grid\",\"columns\":[{\"body\":[{\"type\":\"tpl\",\"tpl\":\"资产发现：\",\"inline\":true,\"wrapperComponent\":\"\",\"id\":\"u:b52ceef9acf2\"}],\"id\":\"u:af0da0971fc2\",\"md\":\"auto\"},{\"body\":[{\"type\":\"tpl\",\"tpl\":\"共发现弱点应用 ${basicInfo.weaknessAppNum || 0} 个${productLevel === '工具箱版' ? '' : `，风险应用 ${basicInfo.riskAppNum || 0} 个`}，弱点API ${basicInfo.weaknessApiNum || 0} 个${productLevel === '工具箱版' ? ''  : `，风险API ${basicInfo.riskApiNum || 0} 个`}；\",\"inline\":false,\"wrapperComponent\":\"\",\"id\":\"u:b6f83611a903\"}],\"id\":\"u:4c3cd74f47ec\"}],\"id\":\"u:d50620c33b17\",\"themeCss\":{\"baseControlClassName\":{\"padding-and-margin:default\":{\"marginTop\":\"10px\"}}},\"visibleOn\":\"${dataSource === 'ActiveScan'}\"},{\"type\":\"grid\",\"columns\":[{\"body\":[{\"type\":\"tpl\",\"tpl\":\"弱点识别：\",\"inline\":true,\"wrapperComponent\":\"\",\"id\":\"u:93686b6e0b02\"}],\"id\":\"u:87685bcf005f\",\"md\":\"auto\"},{\"body\":[{\"type\":\"tpl\",\"tpl\":\"共识别弱点 ${basicInfo.totalWeaknessNum || 0} 个，未鉴权 ${basicInfo.noauthaccessWeaknessNum || 0} 个，登录弱密码 ${basicInfo.weakpasswordWeaknessNum || 0} 个，参数可遍历 ${basicInfo.horizontalaccessWeaknessApiNum || 0} 个，高危弱点 ${basicInfo.highLevelWeaknessNum || 0} 个，中危弱点 ${basicInfo.middleLevelWeaknessNum || 0} 个，低危弱点 ${basicInfo.lowLevelWeaknessNum || 0} 个；\",\"inline\":true,\"wrapperComponent\":\"\",\"id\":\"u:c7828f9d05f9\"}],\"id\":\"u:5988199db049\"}],\"id\":\"u:379a7d5a9956\",\"wrapperCustomStyle\":{\"margin-top\":\"10px\"}},{\"type\":\"container\",\"body\":[{\"type\":\"grid\",\"columns\":[{\"body\":[{\"type\":\"tpl\",\"tpl\":\"风险识别：\",\"inline\":true,\"wrapperComponent\":\"\",\"id\":\"u:0c5ba5d2025d\"}],\"id\":\"u:32c58f436224\",\"md\":\"auto\"},{\"body\":[{\"type\":\"tpl\",\"tpl\":\"共监测风险事件 ${basicInfo.totalRiskNum || 0} 个，高危风险 ${basicInfo.highLevelRiskNum || 0} 个，中危风险 ${basicInfo.middleLevelRiskNum || 0} 个，低危风险 ${basicInfo.lowLevelRiskNum || 0} 个；\",\"inline\":true,\"wrapperComponent\":\"\",\"id\":\"u:47aefc4c0f77\"}],\"id\":\"u:cec0b22f46c5\"}],\"id\":\"u:0076e28d8860\",\"wrapperCustomStyle\":{\"margin-top\":\"10px\"}}],\"style\":{\"position\":\"relative\",\"display\":\"flex\",\"inset\":\"auto\",\"flexWrap\":\"nowrap\",\"flexDirection\":\"column\",\"alignItems\":\"flex-start\",\"flex\":\"0 0 auto\"},\"size\":\"none\",\"wrapperBody\":false,\"id\":\"u:016bae1494f5\",\"isFixedWidth\":false,\"visibleOn\":\"${productLevel !== '工具箱版'}\"}],\"style\":{\"position\":\"relative\",\"display\":\"flex\",\"inset\":\"auto\",\"flexWrap\":\"nowrap\",\"flexDirection\":\"column\",\"alignItems\":\"flex-start\"},\"size\":\"none\",\"wrapperBody\":false,\"id\":\"u:349c8bccd3ed\",\"isFixedHeight\":false,\"isFixedWidth\":false,\"wrapperCustomStyle\":{\"border\":\"1px solid #ddd\",\"border-radius\":\"4px\",\"padding\":\"10px\",\"margin\":\"10px 0\"}},{\"type\":\"container\",\"body\":[{\"type\":\"tpl\",\"tpl\":\"资产发现\",\"inline\":true,\"wrapperComponent\":\"\",\"id\":\"u:c35ab58a2294\",\"wrapperCustomStyle\":{\"font-size\":\"16px\",\"color\":\"#333\"}},{\"type\":\"grid\",\"columns\":[{\"body\":[{\"type\":\"tpl\",\"tpl\":\"共发现web应用 ${basicInfo.totalAppNum || 0} 个，访问量Top10的应用分布如下：\",\"inline\":true,\"wrapperComponent\":\"\",\"id\":\"u:97bfe1fd8ad7\"},{\"type\":\"container\",\"body\":[{\"type\":\"chart\",\"config\":{\"title\":{\"text\":\"访问量TOP10应用\"},\"yAxis\":{\"type\":\"category\",\"data\":[\"Mon\",\"Tue\",\"Wed\",\"Thu\",\"Fri\",\"Sat\",\"Sun\"]},\"xAxis\":{\"type\":\"value\"},\"series\":[{\"data\":[120,200,150,80,70,110,130],\"type\":\"bar\",\"barMaxWidth\":20}]},\"replaceChartOption\":true,\"id\":\"u:09a89f03ccc5\",\"dataFilter\":\"const dataList =\\n  (data.__super &&\\n    data.__super.top10Info &&\\n    data.__super.top10Info.appVisistTop10) ||\\n  [];\\ndataList.reverse();\\nconst yAxisData = dataList.map(item => item.name);\\nconst seriesData = dataList.map(item => item.value);\\nreturn {\\n  title: {\\n    text: '访问量TOP10应用',\\n  },\\n  tooltip: {\\n    show: true\\n  },\\n  yAxis: {\\n    type: 'category',\\n    data: yAxisData,\\n    axisLabel: {\\n      width: 50,\\n      overflow: 'truncate'\\n    }\\n  },\\n  xAxis: {\\n    type: 'value',\\n  },\\n  series: [\\n    {\\n      data: seriesData,\\n      type: 'bar',\\n      barMaxWidth: 20,\\n    },\\n  ],\\n};\\n\",\"wrapperCustomStyle\":{},\"height\":\"400px\"}],\"style\":{\"position\":\"relative\",\"display\":\"flex\",\"inset\":\"auto\",\"flexWrap\":\"nowrap\",\"flexDirection\":\"column\",\"alignItems\":\"flex-start\"},\"size\":\"none\",\"wrapperBody\":false,\"id\":\"u:73320ffa93f4\",\"isFixedHeight\":false,\"isFixedWidth\":false,\"wrapperCustomStyle\":{\"border\":\"1px solid #ddd\",\"border-radius\":\"4px\",\"margin-top\":\"10px\"}}],\"id\":\"u:2a5f30be5909\",\"wrapperCustomStyle\":{}},{\"body\":[{\"type\":\"tpl\",\"tpl\":\"共发现API ${basicInfo.totalApiNum || 0} 个，访问量Top10的API分布如下：\",\"inline\":true,\"wrapperComponent\":\"\",\"id\":\"u:af07c6e7bac2\"},{\"type\":\"container\",\"body\":[{\"type\":\"chart\",\"config\":{\"title\":{\"text\":\"访问量TOP10API\"},\"yAxis\":{\"type\":\"category\",\"data\":[\"Mon\",\"Tue\",\"Wed\",\"Thu\",\"Fri\",\"Sat\",\"Sun\"]},\"xAxis\":{\"type\":\"value\"},\"series\":[{\"data\":[120,200,150,80,70,110,130],\"type\":\"bar\",\"barMaxWidth\":20}]},\"replaceChartOption\":true,\"id\":\"u:987a15710c1e\",\"dataFilter\":\"const dataList =\\n  (data.__super &&\\n    data.__super.top10Info &&\\n    data.__super.top10Info.apiVisistTop10) ||\\n  [];\\ndataList.reverse();\\nconst yAxisData = dataList.map(item => item.name);\\nconst seriesData = dataList.map(item => item.value);\\nreturn {\\n  title: {\\n    text: '访问量TOP10API',\\n  },\\n  tooltip: {\\n    show: true\\n  },\\n  yAxis: {\\n    type: 'category',\\n    data: yAxisData,\\n    axisLabel: {\\n      width: 50,\\n      overflow: 'truncate'\\n    }\\n  },\\n  xAxis: {\\n    type: 'value',\\n  },\\n  series: [\\n    {\\n      data: seriesData,\\n      type: 'bar',\\n      barMaxWidth: 20,\\n    },\\n  ],\\n};\\n\",\"wrapperCustomStyle\":{},\"height\":\"400px\"}],\"style\":{\"position\":\"relative\",\"display\":\"flex\",\"inset\":\"auto\",\"flexWrap\":\"nowrap\",\"flexDirection\":\"column\",\"alignItems\":\"flex-start\"},\"size\":\"none\",\"wrapperBody\":false,\"id\":\"u:b4cb8efd6bea\",\"isFixedHeight\":false,\"isFixedWidth\":false,\"wrapperCustomStyle\":{\"border\":\"1px solid #ddd\",\"border-radius\":\"4px\",\"margin-top\":\"10px\"}}],\"id\":\"u:e2703fb78f6c\"}],\"id\":\"u:f65036b1c7b7\",\"wrapperCustomStyle\":{\"margin-top\":\"10px\",\"margin-bottom\":\"10px\"}}],\"style\":{\"position\":\"relative\",\"display\":\"block\",\"inset\":\"auto\"},\"size\":\"none\",\"wrapperBody\":false,\"id\":\"u:c67bbddc6881\",\"isFixedHeight\":false,\"isFixedWidth\":false,\"visibleOn\":\"${dataSource !== 'ActiveScan'}\"},{\"type\":\"tpl\",\"tpl\":\"${productLevel === '工具箱版' ? '弱点概述' : '风险概述'}\",\"inline\":true,\"wrapperComponent\":\"\",\"id\":\"u:d0cbc47b1c64\",\"wrapperCustomStyle\":{\"font-size\":\"16px\",\"color\":\"#333\"}},{\"type\":\"grid\",\"columns\":[{\"body\":[{\"type\":\"tpl\",\"tpl\":\"共识别web应用 ${basicInfo.totalAppNum || 0} 个，弱点${productLevel !== '工具箱版' ? '/风险' : '' }应用Top10分布如下：\",\"inline\":true,\"wrapperComponent\":\"\",\"id\":\"u:84dd03cc7716\"},{\"type\":\"tabs\",\"tabs\":[{\"title\":\"弱点应用\",\"body\":[{\"type\":\"chart\",\"config\":{\"title\":{\"text\":\"访问量TOP10应用\"},\"yAxis\":{\"type\":\"category\",\"data\":[\"Mon\",\"Tue\",\"Wed\",\"Thu\",\"Fri\",\"Sat\",\"Sun\"]},\"xAxis\":{\"type\":\"value\"},\"series\":[{\"data\":[120,200,150,80,70,110,130],\"type\":\"bar\",\"barMaxWidth\":20}]},\"replaceChartOption\":true,\"id\":\"u:78525d352030\",\"dataFilter\":\"const dataList =\\n  (data.__super &&\\n    data.__super.top10Info &&\\n    data.__super.top10Info.weaknessAppTop10) ||\\n  [];\\ndataList.reverse();\\nconst yAxisData = dataList.map(item => item.name);\\nconst seriesData = dataList.map(item => item.value);\\nreturn {\\n  title: {\\n    text: '弱点应用',\\n  },\\n  tooltip: {\\n    show: true\\n  },\\n  yAxis: {\\n    type: 'category',\\n    data: yAxisData,\\n    axisLabel: {\\n      width: 50,\\n      overflow: 'truncate'\\n    }\\n  },\\n  xAxis: {\\n    type: 'value',\\n  },\\n  series: [\\n    {\\n      data: seriesData,\\n      type: 'bar',\\n      barMaxWidth: 20,\\n    },\\n  ],\\n};\\n\",\"wrapperCustomStyle\":{},\"height\":\"400px\"}],\"id\":\"u:7e8c67503efa\"},{\"title\":\"风险应用\",\"body\":[{\"type\":\"chart\",\"config\":{\"title\":{\"text\":\"访问量TOP10应用\"},\"yAxis\":{\"type\":\"category\",\"data\":[\"Mon\",\"Tue\",\"Wed\",\"Thu\",\"Fri\",\"Sat\",\"Sun\"]},\"xAxis\":{\"type\":\"value\"},\"series\":[{\"data\":[120,200,150,80,70,110,130],\"type\":\"bar\",\"barMaxWidth\":20}]},\"replaceChartOption\":true,\"id\":\"u:9a9bf2ec3718\",\"dataFilter\":\"const dataList =\\n  (data.__super &&\\n    data.__super.top10Info &&\\n    data.__super.top10Info.riskAppTop10) ||\\n  [];\\ndataList.reverse();\\nconst yAxisData = dataList.map(item => item.name);\\nconst seriesData = dataList.map(item => item.value);\\nreturn {\\n  title: {\\n    text: '风险应用',\\n  },\\n  tooltip: {\\n    show: true\\n  },\\n  yAxis: {\\n    type: 'category',\\n    data: yAxisData,\\n    axisLabel: {\\n      width: 50,\\n      overflow: 'truncate'\\n    }\\n  },\\n  xAxis: {\\n    type: 'value',\\n  },\\n  series: [\\n    {\\n      data: seriesData,\\n      type: 'bar',\\n      barMaxWidth: 20,\\n    },\\n  ],\\n};\\n\",\"wrapperCustomStyle\":{},\"height\":\"400px\"}],\"id\":\"u:e64f4250f6d6\",\"visibleOn\":\"${productLevel !== '工具箱版'}\"}],\"mountOnEnter\":true,\"id\":\"u:751be34b96f4\",\"themeCss\":{\"titleControlClassName\":{\"width:default\":\"100%\"}},\"tabsMode\":\"\",\"wrapperCustomStyle\":{\"root\":{\"margin-top\":\"10px\",\"border\":\"1px solid #ddd\",\"border-radius\":\"4px\"}},\"visible\":true}],\"id\":\"u:d17df12318b0\",\"wrapperCustomStyle\":{\"padding\":\"0\"}},{\"body\":[{\"type\":\"tpl\",\"tpl\":\"共识别API ${basicInfo.totalApiNum || 0} 个，弱点${productLevel !== '工具箱版' ? '/风险' : '' }APITop10分布如下：\",\"inline\":true,\"wrapperComponent\":\"\",\"id\":\"u:92947998e204\"},{\"type\":\"tabs\",\"tabs\":[{\"title\":\"弱点API\",\"body\":[{\"type\":\"chart\",\"config\":{\"title\":{\"text\":\"访问量TOP10应用\"},\"yAxis\":{\"type\":\"category\",\"data\":[\"Mon\",\"Tue\",\"Wed\",\"Thu\",\"Fri\",\"Sat\",\"Sun\"]},\"xAxis\":{\"type\":\"value\"},\"series\":[{\"data\":[120,200,150,80,70,110,130],\"type\":\"bar\",\"barMaxWidth\":20}]},\"replaceChartOption\":true,\"id\":\"u:0c709bbc68fb\",\"dataFilter\":\"const dataList =\\n  (data.__super &&\\n    data.__super.top10Info &&\\n    data.__super.top10Info.weaknessApiTop10) ||\\n  [];\\ndataList.reverse();\\nconst yAxisData = dataList.map(item => item.name);\\nconst seriesData = dataList.map(item => item.value);\\nreturn {\\n  title: {\\n    text: '弱点API ',\\n  },\\n  tooltip: {\\n    show: true\\n  },\\n  yAxis: {\\n    type: 'category',\\n    data: yAxisData,\\n    axisLabel: {\\n      width: 50,\\n      overflow: 'truncate'\\n    }\\n  },\\n  xAxis: {\\n    type: 'value',\\n  },\\n  series: [\\n    {\\n      data: seriesData,\\n      type: 'bar',\\n      barMaxWidth: 20,\\n    },\\n  ],\\n};\\n\",\"wrapperCustomStyle\":{},\"height\":\"400px\"}],\"id\":\"u:396506e85ad6\"},{\"title\":\"风险API\",\"body\":[{\"type\":\"chart\",\"config\":{\"title\":{\"text\":\"访问量TOP10应用\"},\"yAxis\":{\"type\":\"category\",\"data\":[\"Mon\",\"Tue\",\"Wed\",\"Thu\",\"Fri\",\"Sat\",\"Sun\"]},\"xAxis\":{\"type\":\"value\"},\"series\":[{\"data\":[120,200,150,80,70,110,130],\"type\":\"bar\",\"barMaxWidth\":20}]},\"replaceChartOption\":true,\"id\":\"u:20d2758dd214\",\"dataFilter\":\"const dataList =\\n  (data.__super &&\\n    data.__super.top10Info &&\\n    data.__super.top10Info.riskApiTop10) ||\\n  [];\\ndataList.reverse();\\nconst yAxisData = dataList.map(item => item.name);\\nconst seriesData = dataList.map(item => item.value);\\nreturn {\\n  title: {\\n    text: '风险API',\\n  },\\n  tooltip: {\\n    show: true\\n  },\\n  yAxis: {\\n    type: 'category',\\n    data: yAxisData,\\n    axisLabel: {\\n      width: 50,\\n      overflow: 'truncate'\\n    }\\n  },\\n  xAxis: {\\n    type: 'value',\\n  },\\n  series: [\\n    {\\n      data: seriesData,\\n      type: 'bar',\\n      barMaxWidth: 20,\\n    },\\n  ],\\n};\\n\",\"wrapperCustomStyle\":{},\"height\":\"400px\"}],\"id\":\"u:fb5c392d74f5\",\"visibleOn\":\"${productLevel !== '工具箱版'}\"}],\"mountOnEnter\":true,\"id\":\"u:41ef00285c21\",\"themeCss\":{\"titleControlClassName\":{\"width:default\":\"100%\"}},\"tabsMode\":\"\",\"wrapperCustomStyle\":{\"root\":{\"margin-top\":\"10px\",\"border-radius\":\"4px\",\"border\":\"1px solid #ddd\"}},\"visible\":true}],\"id\":\"u:a58ade20a1ee\"}],\"id\":\"u:2c715af2b690\",\"wrapperCustomStyle\":{\"margin-top\":\"10px\"}},{\"type\":\"grid\",\"columns\":[{\"body\":[{\"type\":\"tpl\",\"tpl\":\"共识别弱点类别 ${basicInfo.weaknessCategoryNum || 0} 种，弱点类型Top10分布如下：\",\"inline\":true,\"wrapperComponent\":\"\",\"id\":\"u:9bac4ecb6e55\"},{\"type\":\"container\",\"body\":[{\"type\":\"chart\",\"config\":{\"title\":{\"text\":\"访问量TOP10应用\"},\"yAxis\":{\"type\":\"category\",\"data\":[\"Mon\",\"Tue\",\"Wed\",\"Thu\",\"Fri\",\"Sat\",\"Sun\"]},\"xAxis\":{\"type\":\"value\"},\"series\":[{\"data\":[120,200,150,80,70,110,130],\"type\":\"bar\",\"barMaxWidth\":20}]},\"replaceChartOption\":true,\"id\":\"u:c474bb5fe083\",\"dataFilter\":\"const dataList =\\n  (data.__super &&\\n    data.__super.top10Info &&\\n    data.__super.top10Info.weaknessNameTop10) ||\\n  [];\\ndataList.reverse();\\nconst yAxisData = dataList.map(item => item.name);\\nconst seriesData = dataList.map(item => item.value);\\nreturn {\\n  title: {\\n    text: '弱点事件',\\n  },\\n  tooltip: {\\n    show: true\\n  },\\n  yAxis: {\\n    type: 'category',\\n    data: yAxisData,\\n    axisLabel: {\\n      width: 50,\\n      overflow: 'truncate'\\n    }\\n  },\\n  xAxis: {\\n    type: 'value',\\n  },\\n  series: [\\n    {\\n      data: seriesData,\\n      type: 'bar',\\n      barMaxWidth: 20,\\n    },\\n  ],\\n};\\n\",\"wrapperCustomStyle\":{},\"height\":\"400px\"}],\"style\":{\"position\":\"relative\",\"display\":\"flex\",\"inset\":\"auto\",\"flexWrap\":\"nowrap\",\"flexDirection\":\"column\",\"alignItems\":\"flex-start\"},\"size\":\"none\",\"wrapperBody\":false,\"id\":\"u:13e70cfd9c74\",\"isFixedHeight\":false,\"isFixedWidth\":false,\"wrapperCustomStyle\":{\"border\":\"1px solid #ddd\",\"border-radius\":\"4px\",\"margin-top\":\"10px\"}}],\"id\":\"u:5973d9f43036\",\"wrapperCustomStyle\":{\"padding\":\"0\"}},{\"body\":[{\"type\":\"tpl\",\"tpl\":\"共识别风险类型 ${basicInfo.riskCategoryNum || 0} 种，风险类型Top10分布如下：\",\"inline\":true,\"wrapperComponent\":\"\",\"id\":\"u:05e3303a8038\",\"visibleOn\":\"${productLevel !== '工具箱版'}\"},{\"type\":\"container\",\"body\":[{\"type\":\"chart\",\"config\":{\"title\":{\"text\":\"访问量TOP10API\"},\"yAxis\":{\"type\":\"category\",\"data\":[\"Mon\",\"Tue\",\"Wed\",\"Thu\",\"Fri\",\"Sat\",\"Sun\"]},\"xAxis\":{\"type\":\"value\"},\"series\":[{\"data\":[120,200,150,80,70,110,130],\"type\":\"bar\",\"barMaxWidth\":20}]},\"replaceChartOption\":true,\"id\":\"u:e82336bd83e5\",\"dataFilter\":\"const dataList =\\n  (data.__super &&\\n    data.__super.top10Info &&\\n    data.__super.top10Info.riskNameTop10) ||\\n  [];\\ndataList.reverse();\\nconst yAxisData = dataList.map(item => item.name);\\nconst seriesData = dataList.map(item => item.value);\\nreturn {\\n  title: {\\n    text: '风险事件',\\n  },\\n  tooltip: {\\n    show: true\\n  },\\n  yAxis: {\\n    type: 'category',\\n    data: yAxisData,\\n    axisLabel: {\\n      width: 50,\\n      overflow: 'truncate'\\n    }\\n  },\\n  xAxis: {\\n    type: 'value',\\n  },\\n  series: [\\n    {\\n      data: seriesData,\\n      type: 'bar',\\n      barMaxWidth: 20,\\n    },\\n  ],\\n};\\n\",\"wrapperCustomStyle\":{},\"height\":\"400px\",\"visible\":true}],\"style\":{\"position\":\"relative\",\"display\":\"flex\",\"inset\":\"auto\",\"flexWrap\":\"nowrap\",\"flexDirection\":\"column\",\"alignItems\":\"flex-start\"},\"size\":\"none\",\"wrapperBody\":false,\"id\":\"u:ad9c8f2fcdbb\",\"isFixedHeight\":false,\"isFixedWidth\":false,\"wrapperCustomStyle\":{\"border\":\"1px solid #ddd\",\"border-radius\":\"4px\",\"margin-top\":\"10px\"},\"visibleOn\":\"${productLevel !== '工具箱版'}\"}],\"id\":\"u:eb76a1d9432b\"}],\"id\":\"u:e17229dd1b50\",\"wrapperCustomStyle\":{\"margin-top\":\"10px\",\"margin-bottom\":\"10px\"}},{\"type\":\"tpl\",\"tpl\":\"API弱点\",\"inline\":false,\"wrapperComponent\":\"\",\"id\":\"u:149117bbf563\",\"wrapperCustomStyle\":{\"font-size\":\"16px\",\"color\":\"#333\"}},{\"type\":\"grid\",\"columns\":[{\"body\":[{\"type\":\"tpl\",\"tpl\":\"本周期内共发现弱点API ${basicInfo.weaknessApiNum || 0} 个，分布如下：\",\"inline\":false,\"wrapperComponent\":\"\",\"id\":\"u:6c3d113684b5\",\"wrapperCustomStyle\":{\"margin-top\":\"10px\"}}],\"id\":\"u:4d80583f5ec1\"},{\"body\":[{\"type\":\"input-text\",\"label\":\"\",\"name\":\"weaknessApiUrl\",\"id\":\"u:eb98d3a99d9d\",\"value\":\"\",\"placeholder\":\"请输入API进行检索\",\"addOn\":{\"label\":\"检索\",\"type\":\"button\",\"id\":\"u:ca5b37269ece\",\"onEvent\":{\"click\":{\"weight\":0,\"actions\":[{\"componentId\":\"u:23495c097f5e\",\"ignoreError\":false,\"actionType\":\"reload\",\"data\":{\"apiUrl\":\"${weaknessApiUrl}\"},\"dataMergeMode\":\"merge\"}]}}}}],\"id\":\"u:a6181b9426a1\"}],\"id\":\"u:9e4b5e4d984b\"},{\"id\":\"u:23495c097f5e\",\"type\":\"crud2\",\"mode\":\"table2\",\"dsType\":\"api\",\"syncLocation\":true,\"primaryField\":\"id\",\"loadType\":\"pagination\",\"headerToolbar\":[],\"footerToolbar\":[{\"type\":\"flex\",\"direction\":\"row\",\"justify\":\"flex-start\",\"alignItems\":\"stretch\",\"style\":{\"position\":\"static\",\"flexWrap\":\"nowrap\"},\"items\":[{\"type\":\"container\",\"align\":\"right\",\"body\":[{\"type\":\"pagination\",\"behavior\":\"Pagination\",\"layout\":[\"total\",\"perPage\",\"pager\"],\"perPage\":10,\"perPageAvailable\":[10,20,50,100],\"align\":\"right\",\"id\":\"u:a2703db4aae4\"}],\"wrapperBody\":false,\"style\":{\"flexGrow\":1,\"flex\":\"1 1 auto\",\"position\":\"static\",\"display\":\"flex\",\"flexBasis\":\"auto\",\"flexDirection\":\"row\",\"flexWrap\":\"nowrap\",\"alignItems\":\"stretch\",\"justifyContent\":\"flex-end\"},\"id\":\"u:6d21df060ee8\"}],\"id\":\"u:c78cbd1a5bd4\",\"isFixedHeight\":false,\"isFixedWidth\":false}],\"columns\":[{\"type\":\"tpl\",\"title\":\"API\",\"name\":\"apiUrl\",\"id\":\"u:105919848439\",\"placeholder\":\"-\",\"width\":200},{\"type\":\"tpl\",\"title\":\"应用名称\",\"name\":\"appName\",\"id\":\"u:f226b683e5f9\",\"placeholder\":\"-\"},{\"type\":\"tpl\",\"title\":\"弱点ID\",\"name\":\"operationId\",\"id\":\"u:4e9605f033aa\",\"placeholder\":\"-\",\"width\":120},{\"type\":\"tpl\",\"title\":\"弱点名称\",\"name\":\"weaknessName\",\"id\":\"u:fed320fc33d8\",\"placeholder\":\"-\",\"width\":120},{\"type\":\"tpl\",\"title\":\"弱点等级\",\"name\":\"level\",\"id\":\"u:3bdd5a7e2d24\"},{\"type\":\"tpl\",\"title\":\"弱点特征\",\"name\":\"proof\",\"id\":\"u:31c48056a5e6\"},{\"type\":\"tpl\",\"title\":\"弱点状态\",\"name\":\"state\",\"id\":\"u:eb18a0ee761e\"},{\"type\":\"tpl\",\"title\":\"访问域\",\"name\":\"visitDomains\",\"id\":\"u:1dcc04ac52c3\",\"placeholder\":\"-\",\"tpl\":\"${JOIN(visitDomains || [], ',')}\",\"wrapperComponent\":\"\"},{\"type\":\"tpl\",\"title\":\"响应数据标签\",\"name\":\"rspDataLabels\",\"id\":\"u:c056ecd1baa7\",\"placeholder\":\"-\",\"tpl\":\"${JOIN(rspDataLabels||[], ',')}\",\"wrapperComponent\":\"\"},{\"type\":\"tpl\",\"title\":\"发现时间\",\"name\":\"earlyTimestamp\",\"id\":\"u:d71056a519be\",\"placeholder\":\"-\",\"width\":170},{\"type\":\"operation\",\"title\":\"操作\",\"buttons\":[{\"type\":\"api-sample\",\"uri\":\"${uri}\",\"url\":\"${apiUrl}\",\"id\":\"u:a487672a8e24\"}],\"id\":\"u:019a79334d5e\",\"copyable\":false,\"fixed\":\"right\"}],\"editorSetting\":{\"mock\":{\"enable\":true,\"maxDisplayRows\":5}},\"api\":{\"url\":\"/audit-apiv2/report/detail/getApiSafeOperationItem\",\"method\":\"get\",\"requestAdaptor\":\"\",\"adaptor\":\"const total = response.data && response.data.totalCount || 0;\\nconst dataList = response.data && response.data.rows || [];\\nreturn {\\n    status: 0,\\n    data: {\\n        total,\\n        items: dataList.map(item => {\\n            return  item.apiWeaknessItem || {};\\n        })\\n    }\\n}\",\"messages\":{},\"data\":{\"id\":\"${id}\",\"code\":\"API_WEAKNESS_ITEM\",\"page\":\"${page}\",\"limit\":\"${perPage}\",\"&\":\"$$\"}},\"loadDataOnce\":false,\"loadDataOnceFetchOnFilter\":false,\"keepItemSelectionOnPageChange\":false},{\"type\":\"container\",\"body\":[{\"type\":\"tpl\",\"tpl\":\"API风险\",\"inline\":false,\"wrapperComponent\":\"\",\"id\":\"u:aa4bd8b14d1c\",\"wrapperCustomStyle\":{\"font-size\":\"16px\",\"color\":\"#333\"}},{\"type\":\"grid\",\"columns\":[{\"body\":[{\"type\":\"tpl\",\"tpl\":\"本周期内共发现风险事件 ${basicInfo.totalRiskNum || 0} 个，分布如下：\\n\",\"inline\":false,\"wrapperComponent\":\"\",\"id\":\"u:fd684412f635\",\"wrapperCustomStyle\":{\"margin-top\":\"10px\"}}],\"id\":\"u:a683c43d75e6\"},{\"body\":[{\"type\":\"input-text\",\"label\":\"\",\"name\":\"riskApiUrl\",\"id\":\"u:b53e0a8261d1\",\"value\":\"\",\"placeholder\":\"请输入API进行检索\",\"addOn\":{\"label\":\"检索\",\"type\":\"button\",\"id\":\"u:728e88210702\",\"onEvent\":{\"click\":{\"weight\":0,\"actions\":[{\"componentId\":\"u:518b1044b391\",\"ignoreError\":false,\"actionType\":\"reload\",\"data\":{\"apiUrl\":\"${riskApiUrl}\"},\"dataMergeMode\":\"merge\"}]}}}}],\"id\":\"u:434892de19ae\"}],\"id\":\"u:c9ea5b246fc5\"},{\"id\":\"u:518b1044b391\",\"type\":\"crud2\",\"mode\":\"table2\",\"dsType\":\"api\",\"syncLocation\":true,\"primaryField\":\"id\",\"loadType\":\"pagination\",\"headerToolbar\":[],\"footerToolbar\":[{\"type\":\"flex\",\"direction\":\"row\",\"justify\":\"flex-start\",\"alignItems\":\"stretch\",\"style\":{\"position\":\"static\",\"flexWrap\":\"nowrap\"},\"items\":[{\"type\":\"container\",\"align\":\"right\",\"body\":[{\"type\":\"pagination\",\"behavior\":\"Pagination\",\"layout\":[\"total\",\"perPage\",\"pager\"],\"perPage\":10,\"perPageAvailable\":[10,20,50,100],\"align\":\"right\",\"id\":\"u:a5727f353d64\"}],\"wrapperBody\":false,\"style\":{\"flexGrow\":1,\"flex\":\"1 1 auto\",\"position\":\"static\",\"display\":\"flex\",\"flexBasis\":\"auto\",\"flexDirection\":\"row\",\"flexWrap\":\"nowrap\",\"alignItems\":\"stretch\",\"justifyContent\":\"flex-end\"},\"id\":\"u:c93f8af6049f\"}],\"id\":\"u:97058b1d183e\",\"isFixedHeight\":false,\"isFixedWidth\":false}],\"columns\":[{\"type\":\"tpl\",\"title\":\"风险ID\",\"id\":\"u:d278241a8477\",\"name\":\"operationId\",\"placeholder\":\"-\"},{\"type\":\"tpl\",\"title\":\"风险主体\",\"id\":\"u:8b587aa6d8ea\",\"name\":\"entityValue\"},{\"type\":\"tpl\",\"title\":\"API\",\"name\":\"apiUrl\",\"id\":\"u:070a282312d4\",\"placeholder\":\"-\",\"width\":200},{\"type\":\"tpl\",\"title\":\"应用名称\",\"name\":\"appName\",\"id\":\"u:778ba3d6efd8\"},{\"type\":\"tpl\",\"title\":\"风险名称\",\"name\":\"riskName\",\"id\":\"u:e9ded04998e7\"},{\"type\":\"tpl\",\"title\":\"风险描述\",\"name\":\"riskDesc\",\"id\":\"u:3e69fb637b6e\"},{\"type\":\"tpl\",\"title\":\"风险状态\",\"name\":\"state\",\"id\":\"u:a1fa8ad682cd\"},{\"type\":\"tpl\",\"title\":\"发现时间\",\"name\":\"firstTime\",\"id\":\"u:c9080ec7064a\"},{\"type\":\"operation\",\"title\":\"操作\",\"buttons\":[{\"type\":\"button\",\"label\":\"查看\",\"level\":\"link\",\"behavior\":\"View\",\"onEvent\":{\"click\":{\"actions\":[{\"actionType\":\"url\",\"args\":{\"url\":\"/risk/detail?type=${policyId}&riskId=${riskDbId}\"},\"ignoreError\":false}]}},\"id\":\"u:681ac310bbc5\"}],\"id\":\"u:a42424c23e51\"}],\"editorSetting\":{\"mock\":{\"enable\":true,\"maxDisplayRows\":5}},\"api\":{\"url\":\"/audit-apiv2/report/detail/getApiSafeOperationItem\",\"method\":\"get\",\"requestAdaptor\":\"\",\"adaptor\":\"const total = response.data && response.data.totalCount || 0;\\nconst dataList = response.data && response.data.rows || [];\\nreturn {\\n    status: 0,\\n    data: {\\n        total,\\n        items: dataList.map(item => {\\n            return  item.riskInfoItem || {};\\n        })\\n    }\\n}\",\"messages\":{},\"data\":{\"id\":\"${id}\",\"code\":\"RISK_INFO_ITEM\",\"page\":\"${page}\",\"limit\":\"${perPage}\",\"&\":\"$$\"}},\"loadDataOnce\":false,\"loadDataOnceFetchOnFilter\":false,\"keepItemSelectionOnPageChange\":false}],\"style\":{\"position\":\"relative\",\"display\":\"block\",\"inset\":\"auto\"},\"size\":\"none\",\"wrapperBody\":false,\"id\":\"u:d696049c5646\",\"isFixedHeight\":false,\"isFixedWidth\":false,\"visibleOn\":\"${productLevel !== '工具箱版'}\"}],\"id\":\"u:f9a5d27257e6\",\"dsType\":\"api\",\"api\":{\"url\":\"/audit-apiv2/report/detail/getApiSafeOperationStatisticsInfo\",\"method\":\"get\",\"requestAdaptor\":\"\",\"adaptor\":\"\",\"messages\":{},\"data\":{\"id\":\"${id}\"}}}],\"toolbar\":[],\"data\":{\"startDate\":\"20250101\",\"endDate\":\"20250214\",\"resultPath\":\"aa\",\"productLevel\":\"工具箱版\",\"dataSource\":\"ActiveScan\"},\"regions\":[\"body\"]}",
            "cron" : "",
            "params" : "{\"startTime\":\"2024-12-01\",\"endTime\":\"2024-12-26\"}",
            "status" : 1
        }
    )
}
var crossDomainDataflowReportCount = db.scanTask.count({'code':'crossDomainDataflowReport','reportType':'AMIS'});
if (crossDomainDataflowReportCount == 0) {
    db.scanTask.save(
        {
            "code" : "crossDomainDataflowReport",
            "name" : "数据跨域流动报告",
            "policy" : 0,
            "reportType" : "AMIS",
            "amisReportJson" : "{\"type\":\"page\",\"id\":\"u:82b84556c65f\",\"asideResizor\":false,\"pullRefresh\":{\"disabled\":true},\"body\":[{\"type\":\"container\",\"body\":[{\"type\":\"tpl\",\"tpl\":\"数据跨域流动报告\",\"inline\":true,\"wrapperComponent\":\"\",\"id\":\"u:7301240862fd\",\"themeCss\":{\"baseControlClassName\":{\"font:default\":{\"fontWeight\":\"500\",\"fontSize\":\"20px\"},\"padding-and-margin:default\":{}}}},{\"type\":\"tpl\",\"tpl\":\"${startDate} - ${endDate}\",\"inline\":true,\"wrapperComponent\":\"\",\"id\":\"u:b8ca5f3cb327\",\"themeCss\":{\"baseControlClassName\":{\"padding-and-margin:default\":{\"marginTop\":\"0.7142857142857143rem\",\"marginRight\":\"0.7142857142857143rem\",\"marginBottom\":\"0.7142857142857143rem\",\"marginLeft\":\"0.7142857142857143rem\"}}},\"editorSetting\":{\"mock\":{}}},{\"type\":\"button\",\"label\":\"导出报告\",\"onEvent\":{\"click\":{\"actions\":[{\"ignoreError\":false,\"actionType\":\"url\",\"args\":{\"url\":\"/audit-apiv2/report/detail/exportCrossDomain?taskId=${id}\"}}]}},\"id\":\"u:ef0e447819d3\",\"level\":\"primary\",\"wrapperCustomStyle\":{\"root\":{\"position\":\"absolute\",\"top\":\"0\",\"right\":\"0\"}}}],\"style\":{\"position\":\"relative\",\"display\":\"flex\",\"inset\":\"auto\",\"flexWrap\":\"nowrap\",\"flexDirection\":\"column\",\"alignItems\":\"center\"},\"size\":\"none\",\"wrapperBody\":false,\"id\":\"u:933c1c444f1c\",\"isFixedHeight\":false,\"isFixedWidth\":false},{\"type\":\"service\",\"body\":[{\"type\":\"container\",\"body\":[{\"type\":\"tpl\",\"tpl\":\"基本信息\",\"inline\":false,\"wrapperComponent\":\"\",\"id\":\"u:3a7e61941eb5\",\"themeCss\":{\"baseControlClassName\":{\"font:default\":{\"color\":\"#333333\",\"fontSize\":\"16px\"},\"padding-and-margin:default\":{\"marginBottom\":\"10px\"}}}},{\"type\":\"tpl\",\"tpl\":\"跨域统计：共发现跨域应用 ${crossDomainApp} 个，跨域API ${crossDomainApi} 个，跨域数据标签 ${crossDomainDataLabel} 个；\",\"inline\":true,\"wrapperComponent\":\"\",\"id\":\"u:147686928ee0\"}],\"style\":{\"position\":\"relative\",\"display\":\"flex\",\"inset\":\"auto\",\"flexWrap\":\"nowrap\",\"flexDirection\":\"column\",\"alignItems\":\"flex-start\"},\"size\":\"none\",\"wrapperBody\":false,\"id\":\"u:2004733c13bc\",\"isFixedHeight\":false,\"isFixedWidth\":false},{\"type\":\"container\",\"body\":[{\"type\":\"tpl\",\"tpl\":\"跨域应用\",\"inline\":false,\"wrapperComponent\":\"\",\"id\":\"u:521ce6557160\",\"themeCss\":{\"baseControlClassName\":{\"font:default\":{\"color\":\"#333333\",\"fontSize\":\"16px\"},\"padding-and-margin:default\":{\"marginBottom\":\"10px\"}}}},{\"type\":\"grid\",\"id\":\"u:e6983dba65c2\",\"columns\":[{\"body\":[{\"type\":\"tpl\",\"tpl\":\"本周期内共发现跨域应用 ${crossDomainApp} 个，分布如下：\",\"inline\":true,\"wrapperComponent\":\"\",\"id\":\"u:add128301f64\"}],\"id\":\"u:65b512369bc7\"},{\"body\":[{\"type\":\"input-text\",\"label\":\"\",\"name\":\"host\",\"id\":\"u:b53e0a8261d1\",\"value\":\"\",\"placeholder\":\"请输入应用域名进行检索\",\"addOn\":{\"label\":\"检索\",\"type\":\"button\",\"id\":\"u:728e88210702\",\"onEvent\":{\"click\":{\"weight\":0,\"actions\":[{\"componentId\":\"u:69a1cddfc2b2\",\"ignoreError\":false,\"actionType\":\"reload\",\"data\":{\"host\":\"${host}\"},\"dataMergeMode\":\"merge\"}]}}}}],\"id\":\"u:2b9e7ff688ff\"}]},{\"id\":\"u:69a1cddfc2b2\",\"type\":\"crud2\",\"mode\":\"table2\",\"dsType\":\"api\",\"syncLocation\":true,\"primaryField\":\"id\",\"loadType\":\"pagination\",\"api\":{\"url\":\"/audit-apiv2/report/detail/crossDomainData\",\"method\":\"post\",\"requestAdaptor\":\"\",\"adaptor\":\"const total = response.data && response.data.totalCount || 0;\\nconst dataList = response.data && response.data.rows || [];\\nreturn {\\n  status: 0,\\n  data: {\\n    total,\\n    items: dataList,\\n  }\\n}\",\"messages\":{},\"dataType\":\"json\",\"data\":{\"&\":\"$$\",\"category\":\"APP\",\"page\":\"${page}\",\"limit\":\"${perPage}\",\"taskId\":\"${id}\"}},\"headerToolbar\":[],\"footerToolbar\":[{\"type\":\"flex\",\"direction\":\"row\",\"justify\":\"flex-start\",\"alignItems\":\"stretch\",\"style\":{\"position\":\"static\",\"flexWrap\":\"nowrap\"},\"items\":[{\"type\":\"container\",\"align\":\"right\",\"body\":[{\"type\":\"pagination\",\"behavior\":\"Pagination\",\"layout\":[\"total\",\"perPage\",\"pager\"],\"perPage\":10,\"perPageAvailable\":[10,20,50,100],\"align\":\"right\",\"id\":\"u:a6c4df7b7167\",\"size\":\"\"}],\"wrapperBody\":false,\"style\":{\"flexGrow\":1,\"flex\":\"1 1 auto\",\"position\":\"static\",\"display\":\"flex\",\"flexBasis\":\"auto\",\"flexDirection\":\"row\",\"flexWrap\":\"nowrap\",\"alignItems\":\"stretch\",\"justifyContent\":\"flex-end\"},\"id\":\"u:667401a01b0f\"}],\"id\":\"u:cf01545a66d3\",\"isFixedHeight\":false,\"isFixedWidth\":false}],\"columns\":[{\"type\":\"tpl\",\"title\":\"应用域名\",\"name\":\"host\",\"id\":\"u:11d0a776af86\",\"placeholder\":\"-\",\"wrapperCustomStyle\":{},\"width\":200,\"tpl\":\"${host}\",\"wrapperComponent\":\"\",\"maxLine\":3},{\"type\":\"tpl\",\"title\":\"应用名称\",\"name\":\"appName\",\"id\":\"u:5cc8c35d0acc\",\"placeholder\":\"-\",\"width\":200,\"tpl\":\"${appName}\",\"wrapperComponent\":\"\",\"maxLine\":3},{\"type\":\"tpl\",\"title\":\"部署域\",\"name\":\"deployDomains\",\"id\":\"u:82688eeace8f\",\"tpl\":\"${JOIN(deployDomains || [], ',')}\",\"wrapperComponent\":\"\"},{\"type\":\"tpl\",\"title\":\"访问域\",\"name\":\"visitDomains\",\"id\":\"u:993ce406edf0\",\"placeholder\":\"-\",\"tpl\":\"${JOIN(visitDomains || [], ',')}\",\"wrapperComponent\":\"\"},{\"type\":\"tpl\",\"title\":\"请求数据标签\",\"name\":\"reqDataLabelNames\",\"id\":\"u:01e5d2180555\",\"placeholder\":\"-\",\"tpl\":\"${JOIN(reqDataLabelNames || [], ',')}\",\"wrapperComponent\":\"\"},{\"type\":\"tpl\",\"title\":\"响应数据标签\",\"name\":\"rspDataLabelNames\",\"id\":\"u:d62819978331\",\"placeholder\":\"-\",\"tpl\":\"${JOIN(rspDataLabelNames || [],',')}\",\"wrapperComponent\":\"\"},{\"type\":\"tpl\",\"title\":\"跨域类型\",\"name\":\"crossDomainTypeNames\",\"id\":\"u:f548123a9b4b\",\"placeholder\":\"-\",\"tpl\":\"${JOIN(crossDomainTypeNames || [], ',')}\",\"wrapperComponent\":\"\"},{\"type\":\"tpl\",\"title\":\"发现时间\",\"name\":\"discoverTimeFormat\",\"id\":\"u:f6b490e91f47\",\"tpl\":\"\",\"wrapperComponent\":\"\",\"placeholder\":\"-\",\"width\":190},{\"type\":\"operation\",\"title\":\"操作\",\"buttons\":[{\"type\":\"button\",\"label\":\"查看\",\"level\":\"link\",\"behavior\":\"View\",\"onEvent\":{\"click\":{\"actions\":[{\"ignoreError\":false,\"actionType\":\"url\",\"args\":{\"url\":\"/apis/app/portrait?appUri=${appUri}\"}}]}},\"id\":\"u:d0e3810483ad\"}],\"id\":\"u:d9276dd2a392\"}],\"editorSetting\":{\"mock\":{\"enable\":true,\"maxDisplayRows\":5}},\"showHeader\":true,\"autoFillHeight\":false,\"wrapperCustomStyle\":{},\"scroll\":{\"x\":null,\"y\":null}}],\"style\":{\"position\":\"relative\",\"display\":\"flex\",\"inset\":\"auto\",\"flexWrap\":\"nowrap\",\"flexDirection\":\"column\",\"alignItems\":\"flex-start\"},\"size\":\"none\",\"wrapperBody\":false,\"id\":\"u:7b8dc756e5fd\",\"isFixedHeight\":false,\"isFixedWidth\":false,\"themeCss\":{\"baseControlClassName\":{\"padding-and-margin:default\":{\"marginTop\":\"20px\"}}},\"wrapperCustomStyle\":{\"display\":\"block !important\"}},{\"type\":\"container\",\"body\":[{\"type\":\"tpl\",\"tpl\":\"跨域API\",\"inline\":false,\"wrapperComponent\":\"\",\"id\":\"u:90e6cae50fb4\",\"themeCss\":{\"baseControlClassName\":{\"font:default\":{\"color\":\"#333333\",\"fontSize\":\"16px\"},\"padding-and-margin:default\":{\"marginBottom\":\"10px\"}}}},{\"type\":\"grid\",\"columns\":[{\"body\":[{\"type\":\"tpl\",\"tpl\":\"本周期内共发现跨域API ${crossDomainApi}  个，分布如下：\",\"inline\":true,\"wrapperComponent\":\"\",\"id\":\"u:2f37b1075931\"}],\"id\":\"u:a55a87d68d83\"},{\"body\":[{\"type\":\"input-text\",\"label\":\"\",\"name\":\"apiUrl\",\"id\":\"u:a9181b1bccdb\",\"value\":\"\",\"placeholder\":\"请输入API进行检索\",\"addOn\":{\"label\":\"检索\",\"type\":\"button\",\"id\":\"u:c19e2f0211fe\",\"onEvent\":{\"click\":{\"weight\":0,\"actions\":[{\"componentId\":\"u:063e21690107\",\"ignoreError\":false,\"actionType\":\"reload\",\"data\":{\"apiUrl\":\"${apiUrl}\"},\"dataMergeMode\":\"merge\"}]}}}}],\"id\":\"u:8c5cea62eb92\"}],\"id\":\"u:73df3a47e4ce\"},{\"id\":\"u:063e21690107\",\"type\":\"crud2\",\"mode\":\"table2\",\"dsType\":\"api\",\"syncLocation\":true,\"primaryField\":\"id\",\"loadType\":\"pagination\",\"api\":{\"url\":\"/audit-apiv2/report/detail/crossDomainData\",\"method\":\"post\",\"requestAdaptor\":\"\",\"adaptor\":\"const total = response.data && response.data.totalCount || 0;\\nconst dataList = response.data && response.data.rows || [];\\nreturn {\\n  status: 0,\\n  data: {\\n    total,\\n    items: dataList,\\n  }\\n}\",\"messages\":{},\"dataType\":\"json\",\"data\":{\"&\":\"$$\",\"category\":\"API\",\"page\":\"${page}\",\"limit\":\"${perPage}\",\"taskId\":\"${id}\"}},\"headerToolbar\":[],\"footerToolbar\":[{\"type\":\"flex\",\"direction\":\"row\",\"justify\":\"flex-start\",\"alignItems\":\"stretch\",\"style\":{\"position\":\"static\",\"flexWrap\":\"nowrap\"},\"items\":[{\"type\":\"container\",\"align\":\"right\",\"body\":[{\"type\":\"pagination\",\"behavior\":\"Pagination\",\"layout\":[\"total\",\"perPage\",\"pager\"],\"perPage\":10,\"perPageAvailable\":[10,20,50,100],\"align\":\"right\",\"id\":\"u:e1f5ced0d052\",\"size\":\"\"}],\"wrapperBody\":false,\"style\":{\"flexGrow\":1,\"flex\":\"1 1 auto\",\"position\":\"static\",\"display\":\"flex\",\"flexBasis\":\"auto\",\"flexDirection\":\"row\",\"flexWrap\":\"nowrap\",\"alignItems\":\"stretch\",\"justifyContent\":\"flex-end\"},\"id\":\"u:7b5070dfe31d\"}],\"id\":\"u:3a7ee76bcdd4\",\"isFixedHeight\":false,\"isFixedWidth\":false}],\"columns\":[{\"type\":\"tpl\",\"title\":\"应用域名\",\"name\":\"apiUrl\",\"id\":\"u:ce76240cd42f\",\"placeholder\":\"-\",\"wrapperCustomStyle\":{},\"width\":200,\"tpl\":\"${apiUrl}\",\"wrapperComponent\":\"\",\"maxLine\":3},{\"type\":\"tpl\",\"title\":\"应用名称\",\"name\":\"appName\",\"id\":\"u:d94df4bf459a\",\"placeholder\":\"-\",\"width\":200,\"tpl\":\"${appName}\",\"wrapperComponent\":\"\",\"maxLine\":3},{\"type\":\"tpl\",\"title\":\"部署域\",\"name\":\"deployDomains\",\"id\":\"u:4851db1b0231\",\"tpl\":\"${JOIN(deployDomains || [], ',')}\",\"wrapperComponent\":\"\"},{\"type\":\"tpl\",\"title\":\"访问域\",\"name\":\"visitDomains\",\"id\":\"u:17e7a482f5cb\",\"placeholder\":\"-\",\"tpl\":\"${JOIN(visitDomains || [], ',')}\",\"wrapperComponent\":\"\"},{\"type\":\"tpl\",\"title\":\"请求数据标签\",\"name\":\"reqDataLabelNames\",\"id\":\"u:04c4c7f89a09\",\"placeholder\":\"-\",\"tpl\":\"${JOIN(reqDataLabelNames || [], ',')}\",\"wrapperComponent\":\"\"},{\"type\":\"tpl\",\"title\":\"响应数据标签\",\"name\":\"rspDataLabelNames\",\"id\":\"u:8f2986e95882\",\"placeholder\":\"-\",\"tpl\":\"${JOIN(rspDataLabelNames || [],',')}\",\"wrapperComponent\":\"\"},{\"type\":\"tpl\",\"title\":\"跨域类型\",\"name\":\"crossDomainTypeNames\",\"id\":\"u:cd536ac04c56\",\"placeholder\":\"-\",\"tpl\":\"${JOIN(crossDomainTypeNames || [], ',')}\",\"wrapperComponent\":\"\"},{\"type\":\"tpl\",\"title\":\"发现时间\",\"name\":\"discoverTimeFormat\",\"id\":\"u:68a70d615bc0\",\"tpl\":\"\",\"wrapperComponent\":\"\",\"placeholder\":\"-\",\"width\":190},{\"type\":\"operation\",\"title\":\"操作\",\"buttons\":[{\"type\":\"api-sample\",\"uri\":\"${apiUri}\",\"url\":\"${apiUrl}\",\"id\":\"u:1e900c63d3c5\"}],\"id\":\"u:d61d17a4510c\"}],\"editorSetting\":{\"mock\":{\"enable\":true,\"maxDisplayRows\":5}},\"showHeader\":true,\"autoFillHeight\":false,\"wrapperCustomStyle\":{},\"scroll\":{\"x\":null,\"y\":null}}],\"style\":{\"position\":\"relative\",\"display\":\"flex\",\"inset\":\"auto\",\"flexWrap\":\"nowrap\",\"flexDirection\":\"column\",\"alignItems\":\"flex-start\"},\"size\":\"none\",\"wrapperBody\":false,\"id\":\"u:ecaaacc19f81\",\"isFixedHeight\":false,\"isFixedWidth\":false,\"themeCss\":{\"baseControlClassName\":{\"padding-and-margin:default\":{\"marginTop\":\"20px\"}}},\"wrapperCustomStyle\":{\"display\":\"block !important\"}},{\"type\":\"container\",\"body\":[{\"type\":\"tpl\",\"tpl\":\"跨域数据\",\"inline\":false,\"wrapperComponent\":\"\",\"id\":\"u:ecdac4e66596\",\"themeCss\":{\"baseControlClassName\":{\"font:default\":{\"color\":\"#333333\",\"fontSize\":\"16px\"},\"padding-and-margin:default\":{\"marginBottom\":\"10px\"}}}},{\"type\":\"grid\",\"columns\":[{\"body\":[{\"type\":\"tpl\",\"tpl\":\"本周期内共发现跨域数据 ${crossDomainDataLabel}  个，分布如下：\",\"inline\":true,\"wrapperComponent\":\"\",\"id\":\"u:de387491bc65\"}],\"id\":\"u:634a5f897c0d\"},{\"body\":[{\"type\":\"select\",\"label\":\"\",\"name\":\"searchDataLabels\",\"id\":\"u:5ddbe36c7f81\",\"multiple\":true,\"clearable\":true,\"searchable\":true,\"checkAll\":false,\"placeholder\":\"请选择数据标签进行检索\",\"joinValues\":false,\"maxTagCount\":1,\"delimiter\":\",\",\"extractValue\":true,\"source\":{\"method\":\"get\",\"url\":\"/audit-apiv2/api/dataLabel/getAll.do\",\"requestAdaptor\":\"\",\"adaptor\":\"const list = response.data || [];\\nreturn {\\n  status: 0,\\n  data: list.map(item => ({\\n    value: item.id,\\n    label: item.name\\n  }))\\n}\",\"messages\":{}},\"onEvent\":{\"change\":{\"weight\":0,\"actions\":[{\"componentId\":\"u:753c6e404884\",\"ignoreError\":false,\"actionType\":\"reload\",\"data\":{\"dataLabels\":\"${event.data.value}\"},\"dataMergeMode\":\"merge\"}]}},\"value\":\"${[]}\"}],\"id\":\"u:1c109f1cd486\"}],\"id\":\"u:14793c731688\"},{\"id\":\"u:753c6e404884\",\"type\":\"crud2\",\"mode\":\"table2\",\"dsType\":\"api\",\"syncLocation\":true,\"primaryField\":\"id\",\"loadType\":\"pagination\",\"api\":{\"url\":\"/audit-apiv2/report/detail/crossDomainData\",\"method\":\"post\",\"requestAdaptor\":\"\",\"adaptor\":\"const total = response.data && response.data.totalCount || 0;\\nconst dataList = response.data && response.data.rows || [];\\nreturn {\\n  status: 0,\\n  data: {\\n    total,\\n    items: dataList,\\n  }\\n}\",\"messages\":{},\"dataType\":\"json\",\"data\":{\"&\":\"$$\",\"category\":\"DATA_LABEL\",\"page\":\"${page}\",\"limit\":\"${perPage}\",\"taskId\":\"${id}\"}},\"headerToolbar\":[],\"footerToolbar\":[{\"type\":\"flex\",\"direction\":\"row\",\"justify\":\"flex-start\",\"alignItems\":\"stretch\",\"style\":{\"position\":\"static\",\"flexWrap\":\"nowrap\"},\"items\":[{\"type\":\"container\",\"align\":\"right\",\"body\":[{\"type\":\"pagination\",\"behavior\":\"Pagination\",\"layout\":[\"total\",\"perPage\",\"pager\"],\"perPage\":10,\"perPageAvailable\":[10,20,50,100],\"align\":\"right\",\"id\":\"u:6ef2620b12c9\",\"size\":\"\"}],\"wrapperBody\":false,\"style\":{\"flexGrow\":1,\"flex\":\"1 1 auto\",\"position\":\"static\",\"display\":\"flex\",\"flexBasis\":\"auto\",\"flexDirection\":\"row\",\"flexWrap\":\"nowrap\",\"alignItems\":\"stretch\",\"justifyContent\":\"flex-end\"},\"id\":\"u:6954bd51e449\"}],\"id\":\"u:ae7530cc09a1\",\"isFixedHeight\":false,\"isFixedWidth\":false}],\"columns\":[{\"type\":\"tpl\",\"title\":\"数据标签\",\"name\":\"dataLabelName\",\"id\":\"u:2e4313479530\"},{\"type\":\"tpl\",\"title\":\"所属网络域\",\"name\":\"belongDomains\",\"id\":\"u:959928e8d10d\",\"placeholder\":\"-\",\"tpl\":\"${JOIN(belongDomains || [], ',')}\",\"wrapperComponent\":\"\"},{\"type\":\"tpl\",\"title\":\"流向网络域\",\"name\":\"flowDomains\",\"id\":\"u:09624b83a057\",\"placeholder\":\"-\",\"tpl\":\"${JOIN(flowDomains || [], ',')}\",\"wrapperComponent\":\"\"},{\"type\":\"tpl\",\"title\":\"涉及应用数量\",\"name\":\"appCount\",\"id\":\"u:1bb5a6a003f8\",\"placeholder\":\"-\"},{\"type\":\"tpl\",\"title\":\"涉及API数量\",\"name\":\"apiCount\",\"id\":\"u:d1ff63c48bd8\",\"placeholder\":\"-\"},{\"type\":\"tpl\",\"title\":\"跨域类型\",\"name\":\"crossDomainTypeNames\",\"id\":\"u:86d83e82ba93\",\"placeholder\":\"-\",\"tpl\":\"${JOIN(crossDomainTypeNames || [], ',')}\",\"wrapperComponent\":\"\"}],\"editorSetting\":{\"mock\":{\"enable\":true,\"maxDisplayRows\":5}},\"showHeader\":true,\"autoFillHeight\":false,\"wrapperCustomStyle\":{},\"scroll\":{\"x\":null,\"y\":null}}],\"style\":{\"position\":\"relative\",\"display\":\"flex\",\"inset\":\"auto\",\"flexWrap\":\"nowrap\",\"flexDirection\":\"column\",\"alignItems\":\"flex-start\"},\"size\":\"none\",\"wrapperBody\":false,\"id\":\"u:c5b634532be5\",\"isFixedHeight\":false,\"isFixedWidth\":false,\"themeCss\":{\"baseControlClassName\":{\"padding-and-margin:default\":{\"marginTop\":\"20px\"}}},\"wrapperCustomStyle\":{\"display\":\"block !important\"}}],\"id\":\"u:8e87607347f8\",\"dsType\":\"api\",\"api\":{\"url\":\"/audit-apiv2/report/detail/crossDomainBasicInfo\",\"method\":\"get\",\"requestAdaptor\":\"\",\"adaptor\":\"\",\"messages\":{},\"data\":{\"taskId\":\"${id}\"}}}],\"toolbar\":[],\"data\":{\"startDate\":\"20250101\",\"endDate\":\"20250214\",\"id\":\"67ea0b0ff5c35c1f3acd2d00\"},\"regions\":[\"body\"]}",
            "cron" : "",
            "params" : "{\"startTime\":\"2024-12-01\",\"endTime\":\"2024-12-26\"}",
            "status" : 1
        }
    )
}