[{"_id": "APP_AUDIT_2.7_CHUANYIN_API_WEAKNESS_CNT", "name": "传音科技接口弱点数量统计", "repositoryOperateList": [{"repository": "MONGO", "collectionName": "httpApiWeakness", "operateActionEnum": "QUERY", "frontCriteriaList": [{"property": "delFlag", "predicate": "IS", "value": false}, {"property": "date", "predicate": "IS", "value": ""}], "metabaseGroupOperations": [{"groupFields": ["uri"], "aggregateOptionList": [{"aggrType": "COUNT", "aggrField": "count"}]}], "sort": [{"property": "count", "order": "DESC"}], "operateRsp": {"key": "groupList"}}], "rspKeyMap": {"groupList": "groupList"}}, {"_id": "APP_AUDIT_2.7_CHUANYIN_APP_WEAKNESS_CNT", "name": "传音科技应用弱点数量统计", "repositoryOperateList": [{"repository": "MONGO", "collectionName": "httpApiWeakness", "operateActionEnum": "QUERY", "frontCriteriaList": [{"property": "delFlag", "predicate": "IS", "value": false}, {"property": "date", "predicate": "IS", "value": ""}], "metabaseGroupOperations": [{"groupFields": ["appUri"], "aggregateOptionList": [{"aggrType": "COUNT", "aggrField": "count"}]}], "sort": [{"property": "count", "order": "DESC"}], "operateRsp": {"key": "groupList"}}], "rspKeyMap": {"groupList": "groupList"}}, {"_id": "APP_AUDIT_2.7_CHUANYIN_API_RISK_CNT", "name": "传音科技接口风险数量统计", "repositoryOperateList": [{"repository": "MONGO", "collectionName": "riskInfoAgg", "operateActionEnum": "QUERY", "frontCriteriaList": [{"property": "date", "predicate": "IS", "value": ""}], "unwindField": "channels", "metabaseGroupOperations": [{"groupFields": ["channels.value"], "aggregateOptionList": [{"aggrType": "COUNT", "aggrField": "count"}]}], "sort": [{"property": "count", "order": "DESC"}], "operateRsp": {"key": "groupList"}}], "rspKeyMap": {"groupList": "groupList"}}, {"_id": "APP_AUDIT_2.7_CHUANYIN_APP_RISK_CNT", "name": "传音科技应用风险数量统计", "repositoryOperateList": [{"repository": "MONGO", "collectionName": "riskInfoAgg", "operateActionEnum": "QUERY", "frontCriteriaList": [{"property": "date", "predicate": "IS", "value": ""}], "unwindField": "appUriList", "metabaseGroupOperations": [{"groupFields": ["appUriList"], "aggregateOptionList": [{"aggrType": "COUNT", "aggrField": "count"}]}], "sort": [{"property": "count", "order": "DESC"}], "operateRsp": {"key": "groupList"}}], "rspKeyMap": {"groupList": "groupList"}}]