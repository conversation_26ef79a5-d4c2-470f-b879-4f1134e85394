[{"_id": "APP_AUDIT_2.7_API_FEATURE_LABEL_METHOD", "name": "接口数据暴露面数据标签信息查询获取", "localMethodOperateList": [{"methodClassName": "ApiLifeStatServiceImpl", "methodName": "getUriFeatureLabel", "args": [["20211206", "20211207"], ["email"]], "operateRsp": {"key": "labelGroup"}}], "rspKeyMap": {"labelGroup": "labelGroup", "totalCount": "labelGroup"}, "frontObjUdf": {"totalCount": {"backendKeys": ["labelGroup"], "udf": "var a = JSON.parse(originalStr) ; originalStr = a['labelGroup']['totalCnt'].toString()", "udfRspType": "NUMBER"}, "labelGroup": {"backendKeys": ["labelGroup"], "udf": "var a = JSON.parse(originalStr) ;\n\nvar groupList = a['labelGroup']['labelGroup'];\ngroupList = groupList.sort(function(a,b) {return b.uriCnt - a.uriCnt});\ngroupList = groupList.slice(0,4);\n\noriginalStr = JSON.stringify( groupList )", "udfRspType": "JSON"}}}, {"_id": "APP_AUDIT_2.7_URI_LABEL_DATE_STAT_METHOD", "name": "接口数据暴露面走势信息查询获取", "localMethodOperateList": [{"methodClassName": "ApiLifeStatServiceImpl", "methodName": "getUriLabelDateStat", "args": ["20211206", "20211207"], "operateRsp": {"key": "labelGroup", "backend2FrontConvert": {"udf": "var groupList = JSON.parse(originalStr);\nvar result = groupList[\"labelGroup\"];\noriginalStr = JSON.stringify(result);", "udfRspType": "JSON"}}}], "rspKeyMap": {"labelGroup": "labelGroup"}}, {"_id": "APP_AUDIT_2.7_API_UATYPE_METHOD", "name": "接口访问终端分布查询获取", "localMethodOperateList": [{"methodClassName": "ApiLifeStatServiceImpl", "methodName": "getUriUaTypeDateStat", "args": ["20211206", "20211207"], "operateRsp": {"key": "group"}}], "repositoryOperateList": [{"repository": "NACOS", "collectionName": "uaType", "operateActionEnum": "QUERY", "frontCriteriaList": [], "operateRsp": {"key": "typeMap", "backend2FrontConvert": {"udf": "var groupList = JSON.parse(originalStr);\nvar result = {};\n   for(var i = 0;i<groupList[0][\"uaTypeConfigs\"].length;i++){\n        result[groupList[0][\"uaTypeConfigs\"][i][\"uaType\"]]=groupList[0][\"uaTypeConfigs\"][i][\"standardUaType\"];\n    }\noriginalStr = JSON.stringify(result);", "udfRspType": "JSON"}}}], "rspKeyMap": {"group": "group"}, "frontObjUdf": {"group": {"backendKeys": ["group", "typeMap"], "udf": "var a = JSON.parse(originalStr);\nvar groupList = a[\"group\"];\nvar typeMap = a[\"typeMap\"];\nfor(var i=0;i< groupList.length;i++){\n    \n    var uaTypes = groupList[i][\"uaType\"].split(\",\");\n    groupList[i][\"originalUaType\"] = uaTypes;\n\n    var formatUaType = [];\n    for(var j = 0;j< uaTypes.length;j++){\n        formatUaType.push(typeMap[  uaTypes[j]  ] || \"未知\");\n    }\n    groupList[i][\"uaType\"] = formatUaType;\n    groupList[i][\"apiCnt\"] = groupList[i][\"uriCnt\"]\n}\ngroupList = groupList.sort(function(a,b) {return b.uriCnt - a.uriCnt});\noriginalStr = JSON.stringify(groupList);", "udfRspType": "JSON"}}}, {"_id": "APP_AUDIT_2.7_CLICKHOUSE_EVENT_LABEL_VALUE_BY_LOCATION", "name": "单个事件敏感数据详情", "repositoryOperateList": [{"repository": "CLICKHOUSE", "collectionName": "http_defined_event", "operateActionEnum": "QUERY", "frontCriteriaList": [], "primaryOptimize": false, "operateRsp": {"key": "labelValues", "backend2FrontConvert": {"udf": "var a = JSON.parse(originalStr);originalStr = JSON.stringify(a[0])", "convertClassName": "ClickhouseEventHandlerImpl", "convertMethod": "getLabelValuesByLocation", "args": [true]}}}], "rspKeyMap": {"labelValues": "labelValues"}}]