[{"_id": "APP_AUDIT_2.7.0_WEAKNESS_DATE_GROUP_LIST", "name": "弱点首次发现分组", "repositoryOperateList": [{"repository": "MONGO", "collectionName": "httpApiWeakness", "operateActionEnum": "QUERY", "frontCriteriaList": [{"property": "state", "predicate": "IN", "value": ["NEW", "REPAIRING", "FIXED", "REOPEN"]}, {"property": "date", "predicate": "BETWEEN", "value": []}, {"property": "appUri", "predicate": "IS", "value": ""}, {"property": "uri", "predicate": "IS", "value": ""}, {"property": "delFlag", "predicate": "IS", "value": false}], "metabaseGroupOperations": [{"groupFields": ["date"], "aggregateOptionList": [{"aggrType": "COUNT", "aggrField": "count"}]}], "sort": [{"property": "_id", "order": "ASC"}], "operateRsp": {"key": "groupList", "backend2FrontConvert": {"udf": "var groupList = JSON.parse(originalStr);\nvar b=[];\nfor(var i = 0;i < groupList.length;i++) {\n    if(groupList[i]['_id'] && groupList[i]['_id'].length > 0){\n        b.push({\n            'id':groupList[i]['_id'],\n            'resultAlias':groupList[i]['count']\n        });\n    }\n} \noriginalStr = JSON.stringify(b)", "udfRspType": "JSON"}}}], "rspKeyMap": {"groupList": "groupList"}}, {"_id": "APP_AUDIT_2.7.0_RISKINFOAGG_DATE_GROUP_LIST", "name": "风险首次发现分组", "repositoryOperateList": [{"repository": "MONGO", "collectionName": "riskInfoAgg", "operateActionEnum": "QUERY", "frontCriteriaList": [{"property": "state", "predicate": "IN", "value": [0, 2, 3]}, {"property": "date", "predicate": "BETWEEN", "value": []}, {"property": "appUriList", "predicate": "IS", "value": ""}, {"property": "channels.value", "predicate": "IS", "value": ""}], "metabaseGroupOperations": [{"groupFields": ["date"], "aggregateOptionList": [{"aggrType": "COUNT", "aggrField": "count"}]}], "sort": [{"property": "_id", "order": "ASC"}], "operateRsp": {"key": "groupList", "backend2FrontConvert": {"udf": "var groupList = JSON.parse(originalStr);\nvar b=[];\nfor(var i = 0;i < groupList.length;i++) {\n    if(groupList[i]['_id'] && groupList[i]['_id'].length > 0){\n        b.push({\n            'id':groupList[i]['_id'],\n            'resultAlias':groupList[i]['count']\n        });\n    }\n} \noriginalStr = JSON.stringify(b)", "udfRspType": "JSON"}}}], "rspKeyMap": {"groupList": "groupList"}}, {"_id": "APP_AUDIT_2.7.0_WEAKNESS_WEAKNESSID_GROUP_LIST", "name": "弱点策略分组", "repositoryOperateList": [{"repository": "MONGO", "collectionName": "httpApiWeakness", "operateActionEnum": "QUERY", "frontCriteriaList": [{"property": "state", "predicate": "IN", "value": ["NEW", "REPAIRING", "FIXED", "REOPEN"]}, {"property": "appUri", "predicate": "IS", "value": ""}, {"property": "uri", "predicate": "IS", "value": ""}, {"property": "weaknessId", "predicate": "IS", "value": ""}, {"property": "delFlag", "predicate": "IS", "value": false}], "metabaseGroupOperations": [{"groupFields": ["weaknessId"], "aggregateOptionList": [{"aggrType": "COUNT", "aggrField": "count"}, {"aggrType": "FIRST", "aggrField": "name"}]}], "sort": [{"property": "count", "order": "DESC"}, {"property": "_id", "order": "DESC"}], "operateRsp": {"key": "groupList", "backend2FrontConvert": {"udf": "var groupList = JSON.parse(originalStr);\nvar b=[];\nfor(var i = 0;i < groupList.length;i++) {\n    if(groupList[i]['_id'] && groupList[i]['_id'].length > 0){\n        b.push({\n            'label':groupList[i]['_id'],\n            'name':groupList[i]['name'],\n            '_id':groupList[i]['_id'],\n            'count':groupList[i]['count']\n        });\n    }\n} \noriginalStr = JSON.stringify(b)", "udfRspType": "JSON"}}}], "rspKeyMap": {"groupList": "groupList"}}, {"_id": "API_AUDIT_WEAKNESS_STATE_GROUP_LIST", "name": "弱点状态分组", "repositoryOperateList": [{"repository": "MONGO", "collectionName": "httpApiWeakness", "operateActionEnum": "QUERY", "frontCriteriaList": [{"property": "state", "predicate": "IN", "value": ["NEW", "REPAIRING", "FIXED", "REOPEN"]}, {"property": "appUri", "predicate": "IS", "value": ""}, {"property": "uri", "predicate": "IS", "value": ""}, {"property": "delFlag", "predicate": "IS", "value": false}], "metabaseGroupOperations": [{"groupFields": ["state"], "aggregateOptionList": [{"aggrType": "COUNT", "aggrField": "count"}]}], "sort": [{"property": "count", "order": "DESC"}, {"property": "_id", "order": "DESC"}], "operateRsp": {"key": "groupList", "backend2FrontConvert": {"udf": "var a = JSON.parse(originalStr); var b = {'NEW':'待确认','SUSPEND':'已挂起','IGNORED':'已忽略','REPAIRING':'待修复','VERIFYING':'待验证','FIXED':'已修复','REOPEN':'再复现'} ; for(var i = 0; i < a.length;i++){ a[i]['label'] = a[i]['_id']; a[i]['name'] = b[a[i]['_id']] } ;originalStr = JSON.stringify(a)", "udfRspType": "JSON"}}}], "rspKeyMap": {"groupList": "groupList"}}, {"_id": "AUDIT_V2_2.7.0_IP_ACCOUNT_FILE_INFO", "name": "IP/账号上传下载文件名去重", "repositoryOperateList": [{"repository": "CLICKHOUSE", "collectionName": "http_defined_event", "operateActionEnum": "QUERY", "frontCriteriaList": [{"property": "ip", "predicate": "IS", "value": ""}, {"property": "date", "predicate": "BETWEEN", "value": []}, {"property": "isFile", "predicate": "IS", "value": true}, {"property": "eventDefineIds", "predicate": "HAS_ALL_CLICK_HOUSE", "value": ["monitor_event", "file_event"]}, {"property": "account", "predicate": "IS", "value": ""}, {"property": "file_fileDirection", "predicate": "IS", "value": "DOWNLOAD"}], "selectFields": [{"selectField": "DISTINCT( file_fileName )", "selectAsField": "fileName"}], "extraInfo": {"jobType": "AGGTASK", "queryFields": []}, "operateRsp": {"key": "fileNameList"}}], "rspKeyMap": {"fileNameList": "fileNameList"}}, {"_id": "AUDIT_V2_2.7.0_FILE_COUNT_INFO", "name": "文件上传/下载次数", "repositoryOperateList": [{"repository": "CLICKHOUSE", "collectionName": "http_defined_event", "operateActionEnum": "QUERY", "frontCriteriaList": [{"property": "file_sha256", "predicate": "IS", "value": ""}, {"property": "account", "predicate": "NE", "value": "QZ_NULL"}, {"property": "isFile", "predicate": "IS", "value": true}, {"property": "eventDefineIds", "predicate": "HAS_ALL_CLICK_HOUSE", "value": ["monitor_event", "file_event"]}, {"property": "file_fileDirection", "predicate": "IS", "value": "DOWNLOAD"}], "selectFields": [{"selectField": "count( * )", "selectAsField": "count"}], "extraInfo": {"jobType": "AGGTASK", "queryFields": []}, "operateRsp": {"key": "result"}}], "rspKeyMap": {"result": "result"}}, {"_id": "AUDIT_V2_2.7.0_FILE_IP_COUNT_INFO", "name": "文件上传/下载IP个数", "repositoryOperateList": [{"repository": "CLICKHOUSE", "collectionName": "http_defined_event", "operateActionEnum": "QUERY", "frontCriteriaList": [{"property": "file_sha256", "predicate": "IS", "value": ""}, {"property": "account", "predicate": "NE", "value": "QZ_NULL"}, {"property": "isFile", "predicate": "IS", "value": true}, {"property": "eventDefineIds", "predicate": "HAS_ALL_CLICK_HOUSE", "value": ["monitor_event", "file_event"]}, {"property": "file_fileDirection", "predicate": "IS", "value": "DOWNLOAD"}], "selectFields": [{"selectField": "count( DISTINCT ip)", "selectAsField": "count"}], "extraInfo": {"jobType": "AGGTASK", "queryFields": []}, "operateRsp": {"key": "result"}}], "rspKeyMap": {"result": "result"}}, {"_id": "AUDIT_V2_2.7.0_FILE_IP_DISTINCT_INFO", "name": "文件上传/下载IP去重", "repositoryOperateList": [{"repository": "CLICKHOUSE", "collectionName": "http_defined_event", "operateActionEnum": "QUERY", "frontCriteriaList": [{"property": "file_sha256", "predicate": "IS", "value": ""}, {"property": "account", "predicate": "NE", "value": "QZ_NULL"}, {"property": "isFile", "predicate": "IS", "value": true}, {"property": "eventDefineIds", "predicate": "HAS_ALL_CLICK_HOUSE", "value": ["monitor_event", "file_event"]}, {"property": "file_fileDirection", "predicate": "IS", "value": "DOWNLOAD"}], "selectFields": [{"selectField": "DISTINCT ip", "selectAsField": "ip"}], "extraInfo": {"jobType": "AGGTASK", "queryFields": []}, "operateRsp": {"key": "result"}}], "rspKeyMap": {"result": "result"}}, {"_id": "AUDIT_V2_2.7.0_FILE_ACCOUNT_COUNT_INFO", "name": "文件上传/下载账号个数", "repositoryOperateList": [{"repository": "CLICKHOUSE", "collectionName": "http_defined_event", "operateActionEnum": "QUERY", "frontCriteriaList": [{"property": "file_sha256", "predicate": "IS", "value": ""}, {"property": "account", "predicate": "NE", "value": "QZ_NULL"}, {"property": "isFile", "predicate": "IS", "value": true}, {"property": "eventDefineIds", "predicate": "HAS_ALL_CLICK_HOUSE", "value": ["monitor_event", "file_event"]}, {"property": "file_fileDirection", "predicate": "IS", "value": "DOWNLOAD"}], "selectFields": [{"selectField": "count( DISTINCT account)", "selectAsField": "count"}], "extraInfo": {"jobType": "AGGTASK", "queryFields": []}, "operateRsp": {"key": "result"}}], "rspKeyMap": {"result": "result"}}, {"_id": "AUDIT_V2_2.7.0_FILE_ACCOUNT_DISTINCT_INFO", "name": "文件上传/下载账号去重", "repositoryOperateList": [{"repository": "CLICKHOUSE", "collectionName": "http_defined_event", "operateActionEnum": "QUERY", "frontCriteriaList": [{"property": "file_sha256", "predicate": "IS", "value": ""}, {"property": "account", "predicate": "NE", "value": "QZ_NULL"}, {"property": "isFile", "predicate": "IS", "value": true}, {"property": "eventDefineIds", "predicate": "HAS_ALL_CLICK_HOUSE", "value": ["monitor_event", "file_event"]}, {"property": "file_fileDirection", "predicate": "IS", "value": "DOWNLOAD"}], "selectFields": [{"selectField": "DISTINCT account", "selectAsField": "account"}], "extraInfo": {"jobType": "AGGTASK", "queryFields": []}, "operateRsp": {"key": "result"}}], "rspKeyMap": {"result": "result"}}, {"_id": "AUDIT_V2_2.7.0_FILE_APIURL_COUNT_INFO", "name": "文件上传/下载接口个数", "repositoryOperateList": [{"repository": "CLICKHOUSE", "collectionName": "http_defined_event", "operateActionEnum": "QUERY", "frontCriteriaList": [{"property": "file_sha256", "predicate": "IS", "value": ""}, {"property": "account", "predicate": "NE", "value": "QZ_NULL"}, {"property": "isFile", "predicate": "IS", "value": true}, {"property": "eventDefineIds", "predicate": "HAS_ALL_CLICK_HOUSE", "value": ["monitor_event", "file_event"]}, {"property": "file_fileDirection", "predicate": "IS", "value": "DOWNLOAD"}], "selectFields": [{"selectField": "count( DISTINCT apiUrl )", "selectAsField": "count"}], "extraInfo": {"jobType": "AGGTASK", "queryFields": []}, "operateRsp": {"key": "result"}}], "rspKeyMap": {"result": "result"}}, {"_id": "AUDIT_V2_2.7.0_FILE_APIURL_INFO", "name": "文件上传/下载接口统计", "repositoryOperateList": [{"repository": "CLICKHOUSE", "collectionName": "http_defined_event", "operateActionEnum": "QUERY", "frontCriteriaList": [{"property": "file_sha256", "predicate": "IS", "value": "cf40596e55aefabcb20a208cc141d809ff55d4772d5a037d759b5b486b41462e"}, {"property": "account", "predicate": "NE", "value": "QZ_NULL"}, {"property": "isFile", "predicate": "IS", "value": true}, {"property": "eventDefineIds", "predicate": "HAS_ALL_CLICK_HOUSE", "value": ["monitor_event", "file_event"]}, {"property": "file_fileDirection", "predicate": "IS", "value": "DOWNLOAD"}], "metabaseGroupOperations": [{"groupFields": ["apiUrl"], "aggregateOptionList": [{"aggrType": "COUNT", "aggrField": "*", "aggrAsField": "count"}]}], "page": 1, "limit": 3000, "sort": [{"property": "count", "order": "DESC"}], "extraInfo": {"jobType": "AGGTASK", "queryFields": []}, "operateRsp": {"key": "result"}}], "rspKeyMap": {"result": "result"}}]