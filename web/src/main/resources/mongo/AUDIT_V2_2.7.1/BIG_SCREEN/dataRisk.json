[{"_id": "AUDIT_V2_2.7.1_RISK_SCREEN_LEVEL_GROUP", "name": "高中低风险数量统计", "repositoryOperateList": [{"repository": "MONGO", "collectionName": "riskInfoAgg", "operateActionEnum": "QUERY", "metabaseGroupOperations": [{"groupFields": ["level"], "aggregateOptionList": [{"aggrType": "COUNT", "aggrField": "count"}]}], "frontCriteriaList": [{"property": "state", "predicate": "IN", "value": [0, 2, 3]}], "operateRsp": {"key": "group", "backend2FrontConvert": {"udf": "var a = JSON.parse(originalStr); \nfor(var i = 0; i < a.length; i++) {\n    a[i][\"name\"] = a[i][\"_id\"] == 3 ? \"高\" : ( a[i][\"_id\"] == 2 ? \"中\" : \"低\" )\n}\noriginalStr = JSON.stringify(a);", "udfRspType": "JSON"}}}], "rspKeyMap": {"levelGroup": "group"}}, {"_id": "AUDIT_V2_2.7.1_RISK_SCREEN_EXCEPTION_LIST", "name": "实时风险告警", "repositoryOperateList": [{"repository": "MONGO", "collectionName": "riskInfoAgg", "operateActionEnum": "QUERY", "frontCriteriaList": [], "page": 1, "limit": 5, "sort": [{"property": "riskTime", "order": "DESC"}], "fields": ["auxInfo", "riskTime", "policySnapshot.riskPolicyId", "policySnapshot.name", "entities", "level", "state", "lastTime", "_id", "depart", "domainIds"], "operateRsp": {"key": "rows"}}], "rspKeyMap": {"rows": "rows"}}, {"_id": "AUDIT_V2_2.7.1_RISK_SCREEN_ENTITY_IP_GROUP", "name": "风险主体ip排名", "repositoryOperateList": [{"repository": "MONGO", "collectionName": "riskInfoAgg", "operateActionEnum": "QUERY", "metabaseGroupOperations": [{"groupFields": ["entities.value"], "aggregateOptionList": [{"aggrType": "FIRST", "aggrField": "domainIds"}, {"aggrType": "COUNT", "aggrField": "count"}]}], "unwindField": "entities", "page": 1, "limit": 5, "sort": [{"property": "count", "order": "DESC"}], "frontCriteriaList": [{"property": "state", "predicate": "IN", "value": [0, 2, 3]}, {"property": "entities.type", "predicate": "IS", "value": "IP"}], "operateRsp": {"key": "group"}}], "rspKeyMap": {"ipGroup": "group"}}, {"_id": "AUDIT_V2_2.7.1_RISK_SCREEN_ENTITY_ACCOUNT_GROUP", "name": "风险主体账号排名", "repositoryOperateList": [{"repository": "MONGO", "collectionName": "riskInfoAgg", "operateActionEnum": "QUERY", "metabaseGroupOperations": [{"groupFields": ["entities.value"], "aggregateOptionList": [{"aggrType": "COUNT", "aggrField": "count"}, {"aggrType": "FIRST", "aggrField": "depart"}]}], "unwindField": "entities", "page": 1, "limit": 5, "sort": [{"property": "count", "order": "DESC"}, {"property": "_id", "order": "DESC"}], "frontCriteriaList": [{"property": "state", "predicate": "IN", "value": [0, 2, 3]}, {"property": "entities.type", "predicate": "IS", "value": "ACCOUNT"}], "operateRsp": {"key": "group"}}], "rspKeyMap": {"accountGroup": "group"}}, {"_id": "AUDIT_V2_2.7.1_RISK_SCREEN_POLICY_ENTITIES_GROUP", "name": "风险趋势", "repositoryOperateList": [{"repository": "MONGO", "collectionName": "riskInfoAgg", "operateActionEnum": "QUERY", "metabaseGroupOperations": [{"groupFields": ["policySnapshot.riskPolicyId"], "aggregateOptionList": [{"aggrType": "FIRST", "aggrField": "policySnapshot.name", "aggrAsField": "name"}, {"aggrType": "FIRST", "aggrField": "level"}, {"aggrType": "COUNT", "aggrField": "count"}]}], "frontCriteriaList": [{"property": "state", "predicate": "IN", "value": [0, 2, 3]}], "operateRsp": {"key": "group"}, "eachRelateRepositoryOperate": [{"repository": "MONGO", "collectionName": "riskInfoAgg", "operateActionEnum": "QUERY", "frontCriteriaList": [{"property": "policySnapshot.riskPolicyId", "predicate": "IS", "value": "_id"}, {"property": "state", "predicate": "IN", "value": [0, 2, 3]}], "unwindField": "entities", "metabaseGroupOperations": [{"groupFields": ["entities.value"], "aggregateOptionList": [{"aggrType": "COUNT", "aggrField": "count"}]}], "sort": [{"property": "count", "order": "DESC"}, {"property": "_id", "order": "DESC"}], "page": 1, "limit": 10, "operateRsp": {"key": "entitiesGroup", "eachRelateOperateValueConfig": {"key": "_id", "name": "name"}}}]}], "rspKeyMap": {"policyGroup": "group", "entitiesGroup": "entitiesGroup"}}, {"_id": "AUDIT_V2_2.7.1_RISK_SCREEN_CHART", "name": "风险趋势折线图", "repositoryOperateList": [{"repository": "MONGO", "collectionName": "riskInfoAgg", "operateActionEnum": "QUERY", "metabaseGroupOperations": [{"groupFields": ["date"], "aggregateOptionList": [{"aggrType": "COUNT", "aggrField": "count"}, {"aggrType": "FIRST", "aggrField": "date"}]}], "frontCriteriaList": [{"property": "state", "predicate": "IN", "value": [0, 2, 3]}, {"property": "date", "predicate": "BETWEEN", "value": ["A_WEEK_BEFORE_CURRENT_DATE", "CURRENT_DATE"]}, {"property": "level", "predicate": "IS", "value": 1}], "sort": [{"property": "date", "order": "DESC"}], "operateRsp": {"key": "<PERSON><PERSON><PERSON>"}}, {"repository": "MONGO", "collectionName": "riskInfoAgg", "operateActionEnum": "QUERY", "metabaseGroupOperations": [{"groupFields": ["date"], "aggregateOptionList": [{"aggrType": "COUNT", "aggrField": "count"}, {"aggrType": "FIRST", "aggrField": "date"}]}], "frontCriteriaList": [{"property": "state", "predicate": "IN", "value": [0, 2, 3]}, {"property": "date", "predicate": "BETWEEN", "value": ["A_WEEK_BEFORE_CURRENT_DATE", "CURRENT_DATE"]}, {"property": "level", "predicate": "IS", "value": 2}], "sort": [{"property": "date", "order": "DESC"}], "operateRsp": {"key": "<PERSON><PERSON><PERSON>"}}, {"repository": "MONGO", "collectionName": "riskInfoAgg", "operateActionEnum": "QUERY", "metabaseGroupOperations": [{"groupFields": ["date"], "aggregateOptionList": [{"aggrType": "COUNT", "aggrField": "count"}, {"aggrType": "FIRST", "aggrField": "date"}]}], "frontCriteriaList": [{"property": "state", "predicate": "IN", "value": [0, 2, 3]}, {"property": "date", "predicate": "BETWEEN", "value": ["A_WEEK_BEFORE_CURRENT_DATE", "CURRENT_DATE"]}, {"property": "level", "predicate": "IS", "value": 3}], "sort": [{"property": "date", "order": "DESC"}], "operateRsp": {"key": "<PERSON><PERSON><PERSON>"}}, {"repository": "MONGO", "collectionName": "riskInfoAgg", "operateActionEnum": "QUERY", "metabaseGroupOperations": [{"groupFields": ["date"], "aggregateOptionList": [{"aggrType": "COUNT", "aggrField": "count"}, {"aggrType": "FIRST", "aggrField": "date"}]}], "frontCriteriaList": [{"property": "state", "predicate": "IN", "value": [0, 2, 3]}, {"property": "date", "predicate": "BETWEEN", "value": ["A_WEEK_BEFORE_CURRENT_DATE", "CURRENT_DATE"]}], "sort": [{"property": "date", "order": "DESC"}], "operateRsp": {"key": "totalChart"}}], "rspKeyMap": {"lowChart": "<PERSON><PERSON><PERSON>", "middleChart": "<PERSON><PERSON><PERSON>", "highChart": "<PERSON><PERSON><PERSON>", "totalChart": "totalChart"}}, {"_id": "AUDIT_V2_2.7.1_RISK_SCREEN_ENTITIES_APP_GROUP", "name": "风险流动感知", "repositoryOperateList": [{"repository": "MONGO", "collectionName": "riskInfoAgg", "operateActionEnum": "QUERY", "unwindFields": ["entities", "appUriList"], "metabaseGroupOperations": [{"groupFields": ["entities.value", "appUriList"], "aggregateOptionList": [{"aggrType": "COUNT", "aggrField": "count"}]}], "frontCriteriaList": [{"property": "state", "predicate": "IN", "value": [0, 2, 3]}, {"property": "appUriList", "predicate": "NE", "value": "QZ_NULL"}], "operateRsp": {"key": "group", "backend2FrontConvert": {"udf": "var a = JSON.parse(originalStr); \nfor(var i = 0; i < a.length; i++) {\n    a[i][\"host\"] =  a[i][\"appUriList\"].indexOf(\"httpapp:\") != -1 ? a[i][\"appUriList\"].substring(8) :  a[i][\"appUriList\"]\n}\noriginalStr = JSON.stringify(a);", "udfRspType": "JSON"}}}], "rspKeyMap": {"group": "group"}}]