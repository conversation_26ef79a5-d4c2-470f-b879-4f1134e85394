var sysRoleCount = db.sysRole.count();
if(sysRoleCount == 0) {
    db.sysRole.save([
        {
            "roleName" : "工具管理员",
            "remark" : "拥有工具所有功能权限",
            "assetsIdList" : [
                "1"
            ],
            "frailtyIdList" : [
                "4"
            ],
            "reportIdList" : [
                "7"
            ],
            "dataIdList" : [
                "12"
            ],
            "type" : 1,
            "status" : 1,
            "gmtCreate" : NumberLong((new Date()).getTime()),
            "gmtModified" : NumberLong((new Date()).getTime()),
            "envFlag": 1
        }
    ])
}

var sysUserCount = db.sysUser.count();
if(sysUserCount == 0) {
    db.sysRole.find({"roleName": "工具管理员"}, {"_id": 1}).forEach(
        function(role){
            db.sysUser.save([{
                "username" : "admin",
                "password" : "162075d9f13e45f20497b928f3777e623d08060682d7488c",
                "type" : 4,
                "status" : 1,
                "remark" : "拥有工具所有功能权限",
                "gmtCreate" : NumberLong((new Date()).getTime()),
                "gmtModified" : NumberLong((new Date()).getTime()),
                "roleId" : role._id,
                "email" : "",
                "envFlag": 1
            }])
        }
    );
}

// 初始化运维工具
db.toolConfig.drop();
db.toolConfig.save([
    {
        "name" : "请求IP字段统计",
        "keyword" : "orip",
        "createTime" : NumberLong((new Date()).getTime()),
        "savePath" : "/home/<USER>/audit-api",
        "saveName" : "orip",
        "resultType" : "csv",
        "cmd" : "python",
        "scriptName" : "prodtools.py",
        "scriptPath" : "/home/<USER>/audit-api/",
        "desc" : "请求IP字段统计",
        "paramType" : "json",
        "requireParams" : [
            "keyword",
            "mongoHost",
            "mongoPort",
            "savePath",
            "saveName",
            "resultType"
        ]
    },
    {
        "name" : "网段检测",
        "keyword" : "nets",
        "createTime" : NumberLong((new Date()).getTime()),
        "savePath" : "/home/<USER>/audit-api",
        "saveName" : "nets",
        "resultType" : "csv",
        "cmd" : "python",
        "scriptName" : "prodtools.py",
        "scriptPath" : "/home/<USER>/audit-api/",
        "desc" : "检测样例中IP信息，并给出大致的网段列表",
        "paramType" : "json",
        "requireParams" : [
            "keyword",
            "mongoHost",
            "mongoPort",
            "savePath",
            "saveName",
            "resultType"
        ]
    },
    {
        "name" : "顶级域名整理",
        "keyword" : "tldn",
        "createTime" : NumberLong((new Date()).getTime()),
        "savePath" : "/home/<USER>/audit-api",
        "saveName" : "tldn",
        "resultType" : "csv",
        "cmd" : "python",
        "scriptName" : "prodtools.py",
        "scriptPath" : "/home/<USER>/audit-api/",
        "desc" : "检测内网应用，并给出顶级域名",
        "paramType" : "json",
        "requireParams" : [
            "keyword",
            "mongoHost",
            "mongoPort",
            "savePath",
            "saveName",
            "resultType"
        ]
    },
    {
        "name" : "攻击URL整理",
        "keyword" : "atck",
        "createTime" : NumberLong((new Date()).getTime()),
        "savePath" : "/home/<USER>/audit-api",
        "saveName" : "atck",
        "resultType" : "csv",
        "cmd" : "python",
        "scriptName" : "prodtools.py",
        "scriptPath" : "/home/<USER>/audit-api/",
        "desc" : "检测流量中疑似攻击类型的URL",
        "paramType" : "json",
        "requireParams" : [
            "keyword",
            "mongoHost",
            "mongoPort",
            "savePath",
            "saveName",
            "resultType"
        ]
    },
    {
        "name" : "资产定义检测",
        "keyword" : "apsi",
        "createTime" : NumberLong((new Date()).getTime()),
        "savePath" : "/home/<USER>/audit-api",
        "saveName" : "apsi",
        "resultType" : "csv",
        "cmd" : "python",
        "scriptName" : "prodtools.py",
        "scriptPath" : "/home/<USER>/audit-api/",
        "desc" : "检测系统中需要合并或拆分的资产。",
        "paramType" : "json",
        "requireParams" : [
            "keyword",
            "mongoHost",
            "mongoPort",
            "savePath",
            "saveName",
            "resultType"
        ]
    },
    {
        "name" : "同应用下RESTFul接口合并推荐",
        "keyword" : "restfulcheck",
        "createTime" : NumberLong((new Date()).getTime()),
        "savePath" : "/home/<USER>/audit-api",
        "saveName" : "restfulcheck",
        "resultType" : "csv",
        "cmd" : "python",
        "scriptName" : "prodtools.py",
        "scriptPath" : "/home/<USER>/audit-api/",
        "desc" : "同应用下RESTFul接口合并推荐",
        "paramType" : "json",
        "requireParams" : [
            "keyword",
            "mongoHost",
            "mongoPort",
            "savePath",
            "saveName",
            "resultType"
        ]
    }
])

db.scanPolicy.drop();
db.scanPolicy.save([
    // 内置出境工具
    {
        "code":"dataabroad",
        "name":"数据出境工具",
        "description":"通过采集被检单位流量数据，按照多种协议还原成行为事件后，依据五元组信息判断是否出境，并识别敏感出境数据后计算出境数据量，评估出境风险程度。",
        "version":"2020.08.14.1715",
        "modules":[
            {
                "code":"dataabroad",
                "name":"数据出境报告",
                "ruleLists":[

                ]
            }
        ],
        "params":{
            "abroad_email_domains":[
                "gmail.com",
                "yahoo.com"
            ]
        },
        "policyConfig":{
            "means":"针对被检单位内部环境，检测web应用、文件服务、邮件服务的数据出境现象。",
            "scopes":[
                "ftp",
                "web",
                "email"
            ],
            "policyPath":null
        },
        "createTime":NumberLong((new Date()).getTime()),
        "delFlag":false
    }
])

