[{"_id": "APP_AUDIT_2.4_API_UATYPE_METHOD", "name": "接口访问终端分布查询获取", "localMethodOperateList": [{"methodClassName": "ApiOverviewPreloadJob", "methodName": "getApiOverviewInfo", "args": ["20211205", "20211207"], "operateRsp": {"key": "group", "backend2FrontConvert": {"udf": "var groupList = JSON.parse(originalStr);\nvar result = groupList[\"group\"];\noriginalStr = JSON.stringify(result);", "udfRspType": "JSON"}}}], "repositoryOperateList": [{"repository": "NACOS", "collectionName": "uaType", "operateActionEnum": "QUERY", "frontCriteriaList": [], "operateRsp": {"key": "typeMap", "backend2FrontConvert": {"udf": "var groupList = JSON.parse(originalStr);\nvar result = {};\n   for(var i = 0;i<groupList[0][\"uaTypeConfigs\"].length;i++){\n        result[groupList[0][\"uaTypeConfigs\"][i][\"uaType\"]]=groupList[0][\"uaTypeConfigs\"][i][\"standardUaType\"];\n    }\noriginalStr = JSON.stringify(result);", "udfRspType": "JSON"}}}], "rspKeyMap": {"group": "group"}, "frontObjUdf": {"group": {"backendKeys": ["group", "typeMap"], "udf": "var a = JSON.parse(originalStr);\nvar groupList = a[\"group\"];\nvar typeMap = a[\"typeMap\"];\nfor(var i=0;i<groupList.length;i++){\n    var originalUaType = [];\n    for(var j = 0;j<groupList[i][\"uaType\"].length;j++){\n        originalUaType.push(groupList[i][\"uaType\"][j]);\n        groupList[i][\"uaType\"][j] = typeMap[groupList[i][\"uaType\"][j]];\n        if(!groupList[i][\"uaType\"][j]){\n            groupList[i][\"uaType\"][j]=groupList[i][\"_id\"].split(\",\")[j].replace(\"]\",\"\").replace(\"[\",\"\").replace(\" \",\"\");\n        }\n    }\n    groupList[i][\"originalUaType\"] = originalUaType\n}\noriginalStr = JSON.stringify(groupList);", "udfRspType": "JSON"}}}, {"_id": "APP_AUDIT_2.4_APP_UATYPE", "name": "应用访问终端分布", "repositoryOperateList": [{"repository": "MONGO", "collectionName": "httpAppLifeDateStat", "operateActionEnum": "QUERY", "frontCriteriaList": [{"property": "delFlag", "predicate": "IS", "value": false}], "metabaseGroupOperations": [{"groupFields": ["uaTypes"], "aggregateOptionList": [{"aggrType": "ADDTOSET", "aggrField": "uri", "aggrAsField": "uriList"}]}], "projectOperationList": [{"startField": "uriList", "transform": "SIZE", "endField": "appCnt"}, {"startField": "_id", "transform": "IS", "endField": "uaType"}], "sort": [{"property": "appCnt", "order": "DESC"}], "operateRsp": {"key": "group", "backend2FrontConvert": {"udf": "var groupList = JSON.parse(originalStr);\nvar result = [];\nfor(var i =0; i< groupList.length;i++) {\n    if(groupList[i][\"_id\"] && groupList[i][\"uaType\"] && groupList[i][\"uaType\"].length >0) {\n        result.push(groupList[i])\n    } \n}\noriginalStr = JSON.stringify(result);", "udfRspType": "JSON"}}}, {"repository": "NACOS", "collectionName": "uaType", "operateActionEnum": "QUERY", "frontCriteriaList": [], "operateRsp": {"key": "typeMap", "backend2FrontConvert": {"udf": "var groupList = JSON.parse(originalStr);\nvar result = {};\n   for(var i = 0;i<groupList[0][\"uaTypeConfigs\"].length;i++){\n        result[groupList[0][\"uaTypeConfigs\"][i][\"uaType\"]]=groupList[0][\"uaTypeConfigs\"][i][\"standardUaType\"];\n    }\noriginalStr = JSON.stringify(result);", "udfRspType": "JSON"}}}], "rspKeyMap": {"group": "group"}, "frontObjUdf": {"group": {"backendKeys": ["group", "typeMap"], "udf": "var a = JSON.parse(originalStr);   \nvar groupList = a[\"group\"];\nvar typeMap = a[\"typeMap\"];\nfor(var i=0;i<groupList.length;i++){\n    var originalUaType = [];\n    for(var j = 0;j<groupList[i][\"uaType\"].length;j++){\n        originalUaType.push(groupList[i][\"uaType\"][j]);\n        groupList[i][\"uaType\"][j] = typeMap[groupList[i][\"uaType\"][j]];\n        if(!groupList[i][\"uaType\"][j]){\n            groupList[i][\"uaType\"][j]=groupList[i][\"_id\"].split(\",\")[j].replace(\"]\",\"\").replace(\"[\",\"\").replace(\" \",\"\");\n        }\n    }\n    groupList[i][\"originalUaType\"] = originalUaType;\n}\noriginalStr = JSON.stringify(groupList);", "udfRspType": "JSON"}}}, {"_id": "FILE_AUDIT_2.6_ACTIVE_DATE_LIST", "name": "账号活跃日期列表", "repositoryOperateList": [{"repository": "MONGO", "collectionName": "ipDateInfo", "operateActionEnum": "QUERY", "frontCriteriaList": [{"property": "downloadFileDistinctCnt", "predicate": "GT", "value": 0}], "metabaseGroupOperations": [{"groupFields": ["date"]}], "fields": ["date"], "operateRsp": {"key": "dates", "backend2FrontConvert": {"udf": "var a=JSON.parse(originalStr);\nfor(var i = 0; i<a.length;i++){a[i]=a[i][\"_id\"]};\noriginalStr = JSON.stringify(a)", "udfRspType": "JSON"}}}], "rspKeyMap": {"dates": "dates"}}, {"_id": "RISK_INFOAGG_GROUP_LIST", "name": "风险事件分组", "repositoryOperateList": [{"repository": "MONGO", "collectionName": "riskInfoAgg", "operateActionEnum": "QUERY", "frontCriteriaList": [], "metabaseGroupOperations": [{"groupFields": ["policySnapshot.riskPolicyId"], "aggregateOptionList": [{"aggrType": "COUNT", "aggrField": "count"}, {"aggrType": "FIRST", "aggrField": "policySnapshot"}]}], "sort": [{"property": "count", "order": "DESC"}, {"property": "_id", "order": "DESC"}], "operateRsp": {"key": "groupList", "backend2FrontConvert": {"udf": "var a; \na=JSON.parse(originalStr); \nvar b=[];\nvar levelMap = { \"3\":\"高\",\"2\":\"中\",\"1\":\"低\"}\nfor(var i = 0; i < a.length;i++){\n    b.push({\n        \"_id\":a[i][\"_id\"],\n        \"label\":a[i][\"_id\"],\n        \"name\":a[i][\"policySnapshot\"][\"name\"],\n        \"count\":a[i][\"count\"],\n        \"levelName\": levelMap[a[i][\"policySnapshot\"][\"level\"]] || \"低\",\n        \"level\":a[i][\"policySnapshot\"][\"level\"]\n    })\n} \noriginalStr = JSON.stringify(b)", "udfRspType": "JSON"}}}], "rspKeyMap": {"groupList": "groupList"}}]