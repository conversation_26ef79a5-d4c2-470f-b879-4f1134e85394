## 顶级目录 1开头
db.sysMenu.save({'_id':'1','parentId':null,'name':'概览','url':'','perms':'','type':0,'orderNum':1,'gmtCreate':NumberLong((new Date()).getTime()),'open':true,'customCode':'master'})
db.sysMenu.save({'_id':'2','parentId':null,'name':'APIs','url':'','perms':'','type':0,'orderNum':2,'gmtCreate':NumberLong((new Date()).getTime()),'open':true,'customCode':'master'})
db.sysMenu.save({'_id':'3','parentId':null,'name':'弱点','url':'','perms':'','type':0,'orderNum':3,'gmtCreate':NumberLong((new Date()).getTime()),'open':true,'customCode':'master'})
db.sysMenu.save({'_id':'4','parentId':null,'name':'风险','url':'','perms':'','type':0,'orderNum':4,'gmtCreate':NumberLong((new Date()).getTime()),'open':true,'customCode':'master'})
db.sysMenu.save({'_id':'5','parentId':null,'name':'审计','url':'','perms':'','type':0,'orderNum':5,'gmtCreate':NumberLong((new Date()).getTime()),'open':true,'customCode':'master'})
db.sysMenu.save({'_id':'6','parentId':null,'name':'报告','url':'','perms':'','type':0,'orderNum':6,'gmtCreate':NumberLong((new Date()).getTime()),'open':true,'customCode':'master'})
db.sysMenu.save({'_id':'6','parentId':null,'name':'态势','url':'','perms':'','type':0,'orderNum':7,'gmtCreate':NumberLong((new Date()).getTime()),'open':true,'customCode':'master'})


## APIS模块 二级目录 100开头
db.sysMenu.save({'_id':'200','parentId':'2','name':'API','url':'/apis/api','perms':'api:httpApi:view','type':1,'orderNum':1,'gmtCreate':NumberLong((new Date()).getTime()),'open':true,'customCode':'master'})
db.sysMenu.save({'_id':'201','parentId':'2','name':'应用','url':'/apis/app','perms':'api:httpApp:view','type':1,'orderNum':2,'gmtCreate':NumberLong((new Date()).getTime()),'open':true,'customCode':'master'})

## 功能
db.sysMenu.save({'_id':'2001','parentId':'2','name':'接口查看','url':'','perms':'api:httpApi:query','type':1,'orderNum':1,'gmtCreate':NumberLong((new Date()).getTime()),'open':true,'customCode':'master'})
db.sysMenu.save({'_id':'2002','parentId':'2','name':'接口导出','url':'','perms':'api:httpApi:export','type':1,'orderNum':1,'gmtCreate':NumberLong((new Date()).getTime()),'open':true,'customCode':'master'})
db.sysMenu.save({'_id':'2002','parentId':'2','name':'样例脱敏','url':'','perms':'api:httpApi:xxx','type':1,'orderNum':1,'gmtCreate':NumberLong((new Date()).getTime()),'open':true,'customCode':'master'})




###  1000开头



## 弱点模块
#db.sysMenu.save({'_id':'3','parentId':null,'name':'弱点','url':'','perms':'','type':0,'orderNum':3,'gmtCreate':NumberLong((new Date()).getTime()),'open':true,'customId':'master'})



## 风险模块
db.sysMenu.save({'_id':'400','parentId':'4','name':'风险','url':'/risk/list','perms':'api:riskCheckList:view','type':1,'orderNum':1,'gmtCreate':NumberLong((new Date()).getTime()),'open':true,'customCode':'master'})
db.sysMenu.save({'_id':'401','parentId':'4','name':'威胁','url':'/risk/threat','perms':'','type':1,'orderNum':2,'gmtCreate':NumberLong((new Date()).getTime()),'open':true,'customCode':'master'})
db.sysMenu.save({'_id':'402','parentId':'4','name':'异常','url':'','perms':'','type':1,'orderNum':3,'gmtCreate':NumberLong((new Date()).getTime()),'open':true,'customCode':'master'})


## 审计模块
db.sysMenu.save({'_id':'500','parentId':'5','name':'IP','url':'','perms':'','type':1,'orderNum':1,'gmtCreate':NumberLong((new Date()).getTime()),'open':true,'customCode':'master'})
db.sysMenu.save({'_id':'501','parentId':'5','name':'账号','url':'','perms':'','type':1,'orderNum':2,'gmtCreate':NumberLong((new Date()).getTime()),'open':true,'customCode':'master'})
db.sysMenu.save({'_id':'502','parentId':'5','name':'文件','url':'','perms':'','type':1,'orderNum':3,'gmtCreate':NumberLong((new Date()).getTime()),'open':true,'customCode':'master'})
db.sysMenu.save({'_id':'503','parentId':'5','name':'溯源','url':'','perms':'','type':1,'orderNum':4,'gmtCreate':NumberLong((new Date()).getTime()),'open':true,'customCode':'master'})
db.sysMenu.save({'_id':'504','parentId':'5','name':'日志','url':'','perms':'','type':1,'orderNum':5,'gmtCreate':NumberLong((new Date()).getTime()),'open':true,'customCode':'master'})

