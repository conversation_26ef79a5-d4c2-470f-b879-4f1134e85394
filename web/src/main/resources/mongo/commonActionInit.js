

var actionConfigs = [
    {
        "_id": "AUDIT_V2_SINGLE_TRACE_DETAIL",
        "name": "单个溯源任务详情",
        "repositoryOperateList": [{
            "repository": "MONGO",
            "collectionName": "analysisTaskRecord",
            "operateActionEnum": "FINDONE",
            "id": "",
            "frontCriteriaList": [],
            "operateRsp": {
                "key": "detail"
            }
        }],
        "rspKeyMap": {"detail": "detail"}
    },
    {
            "_id":"LOG_SEARCH_MIN_DATE",
            "name":"获取可检索的最小日期",
            "repositoryOperateList":[{
                "repository":"CLICKHOUSE",
                "collectionName":"http_defined_event",
                "operateActionEnum":"QUERY",
                "frontCriteriaList":[],
                "selectFields": [
                    {
                        "selectField": "min(timestamp)",
                        "selectAsField": "minTime"
                    }
                ],
                "extraInfo":{
                    "jobType":"AGGTASK",
                    "queryFields":["minDate"]
                },
                "operateRsp":{
                    "key":"minDate",
                    "backend2FrontConvert":{
                        "udf":"var a = JSON.parse(originalStr); \nvar b = a[0][\'minTime\'] * 1000;\nvar date = new Date(b);\nvar Y = date.getFullYear() + \'-\';\nvar M = (date.getMonth()+1 < 10 ? \'0\'+(date.getMonth()+1) : date.getMonth()+1) + \'-\';\nvar D = date.getDate();\noriginalStr = Y + M + D;\n\n",
                        "udfRspType":"STRING"
                    }
                }
            }],
            "rspKeyMap":{"minDate":"minDate"}
        },
    {
            "_id": "APP_AUDIT_2.1_CLICKHOUSE_EVENT_LABEL_VALUE",
            "name": "单个事件敏感数据详情",
            "repositoryOperateList": [
                {
                    "repository": "CLICKHOUSE",
                    "collectionName": "http_defined_event",
                    "operateActionEnum": "QUERY",
                    "frontCriteriaList": [ ],
                    "selectFields": [
                          {
                               "selectField": "*"
                          }
                    ],
                    "primaryOptimize": false,
                    "operateRsp": {
                        "key": "labelValues",
                        "backend2FrontConvert": {
                            "udf": "var a = JSON.parse(originalStr);originalStr = JSON.stringify(a[0])",
                            "convertClassName": "ClickhouseEventHandlerImpl",
                            "convertMethod": "getLabelValues",
                            "args": [
                                true
                            ]
                        }
                    }
                }
            ],
            "rspKeyMap": {
                "labelValues": "labelValues"
            }
    },
    {
        "_id":"AUDIT_V2_2.6.2_SPECIAL_ACCOUNT_INFO",
        "name":"特权账号审计中登录趋势/特权接口访问次数/风险趋势",
        "repositoryOperateList":[
          {
            "repository":"CLICKHOUSE",
            "collectionName":"special_account",
            "operateActionEnum":"QUERY",
            "frontCriteriaList":[{
              "property":"taskId",
              "predicate":"IS",
              "value":""
            },{
              "property":"account",
              "predicate":"IS",
              "value":""
            },{
              "property":"classifications",
              "predicate":"HAS_ANY_CLICK_HOUSE",
              "value":["login"]
            },{
              "property":"eventDefineIds",
              "predicate":"HAS_ANY_CLICK_HOUSE",
              "value":["login_event"]
            }],
            "selectFields":[
              {
                "selectField":"count(*)",
                "selectAsField":"eventDistinctCnt"
              },
              {
                "selectField":"date",
                "selectAsField":"date"
              }
            ],
            "metabaseGroupOperations":[{
              "groupFields":["date"],
              "aggregateOptionList":[]
            }],
            "extraInfo" : {
              "jobType" : "AGGTASK",
              "queryFields" : []
            },
            "operateRsp":{
              "key":"accountDateLoginEvent"
            }
          },
          {
            "repository":"CLICKHOUSE",
            "collectionName":"special_account",
            "operateActionEnum":"QUERY",
            "frontCriteriaList":[{
              "property":"taskId",
              "predicate":"IS",
              "value":""
            },{
              "property":"account",
              "predicate":"IS",
              "value":""
            },{
              "property":"apiUrl",
              "predicate":"IN",
              "value":[]
            }],
            "selectFields":[
              {
                "selectField":"count(*)",
                "selectAsField":"eventDistinctCnt"
              },
              {
                "selectField":"date",
                "selectAsField":"date"
              }
            ],
            "metabaseGroupOperations":[{
              "groupFields":["date"],
              "aggregateOptionList":[]
            }],
            "extraInfo" : {
              "jobType" : "AGGTASK",
              "queryFields" : []
            },
            "operateRsp":{
              "key":"accountDateSpecialApiEvent"
            }
          },
          {
            "repository":"MONGO",
            "collectionName":"riskInfo",
            "operateActionEnum":"QUERY",
            "frontCriteriaList":[{
              "property":"entities.value",
              "predicate":"IS",
              "value":""
            },{
              "property":"date",
              "predicate":"BETWEEN",
              "value":[]
            }],
            "metabaseGroupOperations": [{
              "groupFields": ["date"],
              "aggregateOptionList": [{
                "aggrType": "COUNT",
                "aggrField": "count"
              }]
            }],
            "sort": [{
              "property": "_id",
              "order": "ASC"
            }],
            "operateRsp":{
              "key":"accountDateRiskInfoAggEvent",
              "backend2FrontConvert": {
                "udf": "var a=JSON.parse(originalStr);\nvar b = [];\nif(a && a.length>0) {\n    for(var i = 0;i < a.length;i++) {\n        if(a[i].hasOwnProperty(\"_id\")) {\n            var year = a[i]['_id'].substring(0,4);\n            var month = a[i]['_id'].substring(4,6);\n            var day = a[i]['_id'].substring(6);\n            var date = year + \"-\" + month + \"-\" + day;\n            b.push({\n                \"date\":date,\n                \"eventDistinctCnt\":a[i][\"count\"]\n            })\n        }\n    }\n}\noriginalStr = JSON.stringify(b)",
                "udfRspType": "JSON"
              }
            }
          }
        ],
        "rspKeyMap":{
          "accountDateLoginEvent":"accountDateLoginEvent",
          "accountDateSpecialApiEvent":"accountDateSpecialApiEvent",
          "accountDateRiskInfoAggEvent":"accountDateRiskInfoAggEvent"
        }
      },
      {
        "_id":"AUDIT_V2_2.6.2_SPECIAL_ACCOUNT_CNT",
        "name":"特权账号审计中登录次数/特权账号访问次数/风险数",
        "repositoryOperateList":[
          {
            "repository":"CLICKHOUSE",
            "collectionName":"special_account",
            "operateActionEnum":"QUERY",
            "frontCriteriaList":[{
              "property":"taskId",
              "predicate":"IS",
              "value":""
            },{
              "property":"account",
              "predicate":"IS",
              "value":""
            },{
              "property":"classifications",
              "predicate":"HAS_ANY_CLICK_HOUSE",
              "value":["login"]
            },{
              "property":"eventDefineIds",
              "predicate":"HAS_ANY_CLICK_HOUSE",
              "value":["login_event"]
            }],
            "selectFields":[
              {
                "selectField":"count(*)",
                "selectAsField":"eventDistinctCnt"
              }
            ],
            "extraInfo" : {
              "jobType" : "AGGTASK",
              "queryFields" : []
            },
            "operateRsp":{
              "key":"accountDateLoginEventCnt"
            }
          },
          {
            "repository":"CLICKHOUSE",
            "collectionName":"special_account",
            "operateActionEnum":"QUERY",
            "frontCriteriaList":[{
              "property":"taskId",
              "predicate":"IS",
              "value":""
            },{
              "property":"account",
              "predicate":"IS",
              "value":""
            },{
              "property":"apiUrl",
              "predicate":"IN",
              "value":[]
            }],
            "selectFields":[
              {
                "selectField":"count(*)",
                "selectAsField":"eventDistinctCnt"
              }
            ],
            "extraInfo" : {
              "jobType" : "AGGTASK",
              "queryFields" : []
            },
            "operateRsp":{
              "key":"accountDateSpecialApiEventCnt"
            }
          },
          {
            "repository":"MONGO",
            "collectionName":"riskInfo",
            "operateActionEnum":"COUNT",
            "frontCriteriaList":[{
              "property":"entities.value",
              "predicate":"IS",
              "value":""
            },{
              "property":"date",
              "predicate":"BETWEEN",
              "value":[]
            }],
            "operateRsp":{
              "key":"accountDateRiskInfoAggEventCnt"
            }
          }
        ],
        "rspKeyMap":{
          "accountDateLoginEventCnt":"accountDateLoginEventCnt",
          "accountDateSpecialApiEventCnt":"accountDateSpecialApiEventCnt",
          "accountDateRiskInfoAggEventCnt":"accountDateRiskInfoAggEventCnt"
        }
      },
    {
            "_id": "LOG_CLICKHOUSE_EVENT_LIST_EXPORT",
            "name": "日志检索列表导出",
            "repositoryOperateList": [
                {
                    "repository": "CLICKHOUSE",
                    "collectionName": "http_defined_event",
                    "operateActionEnum": "EXPORT",
                    "frontCriteriaList": [ ],
                    "sort": [
                        {
                            "property": "timestamp",
                            "order": "DESC"
                        }
                    ],
                    "selectFields": [
                        {
                             "selectField": "timestamp"
                        },
                        {
                              "selectField": "id"
                        },
                        {
                             "selectField": "net_srcIp_v4"
                        },
                        {
                             "selectField": "net_srcIp_v6"
                        },
                        {
                             "selectField": "net_srcPort"
                        },
                        {
                              "selectField": "dstIpPosition_city"
                        },
                        {
                              "selectField": "net_dstIp_v4"
                        },
                        {
                              "selectField": "net_dstIp_v6"
                        },
                        {
                             "selectField": "net_dstPort"
                        },
                        {
                             "selectField": "originIpPosition_city"
                        },
                        {
                             "selectField": "account"
                        },
                        {
                             "selectField": "ip_v4"
                        },
                        {
                             "selectField": "ip_v6"
                         },
                        {
                             "selectField": "host"
                        },
                        {
                            "selectField": "apiUrl"
                        },
                        {
                              "selectField": "classifications"
                        },
                        {
                             "selectField": "featureLabels"
                        },
                        {
                             "selectField": "apiLevel"
                        },
                        {
                            "selectField": "appName"
                        },
                        {
                            "selectField": "appLevel"
                        },
                        {
                            "selectField": "reqContentType"
                        },
                        {
                             "selectField": "reqMethod"
                        },
                        {
                             "selectField": "reqDataLabelIds"
                        },
                        {
                             "selectField": "rspDataLabelIds"
                        },
                        {
                              "selectField": "uaType"
                        },
                        {
                              "selectField": "ua"
                        },
                        {
                              "selectField": "referer"
                        },
                        {
                              "selectField": "rspContentType"
                        },
                        {
                              "selectField": "rspContentLength"
                        },
                        {
                              "selectField": "rspStatus"
                        },
                        {
                              "selectField": "appFeatureLabels"
                        },
                        {
                            "selectField": "req"
                        },
                        {
                            "selectField": "nodeId"
                        },
                        {
                            "selectField": "eventDefineIds"
                        }
                    ],
                    "page": 1,
                    "limit": 10000,
                    "operateRsp": {
                        "key": "list",
                        "backend2FrontConvertMap": {
                            "timestamp": {
                                "convertClassName": "LocalMethodTransformImpl",
                                "convertMethod": "formatTimestamp",
                                "args": [
                                    "yyyy-MM-dd HH:mm:ss"
                                ]
                            },
                            "classifications": {
                                "convertClassName": "LocalMethodTransformImpl",
                                "convertMethod": "transformApiclassificationId2name",
                                "args": [ ]
                            },
                            "rspDataLabelIds": {
                                "convertClassName": "LocalMethodTransformImpl",
                                "convertMethod": "transformDataLabelId2name",
                                "args": [ ]
                            },
                            "reqDataLabelIds": {
                                "convertClassName": "LocalMethodTransformImpl",
                                "convertMethod": "transformDataLabelId2name",
                                "args": [ ]
                            },
                            "account": {
                                "convertClassName": "LocalMethodTransformImpl",
                                "convertMethod": "transAccount",
                                "args": [ ]
                            },
                            "apiFeatureLabels": {
                                "convertClassName": "LocalMethodTransformImpl",
                                "convertMethod": "transformApiFeatureLabelId2name",
                                "args": [ ]
                            },
                            "appFeatureLabels": {
                                 "convertClassName": "LocalMethodTransformImpl",
                                 "convertMethod": "transformAppFeatureLabelId2name",
                                 "args": [ ]
                            },
                            "eventDefineIds": {
                                "convertClassName": "LocalMethodTransformImpl",
                                "convertMethod": "transformEventType",
                                "args": [ ]
                            }
                        }
                    }
                }
            ],
            "exportFileName": "日志检索列表.xls",
            "exportTypeName": "日志检索列表",
            "exportSheetConfig": {
                "rows": {
                    "exportFields": [
                        {
                            "field": "timestamp",
                            "name": "时间"
                        },
                        {
                            "field": "ip",
                            "name": "源IP"
                        },
                        {
                            "field": "net.dstIp",
                            "name": "目的IP"
                        },
                        {
                            "field": "account",
                            "name": "账号"
                        },
                        {
                            "field": "apiUrl",
                            "name": "API"
                        },
                        {
                            "field": "classifications",
                            "name": "API类型"
                        },
                        {
                            "field": "host",
                            "name": "应用"
                        },
                        {
                            "field": "appName",
                            "name": "应用名称"
                        },
                        {
                            "field": "apiLevel",
                            "name": "接口等级"
                        },
                        {
                            "field": "reqContentType",
                            "name": "请求类型"
                        },
                        {
                            "field": "req.method",
                            "name": "请求方法"
                        },
                        {
                            "field": "uaType",
                            "name": "访问终端"
                        },
                        {
                            "field": "ua",
                            "name": "User-Agent"
                        },
                        {
                            "field": "referer",
                            "name": "Referer"
                        },
                        {
                            "field": "rspContentType",
                            "name": "响应类型"
                        },
                        {
                            "field": "rspContentLength",
                            "name": "响应长度"
                        },
                        {
                            "field": "rsp.status",
                            "name": "响应状态码"
                        },
                        {
                            "field": "rspDataLabelIds",
                            "name": "响应数据标签"
                        },
                        {
                            "field": "reqDataLabelIds",
                            "name": "请求数据标签"
                        },
                        {
                            "field": "nodeName",
                            "name": "来源节点"
                        }
                    ],
                    "sheetName": "日志检索列表"
                }
            },
            "rspKeyMap": {
                "rows": "list"
            }
        },
    {
            "_id": "CLICKHOUSE_CHECK_HTTP_EVENT",
            "name": "clickhouse溯源查看样例",
            "repositoryOperateList": [
                {
                    "repository": "CLICKHOUSE",
                    "collectionName": "event_trace",
                    "operateActionEnum": "QUERY",
                    "frontCriteriaList": [
                        {
                            "property": "primary",
                            "predicate": "IS",
                            "value": "17fb0a9e4fe711ebbf1e2e6fc93b3ab7"
                        }
                    ],
                    "selectFields": [
                        {
                            "selectField": "*"
                        }
                    ],
                    "operateRsp": {
                        "key": "httpEvent",
                        "backend2FrontConvert": {
                            "udf": "var a = JSON.parse(originalStr);originalStr = JSON.stringify(a[0])",
                            "convertClassName": "sampleEventService",
                            "convertMethod": "getSampleFromEs",
                            "args": [
                                true
                            ]
                        }
                    }
                }
            ],
            "rspKeyMap": {
                "data": "httpEvent"
            }
        },
        {
            "_id":"API_HIGH_CK_EVENT_LIST",
            "name":"Clickhouse日志列表",
            "repositoryOperateList":[
                {
                    "repository":"CLICKHOUSE",
                    "collectionName":"http_defined_event",
                    "operateActionEnum":"QUERY",
                    "frontCriteriaList":[

                    ],
                    "selectFields": [
                        {
                             "selectField": "timestamp"
                        },
                        {
                              "selectField": "id"
                        },
                        {
                             "selectField": "net_srcIp_v4"
                        },
                        {
                             "selectField": "net_srcIp_v6"
                        },
                        {
                             "selectField": "net_srcPort"
                        },
                        {
                              "selectField": "dstIpPosition_city"
                        },
                        {
                              "selectField": "net_dstIp_v4"
                        },
                        {
                              "selectField": "net_dstIp_v6"
                        },
                        {
                             "selectField": "net_dstPort"
                        },
                        {
                             "selectField": "originIpPosition_city"
                        },
                        {
                             "selectField": "account"
                        },
                        {
                             "selectField": "ip_v4"
                        },
                        {
                             "selectField": "ip_v6"
                         },
                        {
                             "selectField": "host"
                        },
                        {
                            "selectField": "apiUrl"
                        },
                        {
                              "selectField": "classifications"
                        },
                        {
                             "selectField": "featureLabels"
                        },
                        {
                             "selectField": "apiLevel"
                        },
                        {
                            "selectField": "appName"
                        },
                        {
                            "selectField": "appLevel"
                        },
                        {
                            "selectField": "reqContentType"
                        },
                        {
                             "selectField": "reqMethod"
                        },
                        {
                             "selectField": "reqDataLabelIds"
                        },
                        {
                             "selectField": "rspDataLabelIds"
                        },
                        {
                              "selectField": "uaType"
                        },
                        {
                              "selectField": "ua"
                        },
                        {
                              "selectField": "referer"
                        },
                        {
                              "selectField": "rspContentType"
                        },
                        {
                              "selectField": "rspContentLength"
                        },
                        {
                              "selectField": "rspStatus"
                        },
                        {
                              "selectField": "appFeatureLabels"
                        },
                        {
                            "selectField": "nodeId"
                        },
                        {
                            "selectField": "provinces"
                        },
                        {
                            "selectField": "eventDefineIds"
                        }
                    ],
                    "sort":[
                        {
                            "property":"timestamp",
                            "order":"DESC"
                        }
                    ],
                    "extraInfo":{
                        "dbType":"CLICKHOUSE"
                    },
                    "page":1,
                    "limit":10,
                    "operateRsp":{
                        "key":"rows"
                    }
                }
            ],
            "rspKeyMap":{
                "rows":"rows"
            }
        },
    {
        "_id":"API_HIGH_CK_EVENT_LIST_RISK",
        "name":"风险全量证据",
        "repositoryOperateList":[
            {
                "repository":"CLICKHOUSE",
                "collectionName":"http_defined_event",
                "operateActionEnum":"QUERY",
                "frontCriteriaList":[

                ],
                "selectFields": [
                    {
                        "selectField": "timestamp"
                    },
                    {
                        "selectField": "id"
                    },
                    {
                        "selectField": "loginInfo"
                    },
                    {
                        "selectField": "req"
                    },
                    {
                        "selectField": "rspLabelContentDistinctCount"
                    },
                    {
                        "selectField": "originIpPosition_city"
                    },
                    {
                        "selectField": "account"
                    },
                    {
                        "selectField": "ip_v4"
                    },
                    {
                        "selectField": "ip_v6"
                    },
                    {
                        "selectField": "apiUrl"
                    },
                    {
                        "selectField": "reqMethod"
                    },
                    {
                        "selectField": "rspDataLabelIds"
                    },
                    {
                        "selectField": "uaType"
                    },
                    {
                        "selectField": "referer"
                    },
                    {
                        "selectField": "ua"
                    },
                    {
                        "selectField": "rspContentType"
                    },
                    {
                        "selectField": "rspContentLength"
                    },
                    {
                        "selectField": "rspStatus"
                    },
                    {
                        "selectField": "nodeId"
                    }
                ],
                "sort":[
                    {
                        "property":"timestamp",
                        "order":"DESC"
                    }
                ],
                "extraInfo":{
                    "dbType":"CLICKHOUSE"
                },
                "page":1,
                "limit":10,
                "operateRsp":{
                    "key":"rows"
                }
            }
        ],
        "rspKeyMap":{
            "rows":"rows"
        }
    },
        {
                "_id":"API_HIGH_CK_EVENT_COUNT",
                "name":"Clickhouse日志数量",
                "repositoryOperateList":[
                    {
                        "repository":"CLICKHOUSE",
                        "collectionName":"http_defined_event",
                        "operateActionEnum":"COUNT",
                        "frontCriteriaList":[

                        ],
                        "extraInfo":{
                            "jobType":"AGGTASK",
                            "queryFields":[
                                "COUNT"
                            ],
                            "dbType":"CLICKHOUSE"
                        },
                        "operateRsp":{
                            "key":"totalCount",
                            "backend2FrontConvert":{
                                "udf":"var a = JSON.parse(originalStr); originalStr = a[0]['COUNT'].toString()",
                                "udfRspType":"NUMBER"
                            }
                        }
                    }
                ],
                "rspKeyMap":{
                    "totalCount":"totalCount"
                }
            },
           {
            "_id": "AUDIT_V2_TRACE_LIST",
            "name": "溯源任务列表",
            "repositoryOperateList": [{
                "repository": "MONGO",
                "collectionName": "analysisTaskRecord",
                "operateActionEnum": "QUERY",
                "frontCriteriaList": [{
                    "property": "jobType",
                    "predicate": "IS",
                    "value": "TRACETASK"
                }],
                "page": 1,
                "limit": 10,
                "sort": [{"property": "createTime", "order": "DESC"}],
                "operateRsp": {
                    "key": "list"
                }
            }, {
                "repository": "MONGO",
                "collectionName": "analysisTaskRecord",
                "operateActionEnum": "COUNT",
                "frontCriteriaList": [{
                    "property": "jobType",
                    "predicate": "IS",
                    "value": "TRACETASK"
                }],
                "operateRsp": {
                    "key": "totalCount"
                }
            }],
            "rspKeyMap": {"rows": "list", "totalCount": "totalCount"}
    },
    {
            "_id":"APP_AUDIT_2.1_OFFLINE_REPORT_LIST",
            "name":"离线任务列表",
            "repositoryOperateList":[{
                "repository":"MONGO",
                "collectionName":"reportTask",
                "operateActionEnum":"QUERY",
                "frontCriteriaList":[],
                "page":1,
                "limit":10,
                "sort": [{"property": "createTime", "order": "DESC"}],
                "operateRsp":{
                    "key":"list",
                }
            },{
                "repository":"MONGO",
                "collectionName":"reportTask",
                "operateActionEnum":"COUNT",
                "frontCriteriaList":[],
                "operateRsp":{
                    "key":"totalCount",
                }
            }],
            "rspKeyMap":{"rows":"list","totalCount":"totalCount"}
        },
    {
            "_id": "API_ADVANNCE_1.3_GET_AUDIT_ENUM",
            "name": "获取某类型的枚举值",
            "repositoryOperateList": [{
                "repository": "NACOS",
                "collectionName": "auditapiv2Enum",
                "operateActionEnum": "FINDONE",
                "frontCriteriaList": [],
                "operateRsp": {
                    "key": "list",
                    "backend2FrontConvert": {
                        "udf": "var a = JSON.parse(originalStr); originalStr = JSON.stringify( a['data'] )",
                        "udfRspType": "JSON"
                    }
                }
            }],
            "rspKeyMap": {"list": "list"}
        },
    {
            "_id": "AUDIT_V2_TRACE_GROUP_LIST",
            "name": "溯源分组",
            "repositoryOperateList": [{
                "repository": "CLICKHOUSE",
                "collectionName": "event_trace",
                "operateActionEnum": "QUERY",
                "frontCriteriaList": [],
                "sort": [{"property": "COUNT", "order": "DESC"}],
                "metabaseGroupOperations": [{
                    "groupFields": ["staff_depart"],
                    "aggregateOptionList": [{
                        "aggrType": "COUNT",
                        "aggrField": "staff_depart",
                        "aggrAsField": "COUNT"
                    }]
                }],
                "page": 1,
                "limit": 1000000,
                "operateRsp" : {
                    "key" : "list",
                    "backend2FrontConvert" : {
                        "udf" : "var a=JSON.parse(originalStr);\nvar b = [];\nif(a && a.length>0) {\n    for(var i = 0;i < a.length;i++) {\n        if(a[i].hasOwnProperty(\"staff_depart\") && a[i][\"staff_depart\"].trim() != \"\") {\n            \n            b.push({\n                \"staff_depart\":a[i][\"staff_depart\"],\n                \"COUNT\":a[i][\"COUNT\"]\n            })\n        }\n    }\n}\noriginalStr = JSON.stringify(b)",
                        "udfRspType" : "JSON"
                    }
                },
                "extraInfo": {
                    "jobType": "AGGTASK",
                    "queryFields": ["COUNT"]
                },
            }],
            "rspKeyMap": {"groupList": "list"}
        },
        {
                "_id": "AUDIT_V2_TRACE_EVENT_LIST",
                "name": "溯源列表",
                "repositoryOperateList": [{
                    "repository": "CLICKHOUSE",
                    "collectionName": "event_trace",
                    "operateActionEnum": "QUERY",
                    "frontCriteriaList": [],
                    "selectFields": [
                          {
                               "selectField": "timestamp"
                          },
                          {
                                "selectField": "id"
                          },
                          {
                               "selectField":"account"
                          },
                          {
                               "selectField":"ip"
                          },
                          {
                               "selectField":"accessDomains.id"
                          },
                          {
                              "selectField":"host"
                          },
                          {
                              "selectField":"apiUrl"
                          },
                          {
                              "selectField":"classifications"
                          },
                          {
                              "selectField":"featureLabels"
                          },
                          {
                              "selectField":"reqDataLabelIds"
                          },
                          {
                              "selectField":"rspDataLabelIds"
                          },
                        {
                            "selectField":"nodeId"
                        }
                    ],
                    "page": 1,
                    "limit": 10,
                    "sort": [],
                    "operateRsp": {
                        "key": "list"
                    }
                }, {
                    "repository": "CLICKHOUSE",
                    "collectionName": "event_trace",
                    "operateActionEnum": "QUERY",
                    "frontCriteriaList": [],
                    "selectFields": [
                        {
                            "selectField": "count(*) as COUNT"
                        }
                    ],
                    "extraInfo": {
                        "jobType": "AGGTASK",
                        "queryFields": ["COUNT"]
                    },
                    "operateRsp": {
                        "key": "totalCount",
                        "backend2FrontConvert": {
                            "udf": "var a = JSON.parse(originalStr); originalStr = a[0]['COUNT'].toString()",
                            "udfRspType": "NUMBER"
                        }
                    }
                }],
                "rspKeyMap": {"rows": "list", "totalCount": "totalCount"}
            },
            {
                    "_id": "AUDIT_V2_CLUE_TRACE_GROUP",
                    "name": "线索溯源分析结果聚合",
                    "repositoryOperateList": [{
                        "repository": "CLICKHOUSE",
                        "collectionName": "event_trace",
                        "operateActionEnum": "QUERY",
                        "frontCriteriaList": [{
                            "property": "distinctCnt",
                            "predicate": "GT",
                            "value": 0
                        }],
                        "sort": [{"property": "distinctCnt", "order": "DESC"}],
                        "selectFields": [
                            {
                                "selectField": "distinctCnt"
                            }
                        ],
                        "childCollectionNameOperate": {
                            "repository": "CLICKHOUSE",
                            "collectionName": "event_trace",
                            "operateActionEnum": "QUERY",
                            "selectFields": [
                                {
                                    "selectField": "length(arrayIntersect(groupUniqArrayArray(arrayFlatten(labelValue.value)),[ $1 ]))",
                                    "selectAsField": "distinctCnt"
                                }
                            ],
                            "frontCriteriaList": [],
                            "metabaseGroupOperations": [{}]
                        },
                        "extraInfo": {
                            "jobType": "AGGTASK",
                            "queryFields": []
                        },
                        "operateRsp": {
                            "key": "groupList"
                        }
                    }],
                    "rspKeyMap": {"rows": "rows", "totalCount": "totalCount"},
                    "frontObjUdf": {
                        "totalCount": {
                            "backendKeys": ["groupList"],
                            "udf": "var a = JSON.parse(originalStr) ; originalStr = a['groupList'].length.toString()",
                            "udfRspType": "NUMBER"
                        },
                        "rows": {
                            "backendKeys": ["groupList"],
                            "udf": "var a = JSON.parse(originalStr); \nvar b = a[\'groupList\'];\nvar c = [];\nfor(var i = 0; i< b.length;i++) {\n    if(b[i].hasOwnProperty(\"uri\")) {\n        if(b[i][\'uri\'].indexOf(\'@QZKJMGC@\') != -1) {\n            var index = b[i][\'uri\'].indexOf(\'@QZKJMGC@\');\n            b[i][\'apiUrl\'] = b[i][\'uri\'].substring(0,index)\n        } else {\n            b[i][\'apiUrl\'] = b[i][\'uri\'];\n        }\n    }\n    if(b[i].hasOwnProperty(\'account\') && (b[i][\'account\'] == \'QZ_NULL\' || b[i][\'account\'] == '')) {\n        continue;\n    }\n    c.push(b[i]);\n}\noriginalStr = JSON.stringify(c.slice( (page - 1)*limit, (page - 1)*limit + limit ))",
                            "udfRspType": "JSON",
                            "frontKeyValue": {
                                "page": 1,
                                "limit": 10
                            }
                        }
                    }
                },

     {
         "_id":"AUDIT_V2_CLUE_TRACE_DETAIL",
         "name":"线索溯源分析结果",
         "repositoryOperateList":[
             {
                 "repository":"CLICKHOUSE",
                 "collectionName":"event_trace",
                 "operateActionEnum":"QUERY",
                 "selectFields":[
                     {
                         "selectField":"count(*) as COUNT"
                     }
                 ],
                 "frontCriteriaList":[

                 ],
                 "extraInfo":{
                     "jobType":"AGGTASK",
                     "queryFields":[
                         "COUNT"
                     ]
                 },
                 "operateRsp":{
                     "key":"totalCount",
                     "backend2FrontConvert":{
                         "udf":"var a = JSON.parse(originalStr); originalStr = a[0]['COUNT'].toString()",
                         "udfRspType":"NUMBER"
                     }
                 }
             },
            {
                 "repository":"CLICKHOUSE",
                 "collectionName":"event_trace",
                 "operateActionEnum":"QUERY",
                 "selectFields":[
                     {
                         "selectField":"count(Distinct account) as accountDistinctCnt"
                     }
                 ],
                 "frontCriteriaList":[

                 ],
                 "extraInfo":{
                     "jobType":"AGGTASK",
                     "queryFields":[
                         "accountDistinctCnt"
                     ]
                 },
                 "operateRsp":{
                     "key":"accountDistinctCnt",
                     "backend2FrontConvert":{
                         "udf":"var a = JSON.parse(originalStr); originalStr = a[0]['accountDistinctCnt'].toString()",
                         "udfRspType":"NUMBER"
                     }
                 }
             },
             {
                 "repository":"CLICKHOUSE",
                 "collectionName":"event_trace",
                 "operateActionEnum":"QUERY",
                 "frontCriteriaList":[

                 ],
                 "distinctFieldConvertList":[
                     {
                         "distinctField":"ip",
                         "distinctAsField":"ipDistinctCnt"
                     },
                     {
                         "distinctField":"apiUrl",
                         "distinctAsField":"apiUriDistinctCnt"
                     }
                 ],
                 "extraInfo":{
                     "jobType":"AGGTASK",
                     "queryFields":[
                         "ipDistinctCnt",
                         "apiUriDistinctCnt"
                     ]
                 },
                 "operateRsp":{
                     "key":"distinctCnt"
                 }
             }
         ],
         "rspKeyMap":{
             "totalCount":"totalCount",
             "ipDistinctCnt":"ipDistinctCnt",
             "apiUriDistinctCnt":"apiUriDistinctCnt",
             "accountDistinctCnt":"accountDistinctCnt"
         },
         "frontObjUdf":{
             "ipDistinctCnt":{
                 "backendKeys":[
                     "distinctCnt"
                 ],
                 "udf":"var a = JSON.parse(originalStr) ; originalStr = a['distinctCnt'][0]['ipDistinctCnt'].toString()"
             },
             "apiUriDistinctCnt":{
                 "backendKeys":[
                     "distinctCnt"
                 ],
                 "udf":"var a = JSON.parse(originalStr) ; originalStr = a['distinctCnt'][0]['apiUriDistinctCnt'].toString()"
             }
         }
     },
     {
             "_id": "AUDIT_V2_MULTI_TRACE_DETAIL",
             "name": "多溯源任务详情",
             "repositoryOperateList": [{
                 "repository": "MONGO",
                 "collectionName": "traceTaskRecord",
                 "operateActionEnum": "QUERY",
                 "frontCriteriaList": [{
                     "property": "jobType",
                     "predicate": "IS",
                     "value": "TRACETASK"
                 }, {
                     "property": "_id",
                     "predicate": "IN",
                     "value": ["5ff44f8c1ddf3b0a5507e466", "5ff4501d1ddf3b0a5507e46e"]
                 }],
                 "operateRsp": {
                     "key": "list"
                 }
             }],
             "rspKeyMap": {"detail": "list"}
         },
     {
             "_id":"AUDIT_V2_ENTITY_TRACE_DETAIL",
             "name":"主体溯源分析结果",
             "repositoryOperateList":[{
                 "repository":"CLICKHOUSE",
                 "collectionName":"event_trace",
                 "operateActionEnum":"COUNT",
                 "frontCriteriaList":[{
                     "property":"taskId",
                     "predicate":"IS",
                     "value":"aaa"
                 }],
                 "extraInfo":{
                     "jobType":"AGGTASK",
                     "queryFields":["COUNT"]
                 },
                 "operateRsp":{
                     "key":"totalCount",
                     "backend2FrontConvert":{
                         "udf":"var a = JSON.parse(originalStr); originalStr = a[0]['COUNT'].toString()",
                         "udfRspType":"NUMBER"
                     }
                 }
             },{
                 "repository":"CLICKHOUSE",
                 "collectionName":"event_trace",
                 "operateActionEnum":"QUERY",
                 "frontCriteriaList":[{
                     "property":"taskId",
                     "predicate":"IS",
                     "value":"aaa"
                 }],
                 "selectFields":[
                     {
                         "selectField":"sum(length(arrayFlatten(labelValue.value)))",
                         "selectAsField":"sensiValueCnt"
                     }
                 ],
                 "extraInfo":{
                     "jobType":"AGGTASK",
                     "queryFields":["sensiValueCnt"]
                 },
                 "operateRsp":{
                     "key":"sensiValueCnt",
                     "backend2FrontConvert":{
                         "udf":"var a = JSON.parse(originalStr); originalStr = a[0]['sensiValueCnt'].toString()",
                         "udfRspType":"NUMBER"
                     }
                 }
             },{
                 "repository":"CLICKHOUSE",
                 "collectionName":"event_trace",
                 "operateActionEnum":"QUERY",
                 "frontCriteriaList":[{
                     "property":"taskId",
                     "predicate":"IS",
                     "value":"aaa"
                 }],
                 "selectFields":[
                     {
                         "selectField":"length( groupUniqArrayArray(arrayFlatten(labelValue.value)))",
                         "selectAsField":"distinctSensiValueCnt"
                     }
                 ],
                 "extraInfo":{
                     "jobType":"AGGTASK",
                     "queryFields":["distinctSensiValueCnt"]
                 },
                 "operateRsp":{
                     "key":"distinctSensiValueCnt",
                     "backend2FrontConvert":{
                         "udf":"var a = JSON.parse(originalStr); originalStr = a[0]['distinctSensiValueCnt'].toString()",
                         "udfRspType":"NUMBER"
                     }
                 }
             },{
                 "repository":"CLICKHOUSE",
                 "collectionName":"event_trace",
                 "operateActionEnum":"QUERY",
                 "selectFields":[
                     {
                         "selectField":"labelValue.label",
                         "selectAsField":"labelKey"
                     },
                     {
                         "selectField":"length( groupUniqArrayArray( labelValue.value) )",
                         "selectAsField":"labelCnt"
                     }
                 ],
                 "metabaseGroupOperations":[{
                     "groupFields":["labelValue.label"],
                     "aggregateOptionList":[]
                 }],
                 "childCollectionNameOperate":{
                     "repository":"CLICKHOUSE",
                     "collectionName":"event_trace ARRAY JOIN labelValue ",
                     "operateActionEnum":"QUERY",
                     "selectFields":[
                         {
                             "selectField":"labelValue.label"
                         },
                         {
                             "selectField":"labelValue.value"
                         }
                     ],
                     "frontCriteriaList":[{
                         "property":"taskId",
                         "predicate":"IS",
                         "value":"aaa"
                     }]
                 },
                 "extraInfo":{
                     "jobType":"AGGTASK",
                     "queryFields":["labelKey","labelCnt"]
                 },
                 "operateRsp":{
                     "key":"distinctCnt"
                 }
             }],
             "rspKeyMap":{"totalCount":"totalCount","sensiValueCnt":"sensiValueCnt","distinctSensiValueCnt":"distinctSensiValueCnt","distinctCnt":"distinctCnt"}
         },
     {
             "_id":"AUDIT_V2_ENNTITY_TRACE_IP_GROUP",
             "name":"主体溯源分析结果IP,账号，接口聚合",
             "repositoryOperateList":[{
                 "repository":"CLICKHOUSE",
                 "collectionName":"event_trace",
                 "operateActionEnum":"QUERY",
                 "frontCriteriaList":[{
                     "property":"taskId",
                     "predicate":"IS",
                     "value":"aaa"
                 }],
                 "selectFields":[
                     {
                         "selectField":"ip"
                     },
                     {
                         "selectField":"COUNT(*) AS COUNT"
                     },
                     {
                         "selectField":"groupUniqArray(accessDomains.id)",
                         "selectAsField":"accessDomainList"
                     }
                 ],
                 "sort":[{"property":"COUNT","order":"DESC"}],
                 "metabaseGroupOperations":[{
                     "groupFields":["ip"]
                 }],
                 "extraInfo":{
                     "jobType":"AGGTASK",
                     "queryFields":["ip","COUNT","accessDomainList"]
                 },
                 "operateRsp":{
                     "key":"groupList"
                 }
             }],
             "rspKeyMap": {"rows": "rows", "totalCount": "totalCount"},
             "frontObjUdf": {
                 "totalCount": {
                     "backendKeys": ["groupList"],
                     "udf": "var a = JSON.parse(originalStr) ; originalStr = a['groupList'].length.toString()",
                     "udfRspType": "NUMBER"
                 },
                 "rows": {
                     "backendKeys": ["groupList"],
                     "udf": "var a = JSON.parse(originalStr);\nvar b = a[\'groupList\'];\nvar c = [];\nfor(var i = 0; i< b.length;i++) {\n    if(b[i].hasOwnProperty(\"uri\")) {       \n        if(b[i][\'uri\'].indexOf(\'@QZKJMGC@\') != -1) {\n            var index = b[i][\'uri\'].indexOf(\'@QZKJMGC@\');\n            b[i][\'apiUrl\'] = b[i][\'uri\'].substring(0,index)\n        } else {\n            b[i][\'apiUrl\'] = b[i][\'uri\'];      \n        }   \n    }\n    if(b[i].hasOwnProperty(\'account\') && (b[i][\'account\'] == \'QZ_NULL\' || b[i][\'account\'] == '')) {\n        continue;   \n    }   \n    c.push(b[i]);\n}\noriginalStr = JSON.stringify(c.slice( (page - 1)*limit, (page - 1)*limit + limit ))",
                     "udfRspType": "JSON",
                     "frontKeyValue": {
                         "page": 1,
                         "limit": 10
                     }
                 }
             }
         },
     {
             "_id":"ES_CHECK_HTTP_EVENT",
             "name":"es溯源查看样例",
             "repositoryOperateList":[{
                 "repository":"ES",
                 "collectionName":"http_defined_event_20210107",
                 "operateActionEnum":"QUERY",
                 "frontCriteriaList":[{
                     "property":"primary",
                     "predicate":"IS",
                     "value":"17fb0a9e4fe711ebbf1e2e6fc93b3ab7"
                 }],
                 "operateRsp":{
                     "key":"httpEvent",
                     "backend2FrontConvert":{
                         "udf":"var a = JSON.parse(originalStr);originalStr = JSON.stringify(a[0])",
                         "convertClassName":"sampleEventService",
                         "convertMethod":"getSampleFromEs",
                         "args":[true]
                     }
                 }
             }],
             "rspKeyMap":{
                 "data":"httpEvent"
             }
         },
     {
             "_id": "AUDIT_V2_TRACE_EVENT_LIST_EXPORT",
             "name": "数据溯源导出列表",
             "repositoryOperateList": [{
                 "repository": "CLICKHOUSE",
                 "collectionName": "event_trace",
                 "operateActionEnum": "EXPORT",
                 "frontCriteriaList": [],
                 "page": 1,
                 "limit": 10000,
                 "sort": [],
                 "selectFields": [
                                           {
                                                "selectField": "timestamp"
                                           },
                                           {
                                                 "selectField": "id"
                                           },
                                           {
                                                "selectField":"account"
                                           },
                                           {
                                                "selectField":"ip"
                                           },
                                           {
                                                "selectField":"accessDomains.id"
                                           },
                                           {
                                               "selectField":"host"
                                           },
                                           {
                                               "selectField":"apiUrl"
                                           },
                                           {
                                               "selectField":"classifications"
                                           },
                                           {
                                               "selectField":"featureLabels"
                                           },
                                           {
                                               "selectField":"reqDataLabelIds"
                                           },
                                           {
                                               "selectField":"rspDataLabelIds"
                                           },
                                             {
                                                 "selectField":"nodeId"
                                             }
                 ],
                 "operateRsp": {
                     "key": "rows",
                     "backend2FrontConvertMap":{
                         "timestamp":{
                             "convertClassName":"LocalMethodTransformImpl",
                             "convertMethod":"formatTimestamp",
                             "args":["yyyy-MM-dd HH:mm:ss"]
                         },
                         "account":{
                             "udf":"var a = originalStr;\nif (a == \'QZ_NULL\') { \n    originalStr = \'\' \n} else {\n    originalStr = a;\n}",
                             "udfRspType":"STRING"
                         },
                         "accessDomains":{
                             "udf":"var a = JSON.parse(originalStr);\nvar ids = [];\nfor(var i = 0; i < a.length;i++) {\n    ids.push(a[i][\"id\"])\n}\noriginalStr = ids.join(\",\");",
                             "udfRspType":"STRING",
                             "newField":"accessDomains_id"
                         },
                         "rspDataLabelIds": {
                              "convertClassName": "LocalMethodTransformImpl",
                              "convertMethod": "transformDataLabelId2name",
                              "args": [ ]
                         },
                         "reqDataLabelIds": {
                              "convertClassName": "LocalMethodTransformImpl",
                              "convertMethod": "transformDataLabelId2name",
                              "args": [ ]
                         },
                         "apiFeatureLabels": {
                              "convertClassName": "LocalMethodTransformImpl",
                              "convertMethod": "transformApiFeatureLabelId2name",
                              "args": [ ]
                         }
                     }
                 }
             },{
                 "repository": "CLICKHOUSE",
                 "collectionName": "event_trace",
                 "operateActionEnum": "QUERY",
                 "frontCriteriaList": [{
                     "property": "distinctCnt",
                     "predicate": "GT",
                     "value": 0
                 }],
                 "sort": [{"property": "distinctCnt", "order": "DESC"}],
                 "selectFields": [
                     {
                         "selectField": "distinctCnt"
                     },
                     {
                         "selectField":"apiUrl"
                     }
                 ],
                 "childCollectionNameOperate": {
                     "repository": "CLICKHOUSE",
                     "collectionName": "event_trace",
                     "operateActionEnum": "QUERY",
                     "selectFields": [
                         {
                             "selectField": "length(arrayIntersect(groupUniqArrayArray(arrayFlatten(labelValue.value)),[ $1 ]))",
                             "selectAsField": "distinctCnt"
                         },
                         {
                             "selectField":"apiUrl"
                         }
                     ],
                     "frontCriteriaList": [],
                     "metabaseGroupOperations": [{
                         "groupFields":["apiUrl"]
                     }]
                 },
                 "extraInfo": {
                     "jobType": "AGGTASK",
                     "queryFields": ["distinctCnt","apiUrl"]
                 },
                 "operateRsp": {
                     "key": "uriGroupList"
                 }
             },
                 {
                     "repository": "CLICKHOUSE",
                     "collectionName": "event_trace",
                     "operateActionEnum": "QUERY",
                     "frontCriteriaList": [{
                         "property": "distinctCnt",
                         "predicate": "GT",
                         "value": 0
                     }],
                     "sort": [{"property": "distinctCnt", "order": "DESC"}],
                     "selectFields": [
                         {
                             "selectField": "distinctCnt"
                         },
                         {
                             "selectField":"date"
                         }
                     ],
                     "childCollectionNameOperate": {
                         "repository": "CLICKHOUSE",
                         "collectionName": "event_trace",
                         "operateActionEnum": "QUERY",
                         "selectFields": [
                             {
                                 "selectField": "length(arrayIntersect(groupUniqArrayArray(arrayFlatten(labelValue.value)),[ $1 ]))",
                                 "selectAsField": "distinctCnt"
                             },
                             {
                                 "selectField":"date"
                             }
                         ],
                         "frontCriteriaList": [],
                         "metabaseGroupOperations": [{
                             "groupFields":["date"]
                         }]
                     },
                     "extraInfo": {
                         "jobType": "AGGTASK",
                         "queryFields": ["distinctCnt","date"]
                     },
                     "operateRsp": {
                         "key": "dateGroupList"
                     }
                 },{
                     "repository": "CLICKHOUSE",
                     "collectionName": "event_trace",
                     "operateActionEnum": "QUERY",
                     "frontCriteriaList": [{
                         "property": "distinctCnt",
                         "predicate": "GT",
                         "value": 0
                     }],
                     "sort": [{"property": "distinctCnt", "order": "DESC"}],
                     "selectFields": [
                         {
                             "selectField": "distinctCnt"
                         },
                         {
                             "selectField":"ip"
                         },
                         {
                             "selectField":"accessDomainList"
                         }
                     ],
                     "childCollectionNameOperate": {
                         "repository": "CLICKHOUSE",
                         "collectionName": "event_trace",
                         "operateActionEnum": "QUERY",
                         "selectFields": [
                             {
                                 "selectField": "length(arrayIntersect(groupUniqArrayArray(arrayFlatten(labelValue.value)),[ $1 ]))",
                                 "selectAsField": "distinctCnt"
                             },
                             {
                                 "selectField":"ip"
                             },
                             {
                                 "selectField":"groupUniqArrayArray(accessDomains.id)",
                                 "selectAsField":"accessDomainList"
                             }
                         ],
                         "frontCriteriaList": [],
                         "metabaseGroupOperations": [{
                             "groupFields":["ip"]
                         }]
                     },
                     "extraInfo": {
                         "jobType": "AGGTASK",
                         "queryFields": ["distinctCnt","ip","accessDomainList"]
                     },
                     "operateRsp": {
                         "key": "ipGroupList",
                         "backend2FrontConvertMap":{
                             "accessDomainList":{
                                 "udf":"var a = JSON.parse(originalStr);originalStr = a.join(\",\");",
                                 "udfRspType":"STRING"
                             }
                         }
                     }
                 },{
                     "repository": "CLICKHOUSE",
                     "collectionName": "event_trace",
                     "operateActionEnum": "QUERY",
                     "frontCriteriaList": [{
                         "property": "distinctCnt",
                         "predicate": "GT",
                         "value": 0
                     }],
                     "sort": [{"property": "distinctCnt", "order": "DESC"}],
                     "selectFields": [
                         {
                             "selectField": "distinctCnt"
                         },
                         {
                             "selectField":"account"
                         },
                         {
                             "selectField":"staffDepartList"
                         }
                     ],
                     "childCollectionNameOperate": {
                         "repository": "CLICKHOUSE",
                         "collectionName": "event_trace",
                         "operateActionEnum": "QUERY",
                         "selectFields": [
                             {
                                 "selectField": "length(arrayIntersect(groupUniqArrayArray(arrayFlatten(labelValue.value)),[ $1 ]))",
                                 "selectAsField": "distinctCnt"
                             },
                             {
                                 "selectField":"account"
                             },
                             {
                                 "selectField":"groupUniqArray(staff_depart)",
                                 "selectAsField":"staffDepartList"
                             }
                         ],
                         "frontCriteriaList": [],
                         "metabaseGroupOperations": [{
                             "groupFields":["account"]
                         }]
                     },
                     "extraInfo": {
                         "jobType": "AGGTASK",
                         "queryFields": ["distinctCnt","account","staffDepartList"]
                     },
                     "operateRsp": {
                         "key": "accountGroupList",
                         "backend2FrontConvert":{
                             "udf":"var a = JSON.parse(originalStr);\nvar b = [];\nfor(var i = 0; i < a.length ;i++) {\n    if(a[i][\"account\"] == \"QZ_NULL\" || a[i][\"account\"] == '') {\n        continue;\n    }\n    b.push(a[i]);\n}\noriginalStr = JSON.stringify(b);",
                             "udfRspType":"JSON"
                         }
                     }
                 }],
             "exportFileName":"数据溯源列表.xls",
             "exportTypeName":"导出数据溯源",
             "exportSheetConfig":{
                 "rows":{
                     "exportFields":[
                         {
                             "field":"timestamp",
                             "name":"时间"
                         },
                         {
                             "field":"account",
                             "name":"账号"
                         },
                         {
                             "field":"ip",
                             "name":"IP"
                         },
                         {
                             "field":"accessDomains_id",
                             "name":"IP网段"
                         },
                         {
                             "field":"host",
                             "name":"应用"
                         },
                         {
                             "field":"apiUrl",
                             "name":"API"
                         },
                         {
                             "field":"apiFeatureLabels",
                             "name":"API标签"
                         },
                         {
                             "field":"reqDataLabelIds",
                             "name":"请求数据标签"
                         },
                         {
                              "field":"rspDataLabelIds",
                              "name":"响应数据标签"
                         },
                         {
                             "field":"nodeName",
                             "name":"来源节点"
                         }
                     ],
                     "sheetName":"溯源列表"
                 },
                 "uriGroupList":{
                     "exportFields":[
                         {
                             "field":"apiUrl",
                             "name":"API"
                         },
                         {
                             "field":"distinctCnt",
                             "name":"涉及追查数据"
                         }
                     ],
                     "sheetName":"API关联"
                 },
                 "ipGroupList":{
                     "exportFields":[
                         {
                             "field":"ip",
                             "name":"IP"
                         },
                         {
                             "field":"accessDomainList",
                             "name":"网段"
                         },
                         {
                             "field":"distinctCnt",
                             "name":"涉及追查数据"
                         }
                     ],
                     "sheetName":"IP关联"
                 },
                 "dateGroupList":{
                     "exportFields":[
                         {
                             "field":"date",
                             "name":"日期"
                         },
                         {
                             "field":"distinctCnt",
                             "name":"涉及追查数据"
                         }
                     ],
                     "sheetName":"日期关联"
                 },
                 "accountGroupList":{
                     "exportFields":[
                         {
                             "field":"account",
                             "name":"账号"
                         },
                         {
                             "field":"distinctCnt",
                             "name":"涉及追查数据"
                         }
                     ],
                     "sheetName":"账号关联"
                 }
             },
             "rspKeyMap": {"rows": "rows","uriGroupList":"uriGroupList","ipGroupList":"ipGroupList","dateGroupList":"dateGroupList","accountGroupList":"accountGroupList"}
         },
    {
            "_id": "AUDIT_V2_ENTITY_TRACE_EVENT_LIST_EXPORT",
            "name": "主体溯源导出列表",
            "repositoryOperateList": [{
                "repository": "CLICKHOUSE",
                "collectionName": "event_trace",
                "operateActionEnum": "EXPORT",
                "frontCriteriaList": [],
                "page": 1,
                "limit": 10000,
                "sort": [],
                "selectFields": [
                       {
                            "selectField": "timestamp"
                       },
                       {
                            "selectField": "id"
                       },
                       {
                            "selectField":"account"
                       },
                       {
                            "selectField":"ip"
                       },
                       {
                             "selectField":"accessDomains.id"
                       },
                       {
                             "selectField":"host"
                       },
                       {
                              "selectField":"apiUrl"
                       },
                       {
                              "selectField":"classifications"
                       },
                       {
                              "selectField":"featureLabels"
                       },
                       {
                             "selectField":"reqDataLabelIds"
                       },
                       {
                             "selectField":"rspDataLabelIds"
                       },
                        {
                            "selectField":"nodeId"
                        }

                ],
                "operateRsp": {
                    "key": "rows",
                    "backend2FrontConvertMap":{
                        "timestamp":{
                            "convertClassName":"LocalMethodTransformImpl",
                            "convertMethod":"formatTimestamp",
                            "args":["yyyy-MM-dd HH:mm:ss"]
                        },
                        "account":{
                            "udf":"var a = originalStr;\nif (a == \'QZ_NULL\') { \n    originalStr = \'\' \n} else {\n    originalStr = a;\n}",
                            "udfRspType":"STRING"
                        },
                        "accessDomains":{
                            "udf":"var a = JSON.parse(originalStr);\nvar ids = [];\nfor(var i = 0; i < a.length;i++) {\n    ids.push(a[i][\"id\"])\n}\noriginalStr = ids.join(\",\");",
                            "udfRspType":"STRING",
                            "newField":"accessDomains_id"
                        },
                        "rspDataLabelIds": {
                            "convertClassName": "LocalMethodTransformImpl",
                            "convertMethod": "transformDataLabelId2name",
                            "args": [ ]
                        },
                        "reqDataLabelIds": {
                            "convertClassName": "LocalMethodTransformImpl",
                            "convertMethod": "transformDataLabelId2name",
                            "args": [ ]
                        },
                        "apiFeatureLabels": {
                            "convertClassName": "LocalMethodTransformImpl",
                            "convertMethod": "transformApiFeatureLabelId2name",
                            "args": [ ]
                        }
                    }
                }
            },{
                "repository":"CLICKHOUSE",
                "collectionName":"event_trace",
                "operateActionEnum":"QUERY",
                "frontCriteriaList":[{
                    "property":"taskId",
                    "predicate":"IS",
                    "value":"aaa"
                }],
                "selectFields":[
                    {
                        "selectField":"ip"
                    },
                    {
                        "selectField":"COUNT(*) AS COUNT"
                    },
                    {
                        "selectField":"groupUniqArrayArray(accessDomains.id)",
                        "selectAsField":"accessDomainList"
                    }
                ],
                "sort":[{"property":"COUNT","order":"DESC"}],
                "metabaseGroupOperations":[{
                    "groupFields":["ip"]
                }],
                "extraInfo":{
                    "jobType":"AGGTASK",
                    "queryFields":["ip","COUNT","accessDomainList"]
                },
                "operateRsp":{
                    "key":"ipGroupList",
                    "backend2FrontConvertMap":{
                        "accessDomainList":{
                            "udf":"var a = JSON.parse(originalStr);originalStr = a.join(\",\");",
                            "udfRspType":"STRING"
                        }
                    }
                }
            },{
                "repository":"CLICKHOUSE",
                "collectionName":"event_trace",
                "operateActionEnum":"QUERY",
                "frontCriteriaList":[{
                    "property":"taskId",
                    "predicate":"IS",
                    "value":"aaa"
                }],
                "selectFields":[
                    {
                        "selectField":"apiUrl"
                    },
                    {
                        "selectField":"COUNT(*) AS COUNT"
                    }
                ],
                "sort":[{"property":"COUNT","order":"DESC"}],
                "metabaseGroupOperations":[{
                    "groupFields":["apiUrl"]
                }],
                "extraInfo":{
                    "jobType":"AGGTASK",
                    "queryFields":["apiUrl","COUNT"]
                },
                "operateRsp":{
                    "key":"uriGroupList"
                }
            },{
                "repository":"CLICKHOUSE",
                "collectionName":"event_trace",
                "operateActionEnum":"QUERY",
                "frontCriteriaList":[{
                    "property":"taskId",
                    "predicate":"IS",
                    "value":"aaa"
                }],
                "selectFields":[
                    {
                        "selectField":"account"
                    },
                    {
                        "selectField":"COUNT(*) AS COUNT"
                    }
                ],
                "sort":[{"property":"COUNT","order":"DESC"}],
                "metabaseGroupOperations":[{
                    "groupFields":["account"]
                }],
                "extraInfo":{
                    "jobType":"AGGTASK",
                    "queryFields":["account","COUNT"]
                },
                "operateRsp":{
                    "key":"accountGroupList",
                    "backend2FrontConvert":{
                        "udf":"var a = JSON.parse(originalStr);\nvar b = [];\nfor(var i = 0; i < a.length ;i++) {\n    if(a[i][\"account\"] == \"QZ_NULL\" || a[i][\"account\"] == '') {\n        continue;\n    }\n    b.push(a[i]);\n}\noriginalStr = JSON.stringify(b);",
                        "udfRspType":"JSON"
                    }
                }
            }],
            "exportFileName":"主体溯源列表.xls",
            "exportTypeName":"导出主体溯源",
            "exportSheetConfig":{
                "rows":{
                    "exportFields":[
                        {
                            "field":"timestamp",
                            "name":"时间"
                        },
                        {
                            "field":"account",
                            "name":"账号"
                        },
                        {
                            "field":"ip",
                            "name":"IP"
                        },
                        {
                            "field":"accessDomains_id",
                            "name":"IP网段"
                        },
                        {
                            "field":"host",
                            "name":"应用"
                        },
                        {
                            "field":"apiUrl",
                            "name":"API"
                        },
                        {
                             "field":"apiFeatureLabels",
                             "name":"API标签"
                        },
                        {
                             "field":"reqDataLabelIds",
                             "name":"请求数据标签"
                        },
                        {
                             "field":"rspDataLabelIds",
                             "name":"响应数据标签"
                        },
                        {
                            "field":"nodeName",
                            "name":"来源节点"
                        }
                    ],
                    "sheetName":"溯源列表"
                },
                "uriGroupList":{
                    "exportFields":[
                        {
                            "field":"apiUrl",
                            "name":"API"
                        },
                        {
                            "field":"COUNT",
                            "name":"日志数"
                        }
                    ],
                    "sheetName":"API关联"
                },
                "ipGroupList":{
                    "exportFields":[
                        {
                            "field":"ip",
                            "name":"IP"
                        },
                        {
                            "field":"accessDomainList",
                            "name":"网段"
                        },
                        {
                            "field":"COUNT",
                            "name":"日志数"
                        }
                    ],
                    "sheetName":"IP关联"
                },
                "accountGroupList":{
                    "exportFields":[
                        {
                            "field":"account",
                            "name":"账号"
                        },
                        {
                            "field":"COUNT",
                            "name":"日志数"
                        }
                    ],
                    "sheetName":"账号关联"
                }
            },
            "rspKeyMap": {"rows": "rows","uriGroupList":"uriGroupList","ipGroupList":"ipGroupList","accountGroupList":"accountGroupList"}
        },
    {
        "_id": "API_ADVANNCE_1.3_GET_RECOVER_CONFIG_LIST",
        "name": "获取系统清理配置",
        "repositoryOperateList": [{
            "repository": "NACOS",
            "collectionName": "recoverConfig",
            "operateActionEnum": "QUERY",
            "id": "",
            "frontCriteriaList": [],
            "operateRsp": {
                "key": "list"
            }
        }],
        "rspKeyMap": {"list": "list"}
    },
    {
        "_id":"AUDIT_V2_FILE_COUNT",
        "name":"文件总数/涉敏文件数",
        "repositoryOperateList":[
            {
                "repository":"MONGO",
                "collectionName":"fileInfo",
                "operateActionEnum":"COUNT",
                "frontCriteriaList":[

                ],
                "operateRsp":{
                    "key":"totalCount"
                }
            },
            {
                "repository":"MONGO",
                "collectionName":"fileInfo",
                "operateActionEnum":"COUNT",
                "frontCriteriaList":[
                    {
                        "property":"dataDistinctCnt",
                        "predicate":"GT",
                        "value": 0
                    }
                ],
                "operateRsp":{
                    "key":"sensitiveCount"
                }
            }
        ],
        "rspKeyMap":{
            "totalCount":"totalCount",
            "sensitiveCount":"sensitiveCount"
        }
    },
    {
        "_id":"FILE_INFO_FILETYPE_GROUP_LIST",
        "name":"文件格式分组/下拉框",
        "repositoryOperateList":[
            {
                "repository":"MONGO",
                "collectionName":"fileInfo",
                "operateActionEnum":"QUERY",
                "frontCriteriaList":[
                ],
                "metabaseGroupOperations":[
                    {
                        "groupFields":[
                            "fileType"
                        ]
                    }
                ],
                "operateRsp":{
                    "key":"groupList",
                    "backend2FrontConvert": {
                        "udf": "var a = JSON.parse(originalStr);\nvar b=[];\nfor(var i = 0;i < a.length;i++) {\n    if(a[i]['_id'] && a[i]['_id'].length > 0){\n        b.push({\n            \"_id\": a[i][\"_id\"],\n            \"label\" : a[i][\"_id\"],\n            \"name\" : a[i][\"_id\"]\n        });\n    }\n}\noriginalStr = JSON.stringify(b)",
                        "udfRspType":"JSON"
                    }
                }
            }
        ],
        "rspKeyMap":{
            "groupList":"groupList"
        }
    },
    {
        "_id":"FILE_GROUP_INFO",
        "name":"文件ES事件聚合",
        "repositoryOperateList":[{
            "repository":"CLICKHOUSE",
            "collectionName":"http_defined_event",
            "operateActionEnum":"QUERY",
            "frontCriteriaList":[{
                "property":"file_fileDirection",
                "predicate":"IS",
                "value":"UPLOAD"
            },{
                "property":"eventDefineIds",
                "predicate":"HAS_CLICK_HOUSE",
                "value":"file_event"
            }],
            "distinctFieldConvertList":[{
                "distinctField":"ip",
                "distinctAsField":"ipDistinctCnt"
            }],
            "extraInfo":{
                "jobType":"AGGTASK",
                "queryFields":["file_sha256","ipDistinctCnt"]
            },
            "operateRsp":{
                "key":"uploadIpDistinctCnt",
                "backend2FrontConvert":{
                    "udf": "var a = JSON.parse(originalStr);\nvar b = 0; \nif(a && a.length > 0) { \n   \n        if(a[0].hasOwnProperty(\"ipDistinctCnt\")) {\n            b  = a[0][\"ipDistinctCnt\"]\n        }\n        \n} \noriginalStr = b.toString();",
                    "udfRspType":"NUMBER"
                }
            }
        },{
            "repository":"CLICKHOUSE",
            "collectionName":"http_defined_event",
            "operateActionEnum":"QUERY",
            "frontCriteriaList":[{
                "property":"account",
                "predicate":"NE",
                "value":"QZ_NULL"
            },{
                "property":"file_fileDirection",
                "predicate":"IS",
                "value":"UPLOAD"
            },{
                "property":"eventDefineIds",
                "predicate":"HAS_CLICK_HOUSE",
                "value":"file_event"
            }],
            "distinctFieldConvertList":[{
                "distinctField":"account",
                "distinctAsField":"accountDistinctCnt"
            }],
            "extraInfo":{
                "jobType":"AGGTASK",
                "queryFields":["file_sha256","accountDistinctCnt"]
            },
            "operateRsp":{
                "key":"uploadAccountDistinctCnt",
                "backend2FrontConvert":{
                    "udf": "var a = JSON.parse(originalStr);\nvar b = 0; \nif(a && a.length > 0) {\n        if(a[0].hasOwnProperty(\"accountDistinctCnt\")) {\n            b = a[0][\"accountDistinctCnt\"]\n        }\n} \noriginalStr = b.toString();",
                    "udfRspType":"NUMBER"
                }
            }
        },{
            "repository":"CLICKHOUSE",
            "collectionName":"http_defined_event",
            "operateActionEnum":"QUERY",
            "frontCriteriaList":[{
                "property":"file_fileDirection",
                "predicate":"IS",
                "value":"DOWNLOAD"
            },{
                "property":"eventDefineIds",
                "predicate":"HAS_CLICK_HOUSE",
                "value":"file_event"
            }],
            "distinctFieldConvertList":[{
                "distinctField":"ip",
                "distinctAsField":"ipDistinctCnt"
            }],
            "extraInfo":{
                "jobType":"AGGTASK",
                "queryFields":["file_sha256","ipDistinctCnt"]
            },
            "operateRsp":{
                "key":"downloadIpDistinctCnt",
                "backend2FrontConvert":{
                    "udf": "var a = JSON.parse(originalStr);\nvar b = 0; \nif(a && a.length > 0) { \n    \n        if(a[0].hasOwnProperty(\"ipDistinctCnt\")) {\n            b  = a[0][\"ipDistinctCnt\"]\n        }\n} \noriginalStr = b.toString();",
                    "udfRspType":"NUMBER"
                }
            }
        },{
            "repository":"CLICKHOUSE",
            "collectionName":"http_defined_event",
            "operateActionEnum":"QUERY",
            "frontCriteriaList":[{
                "property":"account",
                "predicate":"NE",
                "value":"QZ_NULL"
            },{
                "property":"file_fileDirection",
                "predicate":"IS",
                "value":"DOWNLOAD"
            },{
                "property":"eventDefineIds",
                "predicate":"HAS_CLICK_HOUSE",
                "value":"file_event"
            }],
            "distinctFieldConvertList":[{
                "distinctField":"account",
                "distinctAsField":"accountDistinctCnt"
            }],
            "extraInfo":{
                "jobType":"AGGTASK",
                "queryFields":["file_sha256","accountDistinctCnt"]
            },
            "operateRsp":{
                "key":"downloadAccountDistinctCnt",
                "backend2FrontConvert":{
                    "udf": "var a = JSON.parse(originalStr);\nvar b = 0; \nif(a && a.length > 0) {\n        if(a[0].hasOwnProperty(\"accountDistinctCnt\")) {\n            b = a[0][\"accountDistinctCnt\"]\n        }\n} \noriginalStr = b.toString()",
                    "udfRspType":"NUMBER"
                }
            }
        }],
        "rspKeyMap":{
            "uploadIpDistinctCnt":"uploadIpDistinctCnt",
            "uploadAccountDistinctCnt":"uploadAccountDistinctCnt",
            "downloadIpDistinctCnt":"downloadIpDistinctCnt",
            "downloadAccountDistinctCnt":"downloadAccountDistinctCnt"
        }
    },

    {
        "_id": "API_ADVANCE_1.3_GET_AUDIT_ENUM",
        "name": "获取某类型的枚举值",
        "repositoryOperateList": [{
            "repository": "NACOS",
            "collectionName": "auditapiv2Enum",
            "operateActionEnum": "FINDONE",
            "frontCriteriaList": [],
            "operateRsp": {
                "key": "list",
                "backend2FrontConvert": {
                    "udf": "var a = JSON.parse(originalStr); originalStr = JSON.stringify( a['data'] )",
                    "udfRspType": "JSON"
                }
            }
        }],
        "rspKeyMap": {"list": "list"}
    },

    {
        "_id":"API_ADVANCE_1.3_RISK_POLICY_EDIT",
        "name":"修改风险规则",
        "repositoryOperateList":[{
            "repository":"MONGO",
            "collectionName":"riskPolicy",
            "operateActionEnum":"UPDATE",
            "frontCriteriaList":[{
                "property":"id",
                "predicate":"IS",
                "value":"1"
            }],
            "updateOperateList":[],
            "operateRsp":{
                "key":"upateRiskPolicyState"
            }
        }],
        "rspKeyMap":{"upateRiskPolicyState":"upateRiskPolicyState"}
    },

    {
        "_id":"API_ADVANCE_1.3_GET_RISK_RULE_QUOTA",
        "name":"获取异常指标",
        "repositoryOperateList":[{
            "repository":"NACOS",
            "collectionName":"auditapiv2RiskRuleQuota",
            "operateActionEnum":"QUERY",
            "frontCriteriaList":[],
            "operateRsp":{
                "key":"riskRuleQuota"
            }
        }],
        "rspKeyMap":{"riskRuleQuota":"riskRuleQuota"}
    },

    {
        "_id":"API_ADVANCE_1.3_HOME_TOP_DATA",
        "name":"首页获取统计数据",
        "repositoryOperateList":[{
            "repository":"MONGO",
            "collectionName":"httpApp",
            "operateActionEnum":"COUNT",
            "frontCriteriaList":[
                {
                    "property":"delFlag",
                    "predicate":"IS",
                    "value":false
                }
            ],
            "operateRsp":{
                "key":"totalAppCnt"
            }
        },{
            "repository":"MONGO",
            "collectionName":"httpApp",
            "operateActionEnum":"COUNT",
            "frontCriteriaList":[
                {
                    "property":"storeMonitoring",
                    "predicate":"IS",
                    "value":true
                },
                {
                    "property":"delFlag",
                    "predicate":"IS",
                    "value":false
                }
            ],
            "operateRsp":{
                "key":"totalMonitorAppCnt"
            }
        },{
            "repository":"MONGO",
            "collectionName":"httpApi",
            "operateActionEnum":"COUNT",
            "frontCriteriaList":[
                {
                    "property":"delFlag",
                    "predicate":"IS",
                    "value":false
                }
            ],
            "operateRsp":{
                "key":"totalApiCnt"
            }
        },{
            "repository":"MONGO",
            "collectionName":"httpApi",
            "operateActionEnum":"COUNT",
            "frontCriteriaList":[
                {
                    "property":"delFlag",
                    "predicate":"IS",
                    "value":false
                },
                {
                    "property":"storeMonitor.monitorFlag",
                    "predicate":"IS",
                    "value":1
                },
            ],
            "operateRsp":{
                "key":"totalMonitorApiCnt"
            }
        }, {
            "repository": "MONGO",
            "collectionName": "accountInfo",
            "operateActionEnum": "COUNT",
            "frontCriteriaList": [
                {
                    "property":"date",
                    "predicate":"IS",
                    "value":"********"
                }
            ],
            "operateRsp": {
                "key": "accountCnt"
            }
        },
            {
                "repository": "MONGO",
                "collectionName": "ipInfo",
                "operateActionEnum": "COUNT",
                "frontCriteriaList": [
                    {
                        "property":"date",
                        "predicate":"IS",
                        "value":"********"
                    }
                ],
                "operateRsp": {
                    "key": "ipCnt"
                }
            },{
                "repository": "MONGO",
                "collectionName": "fileInfo",
                "operateActionEnum": "COUNT",
                "frontCriteriaList": [],
                "operateRsp": {
                    "key": "fileCnt"
                }
            }],
        "rspKeyMap":{"totalAppCnt":"totalAppCnt",
            "totalMonitorAppCnt":"totalMonitorAppCnt",
            "totalApiCnt":"totalApiCnt",
            "totalMonitorApiCnt":"totalMonitorApiCnt",
            "accountCnt":"accountCnt",
            "ipCnt":"ipCnt",
            "fileCnt":"fileCnt"
        }
    },

    {
        "_id":"API_ADVANCE_1.3_HOME_WEAKNESS_AND_EXCEPTION_DATA",
        "name":"风险和弱点的统计信息",
        "repositoryOperateList":[{
            "repository":"MONGO",
            "collectionName":"httpApp",
            "operateActionEnum":"COUNT",
            "frontCriteriaList":[
                {
                    "property":"delFlag",
                    "predicate":"IS",
                    "value":false
                }
            ],
            "operateRsp":{
                "key":"totalAppCnt"
            }
        },{
            "repository":"MONGO",
            "collectionName":"httpApi",
            "operateActionEnum":"COUNT",
            "frontCriteriaList":[
                {
                    "property":"delFlag",
                    "predicate":"IS",
                    "value":false
                }
            ],
            "operateRsp":{
                "key":"totalApiCnt"
            }
        },{
            "repository":"MONGO",
            "collectionName":"httpApiWeakness",
            "operateActionEnum":"QUERY",
            "frontCriteriaList":[

            ],
            "metabaseGroupOperations":[{
                "groupFields":["appUri"]
            }],
            "distinctField":"appWeakCnt",
            "operateRsp":{
                "key":"appWeakCnt",
                "backend2FrontConvert":{
                    "udf":"var a = JSON.parse(originalStr); originalStr = a[0]['appWeakCnt'].toString()",
                    "udfRspType":"NUMBER"
                }
            }
        },{
            "repository":"MONGO",
            "collectionName":"httpApiWeakness",
            "operateActionEnum":"QUERY",
            "frontCriteriaList":[],
            "metabaseGroupOperations":[{
                "groupFields":["uri"]
            }],
            "distinctField":"apiWeakCnt",
            "operateRsp":{
                "key":"apiWeakCnt",
                "backend2FrontConvert":{
                    "udf":"var a = JSON.parse(originalStr); originalStr = a[0]['apiWeakCnt'].toString()",
                    "udfRspType":"NUMBER"
                }
            }
        },{
            "repository":"MONGO",
            "collectionName":"riskInfoAgg",
            "operateActionEnum":"QUERY",
            "frontCriteriaList":[
                {
                    "property":"entities.type",
                    "predicate":"IS",
                    "value":"IP"
                }
            ],
            "unwindField":"entities",
            "metabaseGroupOperations":[{
                "groupFields":["entities.value"]
            }],
            "distinctField":"ipExceptionCnt",
            "operateRsp":{
                "key":"ipExceptionCnt",
                "backend2FrontConvert":{
                    "udf":"var a = JSON.parse(originalStr); originalStr = a[0]['ipExceptionCnt'].toString()",
                    "udfRspType":"NUMBER"
                }

            }
        },{
            "repository": "MONGO",
            "collectionName": "accountInfo",
            "operateActionEnum": "COUNT",
            "frontCriteriaList": [
                {
                    "property":"date",
                    "predicate":"IS",
                    "value":"********"
                }
            ],
            "operateRsp": {
                "key": "accountCnt"
            }
        },{
            "repository": "MONGO",
            "collectionName": "ipInfo",
            "operateActionEnum": "COUNT",
            "frontCriteriaList": [
                {
                    "property":"date",
                    "predicate":"IS",
                    "value":"********"
                }
            ],
            "operateRsp": {
                "key": "ipCnt"
            }
        },{
            "repository":"MONGO",
            "collectionName":"riskInfoAgg",
            "operateActionEnum":"QUERY",
            "frontCriteriaList":[
                {
                    "property":"entities.type",
                    "predicate":"IS",
                    "value":"ACCOUNT"
                }
            ],
            "unwindField":"entities",
            "metabaseGroupOperations":[{
                "groupFields":["entities.value"]
            }],
            "distinctField":"accountExceptionCnt",
            "operateRsp":{
                "key":"accountExceptionCnt",
                "backend2FrontConvert":{
                    "udf":"var a = JSON.parse(originalStr); originalStr = a[0]['accountExceptionCnt'].toString()",
                    "udfRspType":"NUMBER"
                }

            }
        }],
        "rspKeyMap":{
            "appWeakCnt":"appWeakCnt",
            "apiWeakCnt":"apiWeakCnt",
            "ipExceptionCnt":"ipExceptionCnt",
            "accountExceptionCnt":"accountExceptionCnt",
            "ipTotalCnt":"ipCnt",
            "totalAppCnt":"totalAppCnt",
            "totalApiCnt":"totalApiCnt",
            "accountTotalCnt":"accountCnt"
        },
        "frontObjUdf": {
            "accountTotalCnt": {
                "backendKeys":["accountCnt","accountExceptionCnt"],
                "udfRspType":"NUMBER",
                "udf": "var a = JSON.parse(originalStr);\nvar accountCnt = a[\'accountCnt\'];\nvar accountExceptionCnt = a[\'accountExceptionCnt\'];\nif( accountCnt == 0 || accountCnt < accountExceptionCnt) {\n    accountCnt = accountExceptionCnt;\n}\noriginnalStr = accountCnt.toString();\n"
            },
            "ipTotalCnt": {
                "backendKeys":["ipCnt","ipExceptionCnt"],
                "udfRspType":"NUMBER",
                "udf": "var a = JSON.parse(originalStr);\nvar ipCnt = a[\"ipCnt\"];\nvar ipExceptionCnt = a[\"ipExceptionCnt\"];\nif( ipCnt == 0 || ipCnt < ipExceptionCnt) {\n    ipCnt = ipExceptionCnt;\n}\noriginnalStr = ipCnt.toString();\n"
            }
        }
    },

    {
        "_id":"API_ADVANCE_1.3_HOME_WEAKNESS_CHART_DATA",
        "name":"首页弱点图表信息",
        "localMethodOperateList":[{
            "methodClassName":"WeaknessHandlerImpl",
            "methodName":"getWeaknessDateGroupList",
            "args":["********","********"],
            "operateRsp":{
                "key":"weaknessDateCntMap"
            }
        },{
            "methodClassName":"WeaknessHandlerImpl",
            "methodName":"getWeaknessTotalCnt",
            "args":["********","********"],
            "operateRsp":{
                "key":"weaknessTotalCnt"
            }
        }],
        "rspKeyMap":{
            "weaknessTotalCnt":"weaknessTotalCnt",
            "weaknessDateCntMap":"weaknessDateCntMap"
        }
    },

    {
        "_id": "API_ADVANCE_1.3_HOME_EXCEPTION_CHART_DATA",
        "name": "首页风险图表信息",
        "localMethodOperateList": [{
            "methodClassName": "LocalMethodTransformImpl",
            "methodName": "getDateList",
            "args": ["********", "********"],
            "operateRsp": {
                "key": "timeList"
            }
        }],
        "repositoryOperateList": [{
            "repository": "MONGO",
            "collectionName": "riskInfoAgg",
            "operateActionEnum": "QUERY",
            "frontCriteriaList": [
                {
                    "property": "date",
                    "predicate": "BETWEEN",
                    "value": ["********", "********"]
                }
            ],
            "metabaseGroupOperations": [{
                "groupFields": ["date"],
                "aggregateOptionList": [{
                    "aggrType": "COUNT",
                    "aggrField": "COUNT"
                }]
            }],
            "operateRsp": {
                "key": "exceptionGroupList"
            }
        }, {
            "repository": "MONGO",
            "collectionName": "riskInfoAgg",
            "operateActionEnum": "COUNT",
            "frontCriteriaList": [
                {
                    "property": "date",
                    "predicate": "BETWEEN",
                    "value": ["********", "********"]
                }
            ],
            "operateRsp": {
                "key": "exceptionTotalCnt"
            }
        }],
        "rspKeyMap": {
            "exceptionGroupMap": "exceptionGroupList",
            "exceptionTotalCnt": "exceptionTotalCnt"
        },
        "frontObjUdf": {
            "exceptionGroupMap": {
                "backendKeys": ["timeList", "exceptionGroupList"],
                "udfRspType": "JSON",
                "udf": "var a = JSON.parse(originalStr);\nvar b = a[\"timeList\"];\nvar exceptionGroupList = a[\"exceptionGroupList\"];\nvar c = {};\n\nfor(var i = 0 ;i < b.length; i++) {\n    c[b[i]] = 0;\n}\nfor(var j = 0; j < exceptionGroupList.length;j++) {\n    \n    c[exceptionGroupList[j][\"_id\"]] = exceptionGroupList[j][\"COUNT\"]\n}\noriginalStr = JSON.stringify(c);\n"
            }
        }
    },

    {
        "_id":"API_ADVANCE_1.4_RISK_IP",
        "name":"异常IP",
        "repositoryOperateList":[{
            "repository":"MONGO",
            "collectionName":"ipInfo",
            "operateActionEnum":"COUNT",
            "frontCriteriaList":[{
                "property":"date",
                "predicate":"IS",
                "value":""
            },{
                "property":"abnCnt",
                "predicate":"GT",
                "value":0
            }],
            "operateRsp":{
                "key":"totalCount"
            }
        },{
            "repository":"MONGO",
            "collectionName":"ipInfo",
            "operateActionEnum":"COUNT",
            "frontCriteriaList":[{
                "property":"date",
                "predicate":"IS",
                "value":""
            },{
                "property":"firstDate",
                "predicate":"IS",
                "value":""
            },{
                "property":"abnCnt",
                "predicate":"GT",
                "value":0
            }],
            "operateRsp":{
                "key":"todayTotalCount"
            }
        }],
        "rspKeyMap":{"totalCount":"totalCount","todayTotalCount":"todayTotalCount"}
    },

    {
        "_id":"API_ADVANCE_1.4_LONIG_ACCOUNT",
        "name":"风险账号",
        "repositoryOperateList":[{
            "repository":"MONGO",
            "collectionName":"apiAccountInfo",
            "operateActionEnum":"QUERY",
            "frontCriteriaList":[
            ],
            "metabaseGroupOperations":[
                {
                    "groupFields":[
                        "account"
                    ]
                }
            ],
            "distinctField":"totalCount",
            "operateRsp":{
                "key":"totalCount",
                "backend2FrontConvert":{
                    "udf":"var a = JSON.parse(originalStr) ; originalStr = a[0]['totalCount'].toString()",
                    "udfRspType":"JSON"
                }
            }
        },{
            "repository":"MONGO",
            "collectionName":"apiAccountInfo",
            "operateActionEnum":"QUERY",
            "frontCriteriaList":[
                {
                    "property":"firstDate",
                    "predicate":"IS",
                    "value":""
                }
            ],
            "metabaseGroupOperations":[
                {
                    "groupFields":[
                        "account"
                    ]
                }
            ],
            "distinctField":"totalCount",
            "operateRsp":{
                "key":"todayTotalCount",
                "backend2FrontConvert":{
                    "udf":"var a = JSON.parse(originalStr) ; originalStr = a[0]['totalCount'].toString()",
                    "udfRspType":"JSON"
                }
            }
        }],
        "rspKeyMap":{"totalCount":"totalCount","todayTotalCount":"todayTotalCount"}
    },

    {
        "_id":"API_ADVANCE_1.4_RISK_EVENT",
        "name":"异常事件",
        "repositoryOperateList":[{
            "repository":"MONGO",
            "collectionName":"riskInfoAgg",
            "operateActionEnum":"QUERY",
            "frontCriteriaList":[{
                "property":"state",
                "predicate":"IN",
                "value":[NumberInt(0),NumberInt(2),NumberInt(3)]
            }],
            "page":NumberInt(1),
            "limit":NumberInt(5),
            "sort":[{"property":"createTime","order":"DESC"}],
            "operateRsp":{
                "key":"list"
            }
        }],
        "rspKeyMap":{"list":"list"}
    },

    {
        "_id":"API_ADVANCE_1.4_APP_LEVEL_GROUP_LIST",
        "name":"应用等级分组",
        "repositoryOperateList":[{
            "repository":"MONGO",
            "collectionName":"httpApp",
            "operateActionEnum":"QUERY",
            "frontCriteriaList":[{
                "property":"delFlag",
                "predicate":"IS",
                "value":false
            }],
            "metabaseGroupOperations":[{
                "groupFields":["level"],
                "aggregateOptionList":[{
                    "aggrType":"COUNT",
                    "aggrField":"count"
                }]
            }],
            "sort":[{"property":"count","order":"DESC"}],
            "operateRsp":{
                "key":"groupList",
                "backend2FrontConvert":{
                    "udf":"var a = JSON.parse(originalStr);for(var i = 0; i < a.length;i++){ a[i]['name'] = a[i]['_id']; } ;originalStr = JSON.stringify(a)",
                    "udfRspType":"JSON"
                }
            }
        }],
        "rspKeyMap":{"groupList":"groupList"}
    },

    {
        "_id":"API_ADVANCE_1.4_API_LEVEL_GROUP_LIST",
        "name":"API等级分组",
        "repositoryOperateList":[{
            "repository":"MONGO",
            "collectionName":"httpApi",
            "operateActionEnum":"QUERY",
            "frontCriteriaList":[{
                "property":"delFlag",
                "predicate":"IS",
                "value":false
            }],
            "metabaseGroupOperations":[{
                "groupFields":["level"],
                "aggregateOptionList":[{
                    "aggrType":"COUNT",
                    "aggrField":"count"
                }]
            }],
            "sort":[{"property":"count","order":"DESC"}],
            "operateRsp":{
                "key":"groupList",
                "backend2FrontConvert":{
                    "udf":"var a = JSON.parse(originalStr);for(var i = 0; i < a.length;i++){ a[i]['name'] = a[i]['_id']; } ;originalStr = JSON.stringify(a)",
                    "udfRspType":"JSON"
                }
            }
        }],
        "rspKeyMap":{"groupList":"groupList"}
    },

    {
        "_id":"API_ADVANCE_1.4_IP_REQLENGTH_TENDENCY_CHART",
        "name": "API高级版IP画像每日请求长度趋势",
        "localMethodOperateList": [{
            "methodClassName": "LocalMethodTransformImpl",
            "methodName": "getTimeRangeList",
            "args": [1616919522451, 1619511522451, "DATE"],
            "operateRsp": {
                "key": "timeList"
            }
        }],
        "repositoryOperateList": [{
            "repository": "MONGO",
            "collectionName": "ipDateInfo",
            "operateActionEnum": "QUERY",
            "frontCriteriaList": [{
                "property": "ip",
                "predicate": "IS",
                "value": "*************"
            }, {
                "property": "date",
                "predicate": "BETWEEN",
                "value": ["20210328", "20210427"]
            }],
            "operateRsp": {
                "key": "reqBodyLengthCntList"
            },
            "fields": [ "date", "reqBodyLengthCnt"]
        }],
        "rspKeyMap": {
            "axisDate": "timeList",
            "yxisDate": "reqBodyLengthCntList"
        },
        "frontObjUdf": {
            "yxisDate": {
                "backendKeys": ["timeList", "reqBodyLengthCntList"],
                "udf": "try { var originalObj = JSON.parse(originalStr); var timeList = originalObj['timeList']; var reqBodyLengthCntList =originalObj['reqBodyLengthCntList'];var reqBodyLengthCntMap={};for(var i=0;i<reqBodyLengthCntList.length;i++){reqBodyLengthCntMap[reqBodyLengthCntList[i]['date']] = reqBodyLengthCntList[i]['reqBodyLengthCnt'];} var reqBodyLengthCntArr=[];for(var i=0;i<timeList.length;i++){reqBodyLengthCntArr.push(reqBodyLengthCntMap[timeList[i]]?reqBodyLengthCntMap[timeList[i]]:0)}; originalStr = JSON.stringify(reqBodyLengthCntArr) ;} catch (error){}"
            }
        }
    },

    {
        "_id":"API_ADVANCE_1.4_IP_RSPLENGTH_TENDENCY_CHART",
        "name": "API高级版IP画像每日响应长度趋势",
        "localMethodOperateList": [{
            "methodClassName": "LocalMethodTransformImpl",
            "methodName": "getTimeRangeList",
            "args": [1616919522451, 1619511522451, "DATE"],
            "operateRsp": {
                "key": "timeList"
            }
        }],
        "repositoryOperateList": [{
            "repository": "MONGO",
            "collectionName": "ipDateInfo",
            "operateActionEnum": "QUERY",
            "frontCriteriaList": [{
                "property": "ip",
                "predicate": "IS",
                "value": "*************"
            }, {
                "property": "date",
                "predicate": "BETWEEN",
                "value": ["20210328", "20210427"]
            }],
            "operateRsp": {
                "key": "rspBodyLengthCntList"
            },
            "fields": [ "date", "rspBodyLengthCnt"]
        }],
        "rspKeyMap": {
            "axisDate": "timeList",
            "yxisDate": "rspBodyLengthCntList"
        },
        "frontObjUdf": {
            "yxisDate": {
                "backendKeys": ["timeList", "rspBodyLengthCntList"],
                "udf": "try { var originalObj = JSON.parse(originalStr); var timeList = originalObj['timeList']; var rspBodyLengthCntList =originalObj['rspBodyLengthCntList'];var rspBodyLengthCntMap={};for(var i=0;i<rspBodyLengthCntList.length;i++){rspBodyLengthCntMap[rspBodyLengthCntList[i]['date']] = rspBodyLengthCntList[i]['rspBodyLengthCnt'];} var rspBodyLengthCntArr=[];for(var i=0;i<timeList.length;i++){rspBodyLengthCntArr.push(rspBodyLengthCntMap[timeList[i]]?rspBodyLengthCntMap[timeList[i]]:0)}; originalStr = JSON.stringify(rspBodyLengthCntArr) ;} catch (error){}"
            }
        }
    },

    {
        "_id":"API_ADVANCE_1.4_API_VISITS_TENDENCY_CHART",
        "name": "API高级版API画像每日访问次数趋势",
        "localMethodOperateList": [{
            "methodClassName": "LocalMethodTransformImpl",
            "methodName": "getTimeRangeList",
            "args": [1616919522451, 1619511522451, "DATE"],
            "operateRsp": {
                "key": "timeList"
            }
        }],
        "repositoryOperateList": [{
            "repository": "MONGO",
            "collectionName": "httpApiDateStat",
            "operateActionEnum": "QUERY",
            "frontCriteriaList": [{
                "property": "uri",
                "predicate": "IS",
                "value": "httpapi:http://home.51cto.com/index"
            }, {
                "property": "date",
                "predicate": "BETWEEN",
                "value": ["20210328", "20210427"]
            }],
            "operateRsp": {
                "key": "dailyAmountList"
            },
            "fields": ["date", "dailyAmount"]
        }],
        "rspKeyMap": {
            "axisDate": "timeList",
            "yxisDate": "dailyAmountList"
        },
        "frontObjUdf": {
            "yxisDate": {
                "backendKeys": ["timeList", "dailyAmountList"],
                "udf": "try { var originalObj = JSON.parse(originalStr); var timeList = originalObj['timeList']; var dailyAmountList =originalObj['dailyAmountList'];var dailyAmountMap={};for(var i=0;i<dailyAmountList.length;i++){dailyAmountMap[dailyAmountList[i]['date']] = dailyAmountList[i]['dailyAmount'];} var dailyAmountArr=[];for(var i=0;i<timeList.length;i++){dailyAmountArr.push(dailyAmountMap[timeList[i]]?dailyAmountMap[timeList[i]]:0)}; originalStr = JSON.stringify(dailyAmountArr) ;} catch (error){}"
            }
        }
    },

    {
        "_id":"API_ADVANCE_1.4_API_REQLENGTH_TENDENCY_CHART",
        "name": "API高级版API画像每日请求长度趋势",
        "localMethodOperateList": [{
            "methodClassName": "LocalMethodTransformImpl",
            "methodName": "getTimeRangeList",
            "args": [1616919522451, 1619511522451, "DATE"],
            "operateRsp": {
                "key": "timeList"
            }
        }],
        "repositoryOperateList": [{
            "repository": "MONGO",
            "collectionName": "httpApiDateStat",
            "operateActionEnum": "QUERY",
            "frontCriteriaList": [{
                "property": "uri",
                "predicate": "IS",
                "value": "httpapi:http://home.51cto.com/index"
            }, {
                "property": "date",
                "predicate": "BETWEEN",
                "value": ["20210328", "20210427"]
            }],
            "operateRsp": {
                "key": "reqBodyLengthCntList"
            },
            "fields": [ "date", "reqBodyLengthCnt"]
        }],
        "rspKeyMap": {
            "axisDate": "timeList",
            "yxisDate": "reqBodyLengthCntList"
        },
        "frontObjUdf": {
            "yxisDate": {
                "backendKeys": ["timeList", "reqBodyLengthCntList"],
                "udf": "try { var originalObj = JSON.parse(originalStr); var timeList = originalObj['timeList']; var reqBodyLengthCntList =originalObj['reqBodyLengthCntList'];var reqBodyLengthCntMap={};for(var i=0;i<reqBodyLengthCntList.length;i++){reqBodyLengthCntMap[reqBodyLengthCntList[i]['date']] = reqBodyLengthCntList[i]['reqBodyLengthCnt'];} var reqBodyLengthCntArr=[];for(var i=0;i<timeList.length;i++){reqBodyLengthCntArr.push(reqBodyLengthCntMap[timeList[i]]?reqBodyLengthCntMap[timeList[i]]:0)}; originalStr = JSON.stringify(reqBodyLengthCntArr) ;} catch (error){}"
            }
        }
    },

    {
        "_id":"API_ADVANCE_1.4_API_RSPLENGTH_TENDENCY_CHART",
        "name": "API高级版API画像每日响应长度趋势",
        "localMethodOperateList": [{
            "methodClassName": "LocalMethodTransformImpl",
            "methodName": "getTimeRangeList",
            "args": [1616919522451, 1619511522451, "DATE"],
            "operateRsp": {
                "key": "timeList"
            }
        }],
        "repositoryOperateList": [{
            "repository": "MONGO",
            "collectionName": "httpApiDateStat",
            "operateActionEnum": "QUERY",
            "frontCriteriaList": [{
                "property": "uri",
                "predicate": "IS",
                "value": "httpapi:http://home.51cto.com/index"
            }, {
                "property": "date",
                "predicate": "BETWEEN",
                "value": ["20210328", "20210427"]
            }],
            "operateRsp": {
                "key": "rspBodyLengthCntList"
            },
            "fields": [ "date", "rspBodyLengthCnt"]
        }],
        "rspKeyMap": {
            "axisDate": "timeList",
            "yxisDate": "rspBodyLengthCntList"
        },
        "frontObjUdf": {
            "yxisDate": {
                "backendKeys": ["timeList", "rspBodyLengthCntList"],
                "udf": "try { var originalObj = JSON.parse(originalStr); var timeList = originalObj['timeList']; var rspBodyLengthCntList =originalObj['rspBodyLengthCntList'];var rspBodyLengthCntMap={};for(var i=0;i<rspBodyLengthCntList.length;i++){rspBodyLengthCntMap[rspBodyLengthCntList[i]['date']] = rspBodyLengthCntList[i]['rspBodyLengthCnt'];} var rspBodyLengthCntArr=[];for(var i=0;i<timeList.length;i++){rspBodyLengthCntArr.push(rspBodyLengthCntMap[timeList[i]]?rspBodyLengthCntMap[timeList[i]]:0)}; originalStr = JSON.stringify(rspBodyLengthCntArr) ;} catch (error){}"
            }
        }
    },

    {
        "_id": "API_ADVANCE_1.4_GET_UNILATERAL_FLOWANALYSIS_INFO",
        "name": "API高级版流量统计事件信息",
        "localMethodOperateList": [{
            "methodClassName": "flowAnalysisServiceImpl",
            "methodName": "getUnilateralFlowAnalysis",
            "args": [],
            "operateRsp": {
                "key": "flowAnalysisInfo"
            }
        }],
        "rspKeyMap": {
            "flowAnalysisInfo": "flowAnalysisInfo"
        }
    },

    {
        "_id": "API_ADVANCE_1.4_FLOW_ANALYSIS_APP_STAT_LIST",
        "name": "流量分析应用类型列表",
        "repositoryOperateList": [{
            "repository": "MONGO",
            "collectionName": "flowAnalysisAppStat",
            "operateActionEnum": "QUERY",
            "page": 1,
            "limit": 10,
            "operateRsp": {
                "key": "list"
            }
        }, {
            "repository": "MONGO",
            "collectionName": "flowAnalysisAppStat",
            "operateActionEnum": "COUNT",
            "operateRsp": {
                "key": "totalCount"
            }
        }],
        "rspKeyMap": {"rows": "list", "totalCount": "totalCount"}
    },

    {
        "_id": "API_ADVANCE_1.4_FLOW_ANALYSIS_API_STAT_LIST",
        "name": "流量分析接口类型列表",
        "repositoryOperateList": [{
            "repository": "MONGO",
            "collectionName": "flowAnalysisApiStat",
            "operateActionEnum": "QUERY",
            "frontCriteriaList": [],
            "page": 1,
            "limit": 10,
            "operateRsp": {
                "key": "list"
            }
        }, {
            "repository": "MONGO",
            "collectionName": "flowAnalysisApiStat",
            "operateActionEnum": "COUNT",
            "frontCriteriaList": [],
            "operateRsp": {
                "key": "totalCount"
            }
        }],
        "rspKeyMap": {"rows": "list", "totalCount": "totalCount"}
    },

    {
        "_id": "API_ADVANCE_1.4_FLOW_ANALYSIS_API_STAT_DETAIL",
        "name": "流量分析接口类型详情",
        "repositoryOperateList": [{
            "repository": "MONGO",
            "collectionName": "flowAnalysisApiStat",
            "operateActionEnum": "FINDONE",
            "id": "",
            "frontCriteriaList": [],
            "operateRsp": {
                "key": "detail"
            }
        }],
        "rspKeyMap": {"detail": "detail"}
    },

    {
        "_id": "API_ADVANCE_1.4_FLOW_STATE_VIEW",
        "name": "流量状态概览",
        "localMethodOperateList": [{
            "methodClassName": "flowAnalysisServiceImpl",
            "methodName": "getFlowStateView",
            "args" : [],
            "operateRsp": {
                "key": "content"
            }
        }],
        "rspKeyMap": {"content": "content"}
    },

    {
        "_id": "API_ADVANCE_1.4_FLOW_ANALYSIS_APP_STAT_TOP_GROUP",
        "name": "流量分析应用类型TOP分组聚合",
        "repositoryOperateList": [{
            "repository": "MONGO",
            "collectionName": "flowAnalysisAppStat",
            "operateActionEnum": "QUERY",
            "page": 1,
            "limit": 100,
            "sort": [{"property": "visitCnt", "order": "DESC"}],
            "operateRsp":{
                "key":"list",
                "backend2FrontConvert":{
                    "convertClassName":"flowAnalysisServiceImpl",
                    "convertMethod":"appGroupByAccessDomainIds",
                    "args":[]
                }
            }
        }],
        "rspKeyMap": {"rows": "list"}
    },

    {
        "_id": "API_ADVANCE_1.4_FLOW_ANALYSIS_API_STAT_TOP_GROUP",
        "name": "流量分析接口类型TOP分组聚合",
        "repositoryOperateList": [{
            "repository": "MONGO",
            "collectionName": "flowAnalysisApiStat",
            "operateActionEnum": "QUERY",
            "page": 1,
            "limit": 50,
            "sort": [{"property": "visitCnt", "order": "DESC"}],
            "operateRsp":{
                "key":"list",
                "backend2FrontConvert":{
                    "convertClassName":"flowAnalysisServiceImpl",
                    "convertMethod":"apiGroupByUaClassifications",
                    "args":[]
                }
            }
        }],
        "rspKeyMap": {"rows": "list"}
    },

    {
        "_id": "API_ADVANCE_1.4_FLOW_ANALYSIS_API_STAT_GROUP",
        "name": "流量分析接口类型分组聚合",
        "repositoryOperateList": [{
            "repository": "MONGO",
            "collectionName": "flowAnalysisApiStat",
            "operateActionEnum": "QUERY",
            "frontCriteriaList": [],
            "operateRsp": {
                "key": "list"
            },
            "unwindField": "visitCntByUaClassifications",
            "metabaseGroupOperations": [{
                "groupFields": ["visitCntByUaClassifications.uaClassification"],
                "aggregateOptionList": [{
                    "aggrType": "COUNT",
                    "aggrAsField": "count"
                }]
            }]
        }],
        "rspKeyMap": {"list": "list"}
    },

    {
        "_id": "API_ADVANCE_1.4_FLOW_ANALYSIS_API_STAT_COUNT_GROUP",
        "name": "流量分析接口类型访问量分组聚合",
        "repositoryOperateList": [{
            "repository": "MONGO",
            "collectionName": "flowAnalysisApiStat",
            "operateActionEnum": "QUERY",
            "frontCriteriaList": [],
            "operateRsp": {
                "key": "list"
            },
            "unwindField": "visitCntByUaClassifications",
            "metabaseGroupOperations": [{
                "groupFields": ["visitCntByUaClassifications.uaClassification"],
                "aggregateOptionList": [{
                    "aggrType": "SUM",
                    "aggrField": "visitCntByUaClassifications.visitCnt",
                    "aggrAsField": "count"
                }]
            }]
        }],
        "rspKeyMap": {"list": "list"}
    },

    {
        "_id": "API_ADVANCE_1.4_FLOW_STATUS",
        "name": "流量状态",
        "localMethodOperateList": [{
            "methodClassName": "flowAnalysisServiceImpl",
            "methodName": "flowStatus",
            "args" : [],
            "operateRsp": {
                "key": "status"
            }
        }],
        "rspKeyMap": {"status": "status"}
    },

    {
        "_id": "API_ADVANCE_1.4_FLOW_CLEAN",
        "name": "流量清理",
        "repositoryOperateList": [{
            "repository": "MONGO",
            "collectionName": "flowAnalysisAppStat",
            "operateActionEnum": "DELETE",
            "frontCriteriaList": [],
            "operateRsp": {
                "key": "deleteFlowAnalysisAppStatState"
            }
        }, {
            "repository": "MONGO",
            "collectionName": "flowAnalysisApiStat",
            "operateActionEnum": "DELETE",
            "frontCriteriaList": [],
            "operateRsp": {
                "key": "deleteFlowAnalysisApiStatState"
            }
        }],
        "localMethodOperateList": [{
            "methodClassName": "flowAnalysisServiceImpl",
            "methodName": "delFlowStateCache",
            "args" : [],
            "operateRsp": {
                "key": "delFlowStateCacheState"
            }
        }],
        "rspKeyMap": {"deleteFlowAnalysisAppStatState": "deleteFlowAnalysisAppStatState", "deleteFlowAnalysisApiStatState": "deleteFlowAnalysisApiStatState", "delFlowStateCacheState": "delFlowStateCacheState"}
    },

    {
        "_id":"API_ADVANCE_1.4_API_HIGH_WEAKNESS_LEVEL_GROUP_LIST",
        "name":"弱点等级分组",
        "repositoryOperateList":[{
            "repository":"MONGO",
            "collectionName":"httpApiWeakness",
            "operateActionEnum":"QUERY",
            "frontCriteriaList":[],
            "metabaseGroupOperations":[{
                "groupFields":["level"],
                "aggregateOptionList":[{
                    "aggrType":"COUNT",
                    "aggrField":"count"
                }]
            }],
            "sort":[{"property":"count","order":"DESC"}],
            "operateRsp":{
                "key":"groupList",
                "backend2FrontConvert":{
                    "udf":"var a = JSON.parse(originalStr);var b = {'1':'低危','2':'中危','3':'高危'};for(var i = 0; i < a.length;i++){ a[i]['label'] = a[i]['_id']; a[i]['name'] = b[a[i]['_id']] } ;originalStr = JSON.stringify(a)",
                    "udfRspType":"JSON"
                }
            }
        }],
        "rspKeyMap":{"groupList":"groupList"}
    },

    {
        "_id": "API_ADVANCE_1.4_DEFINED_EVENT_LIST",
        "name": "敏感事件列表",
        "repositoryOperateList": [{
            "repository": "CLICKHOUSE",
            "collectionName": "http_defined_event",
            "operateActionEnum": "QUERY",
            "frontCriteriaList": [{
                "property": "account",
                "predicate": "IS",
                "value": "QZ_NULL"
            }, {
                "property": "eventDefineIds",
                "predicate": "HAS_CLICK_HOUSE",
                "value": "login_event"
            }],
            "sort": [{
                "property": "timestamp",
                "order": "DESC"
            }],
            "page": 1,
            "limit": 10,
            "operateRsp": {
                "key": "list"
            }
        }],
        "rspKeyMap": {
            "rows": "list"
        }
    },

    {
        "_id": "API_ADVANCE_1.4_DEFINED_EVENT_COUNT",
        "name": "敏感事件数量",
        "repositoryOperateList": [{
            "repository": "CLICKHOUSE",
            "collectionName": "http_defined_event",
            "operateActionEnum": "COUNT",
            "frontCriteriaList": [{
                "property": "account",
                "predicate": "IS",
                "value": "QZ_NULL"
            }, {
                "property": "eventDefineIds",
                "predicate": "HAS_CLICK_HOUSE",
                "value": "login_event"
            }],
            "operateRsp": {
                "key": "totalCount"
            },
            "extraInfo": {
                "jobType": "AGGTASK",
                "queryFields": ["COUNT"]
            }
        }],
        "rspKeyMap": {
            "totalCount": "totalCount"
        },
        "frontObjUdf": {
            "totalCount": {
                "backendKeys": ["totalCount"],
                "udf": "try { var a = JSON.parse(originalStr) ; originalStr = a['totalCount'][0]['COUNT'].toString()} catch (error){}"
            }
        }
    },

    {
        "_id":"API_ADVANCE_1.4_DEFINED_EVENT_GROUP",
        "name": "敏感事件聚合",
        "repositoryOperateList":[{
            "repository": "CLICKHOUSE",
            "collectionName": "http_defined_event",
            "operateActionEnum": "QUERY",
            "frontCriteriaList": [{
                "property":"timestamp",
                "predicate":"BETWEEN",
                "value":[*************,*************]
            },{
                "property":"riskDefinition",
                "predicate":"HAS_ANY_CLICK_HOUSE",
                "value":["IP_VISIT_COUNT_EXCEPTION", "IP_USE_MULTI_ACCOUNT", "IP_PARAM_ERGODIC", "AUTO_SCAN", "ACCOUNT_BRUTE_FORCE", "IP_HIGH_FREQUENCE_HIT_DB", "LOGIN_REMOTE", "PATH_EXPLOR"]
            }],
            "selectFields": [],
            "metabaseGroupOperations": [],
            "extraInfo": {},
            "sort": [],
            "operateRsp": {
                "key": "rows"
            }
        }],
        "rspKeyMap":{"rows":"rows"}
    },

    {
        "_id":"API_ADVANCE_1.4_DEFINED_EVENT_DETAIL",
        "name":"事件详情",
        "repositoryOperateList":[{
            "repository":"CLICKHOUSE",
            "collectionName":"event_trace",
            "operateActionEnum":"QUERY",
            "frontCriteriaList":[{
                "property":"primary",
                "predicate":"IS",
                "value":"17fb0a9e4fe711ebbf1e2e6fc93b3ab7"
            }],
            "operateRsp":{
                "key":"httpEvent",
                "backend2FrontConvert":{
                    "udf":"var a = JSON.parse(originalStr);originalStr = JSON.stringify(a[0])",
                    "convertClassName":"sampleEventService",
                    "convertMethod":"getSampleFromEs",
                    "args":[true]
                }
            }
        }],
        "rspKeyMap":{
            "data":"httpEvent"
        }
    },

    {
        "_id":"API_ADVANCE_1.4_CLICKHOUSE_CHECK_HTTP_EVENT",
        "name":"clickhouse溯源查看样例",
        "repositoryOperateList":[{
            "repository":"CLICKHOUSE",
            "collectionName":"event_trace",
            "operateActionEnum":"QUERY",
            "frontCriteriaList":[{
                "property":"primary",
                "predicate":"IS",
                "value":"17fb0a9e4fe711ebbf1e2e6fc93b3ab7"
            }],
            "operateRsp":{
                "key":"httpEvent",
                "backend2FrontConvert":{
                    "udf":"var a = JSON.parse(originalStr);originalStr = JSON.stringify(a[0])",
                    "convertClassName":"sampleEventService",
                    "convertMethod":"getSampleFromEs",
                    "args":[true]
                }
            }
        }],
        "rspKeyMap":{
            "data":"httpEvent"
        }
    },

    {
        "_id": "API_ADVANCE_1.4_API_HIGH_IP_GROUP_LIST",
        "name": "Ip分组",
        "repositoryOperateList": [{
            "repository": "MONGO",
            "collectionName": "ipInfo",
            "operateActionEnum": "QUERY",
            "frontCriteriaList": [],
            "metabaseGroupOperations": [{
                "groupFields": ["country", "province", "city"],
                "aggregateOptionList": [{
                    "aggrType": "COUNT",
                    "aggrField": "count"
                }]
            }],
            "page": 1,
            "limit": 50,
            "sort": [{
                "property": "count",
                "order": "DESC"
            }, {
                "property": "_id",
                "order": "DESC"
            }],
            "operateRsp": {
                "key": "groupList",
                "backend2FrontConvert": {
                    "udf": "var a; var b=[]; a=JSON.parse(originalStr);for(var i=0;i<a.length;i++){if(a[i]['city'] != undefined){b.push(a[i])}}originalStr = JSON.stringify(b)",
                    "udfRspType": "JSON"
                }
            }
        }],
        "rspKeyMap": {
            "groupList": "groupList"
        }
    },

    {
        "_id":"API_ADVANCE_1.4_CLICKHOUSE_EVENT_LABEL_VALUE",
        "name":"单个事件敏感数据详情",
        "repositoryOperateList":[{
            "repository":"CLICKHOUSE",
            "collectionName":"http_defined_event",
            "operateActionEnum":"QUERY",
            "frontCriteriaList":[],
            "primaryOptimize":false,
            "operateRsp":{
                "key":"labelValues",
                "backend2FrontConvert":{
                    "udf":"var a = JSON.parse(originalStr);originalStr = JSON.stringify(a[0])",
                    "convertClassName":"ClickhouseEventHandlerImpl",
                    "convertMethod":"getLabelValues",
                    "args":[true]
                }
            }
        }],
        "rspKeyMap":{"labelValues":"labelValues"}
    },

    {
        "_id":"API_ADVANCE_1.5_PROBLEM_OVERVIEW",
        "name":"首页问题概览",
        "repositoryOperateList":[{
            "repository":"MONGO",
            "collectionName":"httpApiWeakness",
            "operateActionEnum":"COUNT",
            "frontCriteriaList":[{
                "property":"state",
                "predicate":"IN",
                "value":["NEW","SUSPEND","REPAIRING","VERIFYING","FIXED"]
            },{
                "property":"delFlag",
                "predicate":"IS",
                "value":false
            },{
                "property":"userDelFlag",
                "predicate":"IS",
                "value":false
            }],
            "operateRsp":{
                "key":"weaknessCount"
            }
        },{
            "repository":"MONGO",
            "collectionName":"riskInfoAgg",
            "operateActionEnum":"COUNT",
            "frontCriteriaList":[{
                "property":"state",
                "predicate":"IN",
                "value":[NumberInt(0),NumberInt(2),NumberInt(3)]
            }],
            "operateRsp":{
                "key":"riskCount"
            }
        },{
            "repository":"MONGO",
            "collectionName":"riskInfoAgg",
            "operateActionEnum":"QUERY",
            "frontCriteriaList":[{
                "property":"state",
                "predicate":"IN",
                "value":[NumberInt(0),NumberInt(2),NumberInt(3)]
            }],
            "unwindField": "entities",
            "metabaseGroupOperations": [{
                "groupFields": ["entities.value"]
            }],
            "operateRsp":{
                "key":"riskEntityList"
            }
        }],
        "rspKeyMap":{"weaknessCount":"weaknessCount","riskCount":"riskCount","riskEntityList":"riskEntityList"}
    },

    {
        "_id":"API_ADVANCE_1.5_API_DATA",
        "name":"首页个人信息API分布",
        "repositoryOperateList":[{
            "repository":"MONGO",
            "collectionName":"httpApi",
            "operateActionEnum":"QUERY",
            "frontCriteriaList":[{
                "property":"delFlag",
                "predicate":"IS",
                "value":false
            },{
                "property":"userDelFlag",
                "predicate":"IS",
                "value":false
            }],
            "unwindField": "reqDataLabels",
            "metabaseGroupOperations": [{
                "groupFields": ["reqDataLabels"],
                "aggregateOptionList":[{
                    "aggrType":"COUNT",
                    "aggrField":"count"
                },{
                    "aggrType":"FIRST",
                    "aggrField":"reqDataLabels",
                    "aggrAsField":"name"
                }]
            }],
            "sort":[{
                "property":"_id",
                "order":"ASC"
            }],
            "operateRsp":{
                "key":"rows",
                "backend2FrontConvertMap":{
                    "name":{
                        "convertClassName":"LocalMethodTransformImpl",
                        "convertMethod":"transformDataLabelId2nameById",
                        "args":[]
                    }
                }
            }
        },{
            "repository":"MONGO",
            "collectionName":"httpApi",
            "operateActionEnum":"COUNT",
            "frontCriteriaList":[],
            "operateRsp":{
                "key":"totalCount"
            }
        }],
        "rspKeyMap":{"rows":"rows", "totalCount":"totalCount"}
    },

    {
        "_id":"API_ADVANCE_1.5_NEW_API",
        "name":"首页新发现APIs",
        "repositoryOperateList":[{
            "repository":"MONGO",
            "collectionName":"httpApi",
            "operateActionEnum":"COUNT",
            "frontCriteriaList":[{
                "property":"discoverTime",
                "predicate":"BETWEEN",
                "value":[1624377600000,1624463999999]
            },{
                "property":"delFlag",
                "predicate":"IS",
                "value":false
            },{
                "property":"userDelFlag",
                "predicate":"IS",
                "value":false
            }],
            "operateRsp":{
                "key":"day1"
            }
        },{
            "repository":"MONGO",
            "collectionName":"httpApi",
            "operateActionEnum":"COUNT",
            "frontCriteriaList":[{
                "property":"discoverTime",
                "predicate":"BETWEEN",
                "value":[1624464000000,1624550399999]
            },{
                "property":"delFlag",
                "predicate":"IS",
                "value":false
            },{
                "property":"userDelFlag",
                "predicate":"IS",
                "value":false
            }],
            "operateRsp":{
                "key":"day2"
            }
        },{
            "repository":"MONGO",
            "collectionName":"httpApi",
            "operateActionEnum":"COUNT",
            "frontCriteriaList":[{
                "property":"discoverTime",
                "predicate":"BETWEEN",
                "value":[1624550400000,1624636799999]
            },{
                "property":"delFlag",
                "predicate":"IS",
                "value":false
            },{
                "property":"userDelFlag",
                "predicate":"IS",
                "value":false
            }],
            "operateRsp":{
                "key":"day3"
            }
        },{
            "repository":"MONGO",
            "collectionName":"httpApi",
            "operateActionEnum":"COUNT",
            "frontCriteriaList":[{
                "property":"discoverTime",
                "predicate":"BETWEEN",
                "value":[1624636800000,1624723199999]
            },{
                "property":"delFlag",
                "predicate":"IS",
                "value":false
            },{
                "property":"userDelFlag",
                "predicate":"IS",
                "value":false
            }],
            "operateRsp":{
                "key":"day4"
            }
        },{
            "repository":"MONGO",
            "collectionName":"httpApi",
            "operateActionEnum":"COUNT",
            "frontCriteriaList":[{
                "property":"discoverTime",
                "predicate":"BETWEEN",
                "value":[1624723200000,1624809599999]
            },{
                "property":"delFlag",
                "predicate":"IS",
                "value":false
            },{
                "property":"userDelFlag",
                "predicate":"IS",
                "value":false
            }],
            "operateRsp":{
                "key":"day5"
            }
        },{
            "repository":"MONGO",
            "collectionName":"httpApi",
            "operateActionEnum":"COUNT",
            "frontCriteriaList":[{
                "property":"discoverTime",
                "predicate":"BETWEEN",
                "value":[1624809600000,1624895999999]
            },{
                "property":"delFlag",
                "predicate":"IS",
                "value":false
            },{
                "property":"userDelFlag",
                "predicate":"IS",
                "value":false
            }],
            "operateRsp":{
                "key":"day6"
            }
        },{
            "repository":"MONGO",
            "collectionName":"httpApi",
            "operateActionEnum":"COUNT",
            "frontCriteriaList":[{
                "property":"discoverTime",
                "predicate":"BETWEEN",
                "value":[1624896000000,1624982399999]
            },{
                "property":"delFlag",
                "predicate":"IS",
                "value":false
            },{
                "property":"userDelFlag",
                "predicate":"IS",
                "value":false
            }],
            "operateRsp":{
                "key":"day7"
            }
        }],
        "rspKeyMap":{"day1":"day1","day2":"day2","day3":"day3","day4":"day4","day5":"day5","day6":"day6","day7":"day7"}
    },

    {
        "_id":"API_ADVANCE_1.5_RISK_TREND",
        "name":"首页风险趋势",
        "repositoryOperateList":[{
            "repository":"MONGO",
            "collectionName":"riskInfoAgg",
            "operateActionEnum":"QUERY",
            "frontCriteriaList":[{
                "property":"state",
                "predicate":"IN",
                "value":[NumberInt(0),NumberInt(2),NumberInt(3)]
            }],
            "metabaseGroupOperations": [{
                "groupFields": ["date"],
                "aggregateOptionList":[{
                    "aggrType":"COUNT",
                    "aggrField":"count"
                }]
            }],
            "page":NumberInt(1),
            "limit":NumberInt(7),
            "sort":[{
                "property":"_id",
                "order":"ASC"
            }],
            "operateRsp":{
                "key":"riskList"
            }
        },{
            "repository":"MONGO",
            "collectionName":"riskInfoAgg",
            "operateActionEnum":"QUERY",
            "frontCriteriaList":[{
                "property":"state",
                "predicate":"IS",
                "value":NumberInt(2)
            }],
            "metabaseGroupOperations": [{
                "groupFields": ["date"],
                "aggregateOptionList":[{
                    "aggrType":"COUNT",
                    "aggrField":"count"
                }]
            }],
            "page":NumberInt(1),
            "limit":NumberInt(7),
            "sort":[{
                "property":"_id",
                "order":"ASC"
            }],
            "operateRsp":{
                "key":"riskConfirmList"
            }
        }],
        "rspKeyMap":{"riskList":"riskList","riskConfirmList":"riskConfirmList"}
    },

    {
        "_id":"API_ADVANCE_1.5_RISK_COUNTRY_COUNT",
        "name":"首页风险分布",
        "repositoryOperateList":[{
            "repository":"MONGO",
            "collectionName":"riskCountryCount",
            "operateActionEnum":"QUERY",
            "frontCriteriaList":[],
            "sort":[{
                "property":"country",
                "order":"ASC"
            }],
            "operateRsp":{
                "key":"rows"
            }
        }],
        "rspKeyMap":{"rows":"rows"}
    },

    {
        "_id":"API_ADVANCE_1.5_REQ_COUNT",
        "name":"首页访问量统计",
        "repositoryOperateList":[{
            "repository":"MONGO",
            "collectionName":"httpFullflowDateStat",
            "operateActionEnum":"QUERY",
            "frontCriteriaList":[],
            "metabaseGroupOperations": [{
                "groupFields": [],
                "aggregateOptionList":[{
                    "aggrType":"SUM",
                    "aggrField":"dailyAmount",
                    "aggrAsField":"sum"
                }]
            }],
            "operateRsp":{
                "key":"sum",
                "backend2FrontConvert" : {
                    "udf": "var a = JSON.parse(originalStr); if(a.length == 0) {originalStr = '0'} else{originalStr = a[0]['sum'].toString()}",
                    "udfRspType": "NUMBER"
                }
            }
        },{
            "repository":"MONGO",
            "collectionName":"httpFullflowDateStat",
            "operateActionEnum":"QUERY",
            "frontCriteriaList":[],
            "metabaseGroupOperations": [{
                "groupFields": [],
                "aggregateOptionList":[{
                    "aggrType":"AVG",
                    "aggrField":"dailyAmount",
                    "aggrAsField":"avg"
                }]
            }],
            "operateRsp":{
                "key":"avg",
                "backend2FrontConvert" : {
                    "udf": "var a = JSON.parse(originalStr); if(a.length == 0) {originalStr = '0'} else{originalStr = a[0]['avg'].toString()}",
                    "udfRspType": "String"
                }
            }
        },{
            "repository":"MONGO",
            "collectionName":"httpFullflowDateStat",
            "operateActionEnum":"QUERY",
            "frontCriteriaList":[{
                "property":"date",
                "predicate":"IS",
                "value":"20210716"
            }],
            "metabaseGroupOperations": [{
                "groupFields": [],
                "aggregateOptionList":[{
                    "aggrType":"SUM",
                    "aggrField":"dailyAmount",
                    "aggrAsField":"today"
                }]
            }],
            "operateRsp":{
                "key":"today",
                "backend2FrontConvert":{
                    "udf": "var a = JSON.parse(originalStr); if(a.length == 0) {originalStr = '0'} else{originalStr = a[0]['today'].toString()}",
                    "udfRspType": "NUMBER"
                }
            }
        }],
        "rspKeyMap":{"sum":"sum","avg":"avg","today":"today"}
    },

    {
        "_id" : "API_ADVANCE_1.5_RISK_IP_OR_ACCOUNT",
        "name" : "首页风险IP或风险账号",
        "repositoryOperateList" : [
            {
                "repository" : "MONGO",
                "collectionName" : "riskInfoAgg",
                "operateActionEnum" : "QUERY",
                "frontCriteriaList" : [
                    {
                        "property" : "entities.type",
                        "predicate" : "IS",
                        "value" : "ACCOUNT"
                    },
                    {
                        "property" : "state",
                        "predicate" : "IN",
                        "value":[NumberInt(0),NumberInt(2),NumberInt(3)]
                    }
                ],
                "unwindField" : "entities",
                "metabaseGroupOperations" : [
                    {
                        "groupFields" : [
                            "entities.value"
                        ]
                    }
                ],
                "distinctField" : "totalCount",
                "operateRsp" : {
                    "key" : "totalCount",
                    "backend2FrontConvert" : {
                        "udf" : "var a = JSON.parse(originalStr); if(a.length == 0) {originalStr = '0'}else{originalStr = a[0]['totalCount'].toString()}",
                        "udfRspType" : "NUMBER"
                    }
                }
            },
            {
                "repository" : "MONGO",
                "collectionName" : "riskInfoAgg",
                "operateActionEnum" : "QUERY",
                "frontCriteriaList" : [
                    {
                        "property" : "date",
                        "predicate" : "NE",
                        "value" : "********"
                    },
                    {
                        "property" : "entities.type",
                        "predicate" : "IS",
                        "value" : "ACCOUNT"
                    },
                    {
                        "property" : "state",
                        "predicate" : "IN",
                        "value":[NumberInt(0),NumberInt(2),NumberInt(3)]
                    }
                ],
                "unwindField" : "entities",
                "metabaseGroupOperations" : [
                    {
                        "groupFields" : [
                            "entities.value"
                        ]
                    }
                ],
                "distinctField" : "todayTotalCount",
                "operateRsp" : {
                    "key" : "todayTotalCount",
                    "backend2FrontConvert" : {
                        "udf" : "var a = JSON.parse(originalStr); if(a.length == 0) {originalStr = '0'} else{originalStr = a[0]['todayTotalCount'].toString()}",
                        "udfRspType" : "NUMBER"
                    }
                }
            }
        ],
        "frontObjUdf": {
            "todayTotalCount": {
                "backendKeys": ["totalCount","todayTotalCount"],
                "udf": "var a = JSON.parse(originalStr) ; originalStr = (Number(a['totalCount']) - Number(a['todayTotalCount'])).toString()",
                "udfRspType": "NUMBER"
            }
        },
        "rspKeyMap" : {
            "totalCount" : "totalCount",
            "todayTotalCount" : "todayTotalCount"
        }
    }
]

/**
 * 报告模块接口配置
 */
var reportActionConfigs = [
    //任务相关
    {
        "_id": "API_REPORT_TASK_PAGE",
        "name": "报告任务分页",
        "repositoryOperateList": [{
            "repository": "MONGO",
            "collectionName": "reportTask",
            "operateActionEnum": "QUERY",
            "page": 1,
            "limit": 10,
            "sort": [{"property": "createTime", "order": "DESC"}],
            "frontCriteriaList": [
                {
                    "property": "delFlag",
                    "predicate": "IS",
                    "value": false
                }
            ],
            "operateRsp": {
                "key": "list"
            }
        }, {
            "repository": "MONGO",
            "collectionName": "reportTask",
            "operateActionEnum": "COUNT",
            "frontCriteriaList": [
                {
                    "property": "delFlag",
                    "predicate": "IS",
                    "value": false
                }
            ],
            "operateRsp": {
                "key": "totalCount"
            }
        }
        ],
        "rspKeyMap": {"rows": "list", "totalCount": "totalCount"}
    },
    {
        "_id": "API_REPORT_TASK_DELETE",
        "name": "删除任务报告",
        "localMethodOperateList": [{
            "methodClassName": "reportTaskServiceImpl",
            "methodName": "deleteReportTask",
            "args" : [],
            "operateRsp": {
                "key": "result"
            }
        }],
        "rspKeyMap": {"result": "result"}
    },
    {
        "_id": "API_REPORT_DIFF",
        "name": "任务报告间比对",
        "localMethodOperateList": [{
            "methodClassName": "reportDetailServiceImpl",
            "methodName": "reportTaskCompare",
            "args" : [],
            "operateRsp": {
                "key": "result"
            }
        }],
        "rspKeyMap": {"result": "result"}
    },
    //生命周期相关
    {
        "_id": "API_REPORT_API_LIFE_CYCLE_STATISTICS",
        "name": "API生命周期-概览统计数据",
        "repositoryOperateList": [{
            "repository": "MONGO",
            "collectionName": "report_apiLifeCycle_statistics",
            "operateActionEnum": "FINDONE",
            "frontCriteriaList": [],
            "operateRsp": {
                "key": "countInfo"
            },
            "fields": ["addApiNum", "activeApiNum", "inactivationApiNum", "resurrectionApiNum"]
        }],
        "rspKeyMap": {"countInfo": "countInfo"}
    },
    {
        "_id": "API_REPORT_API_LIFE_CYCLE_TREND",
        "name": "API生命周期-趋势图数据",
        "repositoryOperateList": [{
            "repository": "MONGO",
            "collectionName": "report_apiLifeCycle_statistics",
            "operateActionEnum": "QUERY",
            "sort": [{"property": "date", "order": "ASC"}],
            "frontCriteriaList": [],
            "operateRsp": {
                "key": "list"
            }
        }],
        "rspKeyMap": {"list": "list"}
    },
    {
        "_id": "API_REPORT_API_LIFE_CYCLE_ITEM",
        "name": "API生命周期-清单数据",
        "repositoryOperateList": [{
            "repository": "MONGO",
            "collectionName": "report_apiLifeCycle_item_(taskId)",
            "operateActionEnum": "QUERY",
            "page": 1,
            "limit": 10,
            "frontCriteriaList": [],
            "operateRsp": {
                "key": "list"
            }
        }, {
            "repository": "MONGO",
            "collectionName": "report_apiLifeCycle_item_(taskId)",
            "operateActionEnum": "COUNT",
            "frontCriteriaList": [],
            "operateRsp": {
                "key": "totalCount"
            }
        }
        ],
        "rspKeyMap": {"rows": "list", "totalCount": "totalCount"}
    },
    //数据暴露相关
    {
        "_id": "API_REPORT_DATA_EXPOSE_TREND",
        "name": "数据暴露面治理-趋势图数据",
        "repositoryOperateList": [{
            "repository": "MONGO",
            "collectionName": "report_dataExpose_statistics",
            "operateActionEnum": "QUERY",
            "sort": [{"property": "date", "order": "ASC"}],
            "frontCriteriaList": [],
            "operateRsp": {
                "key": "list",
                "backend2FrontConvert":{
                    "convertClassName":"reportDetailServiceImpl",
                    "convertMethod":"fillingDistinctLabel",
                    "args":[]
                }
            }
        }],
        "rspKeyMap": {"list": "list"}
    },
    {
        "_id": "API_REPORT_DATA_EXPOSE_STATISTICS",
        "name": "数据暴露面治理-统计数据",
        "repositoryOperateList": [{
            "repository": "MONGO",
            "collectionName": "report_dataExpose_statistics",
            "operateActionEnum": "FINDONE",
            "frontCriteriaList": [],
            "operateRsp": {
                "key": "countInfo"
            }
        }],
        "rspKeyMap": {"countInfo": "countInfo"}
    },
    {
        "_id": "API_REPORT_DATA_EXPOSE_DIFF_RESULT_PAGE",
        "name": "数据暴露报告对比结果数据分页展示",
        "repositoryOperateList": [{
            "repository": "MONGO",
            "collectionName": "report_dataExpose_diff",
            "operateActionEnum": "QUERY",
            "page": 1,
            "limit": 10,
            "frontCriteriaList": [],
            "operateRsp": {
                "key": "list"
            }
        }, {
            "repository": "MONGO",
            "collectionName": "report_dataExpose_diff",
            "operateActionEnum": "COUNT",
            "frontCriteriaList": [],
            "operateRsp": {
                "key": "totalCount"
            }
        }
        ],
        "rspKeyMap": {"rows": "list", "totalCount": "totalCount"}
    },
    //账号安全治理相关
    {
        "_id": "API_REPORT_ACCOUNT_SAFE_TREND",
        "name": "账号安全治理-趋势图数据",
        "repositoryOperateList": [{
            "repository": "MONGO",
            "collectionName": "report_accountSafe_statistics",
            "operateActionEnum": "QUERY",
            "sort": [{"property": "date", "order": "ASC"}],
            "frontCriteriaList": [],
            "operateRsp": {
                "key": "list"
            }
        }],
        "rspKeyMap": {"list": "list"}
    },
    {
        "_id": "API_REPORT_ACCOUNT_SAFE_ITEM",
        "name": "账号安全治理-清单数据",
        "repositoryOperateList": [{
            "repository": "MONGO",
            "collectionName": "report_accountSafe_item_(taskId)",
            "operateActionEnum": "QUERY",
            "page": 1,
            "limit": 10,
            "frontCriteriaList": [],
            "operateRsp": {
                "key": "list"
            }
        }, {
            "repository": "MONGO",
            "collectionName": "report_accountSafe_item_(taskId)",
            "operateActionEnum": "COUNT",
            "frontCriteriaList": [],
            "operateRsp": {
                "key": "totalCount"
            }
        }
        ],
        "rspKeyMap": {"rows": "list", "totalCount": "totalCount"}
    },
    {
        "_id": "API_REPORT_ACCOUNT_SAFE_DIFF_RESULT_PAGE",
        "name": "账号安全治理报告对比结果数据分页展示",
        "repositoryOperateList": [{
            "repository": "MONGO",
            "collectionName": "report_accountSafe_diff",
            "operateActionEnum": "QUERY",
            "page": 1,
            "limit": 10,
            "frontCriteriaList": [],
            "operateRsp": {
                "key": "list"
            }
        }, {
            "repository": "MONGO",
            "collectionName": "report_accountSafe_diff",
            "operateActionEnum": "COUNT",
            "frontCriteriaList": [],
            "operateRsp": {
                "key": "totalCount"
            }
        }
        ],
        "rspKeyMap": {"rows": "list", "totalCount": "totalCount"}
    },
    //数据泄漏治理相关
    {
        "_id": "API_REPORT_DATA_REVEAL_TREND",
        "name": "数据泄漏治理-趋势图数据",
        "repositoryOperateList": [{
            "repository": "MONGO",
            "collectionName": "report_dataReveal_statistics",
            "operateActionEnum": "QUERY",
            "sort": [{"property": "date", "order": "ASC"}],
            "frontCriteriaList": [],
            "operateRsp": {
                "key": "list",
                "backend2FrontConvert":{
                    "convertClassName":"reportDetailServiceImpl",
                    "convertMethod":"fillingDistinctLabel",
                    "args":[]
                }
            }
        }],
        "rspKeyMap": {"list": "list"}
    },
    {
        "_id": "API_REPORT_DATA_REVEAL_STATISTICS",
        "name": "数据泄漏治理-统计数据",
        "repositoryOperateList": [{
            "repository": "MONGO",
            "collectionName": "report_dataReveal_statistics",
            "operateActionEnum": "FINDONE",
            "frontCriteriaList": [],
            "operateRsp": {
                "key": "countInfo"
            }
        }],
        "rspKeyMap": {"countInfo": "countInfo"}
    },
    {
        "_id": "API_REPORT_DATA_REVEAL_DIFF_RESULT_PAGE",
        "name": "数据泄漏治理报告对比结果数据分页展示",
        "repositoryOperateList": [{
            "repository": "MONGO",
            "collectionName": "report_dataReveal_diff",
            "operateActionEnum": "QUERY",
            "page": 1,
            "limit": 10,
            "frontCriteriaList": [],
            "operateRsp": {
                "key": "list"
            }
        }, {
            "repository": "MONGO",
            "collectionName": "report_dataReveal_diff",
            "operateActionEnum": "COUNT",
            "frontCriteriaList": [],
            "operateRsp": {
                "key": "totalCount"
            }
        }
        ],
        "rspKeyMap": {"rows": "list", "totalCount": "totalCount"}
    },
    {
        "_id": "URL_STURCTURE_PREURLS_GROUP",
        "name": "应用树形结构preUrls分组查询数量",
        "repositoryOperateList": [{
            "repository": "MONGO",
            "collectionName": "urlStructure",
            "operateActionEnum": "QUERY",
            "frontCriteriaList": [],
            "unwindField":"preUrls",
            "metabaseGroupOperations": [{
                "groupFields": ["preUrls"],
                "aggregateOptionList": [{
                    "aggrType": "COUNT",
                    "aggrField": "count"
                }]
            }],
            "sort": [{
                "property": "count",
                "order": "DESC"
            }],
            "operateRsp": {
                "key": "groupList",
                "backend2FrontConvert": {
                    "udf": "var a; a=JSON.parse(originalStr); for(var i = 0; i < a.length;i++){a[i]['label'] = a[i]['_id'];a[i]['name'] = a[i]['_id'];} originalStr = JSON.stringify(a)",
                    "udfRspType": "JSON"
                }
            }
        }],
        "rspKeyMap": {
            "groupList": "groupList"
        }
    },

    {
        "_id":"APP_AUDIT_2.1_IP_LIST_CITY_GROUP_LIST",
        "name":"Ip地域分组",
        "repositoryOperateList":[{
            "repository":"MONGO",
            "collectionName":"ipInfo",
            "operateActionEnum":"QUERY",
            "frontCriteriaList":[],
            "metabaseGroupOperations":[{
                "groupFields":["country"],
                "aggregateOptionList":[{
                    "aggrType":"COUNT",
                    "aggrField":"count"
                },
                    {
                        "aggrType":"FIRST",
                        "aggrField":"country"
                    }
                ]
            }],
            "sort":[{"property":"count","order":"DESC"}],
            "operateRsp":{
                "key":"groupList",
                "backend2FrontConvert":{
                    "udf":"var a=JSON.parse(originalStr);\nvar b=[]\nif(a && a.length > 0) {\n    for(var i = 0 ;i < a.length;i++) {\n        if(a[i][\'_id\'] && a[i][\'_id\'].length > 0){\n            b.push({\n                \'country\':a[i][\'country\'],\n           \'count\':a[i][\'count\'],\n                \'_id\':a[i][\'_id\']\n            })\n        }\n    }\n}\noriginalStr = JSON.stringify(b)",
                    "udfRspType":"JSON"
                }
            }
        }],
        "rspKeyMap":{"groupList":"groupList"}
    },

    {
        "_id":"APP_AUDIT_2.1_IP_LIST_ACCESSDOMAIN_GROUP_LIST",
        "name":"Ip网段分组",
        "repositoryOperateList":[{
            "repository":"MONGO",
            "collectionName":"ipInfo",
            "operateActionEnum":"QUERY",
            "frontCriteriaList":[],
            "unwindField":"accessDomainIds",
            "metabaseGroupOperations":[{
                "groupFields":["accessDomainIds"],
                "aggregateOptionList":[{
                    "aggrType":"COUNT",
                    "aggrField":"count"
                }
                ]
            }],
            "sort":[{"property":"count","order":"DESC"}],
            "operateRsp":{
                "key":"groupList",
                "backend2FrontConvert":{
                    "udf":"var a=JSON.parse(originalStr);\nvar b = [];\nif(a && a.length>0) {\n    for(var i = 0;i < a.length;i++) {\n        if(a[i].hasOwnProperty(\"_id\")) {\n            \n            b.push({\n                \"_id\":a[i][\"_id\"],\n                \"name\":a[i][\"_id\"],\n                \"count\":a[i][\"count\"]\n            })\n        }\n    }\n}\noriginalStr = JSON.stringify(b)",
                    "udfRspType":"JSON"
                }
            }
        }],
        "rspKeyMap":{"groupList":"groupList"}
    },

    {
        "_id":"APP_AUDIT_2.1_IP_LIST_BLOCKFLAG_GROUP_LIST",
        "name":"Ip阻断状态分组",
        "repositoryOperateList":[{
            "repository":"MONGO",
            "collectionName":"ipInfo",
            "operateActionEnum":"QUERY",
            "frontCriteriaList":[],
            "metabaseGroupOperations":[{
                "groupFields":["blockFlag"],
                "aggregateOptionList":[
                    {
                        "aggrType":"COUNT",
                        "aggrField":"count"
                    }
                ]
            }],
            "sort":[{"property":"count","order":"DESC"}],
            "operateRsp":{
                "key":"groupList",
                "backend2FrontConvert":{
                    "udf":"var a=JSON.parse(originalStr);\nvar b = [];\nif(a && a.length>0) {\n    for(var i = 0;i < a.length;i++) {\n        if(a[i].hasOwnProperty(\"_id\")) {\n            \n            b.push({\n                \"_id\":a[i][\"_id\"],\n                \"name\":a[i][\"_id\"] == \"true\" ? \"是\" : \"否\",\n                \"count\":a[i][\"count\"]\n            })\n        }\n    }\n}\noriginalStr = JSON.stringify(b)",
                    "udfRspType":"JSON"
                }
            }
        }],
        "rspKeyMap":{"groupList":"groupList"}
    },

    {
        "_id":"AUDIT_V2_2.1_FIGURE_DATE_STAT",
        "name":"ip画像按日统计",
        "repositoryOperateList":[
            {
                "repository":"MONGO",
                "collectionName":"ipDateInfo",
                "operateActionEnum":"QUERY",
                "frontCriteriaList":[{
                    "property":"ip",
                    "predicate":"IS",
                    "value":"**************"
                },{
                    "property":"date",
                    "predicate":"BETWEEN",
                    "value":["20201001","20201010"]
                }],
                "sort":[{"property": "visitCnt","order": "DESC"}],
                "page":1,
                "limit":5,
                "operateRsp":{
                    "key":"cntStat",
                    "backend2FrontConvert":{
                        "convertClassName":"PortrayalHandlerImpl",
                        "convertMethod":"getPortrayalTotalStat"
                    }
                }
            },{
                "repository":"MONGO",
                "collectionName":"ipDateInfo",
                "operateActionEnum":"COUNT",
                "frontCriteriaList":[{
                    "property":"ip",
                    "predicate":"IS",
                    "value":"**************"
                }],
                "operateRsp":{
                    "key":"totalCount"
                }
            }
        ],
        "rspKeyMap":{"rows":"cntStat","totalCount":"totalCount"}
    },

    {
        "_id":"AUDIT_V2_2.1_FIGURE_VISIT_TENDENCY_CHART",
        "name":"画像访问次数趋势",
        "localMethodOperateList":[{
            "methodClassName":"LocalMethodTransformImpl",
            "methodName":"getTimeRangeList",
            "args":[1601481600000,1602259200000,"DATE"],
            "operateRsp":{
                "key":"timeList"
            }
        }],
        "repositoryOperateList":[
            {
                "repository":"MONGO",
                "collectionName":"ipDateInfo",
                "operateActionEnum":"QUERY",
                "frontCriteriaList":[],
                "operateRsp":{
                    "key":"dateList",
                    "backend2FrontConvert":{
                        "convertClassName":"PortrayalHandlerImpl",
                        "convertMethod":"getPortrayalTotalStat"
                    }
                }
            }
        ],
        "rspKeyMap":{"axisDate":"timeList","yxisDate":"dateList"},
        "frontObjUdf":{
            "yxisDate": {
                "backendKeys":["timeList","dateList"],
                "frontKeyValue":{
                    "chart_type":"VISIT_CHART"
                },
                "udf":"try { \n    var originalObj = JSON.parse(originalStr); \nvar timeList = originalObj[\"timeList\"]; \nvar dateList =originalObj[\"dateList\"];\nvar dateMap={};\n\nfor(var i=0;i< dateList.length;i++){\n    \n    var date = dateList[i][\"date\"];\n    \n    if(chart_type == \"VISIT\") {\n\n        dateMap[ date ] = dateList[i][\"visitCnt\"]\n        \n    } else if(chart_type == \"LOGIN\") {\n\n        dateMap[ date ] = dateList[i][\"loginCnt\"]\n        \n    } else if(chart_type == \"DOWNLOAD_FILE\") {\n        \n        dateMap[ date ] = dateList[i][\"downloadFileDistinctCnt\"]\n        \n    } else if(chart_type == \"UPLOAD_FILE\") {\n\n        dateMap[ date ] = dateList[i][\"uploadFileDistinctCnt\"]\n    } else if(chart_type  == \"VISIT_APP\" ) {\n\n        dateMap[ date ] = dateList[i][\"distinctAppCnt\"]\n    } else if (chart_type == \"VISIT_API\") {\n\n        dateMap[ date ] = dateList[i][\"distinctApiCnt\"]\n    } else if (chart_type == \"RSP_DATALABEL\") {\n\n        dateMap[ date ] = dateList[i][\"rspDataLabelDistinctCnt\"]\n    } else {\n        dateMap[ date ] = dateList[i][\"visitCnt\"]\n    }\n    \n} \n\nvar visitCntArr=[];\nfor(var i=0;i<timeList.length;i++){\n    visitCntArr.push(dateMap[timeList[i]]?dateMap[timeList[i]]:0)\n}; \noriginalStr = JSON.stringify(visitCntArr) ;} \ncatch (error){}"
            }
        }
    },

    {
        "_id":"AUDIT_V2_2.1_FIGURE_VISIT_LOGIN_HOUR_HOT_CHART",
        "name":"画像访问次数和登录次数小时纬度热力图",
        "repositoryOperateList":[
            {
                "repository":"MONGO",
                "collectionName":"ipDateInfo",
                "operateActionEnum":"QUERY",
                "frontCriteriaList":[],
                "fields":["ip","date","hourlyVisitCnt","loginCntHrly"],
                "operateRsp":{
                    "key":"hourVisitList"

                }
            }
        ],
        "rspKeyMap":{"list":"hourVisitList"}
    },

    {
        "_id":"AUDIT_V2_2.1_IP_RELATE_USER_INFO_ACCOUNT",
        "name":"审计2.1IP画像用户关系账号关系",
        "repositoryOperateList":[{
            "repository":"MONGO",
            "collectionName":"ipDateInfo",
            "operateActionEnum":"QUERY",
            "frontCriteriaList":[],
            "metabaseGroupOperations":[{
                "groupFields":["ip"],
                "aggregateOptionList":[{
                    "aggrType":"ADDTOSET",
                    "aggrField":"relatedAccountList"
                }]
            }],
            "operateRsp":{
                "key":"ipInfo",
                "backend2FrontConvert":{
                    "udf":"var a=JSON.parse(originalStr);var ipInfo=a[0];if(ipInfo['relatedAccountList']&&ipInfo['relatedAccountList'].length>0){var relatedAccountList=[];for(var i=0;i<ipInfo['relatedAccountList'].length;i++){for(var j=0;j<ipInfo['relatedAccountList'][i].length;j++){if(relatedAccountList.indexOf(ipInfo['relatedAccountList'][i][j])==-1){relatedAccountList.push(ipInfo['relatedAccountList'][i][j])}}}ipInfo['relatedAccountList']=relatedAccountList;} originalStr=JSON.stringify(ipInfo);",
                    "udfRspType":"JSON"
                }
            },
            "relateRepositoryOperate":[{
                "repository": "MONGO",
                "collectionName": "accountInfo",
                "operateActionEnum":"QUERY",
                "frontCriteriaList":[
                    {
                        "property":"account",
                        "predicate":"IN",
                        "value":"relatedAccountList",
                        "require":true
                    }
                ],
                "metabaseGroupOperations":[{
                    "groupFields":["staffDepart"],
                    "aggregateOptionList":[{
                        "aggrType":"ADDTOSET",
                        "aggrField":"account"
                    },{
                        "aggrType":"COUNT",
                        "aggrField":"count"
                    }]
                }],
                "sort":[{"property":"count","order":"DESC"},{"property":"_id","order":"DESC"}],
                "page":1,
                "limit":10,
                "operateRsp":{
                    "key":"accountList",
                    "backend2FrontConvert":{
                        "udf": "var a = JSON.parse(originalStr); \nif(a && a.length > 0 ) { \n    for (var i = 0; i < a.length;i++){ \n        if(!a[i]['_id']) {\n            a[i]['_id'] = '其他'\n        } \n    }\n}else { \n    a = [];\n} ;\noriginalStr = JSON.stringify(a)",
                        "udfRspType":"JSON"
                    }
                }
            },{
                "repository": "MONGO",
                "collectionName": "accountInfo",
                "operateActionEnum":"QUERY",
                "frontCriteriaList":[
                    {
                        "property":"account",
                        "predicate":"IN",
                        "value":"relatedAccountList",
                        "require":true
                    }
                ],
                "metabaseGroupOperations":[{
                    "groupFields":["staffDepart"],
                    "aggregateOptionList":[{
                        "aggrType":"ADDTOSET",
                        "aggrField":"account"
                    }]
                }],
                "distinctField":"staffDepart",
                "operateRsp":{
                    "key":"totalCount",
                    "backend2FrontConvert":{
                        "udf":"var a = JSON.parse(originalStr); if(a && a.length >0) { originalStr = a[0]['staffDepart'].toString() } else { originalStr = '0' }",
                        "udfRspType":"NUMBER"
                    }
                }
            }]
        }],
        "rspKeyMap":{"rows":"accountList","totalCount":"totalCount"}
    },

    {
        "_id":"AUDIT_V2_IP_RELATE_USER_INFO_NETWORK",
        "name":"审计2.0IP画像用户关系网段关系",
        "repositoryOperateList":[{
            "repository":"MONGO",
            "collectionName":"ipDateInfo",
            "operateActionEnum":"QUERY",
            "frontCriteriaList":[{
                "property":"ip",
                "predicate":"IS",
                "value":"************"
            },{
                "property":"date",
                "predicate":"BETWEEN",
                "value":["********","********"]
            }],
            "metabaseGroupOperations":[{
                "groupFields":["ip"],
                "aggregateOptionList":[{
                    "aggrType":"ADDTOSET",
                    "aggrField":"accessDomainIds"
                }]
            }],
            "operateRsp": {
                "key": "networkDistinctList",
                "backend2FrontConvert":{
                    "udf":"var a=JSON.parse(originalStr);\nvar ipInfo=a[0];\n\nif(ipInfo[\"accessDomainIds\"]&&ipInfo[\"accessDomainIds\"].length>0){\n    var accessDomainIds = [];\n    for(var i=0;i<ipInfo[\"accessDomainIds\"].length;i++){\n        for(var j=0;j<ipInfo[\"accessDomainIds\"][i].length;j++){\n\n            if(accessDomainIds.indexOf(ipInfo[\"accessDomainIds\"][i][j])==-1){\n                accessDomainIds.push(ipInfo[\"accessDomainIds\"][i][j])\n            }\n        }\n    }\n    ipInfo[\"accessDomainIds\"]=accessDomainIds;\n}\noriginalStr=JSON.stringify(ipInfo);",
                    "udfRspType":"JSON"
                }
            },
            "relateRepositoryOperate":[{
                "repository": "NACOS",
                "collectionName":"networkSegment",
                "operateActionEnum":"QUERY",
                "frontCriteriaList":[
                    {
                        "property":"id",
                        "predicate":"IN",
                        "value":"accessDomainIds"
                    }
                ],
                "page":1,
                "limit":100,
                "operateRsp":{
                    "key":"networkList"
                }
            }]
        }],
        "rspKeyMap":{"rows":"networkList","totalCount":"totalCount"},
        "frontObjUdf": {
            "rows": {
                "backendKeys": [
                    "networkDistinctList","networkList"
                ],
                "udf": "var a = JSON.parse(originalStr);var b = a['networkDistinctList']; var c = a['networkList'] || [] ;if(b['accessDomainIds'].indexOf('互联网') != -1) { c.push({id:'互联网'}) }; originalStr = JSON.stringify(c.slice((page - 1)*limit, (page - 1)*limit + limit ))",
                "udfRspType": "JSON",
                "frontKeyValue":{
                    "page":1,
                    "limit":10
                }
            },
            "totalCount": {
                "backendKeys": [
                    "networkDistinctList","networkList"
                ],
                "udf": "var a = JSON.parse(originalStr);var b = a['networkDistinctList']; var c = a['networkList'] || [] ;if(b['accessDomainIds'].indexOf('互联网') != -1) { c.push({id:'互联网'}) }; originalStr = c.length.toString()",
                "udfRspType": "NUMBER"
            }
        }
    },

    {
        "_id":"AUDIT_V2_2.1_IP_RELATE_USER_INFO_UA",
        "name":"审计2.1IP画像用户关系设备关系",
        "repositoryOperateList":[{
            "repository":"MONGO",
            "collectionName":"ipDateInfo",
            "operateActionEnum":"QUERY",
            "frontCriteriaList":[],
            "metabaseGroupOperations":[{
                "groupFields":["ip"],
                "aggregateOptionList":[{
                    "aggrType":"ADDTOSET",
                    "aggrField":"uaByUaType"
                }]
            }],
            "operateRsp":{
                "key":"ipInfo",
                "backend2FrontConvert":{
                    "udf":"var a=JSON.parse(originalStr);var ipInfo=a[0];if(ipInfo['uaByUaType']&&ipInfo['uaByUaType'].length>0){var uaByUaType={};for(var i=0;i<ipInfo['uaByUaType'].length;i++){for(var p in ipInfo['uaByUaType'][i]){if(uaByUaType[p]==null){uaByUaType[p]=ipInfo['uaByUaType'][i][p]}else{for(var j=0;j<ipInfo['uaByUaType'][i][p].length;j++){if(uaByUaType[p].indexOf(ipInfo['uaByUaType'][i][p][j])==-1){uaByUaType[p].push(ipInfo['uaByUaType'][i][p][j])}}}}}ipInfo['uaByUaType']=uaByUaType;} originalStr=JSON.stringify(ipInfo);",
                    "udfRspType":"JSON"
                }
            }
        }],
        "rspKeyMap":{"rows":"uaByUaType","totalCount":"totalCount"},
        "frontObjUdf": {
            "rows":{
                "backendKeys":["ipInfo"],
                "udf":"var a = JSON.parse(originalStr); var b = a['ipInfo'];var c = b['uaByUaType']; var result = []; if(c != null) { for(var p in c) { result.push({'name':p,'list':c[p]})} } ;originalStr = JSON.stringify(result.slice( (page - 1)*limit, (page - 1)*limit + limit ))",
                "udfRspType": "JSON",
                "frontKeyValue":{
                    "page":1,
                    "limit":10
                }
            },
            "totalCount":{
                "backendKeys":["ipInfo"],
                "udf":"var a = JSON.parse(originalStr); var b = a['ipInfo'];var c = b['uaByUaType']; var result = []; if(c != null) { for(var p in c) { result.push({'name':p,'list':c[p]}) }} ;originalStr = result.length.toString()",
                "udfRspType": "NUMBER"
            }
        }
    },
    {
        "_id" : "FILE_ES_EVENT_LIST_EXPORT",
        "name" : "文件ES事件列表导出",
        "repositoryOperateList" : [
            {
                "repository" : "CLICKHOUSE",
                "collectionName" : "http_defined_event",
                "operateActionEnum" : "EXPORT",
                "frontCriteriaList" : [
                ],
                "sort" : [
                    {
                        "property" : "timestamp",
                        "order" : "DESC"
                    }
                ],
                "page" : 1,
                "limit" : 10000,
                "operateRsp" : {
                    "key" : "list",
                    "backend2FrontConvertMap" : {
                        "timestamp" : {
                            "convertClassName" : "LocalMethodTransformImpl",
                            "convertMethod" : "formatTimestamp",
                            "args" : [
                                "yyyy-MM-dd HH:mm:ss"
                            ]
                        },
                        "file.fileDirection" : {
                            "udf" : "var a = originalStr; var b =  a == 'DOWNLOAD' ? '下载' :'上传'; originalStr = b ",
                            "udfRspType" : "STRING"
                        },
                        "account" : {
                            "convertClassName" : "LocalMethodTransformImpl",
                            "convertMethod" : "transAccount",
                            "args" : []
                        }
                    }
                }
            }
        ],
        "exportFileName" : "导出文件事件.xls",
        "exportTypeName" : "导出文件事件",
        "exportSheetConfig" : {
            "rows" : {
                "exportFields" : [
                    {
                        "field" : "timestamp",
                        "name" : "时间"
                    },
                    {
                        "field" : "account",
                        "name" : "账号"
                    },
                    {
                        "field" : "apiUrl",
                        "name" : "API"
                    },
                    {
                        "field" : "ip",
                        "name" : "IP"
                    },
                    {
                        "field" : "accessDomains[0].id",
                        "name" : "网段"
                    },
                    {
                        "field" : "file.fileDirection",
                        "name" : "动作"
                    }
                ],
                "sheetName" : "文件事件列表"
            }
        },
        "rspKeyMap" : {
            "rows" : "list"
        }
    },
    {
        "_id":"AUDIT_V2_2.1_IP_RELATE_USER_INFO_APP",
        "name":"审计2.1IP画像用户关系应用关系",
        "repositoryOperateList":[
            {
                "repository":"MONGO",
                "collectionName":"ipInfo",
                "operateActionEnum":"QUERY",
                "frontCriteriaList":[],
                "fields":[
                    "appUriList"
                ],
                "operateRsp":{
                    "key":"ipInfo",
                    "backend2FrontConvert":{
                        "udf":"var a=JSON.parse(originalStr);\nvar ipInfo=a[0];originalStr=JSON.stringify(ipInfo);",
                        "udfRspType":"JSON"
                    }
                },
                "relateRepositoryOperate":[
                    {
                        "repository":"MONGO",
                        "collectionName":"httpApp",
                        "operateActionEnum":"QUERY",
                        "frontCriteriaList":[
                            {
                                "property":"uri",
                                "predicate":"IN",
                                "value":"appUriList",
                                "require":true
                            }
                        ],
                        "fields":[
                            "name",
                            "uri"
                        ],
                        "operateRsp":{
                            "key":"appList"
                        }
                    }
                ]
            }
        ],
        "rspKeyMap":{
            "rows":"appList",
            "totalCount":"ipInfo"
        },
        "frontObjUdf":{
            "rows":{
                "backendKeys":["ipInfo","appList"],
                "udf":"var a = JSON.parse(originalStr); \nvar originAppList = a['ipInfo']['appUriList'];\nvar queryAppList = a['appList']; \nvar queryAppMap = {};\n\nfor(var i = 0;i < queryAppList.length; i++){\n    queryAppMap[queryAppList[i]['uri']] = queryAppList[i]['name'];\n}\n\nvar result = []; \nvar resultKey = {};\n\nfor(var i = 0; i< originAppList.length;i++) {\n    \n    if(originAppList[i].indexOf(\"@QZKJMGC@\") != -1) {\n        originAppList[i] = originAppList[i].substring(0,originAppList[i].indexOf(\"@QZKJMGC@\")) \n    }\n    if(resultKey.hasOwnProperty( originAppList[i]   )) continue;\n\n    resultKey[ originAppList[i] ] = 1;\n    \n    result.push({\n        \"uri\":originAppList[i],\n        \"name\":queryAppMap[originAppList[i]] || originAppList[i].substring(\"httpapp:\".length)\n    })\n}\n\noriginalStr = JSON.stringify(result.slice( (page - 1)*limit, (page - 1)*limit + limit ))",
                "udfRspType": "JSON",
                "frontKeyValue":{
                    "page":1,
                    "limit":5
                }
            },
            "totalCount":{
                "backendKeys":[
                    "ipInfo"
                ],
                "udf":"var a = JSON.parse(originalStr); \nvar b = a['ipInfo']['appUriList'];\noriginalStr = b.length.toString() || 0;\n",
                "udfRspType":"NUMBER"
            }
        }
    },
    {
        "_id":"AUDIT_V2_2.1_FIGURE_CNT_STAT",
        "name":"画像行为偏好统计",
        "repositoryOperateList":[
            {
                "repository":"MONGO",
                "collectionName":"ipDateInfo",
                "operateActionEnum":"QUERY",
                "frontCriteriaList":[],
                "metabaseGroupOperations":[{
                    "groupFields":["ip"],
                    "aggregateOptionList":[{
                        "aggrType":"PUSH",
                        "aggrField":"appUriList"
                    },{
                        "aggrType":"PUSH",
                        "aggrField":"apiUriList"
                    },{
                        "aggrType":"SUM",
                        "aggrField":"visitCnt"
                    },{
                        "aggrType":"SUM",
                        "aggrField":"loginCnt"
                    },{
                        "aggrType":"SUM",
                        "aggrField":"uploadFileDistinctCnt"
                    },{
                        "aggrType":"SUM",
                        "aggrField":"downloadFileDistinctCnt"
                    },{
                        "aggrType":"SUM",
                        "aggrField":"rspDataDistinctCnt"
                    }]
                }],
                "projectOperationList":[{
                    "startField":"appUriList",
                    "transform":"IS",
                    "endField":"appUriGroupList"
                },{
                    "startField":"apiUriList",
                    "transform":"IS",
                    "endField":"apiUriGroupList"
                },{
                    "startField":"_id",
                    "transform":"IS",
                    "endField":"ip"
                },{
                    "startField":"visitCnt",
                    "transform":"IS",
                    "endField":"visitCnt"
                },{
                    "startField":"loginCnt",
                    "transform":"IS",
                    "endField":"loginCnt"
                },{
                    "startField":"uploadFileDistinctCnt",
                    "transform":"IS",
                    "endField":"uploadFileDistinctCnt"
                },{
                    "startField":"downloadFileDistinctCnt",
                    "transform":"IS",
                    "endField":"downloadFileDistinctCnt"
                },{
                    "startField":"rspDataDistinctCnt",
                    "transform":"IS",
                    "endField":"rspDataLabelDistinctCnt"
                }],
                "operateRsp":{
                    "key":"stat",
                    "backend2FrontConvert":{
                        "convertClassName":"PortrayalHandlerImpl",
                        "convertMethod":"getPortrayalTotalStatFromGroupDateInfo"
                    }
                }
            }
        ],
        "rspKeyMap":{"stat":"stat"},
        "frontObjUdf":{
            "stat": {
                "backendKeys":["stat"],
                "udf":"var a = JSON.parse(originalStr); var b = a['stat'][0]; originalStr = JSON.stringify(b)",
                "udfRspType":"JSON"
            }
        }
    },

    {
        "_id":"APP_AUDIT_2.1_ACCOUNT_LIST_GROUP_LIST",
        "name":"account部门分组",
        "repositoryOperateList":[{
            "repository":"MONGO",
            "collectionName":"accountInfo",
            "operateActionEnum":"QUERY",
            "frontCriteriaList":[],
            "metabaseGroupOperations":[{
                "groupFields":["staffDepart"],
                "aggregateOptionList":[{
                    "aggrType":"COUNT",
                    "aggrField":"count"
                }
                ]
            }],
            "sort":[{"property":"count","order":"DESC"}],
            "operateRsp":{
                "key":"groupList",
                "backend2FrontConvert":{
                    "udf":"var a=JSON.parse(originalStr);\nvar b = [];\nif(a && a.length>0) {\n    for(var i = 0;i < a.length;i++) {\n        if(a[i].hasOwnProperty(\"_id\") && a[i][\"_id\"] !='') {\n            \n            b.push({\n                \"id\":a[i][\"_id\"],\n                \"name\":a[i][\"_id\"],\n                \"count\":a[i][\"count\"]\n            })\n        }\n    }\n}\noriginalStr = JSON.stringify(b)",
                    "udfRspType":"JSON"
                }
            }
        }],
        "rspKeyMap":{"groupList":"groupList"}
    },

    {
        "_id":"APP_AUDIT_2.1_ACCOUNT_LIST_DEPART_ENUM_LIST",
        "name":"account部门筛选",
        "repositoryOperateList":[{
            "repository":"MONGO",
            "collectionName":"accountInfo",
            "operateActionEnum":"QUERY",
            "frontCriteriaList":[],
            "metabaseGroupOperations":[{
                "groupFields":["staffDepart"]
            }],
            "operateRsp":{
                "key":"groupList",
                "backend2FrontConvert":{
                    "udf":"var a=JSON.parse(originalStr);\nvar b = [];\nif(a && a.length>0) {\n    for(var i = 0;i < a.length;i++) {\n        if(a[i].hasOwnProperty(\"_id\") && a[i][\"_id\"]) {\n            \n            b.push({\n                \"label\":a[i][\"_id\"],\n                \"value\":a[i][\"_id\"],\n            })\n        }\n    }\n}\noriginalStr = JSON.stringify(b)",
                    "udfRspType":"JSON"
                }
            }
        }],
        "rspKeyMap":{"groupList":"groupList"}
    },

    {
        "_id":"APP_AUDIT_2.5_ACCOUNT_LIST_STAFFROLE_GROUP_LIST",
        "name":"account角色分组",
        "repositoryOperateList":[{
            "repository":"MONGO",
            "collectionName":"accountInfo",
            "operateActionEnum":"QUERY",
            "frontCriteriaList":[],
            "metabaseGroupOperations":[{
                "groupFields":["staffRole"],
                "aggregateOptionList":[{
                    "aggrType":"COUNT",
                    "aggrField":"count"
                }
                ]
            }],
            "sort":[{"property":"count","order":"DESC"}],
            "operateRsp":{
                "key":"groupList",
                "backend2FrontConvert":{
                    "udf":"var a=JSON.parse(originalStr);\nvar b = [];\nif(a && a.length>0) {\n    for(var i = 0;i < a.length;i++) {\n        if(a[i].hasOwnProperty(\"_id\") && a[i][\"_id\"] != '') {\n            \n            b.push({\n                \"id\":a[i][\"_id\"],\n                \"name\":a[i][\"_id\"],\n                \"count\":a[i][\"count\"]\n            })\n        }\n    }\n}\noriginalStr = JSON.stringify(b)",
                    "udfRspType":"JSON"
                }
            }
        }],
        "rspKeyMap":{"groupList":"groupList"}
    },

    {
        "_id":"APP_AUDIT_2.5_ACCOUNT_LIST_STAFFROLE_ENUM_LIST",
        "name":"account角色筛选",
        "repositoryOperateList":[{
            "repository":"MONGO",
            "collectionName":"accountInfo",
            "operateActionEnum":"QUERY",
            "frontCriteriaList":[],
            "metabaseGroupOperations":[{
                "groupFields":["staffRole"]
            }],
            "operateRsp":{
                "key":"groupList",
                "backend2FrontConvert":{
                    "udf":"var a=JSON.parse(originalStr);\nvar b = [];\nif(a && a.length>0) {\n    for(var i = 0;i < a.length;i++) {\n        if(a[i].hasOwnProperty(\"_id\") && a[i][\"_id\"]) {\n            \n            b.push({\n                \"label\":a[i][\"_id\"],\n                \"value\":a[i][\"_id\"],\n            })\n        }\n    }\n}\noriginalStr = JSON.stringify(b)",
                    "udfRspType":"JSON"
                }
            }
        }],
        "rspKeyMap":{"groupList":"groupList"}
    },

    {
        "_id" : "IP_AUDIT_2.6_ACTIVE_DATE_LIST",
        "name" : "IP活跃日期列表",
        "repositoryOperateList" : [
            {
                "repository" : "MONGO",
                "collectionName" : "riskInfoAgg",
                "operateActionEnum" : "QUERY",
                "frontCriteriaList" : [
                    {
                        "property": "entities.type",
                        "predicate": "IS",
                        "value": "IP"
                    },
                    {
                        "property": "state",
                        "predicate": "IN",
                        "value": [NumberInt(0),NumberInt(2),NumberInt(3)]
                    }
                ],
                "metabaseGroupOperations" : [
                    {
                        "groupFields" : [
                            "date"
                        ]
                    }
                ],
                "fields" : [
                    "date"
                ],
                "operateRsp" : {
                    "key" : "dates",
                    "backend2FrontConvert" : {
                        "udf" : "var a=JSON.parse(originalStr);\nfor(var i = 0; i<a.length;i++){a[i]=a[i][\"_id\"]};\noriginalStr = JSON.stringify(a)",
                        "udfRspType" : "JSON"
                    }
                }
            }
        ],
        "rspKeyMap" : {
            "dates" : "dates"
        }
    },

    {
        "_id" : "ACCOUNT_AUDIT_2.6_ACTIVE_DATE_LIST",
        "name" : "账号活跃日期列表",
        "repositoryOperateList" : [
            {
                "repository" : "MONGO",
                "collectionName" : "riskInfoAgg",
                "operateActionEnum" : "QUERY",
                "frontCriteriaList" : [
                    {
                        "property": "entities.type",
                        "predicate": "IS",
                        "value": "ACCOUNT"
                    },
                    {
                        "property": "state",
                        "predicate": "IN",
                        "value": [NumberInt(0),NumberInt(2),NumberInt(3)]
                    }
                ],
                "metabaseGroupOperations" : [
                    {
                        "groupFields" : [
                            "date"
                        ]
                    }
                ],
                "fields" : [
                    "date"
                ],
                "operateRsp" : {
                    "key" : "dates",
                    "backend2FrontConvert" : {
                        "udf" : "var a=JSON.parse(originalStr);\nfor(var i = 0; i<a.length;i++){a[i]=a[i][\"_id\"]};\noriginalStr = JSON.stringify(a)",
                        "udfRspType" : "JSON"
                    }
                }
            }
        ],
        "rspKeyMap" : {
            "dates" : "dates"
        }
    },

    {
        "_id":"AUDIT_V2_USER_RELATE_USER_INFO",
        "name":"审计2.1用户画像用户关系",
        "repositoryOperateList":[{
            "repository":"MONGO",
            "collectionName":"accountDateInfo",
            "operateActionEnum":"QUERY",
            "frontCriteriaList":[],
            "metabaseGroupOperations":[{
                "groupFields":["account"],
                "aggregateOptionList":[{
                    "aggrType":"ADDTOSET",
                    "aggrField":"relatedIpList"
                },{
                    "aggrType":"ADDTOSET",
                    "aggrField":"uaByUaType"
                },{
                    "aggrType":"ADDTOSET",
                    "aggrField":"appUriList"
                }]
            }],
            "operateRsp":{
                "key":"accountInfo",
                "backend2FrontConvert":{
                    "udf":"var a=JSON.parse(originalStr);\nvar ipInfo=a[0];\n\n if(ipInfo[\"uaByUaType\"]&&ipInfo[\"uaByUaType\"].length>0){\n    var uaByUaType={};\n    for(var i=0;i<ipInfo[\"uaByUaType\"].length;i++){\n        for(var p in ipInfo[\"uaByUaType\"][i]){\n            if(uaByUaType[p]==null){\n                uaByUaType[p]=ipInfo[\"uaByUaType\"][i][p]\n            }else{\n                for(var j=0;j<ipInfo[\"uaByUaType\"][i][p].length;j++){\n                    if(uaByUaType[p].indexOf(ipInfo[\"uaByUaType\"][i][p][j])==-1){\n                        uaByUaType[p].push(ipInfo[\"uaByUaType\"][i][p][j])\n                    }\n                }\n            }\n        }\n    }\n    ipInfo[\"uaByUaType\"]=uaByUaType;\n } \n \n    if(ipInfo[\"relatedIpList\"]&&ipInfo[\"relatedIpList\"].length>0){\n        var relatedList=[];\n        for(var i=0;i<ipInfo[\"relatedIpList\"].length;i++){\n            for(var j=0;j<ipInfo[\"relatedIpList\"][i].length;j++){\n\n                if(relatedList.indexOf(ipInfo[\"relatedIpList\"][i][j])==-1){\n                    relatedList.push(ipInfo[\"relatedIpList\"][i][j])\n                }\n            }\n        }\n        ipInfo[\"relatedIpList\"]=relatedList;\n    }\n\nif(ipInfo[\"appUriList\"]&&ipInfo[\"appUriList\"].length>0){\n    var appUriList = [];\n    for(var i=0;i<ipInfo[\"appUriList\"].length;i++){\n        for(var j=0;j<ipInfo[\"appUriList\"][i].length;j++){\n\n            if(appUriList.indexOf(ipInfo[\"appUriList\"][i][j])==-1){\n                appUriList.push(ipInfo[\"appUriList\"][i][j])\n            }\n        }\n    }\n    ipInfo[\"appUriList\"]=appUriList;\n}\noriginalStr=JSON.stringify(ipInfo);",
                    "udfRspType":"JSON"
                }
            },
            "relateRepositoryOperate":[{
                "repository": "MONGO",
                "collectionName": "ipInfo",
                "operateActionEnum":"QUERY",
                "frontCriteriaList":[
                    {
                        "property":"ip",
                        "predicate":"IN",
                        "value":"relatedIpList",
                        "require":true
                    }
                ],
                "unwindField":"accessDomainIds",
                "metabaseGroupOperations":[{
                    "groupFields":["accessDomainIds"],
                    "aggregateOptionList":[{
                        "aggrType":"ADDTOSET",
                        "aggrField":"ip"
                    }]
                }],
                "operateRsp":{
                    "key":"ipList"
                }
            },{
                "repository": "MONGO",
                "collectionName": "httpApp",
                "operateActionEnum":"QUERY",
                "frontCriteriaList":[
                    {
                        "property":"uri",
                        "predicate":"IN",
                        "value":"appUriList",
                        "require":true
                    }
                ],
                "fields":["name","uri"],
                "operateRsp":{
                    "key":"appList"
                }
            }]
        }],
        "rspKeyMap":{"uaByUaType":"uaByUaType","ipList":"ipList","appList":"appList"},
        "frontObjUdf": {
            "uaByUaType":{
                "backendKeys":["accountInfo"],
                "udf":"var a = JSON.parse(originalStr); var b = a['accountInfo'];var c = b['uaByUaType']; var result = []; if(c != null) { for(var p in c) { result.push({'name':p,'list':c[p]})} } ;originalStr = JSON.stringify(result)",
                "udfRspType": "JSON"
            },
            "appList":{
                "backendKeys":["accountInfo","appList"],
                "udf":"var a = JSON.parse(originalStr); \nvar originAppList = a[\'accountInfo\'][\'appUriList\'];\nvar queryAppList = a[\'appList\']; \nvar queryAppMap = {};\n\nfor(var i = 0;i < queryAppList.length; i++){\n    queryAppMap[queryAppList[i][\'uri\']] = queryAppList[i][\'name\'];\n}\n\nvar result = []; \nvar resultKey = {};\nfor(var i = 0; i< originAppList.length;i++) {\n    \n    if(originAppList[i].indexOf(\"@QZKJMGC@\") != -1) {\n        originAppList[i] = originAppList[i].substring(0,originAppList[i].indexOf(\"@QZKJMGC@\"));\n    }\n    if(resultKey.hasOwnProperty(originAppList[i])) continue;\n    resultKey[originAppList[i]] = 1;\n    result.push({\n        \"uri\":originAppList[i],\n        \"name\":queryAppMap[originAppList[i]] || originAppList[i].substring(\"httpapp:\".length)\n    })\n}\n\noriginalStr = JSON.stringify(result)",
                "udfRspType": "JSON"
            }
        }
    },

    {
        "_id" : "FILE_AUDIT_2.6_ACTIVE_DATE_LIST",
        "name" : "账号活跃日期列表",
        "repositoryOperateList" : [
            {
                "repository" : "MONGO",
                "collectionName" : "ipDateInfo",
                "operateActionEnum" : "QUERY",
                "frontCriteriaList" : [],
                "metabaseGroupOperations" : [
                    {
                        "groupFields" : [
                            "date"
                        ]
                    }
                ],
                "fields" : [
                    "date"
                ],
                "operateRsp" : {
                    "key" : "dates",
                    "backend2FrontConvert" : {
                        "udf" : "var a=JSON.parse(originalStr);\nfor(var i = 0; i<a.length;i++){a[i]=a[i][\"_id\"]};\noriginalStr = JSON.stringify(a)",
                        "udfRspType" : "JSON"
                    }
                }
            }
        ],
        "rspKeyMap" : {
            "dates" : "dates"
        }
    }
]

for(var i = 0; i < actionConfigs.length;i++) {
    db.apiActionConfig.save(actionConfigs[i]);
}

for (var i = 0; i < reportActionConfigs.length; i++) {
    db.apiActionConfig.save(reportActionConfigs[i]);
}


