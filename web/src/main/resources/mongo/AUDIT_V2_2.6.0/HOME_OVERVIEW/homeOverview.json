[{"_id": "AUDIT_V2_2.6.0_HOME_WEAKNESS_DATA", "name": "首页弱点统计", "repositoryOperateList": [{"repository": "MONGO", "collectionName": "httpApiWeakness", "operateActionEnum": "COUNT", "frontCriteriaList": [{"property": "delFlag", "predicate": "IS", "value": false}, {"property": "state", "predicate": "IN", "value": ["NEW", "REPAIRING", "FIXED", "REOPEN"]}, {"property": "confirmState", "predicate": "IS", "value": "BANK_ASSET"}], "operateRsp": {"key": "totalWeaknessCnt", "name": "弱点总数"}}, {"repository": "MONGO", "collectionName": "httpApiWeakness", "operateActionEnum": "COUNT", "frontCriteriaList": [{"property": "delFlag", "predicate": "IS", "value": false}, {"property": "state", "predicate": "IS", "value": "NEW"}, {"property": "confirmState", "predicate": "IS", "value": "BANK_ASSET"}], "operateRsp": {"key": "notHandleWeaknessCnt", "name": "待处理弱点数"}}, {"repository": "MONGO", "collectionName": "httpApiWeakness", "operateActionEnum": "QUERY", "frontCriteriaList": [{"property": "delFlag", "predicate": "IS", "value": false}, {"property": "state", "predicate": "IN", "value": ["NEW", "REPAIRING", "FIXED", "REOPEN"]}, {"property": "confirmState", "predicate": "IS", "value": "BANK_ASSET"}], "metabaseGroupOperations": [{"groupFields": ["host"]}], "distinctField": "weakAppCnt", "operateRsp": {"key": "weakAppCnt", "name": "弱点应用", "backend2FrontConvert": {"udf": "var groupList = JSON.parse(originalStr); \noriginalStr = groupList[0][\"weakAppCnt\"].toString();", "udfRspType": "NUMBER"}}}, {"repository": "MONGO", "collectionName": "httpApiWeakness", "operateActionEnum": "QUERY", "frontCriteriaList": [{"property": "delFlag", "predicate": "IS", "value": false}, {"property": "state", "predicate": "IN", "value": ["NEW", "REPAIRING", "FIXED", "REOPEN"]}, {"property": "confirmState", "predicate": "IS", "value": "BANK_ASSET"}], "metabaseGroupOperations": [{"groupFields": ["uri"]}], "distinctField": "weakApiCnt", "operateRsp": {"key": "weakApiCnt", "name": "弱点接口", "backend2FrontConvert": {"udf": "var groupList = JSON.parse(originalStr); \noriginalStr = groupList[0][\"weakApiCnt\"].toString();", "udfRspType": "NUMBER"}}}, {"repository": "MONGO", "collectionName": "httpApp", "operateActionEnum": "COUNT", "frontCriteriaList": [{"property": "delFlag", "predicate": "IS", "value": false}, {"property": "confirmState", "predicate": "IS", "value": "BANK_ASSET"}], "operateRsp": {"key": "totalAppCnt"}}, {"repository": "MONGO", "collectionName": "httpApi", "operateActionEnum": "COUNT", "frontCriteriaList": [{"property": "delFlag", "predicate": "IS", "value": false}, {"property": "confirmState", "predicate": "IS", "value": "BANK_ASSET"}], "operateRsp": {"key": "totalApiCnt"}}], "rspKeyMap": {"totalWeaknessCnt": "totalWeaknessCnt", "notHandleWeaknessCnt": "notHandleWeaknessCnt", "weakAppCnt": "weakAppCnt", "weakApiCnt": "weakApiCnt", "totalAppCnt": "totalAppCnt", "totalApiCnt": "totalApiCnt"}}, {"_id": "AUDIT_V2_2.6.0_HOME_RISK_DATA", "name": "首页风险统计", "repositoryOperateList": [{"repository": "MONGO", "collectionName": "riskInfoAgg", "operateActionEnum": "COUNT", "frontCriteriaList": [{"property": "state", "predicate": "IN", "value": [0, 2, 3]}, {"property": "confirmState", "predicate": "IS", "value": "BANK_ASSET"}], "operateRsp": {"key": "totalRiskCnt", "name": "风险总数"}}, {"repository": "MONGO", "collectionName": "riskInfoAgg", "operateActionEnum": "COUNT", "frontCriteriaList": [{"property": "state", "predicate": "IS", "value": 0}, {"property": "confirmState", "predicate": "IS", "value": "BANK_ASSET"}], "operateRsp": {"key": "notHandleRiskCnt", "name": "待处理风险数"}}, {"repository": "MONGO", "collectionName": "riskInfoAgg", "operateActionEnum": "QUERY", "frontCriteriaList": [{"property": "entities.type", "predicate": "IS", "value": "IP"}, {"property": "state", "predicate": "IN", "value": [0, 2, 3]}, {"property": "confirmState", "predicate": "IS", "value": "BANK_ASSET"}], "unwindField": "entities", "metabaseGroupOperations": [{"groupFields": ["entities.value"]}], "distinctField": "ipExceptionCnt", "operateRsp": {"key": "ipExceptionCnt", "name": "风险IP数", "backend2FrontConvert": {"udf": "var a = JSON.parse(originalStr); originalStr = a[0] && a[0][\"ipExceptionCnt\"] ? a[0][\"ipExceptionCnt\"].toString() : \"0\"", "udfRspType": "NUMBER"}}}, {"repository": "MONGO", "collectionName": "accountInfo", "operateActionEnum": "COUNT", "frontCriteriaList": [], "operateRsp": {"key": "accountCnt"}}, {"repository": "MONGO", "collectionName": "ipInfo", "operateActionEnum": "COUNT", "frontCriteriaList": [], "operateRsp": {"key": "ipCnt"}}, {"repository": "MONGO", "collectionName": "riskInfoAgg", "operateActionEnum": "QUERY", "frontCriteriaList": [{"property": "entities.type", "predicate": "IS", "value": "ACCOUNT"}, {"property": "state", "predicate": "IN", "value": [0, 2, 3]}], "unwindField": "entities", "metabaseGroupOperations": [{"groupFields": ["entities.value"]}], "distinctField": "accountExceptionCnt", "operateRsp": {"key": "accountExceptionCnt", "backend2FrontConvert": {"udf": "var a = JSON.parse(originalStr); originalStr = a[0] && a[0][\"accountExceptionCnt\"] ? a[0][\"accountExceptionCnt\"].toString() : \"0\"", "udfRspType": "NUMBER"}}}], "rspKeyMap": {"totalRiskCnt": "totalRiskCnt", "notHandleRiskCnt": "notHandleRiskCnt", "ipExceptionCnt": "ipExceptionCnt", "ipCnt": "ipCnt", "accountCnt": "accountCnt", "accountExceptionCnt": "accountExceptionCnt"}}, {"_id": "AUDIT_V2_2.6.0_HOME_WEAKNESS_CREATE_THREAT_DATA", "name": "首页新增弱点走势图", "localMethodOperateList": [{"methodClassName": "LocalMethodTransformImpl", "methodName": "getDateList", "args": ["********", "********"], "operateRsp": {"key": "timeList"}}], "repositoryOperateList": [{"repository": "MONGO", "collectionName": "httpApiWeakness", "operateActionEnum": "QUERY", "frontCriteriaList": [{"property": "date", "predicate": "BETWEEN", "value": []}, {"property": "delFlag", "predicate": "IS", "value": false}, {"property": "state", "predicate": "IN", "value": ["NEW", "REPAIRING", "FIXED", "REOPEN"]}, {"property": "confirmState", "predicate": "IS", "value": "BANK_ASSET"}], "metabaseGroupOperations": [{"groupFields": ["date"], "aggregateOptionList": [{"aggrType": "COUNT", "aggrField": "COUNT"}]}], "operateRsp": {"key": "exceptionGroupList", "name": "弱点每天新增数"}}], "rspKeyMap": {"exceptionGroupMap": "exceptionGroupList"}, "frontObjUdf": {"exceptionGroupMap": {"backendKeys": ["timeList", "exceptionGroupList"], "udfRspType": "JSON", "udf": "var a = JSON.parse(originalStr);\nvar b = a[\"timeList\"];\nvar exceptionGroupList = a[\"exceptionGroupList\"];\nvar c = {};\n\nfor(var i = 0 ;i < b.length; i++) {\n    c[b[i]] = 0;\n}\nfor(var j = 0; j < exceptionGroupList.length;j++) {\n    \n    c[exceptionGroupList[j][\"_id\"]] = exceptionGroupList[j][\"COUNT\"]\n}\noriginalStr = JSON.stringify(c);\n"}}}, {"_id": "AUDIT_V2_2.6.0_HOME_RISK_CREATE_THREAT_DATA", "name": "首页新增风险走势图", "localMethodOperateList": [{"methodClassName": "LocalMethodTransformImpl", "methodName": "getDateList", "args": ["********", "********"], "operateRsp": {"key": "timeList"}}], "repositoryOperateList": [{"repository": "MONGO", "collectionName": "riskInfoAgg", "operateActionEnum": "QUERY", "frontCriteriaList": [{"property": "date", "predicate": "BETWEEN", "value": ["********", "********"]}, {"property": "state", "predicate": "IN", "value": [0, 2, 3]}, {"property": "confirmState", "predicate": "IS", "value": "BANK_ASSET"}], "metabaseGroupOperations": [{"groupFields": ["date"], "aggregateOptionList": [{"aggrType": "COUNT", "aggrField": "COUNT"}]}], "operateRsp": {"key": "exceptionGroupList"}}], "rspKeyMap": {"exceptionGroupMap": "exceptionGroupList"}, "frontObjUdf": {"exceptionGroupMap": {"backendKeys": ["timeList", "exceptionGroupList"], "udfRspType": "JSON", "udf": "var a = JSON.parse(originalStr);\nvar b = a[\"timeList\"];\nvar exceptionGroupList = a[\"exceptionGroupList\"];\nvar c = {};\n\nfor(var i = 0 ;i < b.length; i++) {\n    c[b[i]] = 0;\n}\nfor(var j = 0; j < exceptionGroupList.length;j++) {\n    \n    c[exceptionGroupList[j][\"_id\"]] = exceptionGroupList[j][\"COUNT\"]\n}\noriginalStr = JSON.stringify(c);\n"}}}, {"_id": "AUDIT_V2_2.6.0_HOME_APP_DATA", "name": "首页应用资产分布概览", "repositoryOperateList": [{"repository": "MONGO", "collectionName": "httpApp", "operateActionEnum": "COUNT", "frontCriteriaList": [{"property": "delFlag", "predicate": "IS", "value": false}, {"property": "confirmState", "predicate": "IS", "value": "BANK_ASSET"}], "operateRsp": {"key": "totalAppCnt", "name": "应用总数"}}, {"repository": "MONGO", "collectionName": "httpApiWeakness", "operateActionEnum": "QUERY", "frontCriteriaList": [{"property": "delFlag", "predicate": "IS", "value": false}, {"property": "state", "predicate": "IN", "value": ["NEW", "REPAIRING", "FIXED", "REOPEN"]}, {"property": "confirmState", "predicate": "IS", "value": "BANK_ASSET"}], "metabaseGroupOperations": [{"groupFields": ["appUri"]}], "distinctField": "weakAppCnt", "operateRsp": {"key": "weakAppCnt", "name": "弱点应用", "backend2FrontConvert": {"udf": "var groupList = JSON.parse(originalStr); \noriginalStr = groupList[0][\"weakAppCnt\"].toString();", "udfRspType": "NUMBER"}}}, {"repository": "MONGO", "collectionName": "httpApp", "operateActionEnum": "COUNT", "frontCriteriaList": [{"property": "delFlag", "predicate": "IS", "value": false}, {"property": "deployDomains", "predicate": "IS", "value": "互联网"}, {"property": "confirmState", "predicate": "IS", "value": "BANK_ASSET"}], "operateRsp": {"key": "deployDomainAppCnt", "name": "部署域含互联网"}}, {"repository": "MONGO", "collectionName": "httpApp", "operateActionEnum": "COUNT", "frontCriteriaList": [{"property": "delFlag", "predicate": "IS", "value": false}, {"property": "visitDomains", "predicate": "IS", "value": "互联网"}, {"property": "confirmState", "predicate": "IS", "value": "BANK_ASSET"}], "operateRsp": {"key": "visitDomianAppCnt", "name": "暴露在互联网的应用"}}, {"repository": "MONGO", "collectionName": "httpApp", "operateActionEnum": "COUNT", "frontCriteriaList": [{"property": "delFlag", "predicate": "IS", "value": false}, {"property": "labelStat.rspDataLabelCount", "predicate": "GT", "value": 0}, {"property": "confirmState", "predicate": "IS", "value": "BANK_ASSET"}], "operateRsp": {"key": "sensiAppCnt", "name": "返回携带敏感数据的应用"}}, {"repository": "MONGO", "collectionName": "httpApp", "operateActionEnum": "COUNT", "frontCriteriaList": [{"property": "delFlag", "predicate": "IS", "value": false}, {"property": "appLifeFlag", "predicate": "IS", "value": 3}, {"property": "confirmState", "predicate": "IS", "value": "BANK_ASSET"}], "operateRsp": {"key": "inActiveCnt", "name": "失活应用"}}], "rspKeyMap": {"totalAppCnt": "totalAppCnt", "weakAppCnt": "weakAppCnt", "deployDomainAppCnt": "deployDomainAppCnt", "visitDomianAppCnt": "visitDomianAppCnt", "sensiAppCnt": "sensiAppCnt", "inActiveCnt": "inActiveCnt"}}, {"_id": "AUDIT_V2_2.6.0_HOME_LABEL_GROUP_DATA", "name": "首页数据分级展示", "repositoryOperateList": [{"repository": "MONGO", "collectionName": "httpApi", "frontCriteriaList": [{"property": "delFlag", "predicate": "IS", "value": false}, {"property": "confirmState", "predicate": "IS", "value": "BANK_ASSET"}], "operateActionEnum": "QUERY", "unwindField": "reqDataLabels", "metabaseGroupOperations": [{"groupFields": ["reqDataLabels"], "aggregateOptionList": [{"aggrType": "COUNT", "aggrAsField": "count"}]}], "operateRsp": {"key": "reqDataLabels", "name": "请求标签聚合"}}, {"repository": "MONGO", "collectionName": "httpApi", "operateActionEnum": "QUERY", "frontCriteriaList": [{"property": "delFlag", "predicate": "IS", "value": false}, {"property": "confirmState", "predicate": "IS", "value": "BANK_ASSET"}], "unwindField": "rspDataLabels", "metabaseGroupOperations": [{"groupFields": ["rspDataLabels"], "aggregateOptionList": [{"aggrType": "COUNT", "aggrAsField": "count"}]}], "operateRsp": {"key": "rspDataLabels", "name": "返回标签聚合"}}, {"repository": "NACOS", "collectionName": "dataLabel", "operateActionEnum": "QUERY", "frontCriteriaList": [], "page": 1, "limit": 1000, "operateRsp": {"key": "dataLabelList"}}], "rspKeyMap": {"reqDataLabels": "reqDataLabels", "rspDataLabels": "rspDataLabels"}, "frontObjUdf": {"reqDataLabels": {"backendKeys": ["reqDataLabels", "dataLabelList"], "udf": "var a = JSON.parse(originalStr) ;\nvar dataLabelGroup = a[\"reqDataLabels\"];\nvar dataLabelList = a[\"dataLabelList\"];\nvar dataLabelMap = {};\nfor( var i = 0 ; i < dataLabelList.length;i++ ) {\n    dataLabelMap[ dataLabelList[i][\"id\"] ] =  dataLabelList[i];\n}\nvar dataLabelFirstClassMap = {\n    \"personal_basic_info\":\"个人基本资料\",\n    \"personal_identity_info\":\"个人身份信息\",\n    \"personal_property_info\":\"个人财产信息\",\n    \"personal_education_work_info\":\"个人教育工作信息\",\n    \"network_identity_info\":\"网络身份标识信息\",\n    \"personal_device_info\":\"个人常用设备信息\",\n    \"personal_communication_info\":\"个人通信信息\",\n    \"personal_health_info\":\"个人健康生理信息\",\n    \"personal_position_info\":\"个人位置信息\",\n    \"unclassified\":\"未分类\"\n}\n\nfor( var i = 0; i < dataLabelGroup.length;i++ ) {\n    \n    if(dataLabelMap[  dataLabelGroup[i][\"_id\"] ] ) {\n        dataLabelGroup[i][\"label\"] = dataLabelGroup[i][\"_id\"];\n        dataLabelGroup[i][\"name\"] = dataLabelMap[  dataLabelGroup[i][\"_id\"] ][\"name\"] ||  dataLabelGroup[i][\"label\"];\n        dataLabelGroup[i][\"labelLevel\"] =dataLabelMap[  dataLabelGroup[i][\"_id\"] ][\"labelLevel\"] || \"未分级\";\n        dataLabelGroup[i][\"labelFirstClass\"] = dataLabelMap[  dataLabelGroup[i][\"_id\"] ][\"firstClass\"] || \"\";\n        dataLabelGroup[i][\"labelFirstClassName\"] =dataLabelFirstClassMap[  dataLabelGroup[i][\"labelFirstClass\"] ] ||  dataLabelGroup[i][\"labelFirstClass\"] || \"未分类\";\n    }\n}\noriginalStr = JSON.stringify(dataLabelGroup);"}, "rspDataLabels": {"backendKeys": ["rspDataLabels", "dataLabelList"], "udf": "var a = JSON.parse(originalStr) ;\nvar dataLabelGroup = a[\"rspDataLabels\"];\nvar dataLabelList = a[\"dataLabelList\"];\nvar dataLabelMap = {};\nfor( var i = 0 ; i < dataLabelList.length;i++ ) {\n    dataLabelMap[ dataLabelList[i][\"id\"] ] =  dataLabelList[i];\n}\nvar dataLabelFirstClassMap = {\n    \"personal_basic_info\":\"个人基本资料\",\n    \"personal_identity_info\":\"个人身份信息\",\n    \"personal_property_info\":\"个人财产信息\",\n    \"personal_education_work_info\":\"个人教育工作信息\",\n    \"network_identity_info\":\"网络身份标识信息\",\n    \"personal_device_info\":\"个人常用设备信息\",\n    \"personal_communication_info\":\"个人通信信息\",\n    \"personal_health_info\":\"个人健康生理信息\",\n    \"personal_position_info\":\"个人位置信息\",\n    \"unclassified\":\"未分类\"\n}\n\nfor( var i = 0; i < dataLabelGroup.length;i++ ) {\n    \n    if( dataLabelMap[  dataLabelGroup[i][\"_id\"] ] ) {\n        dataLabelGroup[i][\"label\"] = dataLabelGroup[i][\"_id\"];\n        dataLabelGroup[i][\"name\"] =dataLabelMap[  dataLabelGroup[i][\"_id\"] ][\"name\"] ||  dataLabelGroup[i][\"label\"];\n        dataLabelGroup[i][\"labelLevel\"] =dataLabelMap[  dataLabelGroup[i][\"_id\"] ][\"labelLevel\"] || \"未分级\";\n        dataLabelGroup[i][\"labelFirstClass\"] =dataLabelMap[  dataLabelGroup[i][\"_id\"] ][\"firstClass\"] || \"\";\n        dataLabelGroup[i][\"labelFirstClassName\"] =dataLabelFirstClassMap[  dataLabelGroup[i][\"labelFirstClass\"] ] || dataLabelGroup[i][\"labelFirstClass\"] || \"\";\n    }\n    \n}\noriginalStr = JSON.stringify(dataLabelGroup);"}}}, {"_id": "AUDIT_V2_2.6.0_HOME_LIFE_FLAG_DATA", "name": "首页资产生命周期概览", "repositoryOperateList": [{"repository": "MONGO", "collectionName": "httpApp", "operateActionEnum": "QUERY", "unwindField": "appLifeFlag", "frontCriteriaList": [{"property": "delFlag", "predicate": "IS", "value": false}, {"property": "confirmState", "predicate": "IS", "value": "BANK_ASSET"}], "metabaseGroupOperations": [{"groupFields": ["appLifeFlag"], "aggregateOptionList": [{"aggrType": "COUNT", "aggrAsField": "count"}]}], "operateRsp": {"key": "appLifeGroup", "name": "应用的生命周期", "backend2FrontConvert": {"udf": "var a =JSON.parse(originalStr);\nvar lifeFlagMap = { \"1\":\"活跃\",\"2\":\"新增\",\"3\":\"失活\",\"4\":\"复活\" }\nfor(var i = 0; i < a.length;i++){\n   \n    a[i][\"label\"] = a[i][\"_id\"];\n    a[i][\"name\"] = lifeFlagMap[a[i][\"_id\"]];\n}\noriginalStr = JSON.stringify(a)", "udfRspType": "JSON"}}}, {"repository": "MONGO", "collectionName": "httpApi", "operateActionEnum": "QUERY", "unwindField": "apiLifeFlag", "frontCriteriaList": [{"property": "delFlag", "predicate": "IS", "value": false}, {"property": "confirmState", "predicate": "IS", "value": "BANK_ASSET"}], "metabaseGroupOperations": [{"groupFields": ["apiLifeFlag"], "aggregateOptionList": [{"aggrType": "COUNT", "aggrAsField": "count"}]}], "operateRsp": {"key": "apiLifeGroup", "name": "接口的生命周期", "backend2FrontConvert": {"udf": "var a =JSON.parse(originalStr);\nvar lifeFlagMap = { \"1\":\"活跃\",\"2\":\"新增\",\"3\":\"失活\",\"4\":\"复活\" }\nfor(var i = 0; i < a.length;i++){\n   \n    a[i][\"label\"] = a[i][\"_id\"];\n    a[i][\"name\"] = lifeFlagMap[a[i][\"_id\"]];\n}\noriginalStr = JSON.stringify(a)", "udfRspType": "JSON"}}}, {"repository": "MONGO", "collectionName": "accountInfo", "operateActionEnum": "QUERY", "unwindField": "accountLifeFlag", "metabaseGroupOperations": [{"groupFields": ["accountLifeFlag"], "aggregateOptionList": [{"aggrType": "COUNT", "aggrAsField": "count"}]}], "operateRsp": {"key": "accountLifeGroup", "name": "账号的生命周期", "backend2FrontConvert": {"udf": "var a =JSON.parse(originalStr);\nvar lifeFlagMap = { \"1\":\"活跃\",\"2\":\"新增\",\"3\":\"失活\",\"4\":\"复活\" }\nfor(var i = 0; i < a.length;i++){\n   \n    a[i][\"label\"] = a[i][\"_id\"];\n    a[i][\"name\"] = lifeFlagMap[a[i][\"_id\"]];\n}\noriginalStr = JSON.stringify(a)", "udfRspType": "JSON"}}}], "rspKeyMap": {"appLifeGroup": "appLifeGroup", "apiLifeGroup": "apiLifeGroup", "accountLifeGroup": "accountLifeGroup"}}, {"_id": "AUDIT_V2_2.6.0_HOME_ACCOUNT_DEPARTMENT_TO_APP_DEPARTMENT_DATA", "name": "首页部门访问流向图", "repositoryOperateList": [{"repository": "MONGO", "collectionName": "accountInfo", "operateActionEnum": "QUERY", "unwindField": "appDepartList", "metabaseGroupOperations": [{"groupFields": ["staff<PERSON>epart", "appDepartList"], "aggregateOptionList": [{"aggrType": "COUNT", "aggrAsField": "count"}]}], "operateRsp": {"key": "staffDepart2AppDepart", "name": "部门流向", "backend2FrontConvert": {"udf": "var a =JSON.parse(originalStr);\nvar b = [];\nfor(var i = 0; i < a.length;i++){\n   \n    if(a[i][\"staffDepart\"] && a[i][\"appDepartList\"]) {\n       b.push(a[i]);\n    }\n}\noriginalStr = JSON.stringify(b)", "udfRspType": "JSON"}}}], "rspKeyMap": {"groupList": "staffDepart2AppDepart"}}, {"_id": "AUDIT_V2_2.6.0_HOME_ACCOUNT_DEPARTMENT_TO_APP_DEPARTMENT_DATA_VIEW", "name": "部门访问流向图", "repositoryOperateList": [{"repository": "MONGO", "collectionName": "homeData", "operateActionEnum": "FINDONE", "id": "AUDIT_V2_2.6.0_HOME_ACCOUNT_DEPARTMENT_TO_APP_DEPARTMENT_DATA", "operateRsp": {"key": "result"}}], "rspKeyMap": {"groupList": "result", "updateTime": "result"}, "frontObjUdf": {"groupList": {"backendKeys": ["result"], "udf": "var a = JSON.parse(originalStr) ; originalStr = JSON.stringify( a['result']['groupList']); "}, "updateTime": {"backendKeys": ["result"], "udf": "var a = JSON.parse(originalStr) ; originalStr = a['result']['updateTime'].toString(); ", "udfRspType": "NUMBER"}}}, {"_id": "AUDIT_V2_2.6.0_HOME_LIFE_FLAG_DATA_VIEW", "name": "资产生命周期概览", "repositoryOperateList": [{"repository": "MONGO", "collectionName": "homeData", "operateActionEnum": "FINDONE", "id": "AUDIT_V2_2.6.0_HOME_LIFE_FLAG_DATA", "operateRsp": {"key": "result"}}], "rspKeyMap": {"accountLifeGroup": "result", "apiLifeGroup": "result", "appLifeGroup": "result", "updateTime": "result"}, "frontObjUdf": {"accountLifeGroup": {"backendKeys": ["result"], "udf": "var a = JSON.parse(originalStr) ; originalStr = JSON.stringify( a['result']['accountLifeGroup']); "}, "apiLifeGroup": {"backendKeys": ["result"], "udf": "var a = JSON.parse(originalStr) ; originalStr = JSON.stringify( a['result']['apiLifeGroup']); "}, "appLifeGroup": {"backendKeys": ["result"], "udf": "var a = JSON.parse(originalStr) ; originalStr = JSON.stringify( a['result']['appLifeGroup']); "}, "updateTime": {"backendKeys": ["result"], "udf": "var a = JSON.parse(originalStr) ; originalStr = a['result']['updateTime'].toString(); ", "udfRspType": "NUMBER"}}}, {"_id": "AUDIT_V2_2.6.0_HOME_APP_DATA_VIEW", "name": "应用资产分布概览", "repositoryOperateList": [{"repository": "MONGO", "collectionName": "homeData", "operateActionEnum": "FINDONE", "id": "AUDIT_V2_2.6.0_HOME_APP_DATA", "operateRsp": {"key": "result"}}], "rspKeyMap": {"result": "result"}}, {"_id": "AUDIT_V2_2.6.0_HOME_LABEL_GROUP_DATA_VIEW", "name": "应用资产分布概览", "repositoryOperateList": [{"repository": "MONGO", "collectionName": "homeData", "operateActionEnum": "FINDONE", "id": "AUDIT_V2_2.6.0_HOME_LABEL_GROUP_DATA", "operateRsp": {"key": "result"}}], "rspKeyMap": {"rspDataLabels": "result", "reqDataLabels": "result", "updateTime": "result"}, "frontObjUdf": {"rspDataLabels": {"backendKeys": ["result"], "udf": "var a = JSON.parse(originalStr) ; originalStr = JSON.stringify( a['result']['rspDataLabels']); "}, "reqDataLabels": {"backendKeys": ["result"], "udf": "var a = JSON.parse(originalStr) ; originalStr = JSON.stringify( a['result']['reqDataLabels']); "}, "updateTime": {"backendKeys": ["result"], "udf": "var a = JSON.parse(originalStr) ; originalStr = a['result']['updateTime'].toString(); ", "udfRspType": "NUMBER"}}}, {"_id": "AUDIT_V2_2.6.0_HOME_RISK_CREATE_THREAT_DATA_VIEW", "name": "风险走势图", "repositoryOperateList": [{"repository": "MONGO", "collectionName": "homeData", "operateActionEnum": "FINDONE", "id": "AUDIT_V2_2.6.0_HOME_RISK_CREATE_THREAT_DATA", "operateRsp": {"key": "result"}}], "rspKeyMap": {"exceptionGroupMap": "result", "updateTime": "result"}, "frontObjUdf": {"exceptionGroupMap": {"backendKeys": ["result"], "udf": "var a = JSON.parse(originalStr) ; originalStr = JSON.stringify( a['result']['exceptionGroupMap']); "}, "updateTime": {"backendKeys": ["result"], "udf": "var a = JSON.parse(originalStr) ; originalStr = a['result']['updateTime'].toString(); ", "udfRspType": "NUMBER"}}}, {"_id": "AUDIT_V2_2.6.0_HOME_WEAKNESS_CREATE_THREAT_DATA_VIEW", "name": "弱点走势图", "repositoryOperateList": [{"repository": "MONGO", "collectionName": "homeData", "operateActionEnum": "FINDONE", "id": "AUDIT_V2_2.6.0_HOME_WEAKNESS_CREATE_THREAT_DATA", "operateRsp": {"key": "result"}}], "rspKeyMap": {"exceptionGroupMap": "result", "updateTime": "result"}, "frontObjUdf": {"exceptionGroupMap": {"backendKeys": ["result"], "udf": "var a = JSON.parse(originalStr) ; originalStr = JSON.stringify( a['result']['exceptionGroupMap']); "}, "updateTime": {"backendKeys": ["result"], "udf": "var a = JSON.parse(originalStr) ; originalStr = a['result']['updateTime'].toString(); ", "udfRspType": "NUMBER"}}}, {"_id": "AUDIT_V2_2.6.0_HOME_RISK_DATA_VIEW", "name": "风险统计", "repositoryOperateList": [{"repository": "MONGO", "collectionName": "homeData", "operateActionEnum": "FINDONE", "id": "AUDIT_V2_2.6.0_HOME_RISK_DATA", "operateRsp": {"key": "result"}}], "rspKeyMap": {"result": "result"}}, {"_id": "AUDIT_V2_2.6.0_HOME_WEAKNESS_DATA_VIEW", "name": "弱点统计", "repositoryOperateList": [{"repository": "MONGO", "collectionName": "homeData", "operateActionEnum": "FINDONE", "id": "AUDIT_V2_2.6.0_HOME_WEAKNESS_DATA", "operateRsp": {"key": "result"}}], "rspKeyMap": {"result": "result"}}, {"_id": "AUDIT_V2_2.6.0_HOME_EVENT_CHART_VIEW", "name": "系统流量趋势", "repositoryOperateList": [{"repository": "MONGO", "collectionName": "eventDateInfo", "operateActionEnum": "QUERY", "frontCriteriaList": [{"property": "date", "predicate": "BETWEEN", "value": []}], "operateRsp": {"key": "result"}}], "rspKeyMap": {"result": "result"}}, {"_id": "AUDIT_V2_2.6.0_HOME_EVENT_TODAY_VIEW", "name": "系统今日流量", "repositoryOperateList": [{"repository": "MONGO", "collectionName": "eventDateInfo", "operateActionEnum": "FINDONE", "frontCriteriaList": [{"property": "date", "predicate": "IS", "value": ""}], "operateRsp": {"key": "count", "backend2FrontConvert": {"udf": "var a =JSON.parse(originalStr);\noriginalStr = a[\"count\"].toString();", "udfRspType": "JSON"}}}], "rspKeyMap": {"count": "count"}}, {"_id": "AUDIT_V2_2.6.2_SYSTEM_EVENT_COUNT", "name": "系统流量(全链路)", "repositoryOperateList": [{"repository": "MONGO", "collectionName": "eventDateInfo", "operateActionEnum": "QUERY", "frontCriteriaList": [{"property": "date", "predicate": "IS", "value": ""}], "operateRsp": {"key": "list"}}], "rspKeyMap": {"list": "list"}}, {"_id": "AUDIT_V2_2.7.0_HOME_FULL_FLOW_DATA", "name": "首页访问量统计", "repositoryOperateList": [{"repository": "MONGO", "collectionName": "httpAppDateStat", "operateActionEnum": "QUERY", "frontCriteriaList": [{"property": "date", "predicate": "IS", "value": "20220721"}], "metabaseGroupOperations": [{"groupFields": ["date"], "aggregateOptionList": [{"aggrType": "SUM", "aggrField": "dailyAmount"}]}], "operateRsp": {"key": "eventDateInfo", "backend2FrontConvert": {"udf": "var a =JSON.parse(originalStr); \nif(a && a.length > 0) { \n    originalStr = a[0][\"dailyAmount\"].toString()\n} else {\n    originalStr = \"0\"\n}", "udfRspType": "NUMBER"}}}], "rspKeyMap": {"eventDateInfo": "eventDateInfo"}}]