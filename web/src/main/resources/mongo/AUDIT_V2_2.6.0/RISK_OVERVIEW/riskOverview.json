[{"_id": "AUDIT_V2_2.6.0_RISK_OVERVIEW_TOP_DATA", "name": "风险概览顶部数据", "currentWeekBaseDateTimestamp": "CURRENT_WEEK_BASE_DATE", "repositoryOperateList": [{"repository": "MONGO", "collectionName": "riskInfoAgg", "operateActionEnum": "COUNT", "frontCriteriaList": [{"property": "state", "predicate": "IN", "value": [1, 2, 3]}, {"property": "operateTime", "predicate": "BETWEEN", "value": ["START_TIMESTAMP_OF_CURRENT_WEEK", "END_TIMESTAMP_OF_CURRENT_WEEK"]}, {"property": "confirmState", "predicate": "IS", "value": "BANK_ASSET"}], "operateRsp": {"key": "currentWeekOperateCnt", "name": "本周已处理风险（不包括状态为未确认风险）"}}, {"repository": "MONGO", "collectionName": "riskInfoAgg", "operateActionEnum": "COUNT", "frontCriteriaList": [{"property": "state", "predicate": "IN", "value": [0, 2, 3]}, {"property": "createTime", "predicate": "BETWEEN", "value": ["START_TIMESTAMP_OF_CURRENT_WEEK", "END_TIMESTAMP_OF_CURRENT_WEEK"]}, {"property": "confirmState", "predicate": "IS", "value": "BANK_ASSET"}], "operateRsp": {"key": "currentWeekNewWithoutIgnoreCnt", "name": "本周新增风险（不包括状态为已忽略风险）"}}, {"repository": "MONGO", "collectionName": "riskInfoAgg", "operateActionEnum": "COUNT", "frontCriteriaList": [{"property": "state", "predicate": "IS", "value": 0}, {"property": "createTime", "predicate": "BETWEEN", "value": ["START_TIMESTAMP_OF_CURRENT_WEEK", "END_TIMESTAMP_OF_CURRENT_WEEK"]}, {"property": "confirmState", "predicate": "IS", "value": "BANK_ASSET"}], "operateRsp": {"key": "curentWeekNotHandleCnt", "name": "本周新增待处理风险"}}, {"repository": "MONGO", "collectionName": "riskInfoAgg", "operateActionEnum": "COUNT", "frontCriteriaList": [{"property": "state", "predicate": "IS", "value": 2}, {"property": "createTime", "predicate": "BETWEEN", "value": ["START_TIMESTAMP_OF_CURRENT_WEEK", "END_TIMESTAMP_OF_CURRENT_WEEK"]}, {"property": "confirmState", "predicate": "IS", "value": "BANK_ASSET"}], "operateRsp": {"key": "curentWeekHasHandleCnt", "name": "本周新增已确认"}}], "rspKeyMap": {"currentWeekOperateCnt": "currentWeekOperateCnt", "currentWeekNewWithoutIgnoreCnt": "currentWeekNewWithoutIgnoreCnt", "curentWeekNotHandleCnt": "curentWeekNotHandleCnt", "curentWeekHasHandleCnt": "curentWeekHasHandleCnt"}}, {"_id": "AUDIT_V2_2.6.0_RISK_OVERVIEW_POLICY_GROUP", "name": "现存待处理风险策略聚合", "repositoryOperateList": [{"repository": "MONGO", "collectionName": "riskInfoAgg", "operateActionEnum": "QUERY", "frontCriteriaList": [{"property": "state", "predicate": "IS", "value": 0}, {"property": "confirmState", "predicate": "IS", "value": "BANK_ASSET"}], "sort": [{"property": "count", "order": "DESC"}, {"property": "_id", "order": "DESC"}], "metabaseGroupOperations": [{"groupFields": ["policySnapshot.riskPolicyId"], "aggregateOptionList": [{"aggrType": "COUNT", "aggrField": "count"}, {"aggrType": "FIRST", "aggrField": "policySnapshot.name", "aggrAsField": "name"}, {"aggrType": "FIRST", "aggrField": "level", "aggrAsField": "level"}]}], "operateRsp": {"key": "list", "backend2FrontConvert": {"udf": "var a =JSON.parse(originalStr);\nvar b=[];\nvar levelMap = { \"3\":\"高\",\"2\":\"中\",\"1\":\"低\"};\nfor(var i = 0; i < a.length;i++){\n    b.push({\n        \"_id\":a[i][\"_id\"],\n        \"label\":a[i][\"_id\"],\n        \"name\":a[i][\"name\"],\n        \"count\":a[i][\"count\"],\n        \"level\":a[i][\"level\"],\n        \"levelName\":levelMap[a[i][\"level\"]] || \"低\"\n    });\n}\noriginalStr = JSON.stringify(b)", "udfRspType": "JSON"}}}], "rspKeyMap": {"groupList": "list"}}, {"_id": "AUDIT_V2_2.6.0_RISK_OVERVIEW_DATE_STATE_GROUP", "name": "风险状态走势", "repositoryOperateList": [{"repository": "MONGO", "collectionName": "riskInfoAgg", "operateActionEnum": "QUERY", "frontCriteriaList": [{"property": "state", "predicate": "IN", "value": [0, 2, 3]}, {"property": "date", "predicate": "LTE", "value": ""}, {"property": "confirmState", "predicate": "IS", "value": "BANK_ASSET"}], "sort": [{"property": "count", "order": "DESC"}], "metabaseGroupOperations": [{"groupFields": ["state"], "aggregateOptionList": [{"aggrType": "COUNT", "aggrField": "count"}]}], "operateRsp": {"key": "list", "backend2FrontConvert": {"udf": "var a =JSON.parse(originalStr);\nvar b= {\"0\":\"待确认\",\"1\":\"已忽略\",\"2\":\"已确认\",\"3\":\"观察中\"};\nvar totalCnt = 0;\nfor(var i = 0; i < a.length;i++){\n    a[i][\"label\"] = a[i][\"_id\"];\n    a[i][\"name\"] = b[a[i][\"_id\"]];\n    totalCnt += a[i][\"count\"];\n}\na.push({\"name\":\"总量\",\"label\":\"总量\",count:totalCnt});\noriginalStr = JSON.stringify(a)", "udfRspType": "JSON"}}}], "rspKeyMap": {"groupList": "list"}}, {"_id": "AUDIT_V2_2.6.0_RISK_OVERVIEW_ENTITY_IP_GROUP", "name": "现存风险IP主体TOP10", "repositoryOperateList": [{"repository": "MONGO", "collectionName": "riskInfoAgg", "operateActionEnum": "QUERY", "frontCriteriaList": [{"property": "state", "predicate": "IN", "value": [0, 2, 3]}, {"property": "entities.type", "predicate": "IS", "value": "IP"}, {"property": "confirmState", "predicate": "IS", "value": "BANK_ASSET"}], "sort": [{"property": "count", "order": "DESC"}, {"property": "_id", "order": "DESC"}], "page": 1, "limit": 10, "unwindField": "entities", "metabaseGroupOperations": [{"groupFields": ["entities.value"], "aggregateOptionList": [{"aggrType": "COUNT", "aggrField": "count"}, {"aggrType": "FIRST", "aggrField": "domainIds"}]}], "operateRsp": {"key": "list", "backend2FrontConvert": {"udf": "var a =JSON.parse(originalStr);\nfor(var i = 0; i < a.length;i++){\n   \n    a[i][\"label\"] = a[i][\"_id\"];\n    a[i][\"name\"] = a[i][\"domainIds\"] ?  a[i][\"_id\"] + \"-\" + a[i][\"domainIds\"].join(\",\") : a[i][\"_id\"];\n}\noriginalStr = JSON.stringify(a)", "udfRspType": "JSON"}}}], "rspKeyMap": {"groupList": "list"}}, {"_id": "AUDIT_V2_2.6.0_RISK_OVERVIEW_ENTITY_ACCOUNT_GROUP", "name": "现存风险账号主体TOP10", "repositoryOperateList": [{"repository": "MONGO", "collectionName": "riskInfoAgg", "operateActionEnum": "QUERY", "frontCriteriaList": [{"property": "state", "predicate": "IN", "value": [0, 2, 3]}, {"property": "entities.type", "predicate": "IS", "value": "ACCOUNT"}, {"property": "confirmState", "predicate": "IS", "value": "BANK_ASSET"}], "sort": [{"property": "count", "order": "DESC"}], "page": 1, "limit": 10, "unwindField": "entities", "metabaseGroupOperations": [{"groupFields": ["entities.value"], "aggregateOptionList": [{"aggrType": "COUNT", "aggrField": "count"}, {"aggrType": "FIRST", "aggrField": "depart"}]}], "operateRsp": {"key": "list", "backend2FrontConvert": {"udf": "var a =JSON.parse(originalStr);\nfor(var i = 0; i < a.length;i++){\n   \n    a[i][\"label\"] = a[i][\"_id\"];\n    a[i][\"name\"] = a[i][\"depart\"] ?  a[i][\"_id\"] + \"-\" + a[i][\"depart\"] :  a[i][\"_id\"];\n}\noriginalStr = JSON.stringify(a)", "udfRspType": "JSON"}}}], "rspKeyMap": {"groupList": "list"}}, {"_id": "AUDIT_V2_2.6.0_RISK_OVERVIEW_TOP_DATA_VIEW", "name": "风险概览顶部数据", "repositoryOperateList": [{"repository": "MONGO", "collectionName": "riskOverviewTopDataDateInfo", "operateActionEnum": "FINDONE", "frontCriteriaList": [{"property": "date", "predicate": "IS", "value": "START_DATE_OF_CURRENT_WEEK"}], "operateRsp": {"key": "currentWeekTopView", "name": "本周风险数据"}}, {"repository": "MONGO", "collectionName": "riskOverviewTopDataDateInfo", "operateActionEnum": "FINDONE", "frontCriteriaList": [{"property": "date", "predicate": "IS", "value": "START_DATE_OF_LAST_WEEK"}], "operateRsp": {"key": "lastWeekTopView", "name": "上周风险数据"}}], "rspKeyMap": {"currentWeekTopView": "currentWeekTopView", "lastWeekTopView": "lastWeekTopView"}}, {"_id": "AUDIT_V2_2.6.0_RISK_OVERVIEW_POLICY_GROUP_VIEW", "name": "现存待处理风险策略聚合", "repositoryOperateList": [{"repository": "MONGO", "collectionName": "riskOverviewData", "operateActionEnum": "FINDONE", "id": "AUDIT_V2_2.6.0_RISK_OVERVIEW_POLICY_GROUP", "operateRsp": {"key": "result"}}], "rspKeyMap": {"groupList": "result", "updateTime": "result"}, "frontObjUdf": {"groupList": {"backendKeys": ["result"], "udf": "var a = JSON.parse(originalStr) ; originalStr = JSON.stringify( a['result']['groupList']); "}, "updateTime": {"backendKeys": ["result"], "udf": "var a = JSON.parse(originalStr) ; originalStr = a['result']['updateTime'].toString(); ", "udfRspType": "NUMBER"}}}, {"_id": "AUDIT_V2_2.6.0_RISK_OVERVIEW_DATE_STATE_GROUP_VIEW", "name": "风险状态走势", "repositoryOperateList": [{"repository": "MONGO", "collectionName": "riskDateStateInfo", "operateActionEnum": "QUERY", "frontCriteriaList": [{"property": "date", "predicate": "BETWEEN", "value": []}], "operateRsp": {"key": "result"}}], "rspKeyMap": {"result": "result"}}, {"_id": "AUDIT_V2_2.6.0_RISK_OVERVIEW_ENTITY_IP_GROUP_VIEW", "name": "现存风险IP主体TOP10", "repositoryOperateList": [{"repository": "MONGO", "collectionName": "riskOverviewData", "operateActionEnum": "FINDONE", "id": "AUDIT_V2_2.6.0_RISK_OVERVIEW_ENTITY_IP_GROUP", "operateRsp": {"key": "result"}}], "rspKeyMap": {"groupList": "result", "updateTime": "result"}, "frontObjUdf": {"groupList": {"backendKeys": ["result"], "udf": "var a = JSON.parse(originalStr) ; originalStr = JSON.stringify( a['result']['groupList']); "}, "updateTime": {"backendKeys": ["result"], "udf": "var a = JSON.parse(originalStr) ; originalStr = a['result']['updateTime'].toString(); ", "udfRspType": "NUMBER"}}}, {"_id": "AUDIT_V2_2.6.0_RISK_OVERVIEW_ENTITY_ACCOUNT_GROUP_VIEW", "name": "现存风险IP主体TOP10", "repositoryOperateList": [{"repository": "MONGO", "collectionName": "riskOverviewData", "operateActionEnum": "FINDONE", "id": "AUDIT_V2_2.6.0_RISK_OVERVIEW_ENTITY_ACCOUNT_GROUP", "operateRsp": {"key": "result"}}], "rspKeyMap": {"groupList": "result", "updateTime": "result"}, "frontObjUdf": {"groupList": {"backendKeys": ["result"], "udf": "var a = JSON.parse(originalStr) ; originalStr = JSON.stringify( a['result']['groupList']); "}, "updateTime": {"backendKeys": ["result"], "udf": "var a = JSON.parse(originalStr) ; originalStr = a['result']['updateTime'].toString(); ", "udfRspType": "NUMBER"}}}]