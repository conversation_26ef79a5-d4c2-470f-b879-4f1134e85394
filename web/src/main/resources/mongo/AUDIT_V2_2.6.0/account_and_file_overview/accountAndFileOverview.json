[{"_id": "ACCOUNT_LIST_COUNT", "name": "账号总数", "repositoryOperateList": [{"repository": "MONGO", "collectionName": "accountInfo", "operateActionEnum": "COUNT", "operateRsp": {"key": "totalCnt"}}], "rspKeyMap": {"totalCnt": "totalCnt"}}, {"_id": "RISK_INFO_AGG_ACCOUNT_TOP10", "name": "风险账号top10", "repositoryOperateList": [{"repository": "MONGO", "collectionName": "riskInfoAgg", "operateActionEnum": "QUERY", "frontCriteriaList": [{"property": "date", "predicate": "IS", "value": ""}, {"property": "entities.type", "predicate": "IS", "value": "ACCOUNT"}, {"property": "state", "predicate": "IN", "value": [0, 2, 3]}], "unwindField": "entities", "metabaseGroupOperations": [{"groupFields": ["entities.value"], "aggregateOptionList": [{"aggrType": "COUNT", "aggrField": "count"}]}], "sort": [{"property": "count", "order": "DESC"}, {"property": "_id", "order": "DESC"}], "page": 1, "limit": 10, "operateRsp": {"key": "groupList", "backend2FrontConvert": {"udf": "var groupList = JSON.parse(originalStr);\nfor(var i = 0;i < groupList.length;i++) {\n    \n    groupList[i][\"label\"] = groupList[i][\"_id\"]; \n    groupList[i][\"name\"] = groupList[i][\"_id\"]\n}; \noriginalStr = JSON.stringify(groupList)", "udfRspType": "JSON"}}}], "rspKeyMap": {"groupList": "groupList"}}, {"_id": "ACCOUNT_RISK_INFO_AGG_GROUP_LIST", "name": "账号对应的风险策略分组", "repositoryOperateList": [{"repository": "MONGO", "collectionName": "riskInfoAgg", "operateActionEnum": "QUERY", "frontCriteriaList": [{"property": "date", "predicate": "IS", "value": ""}, {"property": "entities.type", "predicate": "IS", "value": "ACCOUNT"}, {"property": "state", "predicate": "IN", "value": [0, 2, 3]}, {"property": "entities.value", "predicate": "IS", "value": ""}], "metabaseGroupOperations": [{"groupFields": ["policySnapshot.riskPolicyId"], "aggregateOptionList": [{"aggrType": "COUNT", "aggrField": "count"}, {"aggrType": "FIRST", "aggrField": "policySnapshot"}]}], "sort": [{"property": "count", "order": "DESC"}, {"property": "_id", "order": "DESC"}], "operateRsp": {"key": "groupList", "backend2FrontConvert": {"udf": "var a; \na=JSON.parse(originalStr); \nvar b=[];\nfor(var i = 0; i < a.length;i++){\n    b.push({\n        '_id':a[i]['_id'],\n        'label':a[i]['_id'],\n        'name':a[i]['policySnapshot']['name'],\n        'count':a[i]['count']\n    })\n} \noriginalStr = JSON.stringify(b)", "udfRspType": "JSON"}}}], "rspKeyMap": {"groupList": "groupList"}}, {"_id": "RISK_INFO_AGG_DEPART_DISTRIBUTE", "name": "账号风险部门", "repositoryOperateList": [{"repository": "MONGO", "collectionName": "riskInfoAgg", "operateActionEnum": "QUERY", "frontCriteriaList": [{"property": "date", "predicate": "IS", "value": ""}, {"property": "entities.type", "predicate": "IS", "value": "ACCOUNT"}, {"property": "state", "predicate": "IN", "value": [0, 2, 3]}], "metabaseGroupOperations": [{"groupFields": ["depart"], "aggregateOptionList": [{"aggrType": "COUNT", "aggrField": "count"}]}], "sort": [{"property": "count", "order": "DESC"}], "operateRsp": {"key": "groupList", "backend2FrontConvert": {"udf": "var a = JSON.parse(originalStr);\nvar b=[];\nfor(var i = 0;i < a.length;i++) {\n    if(a[i]['_id'] && a[i]['_id'].length > 0){\n        b.push({\n            \"count\": a[i][\"count\"],\n            \"label\" : a[i][\"_id\"],\n            \"name\" : a[i][\"_id\"]\n        });\n    }\n}\noriginalStr = JSON.stringify(b)", "udfRspType": "JSON"}}}], "rspKeyMap": {"groupList": "groupList"}}, {"_id": "ACCOUNT_DEPART_RISK_INFO_AGG_GROUP_LIST", "name": "账号部门对应的风险策略分组", "repositoryOperateList": [{"repository": "MONGO", "collectionName": "riskInfoAgg", "operateActionEnum": "QUERY", "frontCriteriaList": [{"property": "date", "predicate": "IS", "value": ""}, {"property": "entities.type", "predicate": "IS", "value": "ACCOUNT"}, {"property": "state", "predicate": "IN", "value": [0, 2, 3]}, {"property": "depart", "predicate": "IS", "value": ""}], "metabaseGroupOperations": [{"groupFields": ["policySnapshot.riskPolicyId"], "aggregateOptionList": [{"aggrType": "COUNT", "aggrField": "count"}, {"aggrType": "FIRST", "aggrField": "policySnapshot"}]}], "sort": [{"property": "count", "order": "DESC"}, {"property": "_id", "order": "DESC"}], "operateRsp": {"key": "groupList", "backend2FrontConvert": {"udf": "var a; \na=JSON.parse(originalStr); \nvar b=[];\nfor(var i = 0; i < a.length;i++){\n    b.push({\n        '_id':a[i]['_id'],\n        'label':a[i]['_id'],\n        'name':a[i]['policySnapshot']['name'],\n        'count':a[i]['count']\n    })\n} \noriginalStr = JSON.stringify(b)", "udfRspType": "JSON"}}}], "rspKeyMap": {"groupList": "groupList"}}, {"_id": "ACCOUNT_DATE_TOP10", "name": "账号单日去重数据量top10", "repositoryOperateList": [{"repository": "MONGO", "collectionName": "accountDateInfo", "operateActionEnum": "QUERY", "frontCriteriaList": [{"property": "date", "predicate": "IS", "value": ""}], "fields": ["account", "rspDataDistinctCnt"], "sort": [{"property": "rspDataDistinctCnt", "order": "DESC"}, {"property": "account", "order": "DESC"}], "page": 1, "limit": 10, "operateRsp": {"key": "groupList", "backend2FrontConvert": {"udf": "var a;\na=JSON.parse(originalStr);\nvar b=[];\nfor(var i = 0; i < a.length;i++){\n    if(a[i]['rspDataDistinctCnt'] != null) {\n        b.push({\n            '_id': a[i]['_id'],\n            'account': a[i]['account'],\n            'rspDataDistinctCnt': a[i]['rspDataDistinctCnt']\n        })\n    }\n}\noriginalStr = JSON.stringify(b)", "udfRspType": "JSON"}}}], "rspKeyMap": {"groupList": "groupList"}}, {"_id": "APP_AUDIT_2.6_ACCOUNT_LIFE_STAT", "name": "账号生命状态统计", "repositoryOperateList": [{"repository": "MONGO", "collectionName": "accountLifeDateStat", "operateActionEnum": "FINDONE", "frontCriteriaList": [{"property": "date", "predicate": "IS", "value": ""}], "operateRsp": {"key": "accountLifeCnt"}}], "rspKeyMap": {"accountLifeCnt": "accountLifeCnt"}}, {"_id": "AUDIT_V2_FILE_COUNT", "name": "文件总数/涉敏文件数", "repositoryOperateList": [{"repository": "MONGO", "collectionName": "fileInfo", "operateActionEnum": "COUNT", "frontCriteriaList": [], "operateRsp": {"key": "totalCount"}}, {"repository": "MONGO", "collectionName": "fileInfo", "operateActionEnum": "COUNT", "frontCriteriaList": [{"property": "dataDistinctCnt", "predicate": "GT", "value": 0}], "operateRsp": {"key": "sensitiveCount"}}], "rspKeyMap": {"totalCount": "totalCount", "sensitiveCount": "sensitiveCount"}}, {"_id": "DOWNLOAD_FILE_IP_TOP10", "name": "IP下载文件TOP10", "repositoryOperateList": [{"repository": "MONGO", "collectionName": "ipDateInfo", "operateActionEnum": "QUERY", "frontCriteriaList": [{"property": "date", "predicate": "IS", "value": "********"}, {"property": "downloadFileDistinctCnt", "predicate": "GT", "value": 0}], "sort": [{"property": "downloadFileDistinctCnt", "order": "DESC"}], "page": 1, "limit": 10, "fields": ["ip", "downloadFileDistinctCnt"], "operateRsp": {"key": "downloadList"}}], "rspKeyMap": {"groupList": "downloadList"}}, {"_id": "DOWNLOAD_FILE_ACCOUNT_TOP10", "name": "账号下载文件TOP10", "repositoryOperateList": [{"repository": "MONGO", "collectionName": "accountDateInfo", "operateActionEnum": "QUERY", "frontCriteriaList": [{"property": "downloadFileDistinctCnt", "predicate": "GT", "value": 0}], "sort": [{"property": "downloadFileDistinctCnt", "order": "DESC"}], "page": 1, "limit": 10, "fields": ["account", "downloadFileDistinctCnt"], "operateRsp": {"key": "downloadList"}}], "rspKeyMap": {"groupList": "downloadList"}}, {"_id": "AUDIT_V2_IP_ACCOUNT_DOWNLOAD_FILE_LIST", "name": "IP/账号下载文件按去重数据量排名", "repositoryOperateList": [{"repository": "CLICKHOUSE", "collectionName": "http_defined_event", "operateActionEnum": "QUERY", "frontCriteriaList": [{"property": "ip", "predicate": "IS", "value": ""}, {"property": "account", "predicate": "IS", "value": ""}, {"property": "date", "predicate": "IS", "value": "2022-02-15"}, {"property": "isFile", "predicate": "IS", "value": true}, {"property": "file_fileDirection", "predicate": "IS", "value": "DOWNLOAD"}], "page": 1, "selectFields": [{"selectField": "file_sha256", "selectAsField": "fileId"}, {"selectField": "any ( file_fileName )", "selectAsField": "fileName"}, {"selectField": "max ( rspLabelContentDistinctCount )", "selectAsField": "rspLabelContentDistinctCount"}], "metabaseGroupOperations": [{"groupFields": ["file_sha256"]}], "sort": [{"property": "rspLabelContentDistinctCount", "order": "DESC"}], "extraInfo": {"jobType": "AGGTASK", "queryFields": ["fileName", "rspLabelContentDistinctCount", "fileId"]}, "operateRsp": {"key": "fileList"}}], "rspKeyMap": {"fileList": "fileList"}}, {"_id": "FILE_INFO_FILETYPE_GROUP_LIST", "name": "文件格式分组/下拉框", "repositoryOperateList": [{"repository": "MONGO", "collectionName": "fileInfo", "operateActionEnum": "QUERY", "frontCriteriaList": [], "metabaseGroupOperations": [{"groupFields": ["fileType"]}], "operateRsp": {"key": "groupList", "backend2FrontConvert": {"udf": "var a = JSON.parse(originalStr);\nvar b=[];\nfor(var i = 0;i < a.length;i++) {\n    if(a[i]['_id'] && a[i]['_id'].length > 0){\n        b.push({\n            \"_id\": a[i][\"_id\"],\n            \"label\" : a[i][\"_id\"],\n            \"name\" : a[i][\"_id\"]\n        });\n    }\n}\noriginalStr = JSON.stringify(b)", "udfRspType": "JSON"}}}], "rspKeyMap": {"groupList": "groupList"}}]