[{"_id": "WEAKNESS_2.6.0_GROUP_LIST", "name": "现存弱点分布/现存弱点部门", "repositoryOperateList": [{"repository": "MONGO", "collectionName": "apiWeaknessDateStat", "operateActionEnum": "QUERY", "frontCriteriaList": [], "sort": [{"property": "updateTime", "order": "DESC"}], "page": 1, "limit": 1, "fields": ["departWeaknessStateList", "policyWeaknessStateList", "updateTime"], "operateRsp": {"key": "groupList"}}], "rspKeyMap": {"groupList": "groupList"}}, {"_id": "WEAKNESS_DATE_STAT_2.6.0_GROUP_LIST", "name": "周期内弱点状态走势", "repositoryOperateList": [{"repository": "MONGO", "collectionName": "apiWeaknessDateStat", "operateActionEnum": "QUERY", "frontCriteriaList": [{"property": "date", "predicate": "BETWEEN", "value": ["20220222", "20220222"]}], "page": 1, "fields": ["date", "totalCnt", "newCnt", "reopenCnt", "fixedCnt", "repairingCnt", "updateTime"], "operateRsp": {"key": "groupList"}}], "rspKeyMap": {"groupList": "groupList"}}, {"_id": "WEAKNESS_DEPART_2.6.0_GROUP_LIST", "name": "弱点按部门分组", "repositoryOperateList": [{"repository": "MONGO", "collectionName": "httpApiWeakness", "operateActionEnum": "QUERY", "frontCriteriaList": [{"property": "delFlag", "predicate": "IS", "value": false}], "metabaseGroupOperations": [{"groupFields": ["api.department.department"], "aggregateOptionList": [{"aggrType": "COUNT", "aggrField": "count"}]}], "sort": [{"property": "count", "order": "DESC"}], "operateRsp": {"key": "groupList", "backend2FrontConvert": {"udf": "var a = JSON.parse(originalStr);\nvar b=[];\nfor(var i = 0;i < a.length;i++) {\n    if(a[i]['_id'] && a[i]['_id'].length > 0){\n        b.push({\n            \"count\": a[i][\"count\"],\n            \"label\" : a[i][\"_id\"],\n            \"name\" : a[i][\"_id\"],\n            \"_id\" : a[i][\"_id\"]\n        });\n    }\n}\noriginalStr = JSON.stringify(b)", "udfRspType": "JSON"}}}], "rspKeyMap": {"groupList": "groupList"}}, {"_id": "AUDIT_V2_2.6.0_WEAKNESS_OVERVIEW_TOP_DATA", "name": "弱点概览顶部数据", "currentWeekBaseDateTimestamp": "CURRENT_WEEK_BASE_DATE", "repositoryOperateList": [{"repository": "MONGO", "collectionName": "httpApiWeakness", "operateActionEnum": "COUNT", "frontCriteriaList": [{"property": "state", "predicate": "IN", "value": ["IGNORED", "REPAIRING", "FIXED"]}, {"property": "delFlag", "predicate": "IS", "value": false}, {"property": "operateTime", "predicate": "BETWEEN", "value": ["START_TIMESTAMP_OF_CURRENT_WEEK", "END_TIMESTAMP_OF_CURRENT_WEEK"]}, {"property": "confirmState", "predicate": "IS", "value": "BANK_ASSET"}], "operateRsp": {"key": "currentWeekOperateCnt", "name": "本周已处理弱点（不包括状态为未确认、再复现弱点）"}}, {"repository": "MONGO", "collectionName": "httpApiWeakness", "operateActionEnum": "COUNT", "frontCriteriaList": [{"property": "state", "predicate": "IN", "value": ["REPAIRING", "FIXED", "REOPEN", "NEW"]}, {"property": "delFlag", "predicate": "IS", "value": false}, {"property": "earlyTimestamp", "predicate": "BETWEEN", "value": ["START_TIMESTAMP_OF_CURRENT_WEEK", "END_TIMESTAMP_OF_CURRENT_WEEK"]}, {"property": "confirmState", "predicate": "IS", "value": "BANK_ASSET"}], "operateRsp": {"key": "currentWeekNewWithoutIgnoreCnt", "name": "本周新增弱点（不包括状态为已忽略弱点）"}}, {"repository": "MONGO", "collectionName": "httpApiWeakness", "operateActionEnum": "COUNT", "frontCriteriaList": [{"property": "state", "predicate": "IN", "value": ["NEW", "REOPEN"]}, {"property": "delFlag", "predicate": "IS", "value": false}, {"property": "earlyTimestamp", "predicate": "BETWEEN", "value": ["START_TIMESTAMP_OF_CURRENT_WEEK", "END_TIMESTAMP_OF_CURRENT_WEEK"]}, {"property": "confirmState", "predicate": "IS", "value": "BANK_ASSET"}], "operateRsp": {"key": "currentWeekNotHandleCnt", "name": "本周新增待处理弱点"}}, {"repository": "MONGO", "collectionName": "httpApiWeakness", "operateActionEnum": "COUNT", "frontCriteriaList": [{"property": "state", "predicate": "IN", "value": ["REPAIRING"]}, {"property": "delFlag", "predicate": "IS", "value": false}, {"property": "earlyTimestamp", "predicate": "BETWEEN", "value": ["START_TIMESTAMP_OF_CURRENT_WEEK", "END_TIMESTAMP_OF_CURRENT_WEEK"]}, {"property": "confirmState", "predicate": "IS", "value": "BANK_ASSET"}], "operateRsp": {"key": "currentWeekRepairingCnt", "name": "本周新增待修复"}}], "rspKeyMap": {"currentWeekOperateCnt": "currentWeekOperateCnt", "currentWeekNewWithoutIgnoreCnt": "currentWeekNewWithoutIgnoreCnt", "currentWeekNotHandleCnt": "currentWeekNotHandleCnt", "currentWeekRepairingCnt": "currentWeekRepairingCnt"}}, {"_id": "AUDIT_V2_2.6.0_WEAKNESS_OVERVIEW_TOP_DATA_VIEW", "name": "弱点概览顶部数据", "repositoryOperateList": [{"repository": "MONGO", "collectionName": "weaknessTopCount", "operateActionEnum": "FINDONE", "frontCriteriaList": [{"property": "date", "predicate": "IS", "value": "START_DATE_OF_CURRENT_WEEK"}], "operateRsp": {"key": "currentWeekTopView", "name": "本周弱点数据"}}, {"repository": "MONGO", "collectionName": "weaknessTopCount", "operateActionEnum": "FINDONE", "frontCriteriaList": [{"property": "date", "predicate": "IS", "value": "START_DATE_OF_LAST_WEEK"}], "operateRsp": {"key": "lastWeekTopView", "name": "上周弱点数据"}}], "rspKeyMap": {"currentWeekTopView": "currentWeekTopView", "lastWeekTopView": "lastWeekTopView"}}]