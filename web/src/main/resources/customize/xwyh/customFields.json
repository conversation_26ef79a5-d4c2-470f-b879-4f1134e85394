[{"fieldName": "department", "fieldType": "String", "fieldExplain": "部门", "fieldExample": "\"department\":\"a\"", "type": "EVENT", "isUdp": true, "isDefault": true}, {"fieldName": "rspLabelContentDistinctCount", "fieldType": "Integer", "fieldExplain": "去重后返回数", "fieldExample": "\"rspLabelContentDistinctCount\":0", "type": "EVENT", "isDefault": false, "isUdp": true}, {"fieldName": "rspLabelContentDistinctCountByLabel", "fieldType": "JSONObject", "fieldExplain": "不同标签去重后返回数", "fieldExample": "\"rspLabelContentDistinctCountByLabel\":{\"bankCard\":1}", "type": "EVENT", "isDefault": false, "isUdp": true}, {"fieldName": "isFile", "fieldType": "Boolean", "fieldExplain": "是否文件", "fieldExample": "\"isFile\":true", "type": "EVENT", "isDefault": true, "isUdp": true}, {"fieldName": "fileName", "fieldType": "String", "fieldExplain": "文件名", "fieldExample": "\"fileName\":\"aaa\"", "type": "EVENT", "isDefault": true, "isUdp": false}, {"fieldName": "fileIpDirection", "fieldType": "Array", "fieldExplain": "文件名", "fieldExample": "\"fileIpDirection\":[\"127.0.0.1\",\"*********\"]", "type": "EVENT", "isDefault": true, "isUdp": true}, {"fieldName": "gmtCreate", "fieldType": "String", "fieldExplain": "时间", "fieldExample": "\"gmtCreate\":*************", "type": "AUDIT_LOG", "isDefault": true, "isUdp": false}, {"fieldName": "username", "fieldType": "String", "fieldExplain": "用户名", "fieldExample": "\"username\":\"aaa\"", "type": "AUDIT_LOG", "isDefault": true, "isUdp": false}, {"fieldName": "ip", "fieldType": "String", "fieldExplain": "用户名", "fieldExample": "\"ip\":\"**************\"", "type": "AUDIT_LOG", "isDefault": true, "isUdp": false}, {"fieldName": "type", "fieldType": "String", "fieldExplain": "编辑类型", "fieldExample": "\"type\":\"登录\"", "type": "AUDIT_LOG", "isDefault": true, "isUdp": false}, {"fieldName": "module", "fieldType": "String", "fieldExplain": "操作模块", "fieldExample": "\"module\":\"登录\"", "type": "AUDIT_LOG", "isDefault": true, "isUdp": false}, {"fieldName": "operation", "fieldType": "String", "fieldExplain": "操作模块", "fieldExample": "\"operation\":\"根据用户Id加载用户权限\"", "type": "AUDIT_LOG", "isDefault": true, "isUdp": false}]