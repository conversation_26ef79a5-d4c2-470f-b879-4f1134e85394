#!/bin/bash
NACOS_SVC_IP=$(kubectl get svc nacos-server -n app-audit  -o jsonpath='{.spec.clusterIP}')
MASTER_IP=$(kubectl get node -o wide|grep master|awk '{print $6}')
old_kafka_svc_yaml=$(cat <<EOF
apiVersion: v1
kind: Service
metadata:
  name: kafka-ing-server-exp
  labels:
    app: kafka-ing
spec:
  type: NodePort
  ports:
    - port: 9094
      targetPort: kafka-ing
      protocol: TCP
      name: kafka-ing-port
      nodePort: 9094
  selector:
    app: "kafka"
    rel: "beta"
EOF
)
new_kafka_svc_yaml=$(cat <<EOF
apiVersion: v1
kind: Service
metadata:
  name: kafka-ing-server
spec:
  ports:
    - name: external
      port: 9094
      protocol: TCP
      targetPort: external
      nodePort: 9094
  selector:
    app.kubernetes.io/part-of: kafka
  sessionAffinity: None
  type: NodePort
EOF
)
old_service_yaml=$(cat <<EOF
apiVersion: v1
kind: Service
metadata:
  labels:
    app: clickhouse
  name: ck-nodeport-svc
spec:
  ports:
    - name: tcp
      port: 9000
      protocol: TCP
      targetPort: 9000
      nodePort: 9000
  type: NodePort
  selector:
    app: clickhouse
EOF
)
new_service_yaml=$(cat <<EOF
apiVersion: v1
kind: Service
metadata:
  labels:
    clickhouse.altinity.com/chi: clickhouse-cluster
    clickhouse.altinity.com/cluster: cluster
    clickhouse.altinity.com/app: chop
  name: ck-nodeport-svc
spec:
  ports:
    - name: tcp
      port: 9000
      protocol: TCP
      targetPort: 9000
      nodePort: 9000
  type: NodePort
  selector:
    clickhouse.altinity.com/chi: clickhouse-cluster
EOF
)
function edit_nacos_conf() {
  curl -X GET "http://"$NACOS_SVC_IP":8848/nacos/v1/cs/configs?dataId=discover.bootstrap.properties&group=&appName=&config_tags=&pageNo=1&pageSize=10&tenant=&search=accurate"|jq -rc .pageItems[].content > /tmp/data.txt
  # 查看是否已经配置过了
  if grep -q "discover.sample.forward" /tmp/data.txt; then
     echo "nacos forward config already configured"
    return
  fi
  echo "discover.sample.forward.enable=true" >> /tmp/data.txt
  echo "discover.sample.forward.kafka.server="$MASTER_IP":9094" >> /tmp/data.txt
  echo "discover.sample.forward.kafka.topic=HttpRecognizedSampleEvent" >> /tmp/data.txt
  CONTENT=$(cat /tmp/data.txt)
  curl -X POST "http://"$NACOS_SVC_IP":8848/nacos/v1/cs/configs?dataId=discover.bootstrap.properties&group=discover&appName=&config_tags=&pageNo=1&pageSize=10&tenant=&search=accurate" --data-urlencode "content=${CONTENT}"
  kubectl rollout restart discover-server-app
  echo "restart discover"
}
function export_clickhouse_port() {
   if kubectl get configmap clickhouse-conf -napp-audit &> /dev/null; then
      echo "$old_service_yaml" | kubectl apply -f - -n app-audit
   else
      echo "$new_service_yaml" | kubectl apply -f - -n app-audit
   fi

}
function export_kafka_port() {
   if kubectl get svc kafka-server-controller-0-external -n app-audit &> /dev/null; then
     echo "$new_kafka_svc_yaml" | kubectl apply -f - -n app-audit
   else
      echo "$old_kafka_svc_yaml" | kubectl apply -f - -n app-audit
   fi
}
edit_nacos_conf
export_clickhouse_port
export_kafka_port