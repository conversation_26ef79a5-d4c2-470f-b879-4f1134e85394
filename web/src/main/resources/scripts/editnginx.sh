#!/bin/bash

CONFIGMAP_NAME="audit-api-f-advanced-conf-d"
kube<PERSON>l get configmap $CONFIGMAP_NAME -o yaml > /tmp/${CONFIGMAP_NAME}.yaml

# 定义要插入的配置（精确8空格缩进）
insert_content=$(cat <<EOF
        location /eolink/ {
                proxy_pass $1/;
                proxy_hide_header X-Frame-Options;
                add_header X-Frame-Options ALLOWALL;
                proxy_set_header Host \$host;
                proxy_set_header X-Real-IP \$remote_addr;
                proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
                proxy_set_header X-Forwarded-Proto \$scheme;
                set \$req_prefix '/eolink';
                body_filter_by_lua_file '/home/<USER>';
        }

        location /plugin-frontend/ {
                proxy_pass $1/plugin-frontend/;
                proxy_hide_header X-Frame-Options;
                add_header X-Frame-Options ALLOWALL;
                proxy_set_header Host \$host;
                proxy_set_header X-Real-IP \$remote_addr;
                proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
                proxy_set_header X-Forwarded-Proto \$scheme;
        }

        location /_system/ {
                proxy_pass $1/_system/;
                proxy_hide_header X-Frame-Options;
                add_header X-Frame-Options ALLOWALL;
                proxy_set_header Host \$host;
                proxy_set_header X-Real-IP \$remote_addr;
                proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
                proxy_set_header X-Forwarded-Proto \$scheme;
        }

        location /api/ {
                proxy_pass $1/api/;
                proxy_hide_header X-Frame-Options;
                add_header X-Frame-Options ALLOWALL;
                proxy_set_header Host \$host;
                proxy_set_header X-Real-IP \$remote_addr;
                proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
                proxy_set_header X-Forwarded-Proto \$scheme;
        }
EOF

)

# 使用awk进行精准插入
awk -v insert="$insert_content" '
BEGIN { found=0; inserted=0 }
{
    # 匹配目标location块的结束大括号
    if (/^        }/ && found) {
        print $0
        print insert
        found=0
        inserted=1
        next
    }
    # 查找目标location块的起始行
    if (/^        location \/audit-report\/ {/) {
        found=1
    }
    print $0
}
END {
    if (!inserted) {
        print "ERROR: 未找到插入位置" > "/dev/stderr"
        exit 1
    }
}
' /tmp/${CONFIGMAP_NAME}.yaml > /tmp/configmap.yaml
kubectl apply -f /tmp/configmap.yaml
kubectl rollout restart deployment/audit-api-f-advanced-app


