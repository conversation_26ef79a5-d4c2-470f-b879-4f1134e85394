#!/bin/bash
xml_variable='<yandex>
    <remote_servers>
    </remote_servers>
</yandex>'
zk_service_yaml=$(cat <<EOF
apiVersion: v1
kind: Service
metadata:
  labels:
    app: zookeeper
  name: zk-nodeport-svc
spec:
  ports:
    - name: tcp
      port: 2181
      protocol: TCP
      targetPort: 2181
      nodePort: 2181
  type: NodePort
  selector:
    app: zookeeper
EOF
)

new_service_yaml=$(cat <<EOF
apiVersion: v1
kind: Service
metadata:
  labels:
    clickhouse.altinity.com/chi: clickhouse-cluster
    clickhouse.altinity.com/cluster: cluster
    clickhouse.altinity.com/app: chop
  name: ck-nodeport-svc
spec:
  ports:
    - name: tcp
      port: 9000
      protocol: TCP
      targetPort: 9000
      nodePort: 9000
  type: NodePort
  selector:
    clickhouse.altinity.com/chi: clickhouse-cluster
EOF
)
old_service_yaml=$(cat <<EOF
apiVersion: v1
kind: Service
metadata:
  labels:
    app: clickhouse
  name: ck-nodeport-svc
spec:
  ports:
    - name: tcp
      port: 9000
      protocol: TCP
      targetPort: 9000
      nodePort: 9000
  type: NodePort
  selector:
    app: clickhouse
EOF
)
CONFIGMAP_NAME="chi-clickhouse-cluster-common-configd"
OLD_CONFIGMAP_NAME="clickhouse-conf"




function change_new_ck() {
  new_cluster="$1"
  master_ip="$2"
  kubectl get configmap chi-clickhouse-cluster-common-configd -n app-audit -o json | jq --arg newXml "$NEW_XML" '.data["chop-generated-remote_servers.xml"] = $newXml' | kubectl apply -f - -n app-audit
#  echo "$new_service_yaml" | kubectl apply -f -
#  echo "$zk_service_yaml" | kubectl apply -f -
  kubectl get configmap $CONFIGMAP_NAME  -o json | \
  jq --arg zookeeper_config "$ZOOKEEPER_CONFIG" '.data["zookeeper.conf"] = $zookeeper_config' | \
  kubectl apply -f -
  kubectl scale --replicas=0 deploy altinity-clickhouse-operator
  kubectl rollout restart sts/chi-clickhouse-cluster-cluster-0-0
}

function change_old_ck() {
  new_cluster="$1"
  master_ip="$2"
  kubectl get configmap clickhouse-conf -n app-audit -o json | jq --arg newXml "$NEW_XML" '.data["remote_servers.xml"] = $newXml' | kubectl apply -f - -n app-audit
  kubectl get configmap $OLD_CONFIGMAP_NAME -o json | \
  jq --arg zookeeper_config "$ZOOKEEPER_CONFIG" '.data["zookeeper.conf"] = $zookeeper_config' | \
  kubectl apply -f -
#  echo "$zk_service_yaml" | kubectl apply -f -
#  echo "$old_service_yaml" | kubectl apply -f -
  kubectl rollout restart sts/clickhouse-app


}


function main() {
  new_cluster="$1"
  master_ip="$2"
  ZOOKEEPER_CONFIG="<yandex>
    <zookeeper>
        <node>
            <host>zookeeper-server</host>
            <port>2182</port>
        </node>
    </zookeeper>
  </yandex>"
  xml_with_cluster=$(awk -v new_cluster="$new_cluster" '
    /<remote_servers>/ {
        print $0            # 打印当前行
        printf "%s", new_cluster  # 输出新的段落
        next                # 跳到下一行
    }
    { print }               # 默认打印所有行
  ' <<< "$xml_variable")
   NEW_XML=$(echo "$xml_with_cluster"|xmllint --format -)
   if kubectl get configmap clickhouse-conf  &> /dev/null; then
     change_old_ck "$@"
   else
     change_new_ck "$@"
   fi

}
main "$@"