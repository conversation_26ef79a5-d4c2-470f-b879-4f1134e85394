operations:
  - requestMapping: "/api/riskInfoDetail/handle.do"
    module: "风险"
    type: "处理"
    operation: "修改风险状态"
    description: "将主体为\"${#target}\"的风险\"${#risk}\"状态改为\"${#stateName}\" ${#option}"
  - requestMapping: "/api/riskCheckList/markRisk"
    conditions:
      - module: "风险"
        type: "编辑"
        operation: "点击风险标识"
        description: "关注主体为\"${#target}\"的风险\"${#risk}\""
        condition: "#riskMark == true"
      - module: "风险"
        type: "编辑"
        operation: "点击风险标识"
        description: "取消关注主体为\"${#target}\"的风险\"${#risk}\""
        condition: "#riskMark == false"
  - requestMapping: "/api/riskInfoDetail/getRiskSampleById.do"
    module: "样例"
    type: "取消脱敏"
    operation: "取消脱敏"
    description: "取消脱敏查看API样例\"${#uri}\""
    condition: "#riskSampleSearchDto.desensitize == false"
