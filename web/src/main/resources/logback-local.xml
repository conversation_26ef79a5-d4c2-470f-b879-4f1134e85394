<?xml version="1.0" encoding="UTF-8"?>
<configuration debug="false">
    <!--定义日志文件的存储地址 勿在 LogBack 的配置中使用相对路径-->
    <Property name="log_path" value="${user.home}/logs/auditapiv2"/>
    <property name="console_log_pattern"
              value="%red(%date{yyyy-MM-dd HH:mm:ss}) %highlight(%-5level) %red([%thread]) %boldMagenta(%logger{50}) %cyan(%msg%n)"/>
    <property name="file_log_pattern" value="%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] [%-5level] %logger{50} - %msg%n"/>
    <!-- 最大历史保存文件的数量，只保存最近30天的日志文件，超出的会被删除-->
    <property name="log_maxHistory" value="30"/>
    <!-- 单文件大小-->
    <property name="log_maxSize" value="20MB"/>
    <!--归档日志的最大存储量-->
    <property name="log_totalSizeCap" value="2GB"/>

    <!-- 控制台输出 -->
    <appender name="console" class="ch.qos.logback.core.ConsoleAppender">
        <encoder class="ch.qos.logback.classic.encoder.PatternLayoutEncoder">
            <!--格式化输出：%d表示日期，%thread表示线程名，%-5level：级别从左显示5个字符宽度%msg：日志消息，%n是换行符-->
            <pattern>${console_log_pattern}</pattern>
        </encoder>
    </appender>

    <!-- 1.打印debug级别日志的设置 -->
    <appender name="debugAppender" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <filter class="ch.qos.logback.classic.filter.LevelFilter">
            <level>DEBUG</level>
            <onMatch>ACCEPT</onMatch>
            <onMismatch>DENY</onMismatch>
        </filter>
        <file>${log_path}/debug/debug.log</file>
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <fileNamePattern>${log_path}/debug/debug-%d{yyyy-MM-dd}.%i.log</fileNamePattern>
            <maxFileSize>${log_maxSize}</maxFileSize>
            <maxHistory>${log_maxHistory}</maxHistory>
            <totalSizeCap>${log_totalSizeCap}</totalSizeCap>
        </rollingPolicy>
        <encoder class="ch.qos.logback.classic.encoder.PatternLayoutEncoder">
            <pattern>${file_log_pattern}</pattern>
        </encoder>
    </appender>

    <!-- 2.打印info级别日志的设置 -->
    <appender name="infoAppender" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <!-- 过滤掉非info级别的信息 -->
        <filter class="ch.qos.logback.classic.filter.LevelFilter">
            <level>INFO</level>
            <onMatch>ACCEPT</onMatch>
            <onMismatch>DENY</onMismatch>
        </filter>
        <file>${log_path}/info/info.log</file>
        <!-- 每天生成日志文件 -->
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <fileNamePattern>${log_path}/info/info-%d{yyyy-MM-dd}.%i.log</fileNamePattern>
            <maxFileSize>${log_maxSize}</maxFileSize>
            <maxHistory>${log_maxHistory}</maxHistory>
            <totalSizeCap>${log_totalSizeCap}</totalSizeCap>
        </rollingPolicy>
        <!-- 格式化输出 -->
        <encoder class="ch.qos.logback.classic.encoder.PatternLayoutEncoder">
            <pattern>${file_log_pattern}</pattern>
        </encoder>
    </appender>

    <!-- 3.打印error级别日志的设置 -->
    <appender name="errorAppender" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <!-- 过滤掉非info级别的信息 -->
        <filter class="ch.qos.logback.classic.filter.LevelFilter">
            <level>ERROR</level>
            <onMatch>ACCEPT</onMatch>
            <!-- 作用是拒绝写入所有与<level>不匹配的日志信息，也就是非error级别的日志信息都不会被写入到日志文件中 -->
            <onMismatch>DENY</onMismatch>
        </filter>
        <file>${log_path}/error/error.log</file>
        <!-- 每天生成日志文件 -->
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <fileNamePattern>${log_path}/error/error-%d{yyyy-MM-dd}.%i.log</fileNamePattern>
            <!-- 最大历史保存文件的数量，只保存最近30天的日志文件，超出的会被删除-->
            <maxFileSize>${log_maxSize}</maxFileSize>
            <maxHistory>${log_maxHistory}</maxHistory>
            <totalSizeCap>${log_totalSizeCap}</totalSizeCap>
        </rollingPolicy>
        <!-- 格式化输出 -->
        <encoder class="ch.qos.logback.classic.encoder.PatternLayoutEncoder">
            <pattern>${file_log_pattern}</pattern>
        </encoder>
    </appender>

    <!-- 4、打印warn级别信息 -->
    <appender name="warnAppender" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <!-- 过滤掉非info级别的信息 -->
        <filter class="ch.qos.logback.classic.filter.LevelFilter">
            <level>WARN</level>
            <onMatch>ACCEPT</onMatch>
            <!-- 作用是拒绝写入所有与<level>不匹配的日志信息，也就是非warn级别的日志信息都不会被写入到日志文件中 -->
            <onMismatch>DENY</onMismatch>
        </filter>
        <file>${log_path}/warn/warn.log</file>
        <!-- 每天生成日志文件 -->
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <fileNamePattern>${log_path}/warn/warn-%d{yyyy-MM-dd}.%i.log</fileNamePattern>
            <!-- 最大历史保存文件的数量，只保存最近30天的日志文件，超出的会被删除-->
            <maxFileSize>${log_maxSize}</maxFileSize>
            <maxHistory>${log_maxHistory}</maxHistory>
            <totalSizeCap>${log_totalSizeCap}</totalSizeCap>
        </rollingPolicy>
        <!-- 格式化输出 -->
        <encoder class="ch.qos.logback.classic.encoder.PatternLayoutEncoder">
            <pattern>${file_log_pattern}</pattern>
        </encoder>
    </appender>

    <!-- 日志输出级别 -->
    <root level="INFO">
        <appender-ref ref="console"/>
        <appender-ref ref="warnAppender"/>
        <appender-ref ref="infoAppender"/>
        <appender-ref ref="errorAppender"/>
    </root>

</configuration>








