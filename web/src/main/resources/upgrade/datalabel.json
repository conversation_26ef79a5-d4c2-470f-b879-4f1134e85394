[{"id": "d66be0c673448e31c84c520277abdb77", "detect": {"regex": {"range": "all", "patterns": ["[\"'](?:香港身份证|香港永久性居民身份证|hong kong identity card|hong kong permanent identity card)[\"']\\s*:\\s*[\"']([A-Z]\\d{6}\\([0-9A]\\))[\"']"], "flags": ["caseless"]}}, "validate": {"builtInAlgo": {"algoId": "hkIdCard"}}}, {"id": "idCard", "detect": {"regex": {"range": "all", "patterns": ["(\\d{17}[\\dX])"], "flags": ["caseless"]}}, "validate": {"builtInAlgo": {"algoId": "idCard"}}}, {"id": "depositAmt", "detect": {"regex": {"range": "all", "patterns": ["[\"'][^\"']{0,16}deposit_?(?:amt|amount|amnt)[\"']\\s*:\\s*[\"']?(([0-9]+\\.[0-9]{1,2})|([1-9][0-9]*))[\"']?"], "flags": ["caseless"]}}}, {"id": "f067fcd6996ad0bd694b4cf46b208a56", "detect": {"regex": {"range": "all", "patterns": ["[\"'][^\"']{0,16}(?:tran_?no)[^\"']{0,16}[\"']\\s*:\\s*[\"']([0-9a-zA-Z]{5,40})[\"']"], "flags": ["caseless"]}}}, {"id": "positionName", "detect": {"regex": {"range": "all", "patterns": ["[\"'][^\"']{0,16}(?:post|position)_?name[\"']\\s*:\\s*[\"']([^\"']{1,20})[\"']"], "flags": ["caseless"]}}}, {"id": "f3a02cee98c6f5ebb070728c73d175f3", "detect": {"regex": {"range": "all", "patterns": ["[\"'][^\"']{0,16}(?:deal|trans?)_?(?:amt|price|payment|amount)[^\"]{0,16}[\"']\\s*:\\s*[\"']?(([0-9]+\\.[0-9]{1,2})|([1-9][0-9]*))[\"']?"], "flags": ["caseless"]}}}, {"id": "age", "detect": {"regex": {"range": "all", "patterns": ["[\"'](?:age|[a-zA-Z]{1,10}_age)[\"']\\s*:\\s*[\"']?(\\d{1,3})[\"']?"], "flags": ["caseless"]}}}, {"id": "effectDate", "detect": {"regex": {"range": "all", "patterns": ["[\"']effect_?(?:date|time)[\"']\\s*:\\s*[\"']([0-9-:/. ]{8,19})[\"']"], "flags": ["caseless"]}}}, {"id": "imei", "detect": {"regex": {"range": "all", "patterns": ["[\"'][^\"']{0,16}(?:imei|国际移动设备识别码|手机串号|序列号)[^\"']{0,16}[\"']\\s*:\\s*[\"']?(\\d{15,17})[\"']?"], "flags": ["caseless"]}}, "validate": {"builtInAlgo": {"algoId": "imei"}}}, {"id": "phone", "detect": {"regex": {"range": "all", "patterns": ["(?:^|[\"'>:=,，\\s])((\\d{3,4}-\\d{3,4}-?\\d{3,4})|([(（]\\d{3,4}[)）]\\d{7,8}))(?:$|[\"\\s',，};<])"], "flags": ["caseless"]}}, "validate": {"builtInAlgo": {"algoId": "phone"}}}, {"id": "annualIncome", "detect": {"regex": {"range": "all", "patterns": ["[\"'][^\"]{0,16}income[\"']\\s*:\\s*[\"']?(([0-9]+\\.[0-9]{1,2})|([1-9][0-9]*))[\"']?"], "flags": ["caseless"]}}}, {"id": "personName", "detect": {"builtInAlgo": {"range": "all", "algoId": "personName"}}}, {"id": "03fb789cc50debf95cf13b0559c1a87e", "detect": {"regex": {"range": "all", "patterns": ["\"(?:[^\"]*酒店[^\"]*|hotel|宾馆|旅馆|客栈|饭店)\"\\s*:\\s*(\\{[^}]*\\}|\\[[^]]*\\]|\"[^\"]*\")"], "flags": ["caseless"]}}, "validate": {"builtInAlgo": {"algoId": "hotelRecord"}}}, {"id": "4a9ba447982d6c8c3e6e7431803df23c", "detect": {"regex": {"range": "all", "patterns": ["[\"'](?:[^\"']{0,16}婚姻[^\"']{0,16}|marriage)[\"']\\s*:\\s*[\"'](未婚|已婚|丧偶|离异|离婚)[\"']"], "flags": ["caseless"]}}, "validate": {"builtInAlgo": {"algoId": "maritalStatus"}}}, {"id": "dept", "detect": {"regex": {"range": "all", "patterns": ["[\"'](?:dept|department)[\"']\\s*:\\s*[\"']([^\"']{1,30})[\"']"], "flags": ["caseless"]}}}, {"id": "birthAddress", "detect": {"regex": {"range": "all", "patterns": ["[\"']birth_?(?:address|addr)[\"']\\s*:\\s*[\"']([\\w\\x{4e00}-\\x{9fa5}]{1,40})[\"']"], "flags": ["caseless"]}}}, {"id": "eb5bf3abf6c74db25e494288704c0473", "detect": {"regex": {"range": "all", "patterns": ["(\\d{17}[\\dX])"], "flags": ["caseless"]}}, "validate": {"builtInAlgo": {"algoId": "minorIdCard"}}}, {"id": "65b27462df8d786082100b054ad39474", "detect": {"regex": {"range": "all", "patterns": ["[\"'](?:经纬度|定位|位置|location|gps|lng,lat|lat,lng|latitude,longitude|longitude,latitude|经度,纬度|纬度,经度|[^\"']{0,8}Lon|[^\"']{0,8}Lat|latitude|longitude)[\"']\\s*:\\s*[\"']([\\d\\., \\-]{3,48})[\"']"], "flags": ["caseless"]}}, "validate": {"builtInAlgo": {"algoId": "lal"}}}, {"id": "carNo", "detect": {"regex": {"range": "all", "patterns": ["(([京津沪渝冀豫云辽黑湘皖鲁新苏浙赣鄂桂甘晋蒙陕吉闽贵粤青藏川宁琼使][A-Z]|WJ[\\x{4e00}-\\x{9fa5}]|民航)[A-Z0-9]{4,5}[A-Z0-9挂学警港澳应领])"], "flags": ["caseless"]}}, "validate": {"builtInAlgo": {"algoId": "carNo"}}}, {"id": "4588bff2e20140724e02f855e522a18e", "detect": {"regex": {"range": "all", "patterns": ["(\\d{3,4}-([*]{4}\\d{3,4}|[*]{5}\\d{3}|\\d{3,4}[*]{4}))"], "flags": ["caseless"]}}, "validate": {"builtInAlgo": {"algoId": "antiPhone"}}}, {"id": "province", "detect": {"regex": {"range": "all", "patterns": ["[\"'][^\"']{0,16}province[^\"']{0,16}[\"']\\s*:\\s*[\"']([\\x{4e00}-\\x{9fa5}]{2,10})[\"']"], "flags": ["caseless"]}}, "validate": {"builtInAlgo": {"algoId": "province"}}}, {"id": "freezeDate", "detect": {"regex": {"range": "all", "patterns": ["[\"'][^\"']{0,16}(?:freeze|frozen)_?date[\"']\\s*:\\s*[\"']([^\"']{8,13})[\"']"], "flags": ["caseless"]}}, "validate": {"builtInAlgo": {"algoId": "freezeDate"}}}, {"id": "bae6143ccb90cb0acd7084131805bf96", "detect": {"regex": {"range": "all", "patterns": ["[\"'][^\"']{0,10}(?:增值税账号|增值账号)[^\"']{0,10}[\"']\\s*:\\s*[\"'](\\d{8,28})[\"']"], "flags": ["caseless"]}}, "validate": {"builtInAlgo": {"algoId": "VATAccount"}}}, {"id": "roomNo", "detect": {"regex": {"range": "all", "patterns": ["[\"']room_?(?:no|number)[\"']\\s*:\\s*[\"']?(\\d{1,10})[\"']?"], "flags": ["caseless"]}}}, {"id": "f12c101d1ac5bad5077a04089f82e564", "detect": {"regex": {"range": "all", "patterns": ["[\"'][^\"']{0,16}(?:rfnd|refund)_?(?:amt|price|payment|amount)[\"']\\s*:\\s*[\"']?(([0-9]+\\.[0-9]{1,2})|([1-9][0-9]*))[\"']?"], "flags": ["caseless"]}}}, {"id": "cce03e1eaa426302a30c8e4f53dadf39", "detect": {"regex": {"range": "all", "patterns": ["[\"'][^\"']{0,10}(?:纳税人识别号|增值税号|税号|统一社会信用代码|营业执照号|组织结构代码|社会保险登记号)[^\"']{0,10}[\"']\\s*:\\s*[\"']([0-9a-zA-Z]{15,20})[\"']"], "flags": ["caseless"]}}, "validate": {"builtInAlgo": {"algoId": "taxpayerNo"}}}, {"id": "843455741619a14e9dfdb9f6f4bf0875", "detect": {"regex": {"range": "all", "patterns": ["[\"'](?:港澳通行证|双程证)[\"']\\s*:\\s*[\"'](C\\w\\d{7})[\"']"], "flags": ["caseless"]}}, "validate": {"builtInAlgo": {"algoId": "gaCard"}}}, {"id": "286ccbcc0bf095c1c96a7955689ea0f7", "detect": {"regex": {"range": "all", "patterns": ["[\"'](?:birth|birthday|生日|出生日期)[^\"']{0,16}[\"']\\s*:\\s*[\"']([-./\\d]{8,19})[\"']"], "flags": ["caseless"]}}, "validate": {"builtInAlgo": {"algoId": "birthday"}}}, {"id": "company", "detect": {"builtInAlgo": {"range": "all", "algoId": "company"}}}, {"id": "<PERSON><PERSON><PERSON><PERSON>", "detect": {"builtInAlgo": {"range": "all", "algoId": "<PERSON><PERSON><PERSON><PERSON>"}}}, {"id": "crCtfNo", "detect": {"regex": {"range": "all", "patterns": ["[\"']crCtf_?(?:no|code)[\"']\\s*:\\s*[\"']([0-9A-Z/]{1,6})[\"']"], "flags": ["caseless"]}}}, {"id": "egalPerName", "detect": {"regex": {"range": "all", "patterns": ["[\"'](?:egal|corp)_?(?:person|agent)_?name[\"']\\s*:\\s*[\"']([\\x{4e00}-\\x{9fa5}]{2,4})[\"']"], "flags": ["caseless"]}}}, {"id": "64b56b3e30655bc1c36ae03d72979dc3", "detect": {"regex": {"range": "all", "patterns": ["[\"'][^\"']{0,16}(?:支付账号|pay_?account)[^\"']{0,16}[\"']\\s*:\\s*[\"'](\\w{3,20})[\"']"], "flags": ["caseless"]}}}, {"id": "bankCard", "detect": {"regex": {"range": "all", "patterns": ["([1-9](\\d{18}|\\d{16}|\\d{15}))"], "flags": ["caseless"]}}, "validate": {"builtInAlgo": {"algoId": "bankCard"}}}, {"id": "settleAmt", "detect": {"regex": {"range": "all", "patterns": ["[\"']settle_?(?:amt|amount|amnt)[\"']\\s*:\\s*[\"']?(([0-9]+\\.[0-9]{1,2})|([1-9][0-9]*))[\"']?"], "flags": ["caseless"]}}}, {"id": "instName", "detect": {"regex": {"range": "all", "patterns": ["[\"'](?:inst|organization|institution)_?Name[\"']\\s*:\\s*[\"']([\\x{4e00}-\\x{9fa5}]{1,40})[\"']"], "flags": ["caseless"]}}, "validate": {"builtInAlgo": {"algoId": "instName"}}}, {"id": "antiMobile", "detect": {"regex": {"range": "all", "patterns": ["(1\\d{2}([*]{4}\\d{4}|\\d{4}[*]{4}))"], "flags": ["caseless"]}}, "validate": {"builtInAlgo": {"algoId": "antiMobile"}}}, {"id": "officer<PERSON><PERSON>", "detect": {"regex": {"range": "all", "patterns": ["[南北沈兰成济广参政后装海空]字第\\d{6,8}号"], "flags": ["caseless"]}}, "validate": {"builtInAlgo": {"algoId": "officer<PERSON><PERSON>"}}}, {"id": "5e48c0292edff413c63f577c609767a2", "detect": {"regex": {"range": "all", "patterns": ["[\"'][^\"']{0,16}(?:drugName|drug_name)[^\"']{0,16}[\"']\\s*:\\s*[\"']([^\"']+)[\"']"], "flags": ["caseless"]}}}, {"id": "ccy", "detect": {"regex": {"range": "all", "patterns": ["[\"'][^\"']{0,16}(?:ccy|CURR_?TYPE|currency|currency_?Code|cur_?Code|cur_?type)[\"']\\s*:\\s*[\"']?([A-Z]{3})[\"']?"], "flags": ["caseless"]}}}, {"id": "custType", "detect": {"regex": {"range": "all", "patterns": ["[\"'][^\"']{0,16}(?:cust|customer|client)_?type\"\\s*:\\s*[\"']([\\x{4e00}-\\x{9fa5}]{1,20})[\"']"], "flags": ["caseless"]}}}, {"id": "regionName", "detect": {"regex": {"range": "all", "patterns": ["[\"'](?:region|area)_?(?:name)?[\"']\\s*:\\s*[\"']([\\x{4e00}-\\x{9fa5}]{1,30})[\"']"], "flags": ["caseless"]}}}, {"id": "discountAmount", "detect": {"regex": {"range": "all", "patterns": ["[\"'](?:avlDsnt|discount|mdsct|preferential)_?(?:amt|amount|amnt)[\"']\\s*:\\s*[\"']?(([0-9]+\\.[0-9]{1,2})|([1-9][0-9]*))[\"']?"], "flags": ["caseless"]}}}, {"id": "d8eb557e7df9e967065d9d15faadf2a3", "detect": {"builtInAlgo": {"range": "all", "algoId": "antiAddress"}}}, {"id": "tranPwd", "detect": {"regex": {"range": "all", "patterns": ["[\"'](?:tran|trx|trade|transaction|deal|trans)_?(?:password|pwd|passwd)[\"']\\s*:\\s*[\"']([\\w`~!@#$%^&*()_+-=;:,<.>/\\\\?]{3,256})[\"']"], "flags": ["caseless"]}}, "validate": {"builtInAlgo": {"algoId": "tranPwd"}}}, {"id": "employeeId", "detect": {"regex": {"range": "all", "patterns": ["[\"'][^\"']{0,16}(?:employee|staff)_?id[\"']\\s*:\\s*[\"']([0-9a-zA-Z]{1,30})[\"']"], "flags": ["caseless"]}}}, {"id": "ssn", "detect": {"regex": {"range": "all", "patterns": ["[\"'](?:ssn|Social_?Security_?number)[\"']\\s*:\\s*[\"']?(\\d{3}-\\d{2}-\\d{4})[\"']?"], "flags": ["caseless"]}}, "validate": {"builtInAlgo": {"algoId": "ssn"}}}, {"id": "vin", "detect": {"regex": {"range": "all", "patterns": ["[\"'][^\"']{0,16}(?:车架号|vin)[^\"']{0,16}[\"']\\s*:\\s*[\"']([A-Z0-9]{17})[\"']"], "flags": ["caseless"]}}, "validate": {"builtInAlgo": {"algoId": "VIN"}}}, {"id": "frMobile", "detect": {"regex": {"range": "all", "patterns": ["[\"'>:=]((\\+?33)[67]\\d{8})[\"'<,]"], "flags": ["caseless"]}}}, {"id": "rate", "detect": {"regex": {"range": "all", "patterns": ["[\"'][^\"']{0,10}(?:dis_?rate|rate)[\"']\\s*:\\s*[\"']([\\d.]{1,10}%)[\"']"], "flags": ["caseless"]}}}, {"id": "itMobile", "detect": {"regex": {"range": "all", "patterns": ["[\"'>:=]((\\+?39)\\s?3\\d{2} ?\\d{6,7})[\"'<,]"], "flags": ["caseless"]}}}, {"id": "714b929e7168877802f353d7f9adf56e", "detect": {"regex": {"range": "all", "patterns": ["(((2(5[0-5]|[0-4]\\d))|[0-1]?\\d{1,2})(\\.((2(5[0-5]|[0-4]\\d))|[0-1]?\\d{1,2})){3})"], "flags": ["caseless"]}}}, {"id": "jpMobile", "detect": {"regex": {"range": "all", "patterns": ["[\"'>:=]((\\+?81)\\d{1,4}[ \\-]\\d{1,4}[ \\-]\\d{4})[\"'<,]"], "flags": ["caseless"]}}}, {"id": "creditRisk", "detect": {"regex": {"range": "all", "patterns": ["[\"']credit_?Risk[\"']\\s*:\\s*\"([\\w\\+]{1,6})\""], "flags": ["caseless"]}}}, {"id": "833218fc7e96e1b634098147d6f4c0d2", "detect": {"regex": {"range": "all", "patterns": ["[\"'][^\"']{0,16}(?:drugPrice|drug_price)[^\"']{0,16}[\"']\\s*:\\s*[\"']?(([0-9]+\\.[0-9]{1,2})|([1-9][0-9]*))[\"']?"], "flags": ["caseless"]}}}, {"id": "chargeAmt", "detect": {"regex": {"range": "all", "patterns": ["[\"'](?:chrg|charge)_?(?:amt|amount|amnt)[\"']\\s*:\\s*[\"']?(([0-9]+\\.[0-9]{1,2})|([1-9][0-9]*))[\"']?"], "flags": ["caseless"]}}}, {"id": "certType", "detect": {"regex": {"range": "all", "patterns": ["[\"']certi?_?(?:type|type_?name)[\"']\\s*:\\s*[\"']([\\x{4e00}-\\x{9fa5}]{1,20})[\"']"], "flags": ["caseless"]}}}, {"id": "4e4351f853a8f2f1cf2e73fd3e11810c", "detect": {"regex": {"range": "all", "patterns": ["[\"'][^\"']{0,16}(?:loan|credit)_?(?:amt|price|payment|amount)[^\"']{0,16}[\"']\\s*:\\s*[\"']?(([0-9]+\\.[0-9]{1,2})|([1-9][0-9]*))[\"']?"], "flags": ["caseless"]}}}, {"id": "f85223178777fdc272772d27185857d5", "detect": {"regex": {"range": "all", "patterns": ["[\"'][^\"']{0,10}(?:退休证|retire)[^\"']{0,10}[\"']\\s*:\\s*[\"'](\\d{19}[0-9X])[\"']"], "flags": ["caseless"]}}, "validate": {"builtInAlgo": {"algoId": "retire<PERSON><PERSON>"}}}, {"id": "gatMobile", "detect": {"regex": {"range": "all", "patterns": ["((\\+852|\\+853|00852|\\(\\+852\\)|\\(00852\\)|00853|00852-|00853-|\\(\\+853\\)|\\(00853\\)|\\+886|00886|00886-|\\(\\+886\\)|\\(00886\\))([569]\\d{7}|09\\d{8}))"], "flags": ["caseless"]}}}, {"id": "meid", "detect": {"regex": {"range": "all", "patterns": ["[\"'][^\"']{0,16}(?:meid|移动设备号)[^\"']{0,16}[\"']\\s*:\\s*[\"']([A-Z\\d]{14,15})[\"']"], "flags": ["caseless"]}}, "validate": {"builtInAlgo": {"algoId": "meid"}}}, {"id": "antiBankCard", "detect": {"regex": {"range": "all", "patterns": ["(\\d{4}([\\d*]{12}|[\\d*]{13}|[\\d*]{15}))"], "flags": ["caseless"]}}, "validate": {"builtInAlgo": {"algoId": "antiBankCard"}}}, {"id": "accountId", "detect": {"regex": {"range": "all", "patterns": ["[\"'](?:account|acct)_?id[\"']\\s*:\\s*[\"']?(\\d{1,30})[\"']?"], "flags": ["caseless"]}}}, {"id": "queryPwd", "detect": {"regex": {"range": "all", "patterns": ["[\"']query_?(?:password|pwd|passwd)[\"']\\s*:\\s*[\"']([\\w`~!@#$%^&*()_+-=;:,<.>/\\\\?]{3,256})[\"']"], "flags": ["caseless"]}}}, {"id": "c7c38e0cdbe780549bd036ce816cc7dd", "detect": {"regex": {"range": "all", "patterns": ["[\"'](?:[^\"']*(?:药品购买记录|购药记录|售药记录)[^\"']*)[\"']\\s*:\\s*([\\{|\"][^\\{\\}]+[\\}|\"])"], "flags": ["caseless"]}}, "validate": {"builtInAlgo": {"algoId": "drugPurchaseRecord"}}}, {"id": "mobile", "detect": {"regex": {"range": "all", "patterns": ["([\\d-]{11,13})"], "flags": ["caseless"]}}, "validate": {"builtInAlgo": {"algoId": "mobile"}}}, {"id": "auMobile", "detect": {"regex": {"range": "all", "patterns": ["[\"'>:=]((\\+?61)4\\d{8})[\"'<,]"], "flags": ["caseless"]}}}, {"id": "9270d9aa6fa836d3db269f3dcd9bef5c", "detect": {"regex": {"range": "all", "patterns": ["[\"'][^\"']{0,16}(?:drugAmount|drug_amount)[^\"']{0,16}[\"']\\s*:\\s*[\"']?([0-9]+)[\"']?"], "flags": ["caseless"]}}}, {"id": "goodAmt", "detect": {"regex": {"range": "all", "patterns": ["[\"'](?:good|goods)_?(?:amt|amount|price)[\"']\\s*:\\s*[\"']?(([0-9]+\\.[0-9]{1,2})|([1-9][0-9]*))[\"']?"], "flags": ["caseless"]}}}, {"id": "b5952a7a2d5556489f389b7d4f478e10", "detect": {"regex": {"range": "all", "patterns": ["[\"']qq[\"']\\s*:\\s*[\"']?(\\d{5,10})[\"']?"], "flags": ["caseless"]}}}, {"id": "employeeName", "detect": {"regex": {"range": "all", "patterns": ["[\"'](?:employee|staff)_?name[\"']\\s*:\\s*[\"']([\\x{4e00}-\\x{9fa5}]{2,4})[\"']"], "flags": ["caseless"]}}}, {"id": "17440459776693d95bca710dea935df1", "detect": {"builtInAlgo": {"range": "all", "algoId": "companyCreditReport"}}}, {"id": "149d0ab64cd767a9c905496201990633", "detect": {"regex": {"range": "all", "patterns": ["[\"'](?:入院记录|住院记录)[\"']\\s*:\\s*([\\{\"]([^\\{\\}]+)+[\\}\"])"], "flags": ["caseless"]}}, "validate": {"builtInAlgo": {"algoId": "hospitalRecord"}}}, {"id": "32941ad71ed1257bf62226454aa7a1a6", "detect": {"regex": {"range": "all", "patterns": ["[\"'>:=]([\\w\\s]{28,31})[\"',};<]"], "flags": ["caseless"]}}, "validate": {"builtInAlgo": {"algoId": "imPro"}}}, {"id": "clientNo", "detect": {"regex": {"range": "all", "patterns": ["[\"'](?:client|customer|cus|cust|cst)_?no[\"']\\s*:\\s*[\"']?(\\d{5,15})[\"']?"], "flags": ["caseless"]}}}, {"id": "companyPosit", "detect": {"regex": {"range": "all", "patterns": ["[\"'](?:company|com)_?(?:position|posit)[\"']\\s*:\\s*[\"']([\\x{4e00}-\\x{9fa5}]{1,15})[\"']"], "flags": ["caseless"]}}}, {"id": "12ac17d4d1d57571495631a6d38664f2", "detect": {"regex": {"range": "all", "patterns": ["[\"'][^\"']{0,10}(?:居住证|resident|residence)[^\"']{0,10}[\"']\\s*:\\s*[\"']?(\\w{6,})[\"']"], "flags": ["caseless"]}}}, {"id": "bsnLcnsNo", "detect": {"regex": {"range": "all", "patterns": ["[\"'](?:bsnLcns_?No|reg_?no|reg_?id|bsnLcns_?Id|aprv_?No)[\"']\\s*:\\s*[\"']([0-9a-zA-Z]{15,18})[\"']"], "flags": ["caseless"]}}}, {"id": "jdbcUrl", "detect": {"regex": {"range": "all", "patterns": ["[\"'](jdbc:(mysql|oracle|sqlserver|postgresql|mariadb|db2|sap|informix-sqli|hsqldb|h2|derby|sybase):[^\"']{10,128})[\"']"], "flags": ["caseless"]}}}, {"id": "cusTel", "detect": {"regex": {"range": "all", "patterns": ["[\"'](?:client|customer|cus|cust|cst)_?(?:tel|telPhone|phone)[\"']\\s*:\\s*[\"']?((13[0-9]|14[01456879]|15[0-35-9]|16[2567]|17[0-8]|18[0-9]|19[0-35-9])\\d{8})[\"']?"], "flags": ["caseless"]}}}, {"id": "email", "detect": {"regex": {"range": "all", "patterns": ["((\\w{3,26})@((\\w{1,16}\\.){1,3})(\\w{1,6}))[^\\w\\.]"], "flags": ["caseless"]}}, "validate": {"builtInAlgo": {"algoId": "email"}}}, {"id": "094d5954c8b5ecc265ed865f34622192", "detect": {"regex": {"range": "all", "patterns": ["[\"'][^\"']{0,10}(?:证券账号|证券账户|securities)[^\"']{0,10}[\"']\\s*:\\s*[\"']?([0-9a-zA-Z]{3,20})[\"']?"], "flags": ["caseless"]}}}, {"id": "payBackAmt", "detect": {"regex": {"range": "all", "patterns": ["[\"']pay_?back_?(?:amt|amount|price)[\"']\\s*:\\s*[\"']?(([0-9]+\\.[0-9]{1,2})|([1-9][0-9]*))[\"']?"], "flags": ["caseless"]}}}, {"id": "inMobile", "detect": {"regex": {"range": "all", "patterns": ["[\"'>:=]((\\+?91)[789]\\d{9})[\"'<,]"], "flags": ["caseless"]}}}, {"id": "bsnLcnsName", "detect": {"regex": {"range": "all", "patterns": ["[\"']bsnLcns_?name[\"']\\s*:\\s*[\"']([^\"']{1,30})[\"']"], "flags": ["caseless"]}}}, {"id": "28aefe4dc5d8c016a47b2c0b81a7c0a8", "detect": {"regex": {"range": "all", "patterns": ["[\"'][^\"']{0,16}(?:微信|wechat|wx|weixin|wx_*id)[^\"']{0,16}[\"']\\s*:\\s*[\"']([\\w\\-]{6,20})[\"']"], "flags": ["caseless"]}}}, {"id": "3b8a7a84937c403cd70f954a998ce51b", "detect": {"regex": {"range": "all", "patterns": ["[\"'][^\"']{0,10}(?:动态密码|验证码|激活码|随机密码)[^\"']{0,10}[\"']\\s*:\\s*[\"'](([^\"]+|\\\\\")+)[\"']"], "flags": ["caseless"]}}, "validate": {"builtInAlgo": {"algoId": "sms"}}}, {"id": "a3d6688bc364b4c1c9593f7934587b8c", "detect": {"builtInAlgo": {"range": "all", "algoId": "personCreditReport"}}}, {"id": "schoolName", "detect": {"regex": {"range": "all", "patterns": ["[\"'][^\"']{0,10}school_?name[\"']\\s*:\\s*[\"']([\\x{4e00}-\\x{9fa5}]{2,20})[\"']"], "flags": ["caseless"]}}}, {"id": "247582743c8c2e353ec62de0a83d33dd", "detect": {"regex": {"range": "all", "patterns": ["[\"'](?:[^\"]{0,16}发票|bill)[\"']\\s*:\\s*([\\{\"]([^\\{\\}]+)+[\\}\"])"], "flags": ["caseless"]}}, "validate": {"builtInAlgo": {"algoId": "invoice"}}}, {"id": "35bb72315a746c294f398d2800b07bc0", "detect": {"regex": {"range": "all", "patterns": ["[\"'>:=,]((([0-9A-Fa-f]{1,4}:){7}(([0-9A-Fa-f]{1,4}){1}|:))|(([0-9A-Fa-f]{1,4}:){6}((:[0-9A-Fa-f]{1,4}){1}|((22[0-3]|2[0-1][0-9]|[0-1][0-9][0-9]|([0-9]){1,2})([.](25[0-5]|2[0-4][0-9]|[0-1][0-9][0-9]|([0-9]){1,2})){3})|:))|(([0-9A-Fa-f]{1,4}:){5}((:[0-9A-Fa-f]{1,4}){1,2}|:((22[0-3]|2[0-1][0-9]|[0-1][0-9][0-9]|([0-9]){1,2})([.](25[0-5]|2[0-4][0-9]|[0-1][0-9][0-9]|([0-9]){1,2})){3})|:))|(([0-9A-Fa-f]{1,4}:){4}((:[0-9A-Fa-f]{1,4}){1,3}|:((22[0-3]|2[0-1][0-9]|[0-1][0-9][0-9]|([0-9]){1,2})([.](25[0-5]|2[0-4][0-9]|[0-1][0-9][0-9]|([0-9]){1,2})){3})|:))|(([0-9A-Fa-f]{1,4}:){3}((:[0-9A-Fa-f]{1,4}){1,4}|:((22[0-3]|2[0-1][0-9]|[0-1][0-9][0-9]|([0-9]){1,2})([.](25[0-5]|2[0-4][0-9]|[0-1][0-9][0-9]|([0-9]){1,2})){3})|:))|(([0-9A-Fa-f]{1,4}:){2}((:[0-9A-Fa-f]{1,4}){1,5}|:((22[0-3]|2[0-1][0-9]|[0-1][0-9][0-9]|([0-9]){1,2})([.](25[0-5]|2[0-4][0-9]|[0-1][0-9][0-9]|([0-9]){1,2})){3})|:))|(([0-9A-Fa-f]{1,4}:){1}((:[0-9A-Fa-f]{1,4}){1,6}|:((22[0-3]|2[0-1][0-9]|[0-1][0-9][0-9]|([0-9]){1,2})([.](25[0-5]|2[0-4][0-9]|[0-1][0-9][0-9]|([0-9]){1,2})){3})|:))|(:((:[0-9A-Fa-f]{1,4}){1,7}|(:[fF]{4}){0,1}:((22[0-3]|2[0-1][0-9]|[0-1][0-9][0-9]|([0-9]){1,2})([.](25[0-5]|2[0-4][0-9]|[0-1][0-9][0-9]|([0-9]){1,2})){3})|:)))[\"',};<]"], "flags": ["caseless"]}}}, {"id": "96d6da16bb07c984ff2583e3104463cf", "detect": {"regex": {"range": "all", "patterns": ["[\"'](?:国家|国籍|country|nation|state)[\"']\\s*:\\s*[\"']([^\"]{1,16})[\"']"], "flags": ["caseless"]}}, "validate": {"builtInAlgo": {"algoId": "nationality"}}}, {"id": "channelId", "detect": {"regex": {"range": "all", "patterns": ["[\"'](?:channel|ditch|trench)_?(?:id|code)[\"']\\s*:\\s*[\"']([0-9a-zA-Z]{1,30})[\"']"], "flags": ["caseless"]}}}, {"id": "5ae8fd8f9e5110bc2bf111d9f29eb74d", "detect": {"regex": {"range": "all", "patterns": ["[\"'](?:定位|位置|location|地址)[\"']\\s*:\\s*[\"']([^\"']*)[\"']"], "flags": ["caseless"]}}, "validate": {"builtInAlgo": {"algoId": "location"}}}, {"id": "tradeChannel", "detect": {"regex": {"range": "all", "patterns": ["[\"'](?:trade|tran|trans)_?channel[\"']\\s*:\\s*[\"']([^\"]{1,30})[\"']"], "flags": ["caseless"]}}}, {"id": "singaporeIdCard", "detect": {"regex": {"range": "all", "patterns": ["[\"'>:=]([S,T][0-9]{7}[A,B,C,D,E,F,G,H,I,Z,J])[\"',};<]"], "flags": ["caseless"]}}, "validate": {"builtInAlgo": {"algoId": "singaporeIdCard"}}}, {"id": "tranDate", "detect": {"regex": {"range": "all", "patterns": ["[\"'](?:tran|trx|trade|transaction|deal|trans)_?(?:date|time)[\"']\\s*:\\s*[\"']([0-9-:/. ]{8,19})[\"']"], "flags": ["caseless"]}}}, {"id": "6d24c3d02e55cf0c9fc91cf9da32dff7", "detect": {"regex": {"range": "all", "patterns": ["[\"'][a-zA-Z0-9\\_\\-]{0,16}(?:amt|balance|amount|金额|amnt)[\"']\\s*:\\s*[\"']?(([0-9]+\\.[0-9]{1,2})|([1-9][0-9]*))[\"']?"], "flags": ["caseless"]}}}, {"id": "ruMobile", "detect": {"regex": {"range": "all", "patterns": ["[\"'>:=]((\\+?7|8)9\\d{9})[\"'<,]"], "flags": ["caseless"]}}}, {"id": "9c0d06a62d05ab69ac15bf2949d521d6", "detect": {"regex": {"range": "all", "patterns": ["[\"'][^\"']{0,16}(?:职业|occupation|profession|vocation|job)[^\"']{0,16}[\"']\\s*:\\s*[\"']([^\"']{1,25})[\"']"], "flags": ["caseless"]}}, "validate": {"builtInAlgo": {"algoId": "profession"}}}, {"id": "086569f2901b707eaaf2a04d6da7507e", "detect": {"regex": {"range": "all", "patterns": ["[\"'](?:存折|deposit|bankbook|passbook)[^\"]{0,16}\"\\s*:\\s*[\"']?(\\d{14,19})[\"']?"], "flags": ["caseless"]}}, "validate": {"builtInAlgo": {"algoId": "bankBook"}}}, {"id": "antiIdCard", "detect": {"regex": {"range": "all", "patterns": ["(\\d{2}[\\d*]{15}[\\dX*])"], "flags": ["caseless"]}}, "validate": {"builtInAlgo": {"algoId": "antiIdCard"}}}, {"id": "4960f4919922a086c72577b3a565fc1e", "detect": {"regex": {"range": "all", "patterns": ["[\"'](?:验证码|captcha|verified|verification|sms_*code|verify_*code|vcode)[\"']\\s*:\\s*[\"']?(\\d{4,8})[\"']?"], "flags": ["caseless"]}}}, {"id": "swiftCode", "detect": {"regex": {"range": "all", "patterns": ["[\"']swift_?Code[\"']\\s*:\\s*[\"']([0-9a-zA-Z]{6,11})[\"']"], "flags": ["caseless"]}}}, {"filter": {"dict": {"values": ["", "true", "false", "none", "null", "undefined"], "flags": ["caseless"]}, "builtInAlgo": {"algoId": "password"}}, "detect": {"regex": {"patterns": ["(?:userpass|passwords?|passwds?|pwds?|密码)[\"']?\\s*[:：=]\\s*[\"']([a-zA-Z0-9`~!@#$%^&*()_+\\-=;:,<.>/\\\\?]{4,})[\"']"], "flags": ["caseless"], "range": "all"}}, "id": "e20db111f28dd3b98d2ae7c5eb2ccf47", "validate": {"builtInAlgo": {"algoId": "weakPassword"}}}, {"id": "contractAmt", "detect": {"regex": {"range": "all", "patterns": ["[\"'](?:contract|cont)_?(?:amt|amount|amnt)[\"']\\s*:\\s*[\"']?(([0-9]+\\.[0-9]{1,2})|([1-9][0-9]*))[\"']?"], "flags": ["caseless"]}}}, {"id": "orgCode", "detect": {"regex": {"range": "all", "patterns": ["[\"'][^\"']{0,16}(?:org|organization)_?code[\"']\\s*:\\s*[\"']([0-9A-Z]{9})[\"']"], "flags": ["caseless"]}}, "validate": {"builtInAlgo": {"algoId": "orgCode"}}}, {"id": "discountCard", "detect": {"regex": {"range": "all", "patterns": ["[\"']discount_?card[\"']\\s*:\\s*[\"'](\\d{1,30})[\"']"], "flags": ["caseless"]}}}, {"id": "ukMobile", "detect": {"regex": {"range": "all", "patterns": ["[\"'>:=](447\\d{7,11})[\"'<,]"], "flags": ["caseless"]}}}, {"id": "payeeAccNo", "detect": {"regex": {"range": "all", "patterns": ["[\"']payee_?(?:acc|account)_?no[\"']\\s*:\\s*[\"'](\\d{15,19})[\"']"], "flags": ["caseless"]}}}, {"id": "8503946b3eac5117a538a337e6547083", "detect": {"regex": {"range": "all", "patterns": ["[\"'](?:港澳居民来往内地通行证|回乡证)[\"']\\s*:\\s*[\"'](\\w\\d{10})[\"']"], "flags": ["caseless"]}}, "validate": {"builtInAlgo": {"algoId": "gangAo"}}}, {"id": "ad8fc6907f8fdea54ad16f67beb71304", "detect": {"regex": {"range": "all", "patterns": ["[\"'](?:contr_?No|contract|contract_?no)[\"']\\s*:\\s*[\"']([0-9a-zA-Z-/]{5,30})[\"']"], "flags": ["caseless"]}}}, {"id": "c8cfaa6137aa0c5dd918037a55f9223b", "detect": {"regex": {"range": "all", "patterns": ["[\"'](?:性别|gender|sex)[\"']\\s*:\\s*[\"'](男|男士|女|女士|male|female|m|f)[\"']"], "flags": ["caseless"]}}}, {"id": "passport", "detect": {"regex": {"range": "all", "patterns": ["[\"'][^\"']{0,16}(?:passport|护照)[^\"']{0,16}[\"']\\s*:\\s*[\"']([^\"']{1,15})[\"']"], "flags": ["caseless"]}}, "validate": {"builtInAlgo": {"algoId": "passport"}}}, {"id": "67d109282d30fc98499245522bc44af4", "detect": {"regex": {"range": "all", "patterns": ["[\"'][a-zA-Z0-9_\\-]{0,16}(?:余额|balance|money)[\"']\\s*:\\s*[\"']?([0-9\\.]{1,16})[\"']?"], "flags": ["caseless"]}}, "validate": {"builtInAlgo": {"algoId": "balance"}}}, {"id": "d501c1320abc999d143d7f24eebc423e", "detect": {"regex": {"range": "all", "patterns": ["(?:^|[\"'>=, ])([0-9a-fA-F:-]{17})(?:[\"',};< ]|$)"], "flags": ["caseless"]}}, "validate": {"builtInAlgo": {"algoId": "macAddr"}}}, {"id": "instTp", "detect": {"regex": {"range": "all", "patterns": ["[\"'](?:inst|organization|institution)_?(?:tp|type)[\"']\\s*:\\s*[\"']([\\x{4e00}-\\x{9fa5}]{1,9})[\"']"], "flags": ["caseless"]}}, "validate": {"builtInAlgo": {"algoId": "instTp"}}}, {"id": "dealCcy", "detect": {"regex": {"range": "all", "patterns": ["[\"']deal_?(?:ccy|currency)[\"']\\s*:\\s*[\"']([A-Z]{3})[\"']"], "flags": ["caseless"]}}}, {"id": "birthCert", "detect": {"regex": {"range": "all", "patterns": ["[\"'](?:出生证|birth)[\"']\\s*:\\s*\"([a-z]\\d{9})\""], "flags": ["caseless"]}}, "validate": {"builtInAlgo": {"algoId": "birthCert"}}}, {"id": "03a1956dffff12ce7a8715774f068e5f", "detect": {"regex": {"range": "all", "patterns": ["[\"'][^\"']{0,16}(?:台湾身份证|国民身份证|taiwan identity card)[^\"']{0,16}[\"']\\s*:\\s*[\"']([A-Z1-2]\\d{9})[\"']"], "flags": ["caseless"]}}, "validate": {"builtInAlgo": {"algoId": "twIdCard"}}}, {"id": "taiwanId", "detect": {"regex": {"range": "all", "patterns": ["[\"'][^\"']{0,32}(?:台胞证|台湾居民来往大陆通行证)[^\"']{0,32}[\"']\\s*:\\s*[\"']([A-Z0-9\\(\\)]{8,13})[\"']"], "flags": ["caseless"]}}, "validate": {"builtInAlgo": {"algoId": "taiwanId"}}}, {"id": "d100c556e3075dc5917b9c554fa96242", "detect": {"regex": {"range": "all", "patterns": ["[\"'](?:澳门身份证|澳门居民身份证|macau identity card)[\"']\\s*:\\s*[\"']([157]\\d{6}\\(\\d\\))[\"']"], "flags": ["caseless"]}}, "validate": {"builtInAlgo": {"algoId": "maIdCard"}}}, {"id": "1b780a3470b5315a53e8385aae0eaf3f", "detect": {"regex": {"range": "all", "patterns": ["[\"'](?:民族|nation|ethnic|mz)[^\"']{0,16}[\"']\\s*:\\s*[\"']([\\x{4e00}-\\x{9fa5}]{1,5})[\"']"], "flags": ["caseless"]}}, "validate": {"builtInAlgo": {"algoId": "ethnic"}}}, {"filter": {"dict": {"values": ["", "true", "false", "none", "null", "undefined"], "flags": ["caseless"]}, "builtInAlgo": {"algoId": "password"}}, "detect": {"regex": {"patterns": ["(?:userpass|passwords?|passwds?|pwds?|密码)[\"']?\\s*[:：=]\\s*[\"']([a-zA-Z0-9`~!@#$%^&*()_+\\-=;:,<.>/\\\\?]{4,})[\"']"], "flags": ["caseless"], "range": "all"}}, "id": "password"}, {"id": "interest", "detect": {"regex": {"range": "all", "patterns": ["[\"'](?:interests?)[\"']\\s*:\\s*[\"']?(([0-9]+\\.[0-9]{1,2})|([1-9][0-9]*))[\"']?"], "flags": ["caseless"]}}}, {"id": "invNo", "detect": {"regex": {"range": "all", "patterns": ["[\"'](?:inv|invoice)_?no[\"']\\s*:\\s*[\"']?(\\d{8})[\"']?"], "flags": ["caseless"]}}}, {"id": "uniscid", "detect": {"regex": {"range": "all", "patterns": ["([0-9A-HJ-QWERTY]{2}\\d{6}[0-9A-HJ-QWERTY]{10})"], "flags": ["caseless"]}}, "validate": {"builtInAlgo": {"algoId": "uniscid"}}}, {"id": "cityName", "detect": {"regex": {"range": "all", "patterns": ["[\"'][^\"]{0,16}city_?(?:name)?[\"']\\s*:\\s*[\"']([\\x{4e00}-\\x{9fa5}]{2,10})[\"']"], "flags": ["caseless"]}}}, {"id": "thMobile", "detect": {"regex": {"range": "all", "patterns": ["[\"'>:=]((\\+?0?66\\-)\\d{10})[\"'<,]"], "flags": ["caseless"]}}}, {"id": "70a421754369714eadd4b0807e114f95", "detect": {"regex": {"range": "all", "patterns": ["[\"'](?:学历|education|文化程度|教育经历)[\"']\\s*:\\s*[\"']?(小学|初中|高中|中专|专科|大专|本科|研究生|博士)[\"']?"], "flags": ["caseless"]}}}, {"id": "clientName", "detect": {"regex": {"range": "all", "patterns": ["[\"'][^\"']{0,16}(?:client|customer|cus|cust|cst)_?name[\"']\\s*:\\s*[\"']?([^\"']{1,30})[\"']?"], "flags": ["caseless"]}}}, {"id": "antiEmail", "detect": {"regex": {"range": "all", "patterns": ["([a-zA-z0-9][\\w|\\*]{2,25}@(\\w{1,16}\\.){1,3}\\w{1,6})"], "flags": ["caseless"]}}, "validate": {"builtInAlgo": {"algoId": "antiEmail"}}}]