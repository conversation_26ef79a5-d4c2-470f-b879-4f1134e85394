package com.quanzhi.auditapiv2;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.alibaba.nacos.spring.context.annotation.config.NacosPropertySource;
import com.alibaba.nacos.spring.context.annotation.config.NacosPropertySources;
import com.quanzhi.audit.mix.config.MixConfigService;
import com.quanzhi.audit.mix.config.spring.EnableMixConfig;
import com.quanzhi.audit.mix.schdule.application.annotation.EnableLockedScheduling;
import com.quanzhi.auditapiv2.core.service.config.BizModuleProperties;
import com.quanzhi.re.core.function.annotation.EnableAutoFunction;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.EnableAspectJAutoProxy;

//@SpringBootApplication(exclude = {DataSourceAutoConfiguration.class, HibernateJpaAutoConfiguration.class, SecurityAutoConfiguration.class})
@SpringBootApplication //(exclude = JasyptSpringBootAutoConfiguration.class)
@ComponentScan({"com.quanzhi.auditapiv2", "com.quanzhi.audit_core.extract",
        "com.quanzhi.datataskcore", "com.quanzhi.audit_core.common.config",
        "com.quanzhi.plugincore", "com.quanzhi.operate","com.quanzhi.audit",
        "com.quanzhi.auditapiv2.report.data","com.quanzhi.audit_core.resource.fetcher.client","com.quanzhi.login","com.quanzhi.re","com.java3y.austin.service"})
@NacosPropertySources({
        @NacosPropertySource(
                dataId = MixConfigService.DATA_ID,
                groupId = MixConfigService.GROUP_ID
        ),
        @NacosPropertySource(
                dataId = BizModuleProperties.DATA_ID,
                groupId = BizModuleProperties.GROUP,autoRefreshed = true
        ),
        @NacosPropertySource(dataId = "auditapiv2.bootstrap.properties", groupId = "auditapiv2", autoRefreshed = true, first = true)
})
@EnableMixConfig
@EnableLockedScheduling
@EnableAutoFunction
@EnableAspectJAutoProxy
public class MainApplication {
    public static void main(String[] args) {
        SpringApplication application = new SpringApplication(MainApplication.class);
        application.setAddCommandLineProperties(true);
        JSON.DEFAULT_GENERATE_FEATURE |= SerializerFeature.DisableCircularReferenceDetect.getMask();

        application.run(args);
    }

}
