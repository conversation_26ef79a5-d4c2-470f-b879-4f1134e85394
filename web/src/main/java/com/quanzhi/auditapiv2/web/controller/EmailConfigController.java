package com.quanzhi.auditapiv2.web.controller;

import com.alibaba.fastjson.JSON;
import com.quanzhi.auditapiv2.common.util.dto.ListOutputDto;
import com.quanzhi.auditapiv2.common.util.dto.ResponseVo;
import com.quanzhi.auditapiv2.common.util.entity.EmailConfig;
import com.quanzhi.auditapiv2.common.util.utils.ConstantUtil;
import com.quanzhi.auditapiv2.common.util.utils.DataUtil;
import com.quanzhi.auditapiv2.core.service.manager.web.IEmailConfigService;
import com.quanzhi.auditapiv2.web.common.annotation.OperateLog;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 《邮箱配置Controller》
 *
 * <AUTHOR> [<EMAIL>]
 * @Project:
 * @Module ID:
 * @Comments:
 * @JDK version used:      <JDK1.8>
 * @since 2020-04-08-上午9:56:10
 */
@RestController
@RequestMapping("/api/emailConfig")
public class EmailConfigController {

    private Logger logger = LoggerFactory.getLogger(EmailConfigController.class);

    //邮箱配置service
    private final IEmailConfigService emailConfigServiceImpl;

    public EmailConfigController(IEmailConfigService emailConfigServiceImpl) {
        this.emailConfigServiceImpl = emailConfigServiceImpl;
    }

    /**
     * 查询邮箱配置列表
     *
     * @param
     * @return
     * @Comments: <对此方法的描述，可以引用系统设计中的描述>
     * <AUTHOR> [<EMAIL>]
     * @since 2020-04-08 9:56
     */
    @RequestMapping(value = "listData.do", method = RequestMethod.POST)
    @ApiOperation(value = "查询邮箱配置列表")
    public ResponseVo<ListOutputDto<EmailConfig>> listData(Integer page, Integer limit) {
        logger.warn("通知设置Controller》查询通知设置列表");

        ListOutputDto<EmailConfig> data = new ListOutputDto<>();
        try {
            List<EmailConfig> emailConfigList = emailConfigServiceImpl.getEmailConfigList();
            data.setTotalCount(emailConfigList.size());
            data.setRows(emailConfigList);
        } catch (Exception e) {
            logger.error("查询通知设置列表", e);
            return ResponseVo.error(e.getMessage(), 233);
        }
        return ResponseVo.ok(data);
    }

    /**
     * id查询邮箱配置
     *
     * @param
     * @return
     * @Comments: <对此方法的描述，可以引用系统设计中的描述>
     * <AUTHOR> [<EMAIL>]
     * @since 2020-04-08 9:56
     */
    @RequestMapping(value = "getById.do", method = RequestMethod.POST)
    @ApiOperation(value = "id查询邮箱配置")
    public ResponseVo<EmailConfig> getMailConfig(String id) {
        logger.warn("邮箱配置Controller》id查询邮箱配置");

        try {
            if (DataUtil.isNotEmpty(id)) {
                EmailConfig emailConfig = emailConfigServiceImpl.getEmailConfigById(id);
                return ResponseVo.ok(emailConfig);
            } else {
                return ResponseVo.error("id不得为空", 233);
            }
        } catch (Exception e) {
            logger.error("id查询邮箱配置", e);
            return ResponseVo.error(e.getMessage(), 233);
        }
    }

    /**
     * 保存邮箱配置
     *
     * @param
     * @return
     * @Comments: <对此方法的描述，可以引用系统设计中的描述>
     * <AUTHOR> [<EMAIL>]
     * @since 2020-04-08 9:56
     */
    @OperateLog(value = "配置邮箱服务器", type = "新增", module = "配置")
    @RequestMapping(value = "save.do", method = RequestMethod.POST)
    @ApiOperation(value = "保存邮箱配置")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "params", value = "保存内容", required = true, paramType = "query", dataType = "String")
    })
    public ResponseVo<String> save(@RequestParam(value = "params", defaultValue = "") String params) {
        logger.warn("邮箱配置Controller》保存邮箱配置");

        try {
            if (DataUtil.isNotEmpty(params)) {
                EmailConfig emailConfig = JSON.parseObject(params, EmailConfig.class);
                if (emailConfig != null) {

                    if (DataUtil.isNotEmpty(emailConfig.getServerType())) {

                        if (emailConfig.getServerType().intValue() == EmailConfig.ServerTypeEnum.EXCHANGE.value()) {
                            if (DataUtil.isEmpty(emailConfig.getServer())) {
                                return ResponseVo.error("请填写SMTP服务器", 233);
                            } else if (DataUtil.isEmpty(emailConfig.getUsername())) {
                                return ResponseVo.error("请填写邮箱地址", 233);
                            } else if (DataUtil.isEmpty(emailConfig.getPassword())) {
                                return ResponseVo.error("请填写密码", 233);
                            }
                        } else if (emailConfig.getServerType().intValue() == EmailConfig.ServerTypeEnum.THIRD_PARTY.value()) {
                            if (DataUtil.isEmpty(emailConfig.getServer())) {
                                return ResponseVo.error("请填写SMTP服务器", 233);
                            } else if (DataUtil.isEmpty(emailConfig.getPort())) {
                                return ResponseVo.error("请填写服务器端口", 233);
                            } else if (DataUtil.isEmpty(emailConfig.getEmail())) {
                                return ResponseVo.error("请填写邮箱地址", 233);
                            } else if (DataUtil.isEmpty(emailConfig.getPassword())) {
                                return ResponseVo.error("请填写密码", 233);
                            }
                        }
                    } else {
                        return ResponseVo.error("请填写服务器类型", 233);
                    }
                    emailConfigServiceImpl.saveEmailConfig(emailConfig);
                } else {
                    return ResponseVo.error(ConstantUtil.PARAMS_ERROR, 233);
                }
            } else {
                return ResponseVo.error("params不得为空", 233);
            }
        } catch (Exception e) {
            logger.error("保存邮箱配置", e);
            return ResponseVo.error(e.getMessage(), 233);
        }
        return ResponseVo.ok();
    }

    /**
     * 测试邮件发送
     *
     * @param
     * @return
     * @Comments: <对此方法的描述，可以引用系统设计中的描述>
     * <AUTHOR> [<EMAIL>]
     * @since 2020-04-08 9:56
     */
    @RequestMapping(value = "sendTest.do", method = RequestMethod.POST)
    @ApiOperation(value = "测试邮件发送")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "params", value = "邮箱配置内容", required = true, paramType = "query", dataType = "String"),
            @ApiImplicitParam(name = "recipient", value = "收件地址", required = true, paramType = "query", dataType = "String")
    })
    public ResponseVo<String> sendTest(String params, String recipient) {
        logger.warn("邮箱配置Controller》测试邮件发送");

        try {
            if (DataUtil.isNotEmpty(params)) {
                EmailConfig emailConfig = JSON.parseObject(params, EmailConfig.class);
                if (emailConfig != null) {

                    if (DataUtil.isNotEmpty(emailConfig.getServerType())) {

                        if (emailConfig.getServerType().intValue() == EmailConfig.ServerTypeEnum.EXCHANGE.value()) {
                            if (DataUtil.isEmpty(emailConfig.getServer())) {
                                return ResponseVo.error("请填写SMTP服务器", 233);
                            } else if (DataUtil.isEmpty(emailConfig.getUsername())) {
                                return ResponseVo.error("请填写邮箱地址", 233);
                            } else if (DataUtil.isEmpty(emailConfig.getPassword())) {
                                return ResponseVo.error("请填写密码", 233);
                            }
                        } else if (emailConfig.getServerType().intValue() == EmailConfig.ServerTypeEnum.THIRD_PARTY.value()) {
                            if (DataUtil.isEmpty(emailConfig.getServer())) {
                                return ResponseVo.error("请填写SMTP服务器", 233);
                            } else if (DataUtil.isEmpty(emailConfig.getPort())) {
                                return ResponseVo.error("请填写服务器端口", 233);
                            } else if (DataUtil.isEmpty(emailConfig.getEmail())) {
                                return ResponseVo.error("请填写邮箱地址", 233);
                            } else if (DataUtil.isEmpty(emailConfig.getPassword())) {
                                return ResponseVo.error("请填写密码", 233);
                            }
                        }
                    } else {
                        return ResponseVo.error("请填写服务器类型", 233);
                    }
                    if (DataUtil.isEmpty(recipient)) {
                        return ResponseVo.error("请填写收件人", 233);
                    }
                    emailConfigServiceImpl.sendTest(emailConfig, recipient);
                } else {
                    return ResponseVo.error(ConstantUtil.PARAMS_ERROR, 233);
                }
            } else {
                return ResponseVo.error("params不得为空", 233);
            }
        } catch (Exception e) {
            logger.error("测试邮件发送", e);
            return ResponseVo.error(e.getMessage(), 233);
        }
        return ResponseVo.ok();
    }
}
