package com.quanzhi.auditapiv2.web.controller;


import com.quanzhi.audit.mix.permission.annotation.Permission;
import com.quanzhi.audit.mix.permission.domain.enums.ResourceEnum;
import com.quanzhi.audit_core.common.model.AppFeatureLabel;
import com.quanzhi.audit_core.common.model.FeatureLabel;
import com.quanzhi.auditapiv2.common.dal.dto.query.NacosFilterDto;
import com.quanzhi.auditapiv2.common.dal.dto.query.Predicate;
import com.quanzhi.auditapiv2.common.util.dto.ListOutputDto;
import com.quanzhi.auditapiv2.common.util.utils.DataUtil;
import com.quanzhi.auditapiv2.common.util.utils.HttpResponseUtil;
import com.quanzhi.auditapiv2.common.util.utils.MD5Util;
import com.quanzhi.auditapiv2.common.util.utils.StringUtils;
import com.quanzhi.auditapiv2.core.service.manager.web.IAppFeatureLabelService;
import com.quanzhi.auditapiv2.core.service.manager.web.impl.RuleConfigService;
import com.quanzhi.auditapiv2.web.common.annotation.DirectiveSync;
import com.quanzhi.re.core.domain.entity.po.RuleConfigPO;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiResponse;
import io.swagger.annotations.ApiResponses;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

@RestController
@RequestMapping("/api/appFeatureLabel")
public class AppFeatureLabelController {


    @Autowired
    private IAppFeatureLabelService featureLabelService;

    @Resource
    private RuleConfigService ruleConfigService;

    /**
     * 保存接口分类
     *
     * @param
     * @return
     */
    @RequestMapping(value = "/save.do", method = RequestMethod.POST)
    @DirectiveSync(key = "id", needNotify = true, retry = false, remark = "操作应用标签")
    @Permission(type = ResourceEnum.ACTION, value = "WRITE")
    public String save(
            HttpServletRequest request,
            @RequestBody AppFeatureLabel featureLabel

    ) {
        if (DataUtil.isNotEmpty(request.getSession().getAttribute("username"))) {
            String username = request.getSession().getAttribute("username").toString();
            featureLabel.setOperateName(username);
        }
        if (StringUtils.isNullOrEmpty(featureLabel.getName())) {
            throw new IllegalArgumentException("标签名称不能为空！");
        }
        if (featureLabel.getName().length() > 20) {
            throw new IllegalArgumentException("标签名称过长！");
        }
        if(featureLabel.getEnable()==null){
            featureLabel.setEnable(true);
        }
        if(featureLabel.getType()==null){
            featureLabel.setType(AppFeatureLabel.TypeEnum.CUSTOM.val());
        }
        featureLabel.setUpdateTime(System.currentTimeMillis());
        AppFeatureLabel current = new AppFeatureLabel();
        BeanUtils.copyProperties(featureLabel, current);
        if(DataUtil.isEmpty(featureLabel.getId())){
            featureLabel.setCreateTime(System.currentTimeMillis());
        }
        featureLabelService.save(current);
        return HttpResponseUtil.success();
    }

    /**
     * 分页获取接口分类列表
     *
     * @param page
     * @param limit
     * @return
     */
    @RequestMapping(value = "/page.do", method = RequestMethod.GET)
    @ApiImplicitParams({
            @ApiImplicitParam(name = "page", value = "page",
                    required = true, paramType = "query", dataType = "Integer"),
            @ApiImplicitParam(name = "limit", value = "limit",
                    required = true, paramType = "query", dataType = "Integer"),
            @ApiImplicitParam(name = "name", value = "name",
                    required = false, paramType = "query", dataType = "String")
    })
    @ApiResponses({
            @ApiResponse(code = 200,
                    message = "{\"success\":true,\"errorCode\":0,\"data\":{\"rows\":[{\"id\":\"xxx\",\"name\":\"abc\",\"desc\":\"123\",\"enabled\":true,\"operateName\":\"mingming\",\"createTime\":\"1589426873000\",\"Type\":1,\"classificationType\":\"APP\"}],\"totalCount\":1}}",
                    response = FeatureLabel.class
            ),
    })
    public String page(
            @RequestParam(value = "page", defaultValue = "1") Integer page,
            @RequestParam(value = "limit", defaultValue = "10") Integer limit,
            @RequestParam(value = "name", defaultValue = "") String name
    ) {
        List<NacosFilterDto> nacosFilterDtos=new ArrayList<>();
        if(DataUtil.isNotEmpty(name)){
            NacosFilterDto nacosFilterDto=new NacosFilterDto();
            nacosFilterDto.setKey("name");
            nacosFilterDto.setValue(name);
            nacosFilterDto.setPredicate(Predicate.REGEX);
            nacosFilterDtos.add(nacosFilterDto);
        }
        ListOutputDto<AppFeatureLabel> data = featureLabelService.page(page, limit,nacosFilterDtos);
        if (data.getRows() != null && !data.getRows().isEmpty()){
            List<String> ids = data.getRows().stream().map(AppFeatureLabel::getId).collect(Collectors.toList());
            List<RuleConfigPO> configServiceByIds = ruleConfigService.findByIds(ids);
            data.getRows().stream().forEach(e->{
                if(e.getType()==null){
                    e.setType(AppFeatureLabel.TypeEnum.SYSTEM.val());
                }
                handle(e,configServiceByIds);
            });
        }

        return HttpResponseUtil.success(data);
    }
    private void handle(AppFeatureLabel appFeatureLabel, List<RuleConfigPO> configServiceByIds){
        if(appFeatureLabel.getNewModeFlag() != null && appFeatureLabel.getNewModeFlag()){
            if(configServiceByIds != null && !configServiceByIds.isEmpty()){
                Optional<RuleConfigPO> configPOOptional = configServiceByIds.stream().filter(i -> appFeatureLabel.getId().equals(i.getId())).findAny();
                if(configPOOptional.isPresent()){
                    appFeatureLabel.setEnable(configPOOptional.get().getEnabled());
                    appFeatureLabel.setMatchRule(configPOOptional.get().getMatchRule());
                }
            }
        }
    }

    /**
     * 根据ID，获取详情
     *
     * @param id
     * @return
     */
    @RequestMapping(value = "/getById.do", method = RequestMethod.GET)
    @ApiResponses({
            @ApiResponse(code = 200,
                    message = "{\"success\":true,\"errorCode\":0,\"data\":{\"id\":\"xxx\",\"name\":\"abc\",\"desc\":\"123\",\"enabled\":true,\"operateName\":\"mingming\",\"createTime\":\"1589426873000\",\"Type\":1,\"classificationType\":\"APP\"}}",
                    response = FeatureLabel.class
            ),
    })
    public String getById(
            @RequestParam(value = "id", defaultValue = "") String id
    ) {
        AppFeatureLabel appFeatureLabel = featureLabelService.getById(id);
        if(appFeatureLabel.getNewModeFlag() != null && appFeatureLabel.getNewModeFlag()){
            RuleConfigPO configPO = ruleConfigService.findById(id);
            if(configPO != null){
                appFeatureLabel.setEnable(configPO.getEnabled());
                appFeatureLabel.setMatchRule(configPO.getMatchRule());
            }
        }
        return HttpResponseUtil.success();
    }

    /**
     * 根据ID，删除分类
     *
     * @param ids
     * @return
     */
    @RequestMapping(value = "/delete.do", method = RequestMethod.POST)
    @DirectiveSync(key = "ids", needNotify = true, retry = false, remark = "删除应用标签")
    @Permission(type = ResourceEnum.ACTION, value = "WRITE")
    public String delete(@RequestParam String ids) {
        if (DataUtil.isNotEmpty(ids)) {
            String[] idArray = ids.split(",");
            List<String> idList = Arrays.asList(idArray);
            featureLabelService.remove(idList);
        }
        return HttpResponseUtil.success();
    }
}
