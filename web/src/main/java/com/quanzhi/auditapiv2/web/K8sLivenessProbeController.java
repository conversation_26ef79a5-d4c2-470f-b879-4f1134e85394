package com.quanzhi.auditapiv2.web;

import com.quanzhi.auditapiv2.common.util.utils.HttpResponseUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;

/**
 * <AUTHOR>
 * @date 2021/5/31 4:11 PM
 */
@RestController
@Slf4j
@RequestMapping("/api/k8sLivenessProbe")
public class K8sLivenessProbeController {

    private final MongoTemplate mongoTemplate;

    public K8sLivenessProbeController(MongoTemplate mongoTemplate) {
        this.mongoTemplate = mongoTemplate;
    }

    @GetMapping(value = "available.do")
    public String available(HttpServletResponse response) {
        try {
            mongoTemplate.executeCommand("{ping:1}");
        } catch (Exception e) {
            log.error("mongo execute {ping:1} error", e);
            response.setStatus(500);
            return HttpResponseUtil.error();
        }
        return HttpResponseUtil.success();
    }

}
