package com.quanzhi.auditapiv2.web.controller;


import com.quanzhi.audit_core.common.model.ApiClassification;
import com.quanzhi.audit_core.common.model.AppClassification;
import com.quanzhi.audit_core.common.model.FeatureLabel;
import com.quanzhi.auditapiv2.common.dal.enums.NacosConfigEnum;
import com.quanzhi.auditapiv2.common.util.dto.ListOutputDto;
import com.quanzhi.auditapiv2.common.util.utils.DataUtil;
import com.quanzhi.auditapiv2.common.util.utils.HttpResponseUtil;
import com.quanzhi.auditapiv2.core.service.manager.web.IAppClassificationService;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiResponse;
import io.swagger.annotations.ApiResponses;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.List;
import java.util.Optional;

@RestController
@RequestMapping("/api/appClassification")
public class AppClassificationController {
    @Autowired
    private IAppClassificationService appClassificationService;

    /**
     * 根据资源类型，决定要操作的nacos配置
     * @param sourceType
     */
    private void initCollectionName(String sourceType){
        if (sourceType.equals(ApiClassification.SourceTypeEnum.HTTP.name())){
            appClassificationService.initCollectionName(NacosConfigEnum.HTTP_APP_CLASSIFICATION.getNacosCollection());
        } else if (sourceType.equals(ApiClassification.SourceTypeEnum.FTP.name())){
            appClassificationService.initCollectionName(NacosConfigEnum.FTP_APP_CLASSIFICATION.getNacosCollection());
        } else if (sourceType.equals(ApiClassification.SourceTypeEnum.EMAIL.name())){
            appClassificationService.initCollectionName(NacosConfigEnum.EMAIL_APP_CLASSIFICATION.getNacosCollection());
        }
    }

    /**
     * 保存接口分类
     * @param
     * @return
     */
    @RequestMapping(value = "/save.do", method = RequestMethod.POST)
    @ResponseBody
    public String save(
            HttpServletRequest request,
            @RequestBody AppClassification appClassification

    ) {
        if(DataUtil.isNotEmpty( request.getSession().getAttribute("username") )) {
            String username = request.getSession().getAttribute("username").toString();
            appClassification.setOperateName(username);
        }

        if(DataUtil.isEmpty( appClassification.getId() )) {
            appClassification.setCreateTime( System.currentTimeMillis() );
        }
        appClassification.setUpdateTime(System.currentTimeMillis());

        this.initCollectionName( Optional.ofNullable(appClassification.getSourceType()).orElse("HTTP") );

        appClassificationService.save(appClassification);
        return HttpResponseUtil.success();
    }

    /**
     * 分页获取接口分类列表
     * @param page
     * @param limit
     * @return
     */
    @RequestMapping(value = "/page.do", method = RequestMethod.GET)
    @ApiImplicitParams({
            @ApiImplicitParam(name = "sourceType", value = "sourceType:HTTP/FTP/EMAIL",
                    required = false, paramType = "query", dataType = "String"),
            @ApiImplicitParam(name = "page", value = "page",
                    required = true, paramType = "query", dataType = "Integer"),
            @ApiImplicitParam(name = "limit", value = "limit",
                    required = true, paramType = "query", dataType = "Integer"),
    })
    @ApiResponses({
            @ApiResponse(code = 200,
                    message = "{\"success\":true,\"errorCode\":0,\"data\":{\"rows\":[{\"id\":\"xxx\",\"name\":\"abc\",\"desc\":\"123\",\"enabled\":true,\"operateName\":\"mingming\",\"createTime\":\"1589426873000\",\"Type\":1,\"classificationType\":\"APP\"}],\"totalCount\":1}}",
                    response = FeatureLabel.class
            ),
    })
    public String page(
            @RequestParam(value = "sourceType", defaultValue = "",required = false) String sourceType,
            @RequestParam(value = "page", defaultValue = "1") Integer page,
            @RequestParam(value = "limit", defaultValue = "10") Integer limit
    ) {
        this.initCollectionName(sourceType);

        ListOutputDto<AppClassification> data = appClassificationService.page(sourceType, page, limit);
        return HttpResponseUtil.success(data);
    }

    /**
     * 根据ID，获取详情
     * @param id
     * @return
     */
    @RequestMapping(value = "/getById.do", method = RequestMethod.GET)
    @ApiResponses({
            @ApiResponse(code = 200,
                    message = "{\"success\":true,\"errorCode\":0,\"data\":{\"id\":\"xxx\",\"name\":\"abc\",\"desc\":\"123\",\"enabled\":true,\"operateName\":\"mingming\",\"createTime\":\"1589426873000\",\"Type\":1,\"classificationType\":\"APP\"}}",
                    response = FeatureLabel.class
            ),
    })
    public String getById(
            @RequestParam(value = "sourceType", defaultValue = "",required = false) String sourceType,
            @RequestParam(value = "id", defaultValue = "") String id
    ) {
        this.initCollectionName(sourceType);

        return HttpResponseUtil.success(appClassificationService.getById(id));
    }

    /**
     * 根据ID，删除分类
     * @param ids
     * @return
     */
    @RequestMapping(value = "/delete.do", method = RequestMethod.POST)
    public String delete(@RequestParam(value = "sourceType", defaultValue = "",required = false) String sourceType,
                         @RequestParam List<String> ids) {
        this.initCollectionName(sourceType);

        appClassificationService.delete(ids);
        return HttpResponseUtil.success();
    }
}
