package com.quanzhi.auditapiv2.web.controller;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.quanzhi.audit_core.common.model.EventFilterPlugin;
import com.quanzhi.auditapiv2.common.util.dto.ListOutputDto;
import com.quanzhi.auditapiv2.common.util.utils.ConstantUtil;
import com.quanzhi.auditapiv2.common.util.utils.DataUtil;
import com.quanzhi.auditapiv2.common.util.utils.HttpResponseUtil;
import com.quanzhi.auditapiv2.core.service.manager.web.IEventFilterPluginService;
import com.quanzhi.auditapiv2.web.common.annotation.OperateLog;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import java.util.HashMap;
import java.util.Map;

/**
 *
 * 《事件插件过滤Controller》
 *
 *
 * @Project:
 * @Module ID:
 * @Comments:
 * @JDK version used:      <JDK1.8>
 * <AUTHOR> [<EMAIL>]
 * @since 2020-04-08-上午9:56:10
 */
@RestController
@RequestMapping("/api/eventFilterPlugin")
public class EventFilterPluginController {
    private Logger logger = LoggerFactory.getLogger(EventFilterPluginController.class);

    //事件插件过滤service
    @Autowired
    private IEventFilterPluginService eventFilterPluginServiceImpl;

    /**
     * 查询事件插件过滤列表(分页)
     * @Comments:  <对此方法的描述，可以引用系统设计中的描述>
     * <AUTHOR> [<EMAIL>]
     * @since 2020-04-08 9:56
     * @param
     * @return
     */
    @RequestMapping(value = "listData.do", method = RequestMethod.POST)
    @ApiOperation(value = "查询事件插件过滤列表(分页)")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "page", value = "当前页", required = true, paramType = "query", dataType = "int"),
            @ApiImplicitParam(name = "limit", value = "每页显示条数", required = true, paramType = "query", dataType = "int"),
            @ApiImplicitParam(name = "type", value = "插件类型", required = true, paramType = "query", dataType = "String")
    })
    public String listData(
            Integer page,
            Integer limit,
            String type
            ) {
        logger.warn("事件插件过滤Controller》查询事件插件过滤列表(分页)");

        ListOutputDto<EventFilterPlugin> data = null;
        try {
            data = eventFilterPluginServiceImpl.getEventFilterPluginList(page, limit,type);
        } catch (Exception e) {
            logger.error("查询事件插件过滤列表(分页)", e);
            return HttpResponseUtil.error(e.getMessage());
        }
        return HttpResponseUtil.success(data);
    }

    /**
     * id查询事件插件过滤详情
     * @Comments:  <对此方法的描述，可以引用系统设计中的描述>
     * <AUTHOR> [<EMAIL>]
     * @since 2020-04-08 9:56
     * @param
     * @return
     */
    @RequestMapping(value = "getById.do", method = RequestMethod.POST)
    @ApiOperation(value = "id查询事件插件过滤详情")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "事件插件过滤id", required = true, paramType = "query", dataType = "String")
    })
    public String getById(String id) {
        logger.warn("事件插件过滤Controller》id查询事件插件过滤详情");

        try {
            if(DataUtil.isNotEmpty(id)){
                EventFilterPlugin eventFilterPlugin = eventFilterPluginServiceImpl.getEventFilterPluginById(id);
                return HttpResponseUtil.success(eventFilterPlugin);
            }else{
                return HttpResponseUtil.error("id不得为空");
            }
        } catch (Exception e) {
            logger.error("id查询事件插件过滤详情", e);
            return HttpResponseUtil.error(e.getMessage());
        }
    }

    /**
     * 新增事件插件过滤
     * @Comments:  <对此方法的描述，可以引用系统设计中的描述>
     * <AUTHOR> [<EMAIL>]
     * @since 2020-04-08 9:56
     * @param
     * @return
     */
    @OperateLog(value="新增事件插件过滤", type = "新增", module = "配置")
    @RequestMapping(value = "add.do", method = RequestMethod.POST)
    @ApiOperation(value = "新增事件插件过滤")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "params", value = "新增内容", required = true, paramType = "query", dataType = "String"),
            @ApiImplicitParam(name = "file", value = "插件文件", required = true, paramType = "query", dataType = "String")
    })
    public String add(@RequestParam(value = "params", defaultValue = "") String params, MultipartFile file) {
        logger.warn("事件插件过滤Controller》新增事件插件过滤");

        try {
            if(DataUtil.isNotEmpty(params)) {

                EventFilterPlugin eventFilterPlugin = JSONArray.parseObject(params, EventFilterPlugin.class);
                if(eventFilterPlugin != null){

                    if(DataUtil.isNotEmpty(eventFilterPlugin.getPluginName())) {
                        Long count = eventFilterPluginServiceImpl.isExist(eventFilterPlugin.getPluginName());
                        if(count > 0) {
                            return HttpResponseUtil.error("插件名称已存在");
                        }
                    }
                    eventFilterPluginServiceImpl.addEventFilterPlugin(eventFilterPlugin, file);
                }else{
                    return HttpResponseUtil.error(ConstantUtil.PARAMS_ERROR);
                }
            }else {
                return HttpResponseUtil.error("params不得为空");
            }
        } catch (Exception e) {
            logger.error("新增事件插件过滤", e);
            return HttpResponseUtil.error(e.getMessage());
        }
        return HttpResponseUtil.success();
    }

    /**
     * 编辑事件插件过滤
     * @Comments:  <对此方法的描述，可以引用系统设计中的描述>
     * <AUTHOR> [<EMAIL>]
     * @since 2020-04-08 9:56
     * @param
     * @return
     */
    @OperateLog(value="修改事件插件过滤", type = "编辑", module = "配置")
    @RequestMapping(value = "edit.do", method = RequestMethod.POST)
    @ApiOperation(value = "编辑事件插件过滤")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "params", value = "编辑内容", required = true, paramType = "query", dataType = "String")
    })
    public String edit(@RequestParam(value = "params", defaultValue = "") String params) {
        logger.warn("事件插件过滤Controller》编辑事件插件过滤");

        try {
            if(DataUtil.isNotEmpty(params)) {

                EventFilterPlugin eventFilterPlugin = JSON.parseObject(params, EventFilterPlugin.class);
                if(eventFilterPlugin != null){

                    if(DataUtil.isNotEmpty(eventFilterPlugin.getPluginName())) {
                        EventFilterPlugin eventFilterPluginOld = eventFilterPluginServiceImpl.getEventFilterPluginById(eventFilterPlugin.getId());
                        Long count = eventFilterPluginServiceImpl.isExist(eventFilterPlugin.getPluginName());
                        if(count > 0 && !eventFilterPluginOld.getPluginName().equals(eventFilterPlugin.getPluginName())) {
                            return HttpResponseUtil.error("插件名称已存在");
                        }
                    }
                    eventFilterPluginServiceImpl.editEventFilterPlugin(eventFilterPlugin);
                }else{
                    return HttpResponseUtil.error(ConstantUtil.PARAMS_ERROR);
                }
            }else {
                return HttpResponseUtil.error("params不得为空");
            }
        } catch (Exception e) {
            logger.error("编辑事件插件过滤", e);
            return HttpResponseUtil.error(e.getMessage());
        }
        return HttpResponseUtil.success();
    }

    /**
     * 批量删除事件插件过滤
     * @Comments:  <对此方法的描述，可以引用系统设计中的描述>
     * <AUTHOR> [<EMAIL>]
     * @since 2020-04-08 9:56
     * @param
     * @return
     */
    @OperateLog(value="删除事件插件过滤", type = "删除", module = "配置")
    @RequestMapping(value = "del.do", method = RequestMethod.POST)
    @ApiOperation(value = "批量删除事件插件过滤")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "ids", value = "事件插件过滤id，逗号分隔", required = true, paramType = "query", dataType = "String")
    })
    public String del(String ids, String appIds) {
        logger.warn("事件插件过滤Controller》批量删除事件插件过滤");

        try {
            if(DataUtil.isNotEmpty(ids)) {
                eventFilterPluginServiceImpl.delEventFilterPlugin(ids);
            }else {
                return HttpResponseUtil.error("ids不得为空");
            }
        } catch (Exception e) {
            logger.error("批量删除事件插件过滤", e);
            return HttpResponseUtil.error(e.getMessage());
        }
        return HttpResponseUtil.success();
    }

    /**
     * 启动历史数据清理
     * @Comments:  <对此方法的描述，可以引用系统设计中的描述>
     * <AUTHOR> [<EMAIL>]
     * @since 2020-04-08 9:56
     * @param
     * @return
     */
    @OperateLog(value="启动历史数据清理", type = "新增", module = "配置")
    @RequestMapping(value = "start.do", method = RequestMethod.POST)
    @ApiOperation(value = "启动历史数据清理")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "params", value = "插件参数", required = false, paramType = "query", dataType = "String")
    })
    public String start(String params) {
        logger.warn("事件插件过滤Controller》启动历史数据清理");

        try {
            if(DataUtil.isNotEmpty(params)) {
                Map<String, String> map = new HashMap<String, String>();
                if(DataUtil.isNotEmpty(params)){
                    map = (Map<String, String>) JSON.parse(params);
                }
                return HttpResponseUtil.success(eventFilterPluginServiceImpl.start(map));
            }else {
                return HttpResponseUtil.error("id不得为空");
            }
        } catch (Exception e) {
            logger.error("启动历史数据清理", e);
            return HttpResponseUtil.error(e.getMessage());
        }
    }
}
