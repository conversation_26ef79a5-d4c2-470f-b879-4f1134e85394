package com.quanzhi.auditapiv2.web.controller;

import com.quanzhi.audit.mix.permission.annotation.Permission;
import com.quanzhi.audit.mix.permission.domain.enums.ResourceEnum;
import com.quanzhi.auditapiv2.common.dal.dao.mongo.In;
import com.quanzhi.auditapiv2.common.dal.dto.SmartFilterRuleDto;
import com.quanzhi.auditapiv2.common.dal.dto.filter.SmartFilterCriteriaDTO;
import com.quanzhi.auditapiv2.common.util.dto.ListOutputDto;
import com.quanzhi.auditapiv2.common.util.dto.ResponseVo;
import com.quanzhi.auditapiv2.common.util.utils.DataUtil;
import com.quanzhi.auditapiv2.core.service.manager.web.rule.ISmartFilterRuleService;
import com.quanzhi.metabase.core.model.filter.SmartFilterRule;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 《智能过滤规则Controller》
 *
 * <AUTHOR> [<EMAIL>]
 * @Project:
 * @Module ID:
 * @Comments:
 * @JDK version used:      <JDK1.8>
 * @since 2022-01-07-上午9:56:10
 */
@RestController
@RequestMapping("/api/smartFilterRule")
public class SmartFilterRuleController {
    private Logger logger = LoggerFactory.getLogger(SmartFilterRuleController.class);

    //智能过滤规则service
    @Autowired
    private ISmartFilterRuleService smartFilterRuleImpl;

    /**
     * 查询智能过滤规则列表(分页)
     *
     * @param
     * @return
     * @Comments: <对此方法的描述，可以引用系统设计中的描述>
     * <AUTHOR> [<EMAIL>]
     * @since 2022-01-07 9:56
     */
    @RequestMapping(value = "getSmartFilterRuleListByPage.do", method = RequestMethod.GET)
    @ApiOperation(value = "查询智能过滤规则列表(分页)")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "page", value = "当前页", required = true, paramType = "query", dataType = "int"),
            @ApiImplicitParam(name = "limit", value = "每页显示条数", required = true, paramType = "query", dataType = "int"),
            @ApiImplicitParam(name = "field", value = "排序字段", required = false, paramType = "query", dataType = "String"),
            @ApiImplicitParam(name = "sort", value = "排序", required = false, paramType = "query", dataType = "int"),
            @ApiImplicitParam(name = "host", value = "应用名称", required = false, paramType = "query", dataType = "String")
    })
    @Deprecated
    public ResponseVo<ListOutputDto<SmartFilterRuleDto>> getSmartFilterRuleListByPage(Integer page, Integer limit,String field,Integer sort,String host) {
        logger.warn("智能过滤规则Controller》查询智能过滤规则列表(分页)");

        ListOutputDto<SmartFilterRuleDto> data = null;
        try {
            data = smartFilterRuleImpl.getSmartFilterRuleList(page, limit,field, sort,host);
        } catch (Exception e) {
            logger.error("查询智能过滤规则列表(分页)", e);
            return ResponseVo.error(500, e.getMessage());
        }
        return ResponseVo.ok(data);
    }

    @RequestMapping(value = "list", method = RequestMethod.POST)
    @ApiOperation(value = "查询智能过滤规则列表(分页)")
    public ResponseVo<ListOutputDto<SmartFilterRuleDto>> list(@RequestBody SmartFilterCriteriaDTO smartFilterCriteriaDTO) {
        ListOutputDto<SmartFilterRuleDto> data = null;
        try {
            data = smartFilterRuleImpl.getSmartFilterRuleList(smartFilterCriteriaDTO);
        } catch (Exception e) {
            logger.error("查询智能过滤规则列表(分页)", e);
            return ResponseVo.error(500, e.getMessage());
        }
        return ResponseVo.ok(data);
    }
    /**
     * id查询智能过滤规则详情
     *
     * @param
     * @return
     * @Comments: <对此方法的描述，可以引用系统设计中的描述>
     * <AUTHOR> [<EMAIL>]
     * @since 2022-01-07 9:56
     */
    @RequestMapping(value = "getSmartFilterRuleById.do", method = RequestMethod.GET)
    @ApiOperation(value = "id查询智能过滤规则详情")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "智能过滤规则id", required = true, paramType = "query", dataType = "String")
    })
    public ResponseVo<SmartFilterRuleDto> getSmartFilterRuleById(String id) {
        logger.warn("智能过滤规则Controller》id查询智能过滤规则详情");

        try {
            if (DataUtil.isNotEmpty(id)) {

                SmartFilterRuleDto smartFilterRuleDto = smartFilterRuleImpl.getSmartFilterRuleById(id);
                return ResponseVo.ok(smartFilterRuleDto);
            } else {
                return ResponseVo.error(500, "id不得为空");
            }
        } catch (Exception e) {
            logger.error("id查询智能过滤规则详情", e);
            return ResponseVo.error(500, e.getMessage());
        }
    }

    @RequestMapping(value = "deleteSmartFilterRule.do", method = RequestMethod.POST)
    @ApiOperation(value = "id查询智能过滤规则详情")
    @Deprecated
    public ResponseVo<Boolean> deleteSmartFilterRule(String id) {
        smartFilterRuleImpl.deleteSmartFilterRule(id);
        return ResponseVo.ok();
    }


    @RequestMapping(value = "batchDeleteSmartFilterRule", method = RequestMethod.POST)
    @ApiOperation(value = "批量删除智能过滤规则")
    @Permission(type = ResourceEnum.ACTION, value = "WRITE")
    public ResponseVo<Boolean> batchDeleteSmartFilterRule(@RequestBody SmartFilterCriteriaDTO smartFilterCriteriaDTO) {
        smartFilterRuleImpl.batchDeleteSmartFilterRule(smartFilterCriteriaDTO);
        return ResponseVo.ok();
    }








    /**
     * 编辑智能过滤规则
     *
     * @param
     * @return
     * @Comments: <对此方法的描述，可以引用系统设计中的描述>
     * <AUTHOR> [<EMAIL>]
     * @since 2022-01-07 9:56
     */
    @RequestMapping(value = "editSmartFilterRule.do", method = RequestMethod.POST)
    @ApiOperation(value = "编辑智能过滤规则")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "params", value = "编辑内容", required = true, paramType = "body", dataType = "SmartFilterRuleDto", dataTypeClass = SmartFilterRuleDto.class)
    })
    @Permission(type = ResourceEnum.ACTION, value = "WRITE")
    public ResponseVo<SmartFilterRuleDto> startSmartFilterRule(@RequestBody() SmartFilterRuleDto params) {
        logger.warn("智能过滤规则Controller》启动智能过滤规则");

        try {
            if (DataUtil.isEmpty(params) || DataUtil.isEmpty(params.getId())) {

                ResponseVo.error(500, "id不得为空");
            }

            smartFilterRuleImpl.editSmartFilterRule(params);
        } catch (Exception e) {
            logger.error("风险详情-风险处理", e);
        }

        return ResponseVo.ok(params);
    }

    @RequestMapping(value = "getApiSmartFilterRules", method = RequestMethod.POST)
    @ApiOperation(value = "根据接口uri获取对应的智能过滤规则")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "uri", value = "接口uri", required = true, paramType = "query", dataType = "String")
    })
    public ResponseVo<List<SmartFilterRule>> getApiSmartFilterRules(String uri) {
        return ResponseVo.ok(smartFilterRuleImpl.getApiSmartFilterRules(uri));
    }

    @RequestMapping(value = "saveSmartFilterRules", method = RequestMethod.POST)
    @ApiOperation(value = "保存推荐出来的智能过滤规则")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "smartFilterRules", value = "智能过滤规则集合", required = true)
    })
    @Permission(type = ResourceEnum.ACTION, value = "WRITE")
    public ResponseVo<Boolean> saveSmartFilterRules(@RequestBody List<SmartFilterRule> smartFilterRules) {
        return ResponseVo.ok(smartFilterRuleImpl.saveSmartFilterRules(smartFilterRules));
    }


}
