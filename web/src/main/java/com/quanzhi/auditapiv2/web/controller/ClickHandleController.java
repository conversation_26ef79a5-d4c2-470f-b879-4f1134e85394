package com.quanzhi.auditapiv2.web.controller;

import com.quanzhi.auditapiv2.common.dal.entity.node.ClusterNode;
import com.quanzhi.auditapiv2.common.dal.entity.node.event.ClusterNodeAddEvent;
import com.quanzhi.auditapiv2.common.util.dto.ResponseVo;
import com.quanzhi.auditapiv2.core.service.manager.web.impl.ClickNodeHandleService;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

@Slf4j
@RequestMapping("/api/click")
@RestController
public class ClickHandleController {

    @Resource
    private ClickNodeHandleService clickNodeHandleService;

    @Resource
    private ApplicationEventPublisher eventPublisher;

    @ApiOperation(value = "CK相关处理")
    @PostMapping("/handle")
    public ResponseVo<String> handle() {
        try {
            clickNodeHandleService.handle();
            return ResponseVo.ok();
        } catch (Exception e) {
            log.error("clickhouse handle error", e);
            return ResponseVo.error(500, e.getMessage());
        }
    }

    @ApiOperation(value = "CK相关处理")
    @PostMapping("/test/add")
    public ResponseVo<String> add() {
        try {
            eventPublisher.publishEvent(new ClusterNodeAddEvent(new ClusterNode()));
            return ResponseVo.ok();
        } catch (Exception e) {
            log.error("clickhouse handle error", e);
            return ResponseVo.error(500, e.getMessage());
        }
    }
}
