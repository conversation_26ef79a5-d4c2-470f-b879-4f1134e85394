package com.quanzhi.auditapiv2.web.interceptor;

import com.quanzhi.auditapiv2.common.util.constant.SessionConstant;
import com.quanzhi.auditapiv2.common.util.utils.HttpResponseUtil;
import com.quanzhi.auditapiv2.web.common.utils.RequestUtil;
import org.springframework.web.servlet.handler.HandlerInterceptorAdapter;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;

/**
 * @Author: Linlm
 * @Description: 登录拦截处理类
 * @Date: Created in 2020/7/14 下午6:45
 */
public class SecurityInterceptor extends HandlerInterceptorAdapter {
    private Boolean isDev;

    public SecurityInterceptor(Boolean isDev){
        this.isDev = isDev;
    }

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {
        //一次请求，一次校验，否则可能会出错
        synchronized (this) {
            // 如果是开发环境，则不拦截
            if ((isDev != null) && isDev) {
                return true;
            }

            HttpSession session = request.getSession();

            // 判断是否已有该用户登录的session
            if (session.getAttribute(SessionConstant.LOGIN_USERNAME__SESSION_KEY) != null) {
                return true;
            }

            String msg = HttpResponseUtil.error(401, "login first!!!");

            RequestUtil.writeOut(response, msg);

            return false;
        }
    }
}