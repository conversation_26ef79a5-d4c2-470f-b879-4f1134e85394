package com.quanzhi.auditapiv2.web.controller;

import com.quanzhi.audit.mix.permission.annotation.Permission;
import com.quanzhi.audit.mix.permission.domain.enums.ResourceEnum;
import com.quanzhi.auditapiv2.common.dal.dto.llm.AiInfoResult;
import com.quanzhi.auditapiv2.common.dal.dto.llm.LLMSearchDto;
import com.quanzhi.auditapiv2.common.dal.entity.llm.LLMConfig;
import com.quanzhi.auditapiv2.common.util.dto.ResponseVo;
import com.quanzhi.auditapiv2.core.service.llm.LLMConfigService;
import com.quanzhi.auditapiv2.web.common.annotation.DirectiveSync;
import com.quanzhi.auditapiv2.web.common.annotation.OperateLog;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * create at 2025/4/21 14:19
 * @description:
 **/
@RestController
@RequestMapping("/api/llmConfig")
public class LllmConfigController {


    @Autowired
    private LLMConfigService llmConfigService;


    @OperateLog(value = "新增编辑大模型配置", type = "处理", module = "配置")
    @RequestMapping(value = "saveLlmConfig", method = RequestMethod.POST)
    @ApiOperation(value = "新增编辑大模型配置")
    @Permission(type = ResourceEnum.ACTION, value = "WRITE")
    public ResponseVo<Void> saveLlmConfig(@RequestBody LLMConfig llmConfig){
        return ResponseVo.ok(llmConfigService.saveLlmConfig(llmConfig));
    }

    @RequestMapping(value = "listLlmConfig", method = RequestMethod.POST)
    @ApiOperation(value = "大模型配置分页")
    public ResponseVo<?> listLlmConfig(@RequestBody LLMSearchDto llmSearchDto){
        return ResponseVo.ok(llmConfigService.listLlmConfig(llmSearchDto));
    }


    @RequestMapping(value = "testLlmConnect", method = RequestMethod.POST)
    @ApiOperation(value = "测试大模型连通性")
    public ResponseVo<?> testLlmConnect(@RequestBody LLMConfig llmConfig){
        return ResponseVo.ok(llmConfigService.testLlmConnect(llmConfig));
    }



    @RequestMapping(value = "receiveAiStudyResult", method = RequestMethod.POST)
    @ApiOperation(value = "接收AI学习结果")
    @DirectiveSync(replaceId = "id")
    public ResponseVo<?> receiveAiStudyResult(@RequestBody AiInfoResult aiInfoResult){
        llmConfigService.receiveAiStudyResult(aiInfoResult);
        return ResponseVo.ok();
    }







}