package com.quanzhi.auditapiv2.web.controller;

import com.quanzhi.audit.mix.permission.annotation.Permission;
import com.quanzhi.audit.mix.permission.domain.enums.ResourceEnum;
import com.quanzhi.audit_core.common.model.ResourceLevelStrategy;
import com.quanzhi.auditapiv2.common.util.utils.DataUtil;
import com.quanzhi.auditapiv2.common.util.utils.HttpResponseUtil;
import com.quanzhi.auditapiv2.core.service.manager.web.impl.ResourceLevelServiceImpl;
import com.quanzhi.auditapiv2.web.common.annotation.DirectiveSync;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.List;

/**
 * @ClassName ResourceLevelController
 * @Description TODO
 * <AUTHOR>
 * @Date 2021/2/2 11:43
 **/
@RestController
@RequestMapping("/resource/level")
@Slf4j
public class ResourceLevelController {

    @Autowired
    private ResourceLevelServiceImpl resourceLevelService;

    @ApiOperation("查询单个资产分级配置")
    @RequestMapping(value = "/getById",method = RequestMethod.GET)
    public String getById(@RequestParam(required = true) String id){
        try {
            return HttpResponseUtil.success(resourceLevelService.getById(id));
        }catch (Exception e){
            log.error("查询资产分级错误:{}",e);
            return HttpResponseUtil.error("查询资产分级错误");
        }
    }

    @ApiOperation("批量删除资产分级配置")
    @RequestMapping(value = "/delete",method = RequestMethod.GET)
    @Permission(type = ResourceEnum.ACTION, value = "WRITE")
    public String delete(@RequestParam List<String> ids){
        try {
            return HttpResponseUtil.success(resourceLevelService.delete(ids));
        } catch (Exception e){
            log.error("批量删除分级有误：{}",e);
            return HttpResponseUtil.error("批量删除分级有误");
        }
    }

    @ApiOperation("分页查询资产分级配置")
    @RequestMapping(value = "/list",method = RequestMethod.GET)
    public String list(@RequestParam(required = true,defaultValue = "1") Integer page,@RequestParam(required = true,defaultValue = "10") Integer limit){
        try {
            return HttpResponseUtil.success(resourceLevelService.list(page,limit));
        }catch (Exception e){
            log.error("分页查询资产分级错误：{}",e);
            return HttpResponseUtil.error("分页查询资产分级错误");
        }
    }

    @ApiOperation("保存资产分级配置")
    @RequestMapping(value = "/save",method = RequestMethod.POST)
    @DirectiveSync(key = "id", needNotify = true, retry = false, remark = "保存API敏感等级")
    @Permission(type = ResourceEnum.ACTION, value = "WRITE")
    public String save(HttpServletRequest httpServletRequest, @RequestBody ResourceLevelStrategy resourceLevelStrategy) {
        if (DataUtil.isNotEmpty(httpServletRequest.getSession().getAttribute("username"))) {
            resourceLevelStrategy.setOperateName(String.valueOf(httpServletRequest.getSession().getAttribute("username")));
        }
        //id为空则新增，否则更新
        if (DataUtil.isNotEmpty(resourceLevelStrategy.getId())) {
            resourceLevelStrategy.setCreateTime(System.currentTimeMillis());
        }
        resourceLevelStrategy.setUpdateTime(System.currentTimeMillis());
        resourceLevelService.save(resourceLevelStrategy);
        return HttpResponseUtil.success();
    }

    @ApiOperation("批量开启关闭资产分级配置")
    @RequestMapping(value = "/update",method = RequestMethod.GET)
    @Permission(type = ResourceEnum.ACTION, value = "WRITE")
    public String update(HttpServletRequest httpServletRequest,@RequestParam(required = true) List<String> ids,@RequestParam(required = true) boolean enableFlag){
        try {
            String username="";
            if(DataUtil.isNotEmpty(httpServletRequest.getSession().getAttribute("username"))){
                username=String.valueOf(httpServletRequest.getSession().getAttribute("username"));
            }
            return HttpResponseUtil.success(resourceLevelService.update(ids,username,enableFlag));
        }catch (Exception e){
            log.error("批量开启关闭分级错误：{}",e);
            return HttpResponseUtil.error("批量开启关闭分级错误");
        }
    }
}
