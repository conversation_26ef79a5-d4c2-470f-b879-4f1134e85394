package com.quanzhi.auditapiv2.web.controller;

import com.alibaba.fastjson.JSON;
import com.alibaba.nacos.api.config.annotation.NacosValue;
import com.quanzhi.auditapiv2.common.dal.dto.query.Predicate;
import com.quanzhi.auditapiv2.common.dal.dto.query.QueryNacos;
import com.quanzhi.auditapiv2.common.dal.entity.ReceiveEmailInfo;
import com.quanzhi.auditapiv2.common.dal.entity.SubscribePolicyRule;
import com.quanzhi.auditapiv2.common.util.dto.ListOutputDto;
import com.quanzhi.auditapiv2.common.util.dto.ResponseVo;
import com.quanzhi.auditapiv2.common.util.entity.EmailConfig;
import com.quanzhi.auditapiv2.common.util.utils.ConstantUtil;
import com.quanzhi.auditapiv2.common.util.utils.DataUtil;
import com.quanzhi.auditapiv2.common.util.utils.EmailUtil;
import com.quanzhi.auditapiv2.common.util.utils.StringUtils;
import com.quanzhi.auditapiv2.core.service.SysLogService;
import com.quanzhi.auditapiv2.core.service.manager.web.IEmailConfigService;
import com.quanzhi.auditapiv2.core.service.manager.web.IEmailReceiverService;
import com.quanzhi.auditapiv2.core.service.manager.web.IResourceChangedEventNacosService;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * @author: yangzixian
 * @date: 17/4/2023 18:06
 * @description:
 */
@RestController
@RequestMapping("/api/emailReceiver")
public class EmailReceiverController {

    private Logger logger = LoggerFactory.getLogger(EmailConfigController.class);

    @Autowired
    private IResourceChangedEventNacosService resourceChangedEventNacosService;

    @Autowired
    private IEmailConfigService emailConfigServiceImpl;

    @Autowired
    private IEmailReceiverService emailReceiverServiceImpl;

    @NacosValue(value = "${email.emailGroup.maxLenth:20}", autoRefreshed = true)
    private int maxLenth;

    @NacosValue(value = "${configLogModule:配置}", autoRefreshed = true)
    private String configLogModule;

    @Resource
    private SysLogService sysLogService;

    /**
     * 查询邮箱列表
     *
     * @param page
     * @param limit
     * @return
     */
    @RequestMapping(value = "listData.do", method = RequestMethod.POST)
    @ApiOperation(value = "查询邮箱列表")
    public ResponseVo<ListOutputDto<ReceiveEmailInfo>> listData(Integer page, Integer limit, String query) {
        ListOutputDto<ReceiveEmailInfo> data = new ListOutputDto<>();
        try {
            data = emailReceiverServiceImpl.getReceiveEmailList(page, limit, query);
        } catch (Exception e) {
            logger.error("查询邮箱列表", e);
            return ResponseVo.error(e.getMessage(), 233);
        }
        return ResponseVo.ok(data);
    }

    /**
     * 根据id查询邮箱详情
     *
     * @param id
     * @return
     */
    @RequestMapping(value = "getById.do", method = RequestMethod.POST)
    @ApiOperation(value = "id查询邮箱")
    public ResponseVo<ReceiveEmailInfo> getEmailById(String id) {
        try {
            if (DataUtil.isNotEmpty(id)) {
                ReceiveEmailInfo receiveEmailInfo = emailReceiverServiceImpl.getReceiveEmailById(id);
                return ResponseVo.ok(receiveEmailInfo);
            } else {
                return ResponseVo.error("id不得为空", 233);
            }
        } catch (Exception e) {
            logger.error("id查询邮箱", e);
            return ResponseVo.error(e.getMessage(), 233);
        }
    }

    /**
     * 保存邮箱
     *
     * @param params
     * @return
     */
    @RequestMapping(value = "save.do", method = RequestMethod.POST)
    @ApiOperation(value = "保存邮箱")
    public ResponseVo<String> save(@RequestParam(value = "params", defaultValue = "") String params) {
        try {
            if (DataUtil.isNotEmpty(params)) {
                ReceiveEmailInfo receiveEmailInfo = JSON.parseObject(params, ReceiveEmailInfo.class);
                if (receiveEmailInfo != null) {
                    if (DataUtil.isEmpty(receiveEmailInfo.getEmail())) {
                        return ResponseVo.error("邮箱不能为空", 233);
                    } else if (DataUtil.isEmpty(receiveEmailInfo.getEmailGroup())) {
                        return ResponseVo.error("邮箱分组不能为空", 233);
                    } else {
                        if (receiveEmailInfo.getEmailGroup().length() > maxLenth) {
                            return ResponseVo.error("邮箱分组长度不能超过" + maxLenth, 233);
                        }
                        if (!StringUtils.isEmail(receiveEmailInfo.getEmail())) {
                            return ResponseVo.error("邮箱格式不正确", 233);
                        }

                        List<ReceiveEmailInfo> receiveEmailInfos = emailReceiverServiceImpl.getByEmailGroup(receiveEmailInfo.getEmailGroup());
                        for (ReceiveEmailInfo rec : receiveEmailInfos) {
                            if (receiveEmailInfo.getEmail().equals(rec.getEmail())) {
                                return ResponseVo.error("不允许出现相同的分组+邮箱的组合", 233);
                            }
                        }
                        emailReceiverServiceImpl.saveReceiveEmail(receiveEmailInfo);
                        if (DataUtil.isNotEmpty(receiveEmailInfo.getId())) {
                            sysLogService.insertLog("修改\"" + receiveEmailInfo.getEmailGroup() + "-" + receiveEmailInfo.getEmail() + "\"订阅方式", configLogModule,"编辑");
                        } else {
                            sysLogService.insertLog("新增\"" + receiveEmailInfo.getEmailGroup() + "-" + receiveEmailInfo.getEmail() + "\"邮箱订阅方式", configLogModule,"新增");
                        }
                    }
                } else {
                    return ResponseVo.error(ConstantUtil.PARAMS_ERROR, 233);
                }
            } else {
                return ResponseVo.error("params不得为空", 233);
            }
        } catch (Exception e) {
            logger.error("保存邮箱", e);
            return ResponseVo.error(e.getMessage(), 233);
        }
        return ResponseVo.ok();
    }

    /**
     * 发送测试邮件
     *
     * @param receiveEmail
     * @return
     */
    @RequestMapping(value = "sendTest.do", method = RequestMethod.POST)
    @ApiOperation(value = "测试邮件发送")
    public ResponseVo<String> sendTest(String receiveEmail) {
        try {
            if (DataUtil.isNotEmpty(receiveEmail)) {
                //获取邮箱配置
                List<EmailConfig> emailConfigList = emailConfigServiceImpl.getEmailConfigList();
                if (DataUtil.isNotEmpty(emailConfigList)) {
                    EmailConfig emailConfig = emailConfigList.get(0);
                    //发送邮件
                    EmailUtil.sendMail(emailConfig, receiveEmail, "新增邮箱测试", "该邮件是API风险监测通知测试邮件");
                }
            } else {
                return ResponseVo.error("参数不得为空", 233);
            }
        } catch (Exception e) {
            logger.error("测试邮件发送失败", e);
            return ResponseVo.error(e.getMessage(), 233);
        }
        return ResponseVo.ok();
    }

    /**
     * 根据id删除邮箱信息
     *
     * @param id
     * @return
     */
    @RequestMapping(value = "deleteById.do", method = RequestMethod.POST)
    @ApiOperation(value = "删除邮箱")
    public ResponseVo<String> deleteReceiveEmail(String id) {
        try {
            if (DataUtil.isNotEmpty(id)) {
                ReceiveEmailInfo receiveEmailInfo = emailReceiverServiceImpl.getReceiveEmailById(id);
                sysLogService.insertLog("删除\"" + receiveEmailInfo.getEmailGroup() + "-" + receiveEmailInfo.getEmail() + "\"订阅方式", configLogModule,"删除");
                emailReceiverServiceImpl.deleteById(id);

            } else {
                return ResponseVo.error("id不能为空", 233);
            }
        } catch (Exception e) {
            logger.error("删除邮箱错误", e);
            return ResponseVo.error(e.getMessage(), 233);
        }
        return ResponseVo.ok();
    }

    @RequestMapping(value = "checkIsUsed", method = RequestMethod.POST)
    @ApiOperation("能否直接删除校验")
    public ResponseVo<Boolean> checkIsUsed(String emailGroup) {
        if (DataUtil.isEmpty(emailGroup)) {
            return ResponseVo.error("接口校验参数为空", 233);
        }
        List<ReceiveEmailInfo> receiveEmailInfos = emailReceiverServiceImpl.getByEmailGroup(emailGroup);
        if (receiveEmailInfos.size() == 0) {
            return ResponseVo.error("不存在该邮箱分组", 233);
        }

        QueryNacos query = new QueryNacos();
        query.where("emailGroup", Predicate.IS, emailGroup);
        SubscribePolicyRule subscribePolicyRule = resourceChangedEventNacosService.findOne(query);
        if (receiveEmailInfos.size() == 1 && DataUtil.isNotEmpty(subscribePolicyRule)) {
            return ResponseVo.ok(false);
        } else {
            return ResponseVo.ok(true);
        }
    }

    @RequestMapping(value = "getEmailGroupList", method = RequestMethod.POST)
    @ApiOperation("获取邮箱分组列表")
    public ResponseVo<List<String>> getEmailGroupList() {
        try {
            return ResponseVo.ok(emailReceiverServiceImpl.getEmailGroup());
        } catch (Exception e) {
            return ResponseVo.error("获取邮箱分组列表错误", 233);
        }
    }

}
