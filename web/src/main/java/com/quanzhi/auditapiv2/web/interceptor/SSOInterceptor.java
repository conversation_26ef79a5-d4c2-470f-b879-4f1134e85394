package com.quanzhi.auditapiv2.web.interceptor;

import com.alibaba.nacos.api.config.annotation.NacosValue;
import com.quanzhi.auditapiv2.common.dal.entity.ConstantInfo;
import com.quanzhi.auditapiv2.common.dal.enums.ProductTypeEnum;
import com.quanzhi.auditapiv2.common.util.utils.DataUtil;
import com.quanzhi.auditapiv2.common.util.utils.HttpResponseUtil;
import com.quanzhi.uias.base.sdk.util.entity.AuthInfo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.handler.HandlerInterceptorAdapter;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.quanzhi.auditapiv2.core.service.sso.SsoServiceImpl.SSO_YHSGJ_SESSION_KEY;

@Component
@Slf4j
public class SSOInterceptor extends HandlerInterceptorAdapter {

    @NacosValue(value = "${login.interceptor.enable:false}", autoRefreshed = true)
    private Boolean enableSSO;

    @NacosValue(value = "${yhsgj.login.interceptor.enable:false}", autoRefreshed = true)
    private Boolean yhsgjEnableSSO;

    @NacosValue(value = "${product.type:api}", autoRefreshed = true)
    private String productType;

    private static final String AUTH_TOKEN = "X-API-Key";

    private static final String TOKEN_VALUE = "OPEN_API";

    private static final String SSO_SESSION_ID = "SSO_SESSION_ID";

    // sso系统账号信息
    public static final String SSO_AUTHINFO_SESSION_KEY = "authInfo";

    private Map<String, Long> ssoSessionIdTimeMap = new HashMap<>();

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler)throws IOException {
            if (!enableSSO && !yhsgjEnableSSO) {
            return true;
        }

        // open-api 调用，不要拦了，再拦翻脸
        if ( request.getHeader(AUTH_TOKEN) != null
                && request.getHeader(AUTH_TOKEN).equals(TOKEN_VALUE) ) {
            return true;
        }

        boolean ignoreReq = this.ignoreReq(request);
        if (ignoreReq){
            return true;
        }

        response.setCharacterEncoding("utf-8");    //设置 HttpServletResponse使用utf-8编码
        response.setHeader("Content-Type", "text/html;charset=utf-8");

        //一次请求，一次校验，否则可能会出错
        synchronized (this) {
            if (productType.equals(ProductTypeEnum.ltsk.name()) && enableSSO) {
                String header = request.getHeader(ConstantInfo.Authorization);
                if (!header.startsWith(ConstantInfo.Bearer)) {
                    log.info("接口：{} 需要登录", request.getRequestURI());
                    response.getWriter().write(HttpResponseUtil.error(402,"请到联通数科统一认证系统进行登录跳转！"));
                }
            }else if (productType.equals(ProductTypeEnum.api.name()) && enableSSO){
                AuthInfo authInfo = (AuthInfo)request.getSession().getAttribute(SSO_AUTHINFO_SESSION_KEY);
                if (DataUtil.isEmpty(authInfo)){
                    log.info("接口：{} 需要登录", request.getRequestURI());
                    response.getWriter().write(HttpResponseUtil.error(402,"请到统一认证系统进行登录跳转！"));
                    return false;
                }
            }else if (yhsgjEnableSSO){
                AuthInfo authInfo = (AuthInfo)request.getSession().getAttribute(SSO_YHSGJ_SESSION_KEY);
                if (DataUtil.isEmpty(authInfo)){
                    log.info("接口：{} 需要登录", request.getRequestURI());
                    response.getWriter().write(HttpResponseUtil.error(402,"请到虎盾系统进行登录跳转！"));
                    return false;
                }
            }

            return true;

        }
    }

    /**
     * <AUTHOR>
     * @date 2022/9/29 10:26 AM
     * @Description 判断请求是否忽略拦截
     * @param request
     * @return boolean
     * @Since
     */
    private boolean ignoreReq(HttpServletRequest request){
        String contextPath = "/".equals(request.getContextPath()) ? "" : request.getContextPath();
        String uri = request.getRequestURI();

        List<String> ignorePaths = this.ignorePaths();
        for (String ignorePath : ignorePaths){
            if (uri.startsWith(contextPath + ignorePath)){
                return true;
            }
        }

        return false;
    }

    /**
     * <AUTHOR>
     * @date 2022/9/29 10:09 AM
     * @Description 不需要拦截的请求
     * @param
     * @return java.util.List<java.lang.String>
     * @Since
     */
    private List<String> ignorePaths(){
        List<String> ignorePaths = new ArrayList<>();

        //swagger资源 正式服需删除
        ignorePaths.add("/api/basicConfig/listData.do");
        ignorePaths.add("/api/login/vcode.do");
        ignorePaths.add("/api/login/isLogin.do");
        ignorePaths.add("/api/login/checkLogin.do");
        ignorePaths.add("/api/sysVersion/getSysVersion.do");
        ignorePaths.add("/api/notify/sendSystemNotify.do");
        ignorePaths.add("/api/notify/sendServiceNotify.do");
        ignorePaths.add("/api/gatewayConfig/listData.do");
        ignorePaths.add("/api/pluginManage/downReport.do");
        ignorePaths.add("/login/oauth/authorize");
        ignorePaths.add("/login/oauth/access_token");
        ignorePaths.add("/user");
        ignorePaths.add("/openApi/");
        ignorePaths.add("/api/**");
        ignorePaths.add("/riskPolicy/**");

        // 对外开放接口，不拦截
        ignorePaths.add("/agent/save.do");
        ignorePaths.add("/api/nacosSingelKey/getNacosSingleKeyValues.do");
        ignorePaths.add("/api/license/licenseDetail.do");
        ignorePaths.add("/api/login/auth/get");
        ignorePaths.add("/api/loginAuth/getLoginAuth.do");

        ignorePaths.add("/api/k8sLivenessProbe/available.do");

        // 单独签名校验拦截
        ignorePaths.add("/subsystem/init");
        ignorePaths.add("/subsystem/auth");
        ignorePaths.add("/subsystem/accredit");
        ignorePaths.add("/sso/roleList");
        ignorePaths.add("/sso/login");
        ignorePaths.add("/sso/logout");
        ignorePaths.add("/sso/enable");
        ignorePaths.add("/login/enableOtherLogin");
        ignorePaths.add("/login/dingGetUrl");
        ignorePaths.add("/audit-apiv2/login/dingAuth");

        return ignorePaths;
    }
}
