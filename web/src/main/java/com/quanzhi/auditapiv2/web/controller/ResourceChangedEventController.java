package com.quanzhi.auditapiv2.web.controller;

import com.alibaba.fastjson.JSON;
import com.quanzhi.auditapiv2.common.dal.dto.ResourceChangedEventSearchDto;
import com.quanzhi.auditapiv2.common.dal.entity.ResourceChangeEventModel;
import com.quanzhi.auditapiv2.common.util.dto.ListOutputDto;
import com.quanzhi.auditapiv2.common.util.utils.DataUtil;
import com.quanzhi.auditapiv2.common.util.utils.HttpResponseUtil;
import com.quanzhi.auditapiv2.core.service.manager.dto.ResourceChangeEventViewDto;
import com.quanzhi.auditapiv2.core.service.manager.web.IResourceChangedEventService;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiResponse;
import io.swagger.annotations.ApiResponses;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @date 2020/8/18 下午4:13
 */
@RestController
@RequestMapping("/api/resourceChangedEvent")
public class ResourceChangedEventController {

    private Logger logger = LoggerFactory.getLogger(ResourceChangedEventController.class);

    @Autowired
    private IResourceChangedEventService resourceChangedEventService;

    /**
     * 分页获取订阅内容列表
     * @param page
     * @param limit
     * @return
     */
    @RequestMapping(value = "/page.do", method = RequestMethod.GET)
    @ApiImplicitParams({
            @ApiImplicitParam(name = "page", value = "page",
                    required = true, paramType = "query", dataType = "Integer"),
            @ApiImplicitParam(name = "limit", value = "limit",
                    required = true, paramType = "query", dataType = "Integer"),
    })
    @ApiResponses({
            @ApiResponse(code = 200,
                    message = "{\"success\":true,\"errorCode\":0,\"data\":{\"rows\":[{\"id\":\"xxx\",\"name\":\"abc\",\"desc\":\"123\",\"enabled\":true,\"operateName\":\"mingming\",\"createTime\":\"1589426873000\",\"Type\":1,\"classificationType\":\"APP\"}],\"totalCount\":1}}",
                    response = ResourceChangeEventModel.class
            ),
    })
    public String page(
            @RequestParam(value = "page", defaultValue = "1") Integer page,
            @RequestParam(value = "limit", defaultValue = "10") Integer limit,
            @RequestParam(value = "resourceChangedEventSearchStr",required = false,defaultValue = "") String resourceChangedEventSearchStr
    ) {

        ResourceChangedEventSearchDto resourceChangedEventSearchDto = new ResourceChangedEventSearchDto();
        try {
            if(DataUtil.isNotEmpty( resourceChangedEventSearchStr )) {
                resourceChangedEventSearchDto = JSON.parseObject(resourceChangedEventSearchStr,ResourceChangedEventSearchDto.class);
            }

            ListOutputDto<ResourceChangeEventViewDto> data = resourceChangedEventService.page(page, limit,resourceChangedEventSearchDto);
            return HttpResponseUtil.success(data);

        } catch (Exception e) {

            logger.error("查询订阅内容列表(分页)", e);
            return HttpResponseUtil.error(e.getMessage());
        }
    }
}
