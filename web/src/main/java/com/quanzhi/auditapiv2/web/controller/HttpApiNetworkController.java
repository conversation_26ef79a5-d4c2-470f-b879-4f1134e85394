package com.quanzhi.auditapiv2.web.controller;

import com.quanzhi.auditapiv2.common.dal.dto.HttpApiSearchDto;
import com.quanzhi.auditapiv2.common.util.dto.ResponseVo;
import com.quanzhi.auditapiv2.core.service.manager.web.ISyncApiGatewayService;
import com.quanzhi.auditapiv2.core.service.manager.web.impl.HttpApiService;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/api/httpApi")
public class HttpApiNetworkController {
    @Autowired
    private HttpApiService httpApiService;

    @Autowired
    private ISyncApiGatewayService iSyncApiGatewayService;

    @RequestMapping(value = "/syncApiNetwork", method = RequestMethod.POST)
    @ApiOperation(value = "同步API网关", tags = "API3.0")
    public ResponseVo<?> syncApiNetwork(@RequestBody HttpApiSearchDto httpApiSearchDto) {
        boolean result;
        try {
            result = iSyncApiGatewayService.syncApiNetwork(httpApiSearchDto);
        } catch (Exception e) {
            return ResponseVo.error(500,e.getMessage());
        }
        return ResponseVo.ok(result);
    }
}
