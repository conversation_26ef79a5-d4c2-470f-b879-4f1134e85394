package com.quanzhi.auditapiv2.web.controller;

import com.quanzhi.auditapiv2.common.dal.dto.assetDefinition.ApiReqParamDto;
import com.quanzhi.auditapiv2.common.dal.dto.assetDefinition.CompositeRuleMatchDto;
import com.quanzhi.auditapiv2.common.util.dto.ListOutputDto;
import com.quanzhi.auditapiv2.common.util.dto.ResponseVo;
import com.quanzhi.auditapiv2.core.service.manager.web.impl.resource.ResourceOperateNewService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;


@Api(value = "资源定义包括接口和应用的合并拆分")
@RestController
@RequestMapping("/api/resourceOperate")
@Slf4j
public class ResourceOperateController {


    private final ResourceOperateNewService resourceOperateNewService;

    public ResourceOperateController(ResourceOperateNewService resourceOperateNewService) {
        this.resourceOperateNewService = resourceOperateNewService;
    }

    @ApiOperation("获取(应用|接口)待合并的匹配列表")
    @PostMapping(value = "/getMergedResourceList")
    @ApiImplicitParam(name = "compositeRuleMatchDto", value = "测试匹配传参", dataType = "CompositeRuleMatchDto", paramType = "body")
    public ResponseVo<ListOutputDto> getMergedResourceList(@RequestBody CompositeRuleMatchDto compositeRuleMatchDto) {
        try {
            ListOutputDto<?> mergedResourceList = resourceOperateNewService.getMergedResourceList(compositeRuleMatchDto);
            return ResponseVo.ok(mergedResourceList);
        } catch (Exception e) {
            log.error("获取(应用|接口)待合并的匹配列表异常:{}", e);
            return ResponseVo.ok(new ListOutputDto<>());
        }
    }

    @ApiOperation("获取(应用|接口)待拆分的匹配列表")
    @PostMapping(value = "/getDivideResourceList")
    @ApiImplicitParam(name = "compositeRuleMatchDto", value = "测试匹配传参", dataType = "CompositeRuleMatchDto", paramType = "body")
    public ResponseVo<ListOutputDto> getDivideResourceList(@RequestBody CompositeRuleMatchDto compositeRuleMatchDto) {
        return ResponseVo.ok(resourceOperateNewService.getDivideResourceList(compositeRuleMatchDto));
    }

    @ApiOperation("获取接口路径分割的节点")
    @PostMapping("/getApiPathSplitNodes")
    public ResponseVo<List<String>> getApiPathSplitNodes(String apiUrl) {
        if (apiUrl.split("://").length == 1) {
            return ResponseVo.error("接口格式不正确", 500);
        }
        return ResponseVo.ok(resourceOperateNewService.getApiPathSplitNodes(apiUrl));
    }

    @ApiOperation("获取接口的去重请求参数key")
    @PostMapping("/getApiReqParamKeys")
    public ResponseVo<List<ApiReqParamDto>> getApiReqParamKeys(String apiUrl) {
        return ResponseVo.ok(resourceOperateNewService.getApiReqParamKeys(apiUrl));
    }

    @ApiOperation("获取关键词拆分匹配列表")
    @PostMapping("/getKeywordSplitList")
    public ResponseVo<ListOutputDto> getKeywordSplitList(String host, String keyword) {
        return ResponseVo.ok(resourceOperateNewService.getKeywordSplitList(host, keyword));
    }


}
