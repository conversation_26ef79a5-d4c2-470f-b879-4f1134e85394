package com.quanzhi.auditapiv2.web.controller;

import com.alibaba.fastjson.JSON;
import com.quanzhi.audit.mix.permission.annotation.Permission;
import com.quanzhi.audit.mix.permission.domain.enums.ResourceEnum;
import com.quanzhi.audit_core.common.model.PluginPolicy;
import com.quanzhi.audit_core.common.model.ScopeMatchInfo;
import com.quanzhi.audit_core.common.model.WeaknessRule;
import com.quanzhi.auditapiv2.common.dal.dto.common.CascadeDto;
import com.quanzhi.auditapiv2.common.dal.dto.weakness.WeaknessRuleDTO;
import com.quanzhi.auditapiv2.common.util.dto.ResponseVo;
import com.quanzhi.auditapiv2.common.util.utils.ConstantUtil;
import com.quanzhi.auditapiv2.common.util.utils.DataUtil;
import com.quanzhi.auditapiv2.common.util.utils.HttpResponseUtil;
import com.quanzhi.auditapiv2.core.service.SysLogService;
import com.quanzhi.auditapiv2.core.service.manager.web.IApiWeaknessService;
import com.quanzhi.auditapiv2.core.service.manager.web.IWeaknessRuleService;
import com.quanzhi.auditapiv2.core.service.manager.web.impl.RuleConfigService;
import com.quanzhi.auditapiv2.web.common.annotation.DirectiveSync;
import com.quanzhi.re.core.domain.entity.po.RuleConfigPO;
import com.quanzhi.re.core.validator.ValidatorChain;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang.StringEscapeUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 《弱点规则Controller》
 *
 * <AUTHOR> [<EMAIL>]
 * @Project:
 * @Module ID:
 * @Comments:
 * @JDK version used:      <JDK1.8>
 * @since 2020-08-28-下午16:52:18
 */
@RestController
@RequestMapping("/api/weaknessRule")
public class WeaknessRuleController {
    private Logger logger = LoggerFactory.getLogger(WeaknessRuleController.class);

    //弱点规则service
    @Autowired
    private IWeaknessRuleService weaknessRuleServiceImpl;

    //接口弱点service
    @Autowired
    private IApiWeaknessService apiWeaknessServiceImpl;

    @Autowired
    private SysLogService sysLogService;

    @Resource
    private RuleConfigService ruleConfigService;

    /**
     * 查询弱点规则列表(分页)
     *
     * @param
     * @return
     * @Comments: <对此方法的描述，可以引用系统设计中的描述>
     * <AUTHOR> [<EMAIL>]
     * @since 2020-08-28 16:52
     */
    @RequestMapping(value = "listData.do", method = RequestMethod.POST)
    @ApiOperation(value = "查询弱点规则列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "type", value = "查询类型（1-弱点规则，2-弱点应用）", required = true, paramType = "query", dataType = "int"),
            @ApiImplicitParam(name = "params", value = "检索条件", required = false, paramType = "query", dataType = "String"),
            @ApiImplicitParam(name = "page", value = "当前页", required = false, paramType = "query", dataType = "int"),
            @ApiImplicitParam(name = "limit", value = "每页显示条数", required = false, paramType = "query", dataType = "int")
    })
    public String getApiWeaknessList(Integer type, String params, Integer page, Integer limit) {
        logger.warn("弱点规则Controller》查询弱点规则列表");

        try {
            if (type == 1) {
                return HttpResponseUtil.success(weaknessRuleServiceImpl.getWeaknessRuleList(params));
            } else if (type == 2) {
                return HttpResponseUtil.success(apiWeaknessServiceImpl.getHostList(params, page, limit));
            }
        } catch (Exception e) {
            logger.error("查询弱点规则列表", e);
            return HttpResponseUtil.error(e.getMessage());
        }
        return HttpResponseUtil.success();
    }

    @RequestMapping(value = "pageWeaknessRule", method = RequestMethod.POST)
    @ApiOperation(value = "分页展示弱点规则")
    public String pageWeaknessRule(String name, Integer page, Integer limit) {
        try {
            return HttpResponseUtil.success(weaknessRuleServiceImpl.pageWeaknessRule(name,page,limit));
        } catch (Exception e) {
            logger.error("查询弱点规则列表", e);
            return HttpResponseUtil.error(e.getMessage());
        }
    }


    /**
     * id查询弱点规则详情
     *
     * @param
     * @return
     * @Comments: <对此方法的描述，可以引用系统设计中的描述>
     * <AUTHOR> [<EMAIL>]
     * @since 2020-08-28 16:52
     */
    @RequestMapping(value = "getById.do", method = RequestMethod.POST)
    @ApiOperation(value = "id查询弱点规则详情")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "弱点规则id", required = true, paramType = "query", dataType = "String")
    })
    public String getApiWeaknessById(String id) {
        logger.warn("弱点规则Controller》id查询弱点规则详情");

        try {
            if (DataUtil.isNotEmpty(id)) {
                WeaknessRule weaknessRule = weaknessRuleServiceImpl.getWeaknessRuleById(id);
                if (DataUtil.isEmpty(weaknessRule.getScopeMatchInfo())) {
                    ScopeMatchInfo scopeMatchInfo = new ScopeMatchInfo();
                    scopeMatchInfo.setScopeMatchExpression("");
                    scopeMatchInfo.setMatchRules(new ArrayList<>());
                    weaknessRule.setScopeMatchInfo(scopeMatchInfo);
                }
                if(weaknessRule.getNewModeFlag() != null && weaknessRule.getNewModeFlag()){
                    RuleConfigPO configPO = ruleConfigService.findById(id);
                    if(configPO != null){
                        weaknessRule.setEnable(configPO.getEnabled());
                        weaknessRule.setMatchRule(configPO.getMatchRule());
                    }
                }else{
                    // 问了常畴，之前就这么定的
                    if(weaknessRule.getPluginPolicies() != null && !weaknessRule.getPluginPolicies().isEmpty()){
                        PluginPolicy pluginPolicy = weaknessRule.getPluginPolicies().get(0);
                        weaknessRule.setEnable(pluginPolicy.getEnable() != null ? pluginPolicy.getEnable() : false);
                    }
                }
                return HttpResponseUtil.success(weaknessRule);
            } else {
                return HttpResponseUtil.error("id不得为空");
            }
        } catch (Exception e) {
            logger.error("id查询弱点规则详情", e);
            return HttpResponseUtil.error(e.getMessage());
        }
    }

    /**
     * 新增弱点规则
     *
     * @param
     * @return
     * @Comments: <对此方法的描述，可以引用系统设计中的描述>
     * <AUTHOR> [<EMAIL>]
     * @since 2020-08-28 16:52
     */
    @DirectiveSync(needNotify = true, retry = false, remark = "新增弱点规则")
    @RequestMapping(value = "add.do", method = RequestMethod.POST)
    @ApiOperation(value = "新增弱点规则")
    @Permission(type = ResourceEnum.ACTION, value = "WRITE")
    public String add(@RequestBody WeaknessRuleDTO params) {
        logger.warn("弱点规则Controller》新增弱点规则");

        try {
            if (params == null) {
                return HttpResponseUtil.error("params不得为空");
            }

            if (StringUtils.isEmpty(params.getName())) {
                return HttpResponseUtil.error("请填写弱点名称");
            } else if (StringUtils.isEmpty(params.getType())) {
                return HttpResponseUtil.error("请选择弱点类别");
            }
            if (DataUtil.isNotEmpty(params.getScopeMatchInfo()) && DataUtil.isNotEmpty(params.getScopeMatchInfo().getScopeMatchExpression())) {
                //表达式特殊符号去除转义
                String expression = StringEscapeUtils.unescapeHtml(params.getScopeMatchInfo().getScopeMatchExpression());
                params.getScopeMatchInfo().setScopeMatchExpression(expression);
            }
            params.setMode(WeaknessRule.ModeTypeEnum.CUSTOM.val());
            weaknessRuleServiceImpl.addWeaknessRule(params);
        } catch (Exception e) {
            logger.error("add weakness rule error", e);
            return HttpResponseUtil.error(e.getMessage());
        }
        return HttpResponseUtil.success();
    }

    /**
     * 编辑弱点规则
     *
     * @param
     * @return
     * @Comments: <对此方法的描述，可以引用系统设计中的描述>
     * <AUTHOR> [<EMAIL>]
     * @since 2020-08-28 16:52
     */
    @RequestMapping(value = "edit.do", method = RequestMethod.POST)
    @ApiOperation(value = "编辑弱点规则")
    @DirectiveSync(key = "id", needNotify = true, retry = false, remark = "编辑弱点规则")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "params", value = "编辑内容", required = true, paramType = "query", dataType = "String")
    })
    @Permission(type = ResourceEnum.ACTION, value = "WRITE")
    public String edit(@RequestParam(value = "params", defaultValue = "") String params) {
        logger.warn("弱点规则Controller》编辑弱点规则");

        try {
            if (DataUtil.isNotEmpty(params)) {
                WeaknessRule weaknessRule = JSON.parseObject(params, WeaknessRule.class);
                if (DataUtil.isNotEmpty(weaknessRule.getScopeMatchInfo()) && DataUtil.isNotEmpty(weaknessRule.getScopeMatchInfo().getScopeMatchExpression())) {
                    //表达式特殊符号去除转义
                    String expression = StringEscapeUtils.unescapeHtml(weaknessRule.getScopeMatchInfo().getScopeMatchExpression());
                    weaknessRule.getScopeMatchInfo().setScopeMatchExpression(expression);
                }

                if (weaknessRule != null) {
                    sysLogService.insertLog("编辑弱点规则\""+weaknessRule.getId()+"\"","弱点规则","编辑");
                    if(weaknessRule.getScopeRule() != null){
                        ValidatorChain.validate(weaknessRule.getScopeRule());
                    }
                    weaknessRuleServiceImpl.editWeaknessRule(weaknessRule);
                } else {
                    return HttpResponseUtil.error(ConstantUtil.PARAMS_ERROR);
                }
            } else {
                return HttpResponseUtil.error("params不得为空");
            }
        } catch (Exception e) {
            logger.error("edit weakness rule error", e);
            return HttpResponseUtil.error(e.getMessage());
        }
        return HttpResponseUtil.success();
    }

    /**
     * 启停弱点规则
     *
     * @param
     * @return
     * @Comments: <对此方法的描述，可以引用系统设计中的描述>
     * <AUTHOR> [<EMAIL>]
     * @since 2020-08-28 16:52
     */
    @RequestMapping(value = "filter.do", method = RequestMethod.POST)
    @ApiOperation(value = "启停弱点规则")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "弱点规则id", required = true, paramType = "query", dataType = "String"),
            @ApiImplicitParam(name = "params", value = "启停插件", required = false, paramType = "query", dataType = "String")
    })
    @Permission(type = ResourceEnum.ACTION, value = "WRITE")
    public String filter(String id, String params) {
        logger.warn("弱点规则Controller》启停弱点规则");

        try {
            if (DataUtil.isNotEmpty(id)) {
                Map<String, Boolean> map = new HashMap<String, Boolean>();
                if (DataUtil.isNotEmpty(params)) {
                    map = (Map<String, Boolean>) JSON.parse(params);
                }
                weaknessRuleServiceImpl.enable(id, map);
            } else {
                return HttpResponseUtil.error("id不得为空");
            }
        } catch (Exception e) {
            logger.error("启停弱点规则", e);
            return HttpResponseUtil.error(e.getMessage());
        }
        return HttpResponseUtil.success();
    }

    /**
     * 黑名单
     *
     * @param
     * @return
     * @Comments: <对此方法的描述，可以引用系统设计中的描述>
     * <AUTHOR> [<EMAIL>]
     * @since 2020-08-28 16:52
     */
    @RequestMapping(value = "black.do", method = RequestMethod.POST)
    @ApiOperation(value = "黑名单")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "params", value = "编辑内容", required = true, paramType = "query", dataType = "String")
    })
    @Permission(type = ResourceEnum.ACTION, value = "WRITE")
    public String black(@RequestParam(value = "params", defaultValue = "") String params) {
        logger.warn("弱点规则Controller》黑名单");

        try {
            if (DataUtil.isNotEmpty(params)) {
                List<WeaknessRule> list = JSON.parseArray(params, WeaknessRule.class);
                if (list != null) {
                    weaknessRuleServiceImpl.black(list);
                } else {
                    return HttpResponseUtil.error(ConstantUtil.PARAMS_ERROR);
                }
            } else {
                return HttpResponseUtil.error("params不得为空");
            }
        } catch (Exception e) {
            logger.error("编辑弱点规则", e);
            return HttpResponseUtil.error(e.getMessage());
        }
        return HttpResponseUtil.success();
    }

    @RequestMapping(value = "cascades", method = {RequestMethod.GET, RequestMethod.POST})
    @ApiOperation(value = "弱点规则级联列表", tags = "API2.0.2")
    public ResponseVo<List<CascadeDto>> getCascades() {
        return ResponseVo.ok(weaknessRuleServiceImpl.getCascades());
    }


    @PostMapping(value = "delWeaknessRule")
    @ApiOperation(value = "删除弱点规则")
    @Permission(type = ResourceEnum.ACTION, value = "WRITE")
    public ResponseVo<Boolean> delWeaknessRule(String id) {
        return ResponseVo.ok(weaknessRuleServiceImpl.softDelWeaknessRule(id));
    }

    @PostMapping(value = "importWeaknessIndexParams")
    @ApiOperation(value = "导入自定义的指标参数")
    @Permission(type = ResourceEnum.ACTION, value = "WRITE")
    public ResponseVo<String> importWeaknessIndexParams(MultipartFile multipartFile) {
        return ResponseVo.ok(weaknessRuleServiceImpl.importWeaknessIndexParams(multipartFile));
    }


}
