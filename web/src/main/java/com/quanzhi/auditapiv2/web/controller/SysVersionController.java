package com.quanzhi.auditapiv2.web.controller;

import com.quanzhi.auditapiv2.common.dal.entity.SysVersion;
import com.quanzhi.auditapiv2.common.util.dto.ResponseVo;
import com.quanzhi.auditapiv2.common.util.utils.HttpResponseUtil;
import com.quanzhi.auditapiv2.core.service.manager.web.ISysVersionService;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

/**
 *
 * 《系统版本号Controller》
 *
 *
 * @Project:
 * @Module ID:
 * @Comments:
 * @JDK version used:      <JDK1.8>
 * <AUTHOR> [<EMAIL>]
 * @since 2020-04-08-上午9:56:10
 */
@RestController
@RequestMapping("/api/sysVersion")
public class SysVersionController {
    private Logger logger = LoggerFactory.getLogger(SysVersionController.class);

    //系统版本号service
    @Autowired
    private ISysVersionService sysVersionServiceImpl;

    /**
     * 查询系统版本号
     * @Comments:  <对此方法的描述，可以引用系统设计中的描述>
     * <AUTHOR> [<EMAIL>]
     * @since 2020-04-08 9:56
     * @param
     * @return
     */
    @RequestMapping(value = "getSysVersion.do", method = RequestMethod.POST)
    @ApiOperation(value = "查询系统版本号")
    public String listData() {
        logger.warn("系统版本号Controller》查询系统版本号");

        SysVersion sysVersion = null;
        try {
            sysVersion = sysVersionServiceImpl.getSysVersion();
        } catch (Exception e) {
            logger.error("查询系统版本号", e);
            return HttpResponseUtil.error(e.getMessage());
        }
        return HttpResponseUtil.success(sysVersion);
    }

    @GetMapping(value = "isAudit")
    @ApiOperation(value = "是否审计升级版本")
    public ResponseVo<Boolean> isAudit() {
        try {
            return ResponseVo.ok(sysVersionServiceImpl.isAudit());
        } catch (Exception e) {

        }
        return ResponseVo.error(0,"");
    }
}
