package com.quanzhi.auditapiv2.web.controller;

import com.quanzhi.auditapiv2.common.dal.entity.CustomFields;
import com.quanzhi.auditapiv2.common.util.dto.ResponseVo;
import com.quanzhi.auditapiv2.common.util.utils.DataUtil;
import com.quanzhi.auditapiv2.core.service.manager.web.ICustomFieldsService;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * @author: yangzixian
 * @date: 28/3/2023 15:28
 * @description:
 */
@RestController
@RequestMapping("/api/customFields")
@Slf4j
public class CustomFieldsController {

    private final ICustomFieldsService customFieldsService;

    public CustomFieldsController(ICustomFieldsService customFieldsService) {
        this.customFieldsService = customFieldsService;
    }

    @RequestMapping(value = "/selectCustomFields", method = RequestMethod.POST)
    @ApiOperation(value = "查询自定义字段列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "type", value = "自定义类型", required = true, paramType = "query", dataType = "String")
    })
    public ResponseVo<List<CustomFields>> openWeaknessAutoCheck(String type) {
        if (DataUtil.isEmpty(type)) {
            return ResponseVo.error("参数为空", 233);
        }
        return ResponseVo.ok(customFieldsService.selectCustonFields(type));
    }

}
