package com.quanzhi.auditapiv2.web.controller;

import com.alibaba.nacos.api.config.annotation.NacosValue;
import com.quanzhi.auditapiv2.common.dal.dto.GatewayDto;
import com.quanzhi.auditapiv2.common.dal.entity.Notify;
import com.quanzhi.auditapiv2.common.dal.entity.NotifyConfig;
import com.quanzhi.auditapiv2.common.util.utils.DataUtil;
import com.quanzhi.auditapiv2.common.util.utils.HttpResponseUtil;
import com.quanzhi.auditapiv2.common.util.utils.gw.HttpClient;
import com.quanzhi.auditapiv2.core.service.manager.web.IGatewayConfigSerivce;
import com.quanzhi.auditapiv2.core.service.manager.web.INotifyService;
import com.quanzhi.auditapiv2.web.common.annotation.OperateLog;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;

/**
 *
 * 《网关授权Controller》
 *
 *
 * @Project:
 * @Module ID:
 * @Comments:
 * @JDK version used:      <JDK1.8>
 * <AUTHOR> [<EMAIL>]
 * @since 2020-04-08-上午9:56:10
 */
@RestController
@RequestMapping("/api/gwLicense")
public class GatewayLicenseController {
    private Logger logger = LoggerFactory.getLogger(GatewayLicenseController.class);

    //网关配置service
    @Autowired
    private IGatewayConfigSerivce gatewayConfiServiceImpl;

    //通知service
    @Autowired
    private INotifyService notifyServiceImpl;

    @Autowired
    private HttpServletRequest request;

    @NacosValue(value = "${ip.gwIp:}", autoRefreshed = true)
    private String gwIp;
    @NacosValue(value = "${gwPort:9876}", autoRefreshed = true)
    private String gwPort;
    @NacosValue(value = "${gwProjectName:/hw-admin}", autoRefreshed = true)
    private String gwProjectName;

    /**
     * 上传授权文件
     * @Comments:  <对此方法的描述，可以引用系统设计中的描述>
     * <AUTHOR> [<EMAIL>]
     * @since 2020-04-08 9:56
     * @param
     * @return
     */
    @OperateLog(value="上传网关授权文件", type = "上传", module = "系统授权")
    @RequestMapping(value = "up.do", method = RequestMethod.POST)
    @ApiOperation(value = "上传授权文件")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "multipartFile", value = "文件内容", required = true, paramType = "query", dataType = "multipartFile"),
            @ApiImplicitParam(name = "gatewayIp", value = "网关IP", required = true, paramType = "query", dataType = "String")
    })
    public String up(MultipartFile multipartFile, String gatewayIp) {
        logger.warn("网关授权Controller》上传授权文件");
        
        try {

            String url = "http://" + gatewayIp + ":" + gwPort + gwProjectName + "/license/uploadLicense.do";
            String respStr = HttpClient.uploadFile(url, multipartFile, "multipartFile");

            GatewayDto gatewayDto = gatewayConfiServiceImpl.getGatewayByIp(gatewayIp);
            if(DataUtil.isNotEmpty(gatewayDto) && DataUtil.isNotEmpty(gatewayDto.getLicenseConfig()) && DataUtil.isNotEmpty(gatewayDto.getLicenseConfig().getState())) {
                
                Notify notify = new Notify();
                notify.setCode(NotifyConfig.NotifyCodeEnum.GATEWAY_LICENSE.name());
                
                if(gatewayDto.getLicenseConfig().getState() == 1) {
                    notify.setTitle("网关授权完成");
                    notify.setContent("网关授权完成");
                }else {
                    notify.setTitle("网关授权失败");
                    notify.setContent("网关授权失败");

                    respStr = "网关授权失败";
                }
                notifyServiceImpl.sendServiceNotify(notify, (String) request.getSession().getAttribute("userId"));
            }

            if( respStr.equals("网关授权失败")) {
                return HttpResponseUtil.error("网关授权失败");
            }
            
            return respStr;
        } catch (Exception e) {
            logger.error("上传授权文件", e);
            return HttpResponseUtil.error(e.getMessage());
        }
    }

    /**
     * 查询机器码信息。用于生成授权文件
     * @Comments:  <对此方法的描述，可以引用系统设计中的描述>
     * <AUTHOR> [<EMAIL>]
     * @since 2020-04-08 9:56
     * @param
     * @return
     */
    @RequestMapping(value = "machineCode.do", method = RequestMethod.POST)
    @ApiOperation(value = "查询机器码信息")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "ip", value = "网关IP", required = true, paramType = "query", dataType = "String")
    })
    public String machineCode(String ip) {
        logger.warn("网关授权Controller》查询机器码信息");
        
        try {
            String url = "http://" + ip + ":" + gwPort + gwProjectName + "/license/machineCode.do";
            return HttpClient.post(url, null);
        } catch (Exception e) {
            logger.error("查询机器码信息", e);
            return HttpResponseUtil.error(e.getMessage());
        }
    }

    /**
     * 查询授权详细信息
     * @Comments:  <对此方法的描述，可以引用系统设计中的描述>
     * <AUTHOR> [<EMAIL>]
     * @since 2020-04-08 9:56
     * @param
     * @return
     */
    @RequestMapping(value = "license.do", method = RequestMethod.POST)
    @ApiOperation(value = "查询授权详细信息")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "ip", value = "网关ip", required = true, paramType = "query", dataType = "String")
    })
    public String license(String ip) {
        logger.warn("网关授权Controller》查询授权详细信息");
        
        try {
            String url = "http://" + ip + ":" + gwPort + gwProjectName + "/license/licenseDetail.do";
            return HttpClient.post(url, null);
        } catch (Exception e) {
            logger.error("查询授权详细信息", e);
            return HttpResponseUtil.error(e.getMessage());
        }
    }
}
