package com.quanzhi.auditapiv2.web.controller;

import com.quanzhi.auditapiv2.common.dal.entity.ToolConfig;
import com.quanzhi.auditapiv2.common.util.utils.DataUtil;
import com.quanzhi.auditapiv2.common.util.utils.FileUtils;
import com.quanzhi.auditapiv2.common.util.utils.HttpResponseUtil;
import com.quanzhi.auditapiv2.core.service.manager.web.IToolConfigService;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

@RestController
@RequestMapping("/api/maintenance")
public class MaintenanceController {

    @Autowired
    private IToolConfigService toolConfigService;

    @ApiOperation(value = "查询运维工具列表")
    @RequestMapping(value = "/getAllToolConfig.do", method = RequestMethod.GET)
    public String getAllToolConfig(HttpServletRequest request) {

        return HttpResponseUtil.success(toolConfigService.getAllToolConfig());
    }

    @ApiOperation(value = "判断运维工具是否已启动")
    @RequestMapping(value = "/isStarted.do", method = RequestMethod.POST)
    public String isStarted(String id) throws IOException {
        try {
            Object res = toolConfigService.executeCmdTool(id, 3);;

            return HttpResponseUtil.success(res);

        }catch (Exception e){
            return HttpResponseUtil.error(e.getMessage());
        }
    }

    @ApiOperation(value = "启动运维工具")
    @RequestMapping(value = "/start.do", method = RequestMethod.POST)
    public String start(String id) {
        try {
            Object res=toolConfigService.executeCmdTool(id, 1);

            return HttpResponseUtil.success(res);
        } catch (Exception e) {
            return HttpResponseUtil.error(e.getMessage());
        }
    }

    @ApiOperation(value = "停止运维工具")
    @RequestMapping(value = "/stop.do", method = RequestMethod.POST)
    public String stop(String id) {
        try {
            Object res=toolConfigService.executeCmdTool(id, 2);
            return HttpResponseUtil.success(res);
        } catch (Exception e) {
            return HttpResponseUtil.success(e.getMessage());
        }
    }

    @ApiOperation(value = "读取启动日志")
    @RequestMapping(value = "/getLogs.do", method = RequestMethod.POST)
    public String getLogs(String id) {
        try {
            Object res = toolConfigService.executeCmdTool(id, 4);

            return HttpResponseUtil.success(res);
        } catch (Exception e) {
            return HttpResponseUtil.error(e.getMessage());
        }
    }

    @ApiOperation(value = "判断是否已有执行结果")
    @RequestMapping(value = "/hasResult.do", method = RequestMethod.POST)
    public String hasResult(String id) {
        try {
            Object res = toolConfigService.executeCmdTool(id, 5);

            return HttpResponseUtil.success(res);
        } catch (Exception e) {
            return HttpResponseUtil.error(e.getMessage());
        }
    }

    @ApiOperation(value = "下载执行结果")
    @RequestMapping(value = "/downloadResult.do", method = RequestMethod.GET)
    public String downloadResult(HttpServletRequest request, HttpServletResponse response, String id) {
        String filePath = getToolConfigFilePath(id);
        if (DataUtil.isEmpty(filePath)){
            return HttpResponseUtil.error("文件路径不存在");
        }

        String filePathNew = filePath.substring(0, filePath.lastIndexOf("/") + 1);
        String fileName = filePath.substring(filePath.lastIndexOf("/") + 1);
        FileUtils.downFile(response,fileName, filePathNew);
        return HttpResponseUtil.success();
    }

    @ApiOperation(value = "获取运维工具")
    @RequestMapping(value = "/getToolConfigById.do", method = RequestMethod.GET)
    public String getToolConfigById(String id) {
        ToolConfig toolConfig = toolConfigService.getToolConfigById(id);
        if (DataUtil.isEmpty(toolConfig)){
            return HttpResponseUtil.error("运维工具不存在");
        }

        return HttpResponseUtil.success(toolConfig);
    }

    /**
     * 获取运维工具结果路径
     * @param id
     * @return
     */
    private String getToolConfigFilePath(String id){
        ToolConfig toolConfig = toolConfigService.getToolConfigById(id);
        if (DataUtil.isEmpty(toolConfig)
                || DataUtil.isEmpty(toolConfig.getSavePath())
                || DataUtil.isEmpty(toolConfig.getSaveName())
                || DataUtil.isEmpty(toolConfig.getResultType())
        ){
            return "";
        }

        String filePath = toolConfig.getSavePath() + "/" + toolConfig.getSaveName() + "." + toolConfig.getResultType();

        return filePath;
    }
}
