package com.quanzhi.auditapiv2.web.controller;

import com.quanzhi.auditapiv2.common.dal.dto.SysUpdateLogDto;
import com.quanzhi.auditapiv2.common.util.dto.ListOutputDto;
import com.quanzhi.auditapiv2.common.util.utils.HttpResponseUtil;
import com.quanzhi.auditapiv2.core.service.manager.web.ISysUpdateLogService;
import com.quanzhi.auditapiv2.web.common.annotation.OperateLog;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

/**
 *
 * 《系统升级日志Controller》
 *
 *
 * @Project:
 * @Module ID:
 * @Comments:
 * @JDK version used:      <JDK1.8>
 * <AUTHOR> [<EMAIL>]
 * @since 2020-04-08-上午9:56:10
 */
@RestController
@RequestMapping("/api/sysLogUpdate")
public class SysUpdateLogController {
    private static final Logger logger = LoggerFactory.getLogger(SysUpdateLogController.class);

    //系统升级日志service
    @Autowired
    private ISysUpdateLogService sysUpdateLogServiceImpl;

    /**
     * 查询系统升级日志列表(分页)
     * @Comments:  <对此方法的描述，可以引用系统设计中的描述>
     * <AUTHOR> [<EMAIL>]
     * @since 2020-04-08 9:56
     * @param
     * @return
     */
    @RequestMapping(value = "listData.do", method = RequestMethod.POST)
    @ApiOperation(value = "查询系统升级日志列表(分页)")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "page", value = "当前页", required = true, paramType = "query", dataType = "int"),
            @ApiImplicitParam(name = "limit", value = "每页显示条数", required = true, paramType = "query", dataType = "int")
    })
    public String listData(Integer page, Integer limit) {
        logger.warn("系统升级日志Controller》查询系统升级日志列表(分页)");

        ListOutputDto<SysUpdateLogDto> data = null;
        try {
            data = sysUpdateLogServiceImpl.getSysUpdateLogList(page, limit);
        } catch (Exception e) {
            logger.error("查询系统升级日志列表(分页)", e);
            return HttpResponseUtil.error(e.getMessage());
        }
        return HttpResponseUtil.success(data);
    }

    /**
     * 导出系统升级日志
     * @Comments:  <对此方法的描述，可以引用系统设计中的描述>
     * <AUTHOR> [<EMAIL>]
     * @since 2020-04-08 9:56
     * @param
     * @return
     */
    @OperateLog(value = "导出系统升级日志", type = "导出", module = "日志")
    @RequestMapping(value = "export.do", method = RequestMethod.GET)
    @ApiOperation(value = "导出系统升级日志")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "ids", value = "系统升级日志id，逗号分隔", required = true, paramType = "query", dataType = "String")
    })
    public String export(String ids) {
        logger.warn("系统升级日志Controller》导出系统升级日志");

        try{
            sysUpdateLogServiceImpl.exportSysUpdateLog(ids);
        }catch (Exception e) {
            logger.error("导出系统升级日志", e);
            return HttpResponseUtil.error(e.getMessage());
        }
        return HttpResponseUtil.success();
    }
}