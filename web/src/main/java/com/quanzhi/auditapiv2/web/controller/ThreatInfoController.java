package com.quanzhi.auditapiv2.web.controller;

import com.quanzhi.audit.mix.permission.annotation.DataPermission;
import com.quanzhi.auditapiv2.biz.risk.service.RiskPolicyService;
import com.quanzhi.auditapiv2.biz.risk.service.ThreatInfoService;
import com.quanzhi.auditapiv2.common.dal.dto.common.CommonGroupDto;
import com.quanzhi.auditapiv2.common.util.dto.ListOutputDto;
import com.quanzhi.auditapiv2.common.util.dto.ResponseVo;
import com.quanzhi.auditapiv2.core.risk.dto.search.ThreatInfoSearchDto;
import com.quanzhi.auditapiv2.core.risk.entity.ThreatInfo;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.stream.Collectors;

/**
 * @author: yangzixian
 * @date: 1/3/2023 17:51
 * @description:
 */
@RestController
@RequestMapping("/api/threatInfo")
@Slf4j
public class ThreatInfoController {

    private final ThreatInfoService threatInfoService;

    private final RiskPolicyService riskPolicyService;

    public ThreatInfoController(ThreatInfoService threatInfoService, RiskPolicyService riskPolicyService) {
        this.threatInfoService = threatInfoService;
        this.riskPolicyService = riskPolicyService;
    }

    @PostMapping(value = "/listThreatInfo")
    @ApiOperation(value = "威胁信息清单")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "params", value = "检索条件", required = true, paramType = "body", dataType = "ThreatInfoSearchDto", dataTypeClass = ThreatInfoSearchDto.class)
    })
    @DataPermission
    public ResponseVo<ListOutputDto<ThreatInfo>> listThreatInfo(@RequestBody ThreatInfoSearchDto params) {
        ListOutputDto<ThreatInfo> threatInfoList = null;
        try {
            if (params.getSort() == null) {
                params.setSort(2);
            }
            if (params.getSortField() == null) {
                params.setSortField("firstTime");
            }
            threatInfoList = threatInfoService.listThreatInfos(params);
        } catch (Exception exception) {
            log.error("获取威胁信息清单出错:", exception);
        }
        return ResponseVo.ok(threatInfoList);
    }

    @PostMapping(value = "/getThreatInfoGroup")
    @ApiOperation(value = "威胁信息分组计数")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "params", value = "检索条件", required = true, paramType = "body", dataType = "ThreatInfoSearchDto", dataTypeClass = ThreatInfoSearchDto.class)
    })
    @DataPermission
    public ResponseVo<List<CommonGroupDto>> getThreatInfoGroup(@RequestBody ThreatInfoSearchDto params) {
        List<CommonGroupDto> threatInfoGroup = threatInfoService.getThreatInfoGroup(params);
        return ResponseVo.ok(threatInfoGroup);
    }

    @GetMapping(value = "/getRiskNames")
    @ApiOperation(value = "风险事件枚举")
    public ResponseVo<List<String>> getRiskNames() throws Exception {
        List<String> riskNames = riskPolicyService.getRiskPolicyList().stream().map(riskPolicy -> {
            return riskPolicy.getName();
        }).collect(Collectors.toList());
        return ResponseVo.ok(riskNames);
    }

}
