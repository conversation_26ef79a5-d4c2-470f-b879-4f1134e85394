package com.quanzhi.auditapiv2.web.interceptor;

import com.alibaba.nacos.api.config.annotation.NacosValue;
import com.quanzhi.auditapiv2.common.dal.enums.ProductTypeEnum;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.handler.HandlerInterceptorAdapter;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.net.MalformedURLException;

@Component
public class RefererInterceptor extends HandlerInterceptorAdapter {
    @NacosValue(value = "${login.interceptor.enable:false}", autoRefreshed = true)
    private Boolean enableSSO;

    @NacosValue(value = "${product.type:api}", autoRefreshed = true)
    private String productType;

    private static final String AUTH_TOKEN = "X-API-Key";

    private static final String TOKEN_VALUE = "OPEN_API";

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {

        // 联通数科，所有请求经过他们的网关中转，前端的发送的referer校验一直失败
        if (productType.equals(ProductTypeEnum.ltsk.name()) && enableSSO) {
            return true;
        }

        if ( request.getHeader(AUTH_TOKEN) != null
                && request.getHeader(AUTH_TOKEN).equals(TOKEN_VALUE) ) {
            return true;
        }

        String referer = request.getHeader("referer");
        String host = request.getServerName();

        if (referer == null) {
            // 状态置为404
            response.setStatus(HttpServletResponse.SC_NOT_FOUND);
            return false;
        }

        java.net.URL url = null;

        try {
            url = new java.net.URL(referer);
        } catch (MalformedURLException e) {
            // URL解析异常，也置为404
            response.setStatus(HttpServletResponse.SC_NOT_FOUND);
            return false;
        }

        // 判断请求域名和referer域名是否相同
        if (!host.equals(url.getHost())) {
            // 状态置为403
            response.setStatus(HttpServletResponse.SC_FORBIDDEN);
            return false;
        }

        return true;
    }
}

