package com.quanzhi.auditapiv2.web.controller;

import com.quanzhi.audit_core.common.model.HistoryDataCleanTask;
import com.quanzhi.auditapiv2.common.util.dto.ListOutputDto;
import com.quanzhi.auditapiv2.common.util.utils.HttpResponseUtil;
import com.quanzhi.auditapiv2.core.service.manager.web.IHistoryDataCleanService;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @date 2020/7/20 下午5:12
 */
@RestController
@RequestMapping("/api/historyDataClean")
public class HistoryDataCleanController {

    @Autowired
    private IHistoryDataCleanService historyDataCleanService;

    @ApiOperation(value = "获取历史数据预清理列表")
    @RequestMapping(value = "/getHistoryDataCleanTaskList.do",method = RequestMethod.POST)
    @ApiImplicitParams({
            @ApiImplicitParam(name = "page",
                    value = "page",
                    required = true, paramType = "query", dataType = "int", example = "1"),
            @ApiImplicitParam(name = "limit",
                    value = "limit",
                    required = true, paramType = "query", dataType = "int", example = "10"),
            @ApiImplicitParam(name = "id",
                    value = "id",
                    required = false, paramType = "query", dataType = "String", example = "10")
    })
    public String getHistoryDataCleanTaskList (
            @RequestParam Integer page,
            @RequestParam Integer limit,
            @RequestParam(required = false) String id
    ) {
        ListOutputDto<HistoryDataCleanTask> historyDataCleanTaskListOutputDto = historyDataCleanService.getHistoryDataCleanTaskDto(page,limit,id);
        return HttpResponseUtil.success(historyDataCleanTaskListOutputDto);
    }

    @ApiOperation(value = "删除单个历史数据预清理任务")
    @RequestMapping(value = "/deleteTask.do",method = RequestMethod.POST)
    @ApiImplicitParams({
            @ApiImplicitParam(name = "taskId",
                    value = "taskId",
                    required = true, paramType = "query", dataType = "String", example = "")
    })
    public String deleteTask (
            @RequestParam String taskId
    ) {
        historyDataCleanService.deleteTask(taskId);
        return HttpResponseUtil.success();
    }


}
