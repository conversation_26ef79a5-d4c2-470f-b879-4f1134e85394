package com.quanzhi.auditapiv2.web.interceptor;

import com.alibaba.nacos.api.config.annotation.NacosValue;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.HandlerInterceptor;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
 * <AUTHOR>
 * @class PageRequestInterceptor
 * @created 2025/5/23 15:20
 * @desc
 **/
@Component
public class PageRequestInterceptor implements HandlerInterceptor {

    @NacosValue(value = "${page.request.interceptor.enable:false}", autoRefreshed = true)
    private Boolean enable;

    @NacosValue(value = "${page.request.interceptor.limit:1000}", autoRefreshed = true)
    private Integer limit;
    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {
        return HandlerInterceptor.super.preHandle(request, response, handler);
    }
}
