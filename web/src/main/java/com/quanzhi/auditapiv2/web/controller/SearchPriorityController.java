package com.quanzhi.auditapiv2.web.controller;

import com.alibaba.fastjson.JSON;
import com.alibaba.nacos.api.annotation.NacosInjected;
import com.alibaba.nacos.api.config.ConfigService;
import com.alibaba.nacos.api.config.annotation.NacosValue;
import com.alibaba.nacos.api.exception.NacosException;
import com.quanzhi.audit.mix.permission.annotation.Permission;
import com.quanzhi.audit.mix.permission.domain.enums.ResourceEnum;
import com.quanzhi.auditapiv2.common.util.utils.HttpResponseUtil;
import com.quanzhi.auditapiv2.core.service.SysLogService;
import com.quanzhi.auditapiv2.web.common.annotation.OperateLog;
import com.quanzhi.auditapiv2.web.dto.web.PriorityOfSearch;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;


@Slf4j
@RestController
@RequestMapping("/searchPriority")
public class SearchPriorityController {

    @NacosInjected
    private ConfigService configService;

    @NacosValue(value = "${dataLogModule:数据模块}", autoRefreshed = true)
    private String dataLogModule;

    @Resource
    private SysLogService sysLogService;

    @OperateLog(module = "组织架构配置", type = "编辑", value = "设置主名称")
    @ApiOperation("设置主名称")
    @RequestMapping(value = "/setPriority", method = RequestMethod.POST)
    @Permission(type = ResourceEnum.ACTION, value = "WRITE")
    public String uploadUserPriority(@RequestBody PriorityOfSearch priorityOfSearch) {
        try {
            String dataId = "common.searchPriority.json";
            String group = "common";
            configService.publishConfig(dataId, group, JSON.toJSONString(priorityOfSearch));
        } catch (NacosException e) {
            log.error("set priority error", e);
        }
        return HttpResponseUtil.success();
    }

    @ApiOperation("读取主名称")
    @RequestMapping(value = "/getPriority", method = RequestMethod.GET)
    public String getUserPriority() {

        String result = "";
        try {

            String dataId = "common.searchPriority.json";
            String group = "common";
            result = configService.getConfig(dataId, group, 3000);

        } catch (NacosException e) {
            log.error("get priority error", e);
        }
        return HttpResponseUtil.success(JSON.parse(result));
    }
}
