package com.quanzhi.auditapiv2.web.controller;

import com.quanzhi.audit_core.common.model.RecommendPolicy;
import com.quanzhi.auditapiv2.common.util.utils.DataUtil;
import com.quanzhi.auditapiv2.common.util.utils.HttpResponseUtil;
import com.quanzhi.auditapiv2.core.service.manager.web.IRecognizeConfigerService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * @author: zhousong
 * @data: 2018/7/5
 */
@RestController
@RequestMapping("/api/recognizeConfiger")
@Slf4j
public class RecognizeConfigerController {

    @Autowired
    private IRecognizeConfigerService recognizeConfigerService;

    /**
     * 保存策略
     * @param recommendPolicy
     * @return
     */
    @RequestMapping(value = "/save.do", method = RequestMethod.POST)
    public String save(
            HttpServletRequest request,
            @RequestBody RecommendPolicy recommendPolicy
    ) {
        String username = "";
        if(DataUtil.isNotEmpty(request.getSession().getAttribute("username"))) {
            username = request.getSession().getAttribute("username").toString();
        }
        recommendPolicy.setOperateName(username);

        if(DataUtil.isEmpty( recommendPolicy.getId() )) {
            recommendPolicy.setCreateTime(System.currentTimeMillis());
        }
        recommendPolicy.setUpdateTime(System.currentTimeMillis());
        recognizeConfigerService.save(recommendPolicy);
        return HttpResponseUtil.success();
    }

    /**
     * 分页获取推荐策略接口
     * @param page
     * @param limit
     * @return
     */
    @RequestMapping(value = "/page.do", method = RequestMethod.GET)
    public String page(
            @RequestParam(value = "keyword",defaultValue = "",required = false) String keyword,
            @RequestParam(value = "page",defaultValue = "") Integer page,
            @RequestParam(value = "limit",defaultValue = "") Integer limit
            ) {
        return HttpResponseUtil.success(recognizeConfigerService.page(keyword,page,limit));
    }

    /**
     * 根据ID，获取推荐策略配置详情
     * @param id
     * @return
     */
    @RequestMapping(value = "/getById.do", method = RequestMethod.GET)
    public String getById(@RequestParam(value = "id",defaultValue = "") String id) {
        return HttpResponseUtil.success(recognizeConfigerService.getById(id));
    }

    /**
     * 根据ID，删除风险策略
     * @param ids
     * @return
     */
    @RequestMapping(value = "/delete.do", method = RequestMethod.POST)

    public String delete(@RequestParam(value = "ids",defaultValue = "") List<String> ids) {
        recognizeConfigerService.delete(ids);
        return HttpResponseUtil.success();
    }

    /**
     * 批量开启
     * @param ids
     * @params request
     * @params response
     * @return
     */
    @RequestMapping(value = "/start.do",method = RequestMethod.POST)
    public String start (
            @RequestParam(value = "ids",defaultValue = "") List<String> ids,
            HttpServletRequest request,HttpServletResponse response
    ) {
        try {
            String username = "";
            if(DataUtil.isNotEmpty(request.getSession().getAttribute("username"))) {
                username = request.getSession().getAttribute("username").toString();
            }

            if(DataUtil.isEmpty(ids)) {
                throw new Exception("参数格式有误");
            }

            recognizeConfigerService.startApiFindPolicyByIdList(ids, username);

            return HttpResponseUtil.success(null);
        } catch (Exception e) {
            return HttpResponseUtil.error(e.getMessage());
        }

    }

    /**
     * 批量关闭
     * @param ids
     * @params request
     * @params response
     * @return
     */
    @RequestMapping(value = "/stop.do",method = RequestMethod.POST)
    public String stop (
            @RequestParam(value = "ids",defaultValue = "") List<String> ids,
            HttpServletRequest request,HttpServletResponse response
    ) {
        try {
            String username = "";
            if(DataUtil.isNotEmpty(request.getSession().getAttribute("username"))) {
                username = request.getSession().getAttribute("username").toString();
            }

            if(DataUtil.isEmpty(ids)) {
                throw new Exception("参数格式有误");
            }

            recognizeConfigerService.stopApiFindPolicyByIdList(ids, username);

            return HttpResponseUtil.success(null);
        } catch (Exception e) {
            return HttpResponseUtil.error(e.getMessage());
        }

    }


}
