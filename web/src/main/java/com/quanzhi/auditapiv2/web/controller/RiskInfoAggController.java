package com.quanzhi.auditapiv2.web.controller;

import com.quanzhi.auditapiv2.common.dal.dto.query.RiskInfoAggCriteriaDto;
import com.quanzhi.auditapiv2.common.dal.dto.risk.RiskStatistics;
import com.quanzhi.auditapiv2.common.util.dto.ResponseVo;
import com.quanzhi.auditapiv2.common.util.utils.HttpResponseUtil;
import com.quanzhi.auditapiv2.core.service.manager.web.impl.RiskInfoAggServiceImpl;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

/**
 * @ClassName RiskInfoAggController
 * @Description TODO
 * <AUTHOR>
 * @Date 2020/11/23 15:56
 **/
@Slf4j
@RestController
@RequestMapping("/riskInfoAgg")
public class RiskInfoAggController {

    private Logger logger= LoggerFactory.getLogger(RiskInfoAggController.class);

    @Autowired
    private RiskInfoAggServiceImpl riskInfoAggService;

    @ApiOperation("异常列表导出")
    @RequestMapping(value = "/exportRiskInfoAgg",method = RequestMethod.POST)
    public String exportRiskInfoAgg(@RequestBody RiskInfoAggCriteriaDto riskInfoAggCriteriaDto,String dir,String fileName){
        try{
            return HttpResponseUtil.success(riskInfoAggService.exportRiskInfoAgg(riskInfoAggCriteriaDto,dir,fileName));
        } catch (Exception e){
            logger.error("导出异常列表错误：{}",e);
            return HttpResponseUtil.error("导出异常列表错误");
        }
    }

    @RequestMapping(value = "statistics", method = RequestMethod.GET)
    @ApiOperation(value = "获取风险数量的简单统计", tags = "API1.5")
    public ResponseVo<RiskStatistics> getStatistics() {
        return ResponseVo.ok(riskInfoAggService.simpleStatistics());
    }
}
