package com.quanzhi.auditapiv2.web.controller;

import com.quanzhi.auditapiv2.common.dal.entity.ApiWeaknessLog;
import com.quanzhi.auditapiv2.common.util.utils.HttpResponseUtil;
import com.quanzhi.auditapiv2.core.service.manager.web.IApiWeaknessLogService;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 *
 * 《接口弱点日志Controller》
 *
 *
 * @Project:
 * @Module ID:
 * @Comments:
 * @JDK version used:      <JDK1.8>
 * <AUTHOR> [<EMAIL>]
 * @since 2020-08-28-下午16:52:18
 */
@RestController
@RequestMapping("/api/apiWeaknessLog")
public class ApiWeaknessLogController {
    private Logger logger = LoggerFactory.getLogger(ApiWeaknessLogController.class);

    //接口弱点日志service
    @Autowired
    private IApiWeaknessLogService apiWeaknessLogServiceImpl;

    /**
     * 查询接口弱点日志列表
     * @Comments:  <对此方法的描述，可以引用系统设计中的描述>
     * <AUTHOR> [<EMAIL>]
     * @since 2020-08-28 16:52
     * @param
     * @return
     */
    @RequestMapping(value = "listData.do", method = RequestMethod.POST)
    @ApiOperation(value = "查询接口弱点日志列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "apiWeaknessId", value = "接口弱点id", required = true, paramType = "query", dataType = "String")
    })
    public String getApiWeaknessList(String apiWeaknessId) {
        logger.warn("接口弱点日志Controller》查询接口弱点日志列表");

        List<ApiWeaknessLog> data = null;
        try {
            data = apiWeaknessLogServiceImpl.getApiWeaknessLogList(apiWeaknessId);
        } catch (Exception e) {
            logger.error("查询接口弱点日志列表", e);
            return HttpResponseUtil.error(e.getMessage());
        }
        return HttpResponseUtil.success(data);
    }
}
