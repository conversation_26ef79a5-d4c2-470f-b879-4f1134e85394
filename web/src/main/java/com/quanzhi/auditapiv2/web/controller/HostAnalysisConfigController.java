package com.quanzhi.auditapiv2.web.controller;

import com.quanzhi.audit.mix.permission.annotation.Permission;
import com.quanzhi.audit.mix.permission.domain.enums.ResourceEnum;
import com.quanzhi.audit_core.common.model.HostAnalysisConfig;
import com.quanzhi.auditapiv2.common.util.dto.ResponseVo;
import com.quanzhi.auditapiv2.common.util.utils.DataUtil;
import com.quanzhi.auditapiv2.common.util.utils.MD5Util;
import com.quanzhi.auditapiv2.core.service.manager.web.IHostAnalysisConfigService;
import com.quanzhi.auditapiv2.web.common.annotation.DirectiveSync;
import com.quanzhi.auditapiv2.web.common.annotation.OperateLog;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.List;

/**
 *
 * 《域名解析配置Controller》
 *
 *
 * @Project:
 * @Module ID:
 * @Comments:
 * @JDK version used:      <JDK1.8>
 * <AUTHOR> [<EMAIL>]
 * @since 2022-07-25-上午9:56:10
 */
@RestController
@RequestMapping("/api/hostAnalysisConfig")
public class HostAnalysisConfigController {
    private Logger logger = LoggerFactory.getLogger(HostAnalysisConfigController.class);

    //域名解析配置service
    @Autowired
    private IHostAnalysisConfigService hostAnalysisConfigServiceImpl;

    /**
     * 查询域名解析配置列表(分页)
     * @Comments:  <对此方法的描述，可以引用系统设计中的描述>
     * <AUTHOR> [<EMAIL>]
     * @since 2022-07-25 9:56
     * @param
     * @return
     */
    @RequestMapping(value = "getHostAnalysisConfigList.do", method = RequestMethod.GET)
    @ApiOperation(value = "查询域名解析配置列表(分页)")
    public ResponseVo<List<HostAnalysisConfig>> getHostAnalysisConfigList() {
        logger.warn("域名解析配置Controller》查询域名解析配置列表(分页)");

        List<HostAnalysisConfig> data = null;
        
        try {
            data = hostAnalysisConfigServiceImpl.getHostAnalysisConfigList();
        } catch (Exception e) {
            logger.error("查询域名解析配置列表(分页)", e);
            return ResponseVo.error(500, "查询域名解析配置列表(分页)异常");
        }
        
        return ResponseVo.ok(data);
    }

    /**
     * id查询域名解析配置详情
     * @Comments:  <对此方法的描述，可以引用系统设计中的描述>
     * <AUTHOR> [<EMAIL>]
     * @since 2022-07-25 9:56
     * @param
     * @return
     */
    @RequestMapping(value = "getHostAnalysisConfigById.do", method = RequestMethod.GET)
    @ApiOperation(value = "id查询域名解析配置详情")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "域名解析配置id", required = true, paramType = "query", dataType = "String")
    })
    public ResponseVo<HostAnalysisConfig> getHostAnalysisConfigById(String id) {
        logger.warn("域名解析配置Controller》id查询域名解析配置详情");

        HostAnalysisConfig hostAnalysisConfig = null;
        
        try {
            if(DataUtil.isEmpty(id)){
                
                return ResponseVo.error(500, "id不得为空");
            }

            hostAnalysisConfig = hostAnalysisConfigServiceImpl.getHostAnalysisConfigById(id);
        } catch (Exception e) {
            logger.error("id查询域名解析配置详情", e);
            return ResponseVo.error(500, "id查询域名解析配置详情异常");
        }

        return ResponseVo.ok(hostAnalysisConfig);
    }

    /**
     * 新增域名解析配置
     * @Comments:  <对此方法的描述，可以引用系统设计中的描述>
     * <AUTHOR> [<EMAIL>]
     * @since 2022-07-25 9:56
     * @param
     * @return
     */
    @OperateLog(module = "域名解析配置",type = "新增",value = "新增域名解析配置")
    @RequestMapping(value = "addHostAnalysisConfig.do", method = RequestMethod.POST)
    @ApiOperation(value = "新增域名解析配置")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "params", value = "新增内容", required = true, paramType = "body", dataType = "HostAnalysisConfig", dataTypeClass = HostAnalysisConfig.class)
    })
    @DirectiveSync(key = "id", needNotify = true, retry = false, remark = "新增域名解析配置")
    @Permission(type = ResourceEnum.ACTION, value = "WRITE")
    public ResponseVo<HostAnalysisConfig> addHostAnalysisConfig(@RequestBody() HostAnalysisConfig params) {
        logger.warn("域名解析配置Controller》新增域名解析配置");

        try {
            if(DataUtil.isNotEmpty(params)) {

                if(DataUtil.isEmpty(params.getFieldName())) {

                    return ResponseVo.error(500, "请填写字段名称");
                }else if(hostAnalysisConfigServiceImpl.isExists(params.getPositionOne(), params.getPositionTwo(), params.getFieldName())) {
                    
                    return ResponseVo.error(500, "该域名解析配置已存在，无法重复添加");
                }

                if(DataUtil.isEmpty(params.getId())){
                    params.setId(MD5Util.md5(params.getFieldName()+params.getPositionOne()+params.getPositionTwo()+params.getLevel()));
                }
                params = hostAnalysisConfigServiceImpl.addHostAnalysisConfig(params);
            }
        } catch (Exception e) {
            logger.error("新增域名解析配置异常：", e);
            return ResponseVo.error(500, "编辑域名解析配置异常");
        }

        return ResponseVo.ok(params);
    }

    /**
     * 编辑域名解析配置
     * @Comments:  <对此方法的描述，可以引用系统设计中的描述>
     * <AUTHOR> [<EMAIL>]
     * @since 2022-07-25 9:56
     * @param
     * @return
     */
    @OperateLog(module = "域名解析配置",type = "编辑",value = "编辑域名解析配置")
    @RequestMapping(value = "editHostAnalysisConfig.do", method = RequestMethod.POST)
    @ApiOperation(value = "编辑域名解析配置")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "params", value = "编辑内容", required = true, paramType = "body", dataType = "HostAnalysisConfig", dataTypeClass = HostAnalysisConfig.class)
    })
    @DirectiveSync(key = "id", needNotify = true, retry = false, remark = "编辑域名解析配置")
    @Permission(type = ResourceEnum.ACTION, value = "WRITE")
    public ResponseVo<HostAnalysisConfig> editHostAnalysisConfig(@RequestBody() HostAnalysisConfig params) {
        logger.warn("域名解析配置Controller》编辑域名解析配置");

        try {
            if(DataUtil.isNotEmpty(params)) {

                if(DataUtil.isEmpty(params.getFieldName())) {

                    return ResponseVo.error(500, "请填写字段名称");
                }else if(hostAnalysisConfigServiceImpl.isExists(params.getPositionOne(), params.getPositionTwo(), params.getFieldName())) {

                    HostAnalysisConfig hostAnalysisConfig = hostAnalysisConfigServiceImpl.getHostAnalysisConfigById(params.getId());

                    if(!params.getPositionOne().equals(hostAnalysisConfig.getPositionOne())
                            || !params.getPositionTwo().equals(hostAnalysisConfig.getPositionTwo())
                            || !params.getFieldName().equals(hostAnalysisConfig.getFieldName())) {
                        
                        return ResponseVo.error(500, "该域名解析配置已存在，无法重复添加");
                    }
                }

                params = hostAnalysisConfigServiceImpl.editHostAnalysisConfig(params);
            }
        } catch (Exception e) {
            logger.error("编辑域名解析配置：", e);
            return ResponseVo.error(500, "编辑域名解析配置异常");
        }

        return ResponseVo.ok(params);
    }

    /**
     * 批量更新域名解析配置
     * @Comments:  <对此方法的描述，可以引用系统设计中的描述>
     * <AUTHOR> [<EMAIL>]
     * @since 2022-07-25 9:56
     * @param
     * @return
     */
    @OperateLog(module = "域名解析配置",type = "编辑",value = "编辑域名解析配置")
    @RequestMapping(value = "saveHostAnalysisConfig.do", method = RequestMethod.POST)
    @ApiOperation(value = "批量保存域名解析配置")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "params", value = "保存内容", required = true, paramType = "body", allowMultiple = true, dataType = "HostAnalysisConfig", dataTypeClass = HostAnalysisConfig.class)
    })
    @DirectiveSync(key = "id", needNotify = true, retry = false, remark = "批量保存域名解析配置")
    @Permission(type = ResourceEnum.ACTION, value = "WRITE")
    public ResponseVo<HostAnalysisConfig> saveHostAnalysisConfig(@RequestBody() List<HostAnalysisConfig> params) {
        logger.warn("域名解析配置Controller》批量保存域名解析配置");

        try {
            if(DataUtil.isNotEmpty(params)) {
                
                List<String> list = new ArrayList<String>();

                for (HostAnalysisConfig param : params) {

                    if(DataUtil.isEmpty(param.getFieldName())) {

                        return ResponseVo.error(500, "请填写字段名称");
                    }else if(hostAnalysisConfigServiceImpl.isExists(param.getPositionOne(), param.getPositionTwo(), param.getFieldName())) {

                        if(DataUtil.isNotEmpty(param.getId())) {

                            HostAnalysisConfig hostAnalysisConfig = hostAnalysisConfigServiceImpl.getHostAnalysisConfigById(param.getId());

                            if(!param.getPositionOne().equals(hostAnalysisConfig.getPositionOne())
                                    || !param.getPositionTwo().equals(hostAnalysisConfig.getPositionTwo())
                                    || !param.getFieldName().equals(hostAnalysisConfig.getFieldName())) {

                                return ResponseVo.error(500, "该域名解析配置已存在，无法重复添加");
                            }
                        }else {
                            return ResponseVo.error(500, "该域名解析配置已存在，无法重复添加");
                        }
                    }

                    String text = param.getPositionOne() + param.getPositionTwo() + param.getFieldName();
                    if(DataUtil.isEmpty(param.getId())){
                        param.setId(MD5Util.md5(text));
                    }
                    if(list.contains(text)) {

                        return ResponseVo.error(500, "请勿重复添加域名解析配置");
                    }else {
                        list.add(text);
                    }
                }

                params = hostAnalysisConfigServiceImpl.saveHostAnalysisConfig(params);
            }
        } catch (Exception e) {
            logger.error("批量保存域名解析配置：", e);
            return ResponseVo.error(500, "批量保存域名解析配置异常");
        }

        return ResponseVo.ok(params);
    }

    /**
     * 批量删除域名解析配置
     * @Comments:  <对此方法的描述，可以引用系统设计中的描述>
     * <AUTHOR> [<EMAIL>]
     * @since 2020-04-08 9:56
     * @param
     * @return
     */
    @OperateLog(value = "删除解析方式",module = "域名解析配置",type = "删除")
    @RequestMapping(value = "delHostAnalysisConfig.do", method = RequestMethod.POST)
    @ApiOperation(value = "批量删除域名解析配置")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "ids", value = "域名解析配置id数组", required = true, paramType = "body", allowMultiple = true, dataType = "String", dataTypeClass = String.class)
    })
    @DirectiveSync(key = "id", needNotify = true, retry = false, remark = "批量删除域名解析配置")
    @Permission(type = ResourceEnum.ACTION, value = "WRITE")
    public ResponseVo del(@RequestBody() List<String> ids) {
        logger.warn("域名解析配置Controller》批量删除域名解析配置");

        try {
            if(DataUtil.isNotEmpty(ids)) {

                hostAnalysisConfigServiceImpl.delHostAnalysisConfig(ids);
            }else {
                return ResponseVo.error(500, "ids不得为空");
            }
        } catch (Exception e) {
            logger.error("delete host analysis config", e);
            return ResponseVo.error(500, "批量删除域名解析配置异常");
        }

        return ResponseVo.ok();
    }
}
