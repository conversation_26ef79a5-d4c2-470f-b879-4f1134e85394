package com.quanzhi.auditapiv2.web.controller;


import com.alibaba.nacos.api.config.annotation.NacosValue;
import com.quanzhi.auditapiv2.common.util.utils.FileUtils;
import com.quanzhi.auditapiv2.common.util.utils.HttpResponseUtil;
import com.quanzhi.auditapiv2.web.dto.web.PluginDto;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.IOUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.FileOutputStream;
import java.io.InputStream;

@RestController
@RequestMapping("/api/commonPlugin")
@Slf4j
public class CommonPluginController {

    private Logger logger = LoggerFactory.getLogger(CommonPluginController.class);

    @NacosValue(value = "${auditapi.file.path:/Users/<USER>/Documents/filePath}", autoRefreshed = true)
    private String path;

    /**
     * 上传插件代码
     * @param response
     * @param multipartFile
     * @param request
     * @return
     */
    @RequestMapping(value = "/uploadPlugin.do", method = RequestMethod.POST)
    public String uploadPlugin(@RequestParam(value = "multipartFile", defaultValue = "") MultipartFile multipartFile,
                               HttpServletRequest request, HttpServletResponse response) {
        try {

            if (!FileUtils.judeFileExists(path)) {
                logger.warn("common plugin dir {} not exists, create it", path);
                FileUtils.create(new File(path),path);
            }
            InputStream inputStream = multipartFile.getInputStream();
            byte[] bytes = new byte[1024];
            String filePath = path + "/" + System.currentTimeMillis() + "_" + multipartFile.getOriginalFilename();
            FileOutputStream fileOutputStream = new FileOutputStream(new File(filePath));
            while (inputStream.read(bytes) != -1){
                IOUtils.write(bytes,fileOutputStream);
            }
            PluginDto pluginDto = new PluginDto();
            pluginDto.setPath("file://" + filePath);
            pluginDto.setFileName(filePath.substring(filePath.lastIndexOf("/") + 1));
            return HttpResponseUtil.success(pluginDto);
        }catch (Exception e){
            return HttpResponseUtil.error("上传失败");
        }
    }

}
