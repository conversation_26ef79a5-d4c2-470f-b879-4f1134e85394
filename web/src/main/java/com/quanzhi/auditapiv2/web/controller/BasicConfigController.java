package com.quanzhi.auditapiv2.web.controller;

import com.alibaba.fastjson.JSON;
import com.alibaba.nacos.api.config.annotation.NacosValue;
import com.quanzhi.audit.mix.permission.annotation.Permission;
import com.quanzhi.audit.mix.permission.domain.enums.ResourceEnum;
import com.quanzhi.auditapiv2.common.dal.entity.BasicConfig;
import com.quanzhi.auditapiv2.common.util.utils.ConstantUtil;
import com.quanzhi.auditapiv2.common.util.utils.DataUtil;
import com.quanzhi.auditapiv2.common.util.utils.HttpResponseUtil;
import com.quanzhi.auditapiv2.core.service.manager.web.IBasicConfigService;
import com.quanzhi.auditapiv2.web.common.annotation.OperateLog;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.apache.shiro.codec.Base64;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.io.FileInputStream;
import java.io.InputStream;
import java.util.List;

/**
 * 《基础配置Controller》
 *
 * <AUTHOR> [<EMAIL>]
 * @Project:
 * @Module ID:
 * @Comments:
 * @JDK version used:      <JDK1.8>
 * @since 2020-04-08-上午9:56:10
 */
@RestController
@RequestMapping("/api/basicConfig")
public class BasicConfigController {
    private Logger logger = LoggerFactory.getLogger(BasicConfigController.class);

    @NacosValue(value = "${background_filePath:/home/<USER>/audit-api/loginPageImage}",autoRefreshed = true)
    private String filePath;

    //基础配置service
    @Autowired
    private IBasicConfigService basicConfigServiceImpl;

    /**
     * 查询基础配置列表
     *
     * @param
     * @return
     * @Comments: <对此方法的描述，可以引用系统设计中的描述>
     * <AUTHOR> [<EMAIL>]
     * @since 2020-04-08 9:56
     */
    @RequestMapping(value = "listData.do", method = RequestMethod.POST)
    @ApiOperation(value = "查询础配置列表")
    public String listData() {
        logger.warn("基础配置Controller》查询基础配置列表");

        List<BasicConfig> data = null;
        try {
            data = basicConfigServiceImpl.getBasicConfigList();
            File file = new File(filePath + "/background");
            if (file.exists()) {
                try {
                    InputStream in = null;
                    byte[] filedata = null;
                    // 读取图片字节数组
                    in = new FileInputStream(filePath + "/background");
                    filedata = new byte[in.available()];
                    in.read(filedata);
                    in.close();
                    // 对字节数组Base64编码
                    if (!Base64.encodeToString(filedata).isEmpty()){
                        BasicConfig imgfile=new BasicConfig();
                        imgfile.setEncoded(Base64.encodeToString(filedata));
                        imgfile.setType(5);
                        data.add(imgfile);
                    }
                }catch (Exception e){
                    logger.error("加载背景图",e);
                }
            }
        } catch (Exception e) {
            logger.error("查询基础配置列表", e);
            return HttpResponseUtil.error(e.getMessage());
        }

        return HttpResponseUtil.success(data);
    }

    /**
     * 保存基础配置
     *
     * @param
     * @return
     * @Comments: <对此方法的描述，可以引用系统设计中的描述>
     * <AUTHOR> [<EMAIL>]
     * @since 2020-04-08 9:56
     */
    @OperateLog(value="修改基础配置", type = "编辑", module = "基础配置")
    @RequestMapping(value = "save.do", method = RequestMethod.POST)
    @ApiOperation(value = "保存基础配置")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "params", value = "保存内容", required = true, paramType = "query", dataType = "String"),
            @ApiImplicitParam(name = "multipartFile", value = "图片内容", required = true, paramType = "query", dataType = "String")
    })
    @Permission(type = ResourceEnum.ACTION, value = "WRITE")
    public String save(@RequestParam(value = "params", defaultValue = "") String params, MultipartFile multipartFile) {
        logger.warn("基础配置Controller》保存基础配置");

        try {
            if (DataUtil.isNotEmpty(params)) {
                BasicConfig basicConfig = JSON.parseObject(params, BasicConfig.class);
                if (basicConfig != null) {
                    if ((basicConfig.getType().intValue() == BasicConfig.TypeEnum.LOGO.value()
                            || basicConfig.getType().intValue() == BasicConfig.TypeEnum.ICON.value()
                            || basicConfig.getType().intValue() == BasicConfig.TypeEnum.COMMON.value()) && DataUtil.isEmpty(multipartFile)) {
                        return HttpResponseUtil.error("请上传图片");
                    } else if (basicConfig.getType().intValue() == BasicConfig.TypeEnum.LOGO.value() && !multipartFile.getOriginalFilename().endsWith("png")) {
                        return HttpResponseUtil.error("产品logo图片只能上传png文件");
                    } else if (basicConfig.getType().intValue() == BasicConfig.TypeEnum.ICON.value() && !multipartFile.getOriginalFilename().endsWith("ico")) {
                        return HttpResponseUtil.error("产品icon图片只能上传ico文件");
                    }
                    if (basicConfig.getType().intValue() == BasicConfig.TypeEnum.BACKGROUND.value()){
                        basicConfigServiceImpl.saveBasicConfig(multipartFile);
                    }else {
                        basicConfigServiceImpl.saveBasicConfig(basicConfig, multipartFile);
                    }
                } else {
                    return HttpResponseUtil.error(ConstantUtil.PARAMS_ERROR);
                }
            } else {
                return HttpResponseUtil.error("params不得为空");
            }
        } catch (Exception e) {
            logger.error("保存基础配置", e);
            return HttpResponseUtil.error(e.getMessage());
        }
        return HttpResponseUtil.success();
    }
}
