package com.quanzhi.auditapiv2.web.controller;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.nacos.api.config.annotation.NacosValue;
import com.quanzhi.audit_core.common.monitor.annotation.MonitorTransaction;
import com.quanzhi.auditapiv2.common.util.utils.DataUtil;
import com.quanzhi.auditapiv2.common.util.utils.HttpResponseUtil;
import com.quanzhi.auditapiv2.core.service.manager.dto.UrlStructureSearchDto;
import com.quanzhi.auditapiv2.core.service.manager.web.IUrlStructureService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/api/urlStructure")
public class UrlStructureController {

    @Autowired
    private IUrlStructureService urlStructureService;

    //单个节点下面如果超过5000个子节点，基本就是restful接口没有合并，需要排查
    @NacosValue(value = "${URL_STRUCTURE_MAX_LIMIT:5000}",autoRefreshed = true)
    private Integer URL_STRUCTURE_MAX_LIMIT = 5000;

    @MonitorTransaction(type = "urlStructure",name = "请求树形结构")
    @RequestMapping(value = "pageUrlsByTree.do",method = RequestMethod.POST)
    public String pageUrlsByTree(
            @RequestParam(value = "urlStructureSearch",defaultValue = "") String urlStructureSearch
    ) {

        UrlStructureSearchDto urlStructureSearchDto = JSONObject.parseObject(urlStructureSearch,UrlStructureSearchDto.class);

        if (DataUtil.isEmpty( urlStructureSearchDto.getPreUrl() )) {
            urlStructureSearchDto.setLevel(1);
        }
        urlStructureSearchDto.setPage(1);
        urlStructureSearchDto.setLimit(URL_STRUCTURE_MAX_LIMIT);

        return HttpResponseUtil.success(urlStructureService.pageUrlsByTree(urlStructureSearchDto));
    }

}
