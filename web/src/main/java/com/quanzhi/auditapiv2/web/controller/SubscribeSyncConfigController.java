package com.quanzhi.auditapiv2.web.controller;


import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.nacos.api.annotation.NacosInjected;
import com.alibaba.nacos.api.config.ConfigService;
import com.alibaba.nacos.api.exception.NacosException;
import com.quanzhi.audit.mix.permission.annotation.Permission;
import com.quanzhi.audit.mix.permission.domain.enums.ResourceEnum;
import com.quanzhi.audit_core.common.model.FeatureLabel;
import com.quanzhi.auditapiv2.common.dal.dto.query.NacosFilterDto;
import com.quanzhi.auditapiv2.common.dal.dto.query.Predicate;
import com.quanzhi.auditapiv2.common.dal.dto.query.QueryNacos;
import com.quanzhi.auditapiv2.common.dal.entity.SubscribePolicyRule;
import com.quanzhi.auditapiv2.common.dal.entity.SubscribeSyncConfig;
import com.quanzhi.auditapiv2.common.dal.entity.TestResultEntity;
import com.quanzhi.auditapiv2.common.util.dto.ListOutputDto;
import com.quanzhi.auditapiv2.common.util.dto.ResponseVo;
import com.quanzhi.auditapiv2.common.util.utils.DataUtil;
import com.quanzhi.auditapiv2.core.service.manager.web.IResourceChangedEventNacosService;
import com.quanzhi.auditapiv2.core.service.manager.web.ISubscribeSyncConfigNacosService;
import com.quanzhi.auditapiv2.web.common.annotation.OperateLog;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiResponse;
import io.swagger.annotations.ApiResponses;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

@Slf4j
@RestController
@RequestMapping("/api/subscribeSyncConfig")
public class SubscribeSyncConfigController {


    @Autowired
    private ISubscribeSyncConfigNacosService subscribeSyncConfigNacosService;

    @Autowired
    private IResourceChangedEventNacosService resourceChangedEventNacosService;

    @NacosInjected
    private ConfigService configService;

    /**
     * syslog基础配置
     * 新增配置时需要改变 syncerDsl.source.topics
     * 以及 syncerDsl.storage.properties 中 host，port,protocol
     */
    private String sysLogBaseConfigStr = "{\"syncerDsl\":{\"name\":\"syslog_http_event_9f89c1553da88ea461d4afb227cc676c\",\"source\":{\"topics\":[\"SYSLOG_LOCAL_TOPIC_1614595065885\"],\"poll_timeout\":1000,\"type\":\"KAFKA\",\"priority\":50,\"properties\":{}},\"storage\":{\"storeClass\":\"com.quanzhi.audit.syncer.store.syslog.SyslogWork\",\"type\":\"SYSLOG\",\"priority\":50,\"properties\":{\"protocol\":\"TCP\",\"port\":4444,\"log\":6,\"host\":\"*************\"}},\"threadNum\":1,\"topoloys\":[{\"functions\":[{\"op\":\"Sink\",\"param\":{\"methods\":[],\"collection\":\"syslog\"},\"order\":1}],\"name\":\"SinkTopoloy\"}]},\"type\":\"json\"}";

    private String syncerDataIdPre = "syslog.resource.change.event.";

    private String syncerDataIdEnd = ".json";

    private String groupId = "syncer";

    private String sysLogParentDataId = "datasyncer.sourceHost.storeHost.global.json";

    private final static String topic_prefix = "SYSLOG_LOCAL_TOPIC_";

    /**
     * 保存数据同步配置
     *
     * @param
     * @return
     */
    @OperateLog(module = "数据订阅", type = "编辑", value = "编辑订阅配置")
    @RequestMapping(value = "/save.do", method = RequestMethod.POST)
    @Permission(type = ResourceEnum.ACTION, value = "WRITE")
    public ResponseVo<SubscribeSyncConfig> save(HttpServletRequest request, @RequestBody SubscribeSyncConfig subscribeSyncConfig

    ) throws InterruptedException {
        SubscribeSyncConfig ss = new SubscribeSyncConfig();
        BeanUtils.copyProperties(subscribeSyncConfig, ss);
        if (DataUtil.isNotEmpty(request.getSession()) && DataUtil.isEmpty(ss.getOperateName()) && DataUtil.isNotEmpty(request.getSession().getAttribute("username"))) {
            String username = request.getSession().getAttribute("username").toString();
            ss.setOperateName(username);
        }
        if (DataUtil.isEmpty(ss.getId())) {
            ss.setCreateTime(System.currentTimeMillis());
        }
        ss.setUpdateTime(System.currentTimeMillis());

        if (subscribeSyncConfigNacosService.checkFieldUsed("name", ss, ss.getType())) {
            return ResponseVo.error("同步配置名称已经被使用！", 233);
        }

        for (SubscribeSyncConfig oldInfo : subscribeSyncConfigNacosService.getAll()) {
            if (DataUtil.isNotEmpty(oldInfo.getType()) && DataUtil.isNotEmpty(oldInfo.getIp()) && DataUtil.isNotEmpty(oldInfo.getPort())) {
                //sysLog：Ip+端口+协议组合不重复校验
                if (oldInfo.getType().equals(SubscribeSyncConfig.SynncConfigEnum.SYSLOG.name()) && oldInfo.getIp().equals(subscribeSyncConfig.getIp()) && oldInfo.getPort().equals(subscribeSyncConfig.getPort()) && oldInfo.getProtocol().equals(subscribeSyncConfig.getProtocol())) {
                    if (subscribeSyncConfig.getId() == null || !subscribeSyncConfig.getId().equals(oldInfo.getId())) {
                        return ResponseVo.error("已存在该IP、端口、协议组合的syslog配置！", 233);
                    }
                }
            }
            if (DataUtil.isNotEmpty(oldInfo.getType()) && DataUtil.isNotEmpty(oldInfo.getIpPort()) && DataUtil.isNotEmpty(oldInfo.getTopic())) {
                //Kafka：IP+端口+topic组合不重复校验
                if (oldInfo.getType().equals(SubscribeSyncConfig.SynncConfigEnum.KAFKA.name()) && oldInfo.getIpPort().equals(subscribeSyncConfig.getIpPort()) && oldInfo.getTopic().equals(subscribeSyncConfig.getTopic())) {
                    if (subscribeSyncConfig.getId() == null || !subscribeSyncConfig.getId().equals(oldInfo.getId())) {
                        return ResponseVo.error("已存在该IP和端口、topic组合的kafka订阅方式！", 233);
                    }
                }
            }
            //webhook不重复校验
            if (DataUtil.isNotEmpty(oldInfo.getDingDingRobotParam()) && DataUtil.isNotEmpty(oldInfo.getDingDingRobotParam().getWebhook())) {
                //webhook: webhook+secret组合不重复校验
                if (SubscribeSyncConfig.SynncConfigEnum.WEBHOOK.name().equals(oldInfo.getType())
                        && subscribeSyncConfig.getDingDingRobotParam() != null
                        && oldInfo.getDingDingRobotParam().getWebhook().equals(subscribeSyncConfig.getDingDingRobotParam().getWebhook())
                        && oldInfo.getDingDingRobotParam().getSecret().equals(subscribeSyncConfig.getDingDingRobotParam().getSecret())) {
                    if (subscribeSyncConfig.getId() == null || !subscribeSyncConfig.getId().equals(oldInfo.getId())) {
                        return ResponseVo.error("已存在该webhook、secret组合的webhook订阅方式！",233);
                    }
                }
            }
        }

        if (ss.getType().equals(SubscribeSyncConfig.SynncConfigEnum.SYSLOG.name()) && DataUtil.isEmpty(ss.getTopic())) {

            String topic = topic_prefix + System.currentTimeMillis();
            ss.setTopic(topic);

        }

        SubscribeSyncConfig result = subscribeSyncConfigNacosService.save(ss);

        //更新syslog配置
        if (DataUtil.isNotEmpty(result.getId()) && ss.getType().equals(SubscribeSyncConfig.SynncConfigEnum.SYSLOG.name())) {

            try {
                String dataId = syncerDataIdPre + result.getId() + syncerDataIdEnd;

                JSONObject jsonObject = (JSONObject) JSON.parse(sysLogBaseConfigStr);

                jsonObject.getJSONObject("syncerDsl").put("name", "syslog_http_event_" + result.getId());

                jsonObject.getJSONObject("syncerDsl").getJSONObject("source").put("topics", Arrays.asList(result.getTopic()));

                jsonObject.getJSONObject("syncerDsl").getJSONObject("storage").getJSONObject("properties").put("host", result.getIp());
                jsonObject.getJSONObject("syncerDsl").getJSONObject("storage").getJSONObject("properties").put("port", Integer.valueOf(result.getPort()));
                jsonObject.getJSONObject("syncerDsl").getJSONObject("storage").getJSONObject("properties").put("protocol", result.getProtocol());

                configService.publishConfig(dataId, groupId, JSON.toJSONString(jsonObject));

            } catch (NacosException e) {

                log.error("config syslog error", e);
            }

        }

        return ResponseVo.ok(result);
    }

    /**
     * 分页获取接口分类列表
     *
     * @param page
     * @param limit
     * @return
     */
    @RequestMapping(value = "/page.do", method = RequestMethod.GET)
    @ApiImplicitParams({@ApiImplicitParam(name = "page", value = "page", required = true, paramType = "query", dataType = "Integer"), @ApiImplicitParam(name = "limit", value = "limit", required = true, paramType = "query", dataType = "Integer"),})
    @ApiResponses({@ApiResponse(code = 200, message = "{\"success\":true,\"errorCode\":0,\"data\":{\"rows\":[{\"id\":\"xxx\",\"name\":\"abc\",\"desc\":\"123\",\"enabled\":true,\"operateName\":\"mingming\",\"createTime\":\"1589426873000\",\"Type\":1,\"classificationType\":\"APP\"}],\"totalCount\":1}}", response = FeatureLabel.class),})
    public ResponseVo<ListOutputDto<SubscribeSyncConfig>> page(@RequestParam(value = "page", defaultValue = "1") Integer page, @RequestParam(value = "limit", defaultValue = "10") Integer limit, @RequestParam(value = "type", defaultValue = "") String type) {
        List<NacosFilterDto> nacosFilterDtos = new ArrayList<>();

        if (DataUtil.isNotEmpty(type)) {
            NacosFilterDto nacosFilterDto = new NacosFilterDto("type", Predicate.IS, type);
            nacosFilterDtos.add(nacosFilterDto);
        }

        ListOutputDto<SubscribeSyncConfig> data = subscribeSyncConfigNacosService.page(page, limit, nacosFilterDtos);

        //升级到3.0后，对kafka配置进行适配
        for (SubscribeSyncConfig subscribeSyncConfig : data.getRows()) {
            if (subscribeSyncConfig.getType().equals(SubscribeSyncConfig.SynncConfigEnum.KAFKA.name())) {
                //老配置没有ip port字段，拼接
                if (DataUtil.isEmpty(subscribeSyncConfig.getIpPort()) && DataUtil.isNotEmpty(subscribeSyncConfig.getIp()) && DataUtil.isNotEmpty(subscribeSyncConfig.getPort())) {
                    subscribeSyncConfig.setIpPort(subscribeSyncConfig.getIp() + ":" + subscribeSyncConfig.getPort());
                }
            }
        }

        return ResponseVo.ok(data);
    }

    /**
     * 根据ID，获取详情
     *
     * @param id
     * @return
     */
    @RequestMapping(value = "/getById.do", method = RequestMethod.GET)
    @ApiResponses({@ApiResponse(code = 200, message = "{\"success\":true,\"errorCode\":0,\"data\":{\"id\":\"xxx\",\"name\":\"abc\",\"desc\":\"123\",\"enabled\":true,\"operateName\":\"mingming\",\"createTime\":\"1589426873000\",\"Type\":1,\"classificationType\":\"APP\"}}", response = FeatureLabel.class),})
    public ResponseVo<String> getById(@RequestParam(value = "id", defaultValue = "") String id) {
        return ResponseVo.ok(subscribeSyncConfigNacosService.getById(id));
    }

    /**
     * 根据ID，删除分类
     *
     * @param ids
     * @return
     */
    @OperateLog(module = "数据订阅", type = "删除", value = "删除订阅配置")
    @RequestMapping(value = "/delete.do", method = RequestMethod.POST)
    @Permission(type = ResourceEnum.ACTION, value = "WRITE")
    public ResponseVo<String> delete(@RequestParam List<String> ids) throws InterruptedException {
        subscribeSyncConfigNacosService.delete(ids);
        return ResponseVo.ok("删除成功!");
    }

    /**
     * 测试同步配置
     *
     * @param
     * @return
     */
    @RequestMapping(value = "/test.do", method = RequestMethod.POST)
    public ResponseVo<TestResultEntity> test(HttpServletRequest request, @RequestBody SubscribeSyncConfig subscribeSyncConfig) {
        return ResponseVo.ok(subscribeSyncConfigNacosService.testConnect(subscribeSyncConfig));
    }

    /**
     * 返回同步配置是否使用中
     *
     * @param
     * @return
     */
    @RequestMapping(value = "/checkIsUsed.do", method = RequestMethod.POST)
    public ResponseVo<Boolean> checkIsUsed(HttpServletRequest request, @RequestBody SubscribeSyncConfig subscribeSyncConfig) {

        boolean used = false;

        if (DataUtil.isNotEmpty(subscribeSyncConfig.getId()) && DataUtil.isNotEmpty(subscribeSyncConfig.getType())) {

            switch (SubscribeSyncConfig.SynncConfigEnum.valueOf(subscribeSyncConfig.getType())) {

                case KAFKA:

                    QueryNacos queryKafkaNacos = new QueryNacos();
//                    queryKafkaNacos.where("output", Predicate.IN, Arrays.asList(SubscribePolicyRule.OutputEnum.KAFKA.name()));
                    queryKafkaNacos.where("kafkaConfigId", Predicate.IS, subscribeSyncConfig.getId());

                    SubscribePolicyRule subscribePolicyRule = resourceChangedEventNacosService.findOne(queryKafkaNacos);

                    if (DataUtil.isNotEmpty(subscribePolicyRule)) {
                        used = subscribePolicyRule.getEnabled();
                    }
                    break;
                case SYSLOG:

                    QueryNacos querySyslogNacos = new QueryNacos();
//                    querySyslogNacos.where("output", Predicate.IN, Arrays.asList(SubscribePolicyRule.OutputEnum.SYSLOG.name()));
                    querySyslogNacos.where("sysLogConfigId", Predicate.IS, subscribeSyncConfig.getId());

                    SubscribePolicyRule subscribePolicyRuleForSyslog = resourceChangedEventNacosService.findOne(querySyslogNacos);

                    if (DataUtil.isNotEmpty(subscribePolicyRuleForSyslog)) {
                        used = subscribePolicyRuleForSyslog.getEnabled();
                    }
                    break;
                default:
            }
        }

        return ResponseVo.ok(used);
    }
}
