package com.quanzhi.auditapiv2.web.controller;

import com.quanzhi.auditapiv2.common.util.utils.HttpResponseUtil;
import com.quanzhi.auditapiv2.core.service.manager.web.impl.PolicyConfigInfoServiceImpl;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

/**
 * @ClassName PolicyConfigInfoController
 * @Description TODO
 * <AUTHOR>
 * @Date 2021/2/4 15:09
 **/
@RestController
@RequestMapping("/policy/info")
@Slf4j
public class PolicyConfigInfoController {

    @Autowired
    private PolicyConfigInfoServiceImpl policyConfigInfoService;

    @ApiOperation("获取所有的指标项配置")
    @RequestMapping(value = "/getList",method = RequestMethod.GET)
    public String getList(){
        try {
            return HttpResponseUtil.success(policyConfigInfoService.list());
        }catch (Exception e){
            log.error("获取指标项配置有误:{}",e);
            return HttpResponseUtil.error("获取指标项配置有误");
        }
    }


    @ApiOperation("获取特定的指标项配置")
    @RequestMapping(value = "/getSpecificIndex",method = RequestMethod.GET)
    public String getSpecificIndex(){
        try {
            return HttpResponseUtil.success(policyConfigInfoService.getSpecificIndex());
        }catch (Exception e){
            log.error("获取指标项配置有误:{}",e);
            return HttpResponseUtil.error("获取指标项配置有误");
        }
    }









}
