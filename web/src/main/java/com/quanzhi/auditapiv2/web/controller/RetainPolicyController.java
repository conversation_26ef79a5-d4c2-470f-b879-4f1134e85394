package com.quanzhi.auditapiv2.web.controller;

import com.quanzhi.auditapiv2.common.util.utils.DataUtil;
import com.quanzhi.auditapiv2.common.util.utils.HttpResponseUtil;
import com.quanzhi.auditapiv2.core.service.manager.web.IRetainPolicyService;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiResponse;
import io.swagger.annotations.ApiResponses;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.Map;

@RestController
@RequestMapping("/api/retainPolicy")
public class RetainPolicyController {

    @Autowired
    private IRetainPolicyService retainPolicyService;

    @RequestMapping(value ="/getRetainCount.do",method = RequestMethod.GET)
    @ApiResponses({
            @ApiResponse(code = 200,
                    message = "{\"errorCode\":0,\"success\":true,\"data\":{\"discover_sample_count\":10,\"metabase_snapshot_count\":10}}"
            ),
    })
    public String getRetainCount () {

        Map<String,String> countMap = retainPolicyService.getRetainCount();
        return HttpResponseUtil.success(countMap);
    }

    @RequestMapping(value = "/save.do",method = RequestMethod.POST)
    @ApiImplicitParams({
            @ApiImplicitParam(name = "discover_sample_count", value = "样例最少保留条数", required = true, paramType = "query", dataType = "Integer"),
            @ApiImplicitParam(name = "metabase_snapshot_count", value = "快照留存", required = true, paramType = "query", dataType = "Integer"),
    })
    public String save(
            @RequestParam(value = "discover_sample_count") String discover_sample_count,
            @RequestParam(value = "metabase_snapshot_count") String metabase_snapshot_count
    ) {

        String pattern = "[1-9]\\d*";

        if(DataUtil.isNotEmpty(discover_sample_count ) && !discover_sample_count.matches( pattern )) {
            return HttpResponseUtil.error("请输入大于0的数字");
        }

        if(DataUtil.isNotEmpty(metabase_snapshot_count ) && !metabase_snapshot_count.matches( pattern )) {
            return HttpResponseUtil.error("请输入大于0的数字");
        }

        retainPolicyService.save(discover_sample_count,metabase_snapshot_count);
        return HttpResponseUtil.success();
    }
}
