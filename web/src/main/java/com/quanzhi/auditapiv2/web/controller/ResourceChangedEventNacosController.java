package com.quanzhi.auditapiv2.web.controller;

import com.alibaba.fastjson.JSONObject;
import com.quanzhi.audit.mix.permission.annotation.Permission;
import com.quanzhi.audit.mix.permission.domain.enums.ResourceEnum;
import com.quanzhi.audit.mix.plugin.domain.Plugin;
import com.quanzhi.audit_core.common.model.FeatureLabel;
import com.quanzhi.auditapiv2.common.dal.dto.query.NacosFilterDto;
import com.quanzhi.auditapiv2.common.dal.dto.query.Predicate;
import com.quanzhi.auditapiv2.common.dal.entity.EntityNecessaryLackException;
import com.quanzhi.auditapiv2.common.dal.entity.SubscribePolicyRule;
import com.quanzhi.auditapiv2.common.dal.entity.node.ClusterType;
import com.quanzhi.auditapiv2.common.util.dto.ListOutputDto;
import com.quanzhi.auditapiv2.common.util.dto.ResponseVo;
import com.quanzhi.auditapiv2.common.util.utils.DataUtil;
import com.quanzhi.auditapiv2.common.util.utils.HttpResponseUtil;
import com.quanzhi.auditapiv2.core.service.manager.web.IDataPushPluginService;
import com.quanzhi.auditapiv2.core.service.manager.web.IResourceChangedEventNacosService;
import com.quanzhi.auditapiv2.core.service.manager.web.taskmanage.DailySendTaskService;
import com.quanzhi.auditapiv2.core.service.node.cluster.ClusterNodeService;
import com.quanzhi.auditapiv2.web.common.annotation.OperateLog;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiResponse;
import io.swagger.annotations.ApiResponses;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.context.event.ApplicationReadyEvent;
import org.springframework.context.event.EventListener;
import org.springframework.scheduling.TaskScheduler;
import org.springframework.scheduling.support.CronTrigger;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.*;
import java.util.concurrent.ScheduledFuture;

@RestController
@RequestMapping("/api/resourceChangedEventNacos")
@Slf4j
public class ResourceChangedEventNacosController {

    @Resource
    private ClusterNodeService clusterNodeService;

    @Autowired
    private IResourceChangedEventNacosService resourceChangedEventNacosService;

    @Autowired
    private IDataPushPluginService dataPushPluginService;

    private final DailySendTaskService dailySendTaskService;

    private final TaskScheduler taskScheduler;

    private final Map<String, ScheduledFuture<?>> scheduledManagerMap = new HashMap();

    private static final String CHAR_LOWER = "abcdefghijklmnopqrstuvwxyz";

    public ResourceChangedEventNacosController(DailySendTaskService dailySendTaskService, TaskScheduler taskScheduler) {
        this.dailySendTaskService = dailySendTaskService;
        this.taskScheduler = taskScheduler;
    }

    @EventListener({ApplicationReadyEvent.class})
    public void onEvent(ApplicationReadyEvent applicationReadyEvent) {
        log.info("initDailySendJob!");
        this.initDailySendJob();
        log.info("init success!");
    }

    /**
     * 保存订阅规则
     *
     * @param
     * @return
     */
    @OperateLog(module = "数据订阅", value = "编辑订阅", type = "编辑")
    @RequestMapping(value = "/save.do", method = RequestMethod.POST)
    @Permission(type = ResourceEnum.ACTION, value = "WRITE")
    public String save(
            HttpServletRequest request,
            @RequestParam(value = "pluginFile", required = false) MultipartFile multipartFile,
            @RequestParam String subscribePolicyRuleStr

    ) throws InterruptedException {
        SubscribePolicyRule subscribePolicyRule = JSONObject.parseObject(subscribePolicyRuleStr, SubscribePolicyRule.class);
        try {
            SubscribePolicyRule.checkNecessary(subscribePolicyRule);
        } catch (EntityNecessaryLackException e) {
            return HttpResponseUtil.error(e.getMessage());
        }

        if (DataUtil.isNotEmpty(request.getSession()) && DataUtil.isEmpty(subscribePolicyRule.getOperateName()) && DataUtil.isNotEmpty(request.getSession().getAttribute("username"))) {
            String username = request.getSession().getAttribute("username").toString();
            subscribePolicyRule.setOperateName(username);
        }

        if (DataUtil.isEmpty(subscribePolicyRule.getId())) {
            subscribePolicyRule.setCreateTime(System.currentTimeMillis());
        }
        subscribePolicyRule.setUpdateTime(System.currentTimeMillis());

        if (resourceChangedEventNacosService.checkSingleFieldUsed("name", subscribePolicyRule)) {
            return HttpResponseUtil.error("订阅名称已经被使用！");
        }
        if (subscribePolicyRule.getUseCustomPlugin() != null && Boolean.TRUE.equals(subscribePolicyRule.getUseCustomPlugin())) {
            if (DataUtil.isNotEmpty(multipartFile)) {
                boolean check = dataPushPluginService.checkPlugin(multipartFile);
                if (!check) {
                    return HttpResponseUtil.error("插件格式不符合规范");
                }
                try {
                    subscribePolicyRule = dataPushPluginService.saveDataPushPlugin(multipartFile, subscribePolicyRule);
                } catch (Exception e) {
                    log.error("插件格式不符合规范", e);
                    return HttpResponseUtil.error("插件格式不符合规范");
                }
            }
        } else {
            subscribePolicyRule.setPluginId(null);
            subscribePolicyRule.setPluginNames(new ArrayList<>());
        }

        if (subscribePolicyRule.getOutput() == null || subscribePolicyRule.getOutput().size() < 0) {
            return HttpResponseUtil.error("请选择推送方式！");
        }

        resourceChangedEventNacosService.save(subscribePolicyRule);

        // 这里是主节点并且是订阅审计日志的话加一下推送到所有子节点
        ClusterType clusterType = clusterNodeService.getClusterType();
        if (ClusterType.MASTER.equals(clusterType) && "EVENT".equals(subscribePolicyRule.getType())) {
            List<String> ids = new ArrayList<>();
            if (DataUtil.isNotEmpty(subscribePolicyRule.getKafkaConfigId())) {
                ids.add(subscribePolicyRule.getKafkaConfigId());
            }
            if (DataUtil.isNotEmpty(subscribePolicyRule.getSysLogConfigId())) {
                ids.add(subscribePolicyRule.getSysLogConfigId());
            }
            resourceChangedEventNacosService.pushToSalve(multipartFile, subscribePolicyRule, ids);
        }

        return HttpResponseUtil.success();
    }

    @OperateLog(module = "数据订阅", value = "编辑订阅", type = "编辑")
    @RequestMapping(value = "/saveDailyRule", method = RequestMethod.POST)
    @Permission(type = ResourceEnum.ACTION, value = "WRITE")
    public String saveDailyRule(
            HttpServletRequest request,
            @RequestParam(value = "pluginFile", required = false) MultipartFile multipartFile,
            @RequestParam String subscribePolicyRuleStr

    ) throws InterruptedException {
        SubscribePolicyRule subscribePolicyRule = JSONObject.parseObject(subscribePolicyRuleStr, SubscribePolicyRule.class);
        try {
            SubscribePolicyRule.checkNecessary(subscribePolicyRule);
        } catch (EntityNecessaryLackException e) {
            return HttpResponseUtil.error(e.getMessage());
        }

        if (DataUtil.isNotEmpty(request.getSession().getAttribute("username"))) {
            String username = request.getSession().getAttribute("username").toString();
            subscribePolicyRule.setOperateName(username);
        }

        if (DataUtil.isEmpty(subscribePolicyRule.getId())) {
            subscribePolicyRule.setCreateTime(System.currentTimeMillis());
        }
        subscribePolicyRule.setUpdateTime(System.currentTimeMillis());

        if (resourceChangedEventNacosService.checkSingleFieldUsed("name", subscribePolicyRule)) {
            return HttpResponseUtil.error("订阅名称已经被使用！");
        }
        if (subscribePolicyRule.getUseCustomPlugin() != null && Boolean.TRUE.equals(subscribePolicyRule.getUseCustomPlugin())) {
            if (DataUtil.isNotEmpty(multipartFile)) {
                boolean check = dataPushPluginService.checkPlugin(multipartFile);
                if (!check) {
                    return HttpResponseUtil.error("插件格式不符合规范");
                }
                try {
                    subscribePolicyRule = dataPushPluginService.saveDataPushPlugin(multipartFile, subscribePolicyRule);
                } catch (Exception e) {
                    log.error("插件格式不符合规范", e);
                    return HttpResponseUtil.error("插件格式不符合规范");
                }
            }
        } else {
            subscribePolicyRule.setPluginId(null);
            subscribePolicyRule.setPluginNames(new ArrayList<>());
        }

        if (subscribePolicyRule.getOutput() == null || subscribePolicyRule.getOutput().size() < 0) {
            return HttpResponseUtil.error("请选择推送方式！");
        }

        //根据配置，生成一个定时任务
        String scheduleName = "DailyFullSendJob" + generateRandomString(8);

        Runnable runnable = dailySendTaskService.createRunnable(subscribePolicyRule);
        ScheduledFuture<?> schedule = null;
        if (DataUtil.isNotEmpty(subscribePolicyRule.getCron())) {
            schedule = taskScheduler.schedule(runnable, new CronTrigger(subscribePolicyRule.getCron()));
        } else if (DataUtil.isNotEmpty(subscribePolicyRule.getStartTime()) && subscribePolicyRule.getStartTime() != 0) {
            schedule = taskScheduler.schedule(runnable, new Date(subscribePolicyRule.getStartTime()));
        }
        if (DataUtil.isNotEmpty(schedule)) {
            scheduledManagerMap.put(scheduleName, schedule);
            subscribePolicyRule.setScheduleName(scheduleName);
        }

        resourceChangedEventNacosService.save(subscribePolicyRule);

        return HttpResponseUtil.success();
    }

    public void initDailySendJob() {
        List<SubscribePolicyRule> subscribePolicyRules = resourceChangedEventNacosService.getAll();
        for (SubscribePolicyRule subscribePolicyRule : subscribePolicyRules) {
            //关闭的不初始化
            if (!subscribePolicyRule.getEnabled()) {
                continue;
            }
            //不是全量推送不初始化
            if (!subscribePolicyRule.getCycle().equals("FULL_DAILY")) {
                continue;
            }
            //没有单次推送时间或周期推送时间不初始化
            if (DataUtil.isEmpty(subscribePolicyRule.getStartTime()) && DataUtil.isEmpty(subscribePolicyRule.getCron())) {
                continue;
            }
            //单次推送已过期的不初始化
            if (DataUtil.isNotEmpty(subscribePolicyRule.getStartTime()) && subscribePolicyRule.getStartTime() < System.currentTimeMillis()) {
                continue;
            }
            Runnable runnable = dailySendTaskService.createRunnable(subscribePolicyRule);
            if (DataUtil.isNotEmpty(subscribePolicyRule.getScheduleName()) && DataUtil.isNotEmpty(runnable)) {
                String scheduleName = subscribePolicyRule.getScheduleName();
                ScheduledFuture<?> schedule = null;
                if (DataUtil.isNotEmpty(subscribePolicyRule.getCron())) {
                    schedule = taskScheduler.schedule(runnable, new CronTrigger(subscribePolicyRule.getCron()));
                } else if (DataUtil.isNotEmpty(subscribePolicyRule.getStartTime()) && subscribePolicyRule.getStartTime() != 0) {
                    schedule = taskScheduler.schedule(runnable, new Date(subscribePolicyRule.getStartTime()));
                }
                if (DataUtil.isNotEmpty(schedule)) {
                    scheduledManagerMap.put(scheduleName, schedule);
                }
            }
        }
    }

    private String generateRandomString(int length) {
        StringBuilder sb = new StringBuilder();
        Random rnd = new Random();
        while (sb.length() < length) { // length of the random string.
            int index = (int) (rnd.nextFloat() * CHAR_LOWER.length());
            char randomChar = CHAR_LOWER.charAt(index);
            sb.append(randomChar);
        }
        return sb.toString();
    }

    /**
     * 分页获取订阅规则列表
     *
     * @param page
     * @param limit
     * @return
     */
    @RequestMapping(value = "/page.do", method = RequestMethod.GET)
    @ApiImplicitParams({
            @ApiImplicitParam(name = "page", value = "page",
                    required = true, paramType = "query", dataType = "Integer"),
            @ApiImplicitParam(name = "limit", value = "limit",
                    required = true, paramType = "query", dataType = "Integer"),
    })
    @ApiResponses({
            @ApiResponse(code = 200,
                    message = "{\"success\":true,\"errorCode\":0,\"data\":{\"rows\":[{\"id\":\"xxx\",\"name\":\"abc\",\"desc\":\"123\",\"enabled\":true,\"operateName\":\"mingming\",\"createTime\":\"1589426873000\",\"Type\":1,\"classificationType\":\"APP\"}],\"totalCount\":1}}",
                    response = FeatureLabel.class
            ),
    })
    public ResponseVo<ListOutputDto<SubscribePolicyRule>> page(
            @RequestParam(value = "page", defaultValue = "1") Integer page,
            @RequestParam(value = "limit", defaultValue = "10") Integer limit,
            @RequestParam(value = "name", defaultValue = "") String name
    ) {

        List<NacosFilterDto> nacosFilterDtos = new ArrayList<>();

        if (DataUtil.isNotEmpty(name)) {
            NacosFilterDto nacosFilterDto = new NacosFilterDto("name", Predicate.REGEX, name);
            nacosFilterDtos.add(nacosFilterDto);
        }
        ListOutputDto<SubscribePolicyRule> data = resourceChangedEventNacosService.page(page, limit, nacosFilterDtos);
        return ResponseVo.ok(data);
    }

    /**
     * 根据ID，获取详情
     *
     * @param id
     * @return
     */
    @RequestMapping(value = "/getById.do", method = RequestMethod.GET)
    @ApiResponses({
            @ApiResponse(code = 200,
                    message = "{\"success\":true,\"errorCode\":0,\"data\":{\"id\":\"xxx\",\"name\":\"abc\",\"desc\":\"123\",\"enabled\":true,\"operateName\":\"mingming\",\"createTime\":\"1589426873000\",\"Type\":1,\"classificationType\":\"APP\"}}",
                    response = FeatureLabel.class
            ),
    })
    public ResponseVo<SubscribePolicyRule> getById(
            @RequestParam(value = "id", defaultValue = "") String id
    ) {
        SubscribePolicyRule subscribePolicyRule = resourceChangedEventNacosService.getById(id);
        if (subscribePolicyRule != null
                && subscribePolicyRule.getOutput() != null
                && subscribePolicyRule.getOutput().contains("PRODUCT")) {
            subscribePolicyRule.getOutput().remove("PRODUCT");
        }
        if (DataUtil.isNotEmpty(subscribePolicyRule.getPluginId())) {
            Plugin plugin = dataPushPluginService.getDbPlugin(subscribePolicyRule.getPluginId());
            if (plugin != null) {
                subscribePolicyRule.setPluginNames(Arrays.asList(new String[]{plugin.getFileName()}));
            }
        }
        return ResponseVo.ok(subscribePolicyRule);
    }

    /**
     * 根据ID，删除分类
     *
     * @param ids
     * @return
     */
    @RequestMapping(value = "/delete.do", method = RequestMethod.POST)
    @Permission(type = ResourceEnum.ACTION, value = "WRITE")
    public String delete(@RequestParam List<String> ids) throws InterruptedException {
        for (String id : ids) {
            SubscribePolicyRule subscribePolicyRule = resourceChangedEventNacosService.getById(id);
            if (DataUtil.isNotEmpty(subscribePolicyRule.getPluginId())) {
                dataPushPluginService.deleteDataPushPlugin(subscribePolicyRule.getPluginId());
            }
            if (DataUtil.isNotEmpty(subscribePolicyRule.getScheduleName()) && scheduledManagerMap.containsKey(subscribePolicyRule.getScheduleName())) {
                scheduledManagerMap.get(subscribePolicyRule.getScheduleName()).cancel(true);
            }
        }
        resourceChangedEventNacosService.delete(ids);

        return HttpResponseUtil.success();
    }
}
