package com.quanzhi.auditapiv2.web.controller;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.quanzhi.audit_core.common.model.HandlerEventBlackWhiteList;
import com.quanzhi.auditapiv2.common.util.utils.ConstantUtil;
import com.quanzhi.auditapiv2.common.util.utils.DataUtil;
import com.quanzhi.auditapiv2.common.util.utils.HttpResponseUtil;
import com.quanzhi.auditapiv2.core.service.manager.web.IHandlerEventBlackWhiteListService;
import com.quanzhi.auditapiv2.web.common.annotation.OperateLog;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 *
 * 《事件流量过滤Controller》
 *
 *
 * @Project:
 * @Module ID:
 * @Comments:
 * @JDK version used:      <JDK1.8>
 * <AUTHOR> [<EMAIL>]
 * @since 2020-04-08-上午9:56:10
 */
@RestController
@RequestMapping("/api/handlerEventBlackWhiteList")
public class HandlerEventBlackWhiteListController {
    private Logger logger = LoggerFactory.getLogger(HandlerEventBlackWhiteListController.class);

    //事件流量过滤service
    @Autowired
    private IHandlerEventBlackWhiteListService handlerEventBlackWhiteListServiceImpl;

    /**
     * 查询事件流量过滤列表(分页)
     * @Comments:  <对此方法的描述，可以引用系统设计中的描述>
     * <AUTHOR> [<EMAIL>]
     * @since 2020-04-08 9:56
     * @param
     * @return
     */
    @RequestMapping(value = "listData.do", method = RequestMethod.POST)
    @ApiOperation(value = "查询事件流量过滤列表(分页)")
    public String listData() {
        logger.warn("事件流量过滤Controller》查询事件流量过滤列表(分页)");

        List<HandlerEventBlackWhiteList> data = null;
        try {
            data = handlerEventBlackWhiteListServiceImpl.getHandlerEventBlackWhiteListList();
        } catch (Exception e) {
            logger.error("查询事件流量过滤列表(分页)", e);
            return HttpResponseUtil.error(e.getMessage());
        }
        return HttpResponseUtil.success(data);
    }

    /**
     * id查询事件流量过滤详情
     * @Comments:  <对此方法的描述，可以引用系统设计中的描述>
     * <AUTHOR> [<EMAIL>]
     * @since 2020-04-08 9:56
     * @param
     * @return
     */
    @RequestMapping(value = "getById.do", method = RequestMethod.POST)
    @ApiOperation(value = "id查询事件流量过滤详情")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "事件流量过滤id", required = true, paramType = "query", dataType = "String")
    })
    public String getById(String id) {
        logger.warn("事件流量过滤Controller》id查询事件流量过滤详情");

        try {
            if(DataUtil.isNotEmpty(id)){
                HandlerEventBlackWhiteList handlerEventBlackWhiteList = handlerEventBlackWhiteListServiceImpl.getHandlerEventBlackWhiteListById(id);
                return HttpResponseUtil.success(handlerEventBlackWhiteList);
            }else{
                return HttpResponseUtil.error("id不得为空");
            }
        } catch (Exception e) {
            logger.error("id查询事件流量过滤详情", e);
            return HttpResponseUtil.error(e.getMessage());
        }
    }

    /**
     * 新增事件流量过滤
     * @Comments:  <对此方法的描述，可以引用系统设计中的描述>
     * <AUTHOR> [<EMAIL>]
     * @since 2020-04-08 9:56
     * @param
     * @return
     */
    @OperateLog(value="新增事件黑白名单", type = "新增", module = "配置")
    @RequestMapping(value = "add.do", method = RequestMethod.POST)
    @ApiOperation(value = "新增事件流量过滤")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "params", value = "新增内容", required = true, paramType = "query", dataType = "String")
    })
    public String add(@RequestParam(value = "params", defaultValue = "") String params) {
        logger.warn("事件流量过滤Controller》新增事件流量过滤");

        try {
            if(DataUtil.isNotEmpty(params)) {

                HandlerEventBlackWhiteList handlerEventBlackWhiteList = JSONArray.parseObject(params, HandlerEventBlackWhiteList.class);
                if(handlerEventBlackWhiteList != null){

                    //重复数据过滤
                    if(handlerEventBlackWhiteListServiceImpl.isExists(handlerEventBlackWhiteList.getMatchType().name(), handlerEventBlackWhiteList.getBlackWhiteType().name())) {
                        return HttpResponseUtil.error("该类型已存在，无法重复添加");
                    }

                    handlerEventBlackWhiteListServiceImpl.addHandlerEventBlackWhiteList(handlerEventBlackWhiteList);
                }else{
                    return HttpResponseUtil.error(ConstantUtil.PARAMS_ERROR);
                }
            }else {
                return HttpResponseUtil.error("params不得为空");
            }
        } catch (Exception e) {
            logger.error("新增事件流量过滤", e);
            return HttpResponseUtil.error(e.getMessage());
        }
        return HttpResponseUtil.success();
    }

    /**
     * 编辑事件流量过滤
     * @Comments:  <对此方法的描述，可以引用系统设计中的描述>
     * <AUTHOR> [<EMAIL>]
     * @since 2020-04-08 9:56
     * @param
     * @return
     */
    @OperateLog(value = "修改事件黑白名单", type = "编辑", module = "配置")
    @RequestMapping(value = "edit.do", method = RequestMethod.POST)
    @ApiOperation(value = "编辑事件流量过滤")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "params", value = "编辑内容", required = true, paramType = "query", dataType = "String")
    })
    public String edit(@RequestParam(value = "params", defaultValue = "") String params) {
        logger.warn("事件流量过滤Controller》编辑事件流量过滤");

        try {
            if(DataUtil.isNotEmpty(params)) {

                HandlerEventBlackWhiteList handlerEventBlackWhiteList = JSON.parseObject(params, HandlerEventBlackWhiteList.class);
                if(handlerEventBlackWhiteList != null){

                    handlerEventBlackWhiteListServiceImpl.editHandlerEventBlackWhiteList(handlerEventBlackWhiteList);
                }else{
                    return HttpResponseUtil.error(ConstantUtil.PARAMS_ERROR);
                }
            }else {
                return HttpResponseUtil.error("params不得为空");
            }
        } catch (Exception e) {
            logger.error("编辑事件流量过滤", e);
            return HttpResponseUtil.error(e.getMessage());
        }
        return HttpResponseUtil.success();
    }

    /**
     * 批量删除事件流量过滤
     * @Comments:  <对此方法的描述，可以引用系统设计中的描述>
     * <AUTHOR> [<EMAIL>]
     * @since 2020-04-08 9:56
     * @param
     * @return
     */
    @OperateLog(value="删除事件黑白名单", type = "删除", module = "配置")
    @RequestMapping(value = "del.do", method = RequestMethod.POST)
    @ApiOperation(value = "批量删除事件流量过滤")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "ids", value = "事件流量过滤id，逗号分隔", required = true, paramType = "query", dataType = "String")
    })
    public String del(String ids, String appIds) {
        logger.warn("事件流量过滤Controller》批量删除事件流量过滤");

        try {
            if(DataUtil.isNotEmpty(ids)) {
                handlerEventBlackWhiteListServiceImpl.delHandlerEventBlackWhiteList(ids);
            }else {
                return HttpResponseUtil.error("ids不得为空");
            }
        } catch (Exception e) {
            logger.error("批量删除事件流量过滤", e);
            return HttpResponseUtil.error(e.getMessage());
        }
        return HttpResponseUtil.success();
    }
}
