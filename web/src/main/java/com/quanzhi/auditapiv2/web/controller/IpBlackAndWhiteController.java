package com.quanzhi.auditapiv2.web.controller;

import com.quanzhi.audit.mix.permission.annotation.Permission;
import com.quanzhi.audit.mix.permission.domain.enums.ResourceEnum;
import com.quanzhi.auditapiv2.common.dal.enums.IpLimitTypeEnum;
import com.quanzhi.auditapiv2.common.util.utils.HttpResponseUtil;
import com.quanzhi.auditapiv2.core.service.manager.web.IpBlackAndWhiteService;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.Map;


@RestController
@RequestMapping("/ip/blackAndWhite")
@Slf4j
public class IpBlackAndWhiteController {

    @Autowired
    private IpBlackAndWhiteService ipBlackAndWhiteService;

    @RequestMapping(value = "/save",method = RequestMethod.POST)
    @ApiOperation("保存IP黑白名单")
    @Permission(type = ResourceEnum.ACTION, value = "WRITE")
    public String ipBlackAndWhiteSave(@RequestParam String blackIpList,@RequestParam String whiteIpList){
        try {
            ipBlackAndWhiteService.save(blackIpList,whiteIpList);
            return HttpResponseUtil.success();
        }catch (Exception e){
            return HttpResponseUtil.error(e.getMessage());
        }
    }

    @RequestMapping(value = "/query",method = RequestMethod.POST)
    @ApiOperation("查询IP黑白名单")
    public String ipBlackAndWhiteQuery(){
        try {
            Map<String, String> map = ipBlackAndWhiteService.query();
            if(map==null){
                map=new HashMap<>();
                String ipBlack = IpLimitTypeEnum.BLACK.type();
                String ipWhite = IpLimitTypeEnum.WHITE.type();
                map.put(ipBlack,"");
                map.put(ipWhite,"");
            }
            return HttpResponseUtil.success(map);
        }catch (Exception e){
            return HttpResponseUtil.error(e.getMessage());
        }
    }

}
