package com.quanzhi.auditapiv2.web.controller;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.quanzhi.audit_core.common.model.IpPositionConfig;
import com.quanzhi.auditapiv2.common.util.utils.ConstantUtil;
import com.quanzhi.auditapiv2.common.util.utils.DataUtil;
import com.quanzhi.auditapiv2.common.util.utils.HttpResponseUtil;
import com.quanzhi.auditapiv2.core.service.manager.web.IIpPositionConfigService;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 *
 * 《纯真库Controller》
 *
 *
 * @Project:
 * @Module ID:
 * @Comments:
 * @JDK version used:      <JDK1.8>
 * <AUTHOR> [<EMAIL>]
 * @since 2020-04-08-上午9:56:10
 */
@RestController
@RequestMapping("/api/ipPositionConfig")
public class IpPositionConfigController {
    private Logger logger = LoggerFactory.getLogger(IpPositionConfigController.class);

    //纯真库service
    @Autowired
    private IIpPositionConfigService ipPositionConfigServiceImpl;

    /**
     * 查询纯真库列表(分页)
     * @Comments:  <对此方法的描述，可以引用系统设计中的描述>
     * <AUTHOR> [<EMAIL>]
     * @since 2020-04-08 9:56
     * @param
     * @return
     */
    @RequestMapping(value = "listData.do", method = RequestMethod.POST)
    @ApiOperation(value = "查询纯真库列表(分页)")
    public String listData() {
        logger.warn("纯真库Controller》查询纯真库列表(分页)");

        List<IpPositionConfig> data = null;
        try {
            data = ipPositionConfigServiceImpl.getIpPositionConfigList();
        } catch (Exception e) {
            logger.error("查询纯真库列表(分页)", e);
            return HttpResponseUtil.error(e.getMessage());
        }
        return HttpResponseUtil.success(data);
    }

    /**
     * id查询纯真库详情
     * @Comments:  <对此方法的描述，可以引用系统设计中的描述>
     * <AUTHOR> [<EMAIL>]
     * @since 2020-04-08 9:56
     * @param
     * @return
     */
    @RequestMapping(value = "getById.do", method = RequestMethod.POST)
    @ApiOperation(value = "id查询纯真库详情")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "纯真库id", required = true, paramType = "query", dataType = "String")
    })
    public String getById(String id) {
        logger.warn("纯真库Controller》id查询纯真库详情");

        try {
            if(DataUtil.isNotEmpty(id)){
                IpPositionConfig ipPositionConfig = ipPositionConfigServiceImpl.getIpPositionConfigById(id);
                return HttpResponseUtil.success(ipPositionConfig);
            }else{
                return HttpResponseUtil.error("id不得为空");
            }
        } catch (Exception e) {
            logger.error("id查询纯真库详情", e);
            return HttpResponseUtil.error(e.getMessage());
        }
    }

    /**
     * 新增纯真库
     * @Comments:  <对此方法的描述，可以引用系统设计中的描述>
     * <AUTHOR> [<EMAIL>]
     * @since 2020-04-08 9:56
     * @param
     * @return
     */
    @RequestMapping(value = "add.do", method = RequestMethod.POST)
    @ApiOperation(value = "新增纯真库")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "params", value = "新增内容", required = true, paramType = "query", dataType = "String")
    })
    public String add(@RequestParam(value = "params", defaultValue = "") String params) {
        logger.warn("纯真库Controller》新增纯真库");

        try {
            if(DataUtil.isNotEmpty(params)) {

                IpPositionConfig ipPositionConfig = JSONArray.parseObject(params, IpPositionConfig.class);
                if(ipPositionConfig != null){
                    
                    ipPositionConfigServiceImpl.addIpPositionConfig(ipPositionConfig);
                }else{
                    return HttpResponseUtil.error(ConstantUtil.PARAMS_ERROR);
                }
            }else {
                return HttpResponseUtil.error("params不得为空");
            }
        } catch (Exception e) {
            logger.error("新增纯真库", e);
            return HttpResponseUtil.error(e.getMessage());
        }
        return HttpResponseUtil.success();
    }

    /**
     * 编辑纯真库
     * @Comments:  <对此方法的描述，可以引用系统设计中的描述>
     * <AUTHOR> [<EMAIL>]
     * @since 2020-04-08 9:56
     * @param
     * @return
     */
    @RequestMapping(value = "edit.do", method = RequestMethod.POST)
    @ApiOperation(value = "编辑纯真库")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "params", value = "编辑内容", required = true, paramType = "query", dataType = "String")
    })
    public String edit(@RequestParam(value = "params", defaultValue = "") String params) {
        logger.warn("纯真库Controller》编辑纯真库");

        try {
            if(DataUtil.isNotEmpty(params)) {

                IpPositionConfig ipPositionConfig = JSON.parseObject(params , IpPositionConfig.class);
                if(ipPositionConfig != null){

                    ipPositionConfigServiceImpl.editIpPositionConfig(ipPositionConfig);
                }else{
                    return HttpResponseUtil.error(ConstantUtil.PARAMS_ERROR);
                }
            }else {
                return HttpResponseUtil.error("params不得为空");
            }
        } catch (Exception e) {
            logger.error("编辑纯真库", e);
            return HttpResponseUtil.error(e.getMessage());
        }
        return HttpResponseUtil.success();
    }

    /**
     * 批量删除纯真库
     * @Comments:  <对此方法的描述，可以引用系统设计中的描述>
     * <AUTHOR> [<EMAIL>]
     * @since 2020-04-08 9:56
     * @param
     * @return
     */
    @RequestMapping(value = "del.do", method = RequestMethod.POST)
    @ApiOperation(value = "批量删除纯真库")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "ids", value = "纯真库id，逗号分隔", required = true, paramType = "query", dataType = "String")
    })
    public String del(String ids) {
        logger.warn("纯真库Controller》批量删除纯真库");

        try {
            if(DataUtil.isNotEmpty(ids)) {
                ipPositionConfigServiceImpl.delIpPositionConfig(ids);
            }else {
                return HttpResponseUtil.error("ids不得为空");
            }
        } catch (Exception e) {
            logger.error("批量删除纯真库", e);
            return HttpResponseUtil.error(e.getMessage());
        }
        return HttpResponseUtil.success();
    }
}
