package com.quanzhi.auditapiv2.web.controller;


import com.alibaba.fastjson.JSONObject;
import com.quanzhi.audit.mix.permission.annotation.Permission;
import com.quanzhi.audit.mix.permission.domain.enums.ResourceEnum;
import com.quanzhi.auditapiv2.common.dal.dto.NetworkSegmentDto;
import com.quanzhi.auditapiv2.common.util.dto.ResponseVo;
import com.quanzhi.auditapiv2.common.util.utils.DataUtil;
import com.quanzhi.auditapiv2.common.util.utils.HttpResponseUtil;
import com.quanzhi.auditapiv2.core.model.event.NetSegmentChangeEvent;
import com.quanzhi.auditapiv2.core.service.manager.dto.NetworkSegmentCascadeDto;
import com.quanzhi.auditapiv2.core.service.manager.web.INetworkSegmentService;
import com.quanzhi.auditapiv2.core.service.manager.web.IPLibraryFacadeService;
import com.quanzhi.auditapiv2.web.common.annotation.OperateLog;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.OutputStream;
import java.net.URLEncoder;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/api/networkSegment")
public class NetworkSegmentController {

    private Logger logger = LoggerFactory.getLogger(NetworkSegmentController.class);

    private final INetworkSegmentService networkSegmentService;

    private final ApplicationEventPublisher applicationEventPublisher;

    private final IPLibraryFacadeService ipLibraryFacadeService;

    public NetworkSegmentController(INetworkSegmentService networkSegmentService,
                                    ApplicationEventPublisher applicationEventPublisher,
                                    IPLibraryFacadeService ipLibraryFacadeService
    ) {
        this.networkSegmentService = networkSegmentService;
        this.applicationEventPublisher = applicationEventPublisher;
        this.ipLibraryFacadeService = ipLibraryFacadeService;
    }

    /**
     * 分页列表
     *
     * @param page
     * @param limit
     * @return
     */
    @RequestMapping(value = "/page.do", method = RequestMethod.GET)
    public String page(
            @RequestParam(value = "keyword", defaultValue = "", required = false) String keyword,
            @RequestParam(value = "page", defaultValue = "1") Integer page,
            @RequestParam(value = "limit", defaultValue = "100") Integer limit,
            @RequestParam(value = "needInternet", defaultValue = "false", required = false) Boolean needInternet,
            @RequestParam(value = "type", defaultValue = "", required = false) Integer type
    ) {
        return HttpResponseUtil.success(networkSegmentService.page(keyword, page, limit, needInternet, type));
    }

    /**
     * 应用id查询访问域、部署域
     *
     * @param
     * @return
     * @Comments: <对此方法的描述，可以引用系统设计中的描述>
     * <AUTHOR> [<EMAIL>]
     * @since 2020-04-08 9:56
     */
    @RequestMapping(value = "getListByAppId.do", method = RequestMethod.POST)
    @ApiOperation(value = "应用id查询访问域、部署域")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "appId", value = "应用id", required = true, paramType = "query", dataType = "String")
    })
    public String getListByAppId(String appId) {
        try {
            if (DataUtil.isNotEmpty(appId)) {
                Map<String, List<NetworkSegmentDto>> map = networkSegmentService.getNetworkSegmentListByAppId(appId);
                return HttpResponseUtil.success(map);
            } else {
                return HttpResponseUtil.error("appId不得为空");
            }
        } catch (Exception e) {
            logger.error("host query ip accessDomain and deployDomain error:", e);
            return HttpResponseUtil.error(e.getMessage());
        }
    }

    /**
     * 接口id查询访问域、部署域
     *
     * @param
     * @return
     * @Comments: <对此方法的描述，可以引用系统设计中的描述>
     * <AUTHOR> [<EMAIL>]
     * @since 2020-04-08 9:56
     */
    @RequestMapping(value = "getListByApiId.do", method = RequestMethod.POST)
    @ApiOperation(value = "接口id查询访问域、部署域")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "apiId", value = "接口id", required = true, paramType = "query", dataType = "String")
    })
    public String getListByApiId(String apiId) {
        try {
            if (DataUtil.isNotEmpty(apiId)) {
                Map<String, List<NetworkSegmentDto>> map = networkSegmentService.getNetworkSegmentListByApiId(apiId);
                return HttpResponseUtil.success(map);
            } else {
                return HttpResponseUtil.error("apiId不得为空");
            }
        } catch (Exception e) {
            logger.error("api id query ip accessDomain and deployDomain error:", e);
            return HttpResponseUtil.error(e.getMessage());
        }
    }

    @RequestMapping(value = "/getAll.do", method = RequestMethod.GET)
    public String page() {
        return HttpResponseUtil.success(networkSegmentService.getAll());
    }


    @RequestMapping(value = "/getIpsNetwork.do", method = RequestMethod.GET)
    public String getIpsNetwork(
            @RequestParam(value = "ips", defaultValue = "") List<String> ips
    ) {
        Map<String, String> ipsNetwork = networkSegmentService.getIpsNetwork(ips);
        return HttpResponseUtil.success(ipsNetwork);
    }


    @OperateLog(module = "网段配置", type = "编辑", value = "编辑网段")
    @RequestMapping(value = "/save.do", method = RequestMethod.POST)
    @ApiOperation(value = "新增/修改网段")
    @Permission(type = ResourceEnum.ACTION, value = "WRITE")
    public String save(@RequestParam(value = "params", defaultValue = "") String params,
                       @RequestBody NetworkSegmentDto networkSegmentDto) {
        try {
            if (DataUtil.isEmpty(networkSegmentDto) && DataUtil.isNotEmpty(params)) {
                networkSegmentDto = JSONObject.parseObject(params, NetworkSegmentDto.class);
            }
            networkSegmentService.save(networkSegmentDto);
            List<String> ids = new ArrayList<>();
            ids.add("threatViewMenu");
            ids.add("appViewMenu");
            // 异步计算视图数据
            applicationEventPublisher.publishEvent(new NetSegmentChangeEvent(ids));
            return HttpResponseUtil.success();
        } catch (Exception e) {
            return HttpResponseUtil.error(e.getMessage());
        }
    }

    @OperateLog(module = "网段配置", type = "导出", value = "导出网段配置")
    @RequestMapping(value = "exportExcel.do", method = RequestMethod.GET)
    @Permission(type = ResourceEnum.ACTION, value = "WRITE")
    public void exportExcel(HttpServletResponse response) throws IOException {
        response.setContentType("application/octet-stream;charset=UTF-8");
        String fileName = URLEncoder.encode("导出网段划分.xls", "UTF-8");
        response.setHeader("Content-Disposition", "attachment;filename=" + fileName);
        response.addHeader("Pargam", "no-cache");
        response.addHeader("Cache-Control", "no-cache");
        OutputStream os = response.getOutputStream();
        HSSFWorkbook hssfWorkbook = networkSegmentService.exportNetworkExcel();
        hssfWorkbook.write(os);
        os.flush();
        os.close();
    }

    @GetMapping(value = "downloadTemplate")
    @Permission(type = ResourceEnum.ACTION, value = "WRITE")
    public void downloadTemplate(HttpServletResponse response) throws IOException {
        networkSegmentService.downloadTemplate(response);
    }

    @OperateLog(module = "网段配置", type = "编辑", value = "导入网段配置")
    @RequestMapping(value = "/uploadExcel.do", method = RequestMethod.POST)
    @Permission(type = ResourceEnum.ACTION, value = "WRITE")
    public String uploadExcel(@RequestParam(value = "multipartFile", defaultValue = "") MultipartFile multipartFile,
                              HttpServletRequest request, HttpServletResponse response) {
        try {
            List<String> ids = new ArrayList<>();
            ids.add("threatViewMenu");
            ids.add("appViewMenu");
            applicationEventPublisher.publishEvent(new NetSegmentChangeEvent(ids));
            return networkSegmentService.replaceNetworkSegment(multipartFile);
        } catch (Exception e) {
            logger.error("Excel parse error:", e);
            return HttpResponseUtil.error("上传失败");
        }
    }

    @GetMapping(value = "getNetworkSegmentCascade")
    @ApiOperation(value = "获取网段级联关系")
    public ResponseVo<List<NetworkSegmentCascadeDto>> getNetworkSegmentCascade() {
        return ResponseVo.ok(networkSegmentService.getNetworkSegmentCascade());
    }


    @RequestMapping(value = "/getListById.do", method = RequestMethod.GET)
    @ApiOperation(value = "查看网段配置")
    public String getListById(@RequestParam() String id) {
        return HttpResponseUtil.success(networkSegmentService.getListById(id));
    }

    @OperateLog(module = "网段配置",type = "删除",value = "删除网段")
    @RequestMapping(value = "/delById.do", method = RequestMethod.POST)
    @ApiOperation(value = "删除网段配置")
    @Permission(type = ResourceEnum.ACTION, value = "WRITE")
    public String delById(@RequestParam() String id) {
        networkSegmentService.delById(id);
        return HttpResponseUtil.success();
    }

    @OperateLog(module = "IP地址库", type = "导入", value = "上传文件更新IP地址库")
    @ApiOperation("导入IP离线库")
    @RequestMapping(value = "/uploadIpOffline", method = RequestMethod.POST)
    @Permission(type = ResourceEnum.ACTION, value = "WRITE")
    public String uploadIpOffline(@RequestParam(value = "multipartFile", defaultValue = "") MultipartFile multipartFile) {
        try {
            logger.info("upload ip db");
            ipLibraryFacadeService.upload(multipartFile);
            logger.info("upload ip db success");
            return HttpResponseUtil.success();
        } catch (Exception e) {
            logger.error("upload ip offline error", e);
            return HttpResponseUtil.error("导入错误: " + e.getMessage());
        }
    }
}
