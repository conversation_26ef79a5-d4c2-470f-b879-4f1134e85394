package com.quanzhi.auditapiv2.web.controller;

import com.quanzhi.audit.mix.permission.annotation.Permission;
import com.quanzhi.audit.mix.permission.domain.enums.ResourceEnum;
import com.quanzhi.auditapiv2.common.dal.entity.ComponentInfo;
import com.quanzhi.auditapiv2.common.dal.entity.SystemResourceInfo;
import com.quanzhi.auditapiv2.common.dal.entity.TrafficInfo;
import com.quanzhi.auditapiv2.common.util.utils.HttpResponseUtil;
import com.quanzhi.auditapiv2.core.service.manager.web.ISystemStatusService;
import com.quanzhi.auditapiv2.web.common.annotation.OperateLog;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@Slf4j
@RestController
@RequestMapping("/api/systemStatus")
public class SystemStatusController {
    private final ISystemStatusService systemStatusService;

    public SystemStatusController(ISystemStatusService systemStatusService) {
        this.systemStatusService = systemStatusService;
    }

    @OperateLog(module = "系统监控", type = "查询", value = "系统资源检测")
    @GetMapping(value = "/systemMonitor.do")
    @ApiOperation(value = "获取系统资源使用率")
    public String systemMonitor() {
        log.warn("系统状态Controller》查询系统资源");

        try {
            List<SystemResourceInfo> resourceInfos = systemStatusService.systemMonitor();
            return HttpResponseUtil.success(resourceInfos);
        } catch (Exception e) {
            log.error("获取系统资源失败", e);
            return HttpResponseUtil.error(e.getMessage());
        }
    }

    @OperateLog(module = "系统监控", type = "查询", value = "组件检测")
    @GetMapping(value = "/componentMonitor.do")
    @ApiOperation(value = "获取组件状态信息")
    public String componentMonitor() {
        log.warn("系统状态Controller》查询组件状态信息");

        try {
            List<ComponentInfo> componentInfos = systemStatusService.componentMonitor();
            return HttpResponseUtil.success(componentInfos);
        } catch (Exception e) {
            log.error("获取组件状态信息失败", e);
            return HttpResponseUtil.error(e.getMessage());
        }
    }

    @OperateLog(module = "流量检测", type = "查询", value = "流量信息检测")
    @GetMapping(value = "/trafficMonitor.do")
    @ApiOperation(value = "获取流量使用信息")
    public String trafficMonitor() {
        log.warn("系统状态Controller》查询流量使用信息");

        try {
            // 假设存在一个方法来获取流量信息
            TrafficInfo trafficInfo = systemStatusService.trafficMonitor();
            return HttpResponseUtil.success(trafficInfo);
        } catch (Exception e) {
            log.error("获取流量使用信息失败", e);
            return HttpResponseUtil.error(e.getMessage());
        }
    }

    @OperateLog(module = "其他检测", type = "查询", value = "其他检测")
    @GetMapping(value = "/otherMonitor.do")
    @ApiOperation(value = "其他检测")
    public String otherMonitor() {
        log.warn("系统状态Controller》其他检测");

        try {
            List<String> list = systemStatusService.otherMonitor();
            return HttpResponseUtil.success(list);
        } catch (Exception e) {
            log.error("其他检测失败", e);
            return HttpResponseUtil.error(e.getMessage());
        }
    }

    @OperateLog(module = "系统监控", type = "操作", value = "重启组件")
    @GetMapping(value = "/restartComponent.do")
    @ApiOperation(value = "重启组件")
    @Permission(type = ResourceEnum.ACTION, value = "WRITE")
    public String restartComponent( @RequestParam("componentName") String componentName) {
        log.warn("系统状态Controller》重启组件， 组件名称: {}",  componentName);

        try {
            boolean result = systemStatusService.restartComponent(componentName);
            if (result) {
                return HttpResponseUtil.success("重启成功");
            } else {
                return HttpResponseUtil.error("重启失败");
            }
        } catch (Exception e) {
            log.error("重启组件失败", e);
            return HttpResponseUtil.error("重启失败: " + e.getMessage());
        }
    }

//    @OperateLog(module = "系统维护", type = "操作", value = "清理文件")
//    @GetMapping(value = "/cleanFile.do")
//    @ApiOperation(value = "清理指定文件")
//    public String cleanFile(@RequestParam("filePath") String filePath) {
//        log.warn("系统状态Controller》清理文件，文件路径: {}", filePath);
//
//        try {
//            boolean result = systemStatusService.cleanFile(filePath);
//            if (result) {
//                return HttpResponseUtil.success("清理成功");
//            } else {
//                return HttpResponseUtil.error("清理失败");
//            }
//        } catch (Exception e) {
//            log.error("清理文件失败", e);
//            return HttpResponseUtil.error("清理失败: " + e.getMessage());
//        }
//    }

    @OperateLog(module = "高级运维", type = "操作", value = "高级运维操作")
    @GetMapping(value = "/advancedOperation.do")
    @ApiOperation(value = "执行高级运维操作")
    @Permission(type = ResourceEnum.ACTION, value = "WRITE")
    public String advancedOperation(
            @RequestParam("operationMode") String operationMode,
            @RequestParam("componentName") String componentName,
            @RequestParam("componentIp") String componentIp,
            @RequestParam("specificOperation") String specificOperation
    ) {
        log.warn("系统状态Controller》高级运维操作，操作方式: {}, 组件名称: {}, 组件IP: {}, 具体操作内容: {}",
                operationMode, componentName, componentIp, specificOperation);

        try {
            Object result = systemStatusService.advancedOperation(operationMode, componentName, componentIp, specificOperation);
            return HttpResponseUtil.success(result);
        } catch (Exception e) {
            log.error("高级运维操作失败", e);
            return HttpResponseUtil.error(e.getMessage());
        }


    }
}
