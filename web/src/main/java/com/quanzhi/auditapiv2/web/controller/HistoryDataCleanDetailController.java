package com.quanzhi.auditapiv2.web.controller;

import com.quanzhi.audit_core.common.model.HistoryDataCleanTaskDetail;
import com.quanzhi.auditapiv2.common.util.dto.ListOutputDto;
import com.quanzhi.auditapiv2.common.util.utils.HttpResponseUtil;
import com.quanzhi.auditapiv2.core.service.manager.web.IHistoryDataCleanDetailService;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/7/21 上午10:32
 */

@RestController
@RequestMapping("/api/historyDataCleanDetail")
public class HistoryDataCleanDetailController {

    @Autowired
    private IHistoryDataCleanDetailService historyDataCleanDetailService;

    private List<String> dataTypeList = new ArrayList<>(Arrays.asList( "HTTPAPI","HTTPAPP","HTTPSAMPLE"));

    @ApiOperation(value = "获取历史数据预清理任务详情列表")
    @RequestMapping(value = "/getHistoryDataDetailList.do",method = RequestMethod.POST)
    @ApiImplicitParams({
            @ApiImplicitParam(name = "page",
                    value = "page",
                    required = true, paramType = "query", dataType = "int", example = "1"),
            @ApiImplicitParam(name = "limit",
                    value = "limit",
                    required = true, paramType = "query", dataType = "int", example = "10"),
            @ApiImplicitParam(name = "dataType",
                    value = "dataType",
                    required = true, paramType = "query", dataType = "String", example = "HTTPAPI、HTTPAPP、HTTPSAMPLE"),

            @ApiImplicitParam(name = "taskId",
                    value = "taskId",
                    required = true, paramType = "query", dataType = "String", example = ""),
    })
    public String getHistoryDataDetailList (
            @RequestParam Integer page,
            @RequestParam Integer limit,
            @RequestParam String dataType,
            @RequestParam String taskId
    ) {

        if( !dataTypeList.contains( dataType )) {
            return HttpResponseUtil.error("请输入合法的dataType,比如HTTPAPI、HTTPAPP、HTTPSAMPLE");
        }
        ListOutputDto<HistoryDataCleanTaskDetail>  historyDataDetailList =  historyDataCleanDetailService.getHistoryDataDetailList(page,limit,dataType,taskId);
        return HttpResponseUtil.success(historyDataDetailList);
    }

}
