package com.quanzhi.auditapiv2.web.controller;

import com.alibaba.nacos.api.config.annotation.NacosValue;
import com.quanzhi.audit.mix.permission.annotation.Permission;
import com.quanzhi.audit.mix.permission.domain.enums.ResourceEnum;
import com.quanzhi.auditapiv2.common.dal.entity.NotifyConfig;
import com.quanzhi.auditapiv2.common.util.dto.ResponseVo;
import com.quanzhi.auditapiv2.common.util.utils.DataUtil;
import com.quanzhi.auditapiv2.common.util.utils.HttpResponseUtil;
import com.quanzhi.auditapiv2.core.service.SysLogService;
import com.quanzhi.auditapiv2.core.service.manager.web.INotifyConfigService;
import com.quanzhi.auditapiv2.web.common.annotation.OperateLog;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * 《通知配置Controller》
 *
 * <AUTHOR> [<EMAIL>]
 * @Project:
 * @Module ID:
 * @Comments:
 * @JDK version used:      <JDK1.8>
 * @since 2020-04-08-上午9:56:10
 */
@RestController
@RequestMapping("/api/notifyConfig")
public class NotifyConfigController {
    private Logger logger = LoggerFactory.getLogger(NotifyConfigController.class);

    //通知配置service
    @Autowired
    private INotifyConfigService notifyConfigServiceImpl;

    @NacosValue(value = "${configLogModule:配置}", autoRefreshed = true)
    private String configLogModule;

    @Resource
    private SysLogService sysLogService;

    /**
     * 查询通知配置列表
     *
     * @param
     * @return
     * @Comments: <对此方法的描述，可以引用系统设计中的描述>
     * <AUTHOR> [<EMAIL>]
     * @since 2020-04-08 9:56
     */
    @RequestMapping(value = "listData.do", method = RequestMethod.POST)
    @ApiOperation(value = "查询通知配置列表")
    public ResponseVo<List<NotifyConfig>> listData() {
        logger.warn("通知配置Controller》查询通知配置列表");

        List<NotifyConfig> data = null;
        try {
            data = notifyConfigServiceImpl.getNotifyConfigList();
            data.stream().forEach(notifyConfig -> {
                if ("GATEWAY_LICENSE_EXPIRE".equals(notifyConfig.getCode()) || "SYSTEM_LICENSE_EXPIRE".equals(notifyConfig.getCode())) {
                    notifyConfig.setName(notifyConfig.getName() + "到期");
                } else if ("KAFKA_LAG_OVERLOAD".equals(notifyConfig.getCode())) {
                    notifyConfig.setName(notifyConfig.getName() + "条");
                }
            });
        } catch (Exception e) {
            logger.error("查询通知配置列表", e);
            return ResponseVo.error(e.getMessage(), 233);
        }
        return ResponseVo.ok(data);
    }

    /**
     * id查询通知配置详情
     *
     * @param
     * @return
     * @Comments: <对此方法的描述，可以引用系统设计中的描述>
     * <AUTHOR> [<EMAIL>]
     * @since 2020-04-08 9:56
     */
    @RequestMapping(value = "getById.do", method = RequestMethod.POST)
    @ApiOperation(value = "id查询通知配置详情")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "通知配置id", required = true, paramType = "query", dataType = "String")
    })
    public String getById(String id) {
        logger.warn("通知配置Controller》id查询通知配置详情");

        try {
            if (DataUtil.isNotEmpty(id)) {
                NotifyConfig notifyConfig = notifyConfigServiceImpl.getNotifyConfigById(id);
                return HttpResponseUtil.success(notifyConfig);
            } else {
                return HttpResponseUtil.error("id不得为空");
            }
        } catch (Exception e) {
            logger.error("id查询通知配置详情", e);
            return HttpResponseUtil.error(e.getMessage());
        }
    }

    /**
     * 编辑通知配置
     *
     * @param
     * @return
     * @Comments: <对此方法的描述，可以引用系统设计中的描述>
     * <AUTHOR> [<EMAIL>]
     * @since 2020-04-08 9:56
     */
    @OperateLog(module = "通知管理", type = "编辑", value = "编辑通知管理")
    @RequestMapping(value = "edit.do", method = RequestMethod.POST)
    @ApiOperation(value = "编辑通知配置")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "通知配置id", required = true, paramType = "query", dataType = "String"),
            @ApiImplicitParam(name = "methods", value = "通知方式，逗号分隔", required = false, paramType = "query", dataType = "String"),
            @ApiImplicitParam(name = "isSend", value = "通知状态", required = false, paramType = "query", dataType = "boolean"),
            @ApiImplicitParam(name = "title", value = "通知名称", required = false, paramType = "query", dataType = "String"),
            @ApiImplicitParam(name = "value", value = "通知名称数值", required = false, paramType = "query", dataType = "String")

    })
    @Permission(type = ResourceEnum.ACTION, value = "WRITE")
    public String edit(String id, String methods, Boolean isSend, String emailGroup,String webhookConfigId, String title, String value, String unit) {
        logger.warn("通知配置Controller》编辑通知配置");

        try {
            if (DataUtil.isNotEmpty(id)) {
                NotifyConfig notifyConfig = notifyConfigServiceImpl.getNotifyConfigById(id);
                if (notifyConfig.getIsSend().equals(isSend)) {
                    sysLogService.insertLog("修改" + notifyConfig.getName() + "系统订阅", configLogModule,"编辑");
                } else {
                    if (isSend) {
                        sysLogService.insertLog("启用" + notifyConfig.getName() + "系统订阅", configLogModule,"处理");
                    } else {
                        sysLogService.insertLog("停用" + notifyConfig.getName() + "系统订阅", configLogModule,"处理");
                    }
                }
                notifyConfigServiceImpl.editNotifyConfig(id, methods, isSend, emailGroup,webhookConfigId, title, value, unit);
            } else {
                return HttpResponseUtil.error("id不得为空");
            }
        } catch (Exception e) {
            logger.error("编辑通知配置", e);
            return HttpResponseUtil.error(e.getMessage());
        }
        return HttpResponseUtil.success();
    }
}
