package com.quanzhi.auditapiv2.web.interceptor;

import com.alibaba.nacos.api.config.annotation.NacosValue;
import com.quanzhi.auditapiv2.common.dal.dataobject.SysUser;
import com.quanzhi.auditapiv2.common.dal.enums.ProductTypeEnum;
import com.quanzhi.auditapiv2.common.util.utils.DataUtil;
import com.quanzhi.auditapiv2.common.util.utils.HttpResponseUtil;
import com.quanzhi.auditapiv2.common.util.utils.MD5Util;
import com.quanzhi.auditapiv2.core.model.SysUserStatus;
import com.quanzhi.auditapiv2.core.service.SysUserService;
import com.quanzhi.auditapiv2.web.controller.LoginController;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.handler.HandlerInterceptorAdapter;

import javax.servlet.http.Cookie;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import java.time.Instant;
import java.util.Date;

import static com.quanzhi.auditapiv2.core.service.sso.SsoServiceImpl.SSO_TJDX_SESSION_KEY;
import static com.quanzhi.auditapiv2.core.service.manager.web.impl.LoginServiceImpl.USER_JSE;

@Slf4j
@Component
public class UserInterceptor extends HandlerInterceptorAdapter {

    @NacosValue(value = "${server.servlet.session.timeout:30}",autoRefreshed = true)
    private Integer sessionTimeout;

    @NacosValue(value = "${login.error.count:5}",autoRefreshed = true)
    private Integer login_error_count;

    @NacosValue(value = "${login.interceptor.enable:false}", autoRefreshed = true)
    private Boolean enableSSO;

    @NacosValue(value = "${yhsgj.login.interceptor.enable:false}", autoRefreshed = true)
    private Boolean yhsgjEnableSSO;

    @NacosValue(value = "${product.type:api}", autoRefreshed = true)
    private String productType;

    @Value(value = "${server.servlet.session.cookie.name:JSESSIONID}")
    private String cookieName;

    //开启openAPI配置
    @NacosValue(value = "${openapi.header.username:openapi}",  autoRefreshed = true)
    private String username;

    @NacosValue(value = "${openapi.header.accessKey:606fb87538003b5af575ff2068052e5ca60487f2fd83e4bbfc6147433ecbad519320105fe03ad1489be56ccc786cd5b5}", autoRefreshed = true)
    private String accessKey;

    @NacosValue(value = "${api.openapi:false}", autoRefreshed = true)
    private Boolean openapi;

    @Autowired
    private SysUserService sysUserService;

    private static final String AUTH_TOKEN = "X-API-Key";

    private static final String TOKEN_VALUE = "OPEN_API";

    private static final String OPEN_API_USERNAME = "username";
    private static final String OPEN_API_ACCESS_KEY = "accessKey";



    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler)throws IOException {
        if (productType.equals(ProductTypeEnum.tjdx.name())){
            Object authInfo = request.getSession().getAttribute(SSO_TJDX_SESSION_KEY);
            if (DataUtil.isNotEmpty(authInfo)){
                return true;
            }
            if (DataUtil.isNotEmpty(request.getHeader(AUTH_TOKEN))){
                return true;
            }
        }
        if (enableSSO || yhsgjEnableSSO) {
            return true;
        }

        // open-api 调用，不要拦了，再拦翻脸
        if ( request.getHeader(AUTH_TOKEN) != null
                && request.getHeader(AUTH_TOKEN).equals(TOKEN_VALUE) ) {
            return true;
        }

        if (openapi && request.getHeader(OPEN_API_USERNAME) != null && request.getHeader(OPEN_API_USERNAME).equals(username)) {
            //request请求校验转换
            return  getRequest(request,response);

        }
        //一次请求，一次校验，否则可能会出错
        synchronized (this) {
            String userName = "";
            String jse = "";
            Cookie[] cookies = request.getCookies();
            if (DataUtil.isNotEmpty(cookies)) {
                for (Cookie cookie : cookies) {
                    if (cookie.getName().equals(cookieName)) {
                        jse = cookie.getValue();
                    }
                    if (cookie.getName().equals("QZ_USERNAME")) {
                        try {
                            userName = URLDecoder.decode(cookie.getValue(), "UTF-8");
                        } catch (UnsupportedEncodingException e) {
                            log.error("decode error", e);
                            userName = cookie.getValue();
                        }
                    }
                }
            }
           // 让浏览器用utf8来解析返回的数据
            response.setHeader("Content-type", "text/html;charset=UTF-8");
            // 告诉servlet用UTF-8转码，而不是用默认的ISO8859
            response.setCharacterEncoding("UTF-8");
            if (userName.startsWith(SysUser.SSO_USER_PREFIX)){
                return true;
            }
            if(DataUtil.isNotEmpty(userName)){
                SysUser sysUser = sysUserService.getByUserName(URLDecoder.decode(userName, "UTF-8"));
                if(DataUtil.isNotEmpty(sysUser)){
                    if (DataUtil.isNotEmpty(sysUser.getLockTime())) {
                        long lockTime = sysUser.getLockTime();
                        long currentTime = System.currentTimeMillis();
                        if (lockTime > currentTime && sysUser.getErrorCount() != null && sysUser.getErrorCount() >= login_error_count) {
                            response.getWriter().write(HttpResponseUtil.error(403,"当前账号已被锁定，请联系管理员进行解锁!"));
                            return false;
                        }
                    }
                    if(DataUtil.isNotEmpty(sysUser.getStatus()) && sysUser.getStatus() == SysUser.StatusEnum.DISABLED.value()){
                        response.getWriter().write(HttpResponseUtil.error(404,"该账号已被禁用！"));
                        return false;
                    }
                }
            }

//            SysUserStatus userStatus = USER_JSE.get(userName);
//            if (DataUtil.isNotEmpty(userStatus) && DataUtil.isNotEmpty(userStatus.getJsessionId()) && DataUtil.isNotEmpty(jse) && !userStatus.getJsessionId().equals(jse) && userStatus.getOnlyOneLogin()){
//                userStatus.setOnlyOneLogin(false);
//                response.getWriter().write(HttpResponseUtil.error(402,"该账号已在其他终端登录！"));
//                return false;
//            }
/*            if (DataUtil.isNotEmpty(userStatus) && DataUtil.isNotEmpty(userStatus.getLatestTime()) && (DateUtil.currentTimestamp()- userStatus.getLatestTime())>sessionTimeout*60*1000L){
                response.getWriter().write(HttpResponseUtil.error(401,"登录超时，请重新登录"));
                userStatus.setLoginStatus("离线");
                return false;
            }*/
            return true;
           /* if (DataUtil.isNotEmpty(userStatus) && DataUtil.isNotEmpty(userStatus.getLoginStatus())){
                userStatus.setLatestTime(DateUtil.currentTimestamp());
                return true;
            }
            response.getWriter().write(HttpResponseUtil.error(402,"该账号未登录！"));
            return false;*/
        }
    }

    private boolean getRequest(HttpServletRequest request, HttpServletResponse response) throws IOException {
        Date date = new Date(Long.valueOf(request.getHeader("dates")));
        String usernameHeader = request.getHeader("username");
        String accessKeyHeader = request.getHeader(OPEN_API_ACCESS_KEY);

        if (DataUtil.isEmpty(date) || DataUtil.isEmpty(accessKeyHeader) || DataUtil.isEmpty(usernameHeader)) {
            response.getWriter().write(HttpResponseUtil.error(401,"Permission denied,invalid signature"));
            return false;
        }

        //解析accesskey进行校验

        String md5 = MD5Util.md5(username + accessKey + date.getTime());

        log.info("加密后的md5", md5);
        if (!md5.equals(accessKeyHeader)) {
            response.getWriter().write(HttpResponseUtil.error(401,"当前accessKey加密失败！"));
            return false;
        }

        //增加一个cookie信息
        if (DataUtil.isNotEmpty(usernameHeader)) {
            SysUser sysUser = sysUserService.getByUserName(URLDecoder.decode(usernameHeader, "UTF-8"));
            if (DataUtil.isEmpty(sysUser)) {
                response.getWriter().write(HttpResponseUtil.error(401,"当前openAPI用户不存在"));
                return false;
            }
        }
        if(openapi){
            //签名超过15分钟失效
            Long dateTime = Instant.now().toEpochMilli();
            if (dateTime - date.getTime() > 900000) {
                response.getWriter().write(HttpResponseUtil.error(401,"Permission denied,authentication timed out, signature has expired！"));
                return false;
            }
        }
        return true;
//        TreeMap<String, String> map = new TreeMap<String, String>();
//        map.put("md5", md5);
//        Map<String, String> header = AWSV4Util.auth("POST", map, date, accessKey, openApiKey.getKey());
//        if (!xAmzDate.equals(header.get("x-amz-date")) || !authorization.equals(header.get("Authorization"))) {
//            response.getWriter().write(HttpResponseUtil.error(401,"Permission denied,signature verification failed！"));
//
//        }
    }
}
