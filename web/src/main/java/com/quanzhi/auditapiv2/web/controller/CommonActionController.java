package com.quanzhi.auditapiv2.web.controller;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.quanzhi.audit.mix.permission.domain.Condition;
import com.quanzhi.audit.mix.permission.domain.enums.OwnerEnum;
import com.quanzhi.audit.mix.permission.domain.enums.ResourceEnum;
import com.quanzhi.audit.mix.permission.service.PermissionSupport;
import com.quanzhi.audit_core.common.config.annotation.DynamicValue;
import com.quanzhi.audit_core.common.model.AssetLifeStateConfig;
import com.quanzhi.audit_core.common.model.NetworkDomain;
import com.quanzhi.audit_core.common.model.NetworkSegment;
import com.quanzhi.audit_core.common.model.Position;
import com.quanzhi.audit_core.resource.fetcher.client.fetcher.IpNetworkDomainFetcher;
import com.quanzhi.audit_core.resource.fetcher.client.resolve.convert.NetworkSegmentConverter;
import com.quanzhi.audit_core.resource.fetcher.client.resolve.model.NetworkSegmentIpInfo;
import com.quanzhi.auditapiv2.biz.risk.service.ThreatIpService;
import com.quanzhi.auditapiv2.common.dal.dao.IHttpAppDao;
import com.quanzhi.auditapiv2.common.dal.dao.convert.AssetLifeStateConfigConvert;
import com.quanzhi.auditapiv2.common.dal.dao.impl.common.DataPermissionUtil;
import com.quanzhi.auditapiv2.common.dal.dto.group.DataPermissionTypeEnum;
import com.quanzhi.auditapiv2.common.dal.dto.query.HttpAppCriteriaDto;
import com.quanzhi.auditapiv2.common.dal.entity.ReportConfig;
import com.quanzhi.auditapiv2.common.dal.entity.node.ClusterType;
import com.quanzhi.auditapiv2.common.util.utils.*;
import com.quanzhi.auditapiv2.common.util.utils.request.HttpServletRequestUtil;
import com.quanzhi.auditapiv2.core.model.SubjectType;
import com.quanzhi.auditapiv2.core.model.UaType;
import com.quanzhi.auditapiv2.core.model.event.ActionEvent;
import com.quanzhi.auditapiv2.core.service.SysLogService;
import com.quanzhi.auditapiv2.core.service.manager.web.ICommonActionService;
import com.quanzhi.auditapiv2.core.service.manager.web.INetworkSegmentService;
import com.quanzhi.auditapiv2.core.service.manager.web.IRiskLevelService;
import com.quanzhi.auditapiv2.core.service.manager.web.IUserDepartService;
import com.quanzhi.auditapiv2.core.service.manager.web.common.CommonActionHandler;
import com.quanzhi.auditapiv2.core.service.manager.web.common.DelegateCommonAction2Handlers;
import com.quanzhi.auditapiv2.core.service.node.cluster.ClusterNodeService;
import com.quanzhi.auditapiv2.core.trace.enums.TaskStatus;
import com.quanzhi.auditapiv2.web.dto.web.PriorityOfSearch;
import com.quanzhi.metabase.core.model.MetabaseURI;
import com.quanzhi.metabase.core.model.dto.RiskLevelMatchDto;
import com.quanzhi.metabase.core.model.http.HttpAppResource;
import com.quanzhi.metabase.core.model.http.constant.AssetLifeStateEnum;
import com.quanzhi.operate.atomDefinition.ActionConfig;
import com.quanzhi.operate.atomDefinition.ActionConfigFrontTransform;
import com.quanzhi.operate.atomDefinition.ActionFrontTransformPath;
import com.quanzhi.operate.atomDefinition.RepositoryOperate;
import com.quanzhi.operate.operateHandler.IOperateHandlerService;
import com.quanzhi.operate.query.Criteria;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.CharEncoding;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.net.URLDecoder;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2020/10/22 下午4:03
 */

@RestController
@RequestMapping("/api/commonAction")
@Slf4j
public class CommonActionController {

    @Autowired
    protected IOperateHandlerService operateHandlerService;

    @Resource
    protected SysLogService sysLogService;

    @Autowired
    private ICommonActionService commonActionService;

    @Autowired
    protected ApplicationEventPublisher eventPublisher;

    @Autowired
    private IRiskLevelService riskLevelService;

    @Autowired
    private ThreatIpService threatIpService;

    @Autowired
    private INetworkSegmentService networkSegmentService;

    @Autowired
    private IUserDepartService userDepartService;

    @Autowired
    private AssetLifeStateConfigConvert assetLifeStateConfigConvert;

    @Autowired
    private IHttpAppDao httpAppDao;

    @Autowired
    private CommonActionHandler commonActionHandler;

    private List<String> actionIds = Arrays.asList("AUDIT_V2_2.1_FIGURE_VISIT_TENDENCY_CHART",
            "APP_AUDIT_2.1_IP_LIST_ACCESSDOMAIN_GROUP_LIST",
            "APP_AUDIT_2.1_IP_LIST_CITY_GROUP_LIST",
            "APP_AUDIT_2.1_IP_LIST_BLOCKFLAG_GROUP_LIST"
    );

    @DynamicValue(dataId = "common.searchPriority.json", groupId = "common", typeClz = PriorityOfSearch.class)
    private PriorityOfSearch priorityOfSearch;

    @DynamicValue(dataId = "common.uaType.json", groupId = "common", typeClz = UaType.class)
    private UaType uaType;

    private Map<String, String> uaTypeMap;
    private Map<String, String> networkSegmentMap;

    @DynamicValue(dataId = "common.networksegment.json", groupId = "common", typeClz = NetworkSegment.class)
    private List<NetworkSegment> networkSegments;

    @Autowired
    private DelegateCommonAction2Handlers delegateCommonAction2Handlers;

    @Autowired
    private DataPermissionUtil dataPermissionUtil;

    @PostMapping("/getActionByConfig.do")
    public String getActionByConfig(String actionConfig, HttpServletRequest request) {
        ActionConfig actionConfigModel = null;
        try {
            String aesDecrypt = AESUtil.aesDecrypt(actionConfig);
            String actionConfigText = URLDecoder.decode(aesDecrypt, CharEncoding.UTF_8);
            actionConfigModel = JSONObject.parseObject(actionConfigText,ActionConfig.class);
            sysLogService.insertCommonApiLog(actionConfigModel.getId());
        } catch (Exception e) {
            log.error("log write error", e);
        }
        delegateCommonAction2Handlers.beforeHandle(actionConfigModel);
        this.convertQuery(actionConfigModel);
        fillPermissionQuery(actionConfigModel,null);
        Map<String, Object> objectMap = CommonActionCheckUtil.checkIP(actionConfigModel);
        if (objectMap != null  && !objectMap.isEmpty()) {
            if(objectMap.get("errorMsg") != null){
                return HttpResponseUtil.error(400, objectMap.get("errorMsg").toString());
            }else if(objectMap.get("emptyValue") != null){
                return HttpResponseUtil.success(objectMap.get("emptyValue"));
            }
        }
        Map<String, Object> result = operateHandlerService.handleActions(actionConfigModel, request);
        this.fillInfo(actionConfigModel, result);
        delegateCommonAction2Handlers.handle(actionConfigModel, result);
        eventPublisher.publishEvent(new ActionEvent(actionConfigModel));

        return HttpResponseUtil.success(result);
    }

    /**
     * 填充权限查询条件
     * @param actionConfigModel
     */
    private void fillPermissionQuery(ActionConfig actionConfigModel,ActionConfigFrontTransform actionConfigFrontTransform) {//TODO
        if(actionConfigModel!=null){
            String id = actionConfigModel.getId();
            if("IP_LIST".equals(id)||"USER_LIST".equals(id)||"AUDIT_V2_FILE_LIST".equals(id)){
                List<String> appUriList=getAppUriList();
                if(appUriList.isEmpty()){
                    return;
                }
                //填充条件
                List<RepositoryOperate> repositoryOperateList = actionConfigModel.getRepositoryOperateList();
                for(RepositoryOperate repositoryOperate:repositoryOperateList){
                    List<Criteria> frontCriteriaList = repositoryOperate.getFrontCriteriaList();
                    Criteria appUriCriteria= Criteria.where("appUriList").in(appUriList);
                    frontCriteriaList.add(appUriCriteria);
                }
            }
        }
        if(actionConfigFrontTransform!=null){
            String id = actionConfigFrontTransform.getId();
            if("API_HIGH_CK_EVENT_LIST".equals(id)||"API_HIGH_CK_EVENT_COUNT".equals(id)){
                List<String> appUriList=getAppUriList();
                if(appUriList.isEmpty()){
                    return;
                }
                List<String> hosts = appUriList.stream().map(e -> e.replace("httpapp:", "")).collect(Collectors.toList());
                //填充条件
                List<ActionFrontTransformPath> actionFrontTransformPaths = actionConfigFrontTransform.getActionFrontTransformPaths();
                Optional<ActionFrontTransformPath> firstOp = actionFrontTransformPaths.stream().filter(e -> e.getPath().contains(".frontCriteriaList")).findFirst();
                if(firstOp.isPresent()){
                    ActionFrontTransformPath actionFrontTransformPath = firstOp.get();
                    JSONArray array = (JSONArray) actionFrontTransformPath.getValue();
                    Criteria criteria= Criteria.where("host").in(hosts);
                    array.add(JSONObject.toJSON(criteria));
                }
            }
        }
    }

    private List<String> getAppUriList() {
        List<String> appUriList = new ArrayList<>();
        String userId = HttpServletRequestUtil.getUserId();
        appUriList= dataPermissionUtil.convertAppUriQuery(userId);
        return appUriList;
    }

    @PostMapping("/getActionByFrontConfig.do")
    public String getActionByFrontConfig(String actionConfigFrontTransform, HttpServletRequest request) {
        ActionConfigFrontTransform actionConfigFrontTransformModel = null;
        try {
            String aesDecrypt = AESUtil.aesDecrypt(actionConfigFrontTransform);
            String actionConfigFrontTransformText = URLDecoder.decode(aesDecrypt, CharEncoding.UTF_8);
            actionConfigFrontTransformModel = JSONObject.parseObject(actionConfigFrontTransformText,ActionConfigFrontTransform.class);
            try {
                sysLogService.insertCommonApiLog(actionConfigFrontTransformModel.getId());
            } catch (Exception e) {
                log.error("log insert error", e);
            }
            this.convertQuery(actionConfigFrontTransformModel);
            fillPermissionQuery(null,actionConfigFrontTransformModel);
            Map<String, Object> objectMap = CommonActionCheckUtil.checkIP(actionConfigFrontTransformModel);
            if (objectMap != null  && !objectMap.isEmpty()) {
                if(objectMap.get("errorMsg") != null){
                    return HttpResponseUtil.error(400, objectMap.get("errorMsg").toString());
                }else if(objectMap.get("emptyValue") != null){
                    return HttpResponseUtil.success(objectMap.get("emptyValue"));
                }
            }
            List<ActionFrontTransformPath> actionFrontTransformPaths = actionConfigFrontTransformModel.getActionFrontTransformPaths();
            log.info("getActionByFrontConfig params is {}", JSON.toJSONString(actionConfigFrontTransform));

            Map<String, Object> result = null;
            if (actionIds.contains(actionConfigFrontTransformModel.getId())) {
                result = commonActionService.getActionByFrontConfig(actionConfigFrontTransformModel);
            } else {
                result = operateHandlerService.handleActions(actionConfigFrontTransformModel, request);
            }

            this.fillFrontRowsInfo(actionFrontTransformPaths, result);

            Object rows = result.get("rows");
            // 溯源兼容老数据没有percentage：百分比数值
            if (("AUDIT_V2_2.6.2_TRACE_TASK_RECORD_LIST".equals(actionConfigFrontTransformModel.getId()) ||
                    "APP_AUDIT_2.1_OFFLINE_REPORT_LIST".equals(actionConfigFrontTransformModel.getId())) && DataUtil.isNotEmpty(rows)) {
                for (Map<String, Object> row : (List<Map<String, Object>>) rows) {
                    Boolean flag = false;
                    for (Map.Entry<String, Object> map : row.entrySet()) {
                        if (map.getKey().equals("taskStatus") && map.getValue().equals(TaskStatus.FINISH.name())) {
                            flag = true;
                        }
                    }
                    if (flag) {
                        row.put("percentage", 100);
                    }
                }
            }
            commonActionHandler.handle(actionConfigFrontTransformModel, result);

            List<NetworkSegment> networkSegmentsList = com.quanzhi.audit_core.resource.fetcher.client.utils.DataUtil.deepCopy(networkSegments);

            List<NetworkSegmentIpInfo> networkSegmentConfigInfos = NetworkSegmentConverter.convert2(networkSegmentsList);

            if("AUDIT_V2_TRACE_EVENT_LIST".equals(actionConfigFrontTransformModel.getId()) && DataUtil.isNotEmpty(result) && result.containsKey("rows") && result.get("rows") != null){
                List<JSONObject> jsonObjects = JSONArray.parseArray(JSON.toJSONString(result.get("rows")), JSONObject.class);
                for (JSONObject obj:jsonObjects){
                    if(obj.containsKey("ip")){
                        String ip = obj.getString("ip");
                        Map<String, Object> ipNetwork = IpNetworkDomainFetcher.getNetworkDomainsAndArea(ip, networkSegmentConfigInfos);
                        if(ipNetwork.containsKey("network") && ipNetwork.get("network") != null){
                            obj.put("accessDomains",ipNetwork.get("network"));
                        }
                    }
                }
                result.put("rows",jsonObjects);
            }
            //实时计算源IP、目的IP地域
            if ("CLICKHOUSE_CHECK_HTTP_EVENT".equals(actionConfigFrontTransformModel.getId()) && DataUtil.isNotEmpty(result) && result.containsKey("data")) {
                JSONObject data = JSONObject.parseObject(JSON.toJSONString(result.get("data")));
                if (data.containsKey("httpApiSample")) {
                    JSONObject httpApiSample = data.getJSONObject("httpApiSample");
                    if (httpApiSample.containsKey("net")) {
                        JSONObject net = httpApiSample.getJSONObject("net");
                        if (net.containsKey("srcIp")) {
                            String srcIp = net.getString("srcIp");
                            Map<String, Object> srcIpNetworkDomainsAndArea = IpNetworkDomainFetcher.getNetworkDomainsAndArea(srcIp, networkSegmentConfigInfos);
                            if (srcIpNetworkDomainsAndArea.containsKey("area")) {
                                Position area = (Position) srcIpNetworkDomainsAndArea.get("area");
                                net.put("srcIpLocation", IpLocationUtil.getLocationStr(area));
                            }
                        }
                        if (net.containsKey("dstIp")) {
                            String dstIp = net.getString("dstIp");
                            Map<String, Object> dstIpNetworkDomainsAndArea = IpNetworkDomainFetcher.getNetworkDomainsAndArea(dstIp, networkSegmentConfigInfos);
                            if (dstIpNetworkDomainsAndArea.containsKey("area")) {
                                Position area = (Position) dstIpNetworkDomainsAndArea.get("area");
                                net.put("dstIpLocation", IpLocationUtil.getLocationStr(area));
                            }
                        }
                        httpApiSample.put("net", net);
                        data.put("net", net);
                    }
                    if (httpApiSample.containsKey("ip")) {
                        String ip = data.getString("ip");
                        Map<String, Object> ipNetworkDomainsAndArea = IpNetworkDomainFetcher.getNetworkDomainsAndArea(ip, networkSegmentConfigInfos);
                        if (ipNetworkDomainsAndArea.containsKey("area")) {
                            Position area = (Position) ipNetworkDomainsAndArea.get("area");
                            data.put("ipLocation", IpLocationUtil.getLocationStr(area));
                        }
                    }
                    data.put("httpApiSample", httpApiSample);
                }
                result.put("data", data);
            }
            if("FILE_GROUP_INFO".equals(actionConfigFrontTransformModel.getId())){
                Object object = result.get("downloadAccountDistinctCnt");
                if(object instanceof Map && ((Map<?, ?>) object).get("code") != null){
                    Object code = ((Map<?, ?>) object).get("code");
                    Object msg = ((Map<?, ?>) object).get("msg");
                    if("4002".equals(code.toString()) && msg != null){
                        return HttpResponseUtil.error(msg.toString());
                    }
                }
            }
            return HttpResponseUtil.success(result);
        } catch (Exception e) {
            log.error("getActionByFrontConfig error:", e);
            return HttpResponseUtil.error(e.getMessage());
        }
    }

    @PostMapping("/getTaskPoll")
    public String getTaskPoll(String id, String type) {
        if (DataUtil.isEmpty(id) || DataUtil.isEmpty(type)) {
            return HttpResponseUtil.error(400, "请求参数不能为空");
        }
        Map<String, Object> map = commonActionService.judgmentStatus(id, type);
        return HttpResponseUtil.success(map);
    }

    @PostMapping("/byNameGetTask")
    public String byNameGetTask(String name) {
        if (DataUtil.isEmpty(name)) {
            return HttpResponseUtil.error(400, "请求参数不能为空");
        }
        ReportConfig reportConfig = commonActionService.byNameGetTask(name);
        return HttpResponseUtil.success(reportConfig);
    }


    /**
     * @param actionConfig, result
     * @return void
     * <AUTHOR>
     * @date 2023/3/3 10:11 AM
     * @Description 补充返回信息
     * @Since
     */
    protected void fillInfo(ActionConfig actionConfig, Map<String, Object> result) {
        String id = actionConfig.getId();
        if (DataUtil.isEmpty(id)) {
            return;
        }

        if (DataUtil.isEmpty(result)) {
            return;
        }

        Object rows = result.get("rows");

        String entityType = SubjectType.IP;
        switch (id) {
            case "IP_LIST":

                if (DataUtil.isEmpty(rows)) {
                    return;
                }

                this.fillIpInfo(rows);
                result.put("rows", rows);
                break;
            case "USER_LIST":

                if (DataUtil.isEmpty(rows)) {
                    return;
                }

                if ("USER_LIST".equals(id)) {
                    entityType = SubjectType.ACCOUNT;
                }

                this.fillInfo(rows, entityType);
                result.put("rows", rows);
                break;
            case "IP_BASIC_INFO":
                JSONObject ipBasicInfo = JSONObject.parseObject(JSON.toJSONString(result.get("ipBasicInfo")));

                if (DataUtil.isEmpty(result) || DataUtil.isEmpty(ipBasicInfo)) {
                    if ("IP_BASIC_INFO".equals(id)) {
                        throw new IllegalArgumentException("IP信息不存在或还在计算中，请稍后再试");
                    } else {
                        throw new IllegalArgumentException("账号信息不存在或还在计算中，请稍后再试");
                    }
                }

                String ip = ipBasicInfo.getString("ip");
                List<NetworkSegment> networkSegmentsList = com.quanzhi.audit_core.resource.fetcher.client.utils.DataUtil.deepCopy(networkSegments);

                List<NetworkSegmentIpInfo> networkSegmentConfigInfos = NetworkSegmentConverter.convert2(networkSegmentsList);
                List<NetworkDomain> networkDomains = IpNetworkDomainFetcher.getNetworkDomains(ip, networkSegmentConfigInfos);

                if (com.quanzhi.audit_core.common.utils.DataUtil.isNotEmpty(networkDomains)) {
                    ipBasicInfo.put("accessDomainIds", networkDomains.stream().map(domain -> domain.getId()).collect(Collectors.toList()));
                } else {
                    List<String> list = new ArrayList<>();
                    list.add("--");
                    //不能用Collections.singleton()
                    ipBasicInfo.put("accessDomainIds", list);
                    ipBasicInfo.put("location", IpLocationUtil.OTHER);
                }

                //实时计算IP的IP地域
                Position position = new Position();
                Map<String, Object> ipNetworkDomainsAndArea = IpNetworkDomainFetcher.getNetworkDomainsAndArea(ip, networkSegmentConfigInfos);
                if (ipNetworkDomainsAndArea.containsKey("area")) {
                    position = (Position) ipNetworkDomainsAndArea.get("area");
                }
                String locationStr = IpLocationUtil.getLocationStr(position);
                ipBasicInfo.put("location", locationStr);
                ipBasicInfo.put("city", position.getCity());
                ipBasicInfo.put("country", position.getCountry());
                ipBasicInfo.put("province", position.getProvince());

                result.put("ipBasicInfo", ipBasicInfo);
                this.fillInfo(ipBasicInfo, entityType, false);
                break;
            case "AUDIT_V2_USER_BASIC_INFO":
                Object basicInfo = result.get("ipBasicInfo");
                if ("AUDIT_V2_USER_BASIC_INFO".equals(id)) {
                    basicInfo = result.get("basicInfo");
                    entityType = SubjectType.ACCOUNT;
                }

                if (DataUtil.isEmpty(basicInfo)) {
                    if ("IP_BASIC_INFO".equals(id)) {
                        throw new IllegalArgumentException("IP信息不存在或还在计算中，请稍后再试");
                    } else {
                        throw new IllegalArgumentException("账号信息不存在或还在计算中，请稍后再试");
                    }
                }

                Map<String, Object> info = (Map<String, Object>) basicInfo;

                this.fillInfo(info, entityType, true);

                if ("IP_BASIC_INFO".equals(id)) {
                    result.put("ipBasicInfo", info);
                }

                if ("AUDIT_V2_USER_BASIC_INFO".equals(id)) {
                    result.put("basicInfo", info);
                }

                break;
        }
    }

    private void fillIpInfo(Object rows) {
        if (DataUtil.isEmpty(rows)) {
            return;
        }

        for (Map<String, Object> row : (List<Map<String, Object>>) rows) {
            String ip = (String) row.get("ip");
            try {
                List<NetworkSegment> networkSegmentsList = com.quanzhi.audit_core.resource.fetcher.client.utils.DataUtil.deepCopy(networkSegments);

                List<NetworkSegmentIpInfo> networkSegmentConfigInfos = NetworkSegmentConverter.convert2(networkSegmentsList);
                Map<String, Object> domainsAndArea = IpNetworkDomainFetcher.getNetworkDomainsAndArea(ip, networkSegmentConfigInfos);

                if (com.quanzhi.audit_core.common.utils.DataUtil.isNotEmpty(domainsAndArea)) {
                    Object nets = domainsAndArea.get("network");
                    if(nets instanceof List){
                        List<String> collect = ((List<NetworkDomain>) nets).stream().map(NetworkDomain::getId).filter(id -> id != null && !id.isEmpty()).collect(Collectors.toList());
                        row.put("accessDomainIds", collect);
                    }
                    Object object = domainsAndArea.get("area");
                    if(object != null && object instanceof Position){
                        Position position = (Position) object;
                        row.put("country",position.getCountry());
                        row.put("province",position.getProvince());
                        row.put("city",position.getCity());
                    }
                } else {
                    row.put("accessDomainIds", Collections.singleton("--"));
                    row.put("location", IpLocationUtil.OTHER);
                }
                List<String> appUri = (List<String>) row.get("appUriList");
                if (appUri != null) {
                    row.put("appList", appUri.stream().map(i -> new MetabaseURI(i).getName()).collect(Collectors.toList()));
                }
                // 获取风险数据
                this.fillRisk(row);
            } catch (Exception e) {
                log.error("ip list get accessDomainIds error:", e);
            }
        }
    }

    /**
     * @param rows, entityType
     * @return void
     * <AUTHOR>
     * @date 2023/3/3 11:06 AM
     * @Description 补充风险等级
     * @Since
     */
    private void fillInfo(Object rows, String entityType) {
        if (DataUtil.isEmpty(rows) || DataUtil.isEmpty(entityType)) {
            return;
        }

        for (Map<String, Object> row : (List<Map<String, Object>>) rows) {
            this.fillInfo(row, entityType, false);
        }
    }

    private void fillInfo(Map<String, Object> row, String entityType, boolean isSingle) {
        row.put("relatedAccountBucket", null);
        row.put("relatedIpBucket", null);
        if (row.get("relatedIpList") != null) {
            row.put("relatedIpDistinctCnt", ((Collection<?>) row.get("relatedIpList")).size());
        }
        // 获取风险数据
        this.fillRisk(row);

        // 应用关系
        this.fillAPI(row, isSingle);

        // 网段关系
        this.fillNetwork(row);

        // 威胁标签
        row.put("threatLabels", new ArrayList<>());

        // 终端类型
        this.fillUaType(row);

        // 部门信息
        this.fillDepartInfo(row);

        // 生命周期
        this.fillLife(row);
    }

    private void fillNetwork(Map<String, Object> row) {
        Set<String> relatedNetworkList = new HashSet<>();
        List<String> accessDomainIds = (List) row.get("accessDomainIds");
        if (DataUtil.isNotEmpty(accessDomainIds)) {
            relatedNetworkList.addAll(accessDomainIds);
        }
        row.put("relatedNetworkList", relatedNetworkList);
    }

    private void fillRisk(Map<String, Object> row) {
        Object risk_ = row.get("riskInfo");
        if (risk_ != null) {
            RiskLevelMatchDto.Risk risk = JSON.parseObject(JSON.toJSONString(risk_), RiskLevelMatchDto.Risk.class);
            row.put("riskNames", risk.getRiskNames());
            row.put("confirmRiskCount", risk.getConfirmRiskCount()); // 已确认风险数量
            row.put("riskEventCount", risk.getTotalRiskCnt()); // 已确认和未确认数量
            row.put("riskFirstTime", risk.getRiskFirstTime()); // 触发风险时间
            row.put("riskLastTime", risk.getRiskLastTime()); // 最近风险时间
        }
    }

    private void fillAPI(Map<String, Object> row, boolean isSingle) {
        try {
            LinkedHashSet<String> appList = new LinkedHashSet<>();
            List<String> appUriList = (List) row.get("appUriList");
            if (appUriList == null || appUriList.isEmpty()) {
                appUriList = new ArrayList<>();
                if (row.get("appUri") != null) {
                    appUriList.add((String) row.get("appUri"));
                }
            }
            if (DataUtil.isNotEmpty(appUriList)) {
                // 单条账号信息，对应用关系按账号解析率排序
                if (isSingle) {
                    HttpAppCriteriaDto httpAppCriteriaDto = new HttpAppCriteriaDto();
                    httpAppCriteriaDto.setAppUriList(appUriList);
                    httpAppCriteriaDto.setField("appStat.accountStat.accountApiRate");
                    httpAppCriteriaDto.setSort(ConstantUtil.Sort.ASC);
                    httpAppCriteriaDto.setPage(1);
                    httpAppCriteriaDto.setLimit(10);

                    long totalCount = httpAppDao.countByHttpAppCriDto(httpAppCriteriaDto);
                    if (totalCount > 0) {
                        List<HttpAppResource> httpAppResources = httpAppDao.getAccParseAppList(httpAppCriteriaDto);
                        appUriList = httpAppResources.stream().map(httpAppResource -> httpAppResource.getUri()).collect(Collectors.toList());
                    }
                }

                for (String appUri : appUriList) {
                    appList.add(MetabaseURI.getName(appUri));
                }
            }

            row.put("appUriList", appUriList);
            row.put("appList", appList);

            List<String> uris = (List) row.get("apiUriList");
            if ((uris == null || uris.isEmpty())) {
                uris = new ArrayList<>();
                if (row.get("apiUri") != null) {
                    uris.add((String) row.get("apiUri"));
                }
            }
            row.put("apiList", uris.stream().map(i -> new MetabaseURI(i).getName()).collect(Collectors.toList()));
            row.put("apiUriList", uris);
        } catch (Exception e) {
            log.error("fill app error：", e);
        }
    }

    private void fillLife(Map<String, Object> row) {
        try {
            String firstDate = (String) row.get("firstDate");
            String lastDate = (String) row.get("lastDate");

            Long discoverTime = null;
            if (DataUtil.isNotEmpty(firstDate)) {
                discoverTime = DateFormat.date2TimeStamp(firstDate, DateFormat.FORMAT_YMD);
            }

            Long activeTime = null;
            if (DataUtil.isNotEmpty(lastDate)) {
                activeTime = DateFormat.date2TimeStamp(lastDate, DateFormat.FORMAT_YMD);
            }

            JSONObject jsonObject = new JSONObject();
            jsonObject.put("assetType", "ACCOUNT");
            jsonObject.put("discoverTime", discoverTime);
            jsonObject.put("activeTime", activeTime);
            jsonObject.put("reviveTime", row.get("reviveTime"));
            List<Short> matchLifeFlags = assetLifeStateConfigConvert.getAssetLifeState(jsonObject);

            List<String> accountLifeFlag = matchLifeFlags.stream().map(e -> AssetLifeStateEnum.valueOfNum(e).getName()).collect(Collectors.toList());
            String result = accountLifeFlag.isEmpty() ? "" : String.join(",", accountLifeFlag);

            row.put("accountLifeFlag", matchLifeFlags);
        } catch (Exception e) {
            log.error("转换生命周期失败：", e);
        }
    }

    private void fillUaType(Map<String, Object> row) {
        try {
            if (uaTypeMap == null) {
                uaTypeMap = new HashMap<>();
                if (DataUtil.isNotEmpty(uaType)) {
                    List<UaType.UaTypeDetail> uaTypeConfigs = uaType.getUaTypeConfigs();
                    if (DataUtil.isNotEmpty(uaTypeConfigs)) {
                        for (UaType.UaTypeDetail uaTypeDetail : uaTypeConfigs) {
                            String standardUaType = uaTypeDetail.getStandardUaType();
                            String uaTypeKey = uaTypeDetail.getUaType();

                            uaTypeMap.put(uaTypeKey, standardUaType);
                        }
                    }
                }
            }

            Set<String> uaTypes = new HashSet<>();
            List<String> uaTypeList = (List<String>) row.get("uaTypes");
            if (DataUtil.isNotEmpty(uaTypeList)) {
                for (String uaTypeStardard : uaTypeList) {
                    if (DataUtil.isNotEmpty(uaTypeStardard)) {
                        String uaType = uaTypeStardard;
                        String value = uaTypeMap.get(uaTypeStardard);
                        if (DataUtil.isNotEmpty(value)) {
                            uaType = value;
                        }

                        uaTypes.add(uaType);
                    }
                }
            }

            row.put("uaTypes", uaTypes);
        } catch (Exception e) {
            log.error("获取终端类型失败：", e);
        }
    }

    private void fillDepartInfo(Map<String, Object> row) {
//        Boolean staffFlag = (Boolean) row.get("staffFlag");
//        String account = (String) row.get("account");
//        if (staffFlag == null && DataUtil.isNotEmpty(account)){
//            // 没有添加过staffFlag时，实时匹配部门信息
//            UserDepart userDepart = userDepartService.getUserDepart(account, Arrays.asList(PrimaryNameEnum.values()).stream().map(primaryNameEnum -> primaryNameEnum.name()).collect(Collectors.toList()));
//            if (userDepart != null) {
//                row.put("staffDepart", userDepart.getStaffDepart());
//                row.put("staffNickName", userDepart.getStaffNickName());
//                row.put("staffName", userDepart.getStaffName());
//                row.put("staffChinese", userDepart.getStaffChinese());
//
//                row.put("staffEmail", userDepart.getStaffEmail());
//                row.put("staffId", userDepart.getStaffId());
//                row.put("staffMobile", userDepart.getStaffMobile());
//                row.put("staffRole", userDepart.getStaffRole());
//            }
//        }

        row.put("staffIdCard", "");
        row.put("staffBankCard", "");
    }

    /**
     * @param row, entityType
     * @return java.lang.String
     * <AUTHOR>
     * @date 2023/3/3 11:07 AM
     * @Description 获取主体信息
     * @Since
     */
    private String getEntity(Map<String, Object> row, String entityType) {
        if (DataUtil.isEmpty(entityType)) {
            return null;
        }

        switch (entityType) {
            case SubjectType.IP:
                return (String) row.get("ip");
            case SubjectType.ACCOUNT:
                return (String) row.get("account");
        }

        return null;
    }

    /**
     * @param actionFrontTransformPaths, result
     * @return void
     * <AUTHOR>
     * @date 2023/3/3 10:07 AM
     * @Description 补充一些返回信息
     * @Since
     */
    private void fillFrontRowsInfo(List<ActionFrontTransformPath> actionFrontTransformPaths, Map<String, Object> result) {
        Object rows = result.get("rows");

        // 有些写的key是rows,但是可能没有查询条件，这里兼容一下，当然写其他的就可以了
        if (DataUtil.isNotEmpty(rows) && DataUtil.isNotEmpty(actionFrontTransformPaths)) {
            for (ActionFrontTransformPath actionFrontTransformPath : actionFrontTransformPaths) {
                Object value = actionFrontTransformPath.getValue();
                if (DataUtil.isNotEmpty(value) && "ftp_defined_event".equalsIgnoreCase(value.toString())) {
                    for (Map<String, Object> row : (List<Map<String, Object>>) rows) {
                        row.put("protocol", "FTP");
                    }
                }
            }
        }
    }

    protected void convertQuery(ActionConfig actionConfig) {
        String id = actionConfig.getId();
        if (!"USER_LIST".equals(id)) {
            return;
        }

        // metabaseQuery.where("accountLifeFlag", Predicate.IN, value);
        try {
            List<RepositoryOperate> repositoryOperateList = actionConfig.getRepositoryOperateList();
            if (DataUtil.isNotEmpty(repositoryOperateList)) {
                for (RepositoryOperate repositoryOperate : repositoryOperateList) {
                    List<Criteria> criteriaList = repositoryOperate.getFrontCriteriaList();

                    List<Criteria> criteriaList_result = new ArrayList<>();
                    repositoryOperate.setFrontCriteriaList(criteriaList_result);

                    if (DataUtil.isNotEmpty(criteriaList)) {
                        for (Criteria criteria : criteriaList) {
                            String property = criteria.getProperty();
                            Object value = criteria.getValue(); // 这里要注意是传的数字，还是一个值
                            if ("accountLifeFlag".equals(property) && DataUtil.isNotEmpty(value)) {
                                short lifeFlag = Short.parseShort(value + "");
                                AssetLifeStateEnum assetLifeStateEnum = AssetLifeStateEnum.valueOfNum(lifeFlag);
                                AssetLifeStateConfigConvert.CriteriaTag criteriaTag = assetLifeStateConfigConvert.getCriteria(AssetLifeStateConfig.AssetTypeEnum.ACCOUNT, assetLifeStateEnum);
                                com.quanzhi.metabase.core.model.query.Criteria metabaseCriteria = criteriaTag.getMetabaseCriteria();

                                criteria = JSON.parseObject(JSON.toJSONString(metabaseCriteria), Criteria.class);
//                               criteria = new Criteria();
//                               criteria.setProperty(metabaseCriteria.getProperty());
//                               criteria.setOperator(metabaseCriteria.getOperator().name());
//                               criteria.setValue();
                            }

                            criteriaList_result.add(criteria);
                        }
                    }
                }
            }
        } catch (NumberFormatException e) {
            log.error("参数转换错误：", e);
        }
    }

    private void convertQuery(ActionConfigFrontTransform actionConfigFrontTransform) {
        String id = actionConfigFrontTransform.getId();
        if (!"APP_AUDIT_2.1_ACCOUNT_LIST_GROUP_LIST".equals(id)) {
            return;
        }

        List<ActionFrontTransformPath> actionFrontTransformPaths = actionConfigFrontTransform.getActionFrontTransformPaths();
        if (com.quanzhi.operate.utils.DataUtil.isEmpty(actionFrontTransformPaths)) {
            return;
        }

        for (ActionFrontTransformPath actionFrontTransformPath : actionFrontTransformPaths) {
            String path = actionFrontTransformPath.getPath();
            Object value = actionFrontTransformPath.getValue();

            if (com.quanzhi.operate.utils.DataUtil.isNotEmpty(value)) {
                if ("$.repositoryOperateList[0].frontCriteriaList".equals(path)) {
                    List<Map<String, Object>> criteriaList = new ArrayList<>();
                    actionFrontTransformPath.setValue(criteriaList);

                    List<com.quanzhi.metabase.core.model.query.Criteria> metabaseCriteriaList = JSONArray.parseArray(JSON.toJSONString(value), com.quanzhi.metabase.core.model.query.Criteria.class);
                    for (com.quanzhi.metabase.core.model.query.Criteria criteria : metabaseCriteriaList) {
                        String property = criteria.getProperty();
                        Object value_query = criteria.getValue();

                        Map<String, Object> map = new HashMap<>();
                        map.put("property", property);
                        map.put("value", value_query);
                        map.put("predicate", criteria.getPredicate());

                        if ("accountLifeFlag".equals(property) && DataUtil.isNotEmpty(value_query)) {
                            short lifeFlag = Short.parseShort(value_query + "");
                            AssetLifeStateEnum assetLifeStateEnum = AssetLifeStateEnum.valueOfNum(lifeFlag);
                            AssetLifeStateConfigConvert.CriteriaTag criteriaTag = assetLifeStateConfigConvert.getCriteria(AssetLifeStateConfig.AssetTypeEnum.ACCOUNT, assetLifeStateEnum);
                            com.quanzhi.metabase.core.model.query.Criteria metabaseCriteria = criteriaTag.getMetabaseCriteria();

                            List<com.quanzhi.metabase.core.model.query.Criteria> realCriteriaList = metabaseCriteria.getCriteriaChain();

                            if (DataUtil.isNotEmpty(realCriteriaList)) {
                                com.quanzhi.metabase.core.model.query.Criteria realCriteria = realCriteriaList.get(0);
                                map.put("property", realCriteria.getProperty());
                                map.put("value", realCriteria.getValue());
                                map.put("predicate", realCriteria.getPredicate().name());
                            }
                        }

                        criteriaList.add(map);
                    }
                }
            }
        }
    }
}
