package com.quanzhi.auditapiv2.web.controller;

import com.alibaba.fastjson.JSON;
import com.quanzhi.audit.mix.permission.annotation.Permission;
import com.quanzhi.audit.mix.permission.domain.enums.ResourceEnum;
import com.quanzhi.auditapiv2.common.dal.dto.CompositeRuleDto;
import com.quanzhi.auditapiv2.common.dal.dto.CompositeRuleSearchDto;
import com.quanzhi.auditapiv2.common.dal.dto.assetDefinition.KeywordSplitRuleDto;
import com.quanzhi.auditapiv2.common.util.dto.ListOutputDto;
import com.quanzhi.auditapiv2.common.util.dto.ResponseVo;
import com.quanzhi.auditapiv2.common.util.utils.DataUtil;
import com.quanzhi.auditapiv2.common.util.utils.HttpResponseUtil;
import com.quanzhi.auditapiv2.common.util.utils.StringUtils;
import com.quanzhi.auditapiv2.core.service.manager.web.IResourceDefineService;
import com.quanzhi.auditapiv2.core.service.manager.web.IUrlStructureService;
import com.quanzhi.auditapiv2.core.service.manager.web.IXxlJobService;
import com.quanzhi.auditapiv2.web.common.annotation.DirectiveSync;
import com.quanzhi.auditapiv2.web.common.annotation.OperateLog;
import com.quanzhi.metabase.core.model.http.CompositeRule;
import com.quanzhi.metabase.core.model.http.KeywordSplitRule;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @Author: Linlm
 * @Description: 资源定义:应用、接口的合并、拆分规则定义
 * @Date: Created in 2020/5/15 下午2:00
 */
@RestController
@RequestMapping("/api/resourceDefine")
@Slf4j
public class ResourceDefineController {
    @Autowired
    private IResourceDefineService resourceDefineService;

    @Autowired
    private IXxlJobService xxlJobService;

    @Autowired
    private IUrlStructureService urlStructureService;

    /**
     * 添加资源定义规则
     * 删除同撤销
     *
     * @param compositeRule
     * @return
     */
    @PostMapping("/saveCompositeRule")
    @DirectiveSync(key = "id", needNotify = true, retry = false, remark = "操作资产定义",replaceId = "id")
    @Permission(type = ResourceEnum.ACTION, value = "WRITE")
    public String saveCompositeRule(@RequestBody CompositeRuleDto compositeRule) {
        String conflictResult = resourceDefineService.checkRuleConflict(compositeRule);
        if (conflictResult != null) {
            return conflictResult;
        }
        String appRuleResult = checkAppMergeRulePre(compositeRule);
        if (appRuleResult != null) {
            return HttpResponseUtil.error(appRuleResult);
        }
        xxlJobService.triggerCompositeJob(JSON.toJSONString(resourceDefineService.saveCompositeRule(compositeRule)));
        return HttpResponseUtil.success();
    }

    private String checkAppMergeRulePre(CompositeRuleDto dto) {
        if (CompositeRule.RscTypeEnum.APP.getRscType().equals(dto.getRscType()) &&
                CompositeRule.CompositeTypeEnum.MERGE.getCompositeType().equals(dto.getCompositeType())) {
            String regexRule = dto.getRegexRule();
            String rule = dto.getRule();
            if(rule.contains("*")){
                return "主应用不支持通配符";
            }
            List<String> hosts = Arrays.stream(regexRule.substring(1, regexRule.length() - 1).split("\\|")).collect(Collectors.toList());
            if(hosts.stream().anyMatch(StringUtils::isNullOrEmpty)){
                return "请移除无效的 \",\" 字符 ";
            }
            if (hosts.size() == 1&&!hosts.get(0).contains("*")) {
                return "应用合并若只填1个应用,则必须是通配符形式";
            }
            if (!hosts.contains(rule)) {
                hosts.add(rule);
                String newRegexRule = hosts.stream().collect(Collectors.joining("|", "(", ")"));
                dto.setRegexRule(newRegexRule);
            }
        }
        return null;
    }

    @ApiOperation(value = "判断资产定义是否重复")
    @PostMapping("/checkRuleRepeat")
    public ResponseVo<?> checkRuleRepeat(@RequestBody CompositeRuleDto compositeRuleDto) {
        return ResponseVo.ok(resourceDefineService.checkRuleRepeat(compositeRuleDto));
    }

    @ApiOperation(value = "判断应用合并的主应用是否重复")
    @PostMapping("/checkAppMergeRuleRepeat")
    public ResponseVo<?> checkAppMergeRuleRepeat(@RequestBody CompositeRuleDto compositeRuleDto) {
        return ResponseVo.ok(resourceDefineService.checkAppMergeRuleRepeat(compositeRuleDto.getRule()));
    }


    @OperateLog(module = "资产定义", type = "编辑", value = "编辑资产定义")
    @ApiOperation(value = "更新规则")
    @PostMapping("/updateCompositeRule")
    @DirectiveSync(key = "rule", needNotify = true, retry = false, remark = "编辑资产定义",replaceId = "id")
    @Permission(type = ResourceEnum.ACTION, value = "WRITE")
    public String updateCompositeRule(@RequestBody CompositeRuleDto compositeRuleDto) {
        String appRuleResult = checkAppMergeRulePre(compositeRuleDto);
        if (appRuleResult != null) {
            return HttpResponseUtil.error(appRuleResult);
        }
        return resourceDefineService.updateCompositeRule(compositeRuleDto);
    }

    /**
     * 获取资源定义规则列表
     *
     * @param compositeRuleSearchDto
     * @return
     */
    @PostMapping("/getCompositeRules")
    public String getCompositeRules(@RequestBody CompositeRuleSearchDto compositeRuleSearchDto) {
        // 前端的分页从1开始
        if (compositeRuleSearchDto.getPage() != null) {
            compositeRuleSearchDto.setPage(compositeRuleSearchDto.getPage() - 1);
        }
        ListOutputDto listOutputDto = resourceDefineService.getCompositeRules(compositeRuleSearchDto);
        return HttpResponseUtil.success(listOutputDto);
    }

    @GetMapping("/getCompositeRuleById")
    public String getCompositeRuleById(String compositeRuleId) {
        CompositeRuleDto compositeRule = resourceDefineService.getCompositeRuleById(compositeRuleId);
        return HttpResponseUtil.success(compositeRule);
    }

    @GetMapping(value = "downloadAppTemplate")
    @ApiOperation(value = "下载应用模板")
    @Permission(type = ResourceEnum.ACTION, value = "WRITE")
    public String downloadAppTemplate(HttpServletResponse response) {
        try {
            resourceDefineService.downloadAppTemplate(response);
        } catch (IOException e) {
            log.error("生成应用模板出错:", e);
            return HttpResponseUtil.error();
        }
        return HttpResponseUtil.success();
    }


    @ApiOperation(value = "解析应用模板")
    @PostMapping("analyzeAppTemplate")
    @Permission(type = ResourceEnum.ACTION, value = "WRITE")
    public String analyzeAppTemplate(MultipartFile multipartFile) {
        return resourceDefineService.analyzeAppTemplate(multipartFile);
    }

    @ApiOperation(value = "取消资源,不参与合并拆分")
    @PostMapping("cancelResources")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "compositeType", value = "compositeType",
                    required = true, paramType = "query", dataType = "Integer"),
            @ApiImplicitParam(name = "rscType", value = "rscType",
                    required = true, paramType = "query", dataType = "Integer"),
            @ApiImplicitParam(name = "apiUrl", value = "apiUrl",
                    required = true, paramType = "query", dataType = "String"),
            @ApiImplicitParam(name = "host", value = "host",
                    required = true, paramType = "query", dataType = "String"),
            @ApiImplicitParam(name = "compositeRuleId", value = "compositeRuleId",
                    required = true, paramType = "query", dataType = "String")

    })
    @Permission(type = ResourceEnum.ACTION, value = "WRITE")
    public String cancelResources(Integer compositeType, Integer rscType, String apiUrl, String host, String compositeRuleId) {
        if (DataUtil.isNotEmpty(compositeRuleId) && CompositeRule.RscTypeEnum.APP.getRscType().equals(rscType)) {
            return HttpResponseUtil.error("暂不支持应用合并后的删除操作");
        }
        return resourceDefineService.cancelResources(compositeType, rscType, apiUrl, host, compositeRuleId);
    }


    @OperateLog(module = "资产定义", type = "新增", value = "新增关键词拆分规则")
    @ApiOperation(value = "保存关键词拆分规则")
    @PostMapping("saveKeywordSplitRule")
    @ApiImplicitParam(name = "keywordSplitRuleDto", value = "关键词拆分规则", dataType = "KeywordSplitRuleDto", paramType = "body")
    @DirectiveSync(key = "id", needNotify = true, retry = false, remark = "保存关键词拆分规则")
    @Permission(type = ResourceEnum.ACTION, value = "WRITE")
    public ResponseVo<Boolean> saveKeywordSplitRule(@RequestBody KeywordSplitRuleDto keywordSplitRuleDto) {
        return ResponseVo.ok(resourceDefineService.saveKeywordSplitRule(keywordSplitRuleDto));
    }

    @OperateLog(module = "资产定义", type = "编辑", value = "编辑关键词拆分规则")
    @ApiOperation(value = "更新关键词拆分规则")
    @PostMapping("updateKeywordSplitRule")
    @ApiImplicitParam(name = "keywordSplitRuleDto", value = "关键词拆分规则", dataType = "KeywordSplitRuleDto", paramType = "body")
    @DirectiveSync(key = "id", needNotify = true, retry = false, remark = "更新关键词拆分规则",replaceId = "id")
    @Permission(type = ResourceEnum.ACTION, value = "WRITE")
    public ResponseVo<String> updateKeywordSplitRule(@RequestBody KeywordSplitRuleDto keywordSplitRuleDto) {
        return ResponseVo.ok(resourceDefineService.updateKeywordSplitRule(keywordSplitRuleDto));
    }

    @ApiOperation(value = "关键词拆分规则列表")
    @PostMapping("listKeywordSplitRule")
    public ResponseVo<ListOutputDto<KeywordSplitRule>> listKeywordSplitRule(@RequestBody KeywordSplitRuleDto keywordSplitRuleDto) {
        return ResponseVo.ok(resourceDefineService.listKeywordSplitRule(keywordSplitRuleDto));
    }

    @OperateLog(module = "资产定义", type = "处理", value = "对待确认的规则进行确认")
    @ApiOperation(value = "对待确认的规则进行确认")
    @PostMapping("confirmedRule")
    @DirectiveSync(key = "rule", needNotify = true, retry = false, remark = "对待确认的规则进行确认",replaceId = "id")
    @Permission(type = ResourceEnum.ACTION, value = "WRITE")
    public ResponseVo<Boolean> confirmedRule(@RequestBody CompositeRuleDto compositeRuleDto) {
        return ResponseVo.ok(resourceDefineService.confirmedRule(compositeRuleDto));
    }

    @OperateLog(module = "资产定义", type = "删除", value = "删除待确认的规则")
    @ApiOperation(value = "人工删除待确认的规则")
    @PostMapping("delConfirmedRule")
    @DirectiveSync(key = "rule", needNotify = true, retry = false, remark = "人工删除待确认的规则",replaceId = "id")
    @Permission(type = ResourceEnum.ACTION, value = "WRITE")
    public ResponseVo<Boolean> delConfirmedRule(@RequestBody CompositeRuleDto compositeRuleDto) {
        return ResponseVo.ok(resourceDefineService.delConfirmedRule(compositeRuleDto));
    }

    @ApiOperation(value = "人工触发一次资产自动推荐任务")
    @PostMapping("triggerResourceAutoRecommend")
    @Permission(type = ResourceEnum.ACTION, value = "WRITE")
    public ResponseVo<Boolean> triggerResourceAutoRecommend() {
        return ResponseVo.ok(resourceDefineService.triggerResourceAutoRecommend());
    }

    @OperateLog(module = "资产定义", type = "删除", value = "批量删除资产定义规则")
    @ApiOperation(value = "批量删除规则")
    @PostMapping("batchDelete")
    @DirectiveSync(key = "ids", needNotify = true, retry = false, remark = "人工删除待确认的规则",replaceId = "ids")
    @Permission(type = ResourceEnum.ACTION, value = "WRITE")
    public ResponseVo<Boolean> batchDelete(@RequestBody CompositeRuleSearchDto compositeRuleSearchDto) {
        resourceDefineService.batchDelete(compositeRuleSearchDto);
        return ResponseVo.ok();
    }


}
