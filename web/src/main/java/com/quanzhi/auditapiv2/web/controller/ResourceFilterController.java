package com.quanzhi.auditapiv2.web.controller;


import com.quanzhi.audit.mix.permission.annotation.Permission;
import com.quanzhi.audit.mix.permission.domain.enums.ResourceEnum;
import com.quanzhi.auditapiv2.common.dal.dto.filter.AssetFilterCriteriaDTO;
import com.quanzhi.auditapiv2.common.dal.dto.filter.AssetFilterDTO;
import com.quanzhi.auditapiv2.common.dal.dto.filter.EnableFilterDto;
import com.quanzhi.auditapiv2.common.dal.dto.filter.SmartFilterCriteriaDTO;
import com.quanzhi.auditapiv2.common.util.dto.ListOutputDto;
import com.quanzhi.auditapiv2.common.util.dto.ResponseVo;
import com.quanzhi.auditapiv2.core.service.manager.web.IResourceFilterService;
import com.quanzhi.auditapiv2.core.service.manager.web.impl.resource.ResourceFilterCleanService;
import com.quanzhi.auditapiv2.web.common.annotation.DirectiveSync;
import com.quanzhi.auditapiv2.web.common.annotation.OperateLog;
import com.quanzhi.metabase.core.model.filter.AssetFilterRule;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;


@RequestMapping("/api/resourceFilter")
@RestController
@Slf4j
public class ResourceFilterController {

    private final IResourceFilterService resourceService;
    private final ResourceFilterCleanService filterCleanService;

    ResourceFilterController(IResourceFilterService resourceService, ResourceFilterCleanService filterCleanService) {
        this.resourceService = resourceService;
        this.filterCleanService = filterCleanService;
    }

    @RequestMapping(value = "/getAssetResourceType", method = RequestMethod.POST)
    @ApiOperation("获取当前支持的资产过滤维度")
    public ResponseVo<?> getAssetFilterRule() {
        return ResponseVo.ok(AssetFilterRule.getAssetResourceType());
    }

    @RequestMapping(value = "/getAssetResourceTypeByLocation", method = RequestMethod.GET)
    @ApiOperation("获取当前支持的资产过滤维度按请求和返回区分")
    public ResponseVo<?> getAssetResourceTypeByLocation() {
        return ResponseVo.ok(resourceService.getAssetResourceTypeByLocation());
    }

    @RequestMapping(value = "/getPassRuleFilterCount", method = RequestMethod.GET)
    @ApiOperation("获取白名单规则过滤的数量")
    public ResponseVo<Long> getPassRuleFilterCount(){
        return ResponseVo.ok(resourceService.getPassRuleFilterCount());
    }



    @RequestMapping(value = "/list", method = RequestMethod.POST)
    @ApiOperation("资产过滤列表")
    @ApiImplicitParam(name = "assetFilterDTO", value = "查询dto", required = true)
    public ResponseVo<ListOutputDto<AssetFilterRule>> assetFilterList(@RequestBody AssetFilterDTO assetFilterDTO) {
        return ResponseVo.ok(resourceService.getAssetFilterList(assetFilterDTO));
    }

    @OperateLog(module = "资产过滤", type = "编辑", value = "编辑资产过滤")
    @ApiOperation("新增、更新、删除过滤策略")
    @RequestMapping(value = "/saveAssetFilter", method = RequestMethod.POST)
    @ApiImplicitParam(name = "assetFilterRule", value = "保存对象", required = true)
    @DirectiveSync(key = "id", needNotify = true, retry = false, remark = "新增、更新、删除过滤策略")
    @Permission(type = ResourceEnum.ACTION, value = "WRITE")
    public ResponseVo<Boolean> saveAssetFilter(@RequestBody AssetFilterRule assetFilterRule) {
        return ResponseVo.ok(resourceService.saveAssetFilterRule(assetFilterRule));
    }


    @OperateLog(module = "资产过滤", type = "编辑", value = "批量操作资产过滤")
    @ApiOperation("批量操作过滤策略")
    @RequestMapping(value = "/updateAssetFilters", method = RequestMethod.POST)
    @ApiImplicitParam(name = "assetFilterCriteriaDTO", value = "批量操作对象", required = true)
    @DirectiveSync(key = "ids", needNotify = true, retry = false, remark = "批量操作过滤策略")
    @Permission(type = ResourceEnum.ACTION, value = "WRITE")
    public ResponseVo<Boolean> updateAssetFilters(@RequestBody AssetFilterCriteriaDTO assetFilterCriteriaDTO) {
        return ResponseVo.ok(resourceService.batchUpdatesAssetFilter(assetFilterCriteriaDTO));
    }

    @OperateLog(module = "资产过滤", type = "处理", value = "预清理过滤数据")
    @ApiOperation("根据规则清理历史数据")
    @RequestMapping(value = "/enableFilters", method = RequestMethod.POST)
    @DirectiveSync(key = "ids", needNotify = true, retry = false, remark = "根据规则清理历史数据")
    @Permission(type = ResourceEnum.ACTION, value = "WRITE")
    public String enableFilters(@RequestBody EnableFilterDto enableFilterDto) {
        return resourceService.enableFilters(enableFilterDto);
    }

    @OperateLog(module = "资产过滤", type = "处理", value = "确认清理历史数据")
    @ApiOperation("确认清理历史数据")
    @RequestMapping(value = "/confirmCleanHistoryData", method = RequestMethod.POST)
    @DirectiveSync(key = "taskId", needNotify = true, retry = false, remark = "确认清理历史数据")
    @Permission(type = ResourceEnum.ACTION, value = "WRITE")
    public String confirmCleanHistoryData(@RequestParam String taskId) {
        return resourceService.confirmCleanHistoryData(taskId);
    }

    @ApiOperation("获取本次过滤接口占总接口数的百分比")
    @RequestMapping(value = "/getFilterApiPercent", method = RequestMethod.POST)
    public String getFilterApiPercent(@RequestParam String taskId) {
        return resourceService.getFilterApiPercent(taskId);
    }


    @OperateLog(module = "资产过滤", type = "处理", value = "批量开启智能过滤")
    @ApiOperation("批量开启智能过滤规则")
    @RequestMapping(value = "/batchEnableSmartFilterRule", method = RequestMethod.POST)
    @Permission(type = ResourceEnum.ACTION, value = "WRITE")
    public String batchEnableSmartFilterRule(@RequestBody SmartFilterCriteriaDTO smartFilterCriteriaDTO){
      return resourceService.batchEnableSmartFilterRule(smartFilterCriteriaDTO);
    }

    @ApiOperation("测试跑清理任务")
    @RequestMapping(value = "/testFilters", method = RequestMethod.POST)
    public ResponseVo<Boolean> testFilters(@RequestBody EnableFilterDto enableFilterDto) {
        filterCleanService.executeClean(enableFilterDto);
        return ResponseVo.ok();
    }

}
