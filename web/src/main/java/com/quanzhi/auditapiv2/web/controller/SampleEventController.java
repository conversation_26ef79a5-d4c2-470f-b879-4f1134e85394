package com.quanzhi.auditapiv2.web.controller;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.nacos.api.config.annotation.NacosValue;
import com.quanzhi.audit.mix.permission.annotation.Permission;
import com.quanzhi.audit.mix.permission.annotation.Permissions;
import com.quanzhi.audit.mix.permission.domain.enums.Logical;
import com.quanzhi.audit.mix.permission.domain.enums.ResourceEnum;
import com.quanzhi.auditapiv2.common.dal.dto.HttpApiSearchDto;
import com.quanzhi.auditapiv2.common.dal.dto.sample.SampleSearchDto;
import com.quanzhi.auditapiv2.common.dal.enums.ProductTypeEnum;

import com.quanzhi.auditapiv2.common.util.dto.ListOutputDto;
import com.quanzhi.auditapiv2.common.util.dto.ResponseVo;
import com.quanzhi.auditapiv2.common.util.enums.DecodeInfoTypeEnum;
import com.quanzhi.auditapiv2.common.util.enums.ReqConverterEnum;
import com.quanzhi.auditapiv2.common.util.enums.SampleSourceTypeEnum;
import com.quanzhi.auditapiv2.common.util.utils.DataUtil;
import com.quanzhi.auditapiv2.common.util.utils.HttpResponseUtil;
import com.quanzhi.auditapiv2.core.model.SampleEventConfig;
import com.quanzhi.auditapiv2.core.service.SysLogService;
import com.quanzhi.auditapiv2.core.service.manager.dto.ExtractValueDto;
import com.quanzhi.auditapiv2.core.service.manager.dto.SampleEventBatchTestDto;
import com.quanzhi.auditapiv2.core.service.manager.dto.SampleEventDto;
import com.quanzhi.auditapiv2.core.service.manager.dto.sample.ExtractDto;
import com.quanzhi.auditapiv2.core.service.manager.dto.sample.ThumbSampleDto;
import com.quanzhi.auditapiv2.core.service.manager.export.param.ExportSampleDto;
import com.quanzhi.auditapiv2.core.service.manager.web.ISampleEventService;
import com.quanzhi.auditapiv2.web.common.annotation.OperateLog;
import com.quanzhi.file.sight.common.content.FileContent;
import com.quanzhi.metabase.core.model.http.HttpApiResource;
import com.quanzhi.metabase.core.model.http.HttpApiSample;
import com.quanzhi.metabase.core.model.http.SourceEnum;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang.StringEscapeUtils;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.ImportResource;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.*;

@RestController
@RequestMapping("/api/sampleEvent")
public class SampleEventController {
    private org.slf4j.Logger logger = LoggerFactory.getLogger(SampleEventController.class);

    @Autowired
    private ISampleEventService sampleEventService;

    @Autowired
    private SysLogService sysLogService;

    @NacosValue(value = "${product.type:api}", autoRefreshed = true)
    private String productType;

    @ApiOperation(value = "获取全量接口资源列表")
    @RequestMapping(value = "getSampleList.do", method = RequestMethod.GET)
    public String getSampleList(
            @RequestParam(value = "page", required = false) Integer page,
            @RequestParam(value = "limit", required = false) Integer limit,
            @RequestParam(value = "uri", required = false) String uri,
            @RequestParam(value = "apiUrl", required = false) String apiUrl,
            @RequestParam(value = "httpApiSnapshotLimit", required = false, defaultValue = "4") Integer httpApiSnapshotLimit,
            @RequestParam(value = "desensitize", required = false, defaultValue = "false") Boolean desensitize,
            @RequestParam(value = "reqDataLabels", required = false) List<String> reqDataLabels,
            @RequestParam(value = "rspDataLabels", required = false) List<String> rspDataLabels,
            @RequestParam(value = "compositeType", required = false) Integer compositeType,
            @RequestParam(value = "sampleId", required = false) String sampleId
    ) {
        uri = StringEscapeUtils.unescapeHtml(uri);
        apiUrl = StringEscapeUtils.unescapeHtml(apiUrl);
        ListOutputDto<SampleEventDto> outputDto = new ListOutputDto<>();

        SampleSearchDto sampleSearchDto=new SampleSearchDto();
        sampleSearchDto.setSampleId(sampleId);
        sampleSearchDto.setPage(page);
        sampleSearchDto.setLimit(limit);
        sampleSearchDto.setDesensitize(desensitize);
        sampleSearchDto.setCompositeType(compositeType);
        sampleSearchDto.setReqDataLabels(reqDataLabels);
        sampleSearchDto.setRspDataLabels(rspDataLabels);
        sampleSearchDto.setApiUrl(apiUrl);
        sampleSearchDto.setUri(uri);


        try {
            if (DataUtil.isNotEmpty(compositeType)) {
                //适用于资产定义页面的接口查看样例
                //outputDto = sampleEventService.getSampleListByResource(page, limit, uri, apiUrl, httpApiSnapshotLimit, desensitize, compositeType);
                outputDto = sampleEventService.getSampleListByResource(sampleSearchDto);

            } else {
               // outputDto = sampleEventService.getHttpApiSampleList(page, limit, uri, apiUrl, httpApiSnapshotLimit, desensitize, reqDataLabels, rspDataLabels);
                outputDto=sampleEventService.getHttpApiSampleList(sampleSearchDto);
            }
        } catch (Exception e) {
           logger.error("getSampleList error:",e);
           return HttpResponseUtil.success(outputDto);
        }
        for (SampleEventDto sampleEventDto : outputDto.getRows()) {
            sampleEventDto.setReq(null);
            sampleEventDto.setRsp(null);
            sampleEventDto.setReqDataLabels(null);
            sampleEventDto.setRspDataLabels(null);
        }
        return HttpResponseUtil.success(outputDto);
    }


    @ApiOperation(value = "取消脱敏查看样例")
    @RequestMapping(value = "getSampleListCancelDesensitize", method = RequestMethod.POST)
    @Permissions(value = {
            @Permission(type = ResourceEnum.ACTION,value = "WRITE"),
            @Permission(type = ResourceEnum.FUNCTION,value = "sample:sampleList:cancelDesensitize")
    },logical= Logical.OR)
    public String getSampleListCancelDesensitize(@RequestBody SampleSearchDto sampleSearchDto){
        ListOutputDto<SampleEventDto> outputDto = new ListOutputDto<>();
        try {
            if (DataUtil.isNotEmpty(sampleSearchDto.getCompositeType())) {
                //适用于资产定义页面的接口查看样例
                outputDto = sampleEventService.getSampleListByResource(sampleSearchDto);
            } else {
                outputDto=sampleEventService.getHttpApiSampleList(sampleSearchDto);
            }
        } catch (Exception e) {
            logger.error("getSampleList error:",e);
            return HttpResponseUtil.success(outputDto);
        }
        for (SampleEventDto sampleEventDto : outputDto.getRows()) {
            sampleEventDto.setReq(null);
            sampleEventDto.setRsp(null);
            sampleEventDto.setReqDataLabels(null);
            sampleEventDto.setRspDataLabels(null);
        }
        return HttpResponseUtil.success(outputDto);
    }











    @ApiOperation(value = "获取资产定义模块-接口样例查看")
    @RequestMapping(value = "getSampleListByResource", method = RequestMethod.GET)
    public String getSampleListByResource(
            @RequestParam(value = "page", required = true) Integer page,
            @RequestParam(value = "limit", required = true) Integer limit,
            @RequestParam(value = "apiUrl", required = false) String apiUrl,
            @RequestParam(value = "httpApiSnapshotLimit", required = false, defaultValue = "4") Integer httpApiSnapshotLimit,
            @RequestParam(value = "desensitize", required = false, defaultValue = "false") Boolean desensitize,
            @RequestParam(value = "compositeType", required = true) Integer compositeType

    ) {
        logger.warn("apiUrl:{},compositeType:{}", apiUrl, compositeType);
        ListOutputDto outputDto = sampleEventService.getSampleListByResource(page, limit, null, apiUrl, httpApiSnapshotLimit, desensitize, compositeType);
        return HttpResponseUtil.success(outputDto);
    }


    @ApiOperation(value = "获取快照接口资源列表")
    @RequestMapping(value = "getApiSnapshotSampleList.do", method = RequestMethod.GET)
    public String getApiSnapshotSampleList(
            @RequestParam(value = "page", required = true) Integer page,
            @RequestParam(value = "limit", required = true) Integer limit,
            @RequestParam(value = "uri", required = false) String uri,
            @RequestParam(value = "apiUrl", required = false) String apiUrl,
            @RequestParam(value = "startTimestamp", required = true) Long startTimestamp,
            @RequestParam(value = "desensitize", required = false, defaultValue = "false") Boolean desensitize,
            @RequestParam(value = "reqDataLabels", required = false) List<String> reqDataLabels,
            @RequestParam(value = "rspDataLabels", required = false) List<String> rspDataLabels
    ) {
        ListOutputDto outputDto = sampleEventService.getApiSnapshotSampleList(page, limit, uri, apiUrl, startTimestamp, desensitize, reqDataLabels, rspDataLabels);
        return HttpResponseUtil.success(outputDto);
    }

    @RequestMapping(value = "getSampleById.do", method = RequestMethod.GET)
    public String getSampleById(
            @RequestParam(value = "id") String id,
            @RequestParam(value = "desensitize", required = false, defaultValue = "false") Boolean desensitize
    ) {
        SampleEventDto sampleEventDto = sampleEventService.getSampleById(id, desensitize);
        return HttpResponseUtil.success(sampleEventDto);
    }

    @RequestMapping(value = "getSampleByEventFilterPluginId.do", method = RequestMethod.GET)
    public String getSampleByEventFilterPluginId(
            @RequestParam(value = "httpEventId") String httpEventId,
            @RequestParam(value = "eventFilterPluginId") String eventFilterPluginId,
            @RequestParam(value = "desensitize", required = false, defaultValue = "false") Boolean desensitize
    ) {
        try {
            SampleEventDto sampleEventDto = sampleEventService.getSampleByEventFilterPluginId(httpEventId, eventFilterPluginId, desensitize);
            return HttpResponseUtil.success(sampleEventDto);
        } catch (Exception e) {
            logger.error("查询样例错误", e);
            return HttpResponseUtil.error("查询样例错误");
        }
    }

    @RequestMapping(value = "getSampleByEventFilterId", method = RequestMethod.GET)
    public String getSampleByEventFilterId(
            @RequestParam(value = "id") String id,
            @RequestParam(value = "desensitize", required = false, defaultValue = "false") Boolean desensitize
    ) {
        try {
            SampleEventDto sampleEventDto = sampleEventService.getSampleByEventFilterId(id, desensitize);
            return HttpResponseUtil.success(sampleEventDto);
        } catch (Exception e) {
            logger.error("查询样例错误", e);
            return HttpResponseUtil.error("查询样例错误");
        }
    }


    @RequestMapping(value = "fileView.do", method = RequestMethod.GET)
    public String fileView(
            @RequestParam(value = "fileId") String fileId
    ) {
        String ret = sampleEventService.fileView(fileId);
        FileContent result = new FileContent();
        result.setContent(ret);
        return HttpResponseUtil.success(result);
    }

    @RequestMapping(value = "fileDown.do", method = RequestMethod.GET)
    public void fileDown(
            @RequestParam(value = "fileId") String fileId,
            @RequestParam(value = "timestamp") Long timestamp,
            @RequestParam(value = "fileName") String fileName,
            @RequestParam(value = "fileType",required = false,defaultValue = "") String fileType,
            HttpServletRequest request,
            HttpServletResponse response
    ) {
        sampleEventService.fileDown(timestamp,fileId, fileName,fileType, request, response);
    }



    @RequestMapping(value = "batchTestByHttpApi.do", method = RequestMethod.POST)
    public String batchTestByHttpApi(
            @RequestParam(value = "labelConfigStr", defaultValue = "") String labelConfigStr,
            @RequestParam(value = "page", defaultValue = "1") Integer page,
            @RequestParam(value = "limit", defaultValue = "5") Integer limit,
            @RequestParam(value = "uri", required = false) String uri

    ) {
        ListOutputDto<SampleEventBatchTestDto> batchTestResults = new ListOutputDto();

        List<SampleEventBatchTestDto> sampleEventBatchTestDtos = new ArrayList<>();

        List<HttpApiResource.LabelConfig> labelConfigs = JSONArray.parseArray(labelConfigStr, HttpApiResource.LabelConfig.class);
        ListOutputDto<HttpApiSample> httpEvents = sampleEventService.getHttpEventSampleList(page, limit, uri);

        if (httpEvents.getRows().size() > 0) {
            sampleEventBatchTestDtos = sampleEventService.httpEvent2TestDto(httpEvents.getRows(), labelConfigs);
        }

        batchTestResults.setTotalCount(httpEvents.getTotalCount());
        batchTestResults.setRows(sampleEventBatchTestDtos);

        return HttpResponseUtil.success(batchTestResults);
    }

    @ApiIgnore
    @Deprecated
    @RequestMapping(value = "extractContentByLabels.do", method = RequestMethod.POST)
    public String extractContent(
            HttpServletRequest request,
            @RequestParam(value = "content", defaultValue = "") String content,
            @RequestParam(value = "labels", defaultValue = "") List<String> labels,
            @RequestParam(value = "accuracy", defaultValue = "HIGH") String accuracy,
            @RequestParam(value = "desensitize", defaultValue = "false") Boolean desensitize,
            @RequestParam(value = "originValueDesensiKey", defaultValue = "") String originValueDesensiKey
    ) {
        List<ExtractValueDto> extractValues = sampleEventService.extractContentByLabels(
                request.getParameterMap().get("content") != null ? String.join("", request.getParameterMap().get("content")) : "",
                labels, accuracy, desensitize, originValueDesensiKey);
        return HttpResponseUtil.success(extractValues);
    }

    @ApiOperation("根据标签提取指定内容")
    @ApiImplicitParam(name = "dto", dataTypeClass = ExtractDto.class, paramType = "body", example = "{\"labels\": [\"personName\"],\"desensitize\": false,\"originValueDesensiKey\": \"abc\"}")
    @PostMapping(value = "extractByLabels")
    public String extractByLabels(@RequestBody ExtractDto dto) {
        List<ExtractValueDto> extractValues;
        try {
            extractValues = sampleEventService.extractContentByLabels(dto.getContent(), dto.getLabels(), dto.getAccuracy(), dto.getDesensitize(), dto.getOriginValueDesensiKey());
            return HttpResponseUtil.success(extractValues);
        } catch (Exception e) {
            logger.error("extract error", e);
            return HttpResponseUtil.error();
        }
    }

    @RequestMapping(value = "extractContentByLabelConfigs.do", method = RequestMethod.POST)
    public String extractContentByLabelConfigs(
            HttpServletRequest request,
            @RequestParam(value = "content", defaultValue = "") String content,
            @RequestParam(value = "labelConfigStr", defaultValue = "") String labelConfigStr,
            @RequestParam(value = "desensitize", defaultValue = "false") Boolean desensitize,
            @RequestParam(value = "originValueDesensiKey", defaultValue = "") String originValueDesensiKey
    ) {
        List<HttpApiResource.LabelConfig> labelConfigs = JSONArray.parseArray(labelConfigStr, HttpApiResource.LabelConfig.class);

        List<Map<String, List<ExtractValueDto>>> allLabelValues = sampleEventService.extractContentByLabelConfigs(
                request.getParameterMap().get("content") != null ? String.join("", request.getParameterMap().get("content")) : "",
                labelConfigs, desensitize, originValueDesensiKey);
        return HttpResponseUtil.success(allLabelValues);
    }

    @ApiOperation("根据标签配置提取指定内容")
    @ApiImplicitParam(name = "dto", dataTypeClass = ExtractDto.class, paramType = "body", example = "{\"labels\": [\"personName\"],\"desensitize\": false,\"originValueDesensiKey\": \"abc\"}")
    @PostMapping(value = "extractByLabelConfigs")
    public String extractByLabelConfigs(@RequestBody ExtractDto dto) {
        List<Map<String, List<ExtractValueDto>>> allLabelValues = sampleEventService.extractContentByLabelConfigs("", dto.getLabelConfigs(), dto.getDesensitize(), dto.getOriginValueDesensiKey());
        return HttpResponseUtil.success(allLabelValues);
    }

    @RequestMapping(value = "getSampleLabels.do", method = RequestMethod.GET)
    public String getSampleLabels(
            @RequestParam(value = "uri", defaultValue = "") String uri
    ) {
        uri = StringEscapeUtils.unescapeHtml(uri);
        Map<String, Set<TreeSet<String>>> res = sampleEventService.getSampleLabels(uri);
        return HttpResponseUtil.success(res);
    }

    /**
     * 导出样例
     *
     * @param
     * @return
     * @Comments: <对此方法的描述，可以引用系统设计中的描述>
     * <AUTHOR> [<EMAIL>]
     * @since 2021-09-02 9:56
     */
    @OperateLog(value = "导出样例", type = "导出", module = "样例")
    @RequestMapping(value = "export.do", method = RequestMethod.POST)
    @ApiOperation(value = "导出样例")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "appUri", value = "应用统一资源标识", required = true, paramType = "query", dataType = "String")
    })
    public String export(@RequestParam(value = "appUri", required = false) String appUri,
                         @RequestParam(value = "formJson", required = false) String formJson) {
        logger.warn("样例Controller》导出样例");
        try {

            ExportSampleDto exportSampleDto = new ExportSampleDto();
            HttpApiSearchDto httpApiSearchDto = new HttpApiSearchDto();
            httpApiSearchDto.setAppUri(appUri);
            exportSampleDto.setHttpApiSearchDto(httpApiSearchDto);
            if (productType.equals(ProductTypeEnum.wph.name())) {
                JSONObject jsonObject = JSONObject.parseObject(formJson);
                sampleEventService.exportSampleEvent(appUri, jsonObject.getString("applicant_id"), jsonObject.getString("tags"), jsonObject.getString("reasons"), jsonObject.getString("usage"), "/Users/<USER>/Downloads/test/", exportSampleDto);

            }else {
                sampleEventService.exportSampleEvent(exportSampleDto, "/Users/<USER>/Downloads/test/");

            }
        } catch (Exception e) {
            logger.error("导出样例", e);
            return HttpResponseUtil.error(e.getMessage());
        }
        return HttpResponseUtil.success();
    }

    @ApiOperation(value = "获取接口样例缩略列表")
    @RequestMapping(value = "getThumbSampleList", method = RequestMethod.GET)
    @ApiImplicitParams({
            @ApiImplicitParam(name = "page", value = "页码", required = true, paramType = "query", dataType = "int"),
            @ApiImplicitParam(name = "limit", value = "每页数量", required = true, paramType = "query", dataType = "int"),
            @ApiImplicitParam(name = "reqDataLabels", value = "请求数据标签", required = false, paramType = "query", dataType = "String"),
            @ApiImplicitParam(name = "rspDataLabels", value = "返回数据标签", required = false, paramType = "query", dataType = "String"),
            @ApiImplicitParam(name = "uri", value = "接口uri", required = true, paramType = "query", dataType = "String")
    })
    public ResponseVo<ListOutputDto<ThumbSampleDto>> getThumbSampleList(
            @RequestParam(value = "page", required = true) Integer page,
            @RequestParam(value = "limit", required = true) Integer limit,
            @RequestParam(value = "reqDataLabels", required = false) List<String> reqDataLabels,
            @RequestParam(value = "rspDataLabels", required = false) List<String> rspDataLabels,
            @RequestParam(value = "uri", required = false) String uri,
            @RequestParam(value = "apiUrl", required = false) String apiUrl,
            @RequestParam(value = "compositeType", required = false) Integer compositeType

    ) {
        uri = StringEscapeUtils.unescapeHtml(uri);
        apiUrl = StringEscapeUtils.unescapeHtml(apiUrl);
        ListOutputDto<ThumbSampleDto> outputDto = new ListOutputDto<>();
        try {
            outputDto = sampleEventService.getThumbSampleList(uri, page, limit, reqDataLabels, rspDataLabels, apiUrl, compositeType);
        } catch (Exception e) {
           logger.error("getThumbSampleList error:",e);
        }
        return ResponseVo.ok(outputDto);
    }

    @ApiOperation(value = "提取数据标签内容")
    @RequestMapping(value = "extractContent", method = RequestMethod.GET)
    @ApiImplicitParams({
            @ApiImplicitParam(name = "sampleId", value = "样例id", required = true, paramType = "query", dataType = "String"),
            @ApiImplicitParam(name = "desensitize", value = "是否去除敏感信息", required = false, paramType = "query", dataType = "Boolean"),
            @ApiImplicitParam(name = "position", value = "位置", required = true, paramType = "query", example = "REQ/RSP", dataType = "string"),
            @ApiImplicitParam(name = "sampleType", value = "样例类型(接口样例|风险样例|过滤事件样例)", required = false, paramType = "query", example = "API/RISK/FILTER", dataType = "string"),
    })
    public String extractContent(
            @RequestParam(value = "sampleId", defaultValue = "") String sampleId,
            @RequestParam(value = "desensitize", defaultValue = "false", required = false) Boolean desensitize,
            @RequestParam(value = "position", defaultValue = "") String position,
            @RequestParam(value = "location") Integer location,
            @RequestParam(value = "sampleType", required = false) String sampleType
    ) {
        SampleEventConfig.LocationEnum locationEnum = null;
        if (location != null) {
            locationEnum = SampleEventConfig.LocationEnum.valueOf(location);
        }
        List<ExtractValueDto> extractValues = sampleEventService.extractContent(sampleId, desensitize, SourceEnum.valueOf(position), sampleType, locationEnum);
        return HttpResponseUtil.success(extractValues);
    }

    /**
     * req-raw转换器方法
     *
     * @param sampleId         源请求存放相关id
     * @param reqConverterEnum 需要转换的类型
     * @return
     */
    @ApiOperation(value = "req-raw转换器方法")
    @RequestMapping(value = "getReqConvertedInfo.do", method = RequestMethod.GET)
    public String getReqConvertedInfo(
            @RequestParam(value = "sampleId", defaultValue = "") String sampleId,
            @RequestParam(value = "clickhousePrimary", defaultValue = "") String clickhousePrimary,
            @RequestParam(value = "filterSampleId", defaultValue = "") String filterSampleId,
            @RequestParam(value = "riskSampleId", defaultValue = "") String riskSampleId,
            @RequestParam(value = "collectionName", defaultValue = "http_defined_event") String collectionName,
            @RequestParam(value = "reqConverterEnum", required = true) ReqConverterEnum reqConverterEnum,
            @RequestParam(value = "sampleSourceType", required = true) SampleSourceTypeEnum sampleSourceType
    ) {
        String reqConvertedInfo = "";
        switch (sampleSourceType) {
            case NORMAL:
                reqConvertedInfo = sampleEventService.getReqConvertedInfoForSampleId(sampleId, reqConverterEnum);
                break;
            case FILTER:
                reqConvertedInfo = sampleEventService.getReqConvertedInfoForFilterHttpEventId(filterSampleId, reqConverterEnum);
                break;
            case CK:
                reqConvertedInfo = sampleEventService.getReqConvertedInfoForClickhousePrimary(clickhousePrimary, collectionName, reqConverterEnum);
                break;
            case RISK:
                reqConvertedInfo = sampleEventService.getReqConvertedInfoForRiskSampleId(sampleId, reqConverterEnum);
                break;
            default:
                reqConvertedInfo = "";
        }

        return HttpResponseUtil.success(reqConvertedInfo);
    }

    /**
     * encode解码器方法
     *
     * @param encodeStr          编码信息
     * @param decodeInfoTypeEnum 解码类型
     * @return
     */
    @ApiOperation(value = "encode解码器方法")
    @RequestMapping(value = "getDecodedInfo.do", method = RequestMethod.GET)
    public String getDecodedInfo(
            @RequestParam(value = "encodeStr", defaultValue = "") String encodeStr,
            @RequestParam(value = "reqConverterEnum", required = true) DecodeInfoTypeEnum decodeInfoTypeEnum
    ) {
        String decodeInfo = sampleEventService.getDecodedInfo(encodeStr, decodeInfoTypeEnum);
        return HttpResponseUtil.success(decodeInfo);
    }


}
