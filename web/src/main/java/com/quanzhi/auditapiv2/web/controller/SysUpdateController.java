package com.quanzhi.auditapiv2.web.controller;

import com.quanzhi.audit.mix.permission.annotation.Permission;
import com.quanzhi.audit.mix.permission.domain.enums.ResourceEnum;
import com.quanzhi.audit.mix.upload.dto.MultipartUploadDTO;
import com.quanzhi.audit.mix.upload.dto.UploadResultDTO;
import com.quanzhi.auditapiv2.common.dal.entity.SysUpdateLog;
import com.quanzhi.auditapiv2.common.util.dto.ErrorCode;
import com.quanzhi.auditapiv2.common.util.dto.ResponseVo;
import com.quanzhi.auditapiv2.common.util.utils.DataUtil;
import com.quanzhi.auditapiv2.common.util.utils.HttpResponseUtil;
import com.quanzhi.auditapiv2.core.service.manager.dto.upgrade.UploadFile;
import com.quanzhi.auditapiv2.core.service.manager.web.ISysUpdateLogService;
import com.quanzhi.auditapiv2.core.service.manager.web.ISysUpdateService;
import com.quanzhi.auditapiv2.web.common.annotation.OperateLog;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;

/**
 *
 * 《系统升级Controller》
 *
 *
 * @Project:
 * @Module ID:
 * @Comments:
 * @JDK version used:      <JDK1.8>
 * <AUTHOR> [<EMAIL>]
 * @since 2020-04-08-上午9:56:10
 */
@RestController
@RequestMapping("/api/sysUpdate")
public class SysUpdateController {
    private static final Logger logger = LoggerFactory.getLogger(SysUpdateController.class);

    //系统升级service
    private final ISysUpdateService sysUpdateServiceImpl;

    //系统升级日志service
    private final ISysUpdateLogService sysUpdateLogServiceImpl;

    public SysUpdateController(ISysUpdateService sysUpdateServiceImpl,
                               ISysUpdateLogService sysUpdateLogServiceImpl) {
        this.sysUpdateServiceImpl = sysUpdateServiceImpl;
        this.sysUpdateLogServiceImpl = sysUpdateLogServiceImpl;
    }

    /**
     * 上传升级包
     * @Comments:  <对此方法的描述，可以引用系统设计中的描述>
     * <AUTHOR> [<EMAIL>]
     * @since 2020-04-08 9:56
     * @param
     * @return
     */
    @OperateLog(module = "系统升级", type = "上传", value = "上传升级包")
    @RequestMapping(value = "/upload.do", method = RequestMethod.POST)
    @ApiOperation(value = "上传升级包")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "multipartFile", value = "升级包", required = true, paramType = "query", dataType = "String")
    })
    @Permission(type = ResourceEnum.ACTION, value = "WRITE")
    public String upload(@RequestParam(value = "multipartFile", defaultValue = "") MultipartFile multipartFile) {
        logger.warn("系统升级Controller》上传升级包");

        try {
            SysUpdateLog sysUpdateLog = sysUpdateLogServiceImpl.getSysUpdateLog();
            Integer decryptStatus = sysUpdateServiceImpl.getDecryptStatus();
            if(DataUtil.isNotEmpty(sysUpdateLog) || (decryptStatus != null && decryptStatus == 1)) {
                return HttpResponseUtil.error("上传失败，有正在进行中的升级任务");
            }
            File tempFile = sysUpdateServiceImpl.upload(multipartFile);
            sysUpdateServiceImpl.decrypt(tempFile);
            return HttpResponseUtil.success();
        }catch (Exception e){
            logger.error("上传升级包", e);
            return HttpResponseUtil.error(e.getMessage());
        }
    }

    /**
     * 升级
     * @Comments:  <对此方法的描述，可以引用系统设计中的描述>
     * <AUTHOR> [<EMAIL>]
     * @since 2020-04-08 9:56
     * @param
     * @return
     */
    @OperateLog(module = "系统升级", type = "处理", value = "系统升级")
    @RequestMapping(value = "/update.do", method = RequestMethod.POST)
    @ApiOperation(value = "升级")
    @Permission(type = ResourceEnum.ACTION, value = "WRITE")
    public String update() {
        logger.warn("系统升级Controller》执行升级");

        try {
            SysUpdateLog sysUpdateLog = sysUpdateLogServiceImpl.getSysUpdateLog();
            if(DataUtil.isNotEmpty(sysUpdateLog)) {
                return HttpResponseUtil.error("升级失败，有正在进行中的升级任务");
            }
            return HttpResponseUtil.success(sysUpdateServiceImpl.update());
        }catch (Exception e){
            logger.error("执行升级", e);
            return HttpResponseUtil.error(e.getMessage());
        }
    }

    /**
     * 查询升级日志
     * @Comments:  <对此方法的描述，可以引用系统设计中的描述>
     * <AUTHOR> [<EMAIL>]
     * @since 2020-04-08 9:56
     * @param
     * @return
     */
    @RequestMapping(value = "/getSysUpdateLog.do", method = RequestMethod.POST)
    @ApiOperation(value = "查询升级日志")
    public String getSysUpdateLog() {
        logger.warn("系统升级Controller》查询升级日志");

        try {
            return HttpResponseUtil.success(sysUpdateServiceImpl.getSysUpdateLog());
        }catch (Exception e){
            logger.error("查询升级日志", e);
            return HttpResponseUtil.error(e.getMessage());
        }
    }

    /**
     * 查询升级包解密状态
     * @Comments:  <对此方法的描述，可以引用系统设计中的描述>
     * <AUTHOR> [<EMAIL>]
     * @since 2020-04-08 9:56
     * @param
     * @return
     */
    @RequestMapping(value = "/getDecryptStatus.do", method = RequestMethod.POST)
    @ApiOperation(value = "查询升级包解密状态（1-进行中，2-成功，3-失败）")
    public String getDecryptStatus() {
        logger.warn("系统升级Controller》查询升级包解密状态");

        try {
            return HttpResponseUtil.success(sysUpdateServiceImpl.getDecryptStatus());
        }catch (Exception e){
            logger.error("查询升级包解密状态", e);
            return HttpResponseUtil.error(e.getMessage());
        }
    }

    @PostMapping(value = "/uploadPart")
    @ApiOperation(value ="分块上传文件")
    @Permission(type = ResourceEnum.ACTION, value = "WRITE")
    public ResponseVo<UploadResultDTO> upload(MultipartUploadDTO fileUploadRequestDTO) throws Exception {
        SysUpdateLog sysUpdateLog = sysUpdateLogServiceImpl.getSysUpdateLog();
        Integer decryptStatus = sysUpdateServiceImpl.getDecryptStatus();
        if(DataUtil.isNotEmpty(sysUpdateLog) || (decryptStatus != null && decryptStatus == 1)) {
            return ResponseVo.error(ErrorCode.UPGRADE_IN_PROGRESS_ERROR);
        }
        UploadFile file = sysUpdateServiceImpl.uploadPart(fileUploadRequestDTO);
        if (file.getUploadResultDTO().getProgress() == 100) {
            sysUpdateServiceImpl.decrypt(file.getFile());
        }
        return ResponseVo.ok(file.getUploadResultDTO());
    }

}