package com.quanzhi.auditapiv2.web.controller;

import com.quanzhi.auditapiv2.common.util.dto.ListOutputDto;
import com.quanzhi.auditapiv2.common.util.utils.DataUtil;
import com.quanzhi.auditapiv2.common.util.utils.HttpResponseUtil;
import com.quanzhi.auditapiv2.core.service.manager.web.IHttpApiSampleService;
import com.quanzhi.auditapiv2.core.service.manager.web.IHttpApiSnapshotService;
import com.quanzhi.metabase.core.model.http.HttpApiSample;
import com.quanzhi.metabase.core.model.http.HttpApiSnapshot;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

/**
 *
 * 《接口快照Controller》
 *
 *
 * @Project:
 * @Module ID:
 * @Comments:
 * @JDK version used:      <JDK1.8>
 * <AUTHOR> [<EMAIL>]
 * @since 2020-04-08-上午9:56:10
 */
@RestController
@RequestMapping("/api/httpApiSnapshot")
public class HttpApiSnapshotController {
    private Logger logger = LoggerFactory.getLogger(HttpApiSnapshotController.class);

    //接口快照service
    @Autowired
    private IHttpApiSnapshotService httpApiSnapshotServiceImpl;

    //接口样例service
    @Autowired
    private IHttpApiSampleService httpApiSampleServiceImpl;

    /**
     * 查询接口快照列表(分页)
     * @Comments:  <对此方法的描述，可以引用系统设计中的描述>
     * <AUTHOR> [<EMAIL>]
     * @since 2020-04-08 9:56
     * @param
     * @return
     */
    @RequestMapping(value = "listData.do", method = RequestMethod.POST)
    @ApiOperation(value = "查询接口快照列表(分页)")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "appId", value = "应用id", required = true, paramType = "query", dataType = "String"),
            @ApiImplicitParam(name = "startDate", value = "开始时间", required = true, paramType = "query", dataType = "long"),
            @ApiImplicitParam(name = "endDate", value = "结束时间", required = true, paramType = "query", dataType = "long"),
            @ApiImplicitParam(name = "page", value = "当前页", required = true, paramType = "query", dataType = "int"),
            @ApiImplicitParam(name = "limit", value = "每页显示条数", required = true, paramType = "query", dataType = "int")
    })
    public String listData(String appId, Long startDate, Long endDate, Integer page, Integer limit) {
        logger.warn("接口快照Controller》查询接口快照列表(分页)");

        ListOutputDto<HttpApiSnapshot> data = null;
        try {
            data = httpApiSnapshotServiceImpl.getHttpApiSnapshotList(appId, startDate, endDate, page, limit);
        } catch (Exception e) {
            logger.error("查询接口快照列表(分页)", e);
            return HttpResponseUtil.error(e.getMessage());
        }
        return HttpResponseUtil.success(data);
    }

    /**
     * id查询接口快照详情
     * @Comments:  <对此方法的描述，可以引用系统设计中的描述>
     * <AUTHOR> [<EMAIL>]
     * @since 2020-04-08 9:56
     * @param
     * @return
     */
    @RequestMapping(value = "getById.do", method = RequestMethod.POST)
    @ApiOperation(value = "id查询接口快照详情")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "接口快照id", required = true, paramType = "query", dataType = "String")
    })
    public String getById(String id) {
        logger.warn("接口快照Controller》id查询接口快照详情");

        try {
            if(DataUtil.isNotEmpty(id)){
                HttpApiSnapshot httpApiSnapshot = httpApiSnapshotServiceImpl.getHttpApiSnapshotById(id);
                return HttpResponseUtil.success(httpApiSnapshot);
            }else{
                return HttpResponseUtil.error("id不得为空");
            }
        } catch (Exception e) {
            logger.error("id查询接口快照详情", e);
            return HttpResponseUtil.error(e.getMessage());
        }
    }

    /**
     * 接口快照id查询接口样例列表(分页)
     * @Comments:  <对此方法的描述，可以引用系统设计中的描述>
     * <AUTHOR> [<EMAIL>]
     * @since 2020-04-08 9:56
     * @param
     * @return
     */
    @RequestMapping(value = "getHttpApiSampleList.do", method = RequestMethod.POST)
    @ApiOperation(value = "接口快照id查询接口样例列表(分页)")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "接口快照id", required = true, paramType = "query", dataType = "String"),
            @ApiImplicitParam(name = "page", value = "当前页", required = true, paramType = "query", dataType = "int"),
            @ApiImplicitParam(name = "limit", value = "每页显示条数", required = true, paramType = "query", dataType = "int")
    })
    public String getHttpApiSampleList(String id, Integer page, Integer limit) {
        logger.warn("接口快照Controller》接口快照id查询接口样例列表(分页)");

        ListOutputDto<HttpApiSample> data = null;
        try {
            data = httpApiSampleServiceImpl.getHttpApiSampleListByHttpApiSnapshotId(id, page, limit);
        } catch (Exception e) {
            logger.error("接口快照id查询接口样例列表(分页)", e);
            return HttpResponseUtil.error(e.getMessage());
        }
        return HttpResponseUtil.success(data);
    }
}
