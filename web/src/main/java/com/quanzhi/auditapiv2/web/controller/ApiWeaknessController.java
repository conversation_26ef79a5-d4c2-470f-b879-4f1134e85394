package com.quanzhi.auditapiv2.web.controller;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.nacos.api.config.annotation.NacosValue;
import com.quanzhi.audit.mix.permission.annotation.DataPermission;
import com.quanzhi.audit.mix.permission.annotation.Permission;
import com.quanzhi.audit.mix.permission.domain.enums.ResourceEnum;
import com.quanzhi.auditapiv2.common.dal.dto.ApiWeaknessDto;
import com.quanzhi.auditapiv2.common.dal.dto.DealWeaknessDto;
import com.quanzhi.auditapiv2.common.dal.dto.ExporTitleFieldDto;
import com.quanzhi.auditapiv2.common.dal.dto.common.CommonGroupDto;
import com.quanzhi.auditapiv2.common.dal.enums.ProductTypeEnum;
import com.quanzhi.auditapiv2.common.util.constant.ConfigContants;
import com.quanzhi.auditapiv2.common.util.dto.ListOutputDto;
import com.quanzhi.auditapiv2.common.util.dto.ResponseVo;
import com.quanzhi.auditapiv2.common.util.entity.ExportTaskType;
import com.quanzhi.auditapiv2.common.util.utils.ConstantUtil;
import com.quanzhi.auditapiv2.common.util.utils.DataUtil;
import com.quanzhi.auditapiv2.common.util.utils.HttpResponseUtil;
import com.quanzhi.auditapiv2.core.service.SysLogService;
import com.quanzhi.auditapiv2.core.service.manager.dto.SampleEventDto;
import com.quanzhi.auditapiv2.core.service.manager.dto.weakness.WeaknessStatistics;
import com.quanzhi.auditapiv2.core.service.manager.web.IApiWeaknessLogService;
import com.quanzhi.auditapiv2.core.service.manager.web.IApiWeaknessService;
import com.quanzhi.auditapiv2.web.common.annotation.DirectiveSync;
import com.quanzhi.auditapiv2.web.common.annotation.OperateLog;
import com.quanzhi.metabase.core.model.http.weakness.ApiWeakness;
import com.quanzhi.metabase.core.model.http.weakness.State;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 《接口弱点Controller》
 *
 * <AUTHOR> [<EMAIL>]
 * @Project:
 * @Module ID:
 * @Comments:
 * @JDK version used:      <JDK1.8>
 * @since 2020-08-28-下午16:52:18
 */
@RestController
@RequestMapping("/api/apiWeakness")
public class ApiWeaknessController {
    private Logger logger = LoggerFactory.getLogger(ApiWeaknessController.class);

    //接口弱点service
    @Autowired
    private IApiWeaknessService apiWeaknessServiceImpl;

    private final IApiWeaknessLogService apiWeaknessLogService;

    private final SysLogService sysLogService;

    @NacosValue(value = "${product.type:api}", autoRefreshed = true)
    private String productType;

    public ApiWeaknessController(IApiWeaknessLogService apiWeaknessLogService, SysLogService sysLogService) {
        this.apiWeaknessLogService = apiWeaknessLogService;
        this.sysLogService = sysLogService;
    }

    /**
     * 查询接口弱点列表(分页)
     *
     * @param
     * @return
     * @Comments: <对此方法的描述，可以引用系统设计中的描述>
     * <AUTHOR> [<EMAIL>]
     * @since 2020-08-28 16:52
     */
    @RequestMapping(value = "listData.do", method = RequestMethod.POST)
    @ApiOperation(value = "查询接口弱点列表(分页)")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "params", value = "检索条件", required = false, paramType = "query", dataType = "String"),
            @ApiImplicitParam(name = "field", value = "排序字段", required = false, paramType = "query", dataType = "String"),
            @ApiImplicitParam(name = "sort", value = "1正序，2倒序", required = false, paramType = "query", dataType = "int"),
            @ApiImplicitParam(name = "page", value = "当前页", required = true, paramType = "query", dataType = "int"),
            @ApiImplicitParam(name = "limit", value = "每页显示条数", required = true, paramType = "query", dataType = "int")
    })
    @DataPermission
    public ResponseVo<ListOutputDto<ApiWeaknessDto>> getApiWeaknessList(@RequestParam(value = "params", defaultValue = "") String params, String field, Integer sort, Integer page, Integer limit) {
        logger.warn("接口弱点Controller》查询接口弱点列表(分页)");

        ListOutputDto<ApiWeaknessDto> data = null;
        try {
            Map<String, Object> map = new HashMap<String, Object>();
            if (DataUtil.isNotEmpty(params)) {
                map = (Map<String, Object>) JSON.parse(params);
            }
            data = apiWeaknessServiceImpl.getApiWeaknessList(map, field, sort, page, limit, true);
        } catch (Exception e) {
            logger.error("查询接口弱点列表(分页)", e);
        }
        return ResponseVo.ok(data);
    }

    @DirectiveSync(remark = "处理弱点", needNotify = true)
    @OperateLog(value = "同步弱点", type = "处理", module = "弱点")
    @RequestMapping(value = "dealWeakness", method = RequestMethod.POST)
    @ApiOperation(value = "处理弱点")
    @Permission(type = ResourceEnum.ACTION, value = "WRITE")
    public ResponseVo<String> dealWeakness(@RequestBody DealWeaknessDto dealWeaknessDto) {
        try {
            if (DataUtil.isEmpty(dealWeaknessDto)) {
                return ResponseVo.error("参数为空!", 233);
            }
            //在service内处理
            String str = apiWeaknessServiceImpl.dealWeakness(dealWeaknessDto.getParams()
                    , dealWeaknessDto.getFields()
                    , dealWeaknessDto.getIsAllFields()
                    , dealWeaknessDto.getConfigIds());
            return ResponseVo.ok(str);
        } catch (Exception e) {
            return ResponseVo.error("处理出错：" + e.getMessage(), 233);
        }
    }

    @DirectiveSync(remark = "开启/关闭弱点状态自动检查")
    @RequestMapping(value = "openWeaknessAutoCheck", method = RequestMethod.POST)
    @ApiOperation(value = "开启/关闭弱点状态自动检查")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "isOpen", value = "开启(true)/关闭(false)弱点状态自动检查", required = true, paramType = "query", dataType = "Boolean")
    })
    @Permission(type = ResourceEnum.ACTION, value = "WRITE")
    public ResponseVo<String> openWeaknessAutoCheck(@RequestParam(value = "isOpen", defaultValue = "true") Boolean isOpen) {
        //修改对应配置项
        apiWeaknessServiceImpl.openWeaknessAutoCheck(isOpen);
        if (isOpen) {
            return ResponseVo.ok("已开启弱点自动学习！");
        } else {
            return ResponseVo.ok("已关闭弱点自动学习！");
        }
    }

    @RequestMapping(value = "selectWeaknessAutoCheck", method = RequestMethod.GET)
    @ApiOperation(value = "查询弱点状态自动检查开关")
    public ResponseVo<Boolean> selectWeaknessAutoCheck() {
        return ResponseVo.ok(apiWeaknessServiceImpl.selectWeaknessAutoCheck());
    }

    /**
     * 查询新发现弱点列表
     *
     * @param startTime
     * @param endTime
     * @param page
     * @param limit
     * @param field
     * @param sort
     * @return
     */
    @RequestMapping(value = "/getNewWeaknessList.do", method = RequestMethod.GET)
    public ResponseVo<ListOutputDto<ApiWeaknessDto>> getNewWeaknessList(@RequestParam(required = true) Long startTime, @RequestParam(required = true) Long endTime, @RequestParam(required = true, defaultValue = "1") Integer page, @RequestParam(required = true, defaultValue = "10") Integer limit, String field, Integer sort) {
        ListOutputDto<ApiWeaknessDto> data = null;
        try {
            data = apiWeaknessServiceImpl.getNewWeaknessList(startTime, endTime, page, limit, field, sort);
        } catch (Exception e) {
            logger.error("查询接口弱点列表(分页):{}", e);
            return ResponseVo.error(e.getMessage(), 233);
        }
        return ResponseVo.ok(data);
    }

    /**
     * 查询已修复弱点列表
     *
     * @param startTime
     * @param endTime
     * @param page
     * @param limit
     * @param field
     * @param sort
     * @return
     */
    @RequestMapping(value = "/getFixedWeaknessList.do", method = RequestMethod.GET)
    public ResponseVo<ListOutputDto<ApiWeaknessDto>> getFixedWeaknessList(@RequestParam(required = true) Long startTime, @RequestParam(required = true) Long endTime, @RequestParam(required = true, defaultValue = "1") Integer page, @RequestParam(required = true, defaultValue = "10") Integer limit, String field, Integer sort) {
        ListOutputDto<ApiWeaknessDto> data = null;
        try {
            data = apiWeaknessServiceImpl.getFixedWeaknessList(startTime, endTime, page, limit, field, sort);
        } catch (Exception e) {
            logger.error("查询接口弱点列表(分页):{}", e);
            return ResponseVo.error(e.getMessage(), 233);
        }
        return ResponseVo.ok(data);
    }

    /**
     * 查询待修复弱点列表
     *
     * @param startTime
     * @param endTime
     * @param page
     * @param limit
     * @param field
     * @param sort
     * @return
     */
    @RequestMapping(value = "/getPepairingWeaknessList.do", method = RequestMethod.GET)
    public ResponseVo<ListOutputDto<ApiWeaknessDto>> getPepairingWeaknessList(@RequestParam(required = true) Long startTime, @RequestParam(required = true) Long endTime, @RequestParam(required = true, defaultValue = "1") Integer page, @RequestParam(required = true, defaultValue = "10") Integer limit, String field, Integer sort) {
        ListOutputDto<ApiWeaknessDto> data = null;
        try {
            data = apiWeaknessServiceImpl.getPepairingWeaknessList(startTime, endTime, page, limit, field, sort);
        } catch (Exception e) {
            logger.error("查询接口弱点列表(分页):{}", e);
            return ResponseVo.error(e.getMessage(), 233);
        }
        return ResponseVo.ok(data);
    }
//    /**
//     * 新发现弱点数量
//     * @param startTime
//     * @param endTime
//     * @return
//     */
//    @RequestMapping(value = "/getNewWeakCount.do",method = RequestMethod.GET)
//    public String getNewWeakCount(@RequestParam(required = true) Long startTime,Long endTime){
//        Map<Integer,Integer> newWeakcount=apiWeaknessServiceImpl.getNewWeakCount(startTime,endTime);
//        return HttpResponseUtil.success(newWeakcount);
//    }
//
//    /**
//     * 已修复弱点数量
//     * @param startTime
//     * @param endTime
//     * @return
//     */
//    @RequestMapping(value = "/getFixedWeakCount.do",method = RequestMethod.GET)
//    public String getFixedWeakCount(@RequestParam(required = true) Long startTime,Long endTime){
//        Map<Integer, Integer> fixedCount = apiWeaknessServiceImpl.getFixedCount(startTime, endTime);
//        return HttpResponseUtil.success(fixedCount);
//    }
//
//    /**
//     * 待修复弱点数量
//     * @param startTime
//     * @param endTime
//     * @return
//     */
//    @RequestMapping(value = "/getPepairingWeakCount.do",method = RequestMethod.GET)
//    public String getPepairingWeakCount(@RequestParam(required = true) Long startTime,Long endTime){
//        Map<Integer, Integer> pepairingCount = apiWeaknessServiceImpl.getPepairingCount(startTime, endTime);
//        return HttpResponseUtil.success(pepairingCount);
//    }

    /**
     * 等级饼状图
     *
     * @param startTime
     * @param endTime
     * @return
     */
    @RequestMapping(value = "/getNewLevelAmount.do", method = RequestMethod.GET)
    public ResponseVo<Map<Integer, Long>> getNewLevelAmount(Long startTime, Long endTime, Long startDate, Long endDate, State state) {
        Map<Integer, Long> levelAmount = null;
        try {
            levelAmount = apiWeaknessServiceImpl.getNewLevelAmount(startTime, endTime, startDate, endDate, state);
        } catch (Exception e) {
            logger.error("等级比例饼状图：{}", e);
        }
        return ResponseVo.ok(levelAmount);
    }

    /**
     * 导出弱点报告
     *
     * @param startTime
     * @param endTime
     * @param page
     * @param limit
     * @param field
     * @param sort
     * @param dir
     * @param fileName
     * @return
     */
    @RequestMapping(value = "/exportWeaknessReport.do", method = RequestMethod.GET)
    @Permission(type = ResourceEnum.ACTION, value = "WRITE")
    public ResponseVo<String> exportWeaknessReport(@RequestParam(required = true) Long startTime, @RequestParam(required = true) Long endTime, @RequestParam(required = true, defaultValue = "1") Integer page, @RequestParam(required = true, defaultValue = "10000") Integer limit, String field, Integer sort, @RequestParam(required = true) String dir, @RequestParam(required = true) String fileName,
                                                   @RequestParam(required = false) String formJson) throws Exception {
        String weaknessReport;
        if (productType.equals(ProductTypeEnum.wph.name())) {
            JSONObject jsonObject = JSONObject.parseObject(formJson);
            weaknessReport = apiWeaknessServiceImpl.exportWeaknessReport(startTime, endTime, page, limit, field, sort,
                    jsonObject.getString("applicant_id"),
                    jsonObject.getString("tags"),
                    jsonObject.getString("reasons"),
                    jsonObject.getString("usage"),
                    dir, fileName);
        } else {
            weaknessReport = apiWeaknessServiceImpl.exportWeaknessReport(startTime, endTime, page, limit, field, sort, dir, fileName);
        }
        return ResponseVo.ok(weaknessReport);
    }

    /**
     * 类型饼状图
     *
     * @param startTime
     * @param endTime
     * @return
     */
    @RequestMapping(value = "/getNewTypeAmount.do", method = RequestMethod.GET)
    public ResponseVo<List<Map<String, Long>>> getNewTypeAmount(Long startTime, Long endTime, Long startDate, Long endDate, State state) {
        List<Map<String, Long>> typeAmount = null;
        try {
            typeAmount = apiWeaknessServiceImpl.getTypeAmount(startTime, endTime, startDate, endDate, state);
        } catch (Exception e) {
            logger.error("类型比例饼状图：{}", e);
        }
        return ResponseVo.ok(typeAmount);
    }

    @RequestMapping(value = "/getTypeGroup", method = RequestMethod.POST)
    public ResponseVo<List<Map<String, Long>>> getTypeGroup(String params) {
        try {
            Map<String, Object> map = new HashMap<String, Object>();
            if (DataUtil.isNotEmpty(params)) {
                map = (Map<String, Object>) JSON.parse(params);
            }
            return ResponseVo.ok(apiWeaknessServiceImpl.getTypeCount(map));
        } catch (Exception e) {
            logger.error("error:{}", e);
            return ResponseVo.error("query error", 233);
        }
    }

    /**
     * id查询接口弱点详情
     *
     * @param
     * @return
     * @Comments: <对此方法的描述，可以引用系统设计中的描述>
     * <AUTHOR> [<EMAIL>]
     * @since 2020-08-28 16:52
     */
    @RequestMapping(value = "getById.do", method = RequestMethod.POST)
    @ApiOperation(value = "id查询接口弱点详情")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "接口弱点id", required = true, paramType = "query", dataType = "String")
    })
    public ResponseVo<ApiWeaknessDto> getById(String id) {
        logger.warn("接口弱点Controller》id查询接口弱点详情");

        try {
            if (DataUtil.isNotEmpty(id)) {
                ApiWeaknessDto apiWeaknessDto = apiWeaknessServiceImpl.getApiWeaknessById(id);
                return ResponseVo.ok(apiWeaknessDto);
            } else {
                return ResponseVo.error("id不得为空", 233);
            }
        } catch (Exception e) {
            logger.error("id查询接口弱点详情", e);
            return ResponseVo.error(e.getMessage(), 233);
        }
    }

    /**
     * 编辑接口弱点
     *
     * @param
     * @return
     * @Comments: <对此方法的描述，可以引用系统设计中的描述>
     * <AUTHOR> [<EMAIL>]
     * @since 2020-08-28 16:52
     */
    @OperateLog(value = "批量修改弱点状态", type = "处理", module = "弱点")
    @RequestMapping(value = "edit.do", method = RequestMethod.POST)
    @DirectiveSync(key = "ids", needNotify = true, retry = false, remark = "批量修改弱点状态", replaceId = "params")
    @ApiOperation(value = "编辑接口弱点")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "params", value = "编辑内容", required = true, paramType = "query", dataType = "String")
    })
    @Permission(type = ResourceEnum.ACTION, value = "WRITE")
    public String edit(@RequestParam(value = "params", defaultValue = "") String params) {
        logger.warn("接口弱点Controller》编辑接口弱点");

        try {
            if (DataUtil.isNotEmpty(params)) {
                ApiWeakness apiWeakness = JSON.parseObject(params, ApiWeakness.class);
                if (apiWeakness != null) {
                    apiWeaknessServiceImpl.editApiWeakness(apiWeakness, true);
                } else {
                    return HttpResponseUtil.error(ConstantUtil.PARAMS_ERROR);
                }
            } else {
                return HttpResponseUtil.error("params不得为空");
            }
        } catch (Exception e) {
            logger.error("编辑接口弱点", e);
            return HttpResponseUtil.error(e.getMessage());
        }
        return HttpResponseUtil.success();
    }

    /**
     * 批量忽略接口弱点
     *
     * @param
     * @return
     * @Comments: <对此方法的描述，可以引用系统设计中的描述>
     * <AUTHOR> [<EMAIL>]
     * @since 2020-08-28 16:52
     */
    @DirectiveSync(key = "ids", needNotify = true, retry = false, remark = "批量忽略弱点", replaceId = "ids")
    @RequestMapping(value = "filter.do", method = RequestMethod.POST)
    @ApiOperation(value = "批量忽略接口弱点")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "ids", value = "接口弱点id，逗号分隔", required = true, paramType = "query", dataType = "String")
    })
    @Permission(type = ResourceEnum.ACTION, value = "WRITE")
    public String filter(String ids, String suggestion) {
        logger.warn("接口弱点Controller》批量忽略接口弱点");

        try {
            if (DataUtil.isNotEmpty(ids)) {
                apiWeaknessServiceImpl.filter(ids, suggestion);
            } else {
                return HttpResponseUtil.error("id不得为空");
            }
        } catch (Exception e) {
            logger.error("批量忽略接口弱点", e);
            return HttpResponseUtil.error(e.getMessage());
        }
        return HttpResponseUtil.success();
    }


    @OperateLog(value = "批量修改弱点状态", type = "处理", module = "弱点")
    @RequestMapping(value = "editState", method = RequestMethod.POST)
    @DirectiveSync(key = "ids", needNotify = true, retry = false, remark = "批量修改弱点状态", replaceId = "params")
    @ApiOperation(value = "批量更改弱点状态")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "params", value = "筛选条件", required = true, paramType = "query", dataType = "String"),
            @ApiImplicitParam(name = "state", value = "状态", required = true, paramType = "query", dataType = "String"),
            @ApiImplicitParam(name = "suggestion", value = "处理建议", required = false, paramType = "query", dataType = "String")
    })
    @Permission(type = ResourceEnum.ACTION, value = "WRITE")
    public String editState(String params, State state, String suggestion) {
        try {
            if (DataUtil.isNotEmpty(params)) {
                Map<String, Object> map = (Map<String, Object>) JSON.parse(params);
                apiWeaknessServiceImpl.editState(map, state, suggestion);
            } else {
                return HttpResponseUtil.error("id不得为空");
            }
        } catch (Exception e) {
            logger.error("批量忽略接口弱点", e);
            return HttpResponseUtil.error(e.getMessage());
        }
        return HttpResponseUtil.success();
    }


    /**
     * 批量恢复忽略接口弱点
     *
     * @param
     * @return
     * @Comments: <对此方法的描述，可以引用系统设计中的描述>
     * <AUTHOR> [<EMAIL>]
     * @since 2020-08-28 16:52
     */
    @DirectiveSync(key = "ids", needNotify = true, retry = false, remark = "批量恢复忽略弱点", replaceId = "ids")
    @RequestMapping(value = "restore.do", method = RequestMethod.POST)
    @ApiOperation(value = "批量恢复忽略接口弱点")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "ids", value = "接口弱点id，逗号分隔", required = true, paramType = "query", dataType = "String")
    })
    @Permission(type = ResourceEnum.ACTION, value = "WRITE")
    public String restore(String ids) {
        logger.warn("接口弱点Controller》批量恢复忽略接口弱点");

        try {
            if (DataUtil.isNotEmpty(ids)) {
                apiWeaknessServiceImpl.restore(ids);
            } else {
                return HttpResponseUtil.error("id不得为空");
            }
        } catch (Exception e) {
            logger.error("批量恢复忽略接口弱点", e);
            return HttpResponseUtil.error(e.getMessage());
        }
        return HttpResponseUtil.success();
    }

    /**
     * 导出接口弱点
     *
     * @param
     * @return
     * @Comments: <对此方法的描述，可以引用系统设计中的描述>
     * <AUTHOR> [<EMAIL>]
     * @since 2020-08-28 16:52
     */
    @RequestMapping(value = "export.do", method = RequestMethod.POST)
    @ApiOperation(value = "导出接口弱点")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "page", value = "当前页", required = true, paramType = "query", dataType = "int"),
            @ApiImplicitParam(name = "limit", value = "每页显示条数", required = true, paramType = "query", dataType = "int")
    })
    @Permission(type = ResourceEnum.ACTION, value = "WRITE")
    public String export(Integer page, Integer limit,
                         @RequestParam(required = false, value = "formJson") String formJson) {
        logger.warn("接口弱点Controller》导出接口弱点");

        try {
            List<ExporTitleFieldDto> list = new ArrayList<ExporTitleFieldDto>();
            ExporTitleFieldDto dto1 = new ExporTitleFieldDto();
            dto1.setTitle("弱点名称");
            dto1.setField("name");
            list.add(dto1);

            ExporTitleFieldDto dto2 = new ExporTitleFieldDto();
            dto2.setTitle("弱点等级");
            dto2.setField("level");
            list.add(dto2);

            ExporTitleFieldDto dto3 = new ExporTitleFieldDto();
            dto3.setTitle("访问域");
            dto3.setField("visitDomains");
            list.add(dto3);

            ExporTitleFieldDto dto4 = new ExporTitleFieldDto();
            dto4.setTitle("部署域");
            dto4.setField("deployDomains");
            list.add(dto4);

            ExporTitleFieldDto dto5 = new ExporTitleFieldDto();
            dto5.setTitle("请求数据标签");
            dto5.setField("reqDataLabels");
            list.add(dto5);

            ExporTitleFieldDto dto6 = new ExporTitleFieldDto();
            dto6.setTitle("返回数据标签");
            dto6.setField("rspDataLabels");
            list.add(dto6);

            ExporTitleFieldDto dto7 = new ExporTitleFieldDto();
            dto7.setTitle("访问终端");
            dto7.setField("terminals");
            list.add(dto7);

            ExporTitleFieldDto dto8 = new ExporTitleFieldDto();
            dto8.setTitle("首次发现时间");
            dto8.setField("earlyTimestamp");
            list.add(dto8);
            if (productType.equals(ProductTypeEnum.wph.name())) {
                JSONObject jsonObject = JSONObject.parseObject(formJson);
                apiWeaknessServiceImpl.exportApiWeakness(new HashMap<String, Object>(), "", 1, null, jsonObject.getString("applicant_id"), jsonObject.getString("tags"), jsonObject.getString("reasons"), jsonObject.getString("usage"), list, "/Users/<USER>/Documents/export/" + ConfigContants.exportPathMap.get(ExportTaskType.EXPORT_API_WEAKNESS.name()), null);
            } else {
                apiWeaknessServiceImpl.exportApiWeakness(new HashMap<String, Object>(), "", 1, null, list, "/Users/<USER>/Documents/export/" + ConfigContants.exportPathMap.get(ExportTaskType.EXPORT_API_WEAKNESS.name()), null);

            }
        } catch (Exception e) {
            logger.error("导出接口弱点", e);
            return HttpResponseUtil.error(e.getMessage());
        }
        return HttpResponseUtil.success();
    }

    /**
     * 判断接口弱点导出文件是否生成
     *
     * @param
     * @return
     * @Comments: <对此方法的描述，可以引用系统设计中的描述>
     * <AUTHOR> [<EMAIL>]
     * @since 2020-08-28 16:52
     */
    @RequestMapping(value = "isExistsApiWeaknessFile.do", method = RequestMethod.POST)
    @ApiOperation(value = "判断接口弱点导出文件是否生成")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "filePath", value = "文件下载路径", required = true, paramType = "query", dataType = "String")
    })
    public ResponseVo<Boolean> isExistsApiWeaknessFile(String filePath) {
        logger.warn("接口弱点Controller》判断接口弱点导出文件是否生成");

        Boolean flag = false;
        try {
            flag = apiWeaknessServiceImpl.isExistsApiWeaknessFile(filePath);
        } catch (Exception e) {
            logger.error("判断接口弱点导出文件是否生成", e);
            return ResponseVo.error(e.getMessage(), 233);
        }
        return ResponseVo.ok(flag);
    }

    /**
     * 下载接口弱点导出文件
     *
     * @param
     * @return
     * @Comments: <对此方法的描述，可以引用系统设计中的描述>
     * <AUTHOR> [<EMAIL>]
     * @since 2020-08-28 16:52
     */
    @RequestMapping(value = "downloadApiWeaknessFile.do", method = RequestMethod.GET)
    @ApiOperation(value = "下载接口弱点导出文件")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "filePath", value = "文件下载路径", required = true, paramType = "query", dataType = "String")
    })
    @Permission(type = ResourceEnum.ACTION, value = "WRITE")
    public String downloadApiWeaknessFile(String filePath, HttpServletResponse response) {
        logger.warn("接口弱点Controller》下载接口弱点导出文件");

        try {
            apiWeaknessServiceImpl.downloadApiWeaknessFile(filePath, response);
        } catch (Exception e) {
            logger.error("下载接口弱点导出文件", e);
            return HttpResponseUtil.error(e.getMessage());
        }
        return HttpResponseUtil.success();
    }

    /**
     * 生成接口弱点导出文件
     *
     * @param
     * @return
     * @Comments: <对此方法的描述，可以引用系统设计中的描述>
     * <AUTHOR> [<EMAIL>]
     * @since 2020-08-28 16:52
     */
    @RequestMapping(value = "createApiWeaknessFile.do", method = RequestMethod.POST)
    @ApiOperation(value = "生成接口弱点导出文件")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "params", value = "检索条件", required = false, paramType = "query", dataType = "String"),
            @ApiImplicitParam(name = "field", value = "排序字段", required = false, paramType = "query", dataType = "String"),
            @ApiImplicitParam(name = "sort", value = "1正序，2倒序", required = false, paramType = "query", dataType = "int"),
            @ApiImplicitParam(name = "page", value = "当前页", required = true, paramType = "query", dataType = "int"),
            @ApiImplicitParam(name = "limit", value = "每页显示条数", required = true, paramType = "query", dataType = "int")
    })
    @Permission(type = ResourceEnum.ACTION, value = "WRITE")
    public String createApiWeaknessFile(String params, String field, Integer sort, Integer page, Integer limit, String category,
                                        @RequestParam(required = false, value = "formJson") String formJson) {
        logger.warn("接口弱点Controller》生成接口弱点导出文件");

        String reportDocxPath = null;
        try {
            Map<String, Object> map = new HashMap<String, Object>();
            if (DataUtil.isNotEmpty(params)) {
                map = (Map<String, Object>) JSON.parse(params);
            }
            if (productType.equals(ProductTypeEnum.wph.name())) {
                JSONObject jsonObject = JSONObject.parseObject(formJson);
                reportDocxPath = apiWeaknessServiceImpl.createApiWeaknessFile(map, field, sort, page, limit, category, jsonObject.getString("applicant_id"), jsonObject.getString("tags"), jsonObject.getString("reasons"), jsonObject.getString("usage"), "/Users/<USER>/Downloads/test/弱点导出.docx", null);

            } else {
                reportDocxPath = apiWeaknessServiceImpl.createApiWeaknessFile(map, field, sort, page, limit, category, "/Users/<USER>/Downloads/test/弱点导出.docx");

            }
        } catch (Exception e) {
            logger.error("生成接口弱点导出文件", e);
            return HttpResponseUtil.error(e.getMessage());
        }
        return reportDocxPath;
    }

    @RequestMapping(value = "getSamples", method = RequestMethod.GET)
    @ApiOperation(value = "获取弱点样例列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "弱点主键ID", required = true, dataType = "String")
    })
    public ResponseVo<List<SampleEventDto>> getSamples(String id, Boolean desensitize,
                                                       @RequestParam(required = false) String sampleEventId,
                                                       @RequestParam(required = false) Integer page,
                                                       @RequestParam(required = false) Integer limit) {
        if (page == null) {
            page = 0;
        }
        if (limit == null) {
            limit = 100;
        }
        List<SampleEventDto> sampleEventDtos = apiWeaknessServiceImpl.listSamples(Boolean.TRUE.equals(desensitize), id, sampleEventId);
        return ResponseVo.ok(ListOutputDto.page(sampleEventDtos, page, limit));
    }

    @RequestMapping(value = "statistics", method = RequestMethod.GET)
    @ApiOperation(value = "获取弱点数量的简单统计", tags = "API1.5")
    public ResponseVo<WeaknessStatistics> getStatistics() {
        return ResponseVo.ok(apiWeaknessServiceImpl.simpleStatistics());
    }

    /**
     * 查询接口弱点分组
     *
     * @param
     * @return
     * @Comments: <对此方法的描述，可以引用系统设计中的描述>
     * <AUTHOR> [<EMAIL>]
     * @since 2020-08-28 16:52
     */
    @RequestMapping(value = "getApiWeaknessGroup.do", method = RequestMethod.POST)
    @ApiOperation(value = "查询接口弱点分组")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "params", value = "检索条件", required = false, paramType = "query", dataType = "String"),
            @ApiImplicitParam(name = "sort", value = "1正序，2倒序", required = false, paramType = "query", dataType = "int"),
            @ApiImplicitParam(name = "groupFields", value = "分组字段，逗号分隔", required = true, paramType = "query", dataType = "int")
//            @ApiImplicitParam(name = "categorys", value = "类型", required = false, paramType = "query", dataType = "Array")
    })
    @DataPermission
    public ResponseVo<List<CommonGroupDto>> getApiWeaknessGroup(@RequestParam(value = "params", defaultValue = "") String params, Integer sort, String groupFields) {
        logger.warn("接口弱点Controller》查询接口弱点分组");

        List<CommonGroupDto> data = null;
        try {
            Map<String, Object> map = new HashMap<String, Object>();
            if (DataUtil.isNotEmpty(params)) {
                map = (Map<String, Object>) JSON.parse(params);
            }
            data = apiWeaknessServiceImpl.getApiWeaknessGroup(map, sort, groupFields, map.get("category") != null ? (List<String>) map.get("category") : null);
            for (int i = 0; i < data.size(); i++) {
                if ("SUSPECT".equals(data.get(i).getId())) {
                    data.remove(i);
                }
            }
        } catch (Exception e) {
            logger.error("查询接口弱点分组", e);
            return ResponseVo.error(e.getMessage(), 233);
        }
        return ResponseVo.ok(data);
    }
}
