package com.quanzhi.auditapiv2.web.controller;

import com.quanzhi.auditapiv2.common.dal.dto.ControlStrategyDto;
import com.quanzhi.auditapiv2.common.dal.dto.ControlStrategySearchDto;
import com.quanzhi.auditapiv2.common.dal.dto.IpBlockDto;
import com.quanzhi.auditapiv2.common.dal.entity.ControlStrategyLog;
import com.quanzhi.auditapiv2.common.util.dto.ListOutputDto;
import com.quanzhi.auditapiv2.common.util.utils.HttpResponseUtil;
import com.quanzhi.auditapiv2.core.service.manager.web.IControlStrategyLogService;
import com.quanzhi.auditapiv2.core.service.manager.web.IControlStrategyService;
import com.quanzhi.auditapiv2.web.common.annotation.DirectiveSync;
import com.quanzhi.auditapiv2.web.common.annotation.OperateLog;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;


/**
 * <AUTHOR>
 * create at 2022/12/16 11:06 上午
 * @description: 阻断策略
 **/
@RestController
@Slf4j
@RequestMapping("/api/controlStrategy")
public class ControlStrategyController {

    private final IControlStrategyService controlStrategyService;

    private final IControlStrategyLogService controlStrategyLogService;

    public ControlStrategyController(
            IControlStrategyService controlStrategyService,
            IControlStrategyLogService controlStrategyLogService) {
        this.controlStrategyService = controlStrategyService;
        this.controlStrategyLogService = controlStrategyLogService;
    }

    @PostMapping(value = "list")
    @ApiOperation(value = "阻断策略列表展示")
    public String list(@RequestBody ControlStrategySearchDto controlStrategySearchDto) {
        ListOutputDto<ControlStrategyDto> outputDto = controlStrategyService.list(controlStrategySearchDto);
        return HttpResponseUtil.success(outputDto);
    }

    @OperateLog(module = "阻断策略", type = "新增", value = "新增阻断策略")
    @PostMapping(value = "add")
    @ApiOperation(value = "新增阻断策略")
    @DirectiveSync(needNotify = true, remark = "新增阻断策略")
    public String add(@RequestBody ControlStrategyDto controlStrategyDto) {

        return controlStrategyService.add(controlStrategyDto);
    }

    @OperateLog(module = "阻断策略", type = "编辑", value = "编辑阻断策略")
    @PostMapping(value = "edit")
    @ApiOperation(value = "编辑阻断策略")
    @DirectiveSync(needNotify = true, remark = "编辑阻断策略")
    public String edit(@RequestBody ControlStrategyDto controlStrategyDto) {
        return HttpResponseUtil.success(controlStrategyService.edit(controlStrategyDto));
    }


    @OperateLog(module = "阻断策略", type = "编辑", value = "编辑阻断策略")
    @PostMapping(value = "enable")
    @ApiOperation(value = "启用/禁用 阻断策略")
    @DirectiveSync(needNotify = true, remark = "编辑阻断策略")
    public String enable(String id, boolean enable) {
        return HttpResponseUtil.success(controlStrategyService.enable(id, enable));
    }

    @OperateLog(module = "阻断策略", type = "删除", value = "删除阻断策略")
    @PostMapping(value = "delete")
    @ApiOperation(value = "删除阻断策略")
    @DirectiveSync(needNotify = true, remark = "删除阻断策略")
    public String delete(String id) {
        return HttpResponseUtil.success(controlStrategyService.delete(id));
    }

    @PostMapping(value = "listLog")
    @ApiOperation(value = "阻断策略日志展示")
    public String listLog(Integer page, Integer limit, String logDetail) {
        ListOutputDto<ControlStrategyLog> outputDto = controlStrategyLogService.listLog(page, limit, logDetail);
        return HttpResponseUtil.success(outputDto);
    }

    @PostMapping(value = "/riskBlock")
    @ApiOperation(value = "风险阻断")
    public String riskBlock(String id, boolean blockFlag) {
        try {
            controlStrategyService.riskBlock(id, blockFlag);
        } catch (Exception e) {
            log.error("风险阻断异常", e);
            return HttpResponseUtil.error("风险阻断异常");
        }
        return HttpResponseUtil.success();
    }

    @PostMapping(value = "/ipBlock")
    @OperateLog(value = "修改IP阻断状态", type = "处理", module = "阻断")
    @DirectiveSync(key = "ip", needNotify = true, retry = true, remark = "修改IP阻断状态")
    @ApiOperation(value = "ip阻断")
    public String ipBlock(@RequestBody IpBlockDto ipBlockDto) {
        return controlStrategyService.ipBlock(ipBlockDto);
    }

}
