package com.quanzhi.auditapiv2.web.controller;

import com.quanzhi.audit.analysis.util.DataUtil;
import com.quanzhi.auditapiv2.common.dal.enums.SsoNameEnum;
import com.quanzhi.auditapiv2.common.dal.entity.ConstantInfo;
import com.quanzhi.auditapiv2.common.util.utils.HttpResponseUtil;
import com.quanzhi.uias.base.sdk.util.entity.AuthInfo;
import io.jsonwebtoken.JwtBuilder;
import io.jsonwebtoken.Jwts;
import io.jsonwebtoken.SignatureAlgorithm;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.*;

@RestController
@RequestMapping("/api/token")
@Slf4j
public class LoginTokenController {
    private static final String SECRET_KEY = "U8lQEv6xlVSefInGMcrLvoV0FDz1VLRh";

    private static final long EXPIRATION_TIME = 60 * 60  * 1000;

    @RequestMapping(value = "/getToken", method = RequestMethod.GET)
    public String getToken(
            HttpServletRequest request, HttpServletResponse response
    ) {

        String username = (String) request.getSession().getAttribute(ConstantInfo.username);

        if (DataUtil.isEmpty(username)){

            AuthInfo auth = (AuthInfo) request.getSession().getAttribute(SsoNameEnum.DEFAULT_SSO_AUTH_INFO.name());
            if (DataUtil.isNotEmpty(auth)){
                username = auth.getSsoUserName();
            }
        }
        String jwt = generateToken(username);

        return HttpResponseUtil.success(jwt);
    }
    public static String generateToken(String username) {
//        Date now = new Date();
//        Date expiryDate = new Date(now.getTime() + EXPIRATION_TIME);

        // 定义头部信息
        Map<String, Object> header = new HashMap<>();
        header.put("alg", "HS512");
        header.put("typ", "JWT");

        JwtBuilder builder = Jwts.builder()
                .setHeader(header) // 设置自定义头部
                .setSubject(username)
                .claim("role", "webadmin")
                .setIssuedAt(new Date())
                .setExpiration(new Date(System.currentTimeMillis() + EXPIRATION_TIME))
                .signWith(SignatureAlgorithm.HS512, SECRET_KEY);

        String jwt = builder.compact();

        try {
            // 解析 JWT
             Jwts.parser()
                    .setSigningKey(SECRET_KEY)
                    .parseClaimsJws(jwt)
                    .getBody();
        } catch (Exception e) {
            // JWT 解析失败
            e.printStackTrace();
            return null;
        }

        return jwt;
    }
}
