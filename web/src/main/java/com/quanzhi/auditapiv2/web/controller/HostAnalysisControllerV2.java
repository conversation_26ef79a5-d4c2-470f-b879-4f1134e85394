package com.quanzhi.auditapiv2.web.controller;

import com.quanzhi.audit.mix.permission.annotation.Permission;
import com.quanzhi.audit.mix.permission.domain.enums.ResourceEnum;
import com.quanzhi.audit_core.common.model.HostAnalysisConfigV2;
import com.quanzhi.auditapiv2.common.util.dto.ResponseVo;
import com.quanzhi.auditapiv2.core.service.manager.web.IHostAnalysisConfigV2Service;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

// todo: modify comments and log

/**
 * 《域名解析配置controller》
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/api/hostAnalysisV2")
@Slf4j
public class HostAnalysisControllerV2 {

    private Logger logger = LoggerFactory.getLogger(HostAnalysisControllerV2.class);

    @Autowired
    private IHostAnalysisConfigV2Service hostAnalysisConfigV2ServiceImpl;

    /**
     * 查询域名解析配置列表
     */
    @RequestMapping(value = "list.do", method = RequestMethod.POST)
    @ApiOperation(value = "查询域名解析配置列表")
    public ResponseVo<List<HostAnalysisConfigV2>> list() {
        try {
            return ResponseVo.ok(hostAnalysisConfigV2ServiceImpl.getHostAnalysisConfigList());
        } catch (Exception e) {
            logger.error("get host analysis config V2 error", e);
        }
        return ResponseVo.ok(null);
    }

    /**
     * 保存域名解析配置列表
     */
    @RequestMapping(value = "save.do", method = RequestMethod.POST)
    @ApiOperation(value = "保存域名解析配置")
    @Permission(type = ResourceEnum.ACTION, value = "WRITE")
    public ResponseVo<Void> save(@RequestBody List<HostAnalysisConfigV2> configs) {
        try {
            return ResponseVo.ok(hostAnalysisConfigV2ServiceImpl.saveHostAnalysisConfigList(configs));
        } catch (Exception e) {
            logger.error("save host analysis config V2 error", e);
        }
        return ResponseVo.ok(null);
    }
}
