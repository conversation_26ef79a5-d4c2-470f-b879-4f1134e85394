package com.quanzhi.auditapiv2.web.controller;

import com.quanzhi.auditapiv2.common.dal.dto.HttpAppSnapshotDto;
import com.quanzhi.auditapiv2.common.util.dto.ListOutputDto;
import com.quanzhi.auditapiv2.common.util.utils.HttpResponseUtil;
import com.quanzhi.auditapiv2.core.service.manager.web.IHttpAppSnapshotService;
import com.quanzhi.metabase.core.model.http.HttpAppSnapshot;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

/**
 *
 * 《应用快照Controller》
 *
 *
 * @Project:
 * @Module ID:
 * @Comments:
 * @JDK version used:      <JDK1.8>
 * <AUTHOR> [<EMAIL>]
 * @since 2020-04-08-上午9:56:10
 */
@RestController
@RequestMapping("/api/httpAppSnapshot")
public class HttpAppSnapshotController {
    private Logger logger = LoggerFactory.getLogger(HttpAppSnapshotController.class);

    //接口快照service
    @Autowired
    private IHttpAppSnapshotService httpAppSnapshotServiceImpl;

    /**
     * 查询应用快照列表(分页)
     * @Comments:  <对此方法的描述，可以引用系统设计中的描述>
     * <AUTHOR> [<EMAIL>]
     * @since 2020-04-08 9:56
     * @param
     * @return
     */
    @RequestMapping(value = "listData.do", method = RequestMethod.POST)
    @ApiOperation(value = "查询应用快照列表(分页)")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "appId", value = "应用id", required = true, paramType = "query", dataType = "String"),
            @ApiImplicitParam(name = "page", value = "当前页", required = true, paramType = "query", dataType = "int"),
            @ApiImplicitParam(name = "limit", value = "每页显示条数", required = true, paramType = "query", dataType = "int")
    })
    public String listData(String appId, Integer page, Integer limit) {
        logger.warn("应用快照Controller》查询应用快照列表(分页)");

        ListOutputDto<HttpAppSnapshot> data = null;
        try {
            data = httpAppSnapshotServiceImpl.getHttpAppSnapshotList(appId, page, limit);
        } catch (Exception e) {
            logger.error("查询应用快照列表(分页)", e);
            return HttpResponseUtil.error(e.getMessage());
        }
        return HttpResponseUtil.success(data);
    }

    /**
     * 应用id查询应用快照详情
     * @Comments:  <对此方法的描述，可以引用系统设计中的描述>
     * <AUTHOR> [<EMAIL>]
     * @since 2020-04-08 9:56
     * @param
     * @return
     */
    @RequestMapping(value = "getByAppId.do", method = RequestMethod.POST)
    @ApiOperation(value = "应用id查询应用快照详情")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "appId", value = "应用id", required = true, paramType = "query", dataType = "String"),
            @ApiImplicitParam(name = "startDate", value = "开始时间", required = true, paramType = "query", dataType = "long"),
            @ApiImplicitParam(name = "endDate", value = "结束时间", required = true, paramType = "query", dataType = "long")
    })
    public String getById(String appId, Long startDate, Long endDate) {
        logger.warn("应用快照Controller》应用id查询应用快照详情");

        try {
            HttpAppSnapshotDto httpAppSnapshotDto = httpAppSnapshotServiceImpl.getHttpAppSnapshotByAppId(appId, startDate, endDate);
            return HttpResponseUtil.success(httpAppSnapshotDto);
        } catch (Exception e) {
            logger.error("应用id查询应用快照详情", e);
            return HttpResponseUtil.error(e.getMessage());
        }
    }
}
