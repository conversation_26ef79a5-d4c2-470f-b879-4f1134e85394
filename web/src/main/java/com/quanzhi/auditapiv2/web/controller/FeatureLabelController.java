package com.quanzhi.auditapiv2.web.controller;


import com.quanzhi.audit.mix.permission.annotation.Permission;
import com.quanzhi.audit.mix.permission.domain.enums.ResourceEnum;
import com.quanzhi.audit_core.common.model.ApiClassification;
import com.quanzhi.audit_core.common.model.FeatureLabel;
import com.quanzhi.auditapiv2.common.dal.dto.api.CascadeApiFeatureDto;
import com.quanzhi.auditapiv2.common.dal.dto.api.CommonApiFeature;
import com.quanzhi.auditapiv2.common.dal.dto.common.CommonDto;
import com.quanzhi.auditapiv2.common.dal.dto.query.NacosFilterDto;
import com.quanzhi.auditapiv2.common.dal.dto.query.Predicate;
import com.quanzhi.auditapiv2.common.util.dto.ListOutputDto;
import com.quanzhi.auditapiv2.common.util.dto.ResponseVo;
import com.quanzhi.auditapiv2.common.util.utils.*;
import com.quanzhi.auditapiv2.core.risk.dto.common.GroupDataDto;
import com.quanzhi.auditapiv2.core.service.manager.web.IApiClassifierService;
import com.quanzhi.auditapiv2.core.service.manager.web.IFeatureLabelService;
import com.quanzhi.auditapiv2.core.service.manager.web.impl.RuleConfigService;
import com.quanzhi.auditapiv2.web.common.annotation.DirectiveSync;
import com.quanzhi.re.core.domain.entity.po.RuleConfigPO;
import io.swagger.annotations.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.*;
import java.util.stream.Collectors;

@RestController
@RequestMapping("/api/featureLabel")
@Slf4j
public class FeatureLabelController {

    @Autowired
    private IFeatureLabelService featureLabelService;

    @Autowired
    private IApiClassifierService apiClassifierService;

    @Resource
    private RuleConfigService ruleConfigService;

    /**
     * 保存接口分类
     *
     * @param
     * @return
     */
    @RequestMapping(value = "/save.do", method = RequestMethod.POST)
    @DirectiveSync(key = "id", needNotify = true, retry = false, remark = "操作接口标签")
    @Permission(type = ResourceEnum.ACTION, value = "WRITE")
    public String save(
            HttpServletRequest request,
            @RequestBody FeatureLabel featureLabel

    ) {
        if (DataUtil.isNotEmpty(request.getSession().getAttribute("username"))) {
            String username = request.getSession().getAttribute("username").toString();
            featureLabel.setOperateName(username);
        }
        if (StringUtils.isNullOrEmpty(featureLabel.getName())) {
            throw new IllegalArgumentException("标签名称不能为空！");
        }
        if (featureLabel.getName().length() > 20) {
            throw new IllegalArgumentException("标签名称过长！");
        }
//        if (DataUtil.isEmpty(featureLabel.getId())) {
//
//            Map<String, String> apiClassificationMap = null;
//            try {
//                apiClassificationMap = apiClassifierService.getApiClassificationMap();
//            } catch (Exception e) {
//                e.printStackTrace();
//                apiClassificationMap = new HashMap<>();
//            }
//            boolean contains = apiClassificationMap.values().contains(featureLabel.getName());
//            if (contains) {
//                throw new IllegalArgumentException("标签名称不可重复!");
//            }
//        }
        try {
            featureLabel.setUpdateTime(System.currentTimeMillis());
            FeatureLabel current = new FeatureLabel();
            BeanUtils.copyProperties(featureLabel, current);
            if(DataUtil.isEmpty(featureLabel.getId())){
                featureLabel.setCreateTime(System.currentTimeMillis());
            }
            featureLabelService.save(current);
        }catch (Exception e){
            log.error("save featureLabel error:",e);
            return HttpResponseUtil.error(e.getMessage());
        }
        return HttpResponseUtil.success();
    }

    /**
     * 分页获取接口分类列表
     *
     * @param page
     * @param limit
     * @return
     */
    @RequestMapping(value = "/page.do", method = RequestMethod.GET)
    @ApiImplicitParams({
            @ApiImplicitParam(name = "keyword", value = "搜索内容",
                    required = false, paramType = "query", dataType = "String"),
            @ApiImplicitParam(name = "page", value = "page",
                    required = true, paramType = "query", dataType = "Integer"),
            @ApiImplicitParam(name = "limit", value = "limit",
                    required = true, paramType = "query", dataType = "Integer")
    })
    @ApiResponses({
            @ApiResponse(code = 200,
                    message = "{\"success\":true,\"errorCode\":0,\"data\":{\"rows\":[{\"id\":\"xxx\",\"name\":\"abc\",\"desc\":\"123\",\"enabled\":true,\"operateName\":\"mingming\",\"createTime\":\"1589426873000\",\"Type\":1,\"classificationType\":\"APP\"}],\"totalCount\":1}}",
                    response = FeatureLabel.class
            ),
    })
    public String page(
            @RequestParam(value = "name", defaultValue = "", required = false) String name,
            @RequestParam(value = "page", defaultValue = "1") Integer page,
            @RequestParam(value = "limit", defaultValue = "10") Integer limit
    ) {
        List<NacosFilterDto> nacosFilterDtos = new ArrayList<>();
        if (DataUtil.isNotEmpty(name)) {
            NacosFilterDto nacosFilterDto = new NacosFilterDto();
            nacosFilterDto.setKey("name");
            nacosFilterDto.setValue(name);
            nacosFilterDto.setPredicate(Predicate.REGEX);
            nacosFilterDtos.add(nacosFilterDto);
        }
        ListOutputDto<FeatureLabel> data = featureLabelService.page(page, limit, nacosFilterDtos);
        if(data.getRows() != null && !data.getRows().isEmpty()){
            List<String> ids = data.getRows().stream().map(FeatureLabel::getId).collect(Collectors.toList());
            List<RuleConfigPO> configServiceByIds = ruleConfigService.findByIds(ids);
            data.getRows().stream().forEach(e -> {
                if (e.getType() == null) {
                    e.setType(FeatureLabel.TypeEnum.SYSTEM.val());
                }
                handle(e,configServiceByIds);
            });
        }
        return HttpResponseUtil.success(data);
    }

    /**
     * api标签 级联展示
     */
    @RequestMapping(value = "/cascadeApiFeatureLabels", method = RequestMethod.POST)
    public ResponseVo<List<CommonApiFeature>> cascadeApiFeatureLabels(String name) {
        List<CascadeApiFeatureDto> result = new ArrayList<>();
        List<NacosFilterDto> nacosFilterDtos = new ArrayList<>();
        if (DataUtil.isNotEmpty(name)) {
            NacosFilterDto nacosFilterDto = new NacosFilterDto();
            nacosFilterDto.setKey("name");
            nacosFilterDto.setValue(name);
            nacosFilterDto.setPredicate(Predicate.REGEX);
            nacosFilterDtos.add(nacosFilterDto);
        }
        ListOutputDto<FeatureLabel> featureLabelListOutputDto = featureLabelService.page(1, 1000, nacosFilterDtos);
        ListOutputDto<ApiClassification> apiClassificationListOutputDto = apiClassifierService.page(1, 1000, nacosFilterDtos);

        List<CommonApiFeature> commonApiFeatures = new ArrayList<>();
        List<FeatureLabel> featureLabels = featureLabelListOutputDto.getRows();
        if(featureLabels != null && !featureLabels.isEmpty()){
            List<String> ids = featureLabels.stream().map(FeatureLabel::getId).collect(Collectors.toList());
            List<RuleConfigPO> configServiceByIds = ruleConfigService.findByIds(ids);
            for (FeatureLabel featureLabel : featureLabels) {
                if (Boolean.TRUE.equals(featureLabel.getDelFlag())) {
                    continue;
                }
                CommonApiFeature commonApiFeature = new CommonApiFeature();
                BeanUtils.copyProperties(featureLabel, commonApiFeature);
                if (commonApiFeature.getType() == null) {
                    commonApiFeature.setType(FeatureLabel.TypeEnum.SYSTEM.val());
                }
                if (DataUtil.isEmpty(commonApiFeature.getGroup())) {
                    commonApiFeature.setGroup("other");
                    commonApiFeature.setGroupName("其他");
                }
                handle(featureLabel,configServiceByIds);
                commonApiFeature.setCode(FeatureLabel.CodeEnum.FEATURELABEL.getCode());
                commonApiFeatures.add(commonApiFeature);
            }
        }
        List<ApiClassification> classifications = apiClassificationListOutputDto.getRows();
        for (ApiClassification apiClassification : classifications) {
            if (Boolean.TRUE.equals(apiClassification.getDelFlag())) {
                continue;
            }
            CommonApiFeature commonApiFeature = new CommonApiFeature();
            BeanUtils.copyProperties(apiClassification, commonApiFeature);
            if (commonApiFeature.getType() == null) {
                commonApiFeature.setType(FeatureLabel.TypeEnum.SYSTEM.val());
            }
            if (DataUtil.isEmpty(commonApiFeature.getGroup())) {
                commonApiFeature.setGroup("other");
                commonApiFeature.setGroupName("其他");
            }
            commonApiFeature.setCode(FeatureLabel.CodeEnum.CLASSIFICATIONS.getCode());
            commonApiFeatures.add(commonApiFeature);
        }
        return ResponseVo.ok(commonApiFeatures);
    }

    /**
     * api标签 级联展示
     */
    @RequestMapping(value = "/cascadeApiFeatureLabel", method = RequestMethod.POST)
    public String cascadeApiFeatureLabel(String name) {
        List<CascadeApiFeatureDto> result = new ArrayList<>();
        List<NacosFilterDto> nacosFilterDtos = new ArrayList<>();
        if (DataUtil.isNotEmpty(name)) {
            NacosFilterDto nacosFilterDto = new NacosFilterDto();
            nacosFilterDto.setKey("name");
            nacosFilterDto.setValue(name);
            nacosFilterDto.setPredicate(Predicate.REGEX);
            nacosFilterDtos.add(nacosFilterDto);
        }
        ListOutputDto<FeatureLabel> featureLabelListOutputDto = featureLabelService.page(1, 1000, nacosFilterDtos);
        ListOutputDto<ApiClassification> apiClassificationListOutputDto = apiClassifierService.page(1, 1000, nacosFilterDtos);

        List<CommonApiFeature> commonApiFeatures = new ArrayList<>();
        List<FeatureLabel> featureLabels = featureLabelListOutputDto.getRows();

        for (FeatureLabel featureLabel : featureLabels) {
            if (Boolean.TRUE.equals(featureLabel.getDelFlag())) {
                continue;
            }
            CommonApiFeature commonApiFeature = new CommonApiFeature();
            BeanUtils.copyProperties(featureLabel, commonApiFeature);
            if (commonApiFeature.getType() == null) {
                commonApiFeature.setType(FeatureLabel.TypeEnum.SYSTEM.val());
            }
            if (DataUtil.isEmpty(commonApiFeature.getGroup())) {
                commonApiFeature.setGroup("other");
                commonApiFeature.setGroupName("其他");
            }
            commonApiFeature.setCode(FeatureLabel.CodeEnum.FEATURELABEL.getCode());
            commonApiFeatures.add(commonApiFeature);
        }
        List<ApiClassification> classifications = apiClassificationListOutputDto.getRows();
        for (ApiClassification apiClassification : classifications) {
            if (Boolean.TRUE.equals(apiClassification.getDelFlag())) {
                continue;
            }
            CommonApiFeature commonApiFeature = new CommonApiFeature();
            BeanUtils.copyProperties(apiClassification, commonApiFeature);
            if (commonApiFeature.getType() == null) {
                commonApiFeature.setType(FeatureLabel.TypeEnum.SYSTEM.val());
            }
            if (DataUtil.isEmpty(commonApiFeature.getGroup())) {
                commonApiFeature.setGroup("other");
                commonApiFeature.setGroupName("其他");
            }
            commonApiFeature.setCode(FeatureLabel.CodeEnum.CLASSIFICATIONS.getCode());
            commonApiFeatures.add(commonApiFeature);
        }
        Map<String, List<CommonApiFeature>> collect = commonApiFeatures.stream().collect(Collectors.groupingBy(CommonApiFeature::getGroup));
        collect.forEach((k, v) -> {
            CascadeApiFeatureDto cascadeApiFeatureDto = new CascadeApiFeatureDto();
            cascadeApiFeatureDto.setId(k);
            List<CascadeApiFeatureDto.FeatureInfo> childrens = v.stream().map(e -> CascadeApiFeatureDto.convert(e)).collect(Collectors.toList());
            cascadeApiFeatureDto.setChildren(childrens);
            cascadeApiFeatureDto.setName(v.get(0).getGroupName());
            result.add(cascadeApiFeatureDto);
        });

        return HttpResponseUtil.success(result);
    }


    /**
     * api标签分页展示
     */
    @RequestMapping(value = "/pageApiFeatureLabel", method = RequestMethod.GET)
    public String pageApiFeatureLabel(String name, Integer page, Integer limit) {

        List<NacosFilterDto> nacosFilterDtos = new ArrayList<>();
        if (DataUtil.isNotEmpty(name)) {
            NacosFilterDto nacosFilterDto = new NacosFilterDto();
            nacosFilterDto.setKey("name");
            nacosFilterDto.setValue(name);
            nacosFilterDto.setPredicate(Predicate.REGEX);
            nacosFilterDtos.add(nacosFilterDto);
        }
        ListOutputDto<FeatureLabel> featureLabelListOutputDto = featureLabelService.page(1, 1000, nacosFilterDtos);
        ListOutputDto<ApiClassification> apiClassificationListOutputDto = apiClassifierService.page(1, 1000, nacosFilterDtos);

        List<CommonApiFeature> commonApiFeatures = new ArrayList<>();
        List<FeatureLabel> featureLabels = featureLabelListOutputDto.getRows();
        if(featureLabels != null && !featureLabels.isEmpty()){
            List<String> ids = featureLabels.stream().map(FeatureLabel::getId).collect(Collectors.toList());
            List<RuleConfigPO> configServiceByIds = ruleConfigService.findByIds(ids);
            for (FeatureLabel featureLabel : featureLabels) {
                if (Boolean.TRUE.equals(featureLabel.getDelFlag())) {
                    continue;
                }
                handle(featureLabel,configServiceByIds);
                CommonApiFeature commonApiFeature = new CommonApiFeature();
                BeanUtils.copyProperties(featureLabel, commonApiFeature);
                if (commonApiFeature.getType() == null) {
                    commonApiFeature.setType(FeatureLabel.TypeEnum.SYSTEM.val());
                }
                if (DataUtil.isEmpty(commonApiFeature.getGroup())) {
                    commonApiFeature.setGroup("other");
                    commonApiFeature.setGroupName("其他");
                }
                commonApiFeature.setCode(FeatureLabel.CodeEnum.FEATURELABEL.getCode());
                commonApiFeatures.add(commonApiFeature);
            }
        }

        List<ApiClassification> classifications = apiClassificationListOutputDto.getRows();
        for (ApiClassification apiClassification : classifications) {
            if (Boolean.TRUE.equals(apiClassification.getDelFlag())) {
                continue;
            }
            CommonApiFeature commonApiFeature = new CommonApiFeature();
            BeanUtils.copyProperties(apiClassification, commonApiFeature);
            if (commonApiFeature.getType() == null) {
                commonApiFeature.setType(FeatureLabel.TypeEnum.SYSTEM.val());
            }
            if (DataUtil.isEmpty(commonApiFeature.getGroup())) {
                commonApiFeature.setGroup("other");
                commonApiFeature.setGroupName("其他");
            }
            commonApiFeature.setCode(FeatureLabel.CodeEnum.CLASSIFICATIONS.getCode());
            commonApiFeatures.add(commonApiFeature);
        }

        ListOutputDto listOutputDto = new ListOutputDto();
        if(DataUtil.isEmpty(commonApiFeatures)){
            listOutputDto.setRows(new ArrayList());
            listOutputDto.setTotalCount(0L);
        }else{
            ListPageUtil<CommonApiFeature> commonApiFeatureListPages = new ListPageUtil<>(commonApiFeatures, page, limit);
            listOutputDto.setRows(commonApiFeatureListPages.getPagedList());
            listOutputDto.setTotalCount(commonApiFeatures.size());
        }
        return HttpResponseUtil.success(listOutputDto);
    }

    private void handle(FeatureLabel featureLabel, List<RuleConfigPO> configServiceByIds){
        if(featureLabel.getNewModeFlag() != null && featureLabel.getNewModeFlag()){
            if(configServiceByIds != null && !configServiceByIds.isEmpty()){
                Optional<RuleConfigPO> configPOOptional = configServiceByIds.stream().filter(i -> featureLabel.getId().equals(i.getId())&&!featureLabel.getDelFlag()).findAny();
                if(configPOOptional.isPresent()){
                    featureLabel.setEnable(configPOOptional.get().getEnabled());
                    featureLabel.setMatchRule(configPOOptional.get().getMatchRule());
                }
            }
        }
    }

    /**
     * 分页获取接口标签列表
     *
     * @return
     */
    @RequestMapping(value = "getAll.do", method = RequestMethod.GET)
    @ApiOperation(value = "接口标签")
    public ResponseVo<List<GroupDataDto>> getAll() {

        List<CommonDto> list = new ArrayList<CommonDto>();

        List<FeatureLabel> featureLabelList = featureLabelService.getAll();
        for (FeatureLabel featureLabel : featureLabelList) {

            if (!featureLabel.getDelFlag()) {
                Map<String, Object> map = new HashMap<String, Object>();
                map.put("name", featureLabel.getName());
                map.put("type", FeatureLabel.CodeEnum.FEATURELABEL.getCode());
                map.put("typeName", FeatureLabel.CodeEnum.FEATURELABEL.getName());

                CommonDto commonDto = CommonDto.builder()
                        .key(featureLabel.getId())
                        .value(map)
                        .build();
                list.add(commonDto);
            }
        }

        List<ApiClassification> apiClassificationList = apiClassifierService.getAll();
        for (ApiClassification apiClassification : apiClassificationList) {

            if (!apiClassification.getDelFlag()) {
                Map<String, Object> map = new HashMap<String, Object>();
                map.put("name", apiClassification.getName());
                map.put("type", FeatureLabel.CodeEnum.CLASSIFICATIONS.getCode());
                map.put("typeName", FeatureLabel.CodeEnum.CLASSIFICATIONS.getName());

                CommonDto commonDto = CommonDto.builder()
                        .key(apiClassification.getId())
                        .value(map)
                        .build();
                list.add(commonDto);
            }
        }

        return ResponseVo.ok(list);
    }

    /**
     * 根据ID，获取详情
     *
     * @param id
     * @return
     */
    @RequestMapping(value = "/getById.do", method = RequestMethod.GET)
    @ApiResponses({
            @ApiResponse(code = 200,
                    message = "{\"success\":true,\"errorCode\":0,\"data\":{\"id\":\"xxx\",\"name\":\"abc\",\"desc\":\"123\",\"enabled\":true,\"operateName\":\"mingming\",\"createTime\":\"1589426873000\",\"Type\":1,\"classificationType\":\"APP\"}}",
                    response = FeatureLabel.class
            ),
    })
    public String getById(
            @RequestParam(value = "id", defaultValue = "") String id
    ) {
        return HttpResponseUtil.success(featureLabelService.getById(id));
    }

    /**
     * 根据ID，删除分类
     *
     * @param ids
     * @return
     */
    @RequestMapping(value = "/delete.do", method = RequestMethod.POST)
    @DirectiveSync(key = "ids", needNotify = true, retry = false, remark = "删除接口标签")
    @Permission(type = ResourceEnum.ACTION, value = "WRITE")
    public String delete(@RequestParam String ids) {

        if (DataUtil.isNotEmpty(ids)) {

            String[] idArray = ids.split(",");
            List<String> idList = Arrays.asList(idArray);
            featureLabelService.remove(idList);
        }
        return HttpResponseUtil.success();
    }
}
