package com.quanzhi.auditapiv2.web.controller;

import com.quanzhi.auditapiv2.common.util.utils.HttpResponseUtil;
import com.quanzhi.auditapiv2.core.service.manager.dto.ModuleConfigUpdateDTO;
import com.quanzhi.auditapiv2.core.service.manager.web.ModuleConfigService;
import com.quanzhi.auditapiv2.web.common.annotation.OperateLog;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.util.Map;

@RestController
@RequestMapping("/api/module")
public class ModuleConfigController {

    private final ModuleConfigService moduleConfigService;

    public ModuleConfigController(ModuleConfigService moduleConfigService) {
        this.moduleConfigService = moduleConfigService;
    }

    @RequestMapping(value = "/get", method = RequestMethod.POST)
    public String get() {
        try {
            Map<String, Object> map = moduleConfigService.get();
            return HttpResponseUtil.success(map);
        } catch (Exception e) {
            return HttpResponseUtil.error(e.getMessage());
        }
    }

    @OperateLog(value = "编辑高速模式", module = "系统配置", type = "编辑")
    @RequestMapping(value = "/update", method = RequestMethod.POST)
    public String update(@RequestBody ModuleConfigUpdateDTO moduleConfigUpdateDTO) {
        try {
            boolean flag = moduleConfigService.update(moduleConfigUpdateDTO);
            return HttpResponseUtil.success(flag);
        } catch (Exception e) {
            return HttpResponseUtil.error(e.getMessage());
        }
    }


    @RequestMapping(value = "/list", method = RequestMethod.POST)
    public String list() {
        try {
            return HttpResponseUtil.success(moduleConfigService.list());
        } catch (Exception e) {
            return HttpResponseUtil.error(e.getMessage());
        }
    }
}
