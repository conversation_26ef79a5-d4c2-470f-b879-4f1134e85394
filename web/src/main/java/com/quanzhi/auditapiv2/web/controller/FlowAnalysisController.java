package com.quanzhi.auditapiv2.web.controller;

import com.quanzhi.auditapiv2.common.dal.entity.NacosSingleKey;
import com.quanzhi.auditapiv2.common.dal.enums.NacosSingleKeyConfigEnum;
import com.quanzhi.auditapiv2.common.util.dto.ResponseVo;
import com.quanzhi.auditapiv2.common.util.utils.HttpResponseUtil;
import com.quanzhi.auditapiv2.core.service.cache.RedisCache;
import com.quanzhi.auditapiv2.core.service.manager.web.IFlowAnalysisService;
import com.quanzhi.auditapiv2.core.service.manager.web.INacosSingleKeyUpdateService;
import com.quanzhi.auditapiv2.web.common.annotation.OperateLog;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.List;

/**
 *
 * 《流量分析Controller》
 *
 *
 * @Project:
 * @Module ID:
 * @Comments:
 * @JDK version used:      <JDK1.8>
 * <AUTHOR> [<EMAIL>]
 * @since 2020-08-28-下午16:52:18
 */
@RestController
@RequestMapping("/api/flowAnalysis")
public class FlowAnalysisController {
    private Logger logger = LoggerFactory.getLogger(FlowAnalysisController.class);

    //流量分析service
    @Autowired
    private IFlowAnalysisService flowAnalysisServiceImpl;

    @Autowired
    private INacosSingleKeyUpdateService nacosSingleKeyUpdateService;

    @Autowired
    private RedisCache<String, Long> redisTemplate;

    private String CACHE_KEY = "FLOW_ANALYSIS_ENABLE";

    /**
     * 流量分析启停
     * @Comments:  <对此方法的描述，可以引用系统设计中的描述>
     * <AUTHOR> [<EMAIL>]
     * @since 2020-08-28 16:52
     * @param
     * @return
     */
    @OperateLog(value="流量分析启停", type = "处理", module = "配置")
    @RequestMapping(value = "enable.do", method = RequestMethod.POST)
    @ApiOperation(value = "流量分析启停")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "enable", value = "流量启停（true-启动，false-关闭）", required = true, paramType = "query", dataType = "boolean")
    })
    public String getApiWeaknessList(Boolean enable) {
        logger.warn("流量分析Controller》流量分析启停");

        List<ResponseVo> list = null;
        try {

            Integer flag;
            if(enable) {
                flag = 1;
            }else {
                flag = 0;
            }

            List<NacosSingleKey> nacosSingleKeys = new ArrayList<NacosSingleKey>();
            nacosSingleKeys.add(new NacosSingleKey(NacosSingleKeyConfigEnum.SSL_PARSER_ENABLE.getSingleKey(), flag));
            nacosSingleKeys.add(new NacosSingleKey(NacosSingleKeyConfigEnum.SMB_PARSER_ENABLE.getSingleKey(), flag));
            nacosSingleKeys.add(new NacosSingleKey(NacosSingleKeyConfigEnum.NFS_PARSER_ENABLE.getSingleKey(), flag));
            nacosSingleKeys.add(new NacosSingleKey(NacosSingleKeyConfigEnum.FTP_PARSER_ENABLE.getSingleKey(), flag));
            nacosSingleKeys.add(new NacosSingleKey(NacosSingleKeyConfigEnum.MAIL_PARSER_ENABLE.getSingleKey(), flag));
            nacosSingleKeys.add(new NacosSingleKey(NacosSingleKeyConfigEnum.FLOW_ANALYSIS_ENABLE.getSingleKey(), enable));

            //最少留存不能为0或负
            for(NacosSingleKey nacosSingleKey:nacosSingleKeys){
                if(nacosSingleKey.getKey().equals("discoverSampleCount")){
                    int count=0;
                    if(nacosSingleKey.getValue() instanceof Integer){
                        count= ((Integer) nacosSingleKey.getValue()).intValue();
                    }
                    if(nacosSingleKey.getValue() instanceof String){
                        count=Integer.parseInt(String.valueOf(nacosSingleKey.getValue()));
                    }
                    if(count<1){
                        return HttpResponseUtil.error("最少留存数量必须为正整数");
                    }
                }
            }
            nacosSingleKeyUpdateService.updateNacosSingleKeys(nacosSingleKeys);

            if(enable) {
                //清除历史缓存
                flowAnalysisServiceImpl.delFlowStateCache();
                flowAnalysisServiceImpl.delUnilateralFlowCache();
                //清除历史表数据
                flowAnalysisServiceImpl.dropTable();

                redisTemplate.opsForValue().set(CACHE_KEY, System.currentTimeMillis());
            }else {
                redisTemplate.delete(CACHE_KEY);
            }
        } catch (Exception e) {
            logger.error("流量分析启停", e);
            return HttpResponseUtil.error(e.getMessage());
        }
        return HttpResponseUtil.success(list);
    }

    /**
     * 流量分析状态
     * @Comments:  <对此方法的描述，可以引用系统设计中的描述>
     * <AUTHOR> [<EMAIL>]
     * @since 2020-08-28 16:52
     * @param
     * @return
     */
    @OperateLog(value="流量分析状态", type = "查看", module = "配置")
    @RequestMapping(value = "status.do", method = RequestMethod.POST)
    @ApiOperation(value = "流量分析状态")
    public String getStatus() {
        logger.warn("流量分析Controller》流量分析启停");

        try {
            flowAnalysisServiceImpl.flowStatus();
        } catch (Exception e) {
            e.printStackTrace();
        }
        return HttpResponseUtil.success();
    }
}
