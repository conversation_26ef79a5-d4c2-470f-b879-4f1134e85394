package com.quanzhi.auditapiv2.web.controller;

import cn.hutool.http.HttpResponse;
import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson.JSON;
import com.quanzhi.audit.mix.permission.annotation.Permission;
import com.quanzhi.audit.mix.permission.domain.enums.ResourceEnum;
import com.quanzhi.auditapiv2.common.dal.dto.HttpClientDto;
import com.quanzhi.auditapiv2.common.util.dto.ResponseVo;
import com.quanzhi.auditapiv2.common.util.utils.DataUtil;
import com.quanzhi.auditapiv2.common.util.utils.UrlUtil;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.io.InputStream;
import java.util.Map;

/**
 *
 * 《http请求Controller》
 *
 *
 * @Project:
 * @Module ID:
 * @Comments:
 * @JDK version used:      <JDK1.8>
 * <AUTHOR> [<EMAIL>]
 * @since 2020-04-08-上午9:56:10
 */
@RestController
@RequestMapping("/api/httpClient")
public class HttpClientController {
    private Logger logger = LoggerFactory.getLogger(HttpClientController.class);

    /**
     * HttpClient GET请求
     * @Comments:  <对此方法的描述，可以引用系统设计中的描述>
     * <AUTHOR> [<EMAIL>]
     * @since 2022-05-20 9:56
     * @param
     * @return
     */
    @RequestMapping(value = "httpClientByGet.do", method = RequestMethod.GET)
    @ApiOperation(value = "HttpClient GET请求")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "url", value = "请求地址", required = true, paramType = "query", dataType = "String"),
            @ApiImplicitParam(name = "params", value = "请求参数", required = false, paramType = "query", dataType = "String")
    })
    public ResponseVo<String> httpClientByGet(@RequestParam String url, @RequestParam(required = false) String params) {
        logger.warn("httpClientController》HttpClient GET请求");
        String host = UrlUtil.getUrlHost(url);
        if (!host.contains("monitor-server")){
            return ResponseVo.error(500, "请求地址不合法");
        }
        String rsp = null;
        try {
            if(DataUtil.isNotEmpty(params)){
                Map<String, Object> map = (Map<String, Object>) JSON.parse(params);
                if(DataUtil.isNotEmpty(map)) {

                    url = url + "?";
                    for (String key : map.keySet()) {
                        url = url + key + "=" + map.get(key) + "&";
                    }
                }
                url = url.substring(0, url.length() - 1);
            }
            rsp = HttpUtil.createGet(url).execute().body();
        } catch (Exception e) {
            logger.error("HttpClient GET请求", e);
            return ResponseVo.error(500, e.getMessage());
        }
        return ResponseVo.ok(rsp);
    }

    /**
     * HttpClient POST请求
     * @Comments:  <对此方法的描述，可以引用系统设计中的描述>
     * <AUTHOR> [<EMAIL>]
     * @since 2022-05-20 9:56
     * @param
     * @return
     */
    @RequestMapping(value = "httpClientByPost.do", method = RequestMethod.POST)
    @ApiOperation(value = "HttpClient POST请求")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "httpClientDto", value = "HttpClient请求DTO", required = true, paramType = "body", dataType = "HttpClientDto", dataTypeClass = HttpClientDto.class)
    })
    @Permission(type = ResourceEnum.ACTION, value = "WRITE")
    public ResponseVo<String> httpClientByPost(@RequestBody HttpClientDto httpClientDto) {
        logger.warn("httpClientController》HttpClient POST请求");
        String host = UrlUtil.getUrlHost(httpClientDto.getUrl());
        if (!host.contains("monitor-server")){
            return ResponseVo.error(500, "请求地址不合法");
        }
        String rsp = null;
        try {
            rsp = HttpUtil.createPost(httpClientDto.getUrl()).body(JSON.toJSONString(httpClientDto.getParams())).execute().body();
        } catch (Exception e) {
            logger.error("HttpClient POST请求", e);
            return ResponseVo.error(500, e.getMessage());
        }
        return ResponseVo.ok(rsp);
    }

    /**
     * HttpClient 下载请求
     * @Comments:  <对此方法的描述，可以引用系统设计中的描述>
     * <AUTHOR> [<EMAIL>]
     * @since 2022-05-20 9:56
     * @param
     * @return
     */
    @RequestMapping(value = "httpClientByDownload.do", method = RequestMethod.GET)
    @ApiOperation(value = "HttpClient 下载请求")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "url", value = "请求地址", required = true, paramType = "query", dataType = "String"),
            @ApiImplicitParam(name = "params", value = "请求参数", required = false, paramType = "query", dataType = "String")
    })
    @Permission(type = ResourceEnum.ACTION, value = "WRITE")
    public void httpClientByDownload(@RequestParam String url, @RequestParam(required = false) String params, HttpServletResponse response) {
        logger.warn("httpClientController》HttpClient 下载请求");

        try {
            if(DataUtil.isNotEmpty(params)){
                Map<String, Object> map = (Map<String, Object>) JSON.parse(params);
                if(DataUtil.isNotEmpty(map)) {

                    url = url + "?";
                    for (String key : map.keySet()) {
                        url = url + key + "=" + map.get(key) + "&";
                    }
                }
                url = url.substring(0, url.length() - 1);
            }

            HttpResponse rsp = HttpUtil.createGet(url).execute();
            InputStream inputStream = rsp.bodyStream();

            // 文件的存放路径
            // 设置输出的格式
            response.reset();
            response.setContentType("application/octet-stream");
            response.addHeader("Content-Disposition", "attachment; filename=\"" + rsp.header("filename") + "\"");
            // 循环取出流中的数据
            byte[] b = new byte[100];
            int len;
            while ((len = inputStream.read(b)) > 0) {
                response.getOutputStream().write(b, 0, len);
            }
            response.getOutputStream().flush();
            response.getOutputStream().close();
            inputStream.close();
        } catch (Exception e) {
            logger.error("HttpClient 下载请求", e);
            ResponseVo.error(500, e.getMessage());
        }
    }
}
