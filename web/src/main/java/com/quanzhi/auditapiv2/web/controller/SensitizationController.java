package com.quanzhi.auditapiv2.web.controller;

import com.quanzhi.auditapiv2.common.util.utils.DesensitizationUtil;
import com.quanzhi.auditapiv2.common.util.utils.HttpResponseUtil;
import com.quanzhi.auditapiv2.web.common.annotation.OperateLog;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.util.List;
import java.util.stream.Collectors;


/**
 * @Author: HaoJun
 * @Date: 2020/2/10 3:22 下午
 */
@RestController
@RequestMapping("/api/sensitize")
@Api("解脱敏")
public class SensitizationController {

    @OperateLog(value="查看脱敏数据", type = "查看", module = "样例")
    @ApiOperation("批量解脱敏")
    @RequestMapping(value = "/query", method = RequestMethod.POST)
    public String sensitize(@RequestParam(value = "keys",required = true) List<String> keys, HttpServletRequest request) {
        List<String> values = keys.stream().map(key-> DesensitizationUtil.sensitize(key))
                .collect(Collectors.toList());

        return HttpResponseUtil.success(values);
    }
}
