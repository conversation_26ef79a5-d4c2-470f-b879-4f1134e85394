package com.quanzhi.auditapiv2.web.controller;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.nacos.api.config.annotation.NacosValue;
import com.quanzhi.auditapiv2.common.dal.dto.query.ApiAccountInfoCriteriaDto;
import com.quanzhi.auditapiv2.common.dal.enums.ProductTypeEnum;
import com.quanzhi.auditapiv2.common.util.utils.HttpResponseUtil;
import com.quanzhi.auditapiv2.core.service.manager.web.impl.ApiAccountInfoServiceImpl;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * @ClassName ApiAccountInfoController
 * @Description TODO
 * <AUTHOR>
 * @Date 2020/11/23 9:49
 **/
@RestController
@RequestMapping("/apiAccountInfo")
@Slf4j
public class ApiAccountInfoController {


    private Logger logger = LoggerFactory.getLogger(ApiAccountInfoController.class);

    @Autowired
    private ApiAccountInfoServiceImpl apiAccountInfoService;

    @NacosValue(value = "${product.type:api}", autoRefreshed = true)
    private String productType;

    @ApiOperation("导出账号列表")
    @RequestMapping(value = "/exportApiAccountInfo",method = RequestMethod.POST)
    public String exportApiAccountInfo(@RequestBody ApiAccountInfoCriteriaDto apiAccountInfoCriteriaDto,
                                       @RequestParam(required = false, value = "dir") String dir,
                                       @RequestParam(required = false, value = "fileName") String fileName,
                                       @RequestParam(required = false, value = "formJson") String formJson
){

        try {
            if (productType.equals(ProductTypeEnum.wph.name())) {
                JSONObject jsonObject = JSONObject.parseObject(formJson);
                apiAccountInfoCriteriaDto.setApplicant_id(jsonObject.getString("applicant_id"));
                apiAccountInfoCriteriaDto.setTags(jsonObject.getString("tags"));
                apiAccountInfoCriteriaDto.setUsage(jsonObject.getString("usage"));
                apiAccountInfoCriteriaDto.setReasons(jsonObject.getString("reasons"));
            }
            return HttpResponseUtil.success(apiAccountInfoService.exportApiAccountInfo(apiAccountInfoCriteriaDto,dir,fileName));

        } catch (Exception e){
            logger.error("导出ApiAccountInfo错误：{}",e);
            return HttpResponseUtil.error("导出ApiAccountInfo错误");
        }
    }
}
