package com.quanzhi.auditapiv2.web.controller;

import com.quanzhi.auditapiv2.common.util.dto.ResponseVo;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

@Slf4j
@RestController
@RequestMapping("/api/fullFlow")
public class FullFlowConfigController {

    @RequestMapping(value = "/update", method = RequestMethod.POST)
    @ApiOperation(value = "应用审计状态开关操作")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "uri", value = "应用uri", required = true, paramType = "query", dataType = "String",example = "httpapp:www.baidu.com"),
            @ApiImplicitParam(name = "flag", value = "开/关", required = true, paramType = "query", dataType = "Boolean",example = "false")
    })
    public ResponseVo<?> update(String uri,boolean flag) {
        try {
            return ResponseVo.ok();
        }catch (Exception e){
            log.error("update uri:{},flag:{},auditFlag error:",uri,flag,e);
            return ResponseVo.error(500,e.getMessage());
        }
    }
}
