package com.quanzhi.auditapiv2.web.controller;

import com.alibaba.fastjson.JSON;
import com.quanzhi.audit.mix.permission.annotation.Permission;
import com.quanzhi.audit.mix.permission.domain.enums.ResourceEnum;
import com.quanzhi.auditapiv2.common.dal.dto.OriginIpResolveDto;
import com.quanzhi.auditapiv2.common.dal.entity.OriginIpResolve;
import com.quanzhi.auditapiv2.common.util.utils.DataUtil;
import com.quanzhi.auditapiv2.common.util.utils.HttpResponseUtil;
import com.quanzhi.auditapiv2.core.service.manager.web.IOriginIpResolveService;
import com.quanzhi.auditapiv2.web.common.annotation.DirectiveSync;
import com.quanzhi.auditapiv2.web.common.annotation.OperateLog;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 《来源IP配置Controller》
 *
 * <AUTHOR> [<EMAIL>]
 * @Project:
 * @Module ID:
 * @Comments:
 * @JDK version used:      <JDK1.8>
 * @since 2020-04-08-上午9:56:10
 */
@RestController
@RequestMapping("/api/originIpResolve")
@Slf4j
public class OriginIpResolveController {

    private final IOriginIpResolveService originIpResolveService;

    public OriginIpResolveController(IOriginIpResolveService originIpResolveService) {
        this.originIpResolveService = originIpResolveService;
    }

    /**
     * 查询来源IP配置列表
     *
     * @param
     * @return
     * @Comments: <对此方法的描述，可以引用系统设计中的描述>
     * <AUTHOR> [<EMAIL>]
     * @since 2020-04-08 9:56
     */
    @RequestMapping(value = "listData.do", method = RequestMethod.POST)
    @ApiOperation(value = "查询来源IP配置列表")
    public String get() {
        log.warn("get origin IP list");

        try {
            return HttpResponseUtil.success(originIpResolveService.getOriginIpResolveList());
        } catch (Exception e) {
            log.error("get origin IP list error", e);
            return HttpResponseUtil.error(e.getMessage());
        }
    }

    /**
     * 保存来源IP配置
     *
     * @param
     * @return
     * @Comments: <对此方法的描述，可以引用系统设计中的描述>
     * <AUTHOR> [<EMAIL>]
     * @since 2020-04-08 9:56
     */
    @OperateLog(value = "编辑源IP解析配置", type = "编辑", module = "IP解析配置")
    @RequestMapping(value = "save.do", method = RequestMethod.POST)
    @DirectiveSync(needNotify = true, remark = "编辑源IP解析配置")
    @ApiOperation(value = "保存来源IP配置")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "dtos", value = "保存内容", required = true, paramType = "body", dataType = "OriginIpResolveDto")
    })
    @Permission(type = ResourceEnum.ACTION, value = "WRITE")
    public String save(@RequestBody List<OriginIpResolveDto> dtos) {
        log.info("save origin IP config");

        try {
            originIpResolveService.saveOriginIpResolve(dtos);
        } catch (Exception e) {
            log.error("save origin IP config error", e);
            return HttpResponseUtil.error(e.getMessage());
        }
        return HttpResponseUtil.success();
    }

    /**
     * 删除来源IP配置
     *
     * @param
     * @return
     * @Comments: <对此方法的描述，可以引用系统设计中的描述>
     * <AUTHOR> [<EMAIL>]
     * @since 2020-04-08 9:56
     */
    @OperateLog(value = "删除源IP解析配置", type = "删除", module = "IP解析配置")
    @RequestMapping(value = "del.do", method = RequestMethod.POST)
    @ApiOperation(value = "删除来源IP配置")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "id", required = true, paramType = "query", dataType = "String")
    })
    @Permission(type = ResourceEnum.ACTION, value = "WRITE")
    public String del(String id) {
        log.info("来源IP配置Controller》删除来源IP配置");

        try {
            if (DataUtil.isNotEmpty(id)) {
                originIpResolveService.delOriginIpResolve(id);
            } else {
                return HttpResponseUtil.error("id不得为空");
            }
        } catch (Exception e) {
            log.error("delete origin IP config error", e);
            return HttpResponseUtil.error(e.getMessage());
        }
        return HttpResponseUtil.success();
    }

    /**
     * 来源IP配置
     *
     * @return
     */
    @RequestMapping(value = "getById.do", method = RequestMethod.POST)
    @ApiOperation(value = "获取IP配置")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "id", required = true, paramType = "query", dataType = "String")
    })
    public String getById(String id) {

        try {
            if (DataUtil.isNotEmpty(id)) {
                return HttpResponseUtil.success(new OriginIpResolveDto(JSON.parseObject(JSON.toJSONString(originIpResolveService.getById(id)), OriginIpResolve.class)));
            } else {
                return HttpResponseUtil.error("id不得为空");
            }
        } catch (Exception e) {
            log.error("get origin IP config error", e);
            return HttpResponseUtil.error(e.getMessage());
        }
    }
}
