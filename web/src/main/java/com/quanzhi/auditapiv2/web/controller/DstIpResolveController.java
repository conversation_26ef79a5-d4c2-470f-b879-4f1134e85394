package com.quanzhi.auditapiv2.web.controller;

import com.alibaba.fastjson.JSON;
import com.alibaba.nacos.api.config.annotation.NacosValue;
import com.quanzhi.audit.mix.permission.annotation.Permission;
import com.quanzhi.audit.mix.permission.domain.enums.ResourceEnum;
import com.quanzhi.auditapiv2.common.dal.dto.DstIpResolveDto;
import com.quanzhi.auditapiv2.common.dal.entity.DstIpResolve;
import com.quanzhi.auditapiv2.common.util.utils.DataUtil;
import com.quanzhi.auditapiv2.common.util.utils.HttpResponseUtil;
import com.quanzhi.auditapiv2.core.service.manager.web.IDstIpResolveService;
import com.quanzhi.auditapiv2.web.common.annotation.DirectiveSync;
import com.quanzhi.auditapiv2.web.common.annotation.OperateLog;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.util.List;


@RestController
@RequestMapping("/api/dstIpResolve")
@Slf4j
public class DstIpResolveController {

    private final IDstIpResolveService dstIpResolveService;

    @NacosValue(value = "${dataLogModule:数据模块}", autoRefreshed = true)
    private String dataLogModule;

    public DstIpResolveController(IDstIpResolveService dstIpResolveService) {
        this.dstIpResolveService = dstIpResolveService;
    }

    @RequestMapping(value = "listData.do", method = RequestMethod.POST)
    @ApiOperation(value = "查询目的IP配置列表")
    public String get(
            @RequestParam(defaultValue = "1") int page,
            @RequestParam(defaultValue = "100") int limit
    ) {
        try {
            return HttpResponseUtil.success(dstIpResolveService.getDstIpResolveList(page, limit));
        } catch (Exception e) {
            log.error("查询目的IP配置列表", e);
            return HttpResponseUtil.error(e.getMessage());
        }
    }


    @OperateLog(module = "IP解析配置", type = "编辑", value = "编辑目的IP配置")
    @RequestMapping(value = "save.do", method = RequestMethod.POST)
    @ApiOperation(value = "保存目的IP配置")
    @DirectiveSync(needNotify = true, remark = "编辑目的IP解析配置")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "dtos", value = "保存内容", required = true, paramType = "body", dataType = "DstIpResolveDto")
    })
    @Permission(type = ResourceEnum.ACTION, value = "WRITE")
    public String save(@RequestBody List<DstIpResolveDto> dtos) {
        log.info("save dstIp parse config");
        try {
            dstIpResolveService.saveDstIpResolves(dtos);
        } catch (Exception e) {
            log.error("save dstIp parse config error", e);
            return HttpResponseUtil.error(e.getMessage());
        }

        return HttpResponseUtil.success();
    }


    @OperateLog(value = "删除目的IP解析配置", module = "IP解析配置", type = "删除")
    @RequestMapping(value = "del.do", method = RequestMethod.POST)
    @ApiOperation(value = "删除目的IP配置")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "网关id", required = true, paramType = "query", dataType = "String")
    })
    @Permission(type = ResourceEnum.ACTION, value = "WRITE")
    public String del(String id) {
        log.info("delete dstIp parser config");

        try {
            if (DataUtil.isNotEmpty(id)) {
                dstIpResolveService.delDstIpResolve(id);
            } else {
                return HttpResponseUtil.error("id不得为空");
            }
        } catch (Exception e) {
            log.error("delete dstIp parser config error", e);
            return HttpResponseUtil.error(e.getMessage());
        }
        return HttpResponseUtil.success();
    }

    /**
     * 来源IP配置
     *
     * @return
     */
    @RequestMapping(value = "getById.do", method = RequestMethod.POST)
    @ApiOperation(value = "获取IP配置")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "id", required = true, paramType = "query", dataType = "String")
    })
    public String getById(String id) {

        try {
            if (DataUtil.isNotEmpty(id)) {
                return HttpResponseUtil.success(new DstIpResolveDto(JSON.parseObject(JSON.toJSONString(dstIpResolveService.getById(id)), DstIpResolve.class)));
            } else {
                return HttpResponseUtil.error("id不得为空");
            }
        } catch (Exception e) {
            log.error("获取目的IP配置", e);
            return HttpResponseUtil.error(e.getMessage());
        }
    }
}
