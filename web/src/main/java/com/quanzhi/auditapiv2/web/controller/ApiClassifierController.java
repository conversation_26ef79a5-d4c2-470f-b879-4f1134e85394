package com.quanzhi.auditapiv2.web.controller;


import com.alibaba.fastjson.JSON;
import com.quanzhi.audit.mix.permission.annotation.Permission;
import com.quanzhi.audit.mix.permission.domain.enums.ResourceEnum;
import com.quanzhi.audit_core.common.model.Accuracy;
import com.quanzhi.audit_core.common.model.ApiClassification;
import com.quanzhi.auditapiv2.common.dal.enums.NacosConfigEnum;
import com.quanzhi.auditapiv2.common.util.dto.ListOutputDto;
import com.quanzhi.auditapiv2.common.util.utils.DataUtil;
import com.quanzhi.auditapiv2.common.util.utils.HttpResponseUtil;
import com.quanzhi.auditapiv2.common.util.utils.ServiceException;
import com.quanzhi.auditapiv2.core.service.manager.web.IApiClassifierService;
import com.quanzhi.auditapiv2.web.common.annotation.DirectiveSync;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiResponse;
import io.swagger.annotations.ApiResponses;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.util.Arrays;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

@RestController
@RequestMapping("/api/apiClassifier")
public class ApiClassifierController {

    @Autowired
    private IApiClassifierService apiClassifierService;

    /**
     * 根据资源类型，决定要操作的nacos配置
     * @param sourceType
     */
    private void initCollectionName(String sourceType){
        if (sourceType.equals(ApiClassification.SourceTypeEnum.HTTP.name())){
            apiClassifierService.initCollectionName(NacosConfigEnum.HTTP_API_CLASSIFICATION.getNacosCollection());
        } else if (sourceType.equals(ApiClassification.SourceTypeEnum.FTP.name())){
            apiClassifierService.initCollectionName(NacosConfigEnum.FTP_API_CLASSIFICATION.getNacosCollection());
        } else if (sourceType.equals(ApiClassification.SourceTypeEnum.EMAIL.name())){
            apiClassifierService.initCollectionName(NacosConfigEnum.EMAIL_API_CLASSIFICATION.getNacosCollection());
        }
    }

    /**
     * 保存接口分类
     * @param
     * @return
     */
    @RequestMapping(value = "/save.do", method = RequestMethod.POST)
    @ApiImplicitParams({
            @ApiImplicitParam(name = "params",
                    value = "分类DTO 返回中type为0是内置，1是自定义分类",
                    required = true, paramType = "query", dataType = "String",
                    example = "{\"id\":\"\",\"name\":\"帅哥类型\",\"desc\":\"啦啦啦\",\"sourceType\":\"HTTP\",\"classificationPolicyList\":[{\"accuracy\":\"HIGH\",\"plugin\":{\"starter\":\"xxx.java\",\"name\":\"xxx.javac\",\"path\":\"/home/<USER>/xxx.javac\"},\"enabled\":true}]}"),
    })
    @DirectiveSync(key = "id", needNotify = true, retry = false, remark = "保存接口分类")
    @Permission(type = ResourceEnum.ACTION, value = "WRITE")
    public String save(
            HttpServletRequest request,
            @RequestParam(value = "params", defaultValue = "") String params
    ) {

        ApiClassification apiClassification = JSON.parseObject(params, ApiClassification.class);

        if(DataUtil.isNotEmpty( apiClassification.getClassificationPolicyList() )) {

            if(apiClassification.getClassificationPolicyList().size() > 2) {
                throw new ServiceException("只允许配置高低两种精度的策略！");
            }

            if( apiClassification.getClassificationPolicyList().stream().filter(classificationPolicy -> classificationPolicy.getAccuracy().equals(Accuracy.HIGH.name())).collect(Collectors.toList()).size() > 1 ) {
                throw new ServiceException("只允许配置一种高精度的策略！");
            }

            if( apiClassification.getClassificationPolicyList().stream().filter(classificationPolicy -> classificationPolicy.getAccuracy().equals(Accuracy.LOW.name())).collect(Collectors.toList()).size() > 1 ) {
                throw new ServiceException("只允许配置一种低精度的策略！");
            }
        }
        if(apiClassification.getType()==null){
            apiClassification.setType(ApiClassification.TypeEnum.CUSTOM.val());
        }
        String username = "";
        if(DataUtil.isNotEmpty(request.getSession().getAttribute("username"))) {
            username = request.getSession().getAttribute("username").toString();
        }
        apiClassification.setCreateUser( username );

        this.initCollectionName( Optional.ofNullable(apiClassification.getSourceType()).orElse("HTTP") );

        apiClassifierService.save(apiClassification);
        return HttpResponseUtil.success();
    }

    /**
     * 分页获取接口分类列表
     * @param page
     * @param limit
     * @return
     */
    /**
     *
     * @param keyword
     * @param sourceType
     * @see ApiClassification.SourceTypeEnum
     * @param page
     * @param limit
     * @return
     */
    @RequestMapping(value = "/page.do", method = RequestMethod.GET)
    @ApiImplicitParams({
            @ApiImplicitParam(name = "keyword", value = "关键字：没用到", required = false, paramType = "query", dataType = "String"),
            @ApiImplicitParam(name = "sourceType", value = "资源类型：HTTP、FTP、EMAIL", required = true, paramType = "query", dataType = "String"),
            @ApiImplicitParam(name = "page", value = "当前页", required = true, paramType = "query", dataType = "int"),
            @ApiImplicitParam(name = "limit", value = "每页显示条数", required = true, paramType = "query", dataType = "int")
    })
    @ApiResponses({
            @ApiResponse(code = 200,
                    message = "{\"errorCode\":0,\"success\":true,\"data\":{\"rows\":[{ \"id\":\"\",\"name\":\"帅哥类型\",\"desc\":\"啦啦啦\",\"ClassificationPolicy\":[{\"accuracy\":\"HIGH\",\"pluginId\":\"xxx\",\"enabled\":true}],\"createTime\":1586771208000,\"createUser\":\"admin\"}]},\"totalCount\":1}}",
                    response = ApiClassification.class
            ),
    })
    public String page(
            @RequestParam(value = "keyword", defaultValue = "",required = false) String keyword,
            @RequestParam(value = "sourceType", defaultValue = "HTTP",required = true) String sourceType,
            @RequestParam(value = "page", defaultValue = "") Integer page,
            @RequestParam(value = "limit", defaultValue = "") Integer limit
            ) {
        this.initCollectionName(sourceType);

        ListOutputDto data = apiClassifierService.page(keyword, sourceType, page, limit);
        return HttpResponseUtil.success(data);
    }

    /**
     * 根据ID，获取详情
     * @param id
     * @return
     */
    @RequestMapping(value = "/getById.do", method = RequestMethod.GET)
    @ApiResponses({
            @ApiResponse(code = 200,
                    message = "{\"errorCode\":0,\"success\":true,\"data\":{ \"id\":\"\",\"name\":\"帅哥类型\",\"desc\":\"啦啦啦\",\"ClassificationPolicy\":[{\"accuracy\":\"HIGH\",\"pluginId\":\"xxx\",\"enabled\":true}],\"createTime\":1586771208000,\"createUser\":\"admin\"}",
                    response = ApiClassification.class
            ),
    })
    public String getById(
            @RequestParam(value = "sourceType", defaultValue = "HTTP",required = true) String sourceType,
            @RequestParam(value = "id", defaultValue = "") String id
    ) {
        this.initCollectionName(sourceType);

        return HttpResponseUtil.success(apiClassifierService.getById(id));
    }

    /**
     * 根据ID，删除分类
     * @param ids
     * @return
     */
    @DirectiveSync(key = "id", needNotify = true, retry = false, remark = "删除接口分类")
    @RequestMapping(value = "/delete.do", method = RequestMethod.POST)
    @Permission(type = ResourceEnum.ACTION, value = "WRITE")
    public String delete(@RequestParam(value = "sourceType", defaultValue = "HTTP",required = true) String sourceType,
                         @RequestParam String ids) {

        if(DataUtil.isNotEmpty(ids)) {

            this.initCollectionName(sourceType);

            String [] idArray = ids.split(",");
            List<String> idList = Arrays.asList(idArray);
            for (String id : idList) {
                
                ApiClassification apiClassification = new ApiClassification();

                apiClassification.setId(id);
                apiClassification.setDelFlag(true);
                apiClassification.setUpdateTime(System.currentTimeMillis());

                this.initCollectionName( Optional.ofNullable(apiClassification.getSourceType()).orElse("HTTP") );

                apiClassifierService.delete(apiClassification);
            }
        }
        
        return HttpResponseUtil.success();
    }
}
