package com.quanzhi.auditapiv2.web.controller;

import com.quanzhi.auditapiv2.common.util.dto.ResponseVo;
import com.quanzhi.auditapiv2.core.risk.dto.RiskStrategyDetailDto;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/api/commonAction")
@Slf4j
public class CommonActionStrategyController {


    //TODO：根据IP地址查询IP是否配置管控策略 配置后修改状态
    @RequestMapping(value = "/getStrategyDetail", method = RequestMethod.GET)
    @ApiOperation(value = "根据IP地址查询IP是否配置管控策略 配置后修改状态")
    public ResponseVo<RiskStrategyDetailDto> getStrategyIpDetail(String ip) {
        return ResponseVo.ok();
    }
}
