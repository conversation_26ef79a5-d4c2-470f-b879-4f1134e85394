package com.quanzhi.auditapiv2.web.controller;


import com.quanzhi.audit_core.common.model.EmailFeatureLabel;
import com.quanzhi.audit_core.common.model.FeatureLabel;
import com.quanzhi.auditapiv2.common.util.dto.ListOutputDto;
import com.quanzhi.auditapiv2.common.util.utils.DataUtil;
import com.quanzhi.auditapiv2.common.util.utils.HttpResponseUtil;
import com.quanzhi.auditapiv2.core.service.manager.web.IEmailFeatureLabelService;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiResponse;
import io.swagger.annotations.ApiResponses;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.List;

@RestController
@RequestMapping("/api/emailFeatureLabel")
public class EmailFeatureLabelController {


    @Autowired
    private IEmailFeatureLabelService emailFeatureLabelService;

    /**
     * 保存接口分类
     * @param
     * @return
     */
    @RequestMapping(value = "/save.do", method = RequestMethod.POST)
    public String save(
            HttpServletRequest request,
            @RequestBody EmailFeatureLabel emailFeatureLabel

    ) {
        if(DataUtil.isNotEmpty( request.getSession().getAttribute("username") )) {
            String username = request.getSession().getAttribute("username").toString();
            emailFeatureLabel.setOperateName(username);
        }

        if(DataUtil.isEmpty( emailFeatureLabel.getId() )) {
            emailFeatureLabel.setCreateTime( System.currentTimeMillis() );
        }
        emailFeatureLabel.setUpdateTime(System.currentTimeMillis());

        emailFeatureLabelService.save(emailFeatureLabel);
        return HttpResponseUtil.success();
    }

    /**
     * 分页获取接口分类列表
     * @param page
     * @param limit
     * @return
     */
    @RequestMapping(value = "/page.do", method = RequestMethod.GET)
    @ApiImplicitParams({
            @ApiImplicitParam(name = "keyword", value = "搜索内容",
                    required = false, paramType = "query", dataType = "String"),
            @ApiImplicitParam(name = "page", value = "page",
                    required = true, paramType = "query", dataType = "Integer"),
            @ApiImplicitParam(name = "limit", value = "limit",
                    required = true, paramType = "query", dataType = "Integer")
    })
    @ApiResponses({
            @ApiResponse(code = 200,
                    message = "{\"success\":true,\"errorCode\":0,\"data\":{\"rows\":[{\"id\":\"xxx\",\"name\":\"abc\",\"desc\":\"123\",\"enabled\":true,\"operateName\":\"mingming\",\"createTime\":\"1589426873000\",\"Type\":1,\"classificationType\":\"APP\"}],\"totalCount\":1}}",
                    response = FeatureLabel.class
            ),
    })
    public String page(
            @RequestParam(value = "keyword", defaultValue = "",required = false) String keyword,
            @RequestParam(value = "page", defaultValue = "1") Integer page,
            @RequestParam(value = "limit", defaultValue = "10") Integer limit
    ) {

        ListOutputDto<EmailFeatureLabel> data = emailFeatureLabelService.page(page, limit);
        return HttpResponseUtil.success(data);
    }

    /**
     * 根据ID，获取详情
     * @param id
     * @return
     */
    @RequestMapping(value = "/getById.do", method = RequestMethod.GET)
    @ApiResponses({
            @ApiResponse(code = 200,
                    message = "{\"success\":true,\"errorCode\":0,\"data\":{\"id\":\"xxx\",\"name\":\"abc\",\"desc\":\"123\",\"enabled\":true,\"operateName\":\"mingming\",\"createTime\":\"1589426873000\",\"Type\":1,\"classificationType\":\"APP\"}}",
                    response = FeatureLabel.class
            ),
    })
    public String getById(
            @RequestParam(value = "id", defaultValue = "") String id
    ) {
        return HttpResponseUtil.success(emailFeatureLabelService.getById(id));
    }

    /**
     * 根据ID，删除分类
     * @param ids
     * @return
     */
    @RequestMapping(value = "/delete.do", method = RequestMethod.POST)
    public String delete(@RequestParam List<String> ids) {
        emailFeatureLabelService.delete(ids);
        return HttpResponseUtil.success();
    }
}
