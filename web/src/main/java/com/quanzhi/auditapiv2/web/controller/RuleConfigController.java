package com.quanzhi.auditapiv2.web.controller;

import com.quanzhi.auditapiv2.common.util.dto.ListOutputDto;
import com.quanzhi.auditapiv2.common.util.dto.ResponseVo;
import com.quanzhi.auditapiv2.core.service.manager.web.impl.RuleConfigService;
import com.quanzhi.re.core.application.RuleMapConfigService;
import com.quanzhi.re.core.domain.entity.po.RuleConfigPO;
import com.quanzhi.re.core.domain.entity.po.RuleMapConfig;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@Slf4j
@RestController
@RequestMapping("/api/rule")
public class RuleConfigController {

    private final RuleConfigService ruleConfigService;

    private final RuleMapConfigService ruleMapConfigService;

    public RuleConfigController(RuleConfigService ruleConfigService, RuleMapConfigService ruleMapConfigService) {
        this.ruleConfigService = ruleConfigService;
        this.ruleMapConfigService = ruleMapConfigService;
    }

    @PostMapping("/save")
    @ApiOperation(value = "规则保存或更新")
    public ResponseVo<?> save(@RequestBody RuleConfigPO ruleConfigPO) {
        try {
            ruleConfigService.save(ruleConfigPO);
        }catch (Exception e){
            log.error("save rule error",e);
            return ResponseVo.error(500,e.getMessage());
        }
        return ResponseVo.ok();
    }

    @GetMapping("/getRuleById")
    @ApiOperation(value = "查看单个规则详情")
    public ResponseVo<RuleConfigPO> getRuleById(@RequestParam String id) {
        try {
            RuleConfigPO ruleConfigDTO = ruleConfigService.findById(id);
            return ResponseVo.ok(ruleConfigDTO);
        }catch (Exception e){
            log.error("query single rule:{} error",id,e);
            return ResponseVo.error(500,e.getMessage());
        }
    }

    @GetMapping("/getRuleMap")
    @ApiOperation(value = "获取所有配置需要的简单枚举")
    public ResponseVo<ListOutputDto<RuleMapConfig>> getRuleMap() {
        try {
            ListOutputDto<RuleMapConfig> ruleConfigDTO = new ListOutputDto<>();
            List<RuleMapConfig> all = ruleMapConfigService.getAll();
            ruleConfigDTO.setRows(all);
            ruleConfigDTO.setTotalCount(all.size());
            return ResponseVo.ok(ruleConfigDTO);
        }catch (Exception e){
            log.error("query rule map config error",e);
            return ResponseVo.error(500,e.getMessage());
        }
    }
}
