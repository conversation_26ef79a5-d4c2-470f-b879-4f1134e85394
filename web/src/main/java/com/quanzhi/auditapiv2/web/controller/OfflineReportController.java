package com.quanzhi.auditapiv2.web.controller;

import com.alibaba.nacos.api.config.annotation.NacosValue;
import com.quanzhi.audit.mix.permission.annotation.DataPermission;
import com.quanzhi.audit.mix.permission.annotation.Permission;
import com.quanzhi.audit.mix.permission.domain.enums.ResourceEnum;
import com.quanzhi.auditapiv2.common.dal.dao.IReportTaskDao;
import com.quanzhi.auditapiv2.common.dal.dao.IXxlJobDao;
import com.quanzhi.auditapiv2.common.util.utils.HttpResponseUtil;
import com.quanzhi.auditapiv2.core.report.manage.IReportMaker;
import com.quanzhi.auditapiv2.core.report.manage.model.ReportTask;
import com.quanzhi.auditapiv2.core.service.SysLogService;
import com.quanzhi.auditapiv2.web.common.annotation.OperateLog;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2020/10/22 下午4:03
 */

@RestController
@RequestMapping("/api/offlineReport")
@Slf4j
public class OfflineReportController {

    @Autowired
    private IReportMaker reportMaker;

    @Autowired
    private IXxlJobDao xxlJobDao;

    @Autowired
    private IReportTaskDao reportTaskDao;

    @NacosValue(value = "${auditLogModule:审计模块}",autoRefreshed = true)
    private String auditLogModule;

    @Resource
    private SysLogService sysLogService;

    @Autowired
    private MongoTemplate mongoTemplate;

    @PostMapping("/getOfflineReport.do")
    public String getOfflineReport(@RequestBody ReportTask reportTask, HttpServletRequest request) {

        reportMaker.makeReport(reportTask);

        return HttpResponseUtil.success();
    }

    @OperateLog(value = "创建资产与弱点离线审计任务",type = "新增",module = "溯源")
    @PostMapping("/startOfflineReport.do")
    @Permission(type = ResourceEnum.ACTION, value = "WRITE")
    @DataPermission
    public String startOfflineReport(@RequestBody ReportTask reportTask, HttpServletRequest request) {
        try {
            reportTask.check(reportTask);
            if ( mongoTemplate.exists( Query.query( Criteria.where("name").is( reportTask.getName().trim()) ) ,"reportTask") ) {
                return HttpResponseUtil.error("资产与弱点离线审计报告名称不能重复！");
            }
            if (!reportMaker.checkData(reportTask.getReportSearchDtoList())){
                return HttpResponseUtil.error("报告所需数据可能为空，请确认筛选条件");
            }
        } catch (Exception e) {
            return HttpResponseUtil.error(e.getMessage());
        }
        // 直接慢慢写得了，不要调离线任务了
//         xxlJobDao.addOfflineReportTask(JSON.toJSONString(reportTask));
        reportMaker.makeReport(reportTask);
//        if(DataUtil.isNotEmpty(reportTask.getName())){
//            sysLogService.insertLog(auditLogModule,"创建" + reportTask.getName() + "任务");
//        }
        Map<String, String> task = new HashMap<>();
        task.put("taskName", reportTask.getName());
        return HttpResponseUtil.success(task);
    }

    @OperateLog(value = "删除资产与弱点离线审计任务",type = "编辑",module = "溯源")
    @PostMapping("/deleteOfflineReport.do")
    @Permission(type = ResourceEnum.ACTION, value = "WRITE")
    public String deleteOfflineReport(@RequestParam String taskId, HttpServletRequest request) {

        reportMaker.deleteReport(taskId);

        return HttpResponseUtil.success();
    }

    /**
     * 下载结果
     */
    @OperateLog(value = "下载资产与弱点离线审计任务",type = "导出",module = "溯源")
    @RequestMapping(value = "/downloadResult.do", method = RequestMethod.GET)
    @ApiOperation(httpMethod = "GET", value = "下载导出结果", response = Map.class)
    @ApiImplicitParams({
            @ApiImplicitParam(name = "taskId", value = "taskId", dataType = "String", paramType = "query")
    })
    @Permission(type = ResourceEnum.ACTION, value = "WRITE")
    public String downloadResult(HttpServletResponse response,
                                 @RequestParam(value = "taskId") String taskId
    ) {
        return reportMaker.downloadReport(taskId,response);
    }
}
