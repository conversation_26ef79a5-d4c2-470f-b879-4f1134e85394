package com.quanzhi.auditapiv2.web.interceptor;

import com.alibaba.fastjson.JSONObject;
import com.quanzhi.auditapiv2.common.util.constant.SessionConstant;
import com.quanzhi.auditapiv2.common.util.dto.ResponseVo;
import com.quanzhi.auditapiv2.common.util.utils.DataUtil;
import com.quanzhi.auditapiv2.common.util.utils.HttpResponseUtil;
import com.quanzhi.auditapiv2.core.model.LicenseConfigDto;
import com.quanzhi.auditapiv2.core.service.facade.impl.LicenseFacadeImpl;
import com.quanzhi.auditapiv2.web.common.utils.RequestUtil;
import com.quanzhi.auditapiv2.web.share.enums.LicenseModuleEnum;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.servlet.handler.HandlerInterceptorAdapter;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * @Author: Linlm
 * @Description:
 * @Date: Created in 2020/7/14 下午6:50
 */
public class ProductLicenseInterceptor extends HandlerInterceptorAdapter {
    private static final Logger logger = LoggerFactory.getLogger(ProductLicenseInterceptor.class);

    private final String module_veiw_pre = "audit-api:";

    private LicenseFacadeImpl licenseFacade;
    private Boolean isDev;

    public ProductLicenseInterceptor(LicenseFacadeImpl licenseFacade, Boolean isDev){
        this.licenseFacade = licenseFacade;
        this.isDev = isDev;
    }

    /**
     * 拦截后，校验完产品授权期限，需要校验每个模块的授权情况
     *
     * @param request
     * @param response
     * @param o
     * @return
     */
    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object o) {
        //一次请求，一次校验，否则可能会出错
        synchronized (this) {
            // 如果是开发环境，则不拦截
            if ((isDev != null) && isDev){
                String deviceCode = "mHU8bLnryHqUj9CENTdpmZWxoYjy9O2J";
                request.getSession().setAttribute(SessionConstant.DEVICE_CODE_SESSION_KEY, deviceCode);

                return true;
            }

            logger.debug("请求前授权拦截");
            LicenseConfigDto licenseConfigDto = null;
            try {
                licenseConfigDto = licenseFacade.licenseDetail();
            } catch (Exception e) {
                this.writeError(e.getMessage(), response);
                return false;
            }

            //当前授权文件状态  0:未授权  1:已授权  2:已过期
            if (licenseConfigDto.getState() == 1) {
                // 读取授权文件，获取设备唯一编码
                String deviceCode = licenseConfigDto.getUniqueCode();
                request.getSession().setAttribute(SessionConstant.DEVICE_CODE_SESSION_KEY, deviceCode);

                ResponseVo responseVo = this.checkModule(request, licenseConfigDto);
                if (!responseVo.isSuccess()) {
                    this.writeError(responseVo.getMsg(), response);
                    return false;
                }

                return true;
            } else {
                String msg = "未授权";
                if (licenseConfigDto.getState() == 2) {
                    msg = "不在授权有效期内";
                }

                this.writeError(msg, response);
                return false;
            }
        }
    }

    /**
     * 组装输出到前端页面的错误信息
     * @param msg
     * @param response
     */
    private void writeError(String msg, HttpServletResponse response) {
        String outMsg = HttpResponseUtil.error(700, msg);
        RequestUtil.writeOut(response, outMsg);
    }

    /**
     * 校验具体模块的授权
     *
     * @param request
     * @return
     */
    private ResponseVo checkModule(HttpServletRequest request, LicenseConfigDto licenseConfigDto) {
        ResponseVo responseVo = new ResponseVo();
        responseVo.setSuccess(true);

        String api = request.getServletPath();
        String wildcardApi = api.substring(0, api.lastIndexOf("/")) + "/*";

        for (LicenseModuleEnum licenseModuleEnum : LicenseModuleEnum.values()) {
            String moduleCode = licenseModuleEnum.getModuleCode();
            List<String> moduleApis = licenseModuleEnum.getModuleApis();
            String moduleDesc = licenseModuleEnum.getModuleDesc();

            if (moduleApis.contains(api) || moduleApis.contains(wildcardApi)) {
                if (moduleCode.startsWith(module_veiw_pre)){
                    // 判断是否拥有指定模块的前端展示权限
                    moduleCode = moduleCode.substring(module_veiw_pre.length());

                    String viewModule = licenseConfigDto.getModules();
                    if (DataUtil.isNotEmpty(viewModule)){
                        JSONObject jsonObject = JSONObject.parseObject(viewModule);
                        JSONObject authority = jsonObject.getJSONObject("authority");
                        if (DataUtil.isNotEmpty(authority)){
                           Integer view = authority.getInteger(moduleCode);
                           if (view == null || view.equals(0)){
                               responseVo.setSuccess(false);
                               responseVo.setMsg("模块【" + moduleDesc + "】没有授权");
                           }else{
                               // 工具模块需要工具管理员角色才能使用
                               if (moduleCode.equalsIgnoreCase(LicenseModuleEnum.RW.name())){
                                   String roleName = (String) request.getSession().getAttribute("roleName");
                                    if (roleName == null || !roleName.contains("工具管理员")){
                                        responseVo.setSuccess(false);
                                        responseVo.setMsg("模块【" + moduleDesc + "】需要工具管理员角色");
                                    }
                               }
                           }
                        }
                    }
                } else {
                    // 判断指定模块的授权是否过期
                    boolean moduleExpired = licenseFacade.checkExpired(moduleCode);

                    if (moduleExpired) {
                        // 已过期
                        responseVo.setSuccess(false);
                        responseVo.setMsg("模块【" + moduleDesc + "】不在授权有效期内");
                    }
                }
            }
        }

        return responseVo;
    }
}