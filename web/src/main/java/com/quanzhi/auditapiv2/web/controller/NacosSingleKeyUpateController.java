package com.quanzhi.auditapiv2.web.controller;

import com.quanzhi.audit.mix.permission.annotation.Permission;
import com.quanzhi.audit.mix.permission.domain.enums.ResourceEnum;
import com.quanzhi.auditapiv2.common.dal.entity.NacosSingleKey;
import com.quanzhi.auditapiv2.common.util.utils.DataUtil;
import com.quanzhi.auditapiv2.common.util.utils.HttpResponseUtil;
import com.quanzhi.auditapiv2.core.service.manager.web.ILoginAuthService;
import com.quanzhi.auditapiv2.core.service.manager.web.INacosSingleKeyUpdateService;
import com.quanzhi.auditapiv2.web.common.annotation.DirectiveSync;
import com.quanzhi.auditapiv2.web.common.annotation.OperateLog;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.util.List;
import java.util.Map;

import static com.quanzhi.auditapiv2.core.service.manager.web.impl.LoginAuthServiceImpl.GOOGLE_AUTH;

/**
 * <AUTHOR>
 * @date 2020/10/22 下午4:03
 */

@RestController
@RequestMapping("/api/nacosSingelKey")
@Slf4j
public class NacosSingleKeyUpateController {

    @Autowired
    private INacosSingleKeyUpdateService nacosSingleKeyUpdateService;

    @Autowired
    private ILoginAuthService loginAuthService;

    @OperateLog(module = "配置", type = "编辑", value = "更新配置")
    @PostMapping("/updateNacosSingleKeys.do")
    @DirectiveSync(needNotify = true, remark = "更新配置")
    @Permission(type = ResourceEnum.ACTION, value = "WRITE")
    public String updateNacosSingleKeys(@RequestBody List<NacosSingleKey> nacosSingleKeys, HttpServletRequest request) {

        if (DataUtil.isNotEmpty(nacosSingleKeys)) {
            NacosSingleKey authKey = null;
            //最少留存不能为0或负
            for (NacosSingleKey nacosSingleKey : nacosSingleKeys) {
                if (nacosSingleKey.getKey().equals("discoverSampleCount")) {
                    int count = 0;
                    if (nacosSingleKey.getValue() instanceof Integer) {
                        count = ((Integer) nacosSingleKey.getValue()).intValue();
                    }
                    if (nacosSingleKey.getValue() instanceof String) {
                        count = Integer.parseInt(String.valueOf(nacosSingleKey.getValue()));
                    }
                    if (count < 1 || nacosSingleKey.getValue().toString().contains(".")) {
                        return HttpResponseUtil.error("最少留存数量必须为正整数");
                    }
                }
                if (GOOGLE_AUTH.equals(String.valueOf(nacosSingleKey.getValue()))) {
                    authKey = nacosSingleKey;
                }
            }
            nacosSingleKeyUpdateService.updateNacosSingleKeys(nacosSingleKeys);
            //更新成功后才会执行
            if (authKey != null) {
                loginAuthService.updateGoogleSecret(null, (Boolean) authKey.getOtherField());
            }

            return HttpResponseUtil.success();
        } else {
            return HttpResponseUtil.error("参数值不能为空");
        }

    }

    @PostMapping("/getNacosSingleKeyValues.do")
    public String getNacosSingleKeyValues(@RequestBody List<String> nacosSingleKeys, HttpServletRequest request) {

        Map<String, Object> results = nacosSingleKeyUpdateService.getNacosSingleKeyValus(nacosSingleKeys);

        return HttpResponseUtil.success(results);
    }

}
