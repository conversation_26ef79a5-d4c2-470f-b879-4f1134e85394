<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">

    <parent>
        <groupId>com.quanzhi.auditapiv2</groupId>
        <artifactId>auditapiv2</artifactId>
        <version>3.3.0-SNAPSHOT</version>
        <relativePath>../pom.xml</relativePath>
    </parent>

    <modelVersion>4.0.0</modelVersion>

    <artifactId>auditapiv2-web</artifactId>
    <packaging>jar</packaging>

    <name>auditapiv2-web</name>

    <dependencies>

        <!-- sub projects -->
        <dependency>
            <groupId>com.quanzhi.auditapiv2</groupId>
            <artifactId>auditapiv2-common-dal</artifactId>
        </dependency>
        <dependency>
            <groupId>io.jsonwebtoken</groupId>
            <artifactId>jjwt</artifactId>
            <version>0.9.1</version>
        </dependency>

        <dependency>
            <groupId>com.quanzhi.auditapiv2</groupId>
            <artifactId>auditapiv2-common-monitor</artifactId>
        </dependency>

        <dependency>
            <groupId>com.quanzhi.auditapiv2</groupId>
            <artifactId>auditapiv2-biz-risk</artifactId>
        </dependency>

        <dependency>
            <groupId>com.quanzhi.auditapiv2</groupId>
            <artifactId>auditapiv2-common-report</artifactId>
        </dependency>

        <dependency>
            <groupId>com.quanzhi.auditapiv2</groupId>
            <artifactId>auditapiv2-report-manage</artifactId>
        </dependency>

        <dependency>
            <groupId>com.quanzhi.auditapiv2</groupId>
            <artifactId>auditapiv2-report-api</artifactId>
        </dependency>

        <dependency>
            <groupId>com.quanzhi.auditapiv2</groupId>
            <artifactId>auditapiv2-core-service</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>dubbo</artifactId>
                    <groupId>org.apache.dubbo</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>encrypt-sdk</artifactId>
                    <groupId>com.quanzhi</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.quanzhi.auditapiv2</groupId>
            <artifactId>auditapiv2-common-util</artifactId>
        </dependency>

        <dependency>
            <groupId>com.quanzhi.auditapiv2</groupId>
            <artifactId>auditapiv2-biz-schedule</artifactId>
        </dependency>

        <dependency>
            <groupId>com.github.xiaoymin</groupId>
            <artifactId>knife4j-spring-boot-starter</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>io.springfox</groupId>
                    <artifactId>springfox-spring-webmvc</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>io.minio</groupId>
            <artifactId>minio</artifactId>
        </dependency>

        <dependency>
            <groupId>com.fasterxml</groupId>
            <artifactId>classmate</artifactId>
        </dependency>

        <dependency>
            <groupId>com.quanzhi.auditapiv2</groupId>
            <artifactId>auditapiv2-biz-risk</artifactId>
        </dependency>

        <dependency>
            <groupId>com.quanzhi.auditapiv2</groupId>
            <artifactId>auditapiv2-common-risk</artifactId>
        </dependency>

        <dependency>
            <groupId>com.quanzhi.auditapiv2</groupId>
            <artifactId>auditapiv2-core-risk</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-cache</artifactId>
        </dependency>
        <dependency>
            <groupId>com.github.ben-manes.caffeine</groupId>
            <artifactId>caffeine</artifactId>
        </dependency>
        <dependency>
            <groupId>com.quanzhi.auditapiv2</groupId>
            <artifactId>auditapiv2-open-api</artifactId>
        </dependency>
        <dependency>
            <groupId>junit</groupId>
            <artifactId>junit</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>com.quanzhi.audit.mix</groupId>
            <artifactId>audit-mix-log</artifactId>
        </dependency>
        <dependency>
            <groupId>com.quanzhi.audit.mix</groupId>
            <artifactId>audit-mix-authorization</artifactId>
        </dependency>
        <dependency>
            <groupId>com.quanzhi.auditapiv2</groupId>
            <artifactId>auditapiv2-core-trace</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
        </dependency>

        <dependency>
            <groupId>com.auth0</groupId>
            <artifactId>java-jwt</artifactId>
        </dependency>
        <dependency>
            <groupId>com.fulmicoton</groupId>
            <artifactId>multiregexp</artifactId>
        </dependency>
        <dependency>
            <groupId>dk.brics.automaton</groupId>
            <artifactId>automaton</artifactId>
        </dependency>
        <dependency>
            <groupId>org.json</groupId>
            <artifactId>org.json</artifactId>
        </dependency>
        <dependency>
            <groupId>com.quanzhi</groupId>
            <artifactId>plugin-api</artifactId>
        </dependency>
        <dependency>
            <groupId>org.jeasy</groupId>
            <artifactId>easy-random-core</artifactId>
        </dependency>
        <dependency>
            <groupId>com.quanzhi.audit.mix</groupId>
            <artifactId>audit-hotswap</artifactId>
        </dependency>
        <dependency>
            <groupId>com.quanzhi.re</groupId>
            <artifactId>ruleEngine-repository-mongo</artifactId>
        </dependency>
        <dependency>
            <groupId>com.quanzhi.re</groupId>
            <artifactId>ruleEngine-core</artifactId>
        </dependency>
        <dependency>
            <groupId>com.quanzhi.audit.mix</groupId>
            <artifactId>audit-magic-api</artifactId>
            <version>3.3.0-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>com.quanzhi.audit.mix</groupId>
            <artifactId>audit-permission</artifactId>
        </dependency>
    </dependencies>

    <build>
        <finalName>audit-apiv2</finalName>

        <resources>
            <resource>
                <directory>src/main/resources</directory>
                <filtering>true</filtering>
                <includes>
                    <include>application.properties</include>
                    <include>documents/*</include>
                    <include>riskExamples/*</include>
                    <include>application-${env}.properties</include>
                    <include>logback-${env}.xml</include>
                    <include>audit-log*.yml</include>
                    <include>uploadtemplate/*</include>
                    <include>mongo/**/**/**.json</include>
                    <include>node/*</include>
                    <include>trace/*</include>
                    <include>scripts/*</include>
                    <include>static/*</include>
                    <include>customize/*</include>
                    <include>open-api/**.json</include>
                    <include>weakness-rule/*</include>
                </includes>
            </resource>
        </resources>

        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-resources-plugin</artifactId>
                <!--防止文件内容乱码-->
                <configuration>
                    <delimiters>
                        <delimiter>@</delimiter>
                    </delimiters>
                    <useDefaultDelimiters>false</useDefaultDelimiters>
                    <encoding>UTF-8</encoding>
                    <nonFilteredFileExtensions>
                        <nonFilteredFileExtension>xlsx</nonFilteredFileExtension>
                        <nonFilteredFileExtension>xls</nonFilteredFileExtension>
                        <nonFilteredFileExtension>pdf</nonFilteredFileExtension>
                    </nonFilteredFileExtensions>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <configuration>
                    <source>1.8</source>
                    <target>1.8</target>
                    <encoding>UTF-8</encoding>
                </configuration>
            </plugin>
<!--            <plugin>-->
<!--                <groupId>org.springframework.boot</groupId>-->
<!--                <artifactId>spring-boot-maven-plugin</artifactId>-->
<!--                <configuration>-->
<!--                    <mainClass>com.quanzhi.auditapiv2.MainApplication</mainClass>-->
<!--                    <layout>ZIP</layout>-->
<!--                    &lt;!&ndash;构建完整可执行程序，可以直接运行&ndash;&gt;-->
<!--                    <executable>true</executable>-->
<!--                    <skip>true</skip>-->
<!--                </configuration>-->
<!--            </plugin>-->
        </plugins>
    </build>
    <profiles>
        <profile>
            <!-- 本地开发环境 -->
            <id>local</id>
            <properties>
                <env>local</env>
            </properties>
        </profile>
        <profile>
            <id>dev</id>
            <properties>
                <env>dev</env>
            </properties>
            <activation>
                <!-- 设置默认激活这个配置 -->
                <activeByDefault>true</activeByDefault>
            </activation>
            <build>
                <plugins>
                    <plugin>
                        <groupId>org.springframework.boot</groupId>
                        <artifactId>spring-boot-maven-plugin</artifactId>
                        <version>${spring.boot.version}</version>
                        <configuration>
                            <mainClass>com.quanzhi.auditapiv2.MainApplication</mainClass>
                        </configuration>
                        <executions>
                            <execution>
                                <goals>
                                    <goal>repackage</goal>
                                </goals>
                            </execution>
                        </executions>
                    </plugin>
                </plugins>
            </build>
        </profile>
        <profile>
            <id>prod</id>
            <properties>
                <env>prod</env>
            </properties>
            <build>
                <plugins>
                    <!--<plugin>
                        <groupId>org.springframework.boot</groupId>
                        <artifactId>spring-boot-maven-plugin</artifactId>
                        <version>${spring.boot.version}</version>
                        <configuration>
                            <includes>
                                &lt;!&ndash; 这里只包含一个不存在的项nothing,即代表什么都不包含，当然名字可以随便写 &ndash;&gt;
                                <include>
                                    <groupId>nothing</groupId>
                                    <artifactId>nothing</artifactId>
                                </include>
                            </includes>
                            <layout>ZIP</layout>
                            <mainClass>com.quanzhi.auditapiv2.MainApplication</mainClass>
                        </configuration>
                        <executions>
                            <execution>
                                <goals>
                                    <goal>repackage</goal>
                                </goals>
                            </execution>
                        </executions>
                    </plugin>-->
                </plugins>
            </build>
        </profile>
        <profile>
            <id>deploy</id>
            <properties>
                <env>prod</env>
            </properties>
        </profile>
    </profiles>


</project>
