apiVersion: apps/v1
kind: Deployment
metadata:
  name: {{ .Values.cfg.name.deploy }}
  {{- if .Values.cfg.enabled.namespace }}
  {{- include "NameSpace" . | nindent 2 }}
  {{- end }}
  labels:
    app: {{ .Values.cfg.labels.deploy.app }}
spec:
  replicas: {{ .Values.replicaCount }}
  selector:
    matchLabels:
      {{- include "PodAppLabel" . | nindent 6 }}
  template:
    metadata:
      labels:
        {{- include "PodLabels" . | nindent 8 }}
    spec:
    {{- with .Values.imagePullSecrets }}
      imagePullSecrets:
        {{- toYaml . | nindent 8 }}
    {{- end }}
      {{- if .Values.nodeSelector }}
      nodeSelector:
        {{- include "NodeLabels" . | nindent 8 }}
      {{- end }}
    {{- with .Values.affinity }}
      affinity:
        {{- toYaml . | nindent 8 }}
    {{- end }}
    {{- with .Values.tolerations }}
      tolerations:
        {{- toYaml . | nindent 8 }}
    {{- end }}
    {{- with .Values.podSecurityContext }}
      securityContext:
        {{- toYaml . | nindent 8 }}
    {{- end }}
      initContainers:
        - name: init-dir-permission
        {{- with .Values.securityContext }}
          securityContext:
            {{- toYaml . | nindent 12 }}
        {{- end }}
          image: {{ include "MainClusterImagePath" . | quote }} # {{ .Chart.Version }}
          imagePullPolicy: {{ .Values.image.pullPolicy }}
          env:
            {{- include "Note" "KUBE_SA_NAME" -}} {{- include "KubeEnvSa" . | nindent 12 }}
            {{- include "Note" "KUBE_HOST_IP" -}} {{- include "KubeEnvHostIp" . | nindent 12 }}
            {{- include "Note" "KUBE_NODE_NAME" -}} {{- include "KubeEnvNode" . | nindent 12 }}
            {{- include "Note" "KUBE_POD_UID" -}} {{- include "KubeEnvPodUid" . | nindent 12 }}
            {{- include "Note" "KUBE_POD_IP" -}} {{- include "KubeEnvPodIp" . | nindent 12 }}
            {{- include "Note" "KUBE_POD_NAME" -}} {{- include "KubeEnvPod" . | nindent 12 }}
            {{- include "Note" "KUBE_NS_NAME" -}} {{- include "KubeEnvNs" . | nindent 12 }}
            - name: KUBE_POD_GROUP_NAME
              value: {{ .Values.cfg.pod_group_name }}
          command:
            - bash
          args:
            - -c
            - "mkdir -p globals/data/fe-be/plugins \"$KUBE_NS_NAME/logs/$KUBE_POD_GROUP_NAME/$KUBE_POD_NAME/\" \"$KUBE_NS_NAME/data/$KUBE_POD_GROUP_NAME/d/\" \"$KUBE_NS_NAME/data/$KUBE_POD_GROUP_NAME/$KUBE_POD_NAME/\"; "
          workingDir: {{ .Values.cfg.opt_base_dir }}dd
          volumeMounts:
            - name: {{ include "MountDnsDomain" . }}
              mountPath: {{ .Values.cfg.opt_base_dir }}dd
          

        - name: waiting-metabase
        {{- with .Values.securityContext }}
          securityContext:
            {{- toYaml . | nindent 12 }}
        {{- end }}
          image: {{ include "MainClusterImagePath" . | quote }} # {{ .Chart.Version }}
          imagePullPolicy: {{ .Values.image.pullPolicy }}
          command:
            - bash
          args:
            - -c
            - "until curl --connect-timeout 5 metabase-server:8082; do echo waiting for metabase-server; sleep 2; done;"
        
        - name: demo-war
        {{- with .Values.securityContext }}
          securityContext:
            {{- toYaml . | nindent 12 }}
        {{- end }}
          command:
            - /bin/sh
          args:
            - -c
            - "cp -rf /webapp /data && cp -rf /home/<USER>/lib /data && cp -rf /home/<USER>/resource"
          image: {{ include "ClusterImagePath" (dict "cfg" .Values.cfg "r" .Values.image.war) | quote }}
          imagePullPolicy: {{ .Values.image.pullPolicy }}
          volumeMounts:
            - name: war-file
              mountPath: /data
            - name: mongo-file
              mountPath: /resource
            - name: {{ include "MountDnsDomain" . }}
              mountPath: /data1
              subPath: globals/data/fe-be

        - name: plugin-core
        {{- with .Values.securityContext }}
          securityContext:
            {{- toYaml . | nindent 12 }}
        {{- end }}
          command:
            - /bin/sh
          args:
            - -c
            - "cp -rf /plugin-core/plugin /data1"
          image: {{ include "ClusterImagePath" (dict "cfg" .Values.cfg "r" .Values.image.plugin_core) | quote }}
          imagePullPolicy: {{ .Values.image.pullPolicy }}
          volumeMounts:
            - name: {{ include "MountDnsDomain" . }}
              mountPath: /data1
              subPath: globals/data/fe-be

        - name: catalina-properties
        {{- with .Values.securityContext }}
          securityContext:
            {{- toYaml . | nindent 12 }}
        {{- end }}
          command:
            - /bin/sh
          args:
            - -c
            - "cp -rf /usr/local/tomcat/conf/ /data && sed -i -e 's,^shared.loader=.*$,shared.loader=/home/<USER>/lib/*.jar,g' /data/conf/catalina.properties"
          image: {{ include "MainClusterImagePath" . | quote }} # {{ .Chart.Version }}
          imagePullPolicy: {{ .Values.image.pullPolicy }}
          volumeMounts:
            - name: catalina-properties-file
              mountPath: /data
              
        - name: mongo
        {{- with .Values.securityContext }}
          securityContext:
            {{- toYaml . | nindent 12 }}
        {{- end }}
          command:
            - /bin/sh
          args:
            - -c
            - "mongo -u $MONGO_INITDB_ROOT_USERNAME -p $MONGO_INITDB_ROOT_PASSWORD mongo-server:27017/$MONGO_INITDB_DATABASE /resource/mongo/mongo_init.js && mongo -u $MONGO_INITDB_ROOT_USERNAME -p $MONGO_INITDB_ROOT_PASSWORD mongo-server:27017/$MONGO_INITDB_DATABASE /resource/mongo/commonActionInit.js  && mongo -u $MONGO_INITDB_ROOT_USERNAME -p $MONGO_INITDB_ROOT_PASSWORD mongo-server:27017/$MONGO_INITDB_DATABASE /resource/mongo/custom_report.js && mongo -u $MONGO_INITDB_ROOT_USERNAME -p $MONGO_INITDB_ROOT_PASSWORD mongo-server:27017/$MONGO_INITDB_DATABASE /resource/mongo/menuConfig_init.js"
          env:
            - name: MONGO_INITDB_DATABASE # mongo_initdb_database
              # value: db
              valueFrom:
                configMapKeyRef:
                  name: {{ .Values.cfg.name.cm | quote }}
                  key: "mongo_database"
            - name: MONGO_INITDB_ROOT_USERNAME # mongo_initdb_root_username
              # value: root
              valueFrom:
                configMapKeyRef:
                  name: {{ .Values.cfg.name.cm | quote }}
                  key: "mongo_username"
            - name: MONGO_INITDB_ROOT_PASSWORD # mongo_initdb_root_password
              # value: 123@Audit
              valueFrom:
                secretKeyRef:
                  name: {{ .Values.cfg.name.sn | quote }}
                  key: "mongo_password"
          image: {{ include "ClusterImagePath" (dict "cfg" .Values.cfg "r" .Values.image.mongo) | quote }} # {{ .Chart.Version }}
          imagePullPolicy: {{ .Values.image.pullPolicy }}
          volumeMounts:
            - name: mongo-file
              mountPath: /resource

      containers:
        - name: {{ .Values.cfg.name.pod }} # {{ .Chart.Name }}
        {{- with .Values.securityContext }}
          securityContext:
            {{- toYaml . | nindent 12 }}
        {{- end }}
          #command: []
          env:
            {{- include "Note" "KUBE_SA_NAME" -}} {{- include "KubeEnvSa" . | nindent 12 }}
            {{- include "Note" "KUBE_HOST_IP" -}} {{- include "KubeEnvHostIp" . | nindent 12 }}
            {{- include "Note" "KUBE_NODE_NAME" -}} {{- include "KubeEnvNode" . | nindent 12 }}
            {{- include "Note" "KUBE_POD_UID" -}} {{- include "KubeEnvPodUid" . | nindent 12 }}
            {{- include "Note" "KUBE_POD_IP" -}} {{- include "KubeEnvPodIp" . | nindent 12 }}
            {{- include "Note" "KUBE_POD_NAME" -}} {{- include "KubeEnvPod" . | nindent 12 }}
            {{- include "Note" "KUBE_NS_NAME" -}} {{- include "KubeEnvNs" . | nindent 12 }}
            - name: CATALINA_BASE
              value: /usr/local/tomcat
            - name: CATALINA_HOME
              value: /usr/local/tomcat
            - name: CATALINA_TMPDIR
              value: /usr/local/tomcat/temp
            - name: CATALINA_OPTS
              value: "-Duser.home=/tmp"
            - name: JAVA_OPTS
              value: "-Duser.home=/tmp -XX:+UseConcMarkSweepGC -Xms{{ .Values.cfg.audit_api_b.jvm.xms }} -Xmx{{ .Values.cfg.audit_api_b.jvm.xmx }} -XX:+ExitOnOutOfMemoryError"
            - name: JRE_HOME
              value: /usr/local/openjdk-8
            - name: CLASSPATH
              value: /usr/local/tomcat/bin/bootstrap.jar:/usr/local/tomcat/bin/tomcat-juli.jar
            - name: HOME
              value: "/audit-api"
            - name: TZ
              value: Asia/Shanghai
                  
                  
          image: {{ include "MainClusterImagePath" . | quote }} # {{ .Chart.Version }}
          #image: {{ include "MainPublicImagePath" . | quote }} # {{ .Chart.Version }}
          imagePullPolicy: {{ .Values.image.pullPolicy }}
          ports:
            - name: http
              containerPort: 8080
              protocol: TCP

          livenessProbe:
            httpGet:
              path: /audit-apiv2/api/k8sLivenessProbe/available.do
              port: 8080
              httpHeaders:
                - name: token
                  value: OPEN_API
            initialDelaySeconds: 600
            periodSeconds: 180
            timeoutSeconds: 60
            failureThreshold: 1
          # livenessProbe:
          #   httpGet:
          #     path: /
          #     port: http
          # readinessProbe:
          #   httpGet:
          #     path: /
          #     port: http
          resources:
            {{- toYaml .Values.resources | nindent 12 }}
          volumeMounts:
            - name: catalina-properties-file
              mountPath: /usr/local/tomcat/conf
              subPath: conf
              readOnly: true
            - name: war-file
              mountPath: /usr/local/tomcat/webapps
              subPath: webapp
            - name: war-file
              mountPath: /home/<USER>/lib
              subPath: lib
              readOnly: true
            - name: jar-file
              mountPath: /jar-libs
              readOnly: true
            - name: {{ include "MountDnsDomain" . }}
              mountPath: /home/<USER>/audit-api
              subPath: globals/data/fe-be
            - name: {{ include "MountDnsDomain" . }}
              mountPath: /home/<USER>/audit-api/ipdb
              subPath: globals/data/fe-be/ipdb
            - name: {{ include "MountDnsDomain" . }}
              mountPath: /home/<USER>/audit-api/plugins
              subPath: globals/data/fe-be/plugins
              readOnly: false
            - name: {{ include "MountDnsDomain" . }}
              mountPath: /opt/logs/tmp
              subPath: globals/data/fe-be
              readOnly: false
            - name: {{ include "MountDnsDomain" . }}
              mountPath: /opt/logs
              subPathExpr: $(KUBE_NS_NAME)/logs/{{ .Values.cfg.pod_group_name }}/$(KUBE_POD_NAME)
            - name: {{ include "MountDnsDomain" . }}
              mountPath: /home/<USER>/qz-audit/update
              subPath: globals/update
            - name: {{ include "MountDnsDomain" . }}
              mountPath: /home/<USER>/qz-audit/config
              subPath: globals/conf/version_info
            - name: audit-api
              mountPath: /audit-api
            - name: {{ include "MountDnsDomain" . }}
              mountPath: /home/<USER>/risk-eval-core
              subPath: globals/data/risk-eval-tools-fe-be
            - name: pcaphistory
              mountPath: /home/<USER>/risk-eval-core/pcap/history
            - name: tomcat-conf-context
              mountPath: /usr/local/tomcat/conf/context.xml
              subPath: tomcat.context.xml
              readOnly: true
            - name: tomcat-conf-server
              mountPath : /usr/local/tomcat/conf/server.xml
              subPath: tomcat.server.xml
              readOnly: true
            - name: root
              mountPath: /host
              readOnly: true

            
      volumes:
        {{- include "VolumeDd" . | nindent 8 }}
        # - name: dd-ns-progs
        #   emptyDir:
        #     medium: Memory
        - name: catalina-properties-file
          emptyDir:
            medium: Memory
            sizeLimit: 10Mi
        - name: war-file
          emptyDir:
            medium: Memory
            sizeLimit: 500Mi
        - name: jar-file
          emptyDir:
            medium: Memory
            sizeLimit: 500Mi
        - name: audit-api
          emptyDir:
            sizeLimit: 500Mi
        - name: mongo-file
          emptyDir:
            sizeLimit: 10Mi
        - name: pcaphistory
          hostPath:
            path: /home/<USER>/risk-eval-core/pcap/history
        - name : tomcat-conf-context
          configMap:
            name: {{ .Values.cfg.name.cm }}
        - name : tomcat-conf-server
          configMap:
            name: {{ .Values.cfg.name.cm }}
        - name: root
          hostPath:
            path: /
