apiVersion: v1
kind: Service
metadata:
  name: {{ .Values.cfg.name.svc }}
  {{- if .Values.cfg.enabled.namespace }}
  {{- include "NameSpace" . | nindent 2 }}
  {{- end }}
  labels:
    app: {{ .Values.cfg.labels.svc.app }}
spec:
  type: {{ .Values.service.type }}
  {{- if .Values.cfg.svc_ip }}
  clusterIP: {{ .Values.cfg.svc_ip }}
  {{- end }}
  ports:
    - port: {{ .Values.service.port }}
      targetPort: http
      protocol: TCP
      name: http
      {{- with .Values.service.nodePort }}
      nodePort: {{ . }}
      {{- end }}
  {{- with .Values.service.externalIPs }}
  externalIPs:
    {{- toYaml . | nindent 4 }}
  {{- end }}
  selector:
    {{- include "PodAppLabel" . | nindent 4 }}