{{/*
Version: 1.0.8
*/}}

{{- if .Values.ingress.enabled -}}
{{- $svcName := (include "InstanceName" (dict "cfg" .Values.cfg "nm" .Values.cfg.name.svc)) -}}
{{- $svcPort := .Values.service.port -}}
{{- include "Assert" (dict "e" (hasKey .Values.cfg.name "ing") "s" "Not found cfg.name.ing") -}}
{{- if semverCompare ">=1.14-0" .Capabilities.KubeVersion.GitVersion -}}
apiVersion: networking.k8s.io/v1beta1
{{- else -}}
apiVersion: extensions/v1beta1
{{- end }}
kind: Ingress
metadata:
{{- if .Values.ingress.debug }}
  name: {{ printf "debug-%s" (include "InstanceName" (dict "cfg" .Values.cfg "nm" .Values.cfg.name.ing)) }}
{{- else if .Values.ingress.mon }}
  name: {{ printf "mon-%s" (include "InstanceName" (dict "cfg" .Values.cfg "nm" .Values.cfg.name.ing)) }}
{{- else }}
  name: {{ include "InstanceName" (dict "cfg" .Values.cfg "nm" .Values.cfg.name.ing) }}
{{- end }}
{{- if (or .Values.ingress.ngnix_annotations .Values.ingress.auth .Values.ingress.access_list .Values.ingress.annotations .Values.ingress.ingress_class .Values.ingress.force_ssl_redirect .Values.ingress.ssl_redirect .Values.ingress.client_max_body_size .Values.ingress.ip_white_list .Values.ingress.proxy_connect_timeout .Values.ingress.proxy_send_timeout .Values.ingress.proxy_read_timeout) }}
  annotations:
  {{- with .Values.ingress.annotations }}
    {{- toYaml . | nindent 4 }}
  {{- end }}
  {{- if (or .Values.ingress.access_list .Values.ingress.ip_white_list) }}
    nginx.ingress.kubernetes.io/server-snippet: |
    {{- range .Values.ingress.access_list }}
      {{- . | nindent 6 }}
    {{- end }}
    {{- if .Values.ingress.ip_white_list }}
      {{- range .Values.ingress.ip_white_list }}
        {{- if . }}
        {{- printf "allow %s;" . | nindent 6 }}
        {{- end }}
      {{- end }}
      deny all;
    {{- end }}
  {{- end }}
  {{- if .Values.ingress.ingress_class }}
    kubernetes.io/ingress.class: {{ .Values.ingress.ingress_class }}
  {{- end }}
  {{- if .Values.ingress.client_max_body_size }}
    nginx.ingress.kubernetes.io/proxy-body-size: {{ .Values.ingress.client_max_body_size }} # client_max_body_size
  {{- end }}
  {{- if .Values.ingress.force_ssl_redirect }}
    nginx.ingress.kubernetes.io/force-ssl-redirect: {{ .Values.ingress.force_ssl_redirect | quote }}
  {{- end }}
  {{- if .Values.ingress.ssl_redirect }}
    nginx.ingress.kubernetes.io/ssl-redirect: {{ .Values.ingress.ssl_redirect | quote }}
  {{- end }}
  {{- if .Values.ingress.auth }}
    {{- if .Values.ingress.auth_realm }}
    nginx.ingress.kubernetes.io/auth-realm: {{ .Values.ingress.auth_realm | quote }}
    {{- end }}
    {{- if .Values.ingress.auth_type }}
    nginx.ingress.kubernetes.io/auth-type: {{ .Values.ingress.auth_type | quote }}
    {{- end }}
    {{- if .Values.ingress.auth_secret }}
    nginx.ingress.kubernetes.io/auth-secret: {{ .Values.ingress.auth_secret | quote }}
    {{- else if .Values.cfg.name.ing_sn_auth }}
    nginx.ingress.kubernetes.io/auth-secret: {{ .Values.cfg.name.ing_sn_auth | quote }}
    {{- else }}
      {{- include "Assert" (dict "e" false "s" "Not found cfg.name.ing_sn_auth, ingress.auth_secret ") -}}
    {{- end }}
    {{- if .Values.ingress.auth_secret_type }}
    nginx.ingress.kubernetes.io/auth-secret-type: {{ .Values.ingress.auth_secret_type | quote }}
    {{- end }}
  {{- end }}
  {{- if .Values.ingress.proxy_connect_timeout }}
    nginx.ingress.kubernetes.io/proxy-connect-timeout: {{ .Values.ingress.proxy_connect_timeout | quote }}
  {{- end }}
  {{- if .Values.ingress.proxy_send_timeout }}
    nginx.ingress.kubernetes.io/proxy-send-timeout: {{ .Values.ingress.proxy_send_timeout | quote }}
  {{- end }}
  {{- if .Values.ingress.proxy_read_timeout }}
    nginx.ingress.kubernetes.io/proxy-read-timeout: {{ .Values.ingress.proxy_read_timeout | quote }}
  {{- end }}
  {{- with .Values.ingress.ngnix_annotations }}
    {{- $k_ := keys . | first  -}}
    {{- $_ := set . (printf "nginx.ingress.kubernetes.io/%s" $k_) (get . $k_) -}}
    {{- $_ := unset . $k_ -}}
    {{- toYaml . | nindent 4 }}
  {{- end }}
{{- end }}
spec:
{{- if .Values.ingress.tls }}
  tls:
  {{- range .Values.ingress.tls }}
    - hosts:
      {{- range .hosts }}
        - {{ . | quote }}
      {{- end }}
      secretName: {{ .secretName }}
  {{- end }}
{{- end }}
  rules:
  {{- range .Values.ingress.hosts }}
    - http:
        paths:
        {{- range .paths }}
          - path: {{ . }}
            backend:
              serviceName: {{ $svcName }}
              servicePort: {{ $svcPort }}
        {{- end }}
      {{- if (.host | quote) }}
      host: {{ .host | quote }}
      {{- end }}
  {{- end }}
{{- end }}