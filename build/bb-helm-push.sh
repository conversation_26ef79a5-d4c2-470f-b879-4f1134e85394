#!/bin/bash


# 用于在harbor中发布当前组件的chart包

# 当前目录为仓库中的build目录 

# 加载容器环境变量和函数
source bb-env.sh || exit


## 切换到仓库所在的根目录
#pushd .. || exit


# 输出yaml文件
echo '################'
bb_helm template audit-api-b
echo '################'

echo '################'
bb_helm template audit-api-b-mini
echo '################'

# 将char包发布到harbor中
bb_helm_push audit-api-b  "$DIND_CHARTREPO_URL"  || exit
bb_helm_push audit-api-b-mini  "$DIND_CHARTREPO_URL"  || exit


# 在构建日志显示当前推送信息 TODO
#echo ""
