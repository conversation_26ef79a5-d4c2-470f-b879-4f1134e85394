
# docker build -t common:latest -f Dockerfile  target/

#FROM busybox:latest
#ADD *.jar /home/

# 内网公共仓库环境变量
ARG DIND_PUBLIC_REGIATRY_URL
# 内网私有仓库环境变量
ARG DIND_REGIATRY_URL

#版本信息
ARG VERSION

# git环境
ARG GIT_REPOSITORY
ARG GIT_BRANCH
ARG GIT_TAG
ARG GIT_REVISION
ARG GIT_COMMIT_HASH
ARG GIT_COMMITTER_NAME
ARG GIT_COMMITTER_EMAIL
ARG GIT_COMMITTER_DATE

FROM ${DIND_PUBLIC_REGIATRY_URL}jre:8u382-b05-python3

# 在构建镜像过程中使用参数变量
ARG DIND_PUBLIC_REGIATRY_URL

  #版本信息
ARG VERSION

# git环境
ARG GIT_REPOSITORY
ARG GIT_BRANCH
ARG GIT_TAG
ARG GIT_REVISION
ARG GIT_COMMIT_HASH
ARG GIT_COMMITTER_NAME
ARG GIT_COMMITTER_EMAIL
ARG GIT_COMMITTER_DATE

ADD target/audit-apiv2.jar /home
ADD target/lib /home/<USER>/lib
ADD src/main/resources/mongo /home/<USER>

ADD src/main/resources/tool /opt/dsd/qzprod/globals/data/fe-be/tool/

RUN useradd qzuser &&  usermod -u 9292 qzuser && groupadd -g 9292 newgroup && usermod -g 9292 qzuser
RUN mkdir -p /home/<USER>/qz-audit/update \
&& mkdir -p /home/<USER>/qz-audit/config \
&& mkdir -p /home/<USER>/risk-eval-core \
&& mkdir -p /home/<USER>/audit-api


CMD ["java", "-DJM.LOG.PATH=/tmp", "-DJM.SNAPSHOT.PATH=/tmp","-Dfastjson.parser.safeMode=true", "-Duser.home=/tmp", "-Dloader.path=/home/<USER>/lib/","-jar", "-Dspring.profiles.active=prod", "-Duser.home=/home","/home/<USER>"]
# 基本信息
LABEL name="audit-api-b" description="审计后端"  version="$VERSION"

# 代码信息
LABEL git.revision="$GIT_REVISION" git.commit_hash="$GIT_COMMIT_HASH" git.committer_name="$GIT_COMMITTER_NAME" git.committer_email="$GIT_COMMITTER_EMAIL" git.committer_date="$GIT_COMMITTER_DATE" git.repository="$GIT_REPOSITORY" git.branch="$GIT_BRANCH" git.tag="$GIT_TAG"



