
# docker build -t common:latest -f Dockerfile  target/

#FROM busybox:latest
#ADD *.jar /home/

# 内网公共仓库环境变量
ARG DIND_PUBLIC_REGIATRY_URL
# 内网私有仓库环境变量
ARG DIND_REGIATRY_URL

#版本信息
ARG VERSION

# git环境
ARG GIT_REPOSITORY
ARG GIT_BRANCH
ARG GIT_TAG
ARG GIT_REVISION
ARG GIT_COMMIT_HASH
ARG GIT_COMMITTER_NAME
ARG GIT_COMMITTER_EMAIL
ARG GIT_COMMITTER_DATE

FROM ${DIND_PUBLIC_REGIATRY_URL}jre:8u382-b05-python3

# 在构建镜像过程中使用参数变量
ARG DIND_PUBLIC_REGIATRY_URL

  #版本信息
ARG VERSION

# git环境
ARG GIT_REPOSITORY
ARG GIT_BRANCH
ARG GIT_TAG
ARG GIT_REVISION
ARG GIT_COMMIT_HASH
ARG GIT_COMMITTER_NAME
ARG GIT_COMMITTER_EMAIL
ARG GIT_COMMITTER_DATE

ADD apps/app-start/target/start_enc.jar /home/<USER>/start.jar
ADD apps/app-dfhk/target/dfhk_enc.jar /home/<USER>/dfhk.jar
ADD apps/app-tyy/target/tyy_enc.jar /home/<USER>/tyy.jar
ADD apps/app-hnlt/target/hnlt_enc.jar /home/<USER>/hnlt.jar
ADD apps/app-zryph/target/zryph_enc.jar /home/<USER>/zryph.jar
ADD apps/*/target/libs/ /home/<USER>/lib/
#ADD web/target/lib /home/<USER>/lib
ADD web/src/main/resources/mongo /home/<USER>
ADD config/build.json /home/<USER>/
ADD config/start.sh /home/<USER>/

ADD web/src/main/resources/report /home/<USER>
ADD web/src/main/resources/template-bak /home/<USER>
ADD web/src/main/resources/offline-template /home/<USER>
ADD web/src/main/resources/customize /home/<USER>
ADD web/src/main/resources/tool /home/<USER>/tool
ADD web/src/main/resources/open-api/API开放接口-v3.3.0-高级版.pdf /home/<USER>/API开放接口-v3.3.0-高级版.pdf
ADD web/src/main/resources/open-api/API开放接口-v3.3.0-基础版.pdf /home/<USER>/API开放接口-v3.3.0-基础版.pdf
ADD web/src/main/resources/open-api/API开放接口-v3.3.0-旗舰版.pdf /home/<USER>/API开放接口-v3.3.0-旗舰版.pdf
ADD web/src/main/resources/open-api/qz-sdk.jar /home/<USER>/qz-sdk.jar
ADD web/src/main/resources/report/账号行为风险治理报告.zip /home/<USER>/账号行为风险治理报告.zip
ADD web/src/main/resources/report/运营分析报告.zip /home/<USER>/运营分析报告.zip
ADD web/src/main/resources/report/数据出境报告.zip /home/<USER>/数据出境报告.zip
ADD web/src/main/resources/scripts/change-ck.sh /home/<USER>
ADD web/src/main/resources/scripts/platform-con.sh /home/<USER>
ADD web/src/main/resources/scripts/editnginx.sh /home/<USER>
ADD common/util/src/main/resources/apiWeakness_template.docx /home/<USER>/audit-api/export/template/apiWeakness_template.docx

# 这一步会把镜像变大 483M
# RUN chown -R 9292:9292 /home/<USER>

WORKDIR /home/<USER>

RUN mkdir -p /home/<USER>/qz-audit/update \
&& mkdir -p /home/<USER>/qz-audit/config \
&& mkdir -p /home/<USER>/risk-eval-core && mkdir -p /.python_home/cachedir/packages
#CMD ["java", "-DJM.LOG.PATH=/tmp", "-DJM.SNAPSHOT.PATH=/tmp", "-Duser.home=/tmp", "-Dloader.path=/home/<USER>/lib/","-jar", "-Dspring.profiles.active=prod", "-Duser.home=/home","/home/<USER>"]
RUN chmod +x /home/<USER>/start.sh

ENTRYPOINT ["/home/<USER>/start.sh"]
# 基本信息
LABEL name="audit-api-b-mini" description="审计后端"  version="$VERSION"

# 代码信息
LABEL git.revision="$GIT_REVISION" git.commit_hash="$GIT_COMMIT_HASH" git.committer_name="$GIT_COMMITTER_NAME" git.committer_email="$GIT_COMMITTER_EMAIL" git.committer_date="$GIT_COMMITTER_DATE" git.repository="$GIT_REPOSITORY" git.branch="$GIT_BRANCH" git.tag="$GIT_TAG"