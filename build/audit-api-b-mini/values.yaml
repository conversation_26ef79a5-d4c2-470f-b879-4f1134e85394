# Default values for audit-api-b.
# This is a YAML-formatted file.
# Declare variables to be passed into your templates.


cfg:
  #svc_ip: ***********  # backend-service.app-audit.svc.qzprod
  cluster_regi_url: registry.common.svc.qzprod:8092/
  public_regi_url: registry-ops.qzkeji.cn/library/
  private_regi_url: registry-ops.qzkeji.cn/qzkj-ops/   # registry-ops / registry-rel
  pod_group_name: audit-api-b-apps
  opt_base_dir: /opt/dsd/
  host_opt_base_dir: /opt/dsd/
  namespace: app-audit
  dns_domain: qzprod
  mount_dns_domain: dns-domain
  labels:
    pod:
      app: audit-api-b
      rel: beta
    svc:
      app: audit-api-b
    deploy:
      app: audit-api-b
  name:
    svc: backend-service
    deploy: audit-api-b-app
    pod: audit-api-b
    python: audit-api-b-python
    cm: audit-api-b-conf-d
    sn: qz-secret
    ing: audit-api-b-server
  label_prefix: qz/
  label_suffix: /qz
  node_label_prefix: qz/
  node_label_suffix: /qz
  enabled:
    namespace: false
    label_prefix: false
    label_suffix: false
    node_label_prefix: false
    node_label_suffix: false
    waiting_metabase: false
  audit_api_b:
    jvm:
      xms: 1024m
      xmx: 3072m


replicaCount: 1

image:
  repository: audit-api-b-mini
  version: 3.3.0
  pullPolicy: IfNotPresent
  #pullPolicy: Always
#  war:
#    repository: audit-api-b-mini
#    version: 3.0.0
  mongo:
    repository: mongo
    version: 4.2.24
#  library:
#    repository: audit-api-b-lib
#    version: 3.1.1
  plugin_core:
    repository: plugin-core-api-advance
    version: api-3.3

imagePullSecrets: []
nameOverride: ""
fullnameOverride: ""

serviceAccount:
  # Specifies whether a service account should be created
  create: true
  # Annotations to add to the service account
  annotations: {}
  # The name of the service account to use.
  # If not set and create is true, a name is generated using the fullname template
  name:

podSecurityContext: {}
  # fsGroup: 2000

securityContext:
  # capabilities:
  #   drop:
  #   - ALL
  # readOnlyRootFilesystem: true
  runAsNonRoot: true
  runAsUser: 9292
  runAsGroup: 9292
  # allowPrivilegeEscalation: false
  # privileged: true

service:
  type: ClusterIP
  port: 8080

ingress:
  enabled: true
  debug: false
  client_max_body_size: 10M
  ingress_class: nginx-ingress-public
  annotations:
    nginx.ingress.kubernetes.io/client-body-buffer-size: 1M
      # nginx.ingress.kubernetes.io/proxy-body-size: 500M # client_max_body_size
      # kubernetes.io/ingress.class: nginx-ingress-public
    # kubernetes.io/tls-acme: "true"
  hosts:
    - paths:
        - /audit-apiv2/agent/save.do
  tls: []
  #  - secretName: chart-example-tls
  #    hosts:
  #      - chart-example.local

resources: {}
  # We usually recommend not to specify default resources and to leave this as a conscious
  # choice for the user. This also increases chances charts run on environments with little
  # resources, such as Minikube. If you do want to specify resources, uncomment the following
  # lines, adjust them as necessary, and remove the curly braces after 'resources:'.
  # limits:
  #   cpu: 100m
  #   memory: 128Mi
  # requests:
  #   cpu: 100m
  #   memory: 128Mi

nodeSelector:
  host-name: master
  k8s-name: master

tolerations: []

affinity: {}

runtime: ""

PROJECT_NAME: "default"
