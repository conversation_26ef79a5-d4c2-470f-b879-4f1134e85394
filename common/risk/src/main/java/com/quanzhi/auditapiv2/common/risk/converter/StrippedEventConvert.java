package com.quanzhi.auditapiv2.common.risk.converter;

import com.quanzhi.auditapiv2.common.util.utils.DataUtil;
import com.quanzhi.auditapiv2.core.risk.entity.RiskIpAccountInfo;
import com.quanzhi.auditapiv2.core.risk.entity.StrippedEvent;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;
import org.mapstruct.factory.Mappers;

@Mapper
public interface StrippedEventConvert {


    StrippedEventConvert instance = Mappers.getMapper(StrippedEventConvert.class);

    @Mappings({
            @Mapping(target ="id",ignore = true),
            @Mapping(target = "eventId",source ="id" ),
            @Mapping(target = "loginResult",expression = "java(convertLoginResult(strippedEvent))")
    })
    RiskIpAccountInfo convert(StrippedEvent strippedEvent);

    default String convertLoginResult(StrippedEvent strippedEvent){
        return DataUtil.isEmpty(strippedEvent.getLoginResult())?"UNKNOWN":strippedEvent.getLoginResult();
    }

}
