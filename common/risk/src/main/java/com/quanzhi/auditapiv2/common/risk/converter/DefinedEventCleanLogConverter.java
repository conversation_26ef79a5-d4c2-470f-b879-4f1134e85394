package com.quanzhi.auditapiv2.common.risk.converter;

import com.quanzhi.auditapiv2.common.risk.po.DefinedEventCleanLogPO;
import com.quanzhi.auditapiv2.core.risk.entity.DefinedEventCleanLog;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * @Author: Hao<PERSON>un
 * @Date: 2021/8/13 4:55 下午
 */
@Mapper
public interface DefinedEventCleanLogConverter {

    DefinedEventCleanLogConverter INSTANCE = Mappers.getMapper(DefinedEventCleanLogConverter.class);

    DefinedEventCleanLogPO convert(DefinedEventCleanLog definedEventCleanLog);

    DefinedEventCleanLog convert(DefinedEventCleanLogPO definedEventCleanLog);

}
