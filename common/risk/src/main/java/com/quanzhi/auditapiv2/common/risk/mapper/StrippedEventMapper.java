package com.quanzhi.auditapiv2.common.risk.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.quanzhi.auditapiv2.common.risk.po.StrippedEventPO;
import com.quanzhi.auditapiv2.core.risk.entity.StrippedEvent;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * <AUTHOR>
 * create at 2022/8/30 11:36 上午
 * @description:
 **/
@Mapper
public interface StrippedEventMapper extends BaseMapper<StrippedEventPO> {


    @Select({"SELECT ${field} ",
            "FROM audit.http_stripped_event ",
            "WHERE ${sql} "})
    List<StrippedEvent> find(@Param("sql")String sql,@Param("field") String field);


}