package com.quanzhi.auditapiv2.common.risk.helper;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.metadata.OrderItem;
import com.quanzhi.auditapiv2.core.risk.entity.page.Pageable;
import com.quanzhi.auditapiv2.core.risk.entity.page.Sort;

/**
 * @Author: HaoJun
 * @Date: 2021/1/7 8:28 下午
 */
public class PageHelper {
    public static final <T> IPage<T> convert(Pageable p) {
        return convert(p, null);
    }

    public static final <T> IPage<T> convert(Pageable p, String alis) {
        com.baomidou.mybatisplus.extension.plugins.pagination.Page<T> page
                = new com.baomidou.mybatisplus.extension.plugins.pagination.Page<>(p.getPage(), p.getSize());
        if (p.getSort() != null) {
            for (Sort.Order order : p.getSort().getOrders()) {
                String property = order.getProperty();
                if (alis != null){
                    property = alis + "." + property;
                }
                page.setOrders(order.getDirection() == Sort.Direction.ASC ?
                        OrderItem.ascs(property) :
                        OrderItem.descs(property));
            }
        }
        return page;
    }
}
