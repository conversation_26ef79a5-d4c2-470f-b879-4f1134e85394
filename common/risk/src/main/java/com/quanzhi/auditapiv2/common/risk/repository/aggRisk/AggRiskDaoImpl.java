package com.quanzhi.auditapiv2.common.risk.repository.aggRisk;

import com.mongodb.client.result.UpdateResult;
import com.quanzhi.auditapiv2.common.dal.dao.impl.common.CustomPropertySearchAdapter;
import com.quanzhi.auditapiv2.common.dal.dao.impl.common.Module;
import com.quanzhi.auditapiv2.common.dal.dao.impl.common.QueryAdapterRegistry;
import com.quanzhi.auditapiv2.common.dal.dto.aggRisk.BatchAggRiskOperatorDto;
import com.quanzhi.auditapiv2.common.dal.entity.IpCountEntity;
import com.quanzhi.auditapiv2.core.risk.entity.RiskInfo;
import com.quanzhi.auditapiv2.core.risk.repository.aggRisk.AggRiskDao;
import com.quanzhi.auditapiv2.common.dal.dao.base.impl.BaseDaoImpl;
import com.quanzhi.auditapiv2.common.dal.dto.aggRisk.AggRiskOperatorDto;
import com.quanzhi.auditapiv2.common.dal.entity.aggRisk.AggRiskInfo;
import com.quanzhi.auditapiv2.common.util.dto.AggregationDto;
import com.quanzhi.auditapiv2.common.util.dto.GroupDto;
import com.quanzhi.auditapiv2.common.util.utils.ConstantUtil;
import com.quanzhi.auditapiv2.common.util.utils.DataUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.aggregation.Aggregation;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.stereotype.Repository;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * @Author: yangzx
 * @Date: 2024/10/9 14:38
 */
@Repository
public class AggRiskDaoImpl extends BaseDaoImpl<AggRiskInfo> implements AggRiskDao {

    private final MongoTemplate mongoTemplate;

    private final CustomPropertySearchAdapter customPropertySearchAdapter;

    @Autowired
    public QueryAdapterRegistry queryAdapterRegistry;


    private final String collectionName = "aggRiskInfo";

    public AggRiskDaoImpl(MongoTemplate mongoTemplate, CustomPropertySearchAdapter customPropertySearchAdapter) {
        this.mongoTemplate = mongoTemplate;
        this.customPropertySearchAdapter = customPropertySearchAdapter;
    }

    @Override
    public List<AggRiskInfo> listAggRisk(AggRiskOperatorDto aggRiskOperatorDto) {
        Integer limit = aggRiskOperatorDto.getLimit();
        Integer page = aggRiskOperatorDto.getPage();
        Integer sort = aggRiskOperatorDto.getSort();
        String field = aggRiskOperatorDto.getSortField();
        Criteria criteria = getCriteria(aggRiskOperatorDto);
        // 分页
        Pageable pageable = PageRequest.of(page - 1, limit);
        // 排序
        Sort dbSort = null;
        if (com.quanzhi.audit_core.common.utils.DataUtil.isNotEmpty(field) && com.quanzhi.audit_core.common.utils.DataUtil.isNotEmpty(sort)) {

            if (sort == ConstantUtil.Sort.ASC) {
                dbSort = Sort.by(Sort.Order.desc("riskMark"), Sort.Order.asc(field), Sort.Order.desc("_id"));
            } else if (sort == ConstantUtil.Sort.DESC) {
                dbSort = Sort.by(Sort.Direction.DESC, "riskMark", field, "_id");
            } else {
                dbSort = Sort.by(Sort.Direction.DESC, "riskMark", field, "_id");
            }
        } else {
            dbSort = Sort.by(Sort.Direction.DESC, "riskMark", "_id");
        }
        return mongoTemplate.find(new Query().addCriteria(criteria).with(pageable).with(dbSort), AggRiskInfo.class, collectionName);
    }

    @Override
    public List<AggregationDto> groupAggRisk(
            AggRiskOperatorDto aggRiskOperatorDto,
            GroupDto groupDto
    ) throws Exception {
        return group(getCriteria(aggRiskOperatorDto), groupDto, aggRiskOperatorDto.getSortField(), aggRiskOperatorDto.getSort(), aggRiskOperatorDto.getPage(), aggRiskOperatorDto.getLimit());
    }

    @Override
    public String markAggRisk(AggRiskOperatorDto aggRiskOperatorDto) {
        Update update = new Update();
        update.set("riskMark", aggRiskOperatorDto.getRiskMark());
        UpdateResult result = mongoTemplate.updateFirst(new Query().addCriteria(Criteria.where("_id").is(aggRiskOperatorDto.getId())), update, collectionName);
        if (result.getModifiedCount() != 0) {
            return "success";
        } else if (result.getModifiedCount() == 0) {
            return "success";
        } else {
            return "fail";
        }
    }

    @Override
    public String batchMarkAggRisk(BatchAggRiskOperatorDto batchAggRiskOperatorDto) {
        Update update = new Update();
        update.set("riskMark", batchAggRiskOperatorDto.getRiskMark());
        UpdateResult result = null;
        if (batchAggRiskOperatorDto.getIds() != null) {
            result = mongoTemplate.updateMulti(new Query().addCriteria(Criteria.where("_id").in(batchAggRiskOperatorDto.getIds())), update, collectionName);
        } else {
            AggRiskOperatorDto aggRiskOperatorDto = batchAggRiskOperatorDto.getAggRiskOperatorDto();
            Criteria criteria = getCriteria(aggRiskOperatorDto);
            result = mongoTemplate.updateMulti(new Query().addCriteria(criteria), update, collectionName);
        }
        if (result.getModifiedCount() != 0) {
            return "success";
        } else if (result.getModifiedCount() == 0) {
            return "success";
        } else {
            return "fail";
        }
    }

    @Override
    public Long totalCount(AggRiskOperatorDto aggRiskOperatorDto) {
        return mongoTemplate.count(new Query().addCriteria(getCriteria(aggRiskOperatorDto)), collectionName);
    }

    @Override
    public Criteria getCriteria(AggRiskOperatorDto aggRiskOperatorDto) {
        Criteria criteria = new Criteria();
        List<Criteria> andCriteriaList = new ArrayList<>();
        if (DataUtil.isNotEmpty(aggRiskOperatorDto)) {
            if (DataUtil.isNotEmpty(aggRiskOperatorDto.getHost())) {
                if (DataUtil.isNotEmpty(aggRiskOperatorDto.getHost())) {
                    if (DataUtil.isNotEmpty(aggRiskOperatorDto.getHostIgnoreCase()) && aggRiskOperatorDto.getHostIgnoreCase()) {
                        Pattern pattern = Pattern.compile(DataUtil.regexStrEscape(aggRiskOperatorDto.getHost()), Pattern.CASE_INSENSITIVE);
                        criteria.and("host").regex(pattern);
                    } else {
                        criteria.and("host").regex(DataUtil.regexStrEscape(aggRiskOperatorDto.getHost()));
                    }
                }
            }
            if (DataUtil.isNotEmpty(aggRiskOperatorDto.getAppUris())) {
                criteria.and("appUri").in(aggRiskOperatorDto.getAppUris());
            }
            if (DataUtil.isNotEmpty(aggRiskOperatorDto.getAppUri())) {
                if (aggRiskOperatorDto.getAppUri().startsWith("httpapp")) {
                    criteria.and("appUri").is(aggRiskOperatorDto.getAppUri());
                } else {
                    criteria.and("appUri").regex(aggRiskOperatorDto.getAppUri());
                }
            }
            if (DataUtil.isNotEmpty(aggRiskOperatorDto.getApiUri())) {
                if (aggRiskOperatorDto.getAppUri().startsWith("httpapi")) {
                    criteria.and("apiUri").is(aggRiskOperatorDto.getApiUri());
                } else {
                    criteria.and("apiUri").regex(aggRiskOperatorDto.getApiUri());
                }
            }
            if (DataUtil.isNotEmpty(aggRiskOperatorDto.getAppName())) {
                criteria.and("appName")
                        .regex(com.quanzhi.auditapiv2.common.util.utils.DataUtil.regexStrEscape(aggRiskOperatorDto.getAppName()));
            }
            //3.2新增来源节点筛选
            if (com.quanzhi.audit_core.common.utils.DataUtil.isNotEmpty(aggRiskOperatorDto.getNodeId())) {
                criteria.and("nodes.nid").is(aggRiskOperatorDto.getNodeId());
            }
            if (DataUtil.isNotEmpty(aggRiskOperatorDto.getOperationId())) {
                criteria.and("operationId")
                        .regex(com.quanzhi.auditapiv2.common.util.utils.DataUtil.regexStrEscape(aggRiskOperatorDto.getOperationId()));
            }
            if (DataUtil.isNotEmpty(aggRiskOperatorDto.getName())) {
                criteria.and("name")
                        .in(aggRiskOperatorDto.getName());
            }
            if (DataUtil.isNotEmpty(aggRiskOperatorDto.getType())) {
                criteria.and("type")
                        .in(aggRiskOperatorDto.getType());
            }
            if (DataUtil.isNotEmpty(aggRiskOperatorDto.getLevel())) {
                criteria.and("level")
                        .in(aggRiskOperatorDto.getLevel());
            }
            if (DataUtil.isNotEmpty(aggRiskOperatorDto.getState())) {
                criteria.and("state")
                        .in(aggRiskOperatorDto.getState());
            }
            if (DataUtil.isNotEmpty(aggRiskOperatorDto.getEntityType())) {
                criteria.and("entityType")
                        .is(com.quanzhi.auditapiv2.common.util.utils.DataUtil.regexStrEscape(aggRiskOperatorDto.getEntityType()));
            }
            if (DataUtil.isNotEmpty(aggRiskOperatorDto.getApiOrAppUri())) {
                if (DataUtil.isNotEmpty(aggRiskOperatorDto.getUseRegex()) && aggRiskOperatorDto.getUseRegex()) {
                    criteria.and("apiUri")
                            .regex(DataUtil.regexStrEscape(aggRiskOperatorDto.getApiOrAppUri()));
                } else {
                    if (aggRiskOperatorDto.getApiOrAppUri().startsWith("httpapp:")) {
                        criteria.and("appUri")
                                .is(aggRiskOperatorDto.getApiOrAppUri());
                    } else {
                        criteria.and("apiUri")
                                .is(aggRiskOperatorDto.getApiOrAppUri());
                    }
                }
            }
            if (DataUtil.isNotEmpty(aggRiskOperatorDto.getEntityValue())) {
                criteria.and("entities.value")
                        .regex(com.quanzhi.auditapiv2.common.util.utils.DataUtil.regexStrEscape(aggRiskOperatorDto.getEntityValue()));
            }
            if (DataUtil.isNotEmpty(aggRiskOperatorDto.getRiskMark())) {
                criteria.and("riskMark")
                        .is(aggRiskOperatorDto.getRiskMark());
            }
            if (DataUtil.isNotEmpty(aggRiskOperatorDto.getFirstTimeStart()) && DataUtil.isNotEmpty(aggRiskOperatorDto.getFirstTimeEnd())) {
                Criteria andCriteria = new Criteria();
                andCriteria.andOperator(Criteria.where("firstTime").gte(aggRiskOperatorDto.getFirstTimeStart()), Criteria.where("firstTime").lte(aggRiskOperatorDto.getFirstTimeEnd()));
                andCriteriaList.add(andCriteria);
            }
            if (DataUtil.isNotEmpty(aggRiskOperatorDto.getLastTimeStart()) && DataUtil.isNotEmpty(aggRiskOperatorDto.getLastTimeEnd())) {
                Criteria andCriteria = new Criteria();
                andCriteria.andOperator(Criteria.where("lastTime").gte(aggRiskOperatorDto.getLastTimeStart()), Criteria.where("lastTime").lte(aggRiskOperatorDto.getLastTimeEnd()));
                andCriteriaList.add(andCriteria);
            }
        }
        Criteria customPropertyCriteria = customPropertySearchAdapter.getMongoCriteria(aggRiskOperatorDto.getCustomProperties());
        if (customPropertyCriteria != null) {
            andCriteriaList.add(customPropertyCriteria);
        }
        if (!andCriteriaList.isEmpty()) {
            criteria.andOperator(andCriteriaList.toArray(new Criteria[andCriteriaList.size()]));
        }
        queryAdapterRegistry.module(Module.AGGRISK).query(aggRiskOperatorDto, criteria);
        return criteria;
    }

    @Override
    public String dealAggRisk(AggRiskOperatorDto aggRiskOperatorDto) {
        Update update = new Update();
        update.set("state", aggRiskOperatorDto.getState().get(0));
        update.set("stateName", AggRiskInfo.RiskStateEnum.getRiskStateEnum(aggRiskOperatorDto.getState().get(0)).getName());
        if (DataUtil.isNotEmpty(aggRiskOperatorDto.getRemark())) {
            update.set("remark", aggRiskOperatorDto.getRemark());
        } else {
            update.set("remark", "");
        }
        UpdateResult result = mongoTemplate.updateFirst(new Query().addCriteria(Criteria.where("_id").is(aggRiskOperatorDto.getId())), update, collectionName);
        if (result.getModifiedCount() != 0) {
            return "success";
        } else if (result.getModifiedCount() == 0) {
            return "success";
        } else {
            return "fail";
        }
    }

    @Override
    public String batchDealAggRisk(BatchAggRiskOperatorDto batchAggRiskOperatorDto) {
        Update update = new Update();
        update.set("state", batchAggRiskOperatorDto.getState().get(0));
        update.set("stateName", AggRiskInfo.RiskStateEnum.getRiskStateEnum(batchAggRiskOperatorDto.getState().get(0)).getName());
        if (DataUtil.isNotEmpty(batchAggRiskOperatorDto.getRemark())) {
            update.set("remark", batchAggRiskOperatorDto.getRemark());
        } else {
            update.set("remark", "");
        }
        UpdateResult result = null;
        if (DataUtil.isNotEmpty(batchAggRiskOperatorDto.getIds())) {
            result = mongoTemplate.updateMulti(new Query().addCriteria(Criteria.where("_id").in(batchAggRiskOperatorDto.getIds())), update, collectionName);
        } else {
            AggRiskOperatorDto aggRiskOperatorDto = batchAggRiskOperatorDto.getAggRiskOperatorDto();
            Criteria criteria = getCriteria(aggRiskOperatorDto);
            result = mongoTemplate.updateMulti(new Query().addCriteria(criteria), update, collectionName);
        }
        if (result.getModifiedCount() != 0) {
            return "success";
        } else if (result.getModifiedCount() == 0) {
            return "success";
        } else {
            return "fail";
        }
    }

    @Override
    public String ignoreByEntity(String value, String remark, String policyId) {
        Update update = new Update();
        update.set("state", 1);
        update.set("stateName", "已忽略");
        if (DataUtil.isNotEmpty(remark)) {
            update.set("remark", remark);
        } else {
            update.set("remark", "");
        }
        UpdateResult result = mongoTemplate.updateMulti(new Query().addCriteria(Criteria.where("entities.value").is(value).and("policyId").is(policyId)), update, collectionName);
        if (result.getModifiedCount() != 0) {
            return "success";
        } else if (result.getModifiedCount() == 0) {
            return "success";
        } else {
            return "fail";
        }
    }

    @Override
    public List<AggRiskInfo> findByEntityValue(String value, String policyId) {
        return mongoTemplate.find(new Query().addCriteria(Criteria.where("entities.value").is(value).and("policyId").is(policyId)), AggRiskInfo.class, collectionName);
    }

    @Override
    public AggRiskInfo findById(String id) {
        return mongoTemplate.findOne(new Query().addCriteria(Criteria.where("_id").is(id)), AggRiskInfo.class, collectionName);
    }

    @Override
    public AggRiskInfo findByOperationId(String id) {
        return mongoTemplate.findOne(new Query().addCriteria(Criteria.where("operationId").is(id)), AggRiskInfo.class, collectionName);
    }

    @Override
    public List<String> findRiskIpsByPolicyId(List<String> id) {
        Query query = new Query();
        query.addCriteria(Criteria.where("policySnapshot._id").in(id));
        List<AggRiskInfo> riskInfos = mongoTemplate.find(query, AggRiskInfo.class, collectionName);

        return riskInfos.stream().map(riskInfo -> riskInfo.getIp()).distinct().collect(Collectors.toList());
    }

    @Override
    public List<String> findRiskIpsByLevel(List<Integer> level) {
        Query query = new Query();
        query.addCriteria(Criteria.where("level").in(level));
        List<AggRiskInfo> riskInfos = mongoTemplate.find(query, AggRiskInfo.class, collectionName);

        return riskInfos.stream().map(riskInfo -> riskInfo.getIp()).distinct().collect(Collectors.toList());
    }

    @Override
    public List<AggRiskInfo> findByPolicyIds(List<String> id) {
        return mongoTemplate.find(new Query().addCriteria(Criteria.where("policySnapshot._id").in(id)), AggRiskInfo.class, collectionName);
    }

    @Override
    public List<AggRiskInfo> findByLevel(List<Integer> level) {
        return mongoTemplate.find(new Query().addCriteria(Criteria.where("level").in(level)), AggRiskInfo.class, collectionName);
    }

    @Override
    public List<AggRiskInfo> findByEntityAndApiOrApp(String entity, String uri, String type) {
        if ("api".equals(type)) {
            return mongoTemplate.find(new Query().addCriteria(Criteria.where("entities.value").is(entity)
                    .and("apiUri").is(uri)), AggRiskInfo.class, collectionName);
        } else if ("app".equals(type)) {
            return mongoTemplate.find(new Query().addCriteria(Criteria.where("entities.value").is(entity)
                    .and("appUri").is(uri)), AggRiskInfo.class, collectionName);
        }
        return null;
    }

    @Override
    public List<AggRiskInfo> findByIds(List<String> ids) {
        return mongoTemplate.find(new Query().addCriteria(Criteria.where("_id").in(ids)), AggRiskInfo.class, collectionName);
    }

    @Override
    public void upsertAggRiskInfo(AggRiskInfo aggRiskInfo) {
        Update update = new Update();
        update.setOnInsert("name", aggRiskInfo.getName());
        update.setOnInsert("type", aggRiskInfo.getType());
        update.setOnInsert("riskMark", false);
        update.setOnInsert("id", aggRiskInfo.getId());
        update.setOnInsert("suggest", aggRiskInfo.getSuggest());
        update.setOnInsert("policyId", aggRiskInfo.getPolicyId());
        update.setOnInsert("entities", aggRiskInfo.getEntities());
        update.setOnInsert("entityType", aggRiskInfo.getEntityType());
        update.setOnInsert("policySnapshot", aggRiskInfo.getPolicySnapshot());
//        update.setOnInsert("policySnapshotV2", aggRiskInfo.getPolicySnapshotV2());
        update.set("operationId", aggRiskInfo.getOperationId());
        update.set("apiUri", aggRiskInfo.getApiUri());
        update.set("riskNum", aggRiskInfo.getRiskNum());
        update.set("deployDomains", aggRiskInfo.getDeployDomains());
        update.set("apiUrl", aggRiskInfo.getApiUrl());
        update.set("appName", aggRiskInfo.getAppName());
        update.set("departments", aggRiskInfo.getDepartments());
        update.set("appUri", aggRiskInfo.getAppUri());
        update.set("riskIds", aggRiskInfo.getRiskIds());
        update.set("account", aggRiskInfo.getAccount());
        update.set("host", aggRiskInfo.getHost());
        update.set("ip", aggRiskInfo.getIp());
        update.set("date", aggRiskInfo.getDate());
        update.set("level", aggRiskInfo.getLevel());
        update.set("levelName", aggRiskInfo.getLevelName());
        update.set("firstTime", aggRiskInfo.getFirstTime());
        update.set("lastTime", aggRiskInfo.getLastTime());
        update.set("desc", aggRiskInfo.getDesc());
        update.set("state", aggRiskInfo.getState());
        update.set("stateName", aggRiskInfo.getStateName());
        update.set("nodes", aggRiskInfo.getNodes());
        mongoTemplate.upsert(new Query().addCriteria(Criteria.where("_id").is(aggRiskInfo.getId())), update, collectionName);
    }

    @Override
    public AggRiskInfo getByAggRiskKey(String aggRiskKey) {
        return mongoTemplate.findOne(new Query().addCriteria(Criteria.where("aggRiskKey").is(aggRiskKey)), AggRiskInfo.class, collectionName);
    }

    @Override
    public List<AggRiskInfo> getAllAggRisk() {
        return mongoTemplate.findAll(AggRiskInfo.class, collectionName);
    }

    @Override
    public List<AggRiskInfo> getByDataId(String dataId) {
        return mongoTemplate.find(new Query().addCriteria(Criteria.where("entities.type").regex("DATA").and("entities.value").is(dataId)), AggRiskInfo.class, collectionName);
    }

    @Override
    public List<Long> getStatistics() {
        List<Long> result = new ArrayList<>();
        long totalRisk = mongoTemplate.count(new Query().addCriteria(org.springframework.data.mongodb.core.query.Criteria.where("state").in(new Integer[]{RiskInfo.RiskStateEnum.NOT_HANDLE.getState(), RiskInfo.RiskStateEnum.HAS_HANDLE.getState()})), collectionName);
        result.add(totalRisk);
        long totalHighLevelRisk = mongoTemplate.count(new Query().addCriteria(org.springframework.data.mongodb.core.query.Criteria.where("level").is(RiskInfo.RiskLevelEnum.HIGH.getLevel()).and("state").in(new Integer[]{RiskInfo.RiskStateEnum.NOT_HANDLE.getState(), RiskInfo.RiskStateEnum.HAS_HANDLE.getState()})), collectionName);
        result.add(totalHighLevelRisk);
        return result;
    }

    @Override
    public List<AggRiskInfo> listAggRiskInfoByDate(long startTime, long endTime, List<String> domains) {
        return mongoTemplate.find(new Query().addCriteria(Criteria.where("firstTime").gte(startTime).lte(endTime).and("appUri").in(domains).and("state").in(new Integer[]{RiskInfo.RiskStateEnum.NOT_HANDLE.getState(), RiskInfo.RiskStateEnum.HAS_HANDLE.getState()})).with(Sort.by(Sort.Order.desc("firstTime"), Sort.Order.desc("_id"))).limit(30), AggRiskInfo.class, collectionName);
    }

    @Override
    public List<IpCountEntity> getRiskDistributeByIp(List<Integer> state, List<String> appUris) {
        Criteria criteria = Criteria.where("state").in(state).and("entities.type").is("IP");
        if (DataUtil.isNotEmpty(appUris)) {
            criteria.and("apiUri").in(appUris);
        }
        Aggregation aggregation = Aggregation.newAggregation(
                Aggregation.match(
                        criteria
                )
                , Aggregation.group("entities.value").first("entities.value").as("ip").count().as("count"));
        return mongoTemplate.aggregate(aggregation, collectionName, IpCountEntity.class).getMappedResults();
    }

    @Override
    public List<IpCountEntity> getRiskCountryDistributeByDataReveal(List<String> riskNames, List<String> appUris) {
        Criteria criteria = Criteria.where("state").in(RiskInfo.RiskStateEnum.NOT_HANDLE.getState(), RiskInfo.RiskStateEnum.HAS_HANDLE.getState())
                .and("entities.type").is("IP").and("name").in(riskNames);

        if (DataUtil.isNotEmpty(appUris)) {
            criteria.and("apiUri").in(appUris);
        }

        Aggregation aggregation = Aggregation.newAggregation(
                Aggregation.match(criteria)
                , Aggregation.group("entities.value").first("entities.value").as("ip").count().as("count"));
        return mongoTemplate.aggregate(aggregation, collectionName, IpCountEntity.class).getMappedResults();
    }

    @Override
    public List<AggRiskInfo> selectIpRisk(Integer count, Integer limit) {
        return mongoTemplate.find(new Query().addCriteria(Criteria.where("entities.type").is("IP").and("state").in(Arrays.asList(0, 2))).skip(count).limit(limit), AggRiskInfo.class, collectionName);
    }

    @Override
    public List<AggRiskInfo> selectAccountRisk(Integer count, Integer limit) {
        return mongoTemplate.find(new Query().addCriteria(Criteria.where("entities.type").is("ACCOUNT").and("state").in(Arrays.asList(0, 2))).skip(count).limit(limit), AggRiskInfo.class, collectionName);
    }

    @Override
    public List<AggRiskInfo> selectApiRisk(Integer count, Integer limit) {
        return mongoTemplate.find(new Query().addCriteria(Criteria.where("entities.type").is("API").and("state").in(Arrays.asList(0, 2))).skip(count).limit(limit), AggRiskInfo.class, collectionName);
    }

    @Override
    public List<AggRiskInfo> selectAppRisk(Integer count, Integer limit) {
        return mongoTemplate.find(new Query().addCriteria(Criteria.where("entities.type").is("APP").and("state").in(Arrays.asList(0, 2))).skip(count).limit(limit), AggRiskInfo.class, collectionName);
    }

    @Override
    public <T> List<T> findDistinct(Query query, String field, String collectionName, Class<T> resultClass) {
        return mongoTemplate.findDistinct(query, field, collectionName, resultClass);
    }

    @Override
    public String ignoreByEntityValue(String policyId, String value, String remark, String operateName) {
        Update update = new Update();
        update.set("state", 1);
        update.set("stateName", "已忽略");
        update.set("operateName", operateName);
        update.set("operateTime", System.currentTimeMillis());
        if (DataUtil.isNotEmpty(remark)) {
            update.set("remark", remark);
        } else {
            update.set("remark", "");
        }
        UpdateResult result = mongoTemplate.updateFirst(new Query().addCriteria(Criteria.where("entities.value").is(value).and("policyId").is(policyId)), update, collectionName);
        if (result.getModifiedCount() != 0) {
            return "success";
        } else if (result.getModifiedCount() == 0) {
            return "success";
        } else {
            return "fail";
        }
    }

}
