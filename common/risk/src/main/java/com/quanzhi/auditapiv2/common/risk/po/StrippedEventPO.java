package com.quanzhi.auditapiv2.common.risk.po;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * create at 2022/8/30 11:30 上午
 * @description: 全流量事件表
 **/
@Data
@TableName("http_defined_event")
public class StrippedEventPO extends BasePO {
    /**
     * 事件ID
     */
    private String id;
    /**
     * 事件时间
     */
    private Long timestamp;
    /**
     * 日期，如********
     */
    private String date;
    /**
     * 去掉参数的接口URL
     */
    private String apiUrl;
    /**
     * ip
     */
    private String ip;
    /**
     * 账号
     */
    private String account;
    /**
     * 账号类型
     */
    private String accountType;
    /**
     * 密码
     */
    private String password;
    /**
     * 请求方法
     */
    private String method;
    /**
     * 请求类型
     */
    private String reqContentType;
    /**
     * User-Agent
     */
    private String ua;
    /**
     * User-Agent类型
     */
    private String uaType;
    /**
     * REFERER
     */
    private String referer;
    /**
     * COOKIE 数据
     */
    private String cookie;
    /**
     * 返回状态码
     */
    private String rspStatus;
    /**
     * 返回格式
     */
    private String rspContentType;
    /**
     * 返回长度
     */
    private Integer rspContentLength;
    /**
     * 返回数据标签
     */
    private List<String> rspDataLabelIds;
    /**
     * 请求数据标签
     */
    private List<String> reqDataLabelIds;
    /**
     * 数据标签
     */
    private List<String> dataLabelIds;
    /**
     * 去重标签数量
     */
    private Long rspLabelContentDistinctCount;
    /**
     * 返回标签
     */
    @TableField("labelValue.labels")
    private List<String> nestedLabels;
    /**
     * 返回标签对应值
     */
    @TableField("labelValue.values")
    private List<List<String>> nestedValues;

    /**
     * 登录信息
     */
    private String loginInfo;
    /**
     * 登陆结果
     */
    private String loginResult;
    /**
     * 是否攻击
     */
    private Boolean attack;
    /**
     * 攻击方式
     */
    private String attackType;
    /**
     * 攻击结果
     */
    private Integer attackSuccess;
    /**
     * 事件定义
     */
    private List<String> eventDefineIds;
    /**
     * 部署域
     */
    private List<String> deployDomains;
    /**
     * 访问域
     */
    private List<String> accessDomains;
    /**
     * 应用域
     */
    private String host;
    /**
     * 风险类型
     */
    private List<String> riskTypes;
    /**
     * 接口uri
     */
    private String apiUri;
    /**
     * 应用uri
     */
    private String appUri;
    /**
     * 省
     */
    private String province;
    /**
     * 市
     */
    private String city;
    /**
     * 国家
     */
    private String country;
    /**
     * 提取到的数据
     */
    private List<String> enumerateParaSigns;

    private Long minute;
    /**
     * 风险过滤tag
     */
    private List<String> statMarkingTags;
    /**
     * 保留标记
     */
    private Boolean keepFlag = false;

    /**
     * get请求参数
     */
    private String getParam;
    /**
     * post请求参数
     */
    private String postParam;
    /**
     * 攻击
     */
    private List<String> attackKeywords;


}