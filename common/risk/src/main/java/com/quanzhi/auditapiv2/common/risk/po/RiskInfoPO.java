package com.quanzhi.auditapiv2.common.risk.po;

import com.quanzhi.auditapiv2.core.risk.entity.RiskInfo;
import org.springframework.data.mongodb.core.index.CompoundIndex;
import org.springframework.data.mongodb.core.index.CompoundIndexes;
import org.springframework.data.mongodb.core.mapping.Document;

/**
 * @Author: HaoJun
 * @Date: 2021/8/14 2:12 下午
 */
@Document("riskInfo")
@CompoundIndexes(
        @CompoundIndex(name = "date", def = "{'date': 1}", background = true)
)
public class RiskInfoPO extends RiskInfo {
}
