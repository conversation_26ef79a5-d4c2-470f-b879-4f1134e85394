package com.quanzhi.auditapiv2.common.risk.po;

import com.quanzhi.auditapiv2.core.risk.entity.DefinedEventCleanLog;
import lombok.Data;
import org.springframework.data.mongodb.core.index.Indexed;
import org.springframework.data.mongodb.core.mapping.Document;

/**
 * @Author: HaoJun
 * @Date: 2021/8/13 4:50 下午
 */
@Document("definedEventCleanLog")
@Data
public class DefinedEventCleanLogPO{

    private String startDate;

    private String id;
    /**
     * 创建时间
     */
    private Long createTime;
    /**
     * 更新时间
     */
    private Long updateTime;

    @Indexed(unique = true)
    private String endDate;

    private DefinedEventCleanLog.CleanState state;

    private String msg;

    private Long duration;
}
