package com.quanzhi.auditapiv2.common.risk.converter;

import com.quanzhi.auditapiv2.common.risk.po.DefinedEventPO;
import com.quanzhi.auditapiv2.core.risk.entity.DefinedEvent;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @Author: HaoJun
 * @Date: 2021/8/9 9:54 上午
 */
@Mapper
public interface DefinedEventConverter {

    DefinedEventConverter INSTANCE = Mappers.getMapper(DefinedEventConverter.class);

    @Mapping(target = "labelValues", expression = "java(convertLabelValues(definedEvent))")
    DefinedEvent convert(DefinedEventPO definedEvent);

    @Mapping(target = "nestedLabels", expression = "java(convertLabels(definedEvent))")
    @Mapping(target = "nestedValues", expression = "java(convertValues(definedEvent))")
    DefinedEventPO convert(DefinedEvent definedEvent);

    default List<DefinedEvent.LabelValue> convertLabelValues(DefinedEventPO definedEventPO) {
        List<DefinedEvent.LabelValue> labelValues = new ArrayList<>();
        if (definedEventPO.getNestedLabels() == null
                || definedEventPO.getNestedValues() == null) {
            return labelValues;
        }
        if (definedEventPO.getNestedLabels().size() == 0
                || definedEventPO.getNestedValues().size() != definedEventPO.getNestedLabels().size()) {
            return labelValues;
        }
        for (int i = 0; i < definedEventPO.getNestedLabels().size(); i++) {
            DefinedEvent.LabelValue labelValue = new DefinedEvent.LabelValue();
            labelValue.setLabel(definedEventPO.getNestedLabels().get(i));
            labelValue.setValues(definedEventPO.getNestedValues().get(i));
            labelValues.add(labelValue);
        }
        return labelValues;
    }

    default List<String> convertLabels(DefinedEvent definedEvent) {
        if (definedEvent.getLabelValues() == null || definedEvent.getLabelValues().size() == 0) {
            return Collections.emptyList();
        }
        return definedEvent.getLabelValues().stream().map(labelValue -> labelValue.getLabel()).collect(Collectors.toList());
    }

    default List<List<String>> convertValues(DefinedEvent definedEvent) {
        if (definedEvent.getLabelValues() == null || definedEvent.getLabelValues().size() == 0) {
            return Collections.emptyList();
        }
        return definedEvent.getLabelValues().stream().map(labelValue -> labelValue.getValues()).collect(Collectors.toList());
    }


}
