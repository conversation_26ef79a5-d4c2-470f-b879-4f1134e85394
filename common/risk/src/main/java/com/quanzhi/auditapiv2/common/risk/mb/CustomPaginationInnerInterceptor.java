package com.quanzhi.auditapiv2.common.risk.mb;

import com.baomidou.mybatisplus.annotation.DbType;
import com.baomidou.mybatisplus.core.metadata.OrderItem;
import com.baomidou.mybatisplus.extension.plugins.inner.PaginationInnerInterceptor;

import java.util.List;

/**
 * @Author: HaoJun
 * @Date: 2021/1/13 10:34 上午
 * <p>
 * fix clickhouse函数无法sort的问题
 */
public class CustomPaginationInnerInterceptor extends PaginationInnerInterceptor {

    public CustomPaginationInnerInterceptor(DbType dbType) {
        super(dbType);
    }

    /**
     * 遇到clickhouse函数的时候自己拼接SQL
     *
     * @param originalSql
     * @param orderList
     * @return
     */
    @Override
    protected String concatOrderBy(String originalSql, List<OrderItem> orderList) {
        if (originalSql.contains("match(") 
                || originalSql.contains("hasAny(") 
                || originalSql.contains("hasAll(")
                || originalSql.contains("has(")
                || originalSql.contains("arrayFilter(")) {
            int index = 0;
            int size = orderList.size();
            originalSql += " ORDER BY ";
            for (OrderItem orderItem : orderList) {
                originalSql += orderItem.getColumn() + (orderItem.isAsc() ? " ASC " : " DESC ");
                if (++index < size) {
                    originalSql += ",";
                }
            }
            return originalSql;
        }
        return super.concatOrderBy(originalSql, orderList);
    }

    /**
     * 不优化count sql
     *
     * @param optimizeCountSql
     * @param sql
     * @return
     */
    @Override
    protected String autoCountSql(boolean optimizeCountSql, String sql) {
        return lowLevelCountSql(sql);
    }
}
