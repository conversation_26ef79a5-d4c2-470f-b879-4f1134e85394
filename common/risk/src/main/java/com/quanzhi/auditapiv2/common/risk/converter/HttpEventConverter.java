package com.quanzhi.auditapiv2.common.risk.converter;

import com.alibaba.fastjson.JSON;
import com.quanzhi.audit.crypt.sdk.QzCrypt;
import com.quanzhi.audit.crypt.sdk.enums.EncryModelEnum;
import com.quanzhi.audit_core.common.enums.SourceEnum;
import com.quanzhi.audit_core.common.model.EvidenceInfo;
import com.quanzhi.audit_core.common.model.HttpEvent;
import com.quanzhi.auditapiv2.common.risk.po.DefinedEventPO;
import com.quanzhi.auditapiv2.core.risk.entity.DefinedEvent;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.factory.Mappers;

import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @Author: HaoJun
 * @Date: 2021/8/10 9:42 上午
 */
@Mapper(nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface HttpEventConverter {

    HttpEventConverter INSTANCE = Mappers.getMapper(HttpEventConverter.class);

    @Mapping(target = "date", expression = "java(HttpEventConverterUtils.convertDate(event))")
    @Mapping(target = "account", source = "loginInfo.account")
    @Mapping(target = "password", expression = "java(event.getLoginInfo() != null ? event.getLoginInfo().getPassword() : \"\")")
    @Mapping(target = "method", source = "req.method")
    @Mapping(target = "rspStatus", source = "rsp.status")
    @Mapping(target = "attack", source = "scanInfo.isAttack")
    @Mapping(target = "attackType", source = "scanInfo.scanType")
    @Mapping(target = "attackSuccess", source = "scanInfo.attackSuccess")
    @Mapping(target = "province", source = "originIpPosition.province")
    @Mapping(target = "city", source = "originIpPosition.city")
    @Mapping(target = "country", source = "originIpPosition.country")
    @Mapping(target = "location", source = "originIpPosition.location")
    @Mapping(target = "minute", expression = "java(HttpEventConverterUtils.convertMinute(event))")
    @Mapping(target = "getParam", expression = "java(HttpEventConverterUtils.convertGetParam(event))")
    @Mapping(target = "postParam", expression = "java(HttpEventConverterUtils.convertPostParam(event))")
    @Mapping(target = "accessDomains", expression = "java(HttpEventConverterUtils.convertAccessDomains(event))")
    @Mapping(target = "deployDomains", expression = "java(HttpEventConverterUtils.convertDeployDomains(event))")
    @Mapping(target = "loginResult", expression = "java(event.getLoginInfo() != null ? event.getLoginInfo().getLoginResult() : \"\")")
    @Mapping(target = "attackKeywords",source="scanInfo.attackKeywords" )
    DefinedEventPO convert(HttpEvent event);

    DateTimeFormatter FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd");


    final class HttpEventConverterUtils {

        public static final List<String> convertAccessDomains(HttpEvent httpEvent) {
            if (httpEvent.getAccessDomains() == null) {
                return Collections.emptyList();
            }
            return httpEvent.getAccessDomains().stream().map(domain -> domain.getId()).collect(Collectors.toList());
        }

        public static final List<String> convertDeployDomains(HttpEvent httpEvent) {
            if (httpEvent.getDeployDomains() == null) {
                return Collections.emptyList();
            }
            return httpEvent.getDeployDomains().stream().map(domain -> domain.getId()).collect(Collectors.toList());
        }

        public static final String convertDate(HttpEvent event) {
            return LocalDateTime.ofInstant(Instant.ofEpochMilli(event.getTimestamp()),
                    ZoneId.systemDefault()).format(FORMATTER);
        }

        public static final String convertJsonString(Map<?, ?> obj) {
            if (obj == null) {
                return "";
            }
            return JSON.toJSONString(obj);
        }

        public static final long convertMinute(HttpEvent event) {
            if (event.getTimestamp() == null) {
                return 0;
            }
            return (event.getTimestamp() / 1000 / 60) * 1000 * 60;
        }


        public static final void buildNestedLabels(DefinedEventPO definedEventPO, EvidenceInfo evidenceInfo) {
            if (evidenceInfo == null) {
                return;
            }
            Map<String, Set<String>> values = getLabelMap(evidenceInfo);
            definedEventPO.setNestedLabels(new ArrayList<>(values.keySet()));
            definedEventPO.setNestedValues(new ArrayList<>());
            for (Map.Entry<String, Set<String>> entry : values.entrySet()) {
                definedEventPO.getNestedValues().add(new ArrayList<>(entry.getValue()));
            }
        }

        public static final List<DefinedEvent.LabelValue> convert(EvidenceInfo evidenceInfo) {
            if (evidenceInfo == null || evidenceInfo.getExtractValues() == null) {
                return Collections.emptyList();
            }
            Map<String, Set<String>> values = getLabelMap(evidenceInfo);
            List<DefinedEvent.LabelValue> labelValues = new ArrayList<>();
            for (Map.Entry<String, Set<String>> entry : values.entrySet()) {
                DefinedEvent.LabelValue labelValue = new DefinedEvent.LabelValue();
                labelValue.setLabel(entry.getKey());
                // 标签内容加密
                labelValue.setValues(QzCrypt.encrypt(EncryModelEnum.CLIENT, new ArrayList<>(entry.getValue())));
                labelValues.add(labelValue);
            }
            return labelValues;
        }

        public static final Map<String, Set<String>> getLabelMap(EvidenceInfo evidenceInfo) {
            Map<String, Set<String>> values = new HashMap<>();
            if (evidenceInfo.getExtractValues() == null){
                return values;
            }
            for (EvidenceInfo.ExtractValue extractValue : evidenceInfo.getExtractValues()) {
                if (extractValue == null
                        || extractValue.getLabelValues() == null
                        || extractValue.getSource() == null) {
                    continue;
                }
                if (extractValue.getSource() != SourceEnum.RSP) {
                    continue;
                }
                for (Map.Entry<String, List<String>> entry : extractValue.getLabelValues().entrySet()) {
                    if (values.get(entry.getKey()) == null) {
                        values.put(entry.getKey(), new HashSet<>());
                    }
                    values.get(entry.getKey()).addAll(entry.getValue());
                }
            }
            return values;
        }

        public static final String convertGetParam(HttpEvent event) {
            if (event.getReq() == null || event.getReq().getGetArgsString() == null) {
                return "";
            }
            return event.getReq().getGetArgsString();
        }

        public static final String convertPostParam(HttpEvent event) {
            if (event.getReq() == null || event.getReq().getPostArgsString() == null) {
                return "";
            }
            return event.getReq().getPostArgsString();
        }

        public static final String encrypt(String value) {
            return QzCrypt.encrypt(EncryModelEnum.CLIENT, value);
        }


        public static final Integer convertAttackSuccess(HttpEvent event) {
            return event.getScanInfo()==null?2:event.getScanInfo().getIsAttackSuccess()?1:0;
        }

    }


}
