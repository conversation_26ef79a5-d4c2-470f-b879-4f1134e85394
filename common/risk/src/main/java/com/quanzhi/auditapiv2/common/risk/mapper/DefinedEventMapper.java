package com.quanzhi.auditapiv2.common.risk.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.quanzhi.auditapiv2.common.risk.po.DefinedEventPO;
import com.quanzhi.auditapiv2.core.risk.dto.common.GroupDataDto;
import com.quanzhi.auditapiv2.core.risk.dto.common.RiskDescribeDto;
import com.quanzhi.auditapiv2.core.risk.dto.common.RiskIpJoinAccountDto;
import com.quanzhi.auditapiv2.core.risk.entity.DefinedEvent;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.util.List;

/**
 * @Author: HaoJun
 * @Date: 2021/8/9 9:51 上午
 */
@Mapper
public interface DefinedEventMapper extends BaseMapper<DefinedEventPO> {

    /**
     * 通用查询
     * @Comments:  <对此方法的描述，可以引用系统设计中的描述>
     * <AUTHOR> [<EMAIL>]
     * @since 2021-08-10 9:56
     * @param
     * @return
     */
    @Select({"SELECT ${field} ",
            "FROM audit.qz_http_defined_event ${join} ",
            "WHERE ${sql}"})
    RiskDescribeDto selectDefinedEvent(@Param("sql") String sql, @Param("field") String field, @Param("join") String join);

    /**
     * IPTop10
     * @Comments:  <对此方法的描述，可以引用系统设计中的描述>
     * <AUTHOR> [<EMAIL>]
     * @since 2021-08-10 9:56
     * @param
     * @return
     */
    @Select({"SELECT ip AS name, COUNT(ip) AS count ",
            "FROM audit.qz_http_defined_event ",
            "WHERE ${sql} ",
            "GROUP BY ip ",
            "ORDER BY count DESC ",
            "LIMIT 10"})
    List<GroupDataDto> ipTop10(@Param("sql") String sql);

    /**
     * APITop10
     * @Comments:  <对此方法的描述，可以引用系统设计中的描述>
     * <AUTHOR> [<EMAIL>]
     * @since 2021-08-10 9:56
     * @param
     * @return
     */
    @Select({"SELECT apiUrl AS name, COUNT(apiUrl) AS count ",
            "FROM audit.qz_http_defined_event ",
            "WHERE ${sql} ",
            "GROUP BY apiUrl ",
            "ORDER BY count DESC ",
            "LIMIT 10"})
    List<GroupDataDto> apiTop10(@Param("sql") String sql);

    /**
     * UATop10
     * @Comments:  <对此方法的描述，可以引用系统设计中的描述>
     * <AUTHOR> [<EMAIL>]
     * @since 2021-08-10 9:56
     * @param
     * @return
     */
    @Select({"SELECT ua AS name, COUNT(ua) AS count ",
            "FROM audit.qz_http_defined_event ",
            "WHERE ${sql} ",
            "GROUP BY ua ",
            "ORDER BY count DESC ",
            "LIMIT 10"})
    List<GroupDataDto> uaTop10(@Param("sql") String sql);

    /**
     * 终端类型Top10
     * @Comments:  <对此方法的描述，可以引用系统设计中的描述>
     * <AUTHOR> [<EMAIL>]
     * @since 2021-08-10 9:56
     * @param
     * @return
     */
    @Select({"SELECT uaType AS name, COUNT(uaType) AS count ",
            "FROM audit.qz_http_defined_event ",
            "WHERE ${sql} ",
            "GROUP BY uaType ",
            "ORDER BY count DESC ",
            "LIMIT 10"})
    List<GroupDataDto> uaTypeTop10(@Param("sql") String sql);

    /**
     * 数据分布情况环形图
     * @Comments:  <对此方法的描述，可以引用系统设计中的描述>
     * <AUTHOR> [<EMAIL>]
     * @since 2021-08-10 9:56
     * @param
     * @return
     */
    @Select({"SELECT labelValue.labels AS name, LENGTH( groupUniqArrayArray( labelValue.values) ) AS count ",
            "FROM audit.qz_http_defined_event ",
            "ARRAY JOIN labelValue ",
            "WHERE ${sql} ",
            "GROUP BY labelValue.labels"})
    List<GroupDataDto> dataLabelDonutChart(@Param("sql") String sql);

    /**
     * 事件返回情况环形图
     * @Comments:  <对此方法的描述，可以引用系统设计中的描述>
     * <AUTHOR> [<EMAIL>]
     * @since 2021-08-10 9:56
     * @param
     * @return
     */
    @Select({"<script>",
            "SELECT COUNT(id) AS count ",
            "FROM audit.qz_http_defined_event ",
            "WHERE ${sql} ",
            "<choose>",
            "<when test='rsp == true'>",
            "AND rspContentLength > 0 ",
            "</when>",
            "<when test='rsp == false'>",
            "AND rspContentLength = 0",
            "</when>",
            "</choose>",
            "</script>"})
    Long rspDonutChart(@Param("sql") String sql, @Param("rsp") Boolean rsp) throws Exception;

    /**
     * 登录结果分布环形图
     * @Comments:  <对此方法的描述，可以引用系统设计中的描述>
     * <AUTHOR> [<EMAIL>]
     * @since 2021-08-10 9:56
     * @param
     * @return
     */
    @Select({"SELECT loginResult AS name, COUNT(loginResult) AS count ",
            "FROM audit.qz_http_defined_event ",
            "WHERE ${sql} ",
            "GROUP BY loginResult"})
    List<GroupDataDto> loginResultDonutChart(@Param("sql") String sql);

    /**
     * 返回状态码环形图
     * @Comments:  <对此方法的描述，可以引用系统设计中的描述>
     * <AUTHOR> [<EMAIL>]
     * @since 2021-08-10 9:56
     * @param
     * @return
     */
    @Select({"SELECT rspStatus AS name, COUNT(rspStatus) AS count ",
            "FROM audit.qz_http_defined_event ",
            "WHERE ${sql} ",
            "GROUP BY rspStatus"})
    List<GroupDataDto> rspStatusDonutChart(@Param("sql") String sql);

    /**
     * 攻击结果分布环形图
     * @Comments:  <对此方法的描述，可以引用系统设计中的描述>
     * <AUTHOR> [<EMAIL>]
     * @since 2021-08-10 9:56
     * @param
     * @return
     */
    @Select({"SELECT attackSuccess AS name, COUNT(attackSuccess) AS count ",
            "FROM audit.qz_http_defined_event ",
            "WHERE ${sql} ",
            "GROUP BY attackSuccess"})
    List<GroupDataDto> attackSuccessDonutChart(@Param("sql") String sql);

    /**
     * 数据量分布条形图
     * @Comments:  <对此方法的描述，可以引用系统设计中的描述>
     * <AUTHOR> [<EMAIL>]
     * @since 2021-08-10 9:56
     * @param
     * @return
     */
    @Select({"SELECT rspLabelContentDistinctCount AS name, COUNT(rspLabelContentDistinctCount) AS count ",
            "FROM audit.qz_http_defined_event ",
            "WHERE ${sql} ",
            "GROUP BY rspLabelContentDistinctCount ",
            "ORDER BY name DESC"})
    List<GroupDataDto> dataAmountBarChart(@Param("sql") String sql);

    /**
     * 事件摘要列表
     * @Comments:  <对此方法的描述，可以引用系统设计中的描述>
     * <AUTHOR> [<EMAIL>]
     * @since 2021-08-10 9:56
     * @param
     * @return
     */
    @Select({"SELECT *, labelValue.labels AS nestedLabels, labelValue.values AS nestedValues ",
            "FROM audit.qz_http_defined_event ",
            "WHERE ${sql}"})
    IPage<DefinedEventPO> selectDefinedEventList(@Param("sql") String sql, IPage<DefinedEventPO> page);

    /**
     * 事件摘要分组
     * @Comments:  <对此方法的描述，可以引用系统设计中的描述>
     * <AUTHOR> [<EMAIL>]
     * @since 2021-08-10 9:56
     * @param
     * @return
     */
    @Select({"SELECT ${groupField}, COUNT() AS count ",
            "FROM audit.qz_http_defined_event ",
            "WHERE ${sql} ",
            "GROUP BY ${groupField} ",
            "ORDER BY count DESC ",
            "LIMIT 50"})
    List<GroupDataDto> selectDefinedEventGroup(@Param("sql") String sql, @Param("groupField") String groupField);

    /**
     * 事件去重IP列表
     * @Comments:  <对此方法的描述，可以引用系统设计中的描述>
     * <AUTHOR> [<EMAIL>]
     * @since 2021-08-10 9:56
     * @param
     * @return
     */
    @Select({"SELECT DISTINCT ip",
            "FROM audit.qz_http_defined_event ",
            "WHERE ${sql} ",
            "LIMIT 10000"})
    List<String> selectIpList(@Param("sql") String sql);

    /**
     * 事件去重账号列表
     * @Comments:  <对此方法的描述，可以引用系统设计中的描述>
     * <AUTHOR> [<EMAIL>]
     * @since 2021-08-10 9:56
     * @param
     * @return
     */
    @Select({"SELECT DISTINCT account",
            "FROM audit.qz_http_defined_event ",
            "WHERE ${sql} ",
            "LIMIT 10000"})
    List<String> selectAccountList(@Param("sql") String sql);

    /**
     * 事件去重密码列表
     * @Comments:  <对此方法的描述，可以引用系统设计中的描述>
     * <AUTHOR> [<EMAIL>]
     * @since 2021-08-10 9:56
     * @param
     * @return
     */
    @Select({"SELECT DISTINCT password",
            "FROM audit.qz_http_defined_event ",
            "WHERE ${sql} ",
            "LIMIT 10000"})
    List<GroupDataDto> selectPasswordList(@Param("sql") String sql);

    /**
     * 事件去重账号密码列表
     * @Comments:  <对此方法的描述，可以引用系统设计中的描述>
     * <AUTHOR> [<EMAIL>]
     * @since 2021-08-10 9:56
     * @param
     * @return
     */
    @Select({"SELECT account, password",
            "FROM audit.qz_http_defined_event ",
            "WHERE ${sql} ",
            "GROUP BY account, password",
            "LIMIT 10000"})
    List<GroupDataDto> selectAccountAndPasswordList(@Param("sql") String sql);

    /**
     * IP关联账号列表
     * @Comments:  <对此方法的描述，可以引用系统设计中的描述>
     * <AUTHOR> [<EMAIL>]
     * @since 2021-08-10 9:56
     * @param
     * @return
     */
    @Select({"SELECT account, apiUrl, ",
            "COUNT(loginResult) AS loginCount, ",
            "COUNT(loginResult = 'LOGIN_SUCCESS' OR NULL) AS loginSuccessCount, ",
            "COUNT(loginResult = 'LOGIN_FAIL' OR NULL) AS loginFailCount, ",
            "COUNT(loginResult = 'UNKNOWN' OR NULL) AS loginUnknownCount, ",
            "MIN(timestamp) AS discoverTime, MAX(timestamp) AS activeTime ",
            "FROM audit.qz_http_defined_event ",
            "WHERE ip = #{ip} ",
            "AND account != '' AND account IS NOT NULL ",
            "AND apiUrl != '' AND apiUrl IS NOT NULL ",
            "AND loginResult != '' AND loginResult IS NOT NULL ",
            "GROUP BY account, apiUrl ",
            "ORDER BY loginCount DESC"})
    IPage<RiskIpJoinAccountDto> selectIpJoinAccountList(@Param("ip") String ip, IPage<RiskIpJoinAccountDto> page);

    /**
     * <AUTHOR>
     * @description: 获取数据标签及对应的标签内容
     * @date: 2021/8/13
     * @param sql
     * @Return java.util.List<com.quanzhi.auditapiv2.core.risk.dto.DefinedEventDto>
     */
    @Select({"SELECT labelValue.labels AS label , groupUniqArrayArray( labelValue.values) AS values ",
             "FROM audit.qz_http_defined_event ARRAY JOIN labelValue WHERE ${sql} ",
             "GROUP BY labelValue.labels"})
    List<DefinedEvent.LabelValue> getDataLabelInfo(@Param("sql") String sql);

    @Select("SELECT * FROM audit.qz_http_defined_event WHERE ${sqlSegment} AND date >= #{startDate} AND date <= #{endDate}")
    IPage<DefinedEventPO> findByStartDate(@Param("sqlSegment") String sqlSegment, @Param("startDate")String startDate, @Param("endDate")String endDate, IPage<DefinedEventPO> page);

    @Select("SELECT * FROM audit.qz_http_defined_event WHERE ${sqlSegment} AND date = #{date}")
    IPage<DefinedEventPO> findByDate(@Param("sqlSegment") String sqlSegment, @Param("date")String date, IPage<DefinedEventPO> page);

    @Update("ALTER TABLE audit.qz_http_defined_event DELETE WHERE date=#{date} and keepFlag=0")
    void delete(@Param("date") String date);

    @Update("ALTER TABLE audit.qz_http_defined_event update ${updateSql} where ${whereSql}")
    void updateBatchBy(@Param("updateSql")String updateSql, @Param("whereSql")String whereSql);
}
