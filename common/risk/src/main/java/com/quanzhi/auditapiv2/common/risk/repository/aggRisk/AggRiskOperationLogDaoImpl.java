package com.quanzhi.auditapiv2.common.risk.repository.aggRisk;

import com.quanzhi.auditapiv2.core.risk.repository.aggRisk.AggRiskOperationLogDao;
import com.quanzhi.auditapiv2.common.dal.dao.base.impl.BaseDaoImpl;
import com.quanzhi.auditapiv2.common.dal.entity.aggRisk.AggRiskOperationLog;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * @Author: yangzx
 * @Date: 2024/10/9 14:38
 */
@Repository
public class AggRiskOperationLogDaoImpl extends BaseDaoImpl<AggRiskOperationLog> implements AggRiskOperationLogDao {

    private final MongoTemplate mongoTemplate;

    private final String collectionName = "aggRiskOperationLog";

    public AggRiskOperationLogDaoImpl(MongoTemplate mongoTemplate) {
        this.mongoTemplate = mongoTemplate;
    }


    @Override
    public void insertLog(AggRiskOperationLog aggRiskOperationLog) {
        mongoTemplate.save(aggRiskOperationLog, collectionName);
    }

    @Override
    public void batchInsertLog(List<AggRiskOperationLog> aggRiskOperationLogs) {
        mongoTemplate.insertAll(aggRiskOperationLogs);
    }

    @Override
    public List<AggRiskOperationLog> listLog(String riskId) {
        return mongoTemplate.find(new Query().addCriteria(Criteria.where("riskId").is(riskId)), AggRiskOperationLog.class, collectionName);
    }
}
