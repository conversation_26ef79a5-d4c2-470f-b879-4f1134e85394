package com.quanzhi.auditapiv2.common.risk.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.quanzhi.auditapiv2.common.risk.po.DefinedEventPO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Update;

/**
 * @Author: HaoJun
 * @Date: 2021/8/13 1:59 下午
 */
@Mapper
public interface TempDefinedEventMapper extends BaseMapper<DefinedEventPO> {

    @Update("${sql}")
    void execute(String sql);
}
