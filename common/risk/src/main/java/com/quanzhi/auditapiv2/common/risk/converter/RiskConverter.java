package com.quanzhi.auditapiv2.common.risk.converter;

import com.quanzhi.auditapiv2.common.risk.po.RiskInfoPO;
import com.quanzhi.auditapiv2.core.risk.entity.RiskInfo;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * @Author: HaoJun
 * @Date: 2021/8/14 2:12 下午
 */
@Mapper
public interface RiskConverter {

    RiskConverter INSTANCE = Mappers.getMapper(RiskConverter.class);

    RiskInfoPO convert(RiskInfo riskInfo);

    RiskInfo convert(RiskInfoPO riskInfo);

}
