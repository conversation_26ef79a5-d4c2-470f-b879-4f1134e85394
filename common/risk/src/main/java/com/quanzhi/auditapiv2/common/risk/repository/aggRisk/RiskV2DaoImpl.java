package com.quanzhi.auditapiv2.common.risk.repository.aggRisk;

import com.mongodb.client.result.UpdateResult;
import com.quanzhi.auditapiv2.common.dal.dao.base.impl.BaseDaoImpl;
import com.quanzhi.auditapiv2.common.dal.dao.impl.common.Module;
import com.quanzhi.auditapiv2.common.dal.dao.impl.common.QueryAdapterRegistry;
import com.quanzhi.auditapiv2.common.dal.dto.aggRisk.BatchRiskV2OperatorDto;
import com.quanzhi.auditapiv2.common.dal.dto.aggRisk.RiskV2OperatorDto;
import com.quanzhi.auditapiv2.common.util.dto.AggregationDto;
import com.quanzhi.auditapiv2.common.util.dto.GroupDto;
import com.quanzhi.auditapiv2.common.util.utils.ConstantUtil;
import com.quanzhi.auditapiv2.common.util.utils.DataUtil;
import com.quanzhi.auditapiv2.core.risk.entity.RiskInfo;
import com.quanzhi.auditapiv2.core.risk.repository.aggRisk.RiskV2Dao;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.stereotype.Repository;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.regex.Pattern;

/**
 * @Author: yangzx
 * @Date: 2024/10/9 14:38
 */
@Repository
public class RiskV2DaoImpl extends BaseDaoImpl<RiskInfo> implements RiskV2Dao {

    private final MongoTemplate mongoTemplate;

    private final String collectionName = "riskInfo";

    @Autowired
    public QueryAdapterRegistry queryAdapterRegistry;

    public RiskV2DaoImpl(MongoTemplate mongoTemplate) {
        this.mongoTemplate = mongoTemplate;
    }

    @Override
    public List<RiskInfo> listRiskV2(RiskV2OperatorDto riskV2OperatorDto) throws Exception {
        Integer limit = riskV2OperatorDto.getLimit();
        Integer page = riskV2OperatorDto.getPage();
        Integer sort = riskV2OperatorDto.getSort();
        String field = riskV2OperatorDto.getSortField();
        Criteria criteria = getCriteria(riskV2OperatorDto);
        // 分页
        Pageable pageable = PageRequest.of(page - 1, limit);
        // 排序
        Sort dbSort = null;
        if (com.quanzhi.audit_core.common.utils.DataUtil.isNotEmpty(field) && com.quanzhi.audit_core.common.utils.DataUtil.isNotEmpty(sort)) {
            if (sort == ConstantUtil.Sort.ASC) {
                dbSort = Sort.by(Sort.Order.desc("riskMark"), Sort.Order.asc(field), Sort.Order.desc("_id"));
            } else if (sort == ConstantUtil.Sort.DESC) {
                dbSort = Sort.by(Sort.Direction.DESC, "riskMark", field, "_id");
            } else {
                dbSort = Sort.by(Sort.Direction.DESC, "riskMark", field, "_id");
            }
        } else {
            dbSort = Sort.by(Sort.Direction.DESC, "riskMark", "_id");
        }
        return mongoTemplate.find(new Query().addCriteria(criteria).with(pageable).with(dbSort), RiskInfo.class, collectionName);
    }

    @Override
    public List<AggregationDto> groupRiskV2(RiskV2OperatorDto riskV2OperatorDto, GroupDto groupDto) throws Exception {
        return group(getCriteria(riskV2OperatorDto), groupDto, riskV2OperatorDto.getSortField(), riskV2OperatorDto.getSort(), riskV2OperatorDto.getPage(), riskV2OperatorDto.getLimit());
    }

    @Override
    public String markRiskV2(RiskV2OperatorDto riskV2OperatorDto) {
        Update update = new Update();
        update.set("riskMark", riskV2OperatorDto.getRiskMark());
        UpdateResult result = mongoTemplate.updateFirst(new Query().addCriteria(Criteria.where("_id").is(riskV2OperatorDto.getId())), update, collectionName);
        if (result.getModifiedCount() != 0) {
            return "success";
        } else if (result.getModifiedCount() == 0) {
            return "success";
        } else {
            return "fail";
        }
    }

    @Override
    public String batchMarkRiskV2(BatchRiskV2OperatorDto batchRiskV2OperatorDto) {
        Update update = new Update();
        update.set("riskMark", batchRiskV2OperatorDto.getRiskMark());
        UpdateResult result = null;
        if (batchRiskV2OperatorDto.getIds() != null) {
            result = mongoTemplate.updateMulti(new Query().addCriteria(Criteria.where("_id").in(batchRiskV2OperatorDto.getIds())), update, collectionName);
        } else {
            RiskV2OperatorDto riskV2OperatorDto = batchRiskV2OperatorDto.getRiskV2OperatorDto();
            Criteria criteria = getCriteria(riskV2OperatorDto);
            result = mongoTemplate.updateMulti(new Query().addCriteria(criteria), update, collectionName);
        }
        if (result.getModifiedCount() != 0) {
            return "success";
        } else if (result.getModifiedCount() == 0) {
            return "success";
        } else {
            return "fail";
        }
    }

    @Override
    public Long totalCount(RiskV2OperatorDto riskV2OperatorDto) {
        return mongoTemplate.count(new Query().addCriteria(getCriteria(riskV2OperatorDto)), collectionName);
    }

    @Override
    public Long totalCount(String aggRiskId) {
        return mongoTemplate.count(new Query().addCriteria(Criteria.where("aggRiskId").is(aggRiskId)), collectionName);
    }

    @Override
    public Criteria getCriteria(RiskV2OperatorDto riskV2OperatorDto) {
        Criteria criteria = new Criteria();
        List<Criteria> andCriteriaList = new ArrayList<>();
        if (DataUtil.isNotEmpty(riskV2OperatorDto)) {
            if (DataUtil.isNotEmpty(riskV2OperatorDto.getHost())) {
                if (DataUtil.isNotEmpty(riskV2OperatorDto.getHostIgnoreCase()) && riskV2OperatorDto.getHostIgnoreCase()) {
                    Pattern pattern = Pattern.compile(DataUtil.regexStrEscape(riskV2OperatorDto.getHost()), Pattern.CASE_INSENSITIVE);
                    criteria.and("host").regex(pattern);
                } else {
                    criteria.and("host").regex(DataUtil.regexStrEscape(riskV2OperatorDto.getHost()));
                }
            }
            if (com.quanzhi.audit_core.common.utils.DataUtil.isNotEmpty(riskV2OperatorDto.getNodeId())) {
                criteria.and("nodes.nid").is(riskV2OperatorDto.getNodeId());
            }
            if (com.quanzhi.audit_core.common.utils.DataUtil.isNotEmpty(riskV2OperatorDto.getAggRiskId())) {
                criteria.and("aggRiskId").is(riskV2OperatorDto.getAggRiskId());
            }
            if (DataUtil.isNotEmpty(riskV2OperatorDto.getAppName())) {
                criteria.and("appName")
                        .regex(DataUtil.regexStrEscape(riskV2OperatorDto.getAppName()));
            }
            if (DataUtil.isNotEmpty(riskV2OperatorDto.getApiOrAppUri())) {
                criteria.and("apiUrl").regex(DataUtil.regexStrEscape(riskV2OperatorDto.getApiOrAppUri()));
            }
            if (DataUtil.isNotEmpty(riskV2OperatorDto.getOperationId())) {
                criteria.and("operationId")
                        .regex(DataUtil.regexStrEscape(riskV2OperatorDto.getOperationId()));
            }
            if (DataUtil.isNotEmpty(riskV2OperatorDto.getName())) {
                criteria.and("policySnapshot.name")
                        .in(riskV2OperatorDto.getName());
            }
            if (DataUtil.isNotEmpty(riskV2OperatorDto.getIds())) {
                criteria.and("_id")
                        .in(riskV2OperatorDto.getIds());
            }
            if (DataUtil.isNotEmpty(riskV2OperatorDto.getType())) {
                criteria.and("policySnapshot.group")
                        .in(riskV2OperatorDto.getType());
            }
            if (DataUtil.isNotEmpty(riskV2OperatorDto.getLevel())) {
                criteria.and("level")
                        .in(riskV2OperatorDto.getLevel());
            }
            if (DataUtil.isNotEmpty(riskV2OperatorDto.getState())) {
                criteria.and("state")
                        .in(riskV2OperatorDto.getState());
            }
            if (DataUtil.isNotEmpty(riskV2OperatorDto.getRiskMark())) {
                criteria.and("riskMark")
                        .is(riskV2OperatorDto.getRiskMark());
            }
            if (DataUtil.isNotEmpty(riskV2OperatorDto.getEntityValue())) {
                criteria.and("entities.value")
                        .regex(riskV2OperatorDto.getEntityValue());
            }
            if (DataUtil.isNotEmpty(riskV2OperatorDto.getEntitiesType())) {
                criteria.and("policySnapshot.type")
                        .is(riskV2OperatorDto.getEntitiesType());
            }
            if (DataUtil.isNotEmpty(riskV2OperatorDto.getPolicyId())) {
                criteria.and("policySnapshot._id")
                        .regex(riskV2OperatorDto.getPolicyId());
            }
            if (DataUtil.isNotEmpty(riskV2OperatorDto.getFirstTimeStart()) && DataUtil.isNotEmpty(riskV2OperatorDto.getFirstTimeEnd())) {
                Criteria andCriteria = new Criteria();
                andCriteria.andOperator(Criteria.where("firstTime").gte(riskV2OperatorDto.getFirstTimeStart()), Criteria.where("firstTime").lte(riskV2OperatorDto.getFirstTimeEnd()));
                andCriteriaList.add(andCriteria);
            }
            if (DataUtil.isNotEmpty(riskV2OperatorDto.getLastTimeStart()) && DataUtil.isNotEmpty(riskV2OperatorDto.getLastTimeEnd())) {
                Criteria andCriteria = new Criteria();
                andCriteria.andOperator(Criteria.where("lastTime").gte(riskV2OperatorDto.getLastTimeStart()), Criteria.where("lastTime").lte(riskV2OperatorDto.getLastTimeEnd()));
                andCriteriaList.add(andCriteria);
            }
        }
        if (!andCriteriaList.isEmpty()) {
            criteria.andOperator(andCriteriaList.toArray(new Criteria[andCriteriaList.size()]));
        }
        queryAdapterRegistry.module(Module.RISK).query(riskV2OperatorDto, criteria);
        return criteria;
    }

    @Override
    public String dealRiskV2(RiskV2OperatorDto riskV2OperatorDto) {
        Update update = new Update();
        update.set("state", riskV2OperatorDto.getState().get(0));
        update.set("operateName", riskV2OperatorDto.getOperateName());
        update.set("operateTime", System.currentTimeMillis());
        if (DataUtil.isNotEmpty(riskV2OperatorDto.getRemark())) {
            update.set("remark", riskV2OperatorDto.getRemark());
        } else {
            update.set("remark", "");
        }
        UpdateResult result = mongoTemplate.updateFirst(new Query().addCriteria(Criteria.where("_id").is(riskV2OperatorDto.getId())), update, collectionName);
        if (result.getModifiedCount() != 0) {
            return "success";
        } else if (result.getModifiedCount() == 0) {
            return "success";
        } else {
            return "fail";
        }
    }

    @Override
    public String batchDealRiskV2(BatchRiskV2OperatorDto batchRiskV2OperatorDto) {
        Update update = new Update();
        update.set("state", batchRiskV2OperatorDto.getState().get(0));
        update.set("operateName", batchRiskV2OperatorDto.getOperateName());
        update.set("operateTime", System.currentTimeMillis());
        if (DataUtil.isNotEmpty(batchRiskV2OperatorDto.getRemark())) {
            update.set("remark", batchRiskV2OperatorDto.getRemark());
        } else {
            update.set("remark", "");
        }
        UpdateResult result = null;
        if (DataUtil.isNotEmpty(batchRiskV2OperatorDto.getAggRiskId())) {
            result = mongoTemplate.updateMulti(new Query().addCriteria(Criteria.where("aggRiskId").is(batchRiskV2OperatorDto.getAggRiskId())), update, collectionName);
        } else {
            RiskV2OperatorDto riskV2OperatorDto = batchRiskV2OperatorDto.getRiskV2OperatorDto();
            Criteria criteria = getCriteria(riskV2OperatorDto);
            result = mongoTemplate.updateMulti(new Query().addCriteria(criteria), update, collectionName);
        }
        if (result.getModifiedCount() != 0) {
            return "success";
        } else if (result.getModifiedCount() == 0) {
            return "success";
        } else {
            return "fail";
        }
    }

    @Override
    public String ignoreByEntityValue(String value, String remark, String operateName) {
        Update update = new Update();
        update.set("state", 1);
        update.set("operateName", operateName);
        update.set("operateTime", System.currentTimeMillis());
        if (DataUtil.isNotEmpty(remark)) {
            update.set("remark", remark);
        } else {
            update.set("remark", "");
        }
        UpdateResult result = mongoTemplate.updateFirst(new Query().addCriteria(Criteria.where("entities.value").is(value)), update, collectionName);
        if (result.getModifiedCount() != 0) {
            return "success";
        } else if (result.getModifiedCount() == 0) {
            return "success";
        } else {
            return "fail";
        }
    }

    @Override
    public List<RiskInfo> findByEntityValues(List<String> values) {
        Criteria criteria = Criteria.where("entities").size(values.size()).and("entities.value").all(values);
        return mongoTemplate.find(new Query().addCriteria(criteria), RiskInfo.class, collectionName);
    }

    @Override
    public RiskInfo findById(String id) {
        return mongoTemplate.findOne(new Query().addCriteria(Criteria.where("_id").is(id)), RiskInfo.class, collectionName);
    }

    @Override
    public List<RiskInfo> findByIds(List<String> ids) {
        return mongoTemplate.find(new Query().addCriteria(Criteria.where("_id").in(ids)), RiskInfo.class, collectionName);
    }

    @Override
    public List<RiskInfo> selectIpRisk(Integer count, Integer limit) {
        return mongoTemplate.find(new Query().addCriteria(Criteria.where("entities.type").is("IP").and("state").in(Arrays.asList(0, 2))).skip(count).limit(limit), RiskInfo.class, collectionName);
    }

    @Override
    public List<RiskInfo> selectAccountRisk(Integer count, Integer limit) {
        return mongoTemplate.find(new Query().addCriteria(Criteria.where("entities.type").is("ACCOUNT").and("state").in(Arrays.asList(0, 2))).skip(count).limit(limit), RiskInfo.class, collectionName);
    }

    @Override
    public List<RiskInfo> selectApiRisk(Integer count, Integer limit) {
        return mongoTemplate.find(new Query().addCriteria(Criteria.where("entities.type").is("API").and("state").in(Arrays.asList(0, 2))).skip(count).limit(limit), RiskInfo.class, collectionName);
    }

    @Override
    public List<RiskInfo> selectAppRisk(Integer count, Integer limit) {
        return mongoTemplate.find(new Query().addCriteria(Criteria.where("entities.type").is("APP").and("state").in(Arrays.asList(0, 2))).skip(count).limit(limit), RiskInfo.class, collectionName);
    }

    @Override
    public void updateAggRiskId(String id, String aggRiskId) {
        Update update = new Update();
        update.set("aggRiskId", aggRiskId);
        mongoTemplate.updateFirst(new Query().addCriteria(Criteria.where("_id").is(id)), update, collectionName);
    }

}
