package com.quanzhi.auditapiv2.common.risk.mb;

import org.mybatis.spring.SqlSessionTemplate;
import org.springframework.context.annotation.Configuration;

/**
 * @Author: HaoJun
 * @Date: 2021/1/8 10:54 上午
 */
@Configuration
public class TypeHandlerRegistry {
    public TypeHandlerRegistry(SqlSessionTemplate sqlSessionTemplate) {
        sqlSessionTemplate.getConfiguration().getTypeHandlerRegistry().register(ListArrayTypeHandler.class);
    }
}
