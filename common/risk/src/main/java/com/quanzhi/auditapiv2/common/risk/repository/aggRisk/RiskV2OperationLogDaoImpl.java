package com.quanzhi.auditapiv2.common.risk.repository.aggRisk;

import com.quanzhi.auditapiv2.core.risk.repository.aggRisk.RiskV2OperationLogDao;
import com.quanzhi.auditapiv2.common.dal.dao.base.impl.BaseDaoImpl;
import com.quanzhi.auditapiv2.common.dal.entity.aggRisk.RiskV2OperationLog;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * @Author: yangzx
 * @Date: 2024/10/9 14:38
 */
@Repository
public class RiskV2OperationLogDaoImpl extends BaseDaoImpl<RiskV2OperationLog> implements RiskV2OperationLogDao {

    private final MongoTemplate mongoTemplate;

    private final String collectionName = "riskInfoV2OperationLog";

    public RiskV2OperationLogDaoImpl(MongoTemplate mongoTemplate) {
        this.mongoTemplate = mongoTemplate;
    }


    @Override
    public void insertLog(RiskV2OperationLog riskV2OperationLog) {
        mongoTemplate.save(riskV2OperationLog, collectionName);
    }

    @Override
    public void batchInsertLog(List<RiskV2OperationLog> riskV2OperationLogs) {
        mongoTemplate.insertAll(riskV2OperationLogs);
    }

    @Override
    public List<RiskV2OperationLog> listLog(String riskId) {
        return mongoTemplate.find(new Query().addCriteria(Criteria.where("riskId").is(riskId)), RiskV2OperationLog.class, collectionName);
    }

}
