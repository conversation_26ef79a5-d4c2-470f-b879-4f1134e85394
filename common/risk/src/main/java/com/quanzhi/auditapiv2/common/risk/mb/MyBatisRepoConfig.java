package com.quanzhi.auditapiv2.common.risk.mb;

import com.baomidou.mybatisplus.annotation.DbType;
import com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;



/**
 * @Author: HaoJun
 * @Date: 2020/12/30 1:58 下午
 */
@Configuration
@MapperScan("com.quanzhi.auditapiv2.common.risk.mapper")
public class MyBatisRepoConfig {

    @Bean
    public MybatisPlusInterceptor paginationInterceptor() {
        MybatisPlusInterceptor interceptor = new MybatisPlusInterceptor();
        interceptor.addInnerInterceptor(new CustomPaginationInnerInterceptor(DbType.CLICK_HOUSE));
        return interceptor;
    }
}
