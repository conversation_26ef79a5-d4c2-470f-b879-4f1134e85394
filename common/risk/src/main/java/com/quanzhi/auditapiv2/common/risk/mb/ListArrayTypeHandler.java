package com.quanzhi.auditapiv2.common.risk.mb;

import org.apache.ibatis.type.BaseTypeHandler;
import org.apache.ibatis.type.JdbcType;
import org.apache.ibatis.type.MappedJdbcTypes;
import org.apache.ibatis.type.MappedTypes;

import java.sql.*;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * @Author: HaoJun
 * @Date: 2021/1/7 5:11 下午
 */
@MappedJdbcTypes({JdbcType.ARRAY})
@MappedTypes({List.class})
public class ListArrayTypeHandler extends BaseTypeHandler<List<Object>> {

    @Override
    public void setNonNullParameter(PreparedStatement ps, int i,
                                    List<Object> parameter, JdbcType jdbcType) throws SQLException {
        //  JDBC type is required
        Array array = ps.getConnection().createArrayOf("java.util.List", parameter.toArray());
        try {
            ps.setArray(i, array);
        } finally {
            array.free();
        }
    }

    @Override
    public List<Object> getNullableResult(ResultSet rs, String columnName) throws SQLException {
        return extractArray(rs.getArray(columnName));
    }

    @Override
    public List<Object> getNullableResult(ResultSet rs, int columnIndex) throws SQLException {
        return extractArray(rs.getArray(columnIndex));
    }

    @Override
    public List<Object> getNullableResult(CallableStatement cs, int columnIndex) throws SQLException {
        return extractArray(cs.getArray(columnIndex));
    }

    protected List<Object> extractArray(Array array) throws SQLException {
        if (array == null) {
            return null;
        }
        Object javaArray = array.getArray();
        array.free();
        if (javaArray instanceof String[]) {
            return new ArrayList<>(Arrays.asList((String[]) javaArray));
        } else if (javaArray instanceof String[][]) {
            List<Object> list = new ArrayList<>();
            String[][] ar2 = (String[][]) javaArray;
            for (String[] ar1 : ar2) {
                List<String> childList = new ArrayList<>();
                for (String str : ar1) {
                    childList.add(str);
                }
                list.add(childList);
            }
            return list;
        } else {
            throw new IllegalStateException("UnExpected data.");
        }
    }
}
