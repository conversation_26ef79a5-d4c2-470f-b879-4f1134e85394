<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <groupId>com.quanzhi.auditapiv2</groupId>
        <artifactId>auditapiv2</artifactId>
        <version>3.3.0-SNAPSHOT</version>
        <relativePath>../../pom.xml</relativePath>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>auditapiv2-common-dal</artifactId>
    <packaging>jar</packaging>
    <name>auditapiv2-common-dal</name>

    <dependencies>
        <dependency>
            <groupId>com.quanzhi.auditapiv2</groupId>
            <artifactId>auditapiv2-common-util</artifactId>
        </dependency>
        <dependency>
            <groupId>com.alibaba.boot</groupId>
            <artifactId>nacos-config-spring-boot-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>com.github.xiaoymin</groupId>
            <artifactId>knife4j-spring-boot-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>com.quanzhi.re</groupId>
            <artifactId>ruleEngine-repository-mongo</artifactId>
            <version>1.0.0-SNAPSHOT</version>
        </dependency>

        <dependency>
            <groupId>net.sf.json-lib</groupId>
            <artifactId>json-lib</artifactId>
            <classifier>jdk15</classifier>
        </dependency>
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>com.quanzhi.metabase</groupId>
            <artifactId>metabase-hyper-client</artifactId>
        </dependency>
        <!-- https://mvnrepository.com/artifact/redis.clients/jedis -->
        <dependency>
            <groupId>redis.clients</groupId>
            <artifactId>jedis</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>org.apache.commons</groupId>
                    <artifactId>commons-pool2</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-pool2</artifactId>
        </dependency>
        <dependency>
            <groupId>com.quanzhi.audit.mix</groupId>
            <artifactId>audit-mix-match</artifactId>
        </dependency>
        <dependency>
            <groupId>com.quanzhi.dlp4j</groupId>
            <artifactId>dlp4j-core</artifactId>
        </dependency>
        <dependency>
            <groupId>com.quanzhi.audit.mix</groupId>
            <artifactId>audit-mix-schedule</artifactId>
        </dependency>
        <dependency>
            <groupId>org.mongodb</groupId>
            <artifactId>mongodb-driver-sync</artifactId>
        </dependency>
        <dependency>
            <groupId>com.quanzhi</groupId>
            <artifactId>awdb-core</artifactId>
        </dependency>
        <dependency>
            <groupId>com.quanzhi.login</groupId>
            <artifactId>login</artifactId>
        </dependency>
        <dependency>
            <groupId>com.quanzhi.audit.mix</groupId>
            <artifactId>audit-permission</artifactId>
        </dependency>
        <dependency>
            <groupId>com.java3y.austin</groupId>
            <artifactId>austin-service-api</artifactId>
        </dependency>
    </dependencies>

    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
            </plugin>
        </plugins>
    </build>
</project>
