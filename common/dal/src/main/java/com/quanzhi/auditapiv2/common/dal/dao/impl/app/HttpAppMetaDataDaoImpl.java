package com.quanzhi.auditapiv2.common.dal.dao.impl.app;

import com.quanzhi.auditapiv2.common.dal.dao.app.HttpAppMetaDataDao;
import com.quanzhi.auditapiv2.common.dal.dao.base.impl.MetabaseDaoImpl;
import com.quanzhi.metabase.core.model.http.app.HttpAppMetaData;
import org.springframework.stereotype.Repository;

/**
 * <AUTHOR>
 * @date 2022/7/26 2:44 下午
 */
@Repository
public class HttpAppMetaDataDaoImpl extends MetabaseDaoImpl<HttpAppMetaData>  implements HttpAppMetaDataDao {
    @Override
    public HttpAppMetaData findByUri(String uri) {
        return metabaseClientTemplate.findByUri(uri, HttpAppMetaData.class);
    }
}
