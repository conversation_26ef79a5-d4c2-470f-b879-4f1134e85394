package com.quanzhi.auditapiv2.common.dal.dao.impl;

import com.quanzhi.auditapiv2.common.dal.dao.IEmailConfigNacosDao;
import com.quanzhi.auditapiv2.common.dal.dao.base.impl.NacosBaseDaoImpl;
import com.quanzhi.auditapiv2.common.util.entity.EmailConfig;
import org.springframework.stereotype.Repository;

/**
 * 
 * 《邮箱配置持久层Nacos接口实现》
 * 
 * 
 * @Project: 
 * @Module ID:
 * @Comments: 
 * @JDK version used:      <JDK1.8> 
 * <AUTHOR> [<EMAIL>]
 * @since 2020-04-08-上午9:56:10
 */
@Repository
public class EmailConfigNacosDaoImpl extends NacosBaseDaoImpl<EmailConfig> implements IEmailConfigNacosDao {
}
