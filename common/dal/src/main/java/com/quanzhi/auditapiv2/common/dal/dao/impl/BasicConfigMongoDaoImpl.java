package com.quanzhi.auditapiv2.common.dal.dao.impl;


import com.quanzhi.auditapiv2.common.dal.dao.IBasicConfigMongoDao;
import com.quanzhi.auditapiv2.common.dal.dao.base.impl.BaseDaoImpl;
import com.quanzhi.auditapiv2.common.dal.entity.BasicConfig;
import org.springframework.stereotype.Repository;

/**
 * <AUTHOR>
 * create at 2021/6/10 2:49 下午
 * @description:
 **/
@Repository
public class BasicConfigMongoDaoImpl extends BaseDaoImpl<BasicConfig> implements IBasicConfigMongoDao {

}