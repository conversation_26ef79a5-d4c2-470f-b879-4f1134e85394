package com.quanzhi.auditapiv2.common.dal.dto.filter;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
@ApiModel("资产过滤规则批量操作对象")
public class AssetFilterCriteriaDTO implements Serializable {
    @ApiModelProperty("勾选id集合")
    private List<String> ids;

    @ApiModelProperty("批量操作动作")
    private Target target;

    @ApiModelProperty("是否全选")
    private Boolean complete;

    @ApiModelProperty("筛选条件")
    private AssetFilterDTO assetFilterDTO;

    public enum Target {
        OPEN, CLOSE, DELETE
    }

}
