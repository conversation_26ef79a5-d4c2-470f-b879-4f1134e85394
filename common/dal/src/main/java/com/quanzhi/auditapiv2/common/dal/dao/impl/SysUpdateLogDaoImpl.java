package com.quanzhi.auditapiv2.common.dal.dao.impl;

import com.quanzhi.auditapiv2.common.dal.dao.ISysUpdateLogDao;
import com.quanzhi.auditapiv2.common.dal.dao.base.impl.BaseDaoImpl;
import com.quanzhi.auditapiv2.common.dal.entity.SysUpdateLog;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.stereotype.Repository;

import java.lang.reflect.Field;
import java.util.List;

/**
 *
 * 《系统升级日志持久层接口实现》
 *
 *
 * @Project:
 * @Module ID:
 * @Comments:
 * @JDK version used:      <JDK1.8>
 * <AUTHOR> [<EMAIL>]
 * @since 2020-04-08-上午9:56:10
 */
@Repository
public class SysUpdateLogDaoImpl extends BaseDaoImpl<SysUpdateLog> implements ISysUpdateLogDao {

    @Autowired
    private MongoTemplate mongoTemplate;

    private final String collectionName = "sysUpdateLog";

    /**
     * 查询系统升级日志列表(分页)
     * @Comments:  <对此方法的描述，可以引用系统设计中的描述>
     * <AUTHOR> [<EMAIL>]
     * @since 2020-04-08 9:56
     * @param
     * @return
     */
    @Override
    public List<SysUpdateLog> selectSysUpdateLogList(Integer page, Integer limit) throws Exception {
        //排序
        Sort sort = Sort.by(Sort.Direction.DESC, "createDate");
        return mongoTemplate.find(new Query(new Criteria()).skip((page-1) * limit).limit(limit).with(sort), SysUpdateLog.class, collectionName);
    }

    /**
     * 查询系统升级日志数量
     * @Comments:  <对此方法的描述，可以引用系统设计中的描述>
     * <AUTHOR> [<EMAIL>]
     * @since 2020-04-08 9:56
     * @param
     * @return
     */
    @Override
    public Long totalCount() throws Exception {

        return mongoTemplate.count(new Query(new Criteria()), collectionName);
    }

    /**
     * id查询系统升级日志详情
     * @Comments:  <对此方法的描述，可以引用系统设计中的描述>
     * <AUTHOR> [<EMAIL>]
     * @since 2020-04-08 9:56
     * @param
     * @return
     */
    @Override
    public SysUpdateLog selectSysUpdateLogById(String id) throws Exception {

        //查询条件
        Criteria criteria = Criteria.where("_id").is(id);

        return mongoTemplate.findOne(new Query(criteria), SysUpdateLog.class, collectionName);
    }

    /**
     * 查询进行中状态升级日志
     * @Comments:  <对此方法的描述，可以引用系统设计中的描述>
     * <AUTHOR> [<EMAIL>]
     * @since 2020-04-08 9:56
     * @param
     * @return
     */
    @Override
    public SysUpdateLog selectSysUpdateLog() throws Exception {

        //查询条件
        Criteria criteria = new Criteria();
        criteria.orOperator(Criteria.where("updateStatus").is(SysUpdateLog.StatusEnum.ONGOING.status()), Criteria.where("rollbackStatus").is(SysUpdateLog.StatusEnum.ONGOING.status()));

        return mongoTemplate.findOne(new Query(criteria), SysUpdateLog.class, collectionName);
    }

    /**
     * 新增系统升级日志
     * @Comments:  <对此方法的描述，可以引用系统设计中的描述>
     * <AUTHOR> [<EMAIL>]
     * @since 2020-04-08 9:56
     * @param
     * @return
     */
    @Override
    public SysUpdateLog insertSysUpdateLog(SysUpdateLog sysUpdateLog) throws Exception {

        return mongoTemplate.insert(sysUpdateLog, collectionName);
    }

    /**
     * 编辑系统升级日志
     * @Comments:  <对此方法的描述，可以引用系统设计中的描述>
     * <AUTHOR> [<EMAIL>]
     * @since 2020-04-08 9:56
     * @param
     * @return
     */
    @Override
    public SysUpdateLog updateSysUpdateLog(SysUpdateLog sysUpdateLog) throws Exception {

        //查询条件
        Criteria criteria = Criteria.where("_id").is(sysUpdateLog.getId());

        Update update = new Update();
        Field[] field = sysUpdateLog.getClass().getDeclaredFields();
        for(int i=0; i<field.length; i++){

            //设置是否允许访问，不是修改原来的访问权限修饰词。
            field[i].setAccessible(true);
            //字段值不为空 放入map
            if(field[i].get(sysUpdateLog) != null) {
                update.set(field[i].getName(), field[i].get(sysUpdateLog));
            }
        }
        mongoTemplate.upsert(new Query(criteria), update, SysUpdateLog.class, collectionName);

        return sysUpdateLog;
    }
}
