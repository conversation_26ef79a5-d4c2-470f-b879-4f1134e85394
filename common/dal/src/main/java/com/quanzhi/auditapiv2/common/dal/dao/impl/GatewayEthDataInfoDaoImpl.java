package com.quanzhi.auditapiv2.common.dal.dao.impl;

import com.quanzhi.auditapiv2.common.dal.dao.IGatewayEthDataInfoDao;
import com.quanzhi.auditapiv2.common.dal.dao.base.impl.BaseDaoImpl;
import com.quanzhi.auditapiv2.common.dal.entity.GatewayEthDataInfo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.aggregation.*;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Repository;

import javax.swing.text.Document;
import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @date 2023/9/12 15:55
 */
@Repository("GatewayEthDataInfoDao")
public class GatewayEthDataInfoDaoImpl  extends BaseDaoImpl<GatewayEthDataInfo> implements IGatewayEthDataInfoDao {

    @Autowired
    MongoTemplate mongoTemplate;

    private static String collectionName = "gatewayEthDataInfo";

    @Override
    public List<GatewayEthDataInfo> selectListByItem(Long start, Long end, String gatewayIp, String ethName) {
        return mongoTemplate.find(new Query(new Criteria().and("gatewayIp").is(gatewayIp)
                .and("ethName").is(ethName)
                .andOperator(Criteria.where("time").gte(start),Criteria.where("time").lte(end)))
                .with(Sort.by(Sort.Order.asc("time"))), GatewayEthDataInfo.class,collectionName);
    }

    @Override
    public List<GatewayEthDataInfo> groupGatewayIpAndMaxTimeAndEthName() {
        Criteria criteria = Criteria.where("time").gte(System.currentTimeMillis() - 5 * 60 * 1000);
        MatchOperation match = Aggregation.match(criteria);
        // 1. Sort stage
        SortOperation sortOperation = Aggregation.sort(Sort.Direction.DESC, "time");

        // 2. Group stage
        GroupOperation groupOperation = Aggregation.group("ethName", "gatewayIp")
                .first("time").as("time")
                .first("rxSpeed").as("rxSpeed")
                .first("$$ROOT").as("originalDoc");

        // 3. Project stage
        ProjectionOperation projectionOperation = Aggregation.project()
                .and("_id.ethName").as("ethName")
                .and("_id.gatewayIp").as("gatewayIp")
                .andInclude("time", "rxSpeed")
                .andExclude("_id");

        // Build the aggregation pipeline
        Aggregation aggregation = Aggregation.newAggregation(
                match,
                sortOperation,
                groupOperation,
                projectionOperation
        );

        // Execute the aggregation
        AggregationResults<GatewayEthDataInfo> results = mongoTemplate.aggregate(
                aggregation,
                "gatewayEthDataInfo",
                GatewayEthDataInfo.class
        );

        return results.getMappedResults();
    }
}
