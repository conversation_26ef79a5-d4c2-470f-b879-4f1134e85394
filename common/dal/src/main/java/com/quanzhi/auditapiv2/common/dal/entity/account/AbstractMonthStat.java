package com.quanzhi.auditapiv2.common.dal.entity.account;

import lombok.Data;

import java.util.*;

@Data
public abstract class AbstractMonthStat {
    private String id;
    private String month;
    private Map<String, DateStat> dateStats = new HashMap<>();
    private Map<String, NodeDateStat> nodeDateStats;
    private Long updateTime;
    private Set<String> uaTypes;
    private Set<String> rspDataLabelList;
    private Set<String> reqDataLabelList;
    private Set<String> relatedAccountList;
    private Set<String> appUriList;
    private Map<String, Set<String>> uaByUaType;
    private Date createDate;

    public abstract String getKey();

    public Map<String, DateStat> getAllDateStats() {
        if (nodeDateStats == null || nodeDateStats.isEmpty()) {
            return dateStats == null ? Collections.emptyMap() : dateStats;
        }
        Map<String, DateStat> allDateStats = new HashMap<>(dateStats == null ? Collections.emptyMap() : dateStats);
        for (Map.Entry<String, NodeDateStat> nodeDateStatEntry : nodeDateStats.entrySet()) {
            for (Map.Entry<String, DateStat> dateStatEntry : nodeDateStatEntry.getValue().getDateStats().entrySet()) {
                DateStat dateStat = allDateStats.get(dateStatEntry.getKey());
                if (dateStat == null) {
                    DateStat d = new DateStat();
                    d.setAmount(dateStatEntry.getValue().getAmount());
                    allDateStats.put(dateStatEntry.getKey(), d);
                } else {
                    dateStat.setAmount(dateStat.getAmount() + dateStatEntry.getValue().getAmount());
                }
            }
        }
        return allDateStats;
    }

    @Data
    public static final class NodeDateStat {
        private Map<String, DateStat> dateStats;
    }
}
