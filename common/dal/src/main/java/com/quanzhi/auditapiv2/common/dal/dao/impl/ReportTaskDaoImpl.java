package com.quanzhi.auditapiv2.common.dal.dao.impl;

import com.quanzhi.auditapiv2.common.dal.dao.IReportTaskDao;
import com.quanzhi.auditapiv2.common.dal.entity.ReportConfig;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Repository;

@Repository
public class ReportTaskDaoImpl implements IReportTaskDao {

    @Autowired
    private MongoTemplate mongoTemplate;

    @Override
    public ReportConfig findByName(String name) {
        Query query = new Query();
        query.addCriteria(Criteria.where("name").is(name));
        return mongoTemplate.findOne(query, ReportConfig.class, "reportTask");
    }
}
