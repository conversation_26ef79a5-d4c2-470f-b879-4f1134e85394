package com.quanzhi.auditapiv2.common.dal.dao.impl.api;

import com.quanzhi.auditapiv2.common.dal.dao.IIndexDefine;
import com.quanzhi.auditapiv2.common.dal.dao.IUrlStructureDao;
import com.quanzhi.auditapiv2.common.dal.dao.base.impl.BaseDaoImpl;
import com.quanzhi.auditapiv2.common.dal.dao.common.BatchUpdateOption;
import com.quanzhi.auditapiv2.common.dal.entity.UrlStructure;
import org.springframework.data.mongodb.core.aggregation.Aggregation;
import org.springframework.data.mongodb.core.aggregation.TypedAggregation;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 只是mock UrlStructureDaoImpl 避免报错无法启动，不要使用
 */
@Repository
@Deprecated
public class MockUrlStructureDao extends BaseDaoImpl<UrlStructure> implements IUrlStructureDao, IIndexDefine {
    @Override
    public void createIndex() {

    }

    @Override
    public void urlStructureTempory2UrlStructure() {

    }

    @Override
    public void dropCollection() {

    }

    @Override
    public void batchUpdateByOptionInUrlStructure(List<BatchUpdateOption> options) {

    }

    @Override
    public void batchUpdateByOption(List<BatchUpdateOption> options, Boolean updateSingleApp) {

    }

    @Override
    public <T> List<T> aggregateQuery(TypedAggregation aggregation, Class<T> entityClass) {
        return null;
    }

    @Override
    public <T> List<T> aggregate(Aggregation aggregation, Class<T> entityClass, String collection) {
        return null;
    }
}
