package com.quanzhi.auditapiv2.common.dal.dao;

import com.quanzhi.auditapiv2.common.dal.dto.VariableQueryDTO;
import com.quanzhi.auditapiv2.common.util.utils.ConstantUtil;
import com.quanzhi.auditapiv2.common.util.utils.DataUtil;
import com.quanzhi.re.core.defaults.VariableDefault;
import com.quanzhi.re.core.domain.entity.po.VariablePO;
import com.quanzhi.re.core.domain.entity.variable.Period;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.regex.Pattern;

@Repository
public class VariableDao {

    private final MongoTemplate mongoTemplate;

    public VariableDao(MongoTemplate mongoTemplate) {
        this.mongoTemplate = mongoTemplate;
    }

    public List<VariablePO> find(VariableQueryDTO queryDTO) {
        Query query = buildQuery(queryDTO);
        if (DataUtil.isNotEmpty(queryDTO.getPage()) && DataUtil.isNotEmpty(queryDTO.getLimit())) {
            if (queryDTO.getLimit() > 1000) {
                throw new IllegalArgumentException("More than 1000 pieces of data are obtained per page");
            }
            query.skip((queryDTO.getPage() - 1) * queryDTO.getLimit());
            query.limit(queryDTO.getLimit());
        }
        // 排序
        if (DataUtil.isNotEmpty(queryDTO.getField()) && DataUtil.isNotEmpty(queryDTO.getSort())) {
            if (queryDTO.getSort() == ConstantUtil.Sort.ASC) {
                query.with(Sort.by(Sort.Direction.ASC, queryDTO.getField()));
            } else if (queryDTO.getSort() == ConstantUtil.Sort.DESC) {
                query.with(Sort.by(Sort.Direction.DESC, queryDTO.getField()));
            }
        } else {
            // 新增和复制的放在最前面，但更新的不会到最前面
            query.with(Sort.by(Sort.Direction.DESC, "createTime"));
        }
        return mongoTemplate.find(query, VariablePO.class, VariableDefault.TABLE_NAME);
    }

    public long count(VariableQueryDTO queryDTO) {
        Query query = buildQuery(queryDTO);
        return mongoTemplate.count(query, VariableDefault.TABLE_NAME);
    }

    private Query buildQuery(VariableQueryDTO queryDTO) {
        Query query = new Query();
        if (DataUtil.isNotEmpty(queryDTO.getTitle())) {
            query.addCriteria(Criteria.where("title").regex(Pattern.quote(queryDTO.getTitle())));
        }
        if (DataUtil.isNotEmpty(queryDTO.getCategory())) {
            query.addCriteria(Criteria.where("category").is(queryDTO.getCategory()));
        }
        if (DataUtil.isNotEmpty(queryDTO.getName())) {
            query.addCriteria(Criteria.where("name").regex(Pattern.quote(queryDTO.getName())));
        }
        if (DataUtil.isNotEmpty(queryDTO.getSubjectObj()) && DataUtil.isNotEmpty(queryDTO.getUnSubjectObj())) {
            Criteria criteria = new Criteria().andOperator(
                    Criteria.where("subjectObj").is(queryDTO.getSubjectObj()),
                    Criteria.where("subjectObj").nin(queryDTO.getUnSubjectObj())
            );
            query.addCriteria(criteria);
        } else {
            if (DataUtil.isNotEmpty(queryDTO.getSubjectObj())) {
                query.addCriteria(Criteria.where("subjectObj").is(queryDTO.getSubjectObj()));
            }
            if (DataUtil.isNotEmpty(queryDTO.getUnSubjectObj())) {
                query.addCriteria(Criteria.where("subjectObj").nin(queryDTO.getUnSubjectObj()));
            }
        }
        if (DataUtil.isNotEmpty(queryDTO.getEnabled())) {
            query.addCriteria(Criteria.where("enabled").is(queryDTO.getEnabled()));
        }
        if (DataUtil.isNotEmpty(queryDTO.getHasBaseline())) {
            query.addCriteria(Criteria.where("hasBaseline").is(queryDTO.getHasBaseline()));
        }
        if (DataUtil.isNotEmpty(queryDTO.getPeriod())) {
            query.addCriteria(Criteria.where("period.unit").is(queryDTO.getPeriod().getUnit())
                    .and("period.time").is(queryDTO.getPeriod().getTime()));
        }
        return query;
    }

    public VariablePO findById(String id) {
        return mongoTemplate.findOne(Query.query(Criteria.where("_id").is(id)), VariablePO.class,
                VariableDefault.TABLE_NAME);
    }

    public List<VariablePO> findByPeriod(Period period, String entity) {
        return mongoTemplate.find(new Query().addCriteria(Criteria.where("time").is(period.getTime()).and("unit")
                .is(period.getUnit()).and("subjectObj").is(entity)), VariablePO.class, VariableDefault.TABLE_NAME);
    }

    public List<VariablePO> findByCategory(String category) {
        return mongoTemplate.find(new Query().addCriteria(Criteria.where("category").is(category)), VariablePO.class,
                VariableDefault.TABLE_NAME);
    }

}
