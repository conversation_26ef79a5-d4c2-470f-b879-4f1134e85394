package com.quanzhi.auditapiv2.common.dal.dao.impl;

import com.quanzhi.auditapiv2.common.dal.dao.IImportAppErrorLogDao;
import com.quanzhi.auditapiv2.common.dal.dao.base.impl.BaseDaoImpl;
import com.quanzhi.auditapiv2.common.dal.entity.ImportAppErrorLog;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 *
 * 《应用信息导入日志持久层接口》
 *
 *
 * @Project:
 * @Module ID:
 * @Comments:
 * @JDK version used:      <JDK1.8> 
 * <AUTHOR> [<EMAIL>]
 * @since 2021-09-06-上午9:56:10
 */
@Repository
public class ImportAppErrorLogDaoImpl extends BaseDaoImpl<ImportAppErrorLog> implements IImportAppErrorLogDao {

    @Autowired
    private MongoTemplate mongoTemplate;

    private final String collectionName = "importAppErrorLog";

    /**
     * 查询应用信息导入日志列表(分页)
     * @Comments:  <对此方法的描述，可以引用系统设计中的描述>
     * <AUTHOR> [<EMAIL>]
     * @since 2021-09-06 9:56
     * @param
     * @return
     */
    @Override
    public List<ImportAppErrorLog> selectImportAppErrorLogList(Integer page, Integer limit) throws Exception {

        //分页
        Pageable pageable = PageRequest.of(page - 1, limit);
        //排序
        Sort sort = Sort.by(Sort.Direction.ASC, "_id");

        return mongoTemplate.find(new Query(new Criteria()).with(pageable).with(sort), ImportAppErrorLog.class, collectionName);
    }

    /**
     * 查询应用信息导入日志数量
     * @Comments:  <对此方法的描述，可以引用系统设计中的描述>
     * <AUTHOR> [<EMAIL>]
     * @since 2021-09-06 9:56
     * @param
     * @return
     */
    @Override
    public Long totalCount() throws Exception {

        return mongoTemplate.count(new Query(new Criteria()), collectionName);
    }

    /**
     * 新增应用信息导入日志
     * @Comments:  <对此方法的描述，可以引用系统设计中的描述>
     * <AUTHOR> [<EMAIL>]
     * @since 2020-04-08 9:56
     * @param
     * @return
     */
    @Override
    public ImportAppErrorLog insertImportAppErrorLog(ImportAppErrorLog importAppErrorLog) throws Exception {

        return mongoTemplate.insert(importAppErrorLog, collectionName);
    }

    /**
     * 删除应用信息导入错误日志表
     * @Comments:  <对此方法的描述，可以引用系统设计中的描述>
     * <AUTHOR> [<EMAIL>]
     * @since 2021-09-06 9:56
     * @param
     * @return
     */
    @Override
    public void dropTable() throws Exception {

        mongoTemplate.dropCollection(collectionName);
    }
}
