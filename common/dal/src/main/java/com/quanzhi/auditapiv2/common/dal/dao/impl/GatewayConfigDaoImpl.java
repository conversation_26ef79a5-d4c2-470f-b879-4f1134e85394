package com.quanzhi.auditapiv2.common.dal.dao.impl;

import com.quanzhi.auditapiv2.common.dal.dao.IGatewayConfigDao;
import com.quanzhi.auditapiv2.common.dal.dao.base.impl.BaseDaoImpl;
import com.quanzhi.auditapiv2.common.dal.entity.GatewayConfig;
import com.quanzhi.auditapiv2.common.dal.entity.GatewaySSLPem;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 *
 * 《网关配置持久层接口实现》
 *
 *
 * @Project:
 * @Module ID:
 * @Comments:
 * @JDK version used:      <JDK1.8> 
 * <AUTHOR> [<EMAIL>]
 * @since 2020-04-08-上午9:56:10
 */
@Repository
public class GatewayConfigDaoImpl extends BaseDaoImpl<GatewayConfig> implements IGatewayConfigDao {
    
    @Autowired
    MongoTemplate mongoTemplate;

    private static String collectionName = "gatewayConfig";

    @Override
    public GatewayConfig getByIp(String gatewayIp) {
        Criteria criteria = Criteria.where("ip").is(gatewayIp);
        return mongoTemplate.findOne(Query.query(criteria), GatewayConfig.class, collectionName);
    }

    @Override
    public List<GatewayConfig> selectGatewayConfigPage(Integer page, Integer limit) {
        Sort sort = Sort.by(Sort.Direction.DESC, "createTime");
        return mongoTemplate.find(new Query(new Criteria()).skip((page-1) * limit).limit(limit).with(sort), GatewayConfig.class, collectionName);
    }

    @Override
    public Long count() {
        return mongoTemplate.count(new Query(),collectionName);
    }

    @Override
    public void deleteById(String id) {
        Criteria criteria = Criteria.where("_id").is(id);
        mongoTemplate.remove(new Query(criteria),collectionName);
    }


}
