package com.quanzhi.auditapiv2.common.dal.dao.data;

import com.quanzhi.auditapiv2.common.dal.dto.common.CommonGroupDto;
import com.quanzhi.auditapiv2.common.dal.dto.data.DataInfoDto;
import com.quanzhi.auditapiv2.common.dal.dto.data.DataInfoOperatorDto;
import com.quanzhi.auditapiv2.common.dal.dto.data.FocusDataDto;
import com.quanzhi.auditapiv2.common.dal.dto.home.DataFlowInfoDto;
import com.quanzhi.auditapiv2.common.dal.entity.data.DataInfo;
import com.quanzhi.auditapiv2.common.dal.entity.data.DataMonthInfo;
import com.quanzhi.auditapiv2.common.util.dto.AggregationDto;
import com.quanzhi.auditapiv2.common.util.dto.GroupDto;
import org.springframework.data.mongodb.core.query.Criteria;

import java.util.List;

/**
 * @Author: yangzx
 * @Date: 2024/10/9 14:24
 */
public interface DataDao {

    List<DataInfo> listData(DataInfoOperatorDto dataInfoOperatorDto);

    List<AggregationDto> groupData(DataInfoOperatorDto dataInfoOperatorDto, GroupDto groupDto) throws Exception;

    String markData(DataInfoOperatorDto dataInfoOperatorDto);

    Long totalCount(DataInfoOperatorDto dataInfoOperatorDto);

    String batchMarkData(DataInfoOperatorDto dataInfoOperatorDto);

    FocusDataDto getFocusData();

    FocusDataDto updateFocusData(FocusDataDto focusDataDto);

    DataFlowInfoDto getDataFlow(List<String> focusDatas, String type);

    Criteria getCriteria(DataInfoOperatorDto dataInfoOperatorDto);

    List<DataMonthInfo> getAllDataMonthInfo();

    void upsertDataInfo(DataInfo dataInfo);

    DataInfo getDataInfoByDataId(String dataId);

}
