package com.quanzhi.auditapiv2.common.dal.dto;

import com.quanzhi.auditapiv2.common.dal.dto.api.CompositeAPPURLPathCriteria;
import com.quanzhi.auditapiv2.common.dal.dto.app.CustomPropertyDto;
import com.quanzhi.metabase.core.model.http.constant.LevelEnum;
import lombok.Data;

import java.util.*;

@Data
public class HttpApiSearchDto {

    private String id;

    private List<String> ids;

    private String uri;

    private List<String> uris;

    private String state;

    /**
     * 用于前端接口相关字段的查询
     */
    private String apiSearch;

    /**
     * api路径模糊筛选
     */
    private String apiPath;

    /**
     * apiUrl模糊筛选
     */
    private String apiUrl;

    /**
     * appUrl筛选
     */
    private String appUri;

    /**
     * 应用名称
     */
    private String appName;

    /**
     * 正则匹配搜索
     */
    private String regexApiUr;

    /**
     * 接口名称模糊搜索
     */
    private String apiName;

    /**
     * 域名
     */
    private String host;
    private String hostRegex;
    /**
     * 域名查询忽略大小写
     */
    private Boolean hostIgnoreCase;

    /**
     * url节点
     */
    private Integer urlNode;

    /**
     * url路径
     */
    private String urlPath;

    /**
     * url路径与节点
     */
    private List<UrlPaths> urlPathList;

    private Boolean delFlag;

    private List<Integer> apiType;

    /**
     * API等级
     */
    private List<String> level;

    /**
     * API风险等级
     */
    private List<Integer> apiRiskLevel;

    /**
     * 是否restful接口
     */
    private Short restfulFlag;

    /**
     * 是否when-case接口
     */
    private Short whenFlag;

    /**
     * 所有dataLabels
     */
    private List<String> dataLabels;

    /**
     * 请求数据标签
     */
    private List<String> reqDataLabels;

    /**
     * 返回数据标签
     */
    private List<String> rspDataLabels;

    /**
     * 是否单事件
     */
    @Deprecated
    private Boolean isSingle;
    /**
     * 请求按单事件
     */
    private Boolean reqIsSingle;
    /**
     * 返回按单事件
     */
    private Boolean rspIsSingle;


    /**
     * 访问域列表
     */
    private List<String> visitDomains;
    
    /**
     * 部署域列表
     */
    private List<String> deployDomains;

    /**
     * 接口分类
     */
    private List<String> classifications;

    /**
     * 特征标签
     */
    private List<String> featureLabels;

    /**
     * 低精度接口分类
     */
    private List<String> lowClassifications;

    /**
     * 低精度数据标签
     */
    private List<String> lowDataLabels;

    /**
     * 低精度请求数据标签
     */
    private List<String> lowReqDataLabels;

    /**
     * 低精度返回数据标签
     */
    private List<String> lowRspDataLabels;

    /**
     * 接口请求类型
     */
    private List<String> reqContentTypes;

    /**
     * 接口返回类型
     */
    private List<String> rspContentTypes;

    /**
     * 发现时间范围搜索（开始时间）
     */
    private Long discoverTimeStart;

    /**
     * 发现时间范围搜索（开始时间）
     */
    private Long discoverTimeEnd;

    /**
     * 活跃时间搜索
     */
    private Long activeTimeStart;

    private Long activeTimeEnd;

    /**
     * 访问终端搜索
     */
    private List<String> terminals;

    /**
     * 推荐状态
     */
    private Short recommendFlag;

    /**
     * 请求方式
     */
    private List<String> methods;

    /**
     * 是否是弱点接口
     */
    private Boolean isApiWeakness;

    /**
     * 是否敏感API
     */
    private Boolean isSensitiveApi;

    /**
     * 分组字段
     */
    private String groupField;

    /**
     * 部门
     */
    private String department;

    private List<CustomPropertyDto> customProperties;

    /**
     * appUri集合
     */
    private Set<String> appUriSet;

    /**
     * 部门集合
     */
    private Set<String> departmentSet;

    /**
     * 建单状态
     */
    private Integer orderFlag;

    private Integer totalVisitsGt;

    private Integer totalVisitsLt;

    /**
     * 模糊筛选部署域
     */
    private String regexVisitDomains;
    /**
     * 查询字段的且或操作，key为查询字段
     */
    private Map<String,String> fieldOperateMap=new HashMap<>();

    /**
     * 访问域查询操作符
     * @see com.quanzhi.auditapiv2.common.dal.enums.DBOperatorEnum
     */
    @Deprecated
    private String visitDomainsOperator;

    /**
     * 部署域查询操作符
     * @see com.quanzhi.auditapiv2.common.dal.enums.DBOperatorEnum
     */
    @Deprecated
    private String deployDomainsOperator;
    
    /**
     * 请求数据标签查询操作符
     * @see com.quanzhi.auditapiv2.common.dal.enums.DBOperatorEnum
     */
    @Deprecated
    private String reqDataLabelsOperator;

    /**
     * 返回数据标签查询操作符
     * @see com.quanzhi.auditapiv2.common.dal.enums.DBOperatorEnum
     */
    @Deprecated
    private String rspDataLabelsOperator;

    /**
     * 接口分类查询操作符
     * @see com.quanzhi.auditapiv2.common.dal.enums.DBOperatorEnum
     */
    @Deprecated
    private String classificationsOperator;

    /**
     * 特征标签查询操作符
     * @see com.quanzhi.auditapiv2.common.dal.enums.DBOperatorEnum
     */
    @Deprecated
    private String featureLabelsOperator;

    /**
     * 是否执行资产权限控制
     */
    private Boolean isAssetAuthorization;

    private Long creatTimestamp;

    private Long updateTimestamp;

    /**
     * 一般审计用到
     */
    private Long endUpdateTime;
    private Long startUpdateTime;

    private Integer page;
    private Integer limit;
    private String field;
    private Object sort;



    private List<ExporTitleFieldDto> titleFieldList;

    @Deprecated
    private List<String> riskNames;
    @Deprecated
    private List<String> weaknessNames;

    private List<String> riskIds;
    private List<String> weaknessIds;

    private List<String> apiFormats;
    private String flowSource;
    private List<Short> apiLifeFlag;
    private List<String> showFields;
    private String remark;
    private String nodeId;
    private Boolean isUpdate;
    //标签映射
    private Map<String,String> dataLabelMap=new HashMap<>();
    //弱点映射
    private Map<String, String> weaknessMap=new HashMap<>();
    //风险映射
    private Map<String, String> riskMap=new HashMap<>();

    private CompositeAPPURLPathCriteria compositeApp;

    // 数据权限查询query
    private Map<String, Object> dataPermissionMap;

    /**
     * 敏感API定义的等级范围
     */
    public static List<String> sensitiveLevelList= Arrays.asList(new String[]{LevelEnum.HIGH.getKey(),LevelEnum.MIDDLE.getKey(),LevelEnum.LOW.getKey()});

    public static List<String> noSensitiveLevelList= Arrays.asList(new String[]{LevelEnum.OTHER.getKey(),LevelEnum.NON.getKey()});

    private List<String> province;

    @Data
    public static final class UrlPaths{

        /**
         * url节点
         */
        private Integer urlNode;

        /**
         * url路径
         */
        private String urlPath;
    }
    
    public enum GroupFieldEnum {
        /**
         * 访问域
         */
        VIST_DOMAIN("visitDomains"),
        /**
         * 等级
         */
        LEVEL("level"),
        /**
         * 请求数据标签
         */
        REQ_DATA_LABEL("reqDataLabels"),
        /**
         * 返回数据标签
         */
        RSP_DATA_LABEL("rspDataLabels"),
        /**
         * 请求文件类型
         */
        REQ_CONTENT_TYPE("reqContentTypes"),
        /**
         * 返回文件类型
         */
        RSP_CONTENT_TYPE("rspContentTypes"),
        /**
         * API类型
         */
        API_TYPE("apiType"),
        /**
         * API分类
         */
        CLASSIFICATIONS("classifications"),
        /**
         * API标签
         */
        FEATURELABEL("featureLabels"),
        /**
         * API风险等级
         */
        API_RISK_LEVEL("apiRiskLevel"),
        /**
         * API标签与API类型
         */
        NEW_FEATURELABELS("newFeatureLabels"),

        /**
         * 所属部门
         */
        DEPARTMENT("departments.department"),
        /**
         * 敏感API
         */
        IS_SENSITIVE_API("isSensitiveApi"),
        /**
         * 请求方法
         */
        METHOD("methods"),
        /**
         * 终端
         */
        TERMINAL("terminals"),
        /**
         * 状态
         */
        STATE("state"),
        /**
         * 路径
         */
        URL_PATHS("urlPaths"),
        /**
         * 建单状态
         */
        ORDER_FLAG("orderFlag"),
        /**
         * 接口生命状态
         */
        API_LIFE_FLAG("apiLifeFlag"),
        /**
         * 省份
         */
        PROVINCES("provinces"),
        /**
         * 流量来源
         */
        FLOW_SOURCES("flowSources");

        String name;

        GroupFieldEnum(String name){

            this.name = name;
        }
        public String getName(){
            return name;
        }

    }

}
