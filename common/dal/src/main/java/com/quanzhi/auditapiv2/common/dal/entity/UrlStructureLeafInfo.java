package com.quanzhi.auditapiv2.common.dal.entity;

import lombok.Data;

import java.util.List;


/**
 * url结构树中的信息，包含叶子节点的基本信息
 */
@Data
public class UrlStructureLeafInfo {

    private String id;
    private Integer total;
    private String preUrl;

    private String url;

    private String host;

    private Integer leafCount;

    private List<String> leafs;


    private String path;
    /**
     * 节点路径集合
     */
    private List<String> paths;

    private String apiId;
    /**
     * 节点接口id集合
     */
    private List<String> apiIds;


}
