package com.quanzhi.auditapiv2.common.dal.dto.docx;

import lombok.Data;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 *
 * 《接口未鉴权》
 *
 *
 * @Project:
 * @Module ID:
 * @Comments:
 * @JDK version used:      <JDK1.8>
 * <AUTHOR> [<EMAIL>]
 * @since 2020-08-28-下午16:52:18
 */
@Data
public class Noauthaccess extends ApiWeaknessExportWord {

    @Mapper
    public interface NoauthaccessMapper {

        Noauthaccess.NoauthaccessMapper INSTANCE = Mappers.getMapper(Noauthaccess.NoauthaccessMapper.class);
        Noauthaccess convert(ApiWeaknessExportWord apiWeaknessExportWord);
    }
}
