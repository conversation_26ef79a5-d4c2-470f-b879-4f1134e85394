package com.quanzhi.auditapiv2.common.dal.dao.impl;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.parser.Feature;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.quanzhi.auditapiv2.common.dal.dao.IScanTaskDao;
import com.quanzhi.auditapiv2.common.dal.dao.base.impl.BaseDaoImpl;
import com.quanzhi.auditapiv2.common.dal.dto.ResultFilterRuleDto;
import com.quanzhi.auditapiv2.common.dal.entity.ScanTask;
import com.quanzhi.auditapiv2.common.util.utils.CommonUtils;
import com.quanzhi.auditapiv2.common.util.utils.DataUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.stereotype.Repository;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Repository
public class ScanTaskDaoImpl extends BaseDaoImpl<ScanTask> implements IScanTaskDao {

    @Autowired
    MongoTemplate mongoTemplate;

    String collectionName = "scanTask";

    @Override
    public List<ScanTask> selectScanTaskList() {

        //查询条件
        Criteria criteria = new Criteria();

        return mongoTemplate.find(new Query(criteria), ScanTask.class, collectionName);
    }

    @Override
    public ScanTask findByCode(String code) {
        Criteria criteria = Criteria.where("code").is(code);
        return mongoTemplate.findOne(new Query(criteria), ScanTask.class, collectionName);
    }

    @Override
    public ScanTask getScanTaskByFileHash(String fileHash) {
        Criteria criteria = Criteria.where("fileHash").is(fileHash);
        return mongoTemplate.findOne(new Query(criteria), ScanTask.class, collectionName);
    }

    @Override
    public ScanTask selectScanTaskById(String id) {

        //查询条件
        Criteria criteria = Criteria.where("_id").is(id);

        return mongoTemplate.findOne(new Query(criteria), ScanTask.class, collectionName);
    }

    @Override
    public ScanTask insert(ScanTask scanTask){
        return mongoTemplate.insert(scanTask,collectionName);
    }

    @Override
    public ScanTask findOneByQuery(Query query){
        return mongoTemplate.findOne(query,ScanTask.class,collectionName);
    }

    @Override
    public void updateScanTaskByTaskCode(Query query, Update update){
        mongoTemplate.updateMulti(query,update,ScanTask.class,collectionName);
    }

    @Override
    public void addTaskFilters(String taskCode,String scanRule, List<ResultFilterRuleDto> filters) {
        Query query = new Query();
        query.addCriteria(Criteria.where("code").is(taskCode));
        Map scanTask = mongoTemplate.findOne(query, Map.class, "scanTask");
        String paramsJson = (String) scanTask.get("params");
        Map config = JSONObject.parseObject(paramsJson, Feature.OrderedField);

        if(DataUtil.isEmpty( config )) {
            config = new HashMap();
        }
        if (config.get("filters") == null) {
            config.put("filters", new ArrayList<>());
        }
        List configFilters = (List) config.get("filters");

        String currentResultSet = taskCode + "_" + scanRule;

        List newConfigFilters = (List) configFilters.stream().filter(filterObject -> {

            Map<String, Object> filterMap = (Map) filterObject;
            return !filterMap.get("resultSet").equals(currentResultSet);

        }).collect(Collectors.toList());

        for (ResultFilterRuleDto filterRuleDto : filters) {
            Map<String, Object> filterMap = new HashMap<String, Object>();
            filterMap.put("resultSet", filterRuleDto.getResultSet());
            filterMap.put("field", filterRuleDto.getField());
            filterMap.put("op", filterRuleDto.getOp());
            filterMap.put("value", filterRuleDto.getValue());
            String id = CommonUtils.MD5(JSONObject.toJSONString(filterMap));
            filterMap.put("_id", id);
            newConfigFilters.add(filterMap);
        }
        config.put("filters",newConfigFilters);

        Update update = new Update();
        update.set("params", JSONObject.toJSONString(config, SerializerFeature.MapSortField));
        mongoTemplate.updateFirst(query, update, "scanTask");
    }

    @Override
    public void deleteByName(String name) {
        Query query = new Query();
        query.addCriteria(Criteria.where("name").is(name));
        mongoTemplate.remove(query,collectionName);
    }


}