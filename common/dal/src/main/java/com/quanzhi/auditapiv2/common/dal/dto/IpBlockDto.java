package com.quanzhi.auditapiv2.common.dal.dto;

import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * create at 2023/4/11 8:15 下午
 * @description: ip阻断对象
 **/
@Data
public class IpBlockDto {

    private String ip;

    private BlockRange blockRange;

    private List<String> hosts;

    private BlockOperator blockOperator;


    public enum BlockOperator {
        BLOCK, UNBLOCK
    }

    public enum BlockRange {
        ALL_APP, SPECIAL_APP
    }


}