package com.quanzhi.auditapiv2.common.dal.dao.base.impl;

import com.alibaba.fastjson.JSONObject;
import com.mongodb.*;
import com.mongodb.client.FindIterable;
import com.quanzhi.auditapiv2.common.dal.annotation.NoReflection;
import com.quanzhi.auditapiv2.common.dal.dao.base.IBaseDao;
import com.quanzhi.auditapiv2.common.dal.dao.common.BatchUpdateOption;
import com.quanzhi.auditapiv2.common.dal.dto.UpdateEachTransformer;
import com.quanzhi.auditapiv2.common.dal.dto.query.QueryHelper;
import com.quanzhi.auditapiv2.common.util.constant.DataConstant;
import com.quanzhi.auditapiv2.common.util.dto.AggregationDto;
import com.quanzhi.auditapiv2.common.util.dto.AggregationListDto;
import com.quanzhi.auditapiv2.common.util.dto.GroupDto;
import com.quanzhi.auditapiv2.common.util.dto.ListOutputDto;
import com.quanzhi.auditapiv2.common.util.utils.*;
import com.quanzhi.metabase.core.model.query.MetabaseQuery;
import com.quanzhi.metabase.core.model.query.ResourceUpdates;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
//import org.bson.BSON;
import org.bson.Document;
import org.bson.types.ObjectId;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.aggregation.Aggregation;
import org.springframework.data.mongodb.core.aggregation.AggregationOperation;
import org.springframework.data.mongodb.core.aggregation.AggregationResults;
import org.springframework.data.mongodb.core.query.BasicQuery;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.util.CollectionUtils;

import java.lang.reflect.*;
import java.util.*;
import java.util.stream.Collectors;

import static org.springframework.data.mongodb.core.aggregation.Aggregation.newAggregationOptions;

/**
 * Created by harry on 2017/11/16.
 */
@Slf4j
public class BaseDaoImpl<T> implements IBaseDao<T> {
    @Autowired
    protected MongoTemplate mongoTemplate;

    protected String collectionName;

    private Class<T> clazz;

//    static {
//        try {
//            Class clazz = Class.forName("org.springframework.data.mongodb.core.query.Update$Each");
//            BSON.addEncodingHook(clazz, new UpdateEachTransformer());
//        } catch (ClassNotFoundException e) {
//            log.warn("bson add encode hook err", e);
//        }
//    }

    public BaseDaoImpl() {
        Type type = getClass();
        if (!(type instanceof ParameterizedType)) {
            type = getClass().getGenericSuperclass();
        }
        try {
            clazz = (Class<T>) ((ParameterizedType) type).getActualTypeArguments()[0];
        } catch (Exception e) {
            // ignore
        }

        determineCollectionName(this.clazz);
    }

    /**
     * 根据实体类获取表名
     *
     * @param entityClass
     * @return
     */
    private void determineCollectionName(Class<?> entityClass) {
        if (DataUtil.isEmpty(entityClass)) {
            return;
        }

        // 如果collectionName字段不为空，那么不需要根据类名获取
        if (DataUtil.isNotEmpty(collectionName)) {
            return;
        }
        String collName = entityClass.getSimpleName();
        collName = collName.replaceFirst(collName.substring(0, 1)
                , collName.substring(0, 1).toLowerCase());
        this.collectionName = collName;
    }

    protected String getCollectionName() {
        return this.collectionName;
    }

    protected String getCollectionName(String collection) {
        if (DataUtil.isNotEmpty(collection)) {
            return collection;
        }
        return getCollectionName();
    }

    /**
     * 过滤Id
     *
     * @param id
     * @return
     * @throws Exception
     */
    @Override
    public Object getFilterId(String id) {
        if (id.length() == 24) {
            try {
                return new ObjectId(id);
            } catch (Exception e) {
                return id;
            }
        } else {
            return id;
        }
    }

    @Override
    public T getById(String id) {
        Criteria where = Criteria.where(DataConstant.COLLECTION_BASE_ID).is(getFilterId(id));
        return mongoTemplate.findOne(new Query(where), clazz, getCollectionName());
    }

    @Override
    public T getById(String id, String collectionName) {
        Criteria where = Criteria.where(DataConstant.COLLECTION_BASE_ID).is(getFilterId(id));
        return mongoTemplate.findOne(new Query(where), clazz, getCollectionName(collectionName));
    }

    @Override
    public List<T> getByIds(List<String> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return new ArrayList<>();
        }
        Criteria where = Criteria.where(DataConstant.COLLECTION_BASE_ID).in(ids);
        List<T> objs = mongoTemplate.find(new Query(where), clazz, getCollectionName());
        return sortByIds(objs, ids);
    }

    /**
     * objs按照ids排序
     *
     * @param objs
     * @param ids
     * @return
     */
    protected List<T> sortByIds(List<T> objs, List ids) {
        if (CollectionUtils.isEmpty(objs) || CollectionUtils.isEmpty(ids)) {
            return null;
        }

        List<T> sortedObjs = new ArrayList<>();
        for (Object id : ids) {
            for (T obj : objs) {
                String objId = "";
                try {
                    Method getIdMethod = clazz.getMethod("getId");
                    objId = (String) getIdMethod.invoke(obj);
                } catch (Exception e) {
                    return sortedObjs;
                }

                if (objId.equals(id)) {
                    sortedObjs.add(obj);
                    continue;
                }
            }
        }
        ;

        return sortedObjs;
    }

    /**
     * @param ids
     * @return
     * @throws Exception
     */
    @Override
    public List<Object> getFilterIds(List<String> ids) {
        List<Object> idList = new ArrayList<Object>();
        for (String id : ids) {
            idList.add(getFilterId(id));
        }

        return idList;
    }

    @Override
    public void save(T object) {
        mongoTemplate.save(object, getCollectionName());
    }

    public T saveRt(T object) {
        T obj = mongoTemplate.save(object, getCollectionName());
        return obj;
    }

    @Override
    public void batchSave(List<T> objects, String collectionName) {
        mongoTemplate.insert(objects, getCollectionName(collectionName));
    }

    @Override
    public void batchSave(List<T> objects) {
        mongoTemplate.insert(objects, getCollectionName());
    }

    @Override
    public void update(T object) {
        List<T> updateObjs = Arrays.asList(object);
        batchUpdate(updateObjs);
    }

    @Override
    public void update(T criteria, T object) {
        Criteria where = fields2criteria(criteria);
        Update update = fields2update(object);
        mongoTemplate.updateFirst(new Query(where), update, getCollectionName());
    }

    @Override
    public void updateMulti(T criteria, T object) {
        Criteria where = fields2criteria(criteria);
        Update update = fields2update(object);
        mongoTemplate.updateMulti(new Query(where), update, getCollectionName());
    }

    @Override
    public void updateMulti(Criteria criteria, Update update) {
        mongoTemplate.updateMulti(new Query(criteria), update, getCollectionName());
    }

    @Override
    public void upsert(T criteria, T object) {
        Criteria where = fields2criteria(criteria);
        Update update = fields2update(object);
        mongoTemplate.upsert(new Query(where), update, getCollectionName());
    }

    @Override
    public void upsert(String id, T object) {
        if (DataUtil.isEmpty(id)) {
            save(object);
            return;
        }
        Criteria where = Criteria.where(DataConstant.COLLECTION_BASE_ID).is(getFilterId(id));
        Update update = fields2update(object);
        mongoTemplate.upsert(new Query(where), update, getCollectionName());
    }

    @Override
    public List<T> getAll() {
        return mongoTemplate.find(new Query(), clazz, getCollectionName());
    }

    @Override
    public Long getCount() {
        return mongoTemplate.count(new Query().addCriteria(Criteria.where("delFlag").is(false)), clazz);
    }

    @Override
    public Long getCount(String collectionName) {
        return mongoTemplate.count(new Query(), getCollectionName(collectionName));
    }

    /**
     * 批量更新
     * ref: https://www.jianshu.com/p/3577ccb722a1
     *
     * @param mongoTemplate
     * @param collName
     * @param options
     */
    protected void bathUpdate(MongoTemplate mongoTemplate, String collName,
                              List<BatchUpdateOption> options) {
        doBathUpdate(mongoTemplate, collName, options);
    }


//    protected void bathUpdate(DBCollection dbCollection, String collName,
//                              List<BatchUpdateOption> options) {
//        doBathUpdate(dbCollection, collName, options);
//    }

    @Override
    public void batchUpdateByOption(List<BatchUpdateOption> options) {
        bathUpdate(mongoTemplate, this.collectionName, options);
    }

    @Override
    public void batchUpdateByOption(List<BatchUpdateOption> options, String collectionName) {
        bathUpdate(mongoTemplate, getCollectionName(collectionName), options);
    }

    protected AggregationResults aggregate(Aggregation aggregation) {
        return mongoTemplate.aggregate(aggregation, collectionName, clazz);
    }

    /**
     * 每次批量更新的最大文档数
     */
    private static final int BATCH_UPDATE_SIZE = 500;

    private void doBathUpdate(MongoTemplate mongoTemplate, String collName,
                              List<BatchUpdateOption> options) {
        if (CollectionUtils.isEmpty(options)) {
            return;
        }

        int page = 1;
        int totalPage = options.size() % BATCH_UPDATE_SIZE == 0 ? options.size() / BATCH_UPDATE_SIZE : (options.size() / BATCH_UPDATE_SIZE + 1);
        while (page <= totalPage) {
            List<BatchUpdateOption> curOptions = PagingUtil.page(options, page, BATCH_UPDATE_SIZE);
            page++;
            if (CollectionUtils.isEmpty(curOptions)) {
                continue;
            }

            Document document = new Document();
            document.put("update", collName);
            List<BasicDBObject> updateList = new ArrayList<BasicDBObject>();
            for (BatchUpdateOption option : curOptions) {
                BasicDBObject update = new BasicDBObject();
                update.put("q", option.getQuery().getQueryObject());
                update.put("u", option.getUpdate().getUpdateObject());
                update.put("upsert", option.isUpsert());
                update.put("multi", option.isMulti());
                updateList.add(update);
            }
            document.put("updates", updateList);
            document.put("ordered", false);
            try {
                Document commandResult = mongoTemplate.executeCommand(document);
            } catch (Exception e) {
                log.warn("", e);
            }
        }
    }

    @Override
    public void batchUpdate(List<T> objs) {
        batchUpdate(objs, this.collectionName);
    }

    @Override
    public void batchUpdate(List<T> objs, String collectionName) {
        Field[] fields = clazz.getDeclaredFields();
        if (fields.length == 0) {
            return;
        }

        List<BatchUpdateOption> updateOptions = new ArrayList<>();
        for (T obj : objs) {
            Update update = new Update();
            Criteria where = getCriteria(fields, (T) obj, update);

            // 如果where条件为null，说明未找到id，未防止造成全表更新，这里需要抛出异常
            if (DataUtil.isEmpty(where)) {
                throw new RuntimeException("where is null, maybe id not found");
            }

            BatchUpdateOption updateOption = new BatchUpdateOption();
            updateOption.setQuery(new Query(where));
            updateOption.setUpdate(update);
            updateOptions.add(updateOption);
        }

        batchUpdateByOption(updateOptions, getCollectionName(collectionName));
    }

    @Override
    public void batchUpsert(List<T> objs, String collectionName, boolean upsert, boolean multi) {
        Field[] fields = clazz.getDeclaredFields();
        if (fields.length == 0) {
            return;
        }

        List<BatchUpdateOption> updateOptions = new ArrayList<>();
        for (T obj : objs) {
            Update update = new Update();
            Criteria where = getCriteria(fields, (T) obj, update);

            // 如果where条件为null，说明未找到id，未防止造成全表更新，这里需要抛出异常
            if (where == null) {
                throw new RuntimeException("batch update failed");
            }

            BatchUpdateOption updateOption = new BatchUpdateOption();
            updateOption.setQuery(new Query(where));
            updateOption.setUpdate(update);
            updateOption.setUpsert(upsert);
            updateOption.setMulti(multi);
            updateOptions.add(updateOption);

        }

        batchUpdateByOption(updateOptions, getCollectionName(collectionName));
    }

    private Criteria getCriteria(Field[] fields, T obj, Update update) {
        Criteria where = null;
        for (Field field : fields) {

            // 拼接get方法名称
            String fieldName = field.getName();

            // serialVersionUID字段不处理
            if (StringUtils.isNotEmpty(fieldName) && "serialVersionUID".equals(fieldName)) {
                continue;
            }
            String getMethodName = "get" + upperCase(fieldName);

            // 反射调用get方法，获取字段值
            try {
                Method method = clazz.getMethod(getMethodName);
                Object fieldValue = method.invoke(obj);

                // 如果字段值为null，则不需要更新
                if (null == fieldValue) {
                    continue;
                }

                // 如果是ID，那么拼装where条件
                if (DataConstant.PROPERTY_BASE_ID.equals(fieldName) || DataConstant.COLLECTION_BASE_ID.equals(fieldName)) {
                    String id = (String) fieldValue;
                    where = Criteria.where(DataConstant.COLLECTION_BASE_ID).is(getFilterId(id));
                } else {

                    // 如果不是ID，那么拼装update条件
                    update.set(fieldName, fieldValue);
                }

            } catch (NoSuchMethodException e) {
                // ignore
            } catch (Exception e) {
                log.warn("get method failed", e);
                throw new RuntimeException("batch update failed");
            }
        }
        return where;
    }

    @Override
    public T getByCriteria(Criteria criteria) {
        return mongoTemplate.findOne(new Query(criteria), clazz, getCollectionName());
    }

    @Override
    public T getByCriteria(T criteria) {
        Criteria where = fields2criteria(criteria);
        return mongoTemplate.findOne(new Query(where), clazz, getCollectionName());
    }

    @Override
    public T getByCriteriaAndCollectionName(T criteria, String collectionName) {
        Criteria where = fields2criteria(criteria);
        return mongoTemplate.findOne(new Query(where), clazz, getCollectionName(collectionName));
    }

    @Override
    public Boolean isExists(Criteria criteria) {
        return DataUtil.isNotEmpty(getByCriteria(criteria));
    }

    @Override
    public Boolean isExists(T criteria) {
        return DataUtil.isNotEmpty(getByCriteria(criteria));
    }

    @Override
    public Boolean isExists(T criteria, T fuzzy) {
        return DataUtil.isNotEmpty(getByCriteria(criteria, fuzzy));
    }

    @Override
    public T getByCriteria(T criteria, String excludeId) {
        Criteria where = fields2criteria(criteria);
        if (DataUtil.isNotEmpty(excludeId)) {
            where.and(DataConstant.COLLECTION_BASE_ID).ne(getFilterId(excludeId));
        }
        return mongoTemplate.findOne(new Query(where), clazz, getCollectionName());
    }

    @Override
    public List<T> getAllByCriteria(T criteria, String excludeId) {
        Criteria where = fields2criteria(criteria);
        if (DataUtil.isNotEmpty(excludeId)) {
            where.and(DataConstant.COLLECTION_BASE_ID).ne(getFilterId(excludeId));
        }
        return mongoTemplate.find(new Query(where), clazz, getCollectionName());
    }

    @Override
    public List<T> getAllByCriteria(T criteria) {
        Criteria where = fields2criteria(criteria);
        return mongoTemplate.find(new Query(where), clazz, getCollectionName());
    }

    @Override
    public List<T> getAllByCriteria(Criteria criteria) {
        return mongoTemplate.find(new Query(criteria), clazz, getCollectionName());
    }

    @Override
    public List<T> getLimitByCriteria(Criteria criteria, Integer skip, Integer limit) {
        return mongoTemplate.find(new Query(criteria).skip(skip).limit(limit), clazz, getCollectionName());
    }

    @Override
    public List<T> getAllByCriteria(T criteria, T fuzzy) {
        Criteria where = fields2criteria(criteria);
        Criteria fuzzyWhere = fields2criteria(fuzzy, true);
        where.andOperator(fuzzyWhere);
        return mongoTemplate.find(new Query(where), clazz, getCollectionName());
    }

    @Override
    public List<T> getAllByCriteria(T criteria, T fuzzy, List<String> fields) {
        Criteria where = fields2criteria(criteria);
        Criteria fuzzyWhere = fields2criteria(fuzzy, true);
        where.andOperator(fuzzyWhere);
        return mongoTemplate.find(getQueryWithField(where, fields), clazz, getCollectionName());
    }

    @Override
    public List<T> getAllByCriteria(T criteria, T fuzzy, List<String> fields, String collectionName) {
        Criteria where = fields2criteria(criteria);
        Criteria fuzzyWhere = fields2criteria(fuzzy, true);
        where.andOperator(fuzzyWhere);
        return mongoTemplate.find(getQueryWithField(where, fields), clazz, getCollectionName(collectionName));
    }

    @Override
    public T getByCriteria(T criteria, T fuzzy) {
        Criteria where = fields2criteria(criteria);
        Criteria fuzzyWhere = fields2criteria(where, fuzzy, true);
        return mongoTemplate.findOne(new Query(fuzzyWhere), clazz, getCollectionName());
    }

    @Override
    public List<T> getAllByCriteria(T criteria, List<String> fields) {
        if (DataUtil.isEmpty(fields)) {
            return getAllByCriteria(criteria);
        }

        Criteria where = fields2criteria(criteria);

        Document fieldObject = new Document();
        fields.stream().forEach(field -> {
            fieldObject.put(field, true);
        });

        Query query = new BasicQuery(where.getCriteriaObject(), fieldObject);
        return mongoTemplate.find(query, clazz, getCollectionName());
    }

    @Override
    public List<T> getAllByCriteria(Criteria criteria, List<String> fields) {
        if (DataUtil.isEmpty(fields)) {
            return getAllByCriteria(criteria);
        }

        Document fieldObject = new Document();
        fields.stream().forEach(field -> {
            fieldObject.put(field, true);
        });
        Query query = new BasicQuery(criteria.getCriteriaObject(), fieldObject);
        return mongoTemplate.find(query, clazz, getCollectionName());
    }

    protected Query getQueryWithField(Criteria where, List<String> fields) {
        if (DataUtil.isEmpty(fields)) {
            return new BasicQuery(where.getCriteriaObject());
        }

        Document fieldObject = new Document();
        fields.forEach(field -> fieldObject.put(field, true));
        BasicQuery query = new BasicQuery(where.getCriteriaObject(), fieldObject);
        return new BasicQuery(where.getCriteriaObject(), fieldObject);
    }

    @Override
    public List<T> getListByCriteria(T criteria) {
        Criteria where = fields2criteria(criteria);
        return mongoTemplate.find(new Query(where), clazz);
    }

    @Override
    public Long getCountByCriteria(T criteria) {
        Criteria where = fields2criteria(criteria);
        return mongoTemplate.count(new Query(where), clazz, getCollectionName());
    }

    @Override
    public Long getCountByCriteria(T criteria, T fuzzy) {
        Criteria where = fields2criteria(criteria);
        Criteria fuzzyWhere = fields2criteria(fuzzy, true);
        where.andOperator(fuzzyWhere);
        return mongoTemplate.count(new Query(where), clazz, getCollectionName());
    }

    @Override
    public Long getCountByCriteria(T criteria, T fuzzy, Criteria startDate, Criteria endDate) {
        Criteria where = fields2criteria(criteria);
        Criteria fuzzyWhere = fields2criteria(fuzzy, true);
        where.andOperator(fuzzyWhere, startDate, endDate);
        return mongoTemplate.count(new Query(where), clazz, getCollectionName());
    }

    @Override
    public Long getCountByCriteria(T criteria, T fuzzy, String collectionName) {
        Criteria where = fields2criteria(criteria);
        Criteria fuzzyWhere = fields2criteria(fuzzy, true);
        where.andOperator(fuzzyWhere);
        return mongoTemplate.count(new Query(where), clazz, getCollectionName(collectionName));
    }

    public Long getCountByCriteria(String collectionName, T criteria, T fuzzy) {
        Criteria where = fields2criteria(criteria);
        Criteria fuzzyWhere = fields2criteria(fuzzy, true);
        where.andOperator(fuzzyWhere);
        return mongoTemplate.count(new Query(where), clazz, getCollectionName(collectionName));
    }

    @Override
    public void batchHardDelete(List<String> ids) {
        Criteria where = Criteria.where(DataConstant.COLLECTION_BASE_ID).in(getFilterIds(ids));
        mongoTemplate.remove(new Query(where), clazz, getCollectionName());
    }

    @Override
    public ListOutputDto<T> page(Integer page, Integer limit, T criteria) {
        return page(page, limit, criteria, (Sort) null);
    }

    @Override
    public ListOutputDto<T> page(Integer page, Integer limit, T criteria, Sort sort) {
        if (DataUtil.isNotEmpty(sort)) {
            return page(page, limit, criteria, null, sort);
        } else {
            return page(page, limit, criteria, (T) null);
        }
    }

    @Override
    public ListOutputDto<T> page(Integer page, Integer limit, T criteria, T fuzzy) {
        return page(page, limit, criteria, fuzzy, null);
    }

    @Override
    public ListOutputDto<T> page(Integer page, Integer limit, T criteria, T fuzzy, Sort sort) {
        ListOutputDto<T> listOutputDto = new ListOutputDto<>();

        Long count = getCountByCriteria(criteria, fuzzy);
        listOutputDto.setTotalCount(count.intValue());
        if (count == 0L) {
            return listOutputDto;
        }

        Criteria where = fields2criteria(criteria);
        Criteria fuzzyWhere = fields2criteria(fuzzy, true);
        where.andOperator(fuzzyWhere);
        Pageable pageable = PageRequest.of(page - 1, limit);
        Query query = null;
        if (DataUtil.isNotEmpty(sort)) {
            query = new Query(where).with(sort).with(pageable);
        } else {
            query = new Query(where).with(pageable);
        }
        List<T> rows = mongoTemplate.find(query, clazz, getCollectionName());
        listOutputDto.setRows(rows);

        return listOutputDto;
    }

    @Override
    public ListOutputDto<T> page(String collectionName, Integer page, Integer limit, T criteria, T fuzzy, Sort sort) {
        ListOutputDto<T> listOutputDto = new ListOutputDto<>();

        Long count = getCountByCriteria(collectionName, criteria, fuzzy);
        listOutputDto.setTotalCount(count.intValue());
        if (count == 0L) {
            return listOutputDto;
        }

        Criteria where = fields2criteria(criteria);
        Criteria fuzzyWhere = fields2criteria(fuzzy, true);
        where.andOperator(fuzzyWhere);
        Pageable pageable = PageRequest.of(page - 1, limit);
        Query query = null;
        if (DataUtil.isNotEmpty(sort)) {
            query = new Query(where).with(sort).with(pageable);
        } else {
            query = new Query(where).with(pageable);
        }
        List<T> rows = mongoTemplate.find(query, clazz, getCollectionName(collectionName));
        listOutputDto.setRows(rows);

        return listOutputDto;
    }

    @Override
    public ListOutputDto<T> page(Integer page, Integer limit, Criteria criteria, Criteria fuzzy, Sort sort) {

        return page("", page, limit, criteria, fuzzy, sort);
    }

    @Override
    public ListOutputDto<T> page(String collectionName, Integer page, Integer limit, Criteria criteria, Criteria fuzzy, Sort sort) {
        ListOutputDto<T> listOutputDto = new ListOutputDto<>();

        Criteria where = new Criteria();
        if (DataUtil.isNotEmpty(criteria)) {
            where = criteria;
        }
        if (DataUtil.isNotEmpty(fuzzy)) {
            where.andOperator(fuzzy);
        }
        Long count = mongoTemplate.count(new Query(where), clazz, getCollectionName(collectionName));
        listOutputDto.setTotalCount(count.intValue());
        if (count == 0L) {
            return listOutputDto;
        }

        Pageable pageable = PageRequest.of(page - 1, limit);
        Query query = null;
        if (DataUtil.isNotEmpty(sort)) {
            query = new Query(where).with(sort).with(pageable);
        } else {
            query = new Query(where).with(pageable);
        }
        List<T> rows = mongoTemplate.find(query, clazz, getCollectionName(collectionName));
        listOutputDto.setRows(rows);

        return listOutputDto;
    }

    @Override
    public List<T> getAllByStart(Integer start, Integer limit, T criteria) {
        return getAllByStart(start, limit, criteria, (Sort) null);
    }

    @Override
    public List<T> getAllByStart(Integer start, Integer limit, T criteria, T fuzzy) {
        Criteria where = fields2criteria(criteria);
        Criteria fuzzyWhere = fields2criteria(fuzzy, true);
        where.andOperator(fuzzyWhere);
        Query query = new Query(where).limit(limit);
        return mongoTemplate.find(query, clazz, getCollectionName());
    }

    @Override
    public List<T> getAllByStart(Integer start, Integer limit, T criteria, Sort sort) {
        Criteria where = fields2criteria(criteria);
        Query query = null;
        if (DataUtil.isNotEmpty(sort)) {
            query = new Query(where).with(sort).skip(start).limit(limit);
        } else {
            query = new Query(where).skip(start).limit(limit);
        }
        return mongoTemplate.find(query, clazz, getCollectionName());
    }

    @Override
    public T getOneByStart(Integer start, T criteria) {
        List<T> objs = getAllByStart(start, 1, criteria);
        if (DataUtil.isEmpty(objs)) {
            return null;
        }
        return objs.get(0);
    }

    @Override
    public ListOutputDto<T> page(Integer page, Integer limit) {
        return page(page, limit, null);
    }

    @Override
    public void softDelete(String id) {
        Update update = Update.update(DataConstant.COMMON_FIELD_DELFLAG, 1);
        Criteria where = Criteria.where(DataConstant.COLLECTION_BASE_ID).is(getFilterId(id));
        mongoTemplate.upsert(new Query(where), update, clazz);
    }

    @Override
    public void batchSoftDelete(List<String> ids) {
        Update update = Update.update(DataConstant.COMMON_FIELD_DELFLAG, 1);
        Criteria where = Criteria.where(DataConstant.COLLECTION_BASE_ID).in(getFilterIds(ids));
        mongoTemplate.updateMulti(new Query(where), update, getCollectionName());
    }

    @Override
    public void batchIgnore(List<String> ids) {
        Update update = Update.update(DataConstant.COMMON_FIELD_IGNOREFLAG, 1);
        Criteria where = Criteria.where(DataConstant.COLLECTION_BASE_ID).in(getFilterIds(ids));
        mongoTemplate.updateMulti(new Query(where), update, getCollectionName());
    }

    @Override
    public void batchCancelIgnore(List<String> ids) {
        Update update = Update.update(DataConstant.COMMON_FIELD_IGNOREFLAG, 0);
        Criteria where = Criteria.where(DataConstant.COLLECTION_BASE_ID).in(getFilterIds(ids));
        mongoTemplate.updateMulti(new Query(where), update, getCollectionName());
    }

    @Override
    public void hardDelete(String id) {
        Criteria where = Criteria.where(DataConstant.COLLECTION_BASE_ID).is(getFilterId(id));
        mongoTemplate.remove(new Query(where), clazz, getCollectionName());
    }

    @Override
    public void deleteSave(T obj) {
        String id = null;
        try {
            Method getIdMethod = null;
            try {
                getIdMethod = obj.getClass().getMethod("getId");
            } catch (NoSuchMethodException var6) {
                id = null;
            }
            Object value = null;
            try {
                id = (String) getIdMethod.invoke(obj);
            } catch (InvocationTargetException | IllegalAccessException var5) {
                id = null;
            }
        } catch (Exception e) {
            throw new DaoException("获取ID错误");
        }
        if (DataUtil.isEmpty(id)) {
            throw new DaoException("id不能为空");
        }
        hardDelete(id);
        save(obj);
    }

    @Override
    public void dropCollection(String collectionName) {
        mongoTemplate.dropCollection(getCollectionName(collectionName));
    }

    @Override
    public FindIterable getCursor(Criteria criteria) {
        if (DataUtil.isEmpty(criteria)) {
            criteria = new Criteria();
        }
        return mongoTemplate.getCollection(getCollectionName()).find(new Query(criteria).getQueryObject());
    }

    private Document fieldsToProject(List<String> fields) {
        Document dbObject = new Document();
        if (CollectionUtils.isEmpty(fields)) {
            return dbObject;
        }

        for (String field : fields) {
            dbObject.put(field, 1);
        }
        return dbObject;
    }

    @Override
    public FindIterable getCursor(Criteria criteria, String collectionName) {
        if (DataUtil.isEmpty(criteria)) {
            criteria = new Criteria();
        }
        return mongoTemplate.getCollection(getCollectionName(collectionName)).find(new Query(criteria).getQueryObject());
    }

    @Override
    public FindIterable getCursor(Criteria criteria, Integer skip, String collectionName) {
        if (DataUtil.isEmpty(criteria)) {
            criteria = new Criteria();
        }
        return mongoTemplate.getCollection(getCollectionName(collectionName)).find(new Query(criteria).getQueryObject()).skip(skip);
    }

    @Override
    public FindIterable getCursor(Criteria criteria, Sort sort) {
        if (DataUtil.isEmpty(criteria)) {
            criteria = new Criteria();
        }
        Query query = new Query(criteria).with(sort);
        return mongoTemplate.getCollection(getCollectionName()).find(query.getQueryObject());
    }

    protected T findOne(List<T> objs) {
        if (DataUtil.isEmpty(objs)) {
            return null;
        }
        return objs.get(0);
    }

    protected Criteria fields2criteria(T obj) {
        return fields2criteria(obj, false);
    }

    private Criteria fields2criteria(Criteria where, T obj, Boolean isFuzzy) {
        Field[] fields = clazz.getDeclaredFields();

        if (DataUtil.isEmpty(where)) {
            where = new Criteria();
        }

        if (DataUtil.isEmpty(obj) || DataUtil.isEmpty(fields) || fields.length == 0) {
            return where;
        }

        List<Criteria> fuzzies = new ArrayList<>();
        for (Field field : fields) {

            // 拼接get方法名称
            String fieldName = field.getName();

            // serialVersionUID字段不处理
            if (StringUtils.isNotEmpty(fieldName) && fieldName.equals("serialVersionUID")) {
                continue;
            }
            // 排除一些字段
            if (field.isAnnotationPresent(NoReflection.class)) {
                continue;
            }
            String getMethodName = "get" + upperCase(fieldName);

            // 反射调用get方法，获取字段值
            try {
                Method method = clazz.getMethod(getMethodName);
                Object fieldValue = method.invoke(obj);

                // 如果字段值为null，则不需要更新
                if (DataUtil.isEmpty(fieldValue)) {
                    continue;
                }

                // 如果是ID，那么拼装where条件
                if (fieldName.equals(DataConstant.PROPERTY_BASE_ID) || field.equals(DataConstant.COLLECTION_BASE_ID)) {
                    String id = (String) fieldValue;
                    where = where.and(DataConstant.COLLECTION_BASE_ID).is(getFilterId(id));
                } else {
                    if (isFuzzy) {
                        if (fieldValue instanceof String) {
                            // 模糊查询需要使用or，不区分大小写
                            fuzzies.add(Criteria.where(fieldName).regex(DataUtil.regexStrEscape((String) fieldValue), "i"));
                        } else if (fieldValue instanceof Collection) {
                            Collection<Object> fieldValues = (Collection<Object>) fieldValue;
                            for (Object object : fieldValues) {
                                if (object instanceof String) {
                                    fuzzies.add(Criteria.where(fieldName).regex(DataUtil.regexStrEscape((String) object), "i"));
                                }
                            }
                        } else {
                            throw new DaoException("只有String型字段才支持模糊匹配, fileName: {0}, fieldValue: {1}", fieldName, fieldValue);
                        }
                    } else {
                        jointFieldAndValue(fieldName, fieldValue, where);
                    }
                }

            } catch (Exception e) {
                log.warn("get method failed", e);
                throw new DaoException("fields2criteria failed");
            }
        }

        // 填充模糊查询or
        if (DataUtil.isNotEmpty(fuzzies)) {
            Criteria[] fuzzyArr = new Criteria[fuzzies.size()];
            fuzzies.toArray(fuzzyArr);
            where.orOperator(fuzzyArr);
        }
        return where;
    }

    /**
     * 将对象中的有值字段，转换为Criteria
     *
     * @param obj
     * @param isFuzzy 是否模糊匹配
     * @return
     */
    protected Criteria fields2criteria(T obj, Boolean isFuzzy) {
        return fields2criteria(null, obj, isFuzzy);
    }

    private void jointFieldAndValue(String fieldName, Object fieldValue, Criteria where) {
        if (fieldValue instanceof String || fieldValue instanceof Number || fieldValue instanceof Boolean) {
            where.and(fieldName).is(fieldValue);
            return;
        }

        if (fieldValue instanceof JSONObject) {
            Set<Map.Entry<String, Object>> entries = ((JSONObject) fieldValue).entrySet();
            for (Map.Entry<String, Object> entry : entries) {
                jointFieldAndValue(fieldName + "." + entry.getKey(), entry.getValue(), where);
            }
            return;
        }

        if (fieldValue instanceof Collection) {
            where.and(fieldName).in((Collection) fieldValue);
            return;
        }

        if (fieldValue instanceof Collection) {
            where.and(fieldName).all((Collection) fieldValue);
            return;
        }

        Class subClazz = fieldValue.getClass();
        Field[] fields = subClazz.getDeclaredFields();
        if (DataUtil.isEmpty(fields) || fields.length == 0) {
            return;
        }

        for (Field field : fields) {
            // 拼接get方法名称
            String subFieldName = field.getName();

            // serialVersionUID字段不处理
            if (StringUtils.isNotEmpty(fieldName) && fieldName.equals("serialVersionUID")) {
                continue;
            }
            String getSubMethodName = "get" + upperCase(subFieldName);
            Object subFieldValue = null;

            try {
                Method method = subClazz.getMethod(getSubMethodName);
                subFieldValue = method.invoke(fieldValue);
            } catch (NoSuchMethodException e) {

                // ignore
                continue;
            } catch (Exception e) {
                log.warn("get method failed", e);
                continue;
            }

            if (DataUtil.isEmpty(subFieldValue)) {
                continue;
            }

            jointFieldAndValue(fieldName + "." + subFieldName, subFieldValue, where);
        }
    }

    protected Update fields2update(T obj) {
        Field[] fields = clazz.getDeclaredFields();

        Update update = new Update();
        if (DataUtil.isEmpty(obj) || DataUtil.isEmpty(fields) || fields.length == 0) {
            return update;
        }

        for (Field field : fields) {

            // 拼接get方法名称
            String fieldName = field.getName();

            // serialVersionUID字段不处理
            if (StringUtils.isNotEmpty(fieldName) && fieldName.equals("serialVersionUID")) {
                continue;
            }
            String getMethodName = "get" + upperCase(fieldName);

            // 反射调用get方法，获取字段值
            try {
                Method method = clazz.getMethod(getMethodName);
                Object fieldValue = method.invoke(obj);

                // 如果字段值为null，则不需要更新
                if (DataUtil.isNull(fieldValue)) {
                    continue;
                }

                // 如果是ID，那么拼装where条件
                if (fieldName.equals(DataConstant.PROPERTY_BASE_ID) || field.equals(DataConstant.COLLECTION_BASE_ID)) {

                    // 不允许更新id
                } else {
                    update = update.set(fieldName, fieldValue);
                }

            } catch (NoSuchMethodException e) {
                // ingore
            } catch (Exception e) {
                log.warn("get method failed", e);
                throw new DaoException("fields2update failed");
            }
        }
        return update;
    }

    /**
     * 首字母大写
     *
     * @param str
     * @return
     */
    private String upperCase(String str) {
        char[] ch = str.toCharArray();
        if (ch[0] >= 'a' && ch[0] <= 'z') {
            ch[0] = (char) (ch[0] - 32);
        }
        return new String(ch);
    }

    /**
     * 执行聚合操作，并返回对应key的值，若可以不存，则返回默认value
     *
     * @param aggregation
     * @param key
     * @param defaultVal
     * @return
     */
    protected Object aggregate(Aggregation aggregation, String key, Object defaultVal) {
        List<Map> results = mongoTemplate.aggregate(aggregation, getCollectionName(), Map.class).getMappedResults();
        if (DataUtil.isEmpty(results) || !results.get(0).containsKey(key)) {
            return defaultVal;
        }
        return results.get(0).get("count");
    }

    protected void between(Criteria where, String fieldName, Long startTime, Long endTime) {
        if (DataUtil.isEmpty(where)) {
            where = new Criteria();
        }

        if (DataUtil.isNotEmpty(startTime) && DataUtil.isNotEmpty(endTime)) {
            where.and(fieldName).gte(startTime).lte(endTime);
        } else if (DataUtil.isNotEmpty(startTime)) {
            where.and(fieldName).gte(startTime);
        } else if (DataUtil.isNotEmpty(endTime)) {
            where.and(fieldName).lte(endTime);
        }
    }

    /**
     * @return
     * @TODO 从es查数据
     * 本方法被调用的KafkaStateStoreCleanScheduleJob没有被使用，不需要改了
     */
    @Override
    public Set<String> getApiEventDates() {
        Set<String> tableName = mongoTemplate.getCollectionNames();
        return tableName.stream().filter(s -> {
            return s.startsWith("apiEvent_") && s.length() == 17;
        }).map(s -> {
            return s.replace("apiEvent_", "");
        }).collect(Collectors.toSet());
    }

    @Override
    public void setFieldNull(String id, String fieldName) {
        Criteria where = Criteria.where(DataConstant.COLLECTION_BASE_ID).is(getFilterId(id));
        Update update = Update.update(fieldName, null);
        mongoTemplate.updateFirst(new Query(where), update, getCollectionName());
    }

    @Override
    public ListOutputDto pageLimitTotalCnt(T criteria, T fuzzy, Integer page, Integer limit, Sort sort, Integer maxTotalCnt) {
        Criteria where = fields2criteria(criteria);
        Criteria fuzzyWhere = fields2criteria(fuzzy, true);
        where.andOperator(fuzzyWhere);

        List<T> all = mongoTemplate.find(new Query(where).with(sort).limit(maxTotalCnt), clazz, getCollectionName());
        if (DataUtil.isEmpty(all)) {
            return ListOutputDto.returnNull();
        }

        return ListOutputDto.page(all, page, limit);
    }

//    @Override
//    public void closeCursor(DBCursor cursor) {
//        cursor.close();
//    }

    @Override
    public void removeAll() {
        mongoTemplate.remove(new Query(), clazz, getCollectionName());
    }

    @Override
    public void removeByIds(List<String> ids) {
        Criteria where = Criteria.where(DataConstant.COLLECTION_BASE_ID).in(ids);
        mongoTemplate.remove(new Query(where), clazz, getCollectionName());
    }

    @Override
    public T findOneByIndex(Criteria criteria, Sort sort, int index) {
        return mongoTemplate.findOne(Query.query(criteria).with(sort).skip(index), clazz, getCollectionName());
    }

    @Override
    public long getCount(Criteria criteria) {
        return mongoTemplate.count(Query.query(criteria), clazz, getCollectionName());
    }

//    private void doBathUpdate(DBCollection dbCollection, String collName,
//                              List<BatchUpdateOption> options) {
//        if (CollectionUtils.isEmpty(options)) {
//            return;
//        }
//
//        int page = 1;
//        int totalPage = options.size() % BATCH_UPDATE_SIZE == 0 ? options.size() / BATCH_UPDATE_SIZE : (options.size() / BATCH_UPDATE_SIZE + 1);
//        while (page <= totalPage) {
//            List<BatchUpdateOption> curOptions = PagingUtil.page(options, page, BATCH_UPDATE_SIZE);
//            page++;
//            if (CollectionUtils.isEmpty(curOptions)) {
//                continue;
//            }
//
//            DBObject command = new BasicDBObject();
//            command.put("update", collName);
//            List<BasicDBObject> updateList = new ArrayList<BasicDBObject>();
//            for (BatchUpdateOption option : curOptions) {
//                BasicDBObject update = new BasicDBObject();
//                update.put("q", option.getQuery().getQueryObject());
//                update.put("u", option.getUpdate().getUpdateObject());
//                update.put("upsert", option.isUpsert());
//                update.put("multi", option.isMulti());
//                updateList.add(update);
//            }
//            command.put("updates", updateList);
//            command.put("ordered", false);
//            CommandResult commandResult = dbCollection.getDB().command(command);
//            if (!commandResult.ok()) {
//                log.warn("batchUpdate failed", commandResult.getException());
//                throw new DaoException(commandResult.getErrorMessage());
//            }
//        }
//    }
//
//

    /**
     * 保存
     *
     * @param collectionName
     * @param object
     */
    @Override
    public <T> T save(String collectionName, T object) {
        collectionName = getCollectionName(collectionName);

        boolean isExists = mongoTemplate.collectionExists(collectionName);

        if (!isExists) {
            mongoTemplate.createCollection(collectionName);
        }

        return mongoTemplate.save(object, collectionName);
    }

    /**
     * 更新或插入
     *
     * @param collectionName
     * @param object
     */
    @Override
    public void upsert(String collectionName, T criteria, T fuzzy, T object) {
        Criteria where = fields2criteria(criteria);
        Criteria fuzzyWhere = fields2criteria(fuzzy, true);
        where.andOperator(fuzzyWhere);
        Update update = fields2update(object);

        mongoTemplate.upsert(new Query(where), update, getCollectionName(collectionName));
    }

    /**
     * 删除记录
     *
     * @param collectionName
     * @param ids
     */
    @Override
    public void removeByIds(String collectionName, List<String> ids) {
        Criteria where = Criteria.where(DataConstant.COLLECTION_BASE_ID).in(getFilterIds(ids));
        mongoTemplate.remove(new Query(where), clazz, getCollectionName(collectionName));
    }

    @Override
    public List<T> getListByIds(String collectionName, List<String> idList) {
        Criteria where = Criteria.where(DataConstant.COLLECTION_BASE_ID).in(getFilterIds(idList));
        return mongoTemplate.find(new Query(where), clazz, getCollectionName(collectionName));
    }

    @Override
    public T findOneByIndex(String collectionName, T criteria, T fuzzy, Sort sort, int index) {
        Criteria where = fields2criteria(criteria);
        Criteria fuzzyWhere = fields2criteria(fuzzy, true);
        where.andOperator(fuzzyWhere);

        Query query = new Query(where);
        if (DataUtil.isNotEmpty(sort)) {
            query = query.with(sort);
        }
        query = query.skip(index);

        return mongoTemplate.findOne(query, clazz, getCollectionName(collectionName));
    }

    @Override
    public Long countByCriteria(String collectionName, T criteria, T fuzzy) {
        if (DataUtil.isEmpty(collectionName)) {
            collectionName = getCollectionName();
        }

        Criteria where = fields2criteria(criteria);
        Criteria fuzzyWhere = fields2criteria(fuzzy, true);
        where.andOperator(fuzzyWhere);

        return mongoTemplate.count(new Query(where), getCollectionName(collectionName));
    }

    @Override
    public List<T> pageByCriteria(String collectionName, T criteria, T fuzzy, Sort sort, Pageable pageable) {
        if (DataUtil.isEmpty(collectionName)) {
            collectionName = getCollectionName();
        }

        Criteria where = fields2criteria(criteria);
        Criteria fuzzyWhere = fields2criteria(fuzzy, true);
        where.andOperator(fuzzyWhere);

        Query query = new Query(where);
        if (DataUtil.isNotEmpty(sort)) {
            query = query.with(sort);
        }

        if (DataUtil.isNotEmpty(pageable)) {
            query = query.with(pageable);
        }

        return mongoTemplate.find(query, clazz, getCollectionName(collectionName));
    }

    @Override
    public T findOneByIndex(String collectionName, Criteria where, Sort sort, int index) {
        if (where == null) {
            where = new Criteria();
        }

        Query query = new Query(where);
        if (DataUtil.isNotEmpty(sort)) {
            query = query.with(sort);
        }
        query = query.skip(index);

        return mongoTemplate.findOne(query, clazz, getCollectionName(collectionName));
    }

    @Override
    public Long countByCriteria(String collectionName, Criteria where) {
        if (where == null) {
            where = new Criteria();
        }
        return mongoTemplate.count(new Query(where), getCollectionName(collectionName));
    }

    @Override
    public List<T> pageByCriteria(String collectionName, Criteria where, Sort sort, Pageable pageable) {
        if (where == null) {
            where = new Criteria();
        }

        Query query = new Query(where);
        if (DataUtil.isNotEmpty(sort)) {
            query = query.with(sort);
        }

        if (DataUtil.isNotEmpty(pageable)) {
            query = query.with(pageable);
        }

        return mongoTemplate.find(query, clazz, getCollectionName(collectionName));
    }

    /**
     * 返回指定的域
     *
     * @param collectionName
     * @param fieldsObject
     * @param where
     * @param sort
     * @param pageable
     * @return
     */
    @Override
    public List<T> getFieldsByCriteria(String collectionName, BasicDBObject fieldsObject, Criteria where, Sort sort, Pageable pageable) {
        // 查询条件
//
//        // 要查询的字段
//        BasicDBObject fieldsObject = new BasicDBObject();
//        fieldsObject.put("info.score", 1);
//        fieldsObject.put("info.checkupTime", 1);
//        fieldsObject.put(collectionBaseId, 0);

        Query query = new BasicQuery(where.getCriteriaObject().toJson(), fieldsObject.toJson());

        if (DataUtil.isNotEmpty(sort)) {
            query = query.with(sort);
        }

        if (DataUtil.isNotEmpty(pageable)) {
            query = query.with(pageable);
        }

        return mongoTemplate.find(query, clazz, getCollectionName(collectionName));
    }

    @Override
    public void delete(String collectionName, T criteria, T fuzzy) {
        if (DataUtil.isEmpty(collectionName)) {
            collectionName = getCollectionName();
        }

        Criteria where = fields2criteria(criteria);
        Criteria fuzzyWhere = fields2criteria(fuzzy, true);
        where.andOperator(fuzzyWhere);

        Query query = new Query(where);

        mongoTemplate.remove(query, getCollectionName(collectionName));
    }

    @Override
    public void delete(String collectionName, Criteria criteria) {
        if (DataUtil.isEmpty(collectionName)) {
            collectionName = getCollectionName();
        }

        Query query = new Query(criteria);
        mongoTemplate.remove(query, getCollectionName(collectionName));
    }

    // ============使用分装的查询条件进行查询=========================================================================================
    @Override
    public List<T> find(MetabaseQuery query, String collectionName) {
        return mongoTemplate.find(QueryHelper.toQuery(query), clazz, getCollectionName(collectionName));
    }

    @Override
    public long count(MetabaseQuery query, String collectionName) {
        return mongoTemplate.count(QueryHelper.toQuery(query), getCollectionName(collectionName));
    }

    @Override
    public ListOutputDto<T> page(MetabaseQuery query, String collectionName) {
        ListOutputDto<T> listOutputDto = new ListOutputDto<>();
        Integer limit = 0;
        if (DataUtil.isNotEmpty(query.getLimit())) {
            limit = query.getLimit();
            query.setLimit(0);
        }
        Long count = count(query, getCollectionName(collectionName));
        listOutputDto.setTotalCount(count.intValue());
        if (count == 0L) {
            return listOutputDto;
        }
        query.setLimit(limit);
        List<T> rows = find(query, getCollectionName(collectionName));
        listOutputDto.setRows(rows);
        return listOutputDto;
    }

    @Override
    public Boolean existBy(MetabaseQuery query, String collectionName) {
        return mongoTemplate.exists(QueryHelper.toQuery(query), getCollectionName(collectionName));
    }

    @Override
    public T findOne(MetabaseQuery query, String collectionName) {
        return mongoTemplate.findOne(QueryHelper.toQuery(query), clazz, getCollectionName(collectionName));
    }

    @Override
    public void update(String id, ResourceUpdates updates, String collectionName) {
        if (DataUtil.isEmpty(updates) || DataUtil.isEmpty(updates.getSetMap())) {
            return;
        }
        Criteria where = Criteria.where(DataConstant.COLLECTION_BASE_ID).is(getFilterId(id));
        mongoTemplate.updateFirst(Query.query(where), QueryHelper.toUpdate(updates), getCollectionName(collectionName));
    }

    @Override
    public void update(MetabaseQuery query, ResourceUpdates updates, boolean upsert, String collectionName) {
        if (DataUtil.isEmpty(updates) || DataUtil.isEmpty(updates.getSetMap())) {
            return;
        }
        if (CollectionUtils.isEmpty(query.getCriteria())) {
            throw new ServiceException("empty criteria!");
        }
        if (upsert) {
            mongoTemplate.upsert(QueryHelper.toQuery(query), QueryHelper.toUpdate(updates), clazz, getCollectionName(collectionName));
        } else {
            mongoTemplate.updateMulti(QueryHelper.toQuery(query), QueryHelper.toUpdate(updates), clazz, getCollectionName(collectionName));
        }
    }

    @Override
    public void delete(MetabaseQuery query, String collectionName) {
        mongoTemplate.remove(QueryHelper.toQuery(query), getCollectionName(collectionName));
    }

    /**
     * 分组查询
     *
     * @param
     * @return
     * @Comments: <对此方法的描述，可以引用系统设计中的描述>
     * <AUTHOR> [<EMAIL>]
     * @since 2021-08-10 9:56
     */
    @Override
    public List<AggregationDto> group(Criteria criteria, GroupDto groupDto, String field, Integer sort, Integer page, Integer limit) throws Exception {
       return this.group(criteria,groupDto,collectionName,field,sort,page,limit);
    }

    @Override
    public List<AggregationDto> group(Criteria criteria, GroupDto groupDto, String collection, String field, Integer sort, Integer page, Integer limit) throws Exception {
        List<AggregationOperation> operations = new ArrayList<>();

        //查询条件
        if (DataUtil.isNotEmpty(criteria)) {
            operations.add(Aggregation.match(criteria));
        }
        //展开字段
        if (DataUtil.isNotEmpty(groupDto.getUnwind()) && !"entities.type".equals(groupDto.getGroupFields()[0])) {
            operations.add(Aggregation.unwind(groupDto.getUnwind()));
        }
        //分组
        operations.add(GroupUtils.getGroupOperation(groupDto).as("resultAlias"));
        //排序
        if (DataUtil.isNotEmpty(sort)) {

            if (sort == ConstantUtil.Sort.ASC) {
                operations.add(Aggregation.sort(org.springframework.data.domain.Sort.Direction.ASC, "resultAlias").and(Sort.by(org.springframework.data.domain.Sort.Direction.ASC, "_id")));
            } else if (sort == ConstantUtil.Sort.DESC) {
                operations.add(Aggregation.sort(org.springframework.data.domain.Sort.Direction.DESC, "resultAlias").and(Sort.by(org.springframework.data.domain.Sort.Direction.ASC, "_id")));
            } else {
                operations.add(Aggregation.sort(org.springframework.data.domain.Sort.Direction.DESC, "resultAlias").and(Sort.by(org.springframework.data.domain.Sort.Direction.ASC, "_id")));
            }

        }
        //当前页
        if (DataUtil.isNotEmpty(page)) {

            operations.add(Aggregation.skip((page - 1) * limit));
        }
        //每页显示条数
        if (DataUtil.isNotEmpty(limit)) {

            operations.add(Aggregation.limit(limit));
        }

        Aggregation aggregation = Aggregation.newAggregation(operations).withOptions(newAggregationOptions().allowDiskUse(true).build());

        return mongoTemplate.aggregate(aggregation, collection, AggregationDto.class).getMappedResults();
    }

    @Override
    public List<AggregationListDto> groupList(Criteria criteria, GroupDto groupDto, String field, Integer sort, Integer page, Integer limit) throws Exception {

        List<AggregationOperation> operations = new ArrayList<>();

        //查询条件
        if (DataUtil.isNotEmpty(criteria)) {
            operations.add(Aggregation.match(criteria));
        }
        //展开字段
        if (DataUtil.isNotEmpty(groupDto.getUnwind())) {
            operations.add(Aggregation.unwind(groupDto.getUnwind()));
        }
        //分组
        operations.add(GroupUtils.getGroupOperation(groupDto).as("resultAlias"));
        //排序
        if (DataUtil.isNotEmpty(sort)) {

            if (sort == ConstantUtil.Sort.ASC) {
                operations.add(Aggregation.sort(org.springframework.data.domain.Sort.Direction.ASC, "resultAlias").and(Sort.by(org.springframework.data.domain.Sort.Direction.ASC, "_id")));
            } else if (sort == ConstantUtil.Sort.DESC) {
                operations.add(Aggregation.sort(org.springframework.data.domain.Sort.Direction.DESC, "resultAlias").and(Sort.by(org.springframework.data.domain.Sort.Direction.ASC, "_id")));
            } else {
                operations.add(Aggregation.sort(org.springframework.data.domain.Sort.Direction.DESC, "resultAlias").and(Sort.by(org.springframework.data.domain.Sort.Direction.ASC, "_id")));
            }

        }
        //当前页
        if (DataUtil.isNotEmpty(page)) {

            operations.add(Aggregation.skip((page - 1) * limit));
        }
        //每页显示条数
        if (DataUtil.isNotEmpty(limit)) {

            operations.add(Aggregation.limit(limit));
        }

        Aggregation aggregation = Aggregation.newAggregation(operations).withOptions(newAggregationOptions().allowDiskUse(true).build());

        return mongoTemplate.aggregate(aggregation, collectionName, AggregationListDto.class).getMappedResults();
    }

    @Override
    public <T> List<T> findDistinctByField(String field, String collection, Class<T> resultClass) {
        return mongoTemplate.findDistinct(new Query(), field, collection, resultClass);
    }
}
