package com.quanzhi.auditapiv2.common.dal.dto.risk;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @Description 风险等级配置
 * @Version
 * @createTime 2023/2/20 4:58 PM
 */
@Data
public class RiskLevelSearchDto {
    // 风险等级类型：API、IP、ACCOUNT
    // @See com.quanzhi.auditapiv2.core.model.SubjectType
    private String levelType;

    /**
     * 当前页
     */
    @ApiModelProperty(value = "当前页", name = "page")
    private Integer page = 1;

    /**
     * 每页显示条数
     */
    @ApiModelProperty(value = "每页显示条数", name = "limit")
    private Integer limit = 10;

}
