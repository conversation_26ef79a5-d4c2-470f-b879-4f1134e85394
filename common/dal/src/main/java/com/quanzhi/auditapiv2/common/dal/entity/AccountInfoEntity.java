package com.quanzhi.auditapiv2.common.dal.entity;

import com.quanzhi.auditapiv2.common.dal.entity.account.StatPoint;
import com.quanzhi.metabase.core.model.dto.RiskLevelMatchDto;
import com.quanzhi.metabase.core.model.http.HttpResourceConstant;
import com.quanzhi.metabase.core.model.node.NodeMeta;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.data.mongodb.core.mapping.Document;

import java.io.Serializable;
import java.util.List;
import java.util.Map;
import java.util.Set;

@Data
@Document(HttpResourceConstant.ACCOUNT_INFO)
@AllArgsConstructor
@NoArgsConstructor
public class AccountInfoEntity implements Serializable {

    private Set<String> uaTypes;
    private Set<String> rspDataLabelList;
    private Set<String> appUriList;
    private Map<String, Set<String>> uaByUaType;
    private StatPoint statPoint;
    private long visitCnt;
    private String firstDate;
    private String lastDate;
    private RiskLevelMatchDto.Risk riskInfo;
    private String riskLevelName;
    private Integer riskLevel;
    private Long reviveTime;
    private Long rspDataDistinctCnt;
    private String account;
    private Set<String> apiUriList;
    private String staffDepart;
    private String staffNickName;
    private String staffName;
    private String staffChinese;
    private String staffIdCard;
    private String staffBankCard;
    private String staffEmail;
    private String staffId;
    private String staffMobile;
    private String staffRole;
    private Set<String> relatedIpList;
    private Long relatedIpDistinctCnt;
    private List<NodeMeta> nodes;
    private Integer strategyStatus = 0;

    public String getAppUri() {
        return this.getAppUriList().stream().findAny().orElse(null);
    }
}
