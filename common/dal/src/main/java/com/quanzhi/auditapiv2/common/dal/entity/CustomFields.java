package com.quanzhi.auditapiv2.common.dal.entity;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @author: yangzixian
 * @date: 28/3/2023 15:31
 * @description:
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class CustomFields {

    private String fieldName;

    private String fieldType;

    private String fieldExplain;

    private String fieldExample;

    private String type;

    private Boolean isDefault;

    private Boolean isUdp;

    private String originTypeName;

    private String originType;

}
