package com.quanzhi.auditapiv2.common.dal.dao.common;

import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;

/**
 * <AUTHOR>
 * @date 2018/1/3.
 */
@Data
@NoArgsConstructor
public class BatchUpdateOption {
    private Query query;
    private Update update;
    private boolean upsert = false;
    private boolean multi = false;

    public BatchUpdateOption(Boolean multi, Boolean upsert) {
        this.multi = multi;
        this.upsert = upsert;
    }
}
