package com.quanzhi.auditapiv2.common.dal.dto.query;

import com.quanzhi.auditapiv2.common.dal.dto.ExporTitleFieldDto;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * @ClassName RiskInfoAggCriteriaDto
 * @Description TODO
 * <AUTHOR>
 * @Date 2020/11/23 11:02
 **/
@Data
public class RiskInfoAggCriteriaDto implements Serializable {

    private Integer level;

    /**
     * 异常时间
     */
    private Long riskTime;

    /**
     * 最近发现时间
     */
    private Long lastTime;

    /**
     * 异常状态
     * @see RiskStateEnum
     */
    private Integer state;

    /**
     * 异常规则中文
     */
    @Deprecated
    private String name;

    /**
     * 异常时间范围查询
     */
    private Long startTime;

    private Long endTime;

    /**
     * 异常主体
     */
    private Object value;

    /**
     * 导出字段
     */
    private List<ExporTitleFieldDto> list;

    private Integer page;

    private Integer limit;

    private String field;

    private Integer sort;

    /**
     * 异常规则英文查询
     */
    private String definition;

    /**
     * oa帐号
     */
    private String applicant_id;
    /**
     * 数据内容
     */
    private String tags;
    /**
     * 导出原因
     */
    private String reasons;
    /**
     * 使用范围
     */
    private String usage;
}
