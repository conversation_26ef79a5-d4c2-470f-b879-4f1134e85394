package com.quanzhi.auditapiv2.common.dal.dao.impl.data;

import com.mongodb.client.result.UpdateResult;
import com.quanzhi.auditapiv2.common.dal.dao.base.impl.BaseDaoImpl;
import com.quanzhi.auditapiv2.common.dal.dao.data.DataDao;
import com.quanzhi.auditapiv2.common.dal.dto.data.DataInfoOperatorDto;
import com.quanzhi.auditapiv2.common.dal.dto.data.FocusDataDto;
import com.quanzhi.auditapiv2.common.dal.dto.home.DataFlowInfoDto;
import com.quanzhi.auditapiv2.common.dal.entity.data.DataInfo;
import com.quanzhi.auditapiv2.common.dal.entity.data.DataMonthInfo;
import com.quanzhi.auditapiv2.common.util.dto.AggregationDto;
import com.quanzhi.auditapiv2.common.util.dto.GroupDto;
import com.quanzhi.auditapiv2.common.util.utils.ConstantUtil;
import com.quanzhi.auditapiv2.common.util.utils.DataUtil;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.stereotype.Repository;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;

/**
 * @Author: yangzx
 * @Date: 2024/10/9 14:25
 */
@Repository
public class DataDaoImpl extends BaseDaoImpl<DataInfo> implements DataDao {

    private final MongoTemplate mongoTemplate;

    private final String collectionName = "dataLabelMonthInfo";

    private final String collectionName_focus = "focusData";

    private final String collectionName_data = "dataInfo";

    public DataDaoImpl(MongoTemplate mongoTemplate) {
        this.mongoTemplate = mongoTemplate;
    }

    @Override
    public List<DataInfo> listData(DataInfoOperatorDto dataInfoOperatorDto) {
        Integer limit = dataInfoOperatorDto.getLimit();
        Integer page = dataInfoOperatorDto.getPage();
        Integer sort = dataInfoOperatorDto.getSort();
        String field = dataInfoOperatorDto.getSortField();
        Criteria criteria = getCriteria(dataInfoOperatorDto);
        //分页
        Pageable pageable = PageRequest.of(page - 1, limit);
        //排序
        Sort dbSort = null;
        if (com.quanzhi.audit_core.common.utils.DataUtil.isNotEmpty(field) && com.quanzhi.audit_core.common.utils.DataUtil.isNotEmpty(sort)) {

            if (sort == ConstantUtil.Sort.ASC) {
                dbSort = Sort.by(Sort.Order.desc("markState"), Sort.Order.asc(field), Sort.Order.desc("_id"));
            } else if (sort == ConstantUtil.Sort.DESC) {
                dbSort = Sort.by(Sort.Direction.DESC, "markState", field, "_id");
            } else {
                dbSort = Sort.by(Sort.Direction.DESC, "markState", field, "_id");
            }
        } else {
            dbSort = Sort.by(Sort.Direction.DESC, "markState", "_id");
        }
        return mongoTemplate.find(new Query().addCriteria(criteria).with(pageable).with(dbSort), DataInfo.class, collectionName_data);
    }

    @Override
    public List<AggregationDto> groupData(DataInfoOperatorDto dataInfoOperatorDto, GroupDto groupDto) throws Exception {
        return group(getCriteria(dataInfoOperatorDto), groupDto, dataInfoOperatorDto.getSortField(), dataInfoOperatorDto.getSort(), dataInfoOperatorDto.getPage(), dataInfoOperatorDto.getLimit());
    }

    @Override
    public Long totalCount(DataInfoOperatorDto dataInfoOperatorDto) {
        return mongoTemplate.count(new Query().addCriteria(getCriteria(dataInfoOperatorDto)), collectionName_data);
    }

    @Override
    public String markData(DataInfoOperatorDto dataInfoOperatorDto) {
        Update update = new Update();
        update.set("markState", dataInfoOperatorDto.getMarkState());
        UpdateResult result = mongoTemplate.updateFirst(new Query().addCriteria(Criteria.where("_id").is(dataInfoOperatorDto.getId())), update, collectionName_data);
        mongoTemplate.updateFirst(new Query().addCriteria(Criteria.where("dataId").is(dataInfoOperatorDto.getId())), update, collectionName);
        if (result.getModifiedCount() != 0) {
            return "success";
        } else {
            return "fail";
        }
    }

    @Override
    public String batchMarkData(DataInfoOperatorDto dataInfoOperatorDto) {
        Update update = new Update();
        update.set("markState", dataInfoOperatorDto.getMarkState());
        UpdateResult result = null;
        if (dataInfoOperatorDto.getIds() != null) {
            result = mongoTemplate.updateMulti(new Query().addCriteria(Criteria.where("_id").in(dataInfoOperatorDto.getIds())), update, collectionName_data);
        } else {
            dataInfoOperatorDto.setMarkState(null);
            Criteria criteria = getCriteria(dataInfoOperatorDto);
            result = mongoTemplate.updateMulti(new Query().addCriteria(criteria), update, collectionName_data);
        }
        if (result.getModifiedCount() != 0) {
            return "success";
        } else {
            return "fail";
        }
    }

    @Override
    public FocusDataDto getFocusData() {
        return mongoTemplate.findOne(new Query().addCriteria(Criteria.where("name").is("关注数据")), FocusDataDto.class, collectionName_focus);
    }

    @Override
    public FocusDataDto updateFocusData(FocusDataDto focusDataDto) {
        Update update = new Update();
        update.set("focusData", focusDataDto.getFocusData());
        mongoTemplate.updateFirst(new Query().addCriteria(Criteria.where("name").is("关注数据")), update, collectionName_focus);
        return focusDataDto;
    }

    @Override
    public DataFlowInfoDto getDataFlow(List<String> focusDatas, String type) {
        Map<String, Map<String, Long>> dataLabelVisit = new HashMap<>();
        //获取数据
        Set<String> months = getLast30Days();
        Criteria criteria = new Criteria();
        criteria.and("month").in(months);
        criteria.and("dataId").in(focusDatas);
        List<DataMonthInfo> dataMonthInfos = mongoTemplate.find(new Query().addCriteria(criteria), DataMonthInfo.class, collectionName);

        for (String dataId : focusDatas) {
            dataLabelVisit.put(dataId, getLast30DaysMap());
        }

        for (DataMonthInfo dataMonthInfo : dataMonthInfos) {
            Map<String, Long> dateMap = dataLabelVisit.get(dataMonthInfo.getDataId());
            switch (type) {
                case "req":
                    for (String day : dataMonthInfo.getReqDataDateStats().keySet()) {
                        String keyDay = day;
                        if (day.length() == 1) {
                            keyDay = "0" + keyDay;
                        }
                        String yyyyMMdd = dataMonthInfo.getMonth() + keyDay;
                        if (dateMap.containsKey(yyyyMMdd)) {
                            dateMap.put(yyyyMMdd, dataMonthInfo.getReqDataDateStats().get(day).getAmount());
                        }
                    }
                    break;
                case "rsp":
                    for (String day : dataMonthInfo.getRspDataDateStats().keySet()) {
                        String keyDay = day;
                        if (day.length() == 1) {
                            keyDay = "0" + keyDay;
                        }
                        String yyyyMMdd = dataMonthInfo.getMonth() + keyDay;
                        if (dateMap.containsKey(yyyyMMdd)) {
                            dateMap.put(yyyyMMdd, dataMonthInfo.getRspDataDateStats().get(day).getAmount());
                        }
                    }
                    break;
                default:
                    break;
            }
        }

        return DataFlowInfoDto.builder().dataLabelVisit(dataLabelVisit).build();
    }

    public Set<String> getLast30Days() {
        //创建一个Map存储结果，key是yyyyMM，value是Map<String, Long>，每个Map表示当天和0L
        Set<String> dateList = new HashSet<>();

        //获取当前日期
        LocalDate today = LocalDate.now();

        //定义日期格式为yyyyMM和dd
        DateTimeFormatter yearMonthFormatter = DateTimeFormatter.ofPattern("yyyyMM");

        //循环获取过去30天的日期，包括今天
        for (int i = 0; i < 30; i++) {
            //获取当天日期减去i天的日期
            LocalDate date = today.minusDays(i);

            //格式化为yyyyMM和dd
            String yearMonth = date.format(yearMonthFormatter);

            dateList.add(yearMonth);
        }
        return dateList;
    }

    public Map<String, Long> getLast30DaysMap() {
        //创建一个Map存储结果，key是yyyyMMdd
        Map<String, Long> dateMap = new LinkedHashMap<>();

        //获取当前日期
        LocalDate today = LocalDate.now();

        //定义日期格式为yyyyMMdd
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyyMMdd");

        //循环获取过去30天的日期，包括今天
        for (int i = 0; i < 30; i++) {
            //获取当天日期减去i天的日期
            LocalDate date = today.minusDays(i);

            //格式化为yyyyMMdd
            String ymd = date.format(formatter);

            //将当天和0L作为键值对放入对应的Map中
            dateMap.put(ymd, 0L);
        }
        return dateMap;
    }

    @Override
    public Criteria getCriteria(DataInfoOperatorDto dataInfoOperatorDto) {
        Criteria criteria = new Criteria();
//        criteria.and("delFlg").is(false);
        List<Criteria> andCriteriaList = new ArrayList<>();
        if (DataUtil.isNotEmpty(dataInfoOperatorDto)) {
            if (DataUtil.isNotEmpty(dataInfoOperatorDto.getDataName())) {
                criteria.and("dataName").is(dataInfoOperatorDto.getDataName());
            }
            if (DataUtil.isNotEmpty(dataInfoOperatorDto.getDataType())) {
                criteria.and("dataType").in(dataInfoOperatorDto.getDataType());
            }
            if (DataUtil.isNotEmpty(dataInfoOperatorDto.getDataLevel())) {
                criteria.and("dataLevel").in(dataInfoOperatorDto.getDataLevel());
            }
            if (DataUtil.isNotEmpty(dataInfoOperatorDto.getDataRiskLevel())) {
                criteria.and("dataRiskLevel").is(dataInfoOperatorDto.getDataRiskLevel());
            }
            if (DataUtil.isNotEmpty(dataInfoOperatorDto.getHost())) {
                criteria.and("relatedApps").is(dataInfoOperatorDto.getHost());
            }
            if (DataUtil.isNotEmpty(dataInfoOperatorDto.getAppName())) {
                criteria.and("appName").regex(com.quanzhi.auditapiv2.common.util.utils.DataUtil.regexStrEscape(dataInfoOperatorDto.getAppName()));
            }
            if (DataUtil.isNotEmpty(dataInfoOperatorDto.getMarkState())) {
                criteria.and("markState").is(dataInfoOperatorDto.getMarkState());
            }
            if (DataUtil.isNotEmpty(dataInfoOperatorDto.getFirstTimeStart()) && DataUtil.isNotEmpty(dataInfoOperatorDto.getFirstTimeEnd())) {
                Criteria andCriteria = new Criteria();
                andCriteria.andOperator(Criteria.where("firstTime").gte(dataInfoOperatorDto.getFirstTimeStart()), Criteria.where("firstTime").lte(dataInfoOperatorDto.getFirstTimeEnd()));
                andCriteriaList.add(andCriteria);
            }
            if (DataUtil.isNotEmpty(dataInfoOperatorDto.getLastTimeStart()) && DataUtil.isNotEmpty(dataInfoOperatorDto.getLastTimeEnd())) {
                Criteria andCriteria = new Criteria();
                andCriteria.andOperator(Criteria.where("lastTime").gte(dataInfoOperatorDto.getLastTimeStart()), Criteria.where("lastTime").lte(dataInfoOperatorDto.getLastTimeEnd()));
                andCriteriaList.add(andCriteria);
            }
        }
        if (!andCriteriaList.isEmpty()) {
            criteria.andOperator(andCriteriaList.toArray(new Criteria[andCriteriaList.size()]));
        }
        return criteria;
    }

    @Override
    public List<DataMonthInfo> getAllDataMonthInfo() {
        return mongoTemplate.find(new Query(), DataMonthInfo.class, collectionName);
    }

    @Override
    public void upsertDataInfo(DataInfo dataInfo) {
        Update update = new Update();
        update.setOnInsert("dataId", dataInfo.getDataId());
        update.setOnInsert("firstTime", dataInfo.getCreateDate().getTime());
        update.setOnInsert("markState", false);
        update.setOnInsert("delFlag", false);
        update.set("dataName", dataInfo.getDataName());
        update.set("nodes", dataInfo.getNodes());
        update.set("dataType", dataInfo.getDataType());
        update.set("dataTypeName", dataInfo.getDataTypeName());
        update.set("lastTime", dataInfo.getLastTime());
        update.set("totalCount", dataInfo.getTotalCount());
        update.set("reqCount", dataInfo.getReqCount());
        update.set("rspCount", dataInfo.getRspCount());
        update.set("dataRiskLevel", dataInfo.getDataRiskLevel());
        update.set("dataRiskLevelName", dataInfo.getDataRiskLevelName());
        update.set("riskNum", dataInfo.getRelatedRisks().size());
        update.set("relatedRisks", dataInfo.getRelatedRisks());
        update.set("dataLevel", dataInfo.getDataLevel());
        update.set("dataLevelName", dataInfo.getDataLevelName());
        update.set("ipNum", dataInfo.getIpNum());
        update.set("accountNum", dataInfo.getAccountNum());
        update.set("appNum", dataInfo.getAppNum());
        mongoTemplate.upsert(new Query().addCriteria(Criteria.where("_id").is(dataInfo.getDataId())), update, collectionName_data);
    }

    @Override
    public DataInfo getDataInfoByDataId(String dataId) {
        return mongoTemplate.findOne(new Query().addCriteria(Criteria.where("dataId").is(dataId)), DataInfo.class, collectionName_data);
    }

}
