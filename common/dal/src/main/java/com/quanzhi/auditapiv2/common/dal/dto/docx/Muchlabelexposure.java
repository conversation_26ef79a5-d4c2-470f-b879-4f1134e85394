package com.quanzhi.auditapiv2.common.dal.dto.docx;

import lombok.Data;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 *
 * 《单次返回类型过多》
 *
 *
 * @Project:
 * @Module ID:
 * @Comments:
 * @JDK version used:      <JDK1.8>
 * <AUTHOR> [<EMAIL>]
 * @since 2020-08-28-下午16:52:18
 */
@Data
public class Muchlabelexposure extends ApiWeaknessExportWord {

    @Mapper
    public interface MuchlabelexposureMapper {

        Muchlabelexposure.MuchlabelexposureMapper INSTANCE = Mappers.getMapper(Muchlabelexposure.MuchlabelexposureMapper.class);
        Muchlabelexposure convert(ApiWeaknessExportWord apiWeaknessExportWord);
    }
}
