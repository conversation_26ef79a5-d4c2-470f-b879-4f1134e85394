package com.quanzhi.auditapiv2.common.dal.dto.systemSecurity;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * @author: yang<PERSON><PERSON>n
 * @date: 2022/7/18 17:57
 * @description:
 */
@Data
public class SystemSecurityDto {

    @ApiModelProperty("id")
    private String id;

    /**
     * @see com.quanzhi.auditapiv2.common.dal.entity.systemSecurity.SystemSecurity.ListTypeEnum
     */
    @ApiModelProperty("名单类型")
    private Integer listType;

    @ApiModelProperty("名单内容")
    private List<String> listValue;

    @ApiModelProperty("创建时间")
    private Long createTime;

    @ApiModelProperty("更新时间")
    private Long updateTime;

}
