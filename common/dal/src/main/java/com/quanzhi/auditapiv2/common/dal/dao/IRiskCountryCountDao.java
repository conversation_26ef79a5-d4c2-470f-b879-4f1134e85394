package com.quanzhi.auditapiv2.common.dal.dao;

import com.quanzhi.auditapiv2.common.dal.dao.base.IBaseDao;
import com.quanzhi.auditapiv2.common.dal.entity.RiskCountryCount;

import java.util.List;
import java.util.Set;

/**
 * 《风险国家分布统计持久层接口》
 *
 * <AUTHOR> [<EMAIL>]
 * @Project:
 * @Module ID:
 * @Comments:
 * @JDK version used:      <JDK1.8>
 * @since 2020-07-01-上午9:56:10
 */
public interface IRiskCountryCountDao extends IBaseDao<RiskCountryCount> {

    /**
     * 国家查询风险国家分布统计详情
     *
     * @param
     * @return
     * @Comments: <对此方法的描述，可以引用系统设计中的描述>
     * <AUTHOR> [<EMAIL>]
     * @since 2020-07-01 9:56
     */
    RiskCountryCount getRiskCountryCountByCountry(String country, String groupId) throws Exception;

    /**
     * 获取风险国家分布信息
     *
     * @return
     */
    List<RiskCountryCount> getRiskCountryCount(String userGroupId);

    /**
     * 保存风险国家分布统计
     *
     * @param
     * @return
     * @Comments: <对此方法的描述，可以引用系统设计中的描述>
     * <AUTHOR> [<EMAIL>]
     * @since 2020-07-01 9:56
     */
    RiskCountryCount saveRiskCountryCount(RiskCountryCount riskCountryCount, String groupId) throws Exception;

    /**
     * 更新保存的风险国家分布信息
     *
     * @param riskCountryCount
     * @return
     */
    void updateRiskCountryCount(RiskCountryCount riskCountryCount, String groupId);

    void saveRiskCountryCountByDataReveal(RiskCountryCount riskCountryCount);

    List<RiskCountryCount> getRiskCountryCountByDataReveal();

    void dealNoConfirmCountry(Set<String> nowCountrys);

    void clear();
}
