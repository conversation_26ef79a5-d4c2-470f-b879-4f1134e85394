package com.quanzhi.auditapiv2.common.dal.dao;


import com.quanzhi.auditapiv2.common.dal.dto.AuditLogSearchDto;
import com.quanzhi.auditapiv2.common.dal.entity.AuditLogModel;
import org.springframework.data.mongodb.core.query.Criteria;

import java.util.List;

/**
 * Created by she<PERSON><PERSON> on 2018/1/3.
 */
public interface IAuditLogDao {

    /**
     * 解析筛选条件
     * @param searchDto
     * @return
     */
    Criteria getCriteriaBySearch(AuditLogSearchDto searchDto);

    /**
     * 根据筛选条件获取记录数
     * @param searchDto
     * @return
     */
    Long getCount(AuditLogSearchDto searchDto);

    /**
     * 根据筛选条件获取记录集
     * @param searchDto
     * @param start
     * @param limit
     * @return
     */
    List<AuditLogModel> getList(AuditLogSearchDto searchDto, Integer start, Integer limit);

    /**
     * 插入审计日志
     * @param auditLogModel
     */
    void insertLog(AuditLogModel auditLogModel);
}
