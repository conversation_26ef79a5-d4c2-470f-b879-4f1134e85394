package com.quanzhi.auditapiv2.common.dal.dao;

import com.mongodb.client.MongoCursor;
import com.quanzhi.auditapiv2.common.dal.entity.GatewayMonitorData;
import org.bson.Document;

import java.util.List;

public interface IGatewayMonitorDataDao {

    List<GatewayMonitorData> getList(Long endTime);

    List<GatewayMonitorData> getList(Long startTime, Long endTime);

    List<GatewayMonitorData> getList(Long startTime, Long endTime, Integer start, Integer limit);

    Long getCount(Long start, Long end);

    Long countByIpAndTime(String ip, Long startTime, Long endTime);

    GatewayMonitorData getGatewayDataByTime(Long startTime, Long endTime);

    MongoCursor<Document> getCursor(Long start, Long end);

    MongoCursor<Document> getCursor(String ip, Long startTime, Long endTime);

    GatewayMonitorData getLatestByIp(String gatewayIp, Long endTime);

    void insertGatewayMonitorData(List<GatewayMonitorData> gateway);
}
