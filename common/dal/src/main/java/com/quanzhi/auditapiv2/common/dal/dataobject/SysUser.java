package com.quanzhi.auditapiv2.common.dal.dataobject;

import com.quanzhi.auditapiv2.common.dal.annotation.NoReflection;
import com.quanzhi.auditapiv2.common.dal.entity.SysUserModel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.index.Indexed;
import org.springframework.data.mongodb.core.mapping.Document;

import java.io.Serializable;
import java.util.Collections;
import java.util.List;

/**
 * @Description:
 * @Author: danniel.yu
 * @Date: 2020-05-11 15:24
 */
@ApiModel(description = "系统用户")
@Data
@Document
public class SysUser implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 用户id
     */
    @ApiModelProperty(value = "用户Id")
    @Id
    private String id;

    /**
     * 用户名
     */
    @ApiModelProperty("用户名")
    @Indexed(unique = true, background = true)
    private String username;

    /**
     * 密码
     */
    @ApiModelProperty("密码")
    private String password;

    /**
     * google 身份认证密钥
     */
    @ApiModelProperty("google 身份认证密钥")
    private String googleAuthSecret;

    /**
     * 密钥二维码字符串
     */
    @ApiModelProperty("密钥二维码字符串")
    private String googleSecretQRCode;

    /**
     * UAC用户名
     */
    @ApiModelProperty("UAC用户名")
    private String uac;

    /**
     * 用户类型（1-系统管理员，2-审计员，3-普通用户, 4-工具管理员）
     */
    @ApiModelProperty("用户类型（1-系统管理员，2-审计员，3-普通用户, 4-工具管理员）")
    private Integer type;

    /**
     * 状态(0：禁用   1：正常)
     */
    @ApiModelProperty("状态(0：禁用   1：正常)")
    private Integer status;

    /**
     * 备注
     */
    @ApiModelProperty("备注")
    private String remark;

    /**
     * 创建时间
     */
    @ApiModelProperty("创建时间")
    private Long gmtCreate;

    /**
     * 创建者id
     */
    @ApiModelProperty("创建者id")
    private String userIdCreate;

    /**
     * 创建者name
     */
    @ApiModelProperty("创建者name")
    private String userNameCreate;

    /**
     * 修改时间
     */
    @ApiModelProperty("修改时间")
    private Long gmtModified;

    @ApiModelProperty("密码更新时间")
    private Long pwdModifiedTime;

    /**
     * 角色id
     */
    @ApiModelProperty("角色id")
    private String roleId;

    /**
     * 角色名称
     */
    @ApiModelProperty("角色名称")
    private String roleName;

    /**
     * 邮箱
     */
    @ApiModelProperty("邮箱")
    private String email;

    /**
     * 解锁时间
     */
    @ApiModelProperty("解锁时间")
    private Long lockTime;

    /**
     * 有效时间
     */
    @ApiModelProperty("有效时间")
    private Long validTime;

    /**
     * 错误次数
     */
    @ApiModelProperty("错误次数")
    private Integer errorCount;

    /**
     * 是否初始密码
     * null/false - 初始密码，未修改，true - 已修改初始密码
     */
    @ApiModelProperty("是否已修改初始密码")
    private Boolean updatedPassword;

    /**
     * 环境标识，与配置文件中保持一致：1 - 默认使用api产品，2 - 使用出境工具的网关配置
     */
    @ApiModelProperty("环境标识，与配置文件中保持一致：1 - 默认使用api产品，2 - 使用出境工具的网关配置")
    private Integer envFlag;

    /**
     * 是否展示
     */
    @ApiModelProperty("是否展示")
    private Boolean showFlag;

    private String resource;

    private String openId;

    @NoReflection
    public static final String SSO_USER_PREFIX = "SSO-";

    /**
     * 是否内置 true:内置
     */
    private Boolean defaultFlag;
    /**
     * 数据分组ids
     */
    private List<String> dataGroupIds;

    public static SysUser createSSOUser(String username){
        SysUser sysUser = new SysUser();
        sysUser.setUsername(username);
        sysUser.setId(username);
        sysUser.setType(SysUser.TypeEnum.WEBADMIN.value());
        sysUser.setStatus(SysUser.StatusEnum.RESTORE.value());
        sysUser.setUpdatedPassword(false);
        sysUser.setPassword("****** secure ****** ");
        return sysUser;
    }

    public static SysUserModel createSSOUserModel(String username){
        return SysUserModel.createSSOUserModel(username);
    }

    public static SysRole createSSORole(){
        return SysRole.createSSORole();
    }

    /**
     * 用户类型枚举类
     */
    public static enum TypeEnum {

        /**
         * 系统管理员
         */
        SYSADMIN(1),

        /**
         * 审计员
         */
        AUDITOR(2),

        /**
         * 普通用户
         */
        WEBADMIN(3),

        /**
         * 工具管理员
         */
        RISKEVAL(4),

        /**
         * 运维管理员
         */
        WORKER(5);

        private int val;

        TypeEnum(int val) {
            this.val = val;
        }

        public int value() {
            return this.val;
        }

        public static SysUser.TypeEnum getTypeEnum(int val) {

            for (SysUser.TypeEnum typeEnum : SysUser.TypeEnum.values()) {
                if(typeEnum.value() == val) {
                    return typeEnum;
                }
            }
            return null;
        }
    }

    /**
     * 状态枚举类
     */
    public static enum StatusEnum {

        /**
         * 禁用
         */
        DISABLED(0),

        /**
         * 正常
         */
        RESTORE(1);

        private int val;

        StatusEnum(int val) {
            this.val = val;
        }

        public int value() {
            return this.val;
        }
    }
}
