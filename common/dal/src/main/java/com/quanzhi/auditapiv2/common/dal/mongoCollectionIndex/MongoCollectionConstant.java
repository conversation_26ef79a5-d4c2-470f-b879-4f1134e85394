package com.quanzhi.auditapiv2.common.dal.mongoCollectionIndex;

/**
 * <AUTHOR>
 * @date 2020/11/16 上午11:11
 */
public interface MongoCollectionConstant {

    /**
     * ip表
     */
    String IP_INFO = "ipInfo";

    String IP_LAST_INFO = "ipLastInfo";

    /**
     * ip按天统计表
     */
    String IP_DATE_INFO = "ipDateInfo";

    /**
     * apiIpDate 指标表
     */
    String API_IP_DATE_INFO = "apiIpDateInfo";

    /**
     * 账号表
     */
    String ACCOUNT_INFO = "accountInfo";

    /**
     * 爱音乐应用阻断规则表
     */
    String APP_RULE_CONFIG_MODEL = "appRuleConfigModel";

    /**
     * 账号存量表
     */
    String ACCOUNT_LAST_INFO = "accountLastInfo";

    /**
     * httpApi
     */
    String HTTP_API = "httpApi";

    String RISK_SAMPLE = "riskSample";

    String AGG_RISK = "aggRiskInfo";

    String DATA_INFO = "dataInfo";

    /**
     * httpApp
     */
    String HTTP_APP = "httpApp";

    /**
     * 账号按天统计表
     */
    String ACCOUNT_DATE_INFO = "accountDateInfo";

    /**
     * apiAccountDate 指标表
     */
    String API_ACCOUNT_DATE_INFO = "apiAccountDateInfo";

    /**
     * apiAccount 接口账号聚合表
     */
    String API_ACCOUNT_INFO = "apiAccountInfo";

    /**
     * 聚合风险表
     */
    String RISK_INFO_AGG = "riskInfoAgg";

    /**
     * 风险策略白名单
     */
    String RISK_POLICY_ALLOW_LIST = "riskPolicyAllowList";


    String GATEWAY_MONITOR_DATA ="gatewayMonitorData";

    /**
     * 元数据表
     */
    String HTTP_API_META_DATA = "httpApiMetaData";

    /**
     * 文件表
     */
    String FILE_INFO = "fileInfo";

    String SYS_LOG = "sysLog";

    /**
     * 应用结构
     */
    String URL_STRUCTURE = "urlStructure";
}
