package com.quanzhi.auditapiv2.common.dal.dto.ip;

import com.quanzhi.auditapiv2.common.dal.entity.ip.IpLabel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * @Auther: yangzixian
 * @Date: 2021/9/11 16:29
 * @Description:
 */
@Data
@ApiModel("IP标签")
public class IpLabelDto {

    @ApiModelProperty("id")
    private String id;

    @ApiModelProperty("名称")
    private String name;

    @ApiModelProperty("是否内置（true不可删除/false可删除）")
    private boolean isBuiltIn;

    @Mapper
    public interface IpLabelDtoMapper {

        IpLabelDto.IpLabelDtoMapper INSTANCE = Mappers.getMapper(IpLabelDto.IpLabelDtoMapper.class);

        IpLabelDto convert(IpLabel ipLabel);

    }

}
