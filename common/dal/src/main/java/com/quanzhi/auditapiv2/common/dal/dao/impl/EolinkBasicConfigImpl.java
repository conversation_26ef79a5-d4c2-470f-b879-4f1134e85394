package com.quanzhi.auditapiv2.common.dal.dao.impl;

import com.quanzhi.auditapiv2.common.dal.dao.EolinkBasicConfigDao;
import com.quanzhi.auditapiv2.common.dal.entity.riskEolink.EolinkBasicConfigDto;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.stereotype.Repository;

@Slf4j
@Repository
public class EolinkBasicConfigImpl implements EolinkBasicConfigDao {

    @Autowired
    private MongoTemplate mongoTemplate;

    private final String collectionName = "eolinkBasicConfig";

    @Override
    public EolinkBasicConfigDto saveBasicConfig(EolinkBasicConfigDto licenseConfigDto) {
        //根据名称查找是否有数据
        EolinkBasicConfigDto dto = new EolinkBasicConfigDto();
        if (licenseConfigDto.getId() != null) {
            dto = editBasicConfig(licenseConfigDto);
        }else {
            String time = String.valueOf(System.currentTimeMillis());
            licenseConfigDto.setCreateTime(time);
            licenseConfigDto.setStartTime(time);
            dto = mongoTemplate.insert(licenseConfigDto, collectionName);
        }
        return dto;
    }

    @Override
    public EolinkBasicConfigDto editBasicConfig(EolinkBasicConfigDto licenseConfigDto) {

        Criteria criteria = Criteria.where("_id").is(licenseConfigDto.getId());
        Query query = new Query(criteria);
        Update update = new Update();
        String time = String.valueOf(System.currentTimeMillis());

        update.set("productName", licenseConfigDto.getProductName());
        update.set("version", licenseConfigDto.getVersion());
        update.set("state", licenseConfigDto.getState());
        update.set("isDel", 0);
        update.set("startTime", licenseConfigDto.getStartTime());
        update.set("endTime", licenseConfigDto.getEndTime());
        update.set("updateTime", time);

        mongoTemplate.updateFirst(query, update, collectionName);

        return licenseConfigDto;
    }

    @Override
    public EolinkBasicConfigDto findByProductId(String productId) {
        Criteria criteria = Criteria.where("productId").is(productId);

        return mongoTemplate.findOne(new Query(criteria), EolinkBasicConfigDto.class, collectionName);
    }

    @Override
    public void deleteByProductId(String productId) {
        Criteria criteria = Criteria.where("productId").is(productId);
        Query query = new Query(criteria);
        Update update = new Update();
        String time = String.valueOf(System.currentTimeMillis());

        update.set("isDel", 1);
        update.set("updateTime", time);

        mongoTemplate.updateFirst(query, update, collectionName);
    }
}
