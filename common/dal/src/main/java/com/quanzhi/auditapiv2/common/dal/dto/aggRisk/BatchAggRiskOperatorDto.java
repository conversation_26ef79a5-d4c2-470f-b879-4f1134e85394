package com.quanzhi.auditapiv2.common.dal.dto.aggRisk;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * @Author: yangzx
 * @Date: 2024/10/9 11:25
 */
@Data
public class BatchAggRiskOperatorDto {

    @ApiModelProperty("风险状态")
    private List<Integer> state;

    @ApiModelProperty("备注")
    private String remark;

    @ApiModelProperty("置顶状态")
    private Boolean riskMark;

    @ApiModelProperty("批量操作id")
    private List<String> ids;

    @ApiModelProperty("操作人")
    private String operateName;

    @ApiModelProperty("跨页全选")
    private AggRiskOperatorDto aggRiskOperatorDto;

}
