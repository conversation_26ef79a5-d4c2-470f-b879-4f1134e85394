package com.quanzhi.auditapiv2.common.dal.entity;

import com.quanzhi.audit_core.common.adaptive.AdaptiveCounting;
import com.quanzhi.metabase.core.model.Entity;
import com.quanzhi.metabase.core.model.http.HttpResourceConstant;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/10/21 下午5:57
 */
@Data
@Entity(HttpResourceConstant.ACCOUNT_LAST_INFO)
public class AccountLastInfo {

    private String account;

    /**
     * 首次发现日期
     */
    private String firstDate;

    /**
     * 部门
     */
    private String staffDepart;

    /**
     * 身份证号
     */
    private String staffIdCard;

    /**
     * 姓名
     */
    private String staffName;

    /**
     * 员工Id
     */
    private String staffId;

    /**
     * 邮箱
     */
    private String staffEmail;

    /**
     * 昵称
     */
    private String staffNickName;

    /**
     * 银行卡号
     */
    private String staffBankCard;

    /**
     * 手机号码
     */
    private String staffMobile;

    /**
     * 姓名
     */
    private String staffChinese;

    /**
     * 角色
     */
    private String staffRole;

    private Long visitCnt;

    /**
     * 登录次数
     */
    private Long loginCnt;

    /**
     * 返回数据标签
     */
    private List<String> rspDataLabelList;

    private List<String> relatedIpList;

    private List<String> appUriList;

    private List<String> apiUriList;

    //ua 去重列表
    private List<String> uaTypes;

    /**
     * 关联IP去重桶
     */
    private AdaptiveCounting relatedIpBucket;

    private DateCnt dateCnt;

    private Long updateTime;

    @Data
    public static class DateCnt {
        // 统计日期
        private String date;

        // 上次计算量
        private Long lastVisitCnt;
        private Long lastLoginCnt;
        private Long lastRspDataDistinctCnt;
    }
}
