package com.quanzhi.auditapiv2.common.dal.dao;

import com.quanzhi.audit_core.common.model.ApiAccountInfo;
import com.quanzhi.auditapiv2.common.dal.dto.query.ApiAccountInfoCriteriaDto;

import java.util.List;

/**
 * @ClassName IApiAccountInfoDao
 * @Description TODO
 * <AUTHOR>
 * @Date 2020/11/22 23:22
 **/
public interface IApiAccountInfoDao {

    List<ApiAccountInfo> getApiAccountInfoList(ApiAccountInfoCriteriaDto apiAccountInfoCriteriaDto);

    ApiAccountInfo getApiAccountInfo(String account);

    Long getApiAccountInfoCount(ApiAccountInfoCriteriaDto apiAccountInfoCriteriaDto);

    void remove(long start, long end);
}
