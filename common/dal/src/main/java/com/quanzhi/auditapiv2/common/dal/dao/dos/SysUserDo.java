package com.quanzhi.auditapiv2.common.dal.dao.dos;

import lombok.Data;

import java.util.List;


@Data
public class SysUserDo {

    private String id;

    private String username;

    private String password;

    private String repassword;

    private String groupId;

    private String groupName;

    /**
     * 是否为内置用户
     * 1：内置用户
     * 0：自定义用户
     */
    private Integer type;

    private List<String> visitIps;

    private Long updateTime;

    private String updateTimeFormat;

    private Long lockTime;

    private String lockTimeFormat;

    private Integer errorCount;

    /**
     * 邮箱地址
     */
    private String mailAddr;
}
