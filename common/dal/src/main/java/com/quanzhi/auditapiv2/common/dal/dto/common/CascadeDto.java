package com.quanzhi.auditapiv2.common.dal.dto.common;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/8/30 3:52 下午
 */
@Data
@ApiModel("级联")
public class CascadeDto {
    @ApiModelProperty("名称")
    private String label;
    @ApiModelProperty("ID")
    private String value;
    @ApiModelProperty("子列表")
    private List<CascadeDto> children;
}
