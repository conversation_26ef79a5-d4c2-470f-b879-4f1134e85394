/*
package com.quanzhi.auditapiv2.common.dal.entity;


import com.quanzhi.auditapiv2.common.dal.dto.SmartFilterRuleDto;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

*/
/**
 * 智能过滤规则
 *//*

@Data
@ApiModel
public class SmartFilterRule extends Object {

    */
/**
     * id
     *//*

    private String id;
    
    */
/**
     * 过滤规则所属应用
     *//*

    private String host;

    */
/**
     * 真实的host，指IP+端口的
     *//*

    private String realHost;

    */
/**
     * 过滤时判断的目标
     *//*

    private String target;

    */
/**
     * 针对过滤目标的计算
     *//*

    private String targetOp;

    */
/**
     * 针对过滤目标和结果比较的运算操作，默认为eq
     *//*

    private String compareOp;

    */
/**
     * 针对过滤目标计算后，对结果的比较值
     *//*

    private String compareValue;

    */
/**
     * 匹配过滤规则的百分比*100
     *//*

    private Integer matchRate;

    */
/**
     * 规则的优先级，优先级越高，越提前使用
     *//*

    private Integer priority;

    */
/**
     * 是否启用规则
     *//*

    private Boolean enable;

    */
/**
     * 是否删除
     *//*

    private Boolean delFlag;

    */
/**
     * 创建时间
     *//*

    private Long createTime;

    */
/**
     * 更新时间
     *//*

    private Long updateTime;

    */
/**
     * 描述信息
     *//*

    private String desc;

    public String getRuleKey() {
        return String.format("host:%s,target:%s,targetOp:%s,compareOp:%s,compareValue:%s", host, target, targetOp, compareOp, compareValue);
    }

    @Override
    public boolean equals(Object obj){
        //使用 == 检查参数是否为这个对象的引用。
        if (obj == this) {
            return true;
        }
        if (!(obj instanceof SmartFilterRule)) {
            return false;
        }
        SmartFilterRule otherRule = (SmartFilterRule) obj;
        if (otherRule.getHost().equals(host) && otherRule.getRealHost().equals(realHost)
                && otherRule.getTarget().equals(target) && otherRule.getTargetOp().equals(targetOp)
                && otherRule.getCompareOp().equals(compareOp) && otherRule.getCompareValue().equals(compareValue)) {
            return true;
        }
        return false;
    }

    @Mapper
    public interface SmartFilterRuleMapper {
        SmartFilterRule.SmartFilterRuleMapper INSTANCE = Mappers.getMapper(SmartFilterRule.SmartFilterRuleMapper.class);
        SmartFilterRule convert(SmartFilterRuleDto smartFilterRuleDto);
    }
}

*/
