package com.quanzhi.auditapiv2.common.dal.entity.riskEolink;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 策略阻断日志筛选条件
 */
@Data
public class BlockingStrategyLogSearch {
    /**
     * 筛选内容
     */
    @ApiModelProperty(value = "筛选内容", name = "content")
    private String content;

    /**
     * 排序字段
     */
    @ApiModelProperty(value = "排序字段", name = "sortField")
    private String sortField;

    /**
     * 排序方式
     */
    @ApiModelProperty(value = "排序方式（1-正序，2-倒序）", name = "sort")
    private Integer sort;

    /**
     * 当前页
     */
    @ApiModelProperty(value = "当前页", name = "page")
    private Integer page = 1;

    /**
     * 每页显示条数
     */
    @ApiModelProperty(value = "每页显示条数", name = "limit")
    private Integer limit = 10;

    /**
     * 分组字段
     */
    @ApiModelProperty(value = "分组字段", name = "groupField")
    private String groupField;
}
