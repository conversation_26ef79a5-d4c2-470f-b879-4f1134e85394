package com.quanzhi.auditapiv2.common.dal.dao.impl;

import com.quanzhi.auditapiv2.common.dal.dao.IToolConfigDao;
import com.quanzhi.auditapiv2.common.dal.entity.ToolConfig;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.stereotype.Repository;

import java.util.List;


@Repository("toolConfigDao")
public class ToolConfigDaoImpl implements IToolConfigDao {
    private String collectionName = "toolConfig";

    @Autowired
    private MongoTemplate mongoTemplate;

    @Override
    public List<ToolConfig> getAllToolConfig () {
        return mongoTemplate.findAll(ToolConfig.class);
    }

    @Override
    public ToolConfig getToolConfigById(String id) {
        return mongoTemplate.findById(id, ToolConfig.class, collectionName);
    }
}
