package com.quanzhi.auditapiv2.common.dal.entity;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.Instant;
import java.util.Date;

/**
 *
 * 《报告任务结果》
 *
 *
 * @Project:
 * @Module ID:
 * @Comments:
 * @JDK version used:      <JDK1.8> 
 * <AUTHOR> [<EMAIL>]
 * @since 2019-07-23-上午9:09:10
 */
@Data
@NoArgsConstructor
public class ScanTaskResult {

    public ScanTaskResult(String taskId, String taskName, String taskCode, JobStateEnum stateEum,Integer policy){
        long now = Instant.now().toEpochMilli();
        this.taskId = taskId;
        this.name = taskName;
        this.taskCode = taskCode;
        this.state = stateEum.getValue();
        this.policy = policy;
        this.insertTime = now;
        this.updateTime = now;
    }

    private String id;

    /**
     * 任务结果code
     */
    @ApiModelProperty(value = "任务结果code", name = "code")
    private String code;

    /**
     * 任务结果名称
     */
    @ApiModelProperty(value = "任务结果名称", name = "name")
    private String name;

    /**
     * 任务id
     */
    @ApiModelProperty(value = "任务id", name = "taskId")
    private String taskId;

    /**
     * 任务code
     */
    @ApiModelProperty(value = "任务code", name = "taskCode")
    private String taskCode;

    /**
     * 任务结果路径
     */
    @ApiModelProperty(value = "任务结果路径", name = "resultPath")
    private String resultPath;

    /**
     * 任务状态(0是初始状态，1执行中，2已完成，3失败，4已停止)
     * @see JobStateEnum
     */
    @ApiModelProperty(value = "任务状态(0是初始状态，1执行中，2已完成，3失败,4stop)", name = "state")
    private Integer state;

    /**
     * 策略code
     */
    @ApiModelProperty(value = "0单次运行，1周期运行", name = "policy")
    private Integer policy;

    /**
     * 结果状态中文名
     */
    private String stateName;



    /**
     * 报表插件运行需要的入参，以json字符串存储
     */
    @ApiModelProperty(value = "报表插件运行需要的入参，以json字符串存储", name = "params_json")
    private String paramsJson;

    /**
     * 保留字段。
     */
    private Date date;

    @ApiModelProperty(value = "报表运行的日志存储路径", name = "logPath")
    private String logPath;

    @ApiModelProperty(value = "报表运行进程的PID", name = "pid")
    private Integer pid;

    @ApiModelProperty(value = "报表运行的进程父进程的PID", name = "ppid")
    private Integer ppid;
    /**
     * 任务扫描开始时间
     */
    @ApiModelProperty(value = "任务扫描开始时间", name = "startTime")
    private Long startTime;

    /**
     * 任务扫描结束时间
     */
    @ApiModelProperty(value = "任务扫描结束时间", name = "endTime")
    private Long endTime;

    /**
     * 任务扫描开始时间
     */
    @ApiModelProperty(value = "任务开始时间", name = "createTime")
    private Long createTime;

    /**
     * 任务扫描结束时间
     */
    @ApiModelProperty(value = "任务结束时间", name = "stopTime")
    private Long stopTime;

    /**
     * 记录更新时间
     */
    @ApiModelProperty(value = "记录更新时间", name = "updateTime")
    private Long updateTime;

    /**
     * 记录插入时间
     */
    @ApiModelProperty(value = "记录插入时间", name = "insertTime")
    private Long insertTime;

    /**
     * 任务结果
     */
    @ApiModelProperty(value = "任务结果", name = "result")
    private Result result;


    private String amisReportJson;

    @ApiModelProperty(value = "导出人", name = "operator")
    private String operator;

    @Data
    public static class Result{

        /**
         * 错误信息
         */
        @ApiModelProperty(value = "错误信息", name = "message")
        private String message;

        /**
         * 错误调用堆栈
         */
        @ApiModelProperty(value = "错误调用堆栈", name = "stacktrace")
        private String stacktrace;

        /**
         * 结果html文件路径
         */
        @ApiModelProperty(value = "结果html文件路径", name = "htmlPath")
        private String htmlPath;

        /**
         * 结果data文件路径
         */
        @ApiModelProperty(value = "结果data文件路径", name = "dataPath")
        private String dataPath;

        /**
         * 结果zip压缩包路径
         */
        @ApiModelProperty(value = "结果zip压缩包路径", name = "zipPath")
        private String zipPath;

        @ApiModelProperty(value = "报告生成的任务状态", name = "state")
        private JobStateEnum state;
    }

    public enum JobStateEnum{
        NEW(0),
        RUNNING(1),
        FINISH(2),
        FAILED(3),
        STOPPED(4);

        private int state;

        JobStateEnum(int state){
            this.state = state;
        }

        public int getValue(){
            return this.state;
        }
    }
}
