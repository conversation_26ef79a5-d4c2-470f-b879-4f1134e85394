package com.quanzhi.auditapiv2.common.dal.dao.impl;

import com.quanzhi.audit_core.common.model.WeaknessRule;
import com.quanzhi.auditapiv2.common.dal.dao.IWeaknessRuleNacosDao;
import com.quanzhi.auditapiv2.common.dal.dao.base.impl.NacosBaseDaoImpl;
import org.springframework.stereotype.Repository;

/**
 * 
 * 《弱点规则持久层Nacos接口实现》
 * 
 * 
 * @Project: 
 * @Module ID:
 * @Comments: 
 * @JDK version used:      <JDK1.8> 
 * <AUTHOR> [<EMAIL>]
 * @since 2020-08-28-下午16:52:18
 */
@Repository
public class WeaknessRuleNacosDaoImpl extends NacosBaseDaoImpl<WeaknessRule> implements IWeaknessRuleNacosDao {
}
