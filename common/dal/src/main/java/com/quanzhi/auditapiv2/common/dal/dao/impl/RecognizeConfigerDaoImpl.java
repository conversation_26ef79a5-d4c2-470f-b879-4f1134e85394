package com.quanzhi.auditapiv2.common.dal.dao.impl;


import com.quanzhi.audit_core.common.model.RecommendPolicy;
import com.quanzhi.auditapiv2.common.dal.dao.IRecognizeConfigerDao;
import com.quanzhi.auditapiv2.common.dal.dao.base.impl.NacosBaseDaoImpl;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository("recognizeConfigerDao")
public class RecognizeConfigerDaoImpl extends NacosBaseDaoImpl<RecommendPolicy> implements IRecognizeConfigerDao {


    @Override
    public Boolean checkNameExist(String name,String excludeId) {

        List<RecommendPolicy> recognizeConfigerList = getAll();

        for(int i = 0; i < recognizeConfigerList.size();i++) {

            RecommendPolicy recognizeConfiger = recognizeConfigerList.get(i);

            if(!recognizeConfiger.getId().equals(excludeId) && recognizeConfiger.getName().equals(name)) {
                return true;
            }
        }

        return false;
    }
}
