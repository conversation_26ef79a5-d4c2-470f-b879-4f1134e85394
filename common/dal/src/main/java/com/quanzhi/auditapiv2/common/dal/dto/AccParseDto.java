package com.quanzhi.auditapiv2.common.dal.dto;

import com.quanzhi.metabase.core.model.http.HttpApiResource;
import com.quanzhi.metabase.core.model.http.HttpAppResource;
import lombok.Data;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.io.Serializable;
import java.util.List;

/**
 * @ClassName AccParseDto
 * @Description TODO
 * <AUTHOR>
 * @Date 2020/10/29 11:08
 **/
@Data
public class AccParseDto implements Serializable {

    private String uri;

    /**
     * 账号配置
     */
    private List<HttpApiResource.AccParseConfigs> userParseConfigs;

    /**
     * session配置
     */
    private List<HttpApiResource.AccParseConfigs> sessionParseConfigs;

    /**
     * 密码配置
     */
    private List<HttpApiResource.AccParseConfigs> passwordParseConfigs;

    /**
     * 登录凭证配置
     */
    private List<HttpApiResource.AccParseConfigs> loginSuccessConfigs;

    /**
     * 组织架构部门
     */
    private List<HttpApiResource.AccParseConfigs> staffDepartParseConfigs;

    private List<HttpApiResource.AccParseConfigs> staffNameParseConfigs;

    private List<HttpApiResource.AccParseConfigs> staffChineseParseConfigs;

    private List<HttpApiResource.AccParseConfigs> staffIdCardParseConfigs;

    private List<HttpApiResource.AccParseConfigs> staffBankCardParseConfigs;

    private List<HttpApiResource.AccParseConfigs> staffEmailParseConfigs;

    private List<HttpApiResource.AccParseConfigs> staffIdParseConfigs;

    private List<HttpApiResource.AccParseConfigs> staffMobileParseConfigs;

    private List<HttpApiResource.AccParseConfigs> staffRoleParseConfigs;

    /**
     * 昵称
     */
    private List<HttpApiResource.AccParseConfigs> staffNickNameParseConfigs;

    private List<String> tags;

    private Boolean isSso;

    private Boolean accParseEnable;

    private Boolean staffInfoParseEnable;

    @Mapper
    public interface AccParseDtoMapper {

        AccParseDtoMapper INSTANCE = Mappers.getMapper(AccParseDtoMapper.class);

        HttpAppResource convert1(AccParseDto accParseDto);

        ApiAccParseCriteriaDto convert2(AccParseDto accParseDto);

    }

}
