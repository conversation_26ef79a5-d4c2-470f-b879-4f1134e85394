package com.quanzhi.auditapiv2.common.dal.dao.convert;

import org.springframework.data.mapping.MappingException;
import org.springframework.data.mapping.context.MappingContext;
import org.springframework.data.mongodb.core.convert.DbRefResolver;
import org.springframework.data.mongodb.core.convert.MappingMongoConverter;
import org.springframework.data.mongodb.core.mapping.MongoPersistentEntity;
import org.springframework.data.mongodb.core.mapping.MongoPersistentProperty;


public class CustomMappingMongoConverter extends MappingMongoConverter {

    protected String mapKeyDollarReplacement = null;

    public CustomMappingMongoConverter(DbRefResolver dbRefResolver,
                                 MappingContext<? extends MongoPersistentEntity<?>, MongoPersistentProperty> mappingContext) {
        super(dbRefResolver, mappingContext);
    }

    public void setMapKeyDollarReplacement(String mapKeyDollarReplacement) {
        this.mapKeyDollarReplacement = mapKeyDollarReplacement;
    }

    /**
     * Potentially replaces dots in the given map key with the configured map key replacement if configured or aborts
     * conversion if none is configured.
     *
     * @see #setMapKeyDotReplacement(String)
     * @param source
     * @return
     */
    @Override
    protected String potentiallyEscapeMapKey(String source) {
        source = super.potentiallyEscapeMapKey(source);
        if (!source.contains("$")) {
            return source;
        }
        if (mapKeyDotReplacement == null) {
            throw new MappingException(String.format(
                    "Map key %s contains dots but no replacement was configured! Make "
                            + "sure map keys don't contain dots in the first place or configure an appropriate replacement!",
                    source));
        }
        return source.replaceAll("\\$", mapKeyDotReplacement);
    }

    /**
     * Translates the map key replacements in the given key just read with a dot in case a map key replacement has been
     * configured.
     *
     * @param source
     * @return
     */
    @Override
    protected String potentiallyUnescapeMapKey(String source) {
        source = super.potentiallyUnescapeMapKey(source);
        source = mapKeyDollarReplacement == null ? source : source.replaceAll(mapKeyDollarReplacement, "\\$");
        return source;
    }
}
