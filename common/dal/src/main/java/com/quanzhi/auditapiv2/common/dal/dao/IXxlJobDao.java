package com.quanzhi.auditapiv2.common.dal.dao;

import com.quanzhi.audit_core.common.model.ApiOfflineDiscoverParam;
import com.quanzhi.auditapiv2.common.dal.dao.schedule.Executor;
import com.quanzhi.auditapiv2.common.dal.dto.XxlJobTaskDto;
import com.quanzhi.auditapiv2.common.dal.dto.XxlJobTaskListDto;
import com.quanzhi.auditapiv2.common.dal.dto.filter.EnableFilterDto;
import com.quanzhi.auditapiv2.common.util.dto.ListOutputDto;
import com.quanzhi.metabase.core.domain.entity.CompositeRuleScheduleInfo;
import com.quanzhi.metabase.core.model.http.KeywordSplitRule;

import java.util.List;
import java.util.Map;

public interface IXxlJobDao {

    void addTask(XxlJobTaskDto xxlJobTaskDto);

    void addReturnTask(ApiOfflineDiscoverParam apiOfflineDiscoverParam);


    void addOfflineReportTask (String params);

    ListOutputDto<XxlJobTaskListDto> getXxlJobList(
            Integer page,Integer limit,Long startTime,Long endTime,
            List<Executor> groupNameAndjobHandler,
            String taskType
    );

    void triggerCompositeJob (String compositeRule);

    void triggerCompositeBatchJob(CompositeRuleScheduleInfo compositeRuleScheduleInfo);

    void triggerWeaknessJob ();

    void addHistoryDataCleanTask(Map<String,String> taskMap);

    ListOutputDto<XxlJobTaskListDto> getHistoryDataCleanJobList(
            Integer page,Integer limit,Long startTime,Long endTime,
            Map<String,String> groupNameAndjobHandler
    );


    void triggerEventFilterPluginJob(Map<String, String> plugins);

    void triggerKeywordSplitJob(KeywordSplitRule saveRule);

    void triggerAppAutoMergeJob();

    void triggerApiAutoSplitJob();

    void triggerFilterRuleJob(EnableFilterDto enableFilterDto);

    void triggerJob(String app,String executor,String params);

}
