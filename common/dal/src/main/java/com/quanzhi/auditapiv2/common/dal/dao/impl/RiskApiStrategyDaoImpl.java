package com.quanzhi.auditapiv2.common.dal.dao.impl;

import com.quanzhi.auditapiv2.common.dal.dao.RiskApiStrategyDao;
import com.quanzhi.auditapiv2.common.dal.entity.riskEolink.RiskApiStrategyDto;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public class RiskApiStrategyDaoImpl implements RiskApiStrategyDao {

    @Autowired
    private MongoTemplate mongoTemplate;

    private String collectionName = "riskApiStrategy";
    @Override
    public RiskApiStrategyDto save(RiskApiStrategyDto dto) {
        return mongoTemplate.save(dto, collectionName);
    }

    @Override
    public RiskApiStrategyDto update(RiskApiStrategyDto dto) {
        return null;
    }

    @Override
    public List<RiskApiStrategyDto> listRiskApiStrategy(String api, String app) {
        return null;
    }

    @Override
    public RiskApiStrategyDto findById(String id) {
        Criteria criteria = Criteria.where("riskId").is(id);

        return mongoTemplate.findOne(new Query(criteria), RiskApiStrategyDto.class, collectionName);
    }

    @Override
    public RiskApiStrategyDto findByOperationId(String operationId) {
        Criteria criteria = Criteria.where("operationId").is(operationId);

        return mongoTemplate.findOne(new Query(criteria), RiskApiStrategyDto.class, collectionName);
    }

    @Override
    public void deleteById(String id) {
        Criteria criteria = Criteria.where("riskId").is(id);

        mongoTemplate.remove(new Query(criteria), collectionName);
    }

    @Override
    public void deleteByStrategyId(String strategyId) {
        Criteria criteria = Criteria.where("strategyId").is(strategyId);

        mongoTemplate.remove(new Query(criteria), collectionName);
    }
}
