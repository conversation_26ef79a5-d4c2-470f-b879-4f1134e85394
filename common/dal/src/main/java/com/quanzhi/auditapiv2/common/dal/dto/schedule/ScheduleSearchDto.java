package com.quanzhi.auditapiv2.common.dal.dto.schedule;

import com.quanzhi.audit.mix.schdule.domain.entity.Schedule;
import com.quanzhi.auditapiv2.common.dal.dto.query.BaseSearchDto;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * @author: yangzixian
 * @date: 2023.08.21 10:38
 * @description:
 */
@Data
public class ScheduleSearchDto extends BaseSearchDto {

    @ApiModelProperty(value = "id", name = "id")
    private String id;

    @ApiModelProperty(value = "任务名称", name = "name")
    private String name;

    @ApiModelProperty(value = "任务执行周期", name = "cron")
    private String cron;

    @ApiModelProperty(value = "任务描述", name = "description")
    private String description;

    @ApiModelProperty(value = "任务id", name = "executor")
    private String executor;

    @ApiModelProperty(value = "任务所属组件", name = "app")
    private String app;

    @ApiModelProperty(value = "排序字段", name = "field")
    private String field;

    @ApiModelProperty(value = "排序方式", name = "sort")
    private Integer sort;

    @ApiModelProperty(value = "创建时间开始", name = "createTimeStart")
    private Long createTimeStart;

    @ApiModelProperty(value = "创建时间结束", name = "createTimeEnd")
    private Long createTimeEnd;

    @ApiModelProperty(value = "更新时间开始", name = "updateTimeStart")
    private Long updateTimeStart;

    @ApiModelProperty(value = "更新时间结束", name = "updateTimeEnd")
    private Long updateTimeEnd;

}
