package com.quanzhi.auditapiv2.common.dal.entity;

import com.alibaba.fastjson.JSONObject;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * create at 2024/11/5 18:59
 * @description: 自定义报表任务
 **/
@Data
@AllArgsConstructor
@NoArgsConstructor
public class CustomReportTaskEvent {

    /**
     * 任务记录ID
     */
    private String id;
    /**
     * 业务报表类型code
     */
    private String code;
    /**
     * 删除还是新增任务 ADD|DEL
     */
    private String operate;
    /**
     * 0:立即执行
     * 1:周期任务
     */
    private Integer policy;

    /**
     * 任务参数
     */
    private JSONObject params;

}