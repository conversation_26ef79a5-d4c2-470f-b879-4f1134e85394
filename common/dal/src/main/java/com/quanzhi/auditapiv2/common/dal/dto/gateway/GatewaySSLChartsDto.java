package com.quanzhi.auditapiv2.common.dal.dto.gateway;

import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @description  加密信息图表分布
 * @date 2023/8/17 14:31
 */
@Data
public class GatewaySSLChartsDto {

    //ssl事件占比
    private Double sslCntPercent;
    //http事件占比
    private Double httpCntPercent;
    //ftp事件占比
    private Double ftpCntPercent;
    //其他时间占比
    private Double otherCntPercent;

    //时间轴
    private List<String> axisData;

    //流量
    private List<Long> seriesData;
}
