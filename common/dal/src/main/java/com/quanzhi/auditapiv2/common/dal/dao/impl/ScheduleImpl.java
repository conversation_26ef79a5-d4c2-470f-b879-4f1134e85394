package com.quanzhi.auditapiv2.common.dal.dao.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.quanzhi.audit.mix.schdule.application.service.ScheduleApplicationService;
import com.quanzhi.audit.mix.schdule.domain.entity.ScheduleLog;
import com.quanzhi.audit.mix.schdule.domain.entity.ScheduleState;
import com.quanzhi.audit.mix.schdule.domain.exception.ScheduleException;
import com.quanzhi.audit_core.common.constant.Jobs;
import com.quanzhi.audit_core.common.model.ApiCleanJobParam;
import com.quanzhi.audit_core.common.model.ApiOfflineDiscoverParam;
import com.quanzhi.audit_core.common.model.JobParam;
import com.quanzhi.auditapiv2.common.dal.dao.IXxlJobDao;
import com.quanzhi.auditapiv2.common.dal.dao.schedule.Executor;
import com.quanzhi.auditapiv2.common.dal.dto.XxlJobTaskDto;
import com.quanzhi.auditapiv2.common.dal.dto.XxlJobTaskListDto;
import com.quanzhi.auditapiv2.common.dal.dto.filter.EnableFilterDto;
import com.quanzhi.auditapiv2.common.util.dto.ListOutputDto;
import com.quanzhi.auditapiv2.common.util.utils.DataUtil;
import com.quanzhi.auditapiv2.common.util.utils.DateUtil;
import com.quanzhi.metabase.core.domain.entity.CompositeRuleScheduleInfo;
import com.quanzhi.metabase.core.model.http.KeywordSplitRule;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Repository;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Repository
public class ScheduleImpl implements IXxlJobDao {

    private final ScheduleApplicationService scheduleApplicationService;

    @Value(value = "${audit.mix.schedule.app}")
    private String appname;

    public ScheduleImpl(ScheduleApplicationService scheduleApplicationService) {
        this.scheduleApplicationService = scheduleApplicationService;
    }

    @Override
    public void addTask(XxlJobTaskDto xxlJobTaskDto) {
        if (DataUtil.isNotEmpty(xxlJobTaskDto)) {
            String param = null;
            if (xxlJobTaskDto.getEntity().equals(XxlJobTaskDto.EntityEnum.API.name())) {

                ApiCleanJobParam apiCleanJobParam = new ApiCleanJobParam();

                apiCleanJobParam.setCleanMethod(xxlJobTaskDto.getCleanMethod());
                apiCleanJobParam.setDataType(xxlJobTaskDto.getDataType());
                apiCleanJobParam.setDataItems(xxlJobTaskDto.getDataItems());
                apiCleanJobParam.setStartTimestamp(xxlJobTaskDto.getStartTime());
                apiCleanJobParam.setEndTimestamp(xxlJobTaskDto.getEndTime());
                apiCleanJobParam.setDataItemNames(xxlJobTaskDto.getDataItemNames());
                apiCleanJobParam.setUserId(xxlJobTaskDto.getUserId());

                String groupName = null;
                String jobHandler = null;
                switch (XxlJobTaskDto.TaskTypeEnum.valueOf(xxlJobTaskDto.getTaskType())) {
                    case CLEAN:
                        if ("RISK".equals(xxlJobTaskDto.getDataType()) || "RISK".equals(xxlJobTaskDto.getCleanMethod())) {
                            groupName = appname;
                            jobHandler = "manualClearRiskJob";
                        } else if ("LOG".equals(xxlJobTaskDto.getDataType()) || "LOG".equals(xxlJobTaskDto.getCleanMethod())) {
                            groupName = appname;
                            jobHandler = "manualClearLog";
                        } else if ("FILE".equals(xxlJobTaskDto.getDataType()) || "FILE".equals(xxlJobTaskDto.getCleanMethod())) {
                            groupName = appname;
                            jobHandler = "manualClearFile";
                        } else if ("IP".equals(xxlJobTaskDto.getDataType()) || "IP".equals(xxlJobTaskDto.getCleanMethod())) {
                            groupName = appname;
                            jobHandler = "manualClearIP";
                        }  else if ("ACCOUNT".equals(xxlJobTaskDto.getDataType()) || "ACCOUNT".equals(xxlJobTaskDto.getCleanMethod())) {
                            groupName = appname;
                            jobHandler = "manualClearAccount";
                        } else {
                            groupName = Jobs.Metabase.APP_METABASE;
                            jobHandler = Jobs.Metabase.JOB_CLEAN_API;
                        }
                        break;
                    case RERUN:
                        groupName = Jobs.Discover.APP_DISCOVER;
                        jobHandler = Jobs.Discover.JOB_DISCOVER_API;
                        break;

                    default:
                        break;
                }

                if (xxlJobTaskDto.getTaskType().equals(XxlJobTaskDto.TaskTypeEnum.CLEAN.name())) {
                    param = JSON.toJSONString(apiCleanJobParam, SerializerFeature.IgnoreNonFieldGetter
                            , SerializerFeature.WriteNullListAsEmpty);
                } else if (xxlJobTaskDto.getTaskType().equals(XxlJobTaskDto.TaskTypeEnum.RERUN.name())) {
                    ApiOfflineDiscoverParam offlineDiscoverParam = new ApiOfflineDiscoverParam();
                    offlineDiscoverParam.setEndTimestamp(apiCleanJobParam.getEndTimestamp());
                    offlineDiscoverParam.setStartTimestamp(apiCleanJobParam.getStartTimestamp());
                    offlineDiscoverParam.setUserId(apiCleanJobParam.getUserId());
                    offlineDiscoverParam.setOfflineDiscoverTypes(xxlJobTaskDto.getOfflineDiscoverTypes());
                    JobParam jobParam = new JobParam();
                    jobParam.setDataItemNames(apiCleanJobParam.getDataItemNames());
                    jobParam.setDataItems(apiCleanJobParam.getDataItems());
                    if (StringUtils.isNotEmpty(apiCleanJobParam.getDataType())) {
                        jobParam.setDataType(JobParam.DataType.valueOf(apiCleanJobParam.getDataType()));
                    }
                    offlineDiscoverParam.setJobParams(Collections.singletonList(jobParam));
                    param = JSON.toJSONString(offlineDiscoverParam, SerializerFeature.IgnoreNonFieldGetter
                            , SerializerFeature.WriteNullListAsEmpty);
                }
                try {
                    scheduleApplicationService.execute(groupName, jobHandler, param);
                } catch (ScheduleException e) {
                    throw new IllegalStateException("execute schedule fail", e);
                }
            }
        }
    }


    @Override
    public void addReturnTask(ApiOfflineDiscoverParam apiOfflineDiscoverParam) {
       String param=JSON.toJSONString(apiOfflineDiscoverParam, SerializerFeature.IgnoreNonFieldGetter, SerializerFeature.WriteNullListAsEmpty);
        try {
            scheduleApplicationService.execute(Jobs.Discover.APP_DISCOVER, Jobs.Discover.JOB_DISCOVER_API, param);
        } catch (ScheduleException e) {
            throw new IllegalStateException("execute schedule fail", e);
        }
    }

    @Override
    public void addOfflineReportTask(String params) {
        try {
            scheduleApplicationService.execute(Jobs.Auditapiv2.APP_METABASE, Jobs.Auditapiv2.JOB_OFFLINE_REPORT, params);
        } catch (ScheduleException e) {
            throw new IllegalStateException("execute schedule fail", e);
        }
    }

    @Override
    public ListOutputDto<XxlJobTaskListDto> getXxlJobList(Integer page, Integer limit, Long startTime, Long endTime, List<Executor> executors, String taskType) {
        ListOutputDto listOutputDto = new ListOutputDto();
        Query query = new Query();
        Criteria criteria = Criteria.where("schedule.executor").in(executors.stream().map(Executor::getExecutor).collect(Collectors.toList()))
                .and("schedule.app").in(executors.stream().map(Executor::getApp).distinct().collect(Collectors.toList()));
        if (DataUtil.isNotEmpty(startTime) && DataUtil.isNotEmpty(endTime)) {
            criteria = criteria.andOperator(
                    Criteria.where("startTime").gte(startTime),
                    Criteria.where("startTime").lte(endTime)
            );
        }
        query.addCriteria(criteria);
        Pageable pageable = PageRequest.of(page - 1, limit, Sort.by(Sort.Order.desc("startTime")));
        listOutputDto.setTotalCount(scheduleApplicationService.countScheduleLogs(query));
        if (listOutputDto.getTotalCount() != 0) {
            Page<ScheduleLog> scheduleLogs = scheduleApplicationService.listScheduleLogs(query, pageable);
            List<ScheduleLog> content = scheduleLogs.getContent();
            List<XxlJobTaskListDto> xxlJobTaskListDtos = new ArrayList<>();
            for (ScheduleLog scheduleLog : content) {
                XxlJobTaskListDto xxlJobTaskListDto = new XxlJobTaskListDto();
                xxlJobTaskListDto.setCreateTime(DateUtil.tm2time(scheduleLog.getStartTime()));
                xxlJobTaskListDto.setStartTime(DateUtil.tm2time(scheduleLog.getStartTime()));
                xxlJobTaskListDto.setEndTime(DateUtil.tm2time(scheduleLog.getEndTime()));
                xxlJobTaskListDto.setStatus(convertStat(scheduleLog.getState()));
                String parameter = scheduleLog.getParameter();
                xxlJobTaskListDto.setExecutorParam(parameter);
                if (DataUtil.isNotEmpty(taskType) && taskType.equals(XxlJobTaskDto.TaskTypeEnum.CLEAN.name())) {
                    if (DataUtil.isNotEmpty(parameter)) {
                        ApiCleanJobParam apiCleanJobParam = JSON.parseObject(parameter, ApiCleanJobParam.class);
                        String cleanMethod = apiCleanJobParam.getCleanMethod();
                        if (DataUtil.isNotEmpty(cleanMethod)) {
                            xxlJobTaskListDto.setTaskName(convertCleanMethod(cleanMethod));
                        }
                    }else{
                        xxlJobTaskListDto.setTaskName(scheduleLog.getSchedule().getName());
                    }
                } else if (DataUtil.isNotEmpty(taskType) && taskType.equals(XxlJobTaskDto.TaskTypeEnum.RERUN.name())) {
                    if (DataUtil.isNotEmpty(parameter)) {
                        JSONObject parseObject = JSONObject.parseObject(parameter);
                        JSONArray jobParamsArray = parseObject.getJSONArray("jobParams");
                        if(jobParamsArray.isEmpty()){
                            xxlJobTaskListDto.setTaskName("全量");
                        }else{
                            for(Object o:jobParamsArray){
                                JSONObject jsonObject= (JSONObject) o;
                                String dataType = jsonObject.getString("dataType");
                                if ("APP".equalsIgnoreCase(dataType)) {
                                    xxlJobTaskListDto.setTaskName("指定应用");
                                    break;
                                } else if ("URL".equalsIgnoreCase(dataType)) {
                                    xxlJobTaskListDto.setTaskName("指定API");
                                    break;
                                }
                            }
                        }

                    }
                }
                xxlJobTaskListDtos.add(xxlJobTaskListDto);
            }
            listOutputDto.setRows(xxlJobTaskListDtos);
        } else {
            listOutputDto.setRows(new ArrayList());
        }

        return listOutputDto;
    }

    private String convertCleanMethod(String cleanMethod) {
        String name = "";
        switch (cleanMethod) {
            case "LABEL":
                name = "清理标签";
                break;
            case "FILE":
                name = "清理文件";
                break;
            case "SAMPLE":
                name = "清理API样例";
                break;
            case "ACCOUNT":
                name = "清理账号";
                break;
            case "ASSET":
                name = "清理应用清单、API清单";
                break;
            case "RISK":
                name = "清理风险清单";
                break;
            case "LOG":
                name = "清理审计日志";
                break;
            case "WEAKNESS":
                name = "清理弱点清单";
                break;
            case "IP":
                name = "清理IP清单";
                break;
        }
        return name;
    }

    private String convertStat(ScheduleState state) {
        switch (state) {
            case FAIL:
                return "失败";
            case INIT:
                return "初始化";
            case RUNNING:
                return "运行中";
            case SUCCESS:
                return "成功";
            default:
                break;
        }
        return state.name();
    }

    @Override
    public void triggerCompositeJob(String compositeRule) {

        try {
            scheduleApplicationService.execute(Jobs.Metabase.APP_METABASE, Jobs.Metabase.JOB_COMPOSITE, compositeRule);
        } catch (ScheduleException e) {
            throw new IllegalStateException("execute schedule fail", e);
        }
    }

    @Override
    public void triggerCompositeBatchJob(CompositeRuleScheduleInfo compositeRuleScheduleInfo) {
        try {
            scheduleApplicationService.execute(Jobs.Metabase.APP_METABASE, Jobs.Metabase.JOB_COMPOSITE, JSON.toJSONString(compositeRuleScheduleInfo));
        } catch (ScheduleException e) {
            throw new IllegalStateException("execute schedule fail", e);
        }
    }

    @Override
    public void triggerWeaknessJob() {
        try {
            scheduleApplicationService.execute(Jobs.Metabase.APP_METABASE, Jobs.Metabase.JOB_WEAKNESS);
        } catch (ScheduleException e) {
            throw new IllegalStateException("execute schedule fail", e);
        }
    }

    @Override
    public void addHistoryDataCleanTask(Map<String, String> taskMap) {
        try {
            scheduleApplicationService.execute(Jobs.Metabase.APP_METABASE, Jobs.Metabase.JOB_HISTORY_DATA_CLEAN, JSON.toJSONString(taskMap));
        } catch (ScheduleException e) {
            throw new IllegalStateException("execute schedule fail", e);
        }
    }

    @Override
    public ListOutputDto<XxlJobTaskListDto> getHistoryDataCleanJobList(Integer page, Integer limit, Long startTime, Long endTime, Map<String, String> groupNameAndjobHandler) {
        return null;
    }

    @Override
    public void triggerEventFilterPluginJob(Map<String, String> plugins) {
        try {
            scheduleApplicationService.execute(appname, "eventFilterPluginJob", JSON.toJSONString(plugins));
        } catch (ScheduleException e) {
            throw new IllegalStateException("execute schedule fail", e);
        }
    }

    @Override
    public void triggerKeywordSplitJob(KeywordSplitRule saveRule) {
        try {
            scheduleApplicationService.execute(Jobs.Metabase.APP_METABASE, "apiKeywordSplitJob", JSON.toJSONString(saveRule));
        } catch (ScheduleException e) {
            throw new IllegalStateException("execute schedule fail", e);
        }
    }

    @Override
    public void triggerAppAutoMergeJob() {
        try {
            scheduleApplicationService.execute(Jobs.Metabase.APP_METABASE, "appAutoMergeJob");
        } catch (ScheduleException e) {
            throw new IllegalStateException("execute schedule fail", e);
        }
    }

    @Override
    public void triggerApiAutoSplitJob() {
        try {
            scheduleApplicationService.execute(Jobs.Auditapiv2.APP_METABASE, "apiAutoSplitJob");
        } catch (ScheduleException e) {
            throw new IllegalStateException("execute schedule fail", e);
        }
    }

    @Override
    public void triggerFilterRuleJob(EnableFilterDto enableFilterDto) {
        try {
            scheduleApplicationService.execute(Jobs.Auditapiv2.APP_METABASE, "filterRuleJob", JSON.toJSONString(enableFilterDto));
        } catch (ScheduleException e) {
            throw new IllegalStateException("execute schedule fail", e);
        }
    }

    @Override
    public void triggerJob(String app, String executor, String params) {
        try {
            scheduleApplicationService.execute(app, executor, params);
        } catch (ScheduleException e) {
            throw new IllegalStateException("execute schedule fail", e);
        }
    }
}
