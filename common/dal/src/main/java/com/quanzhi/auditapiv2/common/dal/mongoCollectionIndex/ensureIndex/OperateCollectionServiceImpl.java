package com.quanzhi.auditapiv2.common.dal.mongoCollectionIndex.ensureIndex;

import com.alibaba.nacos.api.config.annotation.NacosValue;
import com.quanzhi.metabase.common.utils.ScannerUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.context.event.ApplicationReadyEvent;
import org.springframework.context.event.EventListener;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.index.IndexOperations;
import org.springframework.data.mongodb.core.index.MongoPersistentEntityIndexResolver;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.mongodb.core.mapping.MongoMappingContext;
import org.springframework.data.util.ClassTypeInformation;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * @date 2020/12/11 上午11:06
 */

@Service
@Slf4j
public class OperateCollectionServiceImpl implements  IOperateCollectionService{

    private final MongoTemplate mongoTemplate;

    private final MongoMappingContext mongoMappingContext;

    @NacosValue(value = "${ensureIndexFlag:false}",autoRefreshed = true)
    private boolean ensureIndexFlag;

    public OperateCollectionServiceImpl(MongoTemplate mongoTemplate
            , MongoMappingContext mongoMappingContext) {
        this.mongoTemplate = mongoTemplate;
        this.mongoMappingContext = mongoMappingContext;
    }

    @EventListener(ApplicationReadyEvent.class)
    public void ensureIndex() {
        new Thread(new Runnable() {
            @Override
            public void run() {
                Set<Class<?>> documentClassSet = ScannerUtils.scanClass(DOCUMENT_PACKAGES, Document.class);
                for (Class<?> clz : documentClassSet) {

                    try {
                        List<String> nameList=new ArrayList<>();
                        IndexOperations indexOps = mongoTemplate.indexOps(clz);
                        MongoPersistentEntityIndexResolver resolver = new MongoPersistentEntityIndexResolver(mongoMappingContext);
                        Iterable<? extends MongoPersistentEntityIndexResolver.IndexDefinitionHolder> indexDefinitionHolders = resolver.resolveIndexFor(ClassTypeInformation.from(clz));
                        for(MongoPersistentEntityIndexResolver.IndexDefinitionHolder indexDefinitionHolder:indexDefinitionHolders){
                            org.bson.Document indexOptions = indexDefinitionHolder.getIndexDefinition().getIndexOptions();
                            nameList.add(String.valueOf(indexOptions.get("name")));
                            indexOps.ensureIndex(indexDefinitionHolder);
                        }
                        // 将原有创建的不在最新索引类中声明的索引删掉，否则可能造成索引错乱，降低性能
                        if (ensureIndexFlag) {
                            indexOps.getIndexInfo().stream().forEach(index -> {
                                String name = index.getName();
                                if (!nameList.contains(name) && !name.equals("_id_")) {
                                    indexOps.dropIndex(name);
                                }
                            });
                        }
                    } catch (Exception e) {
                        log.error("{} ensure index error", clz.getCanonicalName(), e);
                    }
                }
            }
        }).start();
    }
}
