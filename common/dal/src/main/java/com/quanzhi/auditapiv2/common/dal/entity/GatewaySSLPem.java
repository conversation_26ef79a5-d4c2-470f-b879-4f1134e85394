package com.quanzhi.auditapiv2.common.dal.entity;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @description  加密流量监控信息
 * @date 2023/8/10 15:05
 */
@Data
public class GatewaySSLPem {

    private String id;

    private String name;

    /**
     * 网关ips
     */
    private List<String> ips;

    /**
     * 保存在本地的文件名
     */
    private String fileName;

    /**
     * 作用域是否为全部gatewayIp
     */
    private Boolean isAll;


    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间", name = "createTime")
    private Long createTime;

    /**
     * 更新时间
     */
    @ApiModelProperty(value = "更新时间", name = "updateTime")
    private Long updateTime;
}
