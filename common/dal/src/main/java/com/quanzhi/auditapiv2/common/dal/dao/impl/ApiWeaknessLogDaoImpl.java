package com.quanzhi.auditapiv2.common.dal.dao.impl;

import com.quanzhi.auditapiv2.common.dal.dao.IApiWeaknessLogDao;
import com.quanzhi.auditapiv2.common.dal.dao.base.impl.MetabaseDaoImpl;
import com.quanzhi.auditapiv2.common.dal.entity.ApiWeaknessLog;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 
 * 《接口弱点日志持久层接口实现》
 * 
 * 
 * @Project: 
 * @Module ID:
 * @Comments: 
 * @JDK version used:      <JDK1.8> 
 * <AUTHOR> [<EMAIL>]
 * @since 2020-08-28-下午16:52:18
 */
@Repository
public class ApiWeaknessLogDaoImpl extends MetabaseDaoImpl<ApiWeaknessLog> implements IApiWeaknessLogDao {

    @Autowired
    private MongoTemplate mongoTemplate;

    private final String collectionName = "apiWeaknessLog";
    
    /**
     * 查询接口弱点列表(分页)
     * @Comments:  <对此方法的描述，可以引用系统设计中的描述>
     * <AUTHOR> [<EMAIL>]
     * @since 2020-08-28 16:52
     * @param
     * @return
     */
    @Override
    public List<ApiWeaknessLog> selectApiWeaknessLogList(String apiWeaknessId) throws Exception {

        //查询条件
        Criteria criteria = Criteria.where("apiWeaknessId").is(apiWeaknessId);
        //排序
        Sort sort = Sort.by(Sort.Direction.ASC, "updateTime");

        return mongoTemplate.find(new Query(criteria).with(sort), ApiWeaknessLog.class, collectionName);
    }

    /**
     * 新增接口弱点日志
     * @Comments:  <对此方法的描述，可以引用系统设计中的描述>
     * <AUTHOR> [<EMAIL>]
     * @since 2020-08-28 16:52
     * @param
     * @return
     */
    @Override
    public ApiWeaknessLog insertApiWeaknessLog(ApiWeaknessLog apiWeaknessLog) throws Exception {

        return mongoTemplate.insert(apiWeaknessLog, collectionName);
    }

    /**
     * 删除接口弱点日志
     * @Comments:  <对此方法的描述，可以引用系统设计中的描述>
     * <AUTHOR> [<EMAIL>]
     * @since 2020-08-28 16:52
     * @param
     * @return
     */
    @Override
    public void delApiWeaknessLog(String id) throws Exception {

        //删除条件
        Criteria criteria = Criteria.where("_id").is(id);
        
        mongoTemplate.remove(new Query(criteria), collectionName);
    }
}
