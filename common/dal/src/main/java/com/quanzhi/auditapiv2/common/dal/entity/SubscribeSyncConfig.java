package com.quanzhi.auditapiv2.common.dal.entity;

import com.java3y.austin.domain.DingDingRobotParam;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2020/8/18 上午11:22
 */
@Data
public class SubscribeSyncConfig extends BaseNacosConfig implements Comparable<SubscribeSyncConfig> {

    private String id;

    /**
     * 配置名称
     */
    private String name;

    /**
     * 同步类型
     *
     * @see SynncConfigEnum
     */
    private String type;

    private String ip;

    private String port;

    /**
     * kafka链接地址（支持集群*******:8080,*******:8081）
     */
    private String ipPort;

    /**
     * syslog protocol
     */
    private String protocol;

    /**
     * kafka topic
     */
    private String topic;

    /**
     * 账号用于消费写入KAFKA
     */
    private String KafkaClientUsername;

    /**
     * 密码用于消费写入KAFKA
     */
    private String KafkaClientPassword;

    private Long createTime;

    private Long updateTime;

    /**
     * webhook
     */
    private DingDingRobotParam dingDingRobotParam;

    @Override
    public int compareTo(SubscribeSyncConfig o) {
        return Long.compare(o.updateTime, updateTime);
    }

    public enum SynncConfigEnum {
        SYSLOG, KAFKA,WEBHOOK
    }

}
