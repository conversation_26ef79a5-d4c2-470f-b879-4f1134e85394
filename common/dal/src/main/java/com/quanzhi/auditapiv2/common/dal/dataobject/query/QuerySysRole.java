package com.quanzhi.auditapiv2.common.dal.dataobject.query;

import com.quanzhi.auditapiv2.common.dal.common.entity.QueryBase;
import lombok.Data;
import org.bson.types.ObjectId;

import java.util.List;

/**
 * @Description: 用户角色实体类
 * @Author: danniel.yu
 * @Date: 2020-05-11 15:24
 */
@Data
public class QuerySysRole  extends QueryBase {
    private static final long serialVersionUID = 1L;

    /**
     * 角色id
     */
    private String id;

    private List<String> ids;

    /**
     * 角色名称
     */
    private String roleName;

    private Integer envFlag;

    /**
     * 角色标识
     */
    private String roleSign;
    /**
     * 角色状态
     */
    private Integer status;


}
