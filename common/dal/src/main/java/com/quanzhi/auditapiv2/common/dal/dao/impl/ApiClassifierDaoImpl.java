package com.quanzhi.auditapiv2.common.dal.dao.impl;

import com.quanzhi.audit_core.common.model.ApiClassification;
import com.quanzhi.auditapiv2.common.dal.dao.IApiClassifierDao;
import com.quanzhi.auditapiv2.common.dal.dao.base.impl.NacosBaseDaoImpl;
import com.quanzhi.auditapiv2.common.util.utils.DataUtil;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository("apiClassifierDao")
public class ApiClassifierDaoImpl extends NacosBaseDaoImpl<ApiClassification> implements IApiClassifierDao {

    @Override
    public Boolean checkNameExist(String name, String excludeId) {

        List<ApiClassification> list = getAll();

        for (int i = 0; i < list.size(); i++) {

            ApiClassification apiClassification = list.get(i);
            if (apiClassification.getDelFlag()) {
                continue;
            }
            if (!excludeId.equals(apiClassification.getId()) && name.equals(apiClassification.getName())&& (DataUtil.isEmpty(apiClassification.getDelFlag()) || !apiClassification.getDelFlag())) {
                return true;
            }
        }

        return false;
    }
}
