package com.quanzhi.auditapiv2.common.dal.entity;

import com.quanzhi.auditapiv2.common.util.utils.DataUtil;
import com.quanzhi.auditapiv2.common.util.utils.DateUtil;
import lombok.Data;

import java.util.Date;

/**
 * Created by she<PERSON><PERSON> on 2017/11/9.
 */
@Data
public class SysUserGroupModel {

    private String id;

    private String name;

    private String params;

    private Long updateTime;

    private String updateTimeFormat;

    public enum GroupIdEnum {

        /**
         * 系统管理员
         */
        SYS_ADMIN("1"),

        /**
         * 审计管理员
         */
        AUDIT_ADMIN("2"),

        /**
         * 普通管理员
         */
        WEB_ADMIN("3");

        private String val;

        GroupIdEnum(String val) {
            this.val = val;
        }

        public String val() {
            return this.val;
        }
    }


    public String getUpdateTimeFormat() {
        if (DataUtil.isEmpty(updateTime)) {
            return "0000-00-00 00:00:00";
        } else {
            return DateUtil.format(new Date(updateTime), DateUtil.DATE_PATTERN.YYYY_MM_DD_HH_MM_SS);
        }
    }

}
