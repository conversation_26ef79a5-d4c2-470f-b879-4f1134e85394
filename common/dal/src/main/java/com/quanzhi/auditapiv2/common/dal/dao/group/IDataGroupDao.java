package com.quanzhi.auditapiv2.common.dal.dao.group;

import com.quanzhi.auditapiv2.common.dal.dataobject.DataGroup;
import com.quanzhi.auditapiv2.common.dal.dto.group.DataGroupDto;
import com.quanzhi.auditapiv2.common.dal.dto.group.DataGroupSearchDto;
import com.quanzhi.auditapiv2.common.util.dto.ListOutputDto;
import org.springframework.data.mongodb.core.query.Query;

import java.util.List;

/**
 * <AUTHOR>
 * create at 2024/11/13 14:36
 * @description:
 **/
public interface IDataGroupDao {

    List<DataGroup> find(Query query);

    List<DataGroup> find(DataGroupSearchDto dataGroupSearchDto);

    long count(Query query);

    DataGroup save(DataGroup dataGroup);


    ListOutputDto<DataGroup> page(DataGroupSearchDto dataGroupSearchDto);




}