package com.quanzhi.auditapiv2.common.dal.dao.impl;

import com.quanzhi.audit_core.common.model.EmailFeatureLabel;
import com.quanzhi.auditapiv2.common.dal.dao.IEmailFeatureLabelDao;
import com.quanzhi.auditapiv2.common.dal.dao.base.impl.NacosBaseDaoImpl;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public class EmailFeatureLabelDaoImpl extends NacosBaseDaoImpl<EmailFeatureLabel> implements IEmailFeatureLabelDao {

    @Override
    public Boolean checkNameExist(String name,String excludeId) {

        List<EmailFeatureLabel> list = getAll();

        for(int i = 0; i < list.size();i++) {

            EmailFeatureLabel emailFeatureLabel = list.get(i);

            if(!emailFeatureLabel.getId().equals(excludeId) && emailFeatureLabel.getName().equals(name)) {
                return true;
            }
        }

        return false;
    }


}
