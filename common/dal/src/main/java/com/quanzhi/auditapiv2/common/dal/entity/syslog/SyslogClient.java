package com.quanzhi.auditapiv2.common.dal.entity.syslog;

import java.io.IOException;

public abstract class SyslogClient {

    protected static char[] DelimiterSequence = new char[]{'\n'};

    protected final String host;

    protected final Integer port;

    public SyslogClient(String host,Integer port){
        this.host = host;
        this.port = port;
    }

    /**
     * 当前连接是有效的。
     */
    public abstract boolean effective();

    public abstract void close();

    public abstract void flush();

    public abstract void write(char[] chars)throws IOException;

}
