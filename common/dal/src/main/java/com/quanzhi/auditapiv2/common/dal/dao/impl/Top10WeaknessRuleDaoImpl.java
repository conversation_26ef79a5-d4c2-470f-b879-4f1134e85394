package com.quanzhi.auditapiv2.common.dal.dao.impl;

import com.quanzhi.auditapiv2.common.dal.dao.ITop10WeaknessRuleDao;
import com.quanzhi.auditapiv2.common.dal.entity.weakness.Top10Weakness;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * @author: yangzixian
 * @date: 2023.05.24 19:21
 * @description:
 */
@Repository
public class Top10WeaknessRuleDaoImpl implements ITop10WeaknessRuleDao {

    private final MongoTemplate mongoTemplate;

    private String collectionName = "top10WeaknessRule";

    public Top10WeaknessRuleDaoImpl(MongoTemplate mongoTemplate) {
        this.mongoTemplate = mongoTemplate;
    }

    @Override
    public List<Top10Weakness> getAll() {
        return mongoTemplate.findAll(Top10Weakness.class, collectionName);
    }

}
