package com.quanzhi.auditapiv2.common.dal.dao;

import com.quanzhi.auditapiv2.common.dal.entity.riskEolink.BlockingStrategyLog;
import com.quanzhi.auditapiv2.common.dal.entity.riskEolink.BlockingStrategyLogSearch;

import java.util.List;

/**
 * 《阻断策略日志持久层接口》
 */
public interface IBlockingStrategyLogDto{

    /**
     *
     * @return
     */
    List<BlockingStrategyLog> selectBlockingStartegyLogList(BlockingStrategyLogSearch strategyLogSearch);

    BlockingStrategyLog selectDetail(String blockId);

    BlockingStrategyLog selectDetail(String blockId, String ip, String url);

    BlockingStrategyLog insertStartegyLog(BlockingStrategyLog log);

    void updateStartegyLog(BlockingStrategyLog log);

    long selectCountStartegyLog(BlockingStrategyLogSearch strategyLogSearch);
}
