package com.quanzhi.auditapiv2.common.dal.aop;

import com.quanzhi.auditapiv2.common.dal.dao.ISysConfigDao;
import com.quanzhi.auditapiv2.common.util.utils.DataUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeansException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.config.BeanPostProcessor;

import java.lang.reflect.Field;

/**
 * @author: zhousong
 * @data: 2018/10/9
 */
@Slf4j
public class SysConfigProcessor implements BeanPostProcessor {

    @Autowired
    private ISysConfigDao sysConfigDao;

    @Override
    public Object postProcessBeforeInitialization(Object bean, String beanName) throws BeansException {
        Field[] fields = bean.getClass().getDeclaredFields();
        for (Field field : fields) {
            if(!field.isAccessible()){
                field.setAccessible(true);
            }

            // 判断字段上是否有SysConfigValue
            SysConfigValue sysConfigValue = field.getAnnotation(SysConfigValue.class);
            if (DataUtil.isEmpty(sysConfigValue)) {
                continue;
            }

            // 获取系统配置名称，从数据库中获取配置值
            String sysConfigName = sysConfigValue.value();
            String sysConfigStr = sysConfigDao.getPropertyValue(sysConfigName);

            if (DataUtil.isEmpty(sysConfigStr)) {
                log.info("系统配置不存在, sysConfigName: {}", sysConfigName);
                continue;
            }

            // 设置配置值
            try {
                field.set(bean, sysConfigStr);
            } catch (Exception e) {
                log.warn("重设系统配置字段失败, sysConfigName: {}, value: {}", sysConfigName,sysConfigStr);
            }
        }
        return bean;
    }

    @Override
    public Object postProcessAfterInitialization(Object bean, String beanName) throws BeansException {

        return bean;
    }
}
