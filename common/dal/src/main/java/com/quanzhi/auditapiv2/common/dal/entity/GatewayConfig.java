package com.quanzhi.auditapiv2.common.dal.entity;

import com.quanzhi.auditapiv2.common.dal.dto.GatewayDto;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 *
 * 《网关配置》
 *
 *
 * @Project:
 * @Module ID:
 * @Comments:
 * @JDK version used:      <JDK1.8>
 * <AUTHOR> [<EMAIL>]
 * @since 2020-04-08-上午9:56:10
 */
@Data
public class GatewayConfig {

    @ApiModelProperty(value = "id", name = "id")
    private String id;

    @ApiModelProperty(value = "网关别名", name = "name")
    private String name;

    @ApiModelProperty(value = "网关状态", name ="status")
    private Integer status;
    /**
     * 网关IP
     */
    @ApiModelProperty(value = "网关IP", name = "ip")
    private String ip;

    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间", name = "createTime")
    private Long createTime;

    /**
     * 更新时间
     */
    @ApiModelProperty(value = "更新时间", name = "updateTime")
    private Long updateTime;

    /**
     * 部署方式
     */
    @ApiModelProperty(value = "部署方式", name = "type")
    private String type;

    /**
     * 网关版本
     */
    @ApiModelProperty(value = "网关版本", name = "version")
    private GatewayDto.GatewayVersion version;

    /**
     * 网关授权状态
     */
    @ApiModelProperty(value = "网关授权状态", name = "licenseConfig")
    private GatewayDto.LicenseConfig licenseConfig;
}
