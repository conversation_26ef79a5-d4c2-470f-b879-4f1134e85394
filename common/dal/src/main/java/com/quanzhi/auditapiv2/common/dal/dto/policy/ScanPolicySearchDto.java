package com.quanzhi.auditapiv2.common.dal.dto.policy;

import com.quanzhi.auditapiv2.common.dal.dto.query.BaseSearchDto;
import com.quanzhi.auditapiv2.common.util.constant.DataConstant;
import com.quanzhi.auditapiv2.common.util.utils.DataUtil;
import com.quanzhi.metabase.core.model.query.MetabaseQuery;
import com.quanzhi.metabase.core.model.query.Predicate;
import lombok.Data;

/**
 * @Author: Linlm
 * @Description:
 * @Date: Created in 2020/6/2 下午5:59
 */
@Data
public class ScanPolicySearchDto extends BaseSearchDto {
    private static final long serialVersionUID = 3975929130973577605L;

    /**
     * 名称：模糊
     */
    private String name;

    public MetabaseQuery createMetabaseQuery(){
        MetabaseQuery query = new MetabaseQuery();
        query.where(DataConstant.COMMON_FIELD_DELFLAG, Predicate.IS, false);

        if (DataUtil.isNotEmpty(name)){
            query.where(DataConstant.COMMON_FIELD_NAME, Predicate.LIKE, name);
        }

        fillMetabaseQuery(query);

        return query;
    }
}
