package com.quanzhi.auditapiv2.common.dal.dto.docx;

import lombok.Data;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 *
 * 《返回数据量可修改》
 *
 *
 * @Project:
 * @Module ID:
 * @Comments:
 * @JDK version used:      <JDK1.8>
 * <AUTHOR> [<EMAIL>]
 * @since 2020-08-28-下午16:52:18
 */
@Data
public class Querynolimit extends ApiWeaknessExportWord {

    @Mapper
    public interface QuerynolimitMapper {

        Querynolimit.QuerynolimitMapper INSTANCE = Mappers.getMapper(Querynolimit.QuerynolimitMapper.class);
        Querynolimit convert(ApiWeaknessExportWord apiWeaknessExportWord);
    }
}
