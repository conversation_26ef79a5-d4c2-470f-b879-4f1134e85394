package com.quanzhi.auditapiv2.common.dal.dto.data;

import com.quanzhi.auditapiv2.common.dal.dto.ExporTitleFieldDto;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * @Author: yangzx
 * @Date: 2024/10/9 11:12
 */
@Data
public class DataInfoOperatorDto {

    @ApiModelProperty("数据名称")
    private String dataName;

    @ApiModelProperty("数据类型")
    private List<String> dataType;

    @ApiModelProperty("数据级别")
    private List<Integer> dataLevel;

    @ApiModelProperty("数据风险等级")
    private Integer dataRiskLevel;

    @ApiModelProperty("域名")
    private String host;

    @ApiModelProperty("应用名称")
    private String appName;

    @ApiModelProperty("关注/停用状态")
    private Boolean markState;

    @ApiModelProperty("首次发现时间开始")
    private Long firstTimeStart;

    @ApiModelProperty("首次发现时间结束")
    private Long firstTimeEnd;

    @ApiModelProperty("最近活跃时间开始")
    private Long lastTimeStart;

    @ApiModelProperty("最近活跃时间结束")
    private Long lastTimeEnd;

    @ApiModelProperty("排序方式")
    private Integer sort;

    @ApiModelProperty("排序字段")
    private String sortField;

    @ApiModelProperty("每页显示数量")
    private Integer limit = 10;

    @ApiModelProperty("页码")
    private Integer page = 1;

    @ApiModelProperty("分组字段")
    private String groupField;

    @ApiModelProperty("批量操作id数组")
    private List<String> ids;

    @ApiModelProperty("批量操作id")
    private String id;

    @ApiModelProperty("关注数据")
    private List<String> focusDatas;

    /**
     * 分组字段枚举
     */
    public enum GroupFieldEnum {

        DATA_TYPE("dataType"),

        DATA_RISK_LEVEL("dataRiskLevel"),

        DATA_LEVEL("dataLevel");

        String name;

        GroupFieldEnum(String name) {

            this.name = name;
        }

        public String getName() {
            return name;
        }

    }

}
