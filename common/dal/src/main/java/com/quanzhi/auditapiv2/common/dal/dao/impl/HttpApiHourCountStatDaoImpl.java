package com.quanzhi.auditapiv2.common.dal.dao.impl;

import com.quanzhi.auditapiv2.common.dal.dao.IHttpApiHourCountStatDao;
import com.quanzhi.auditapiv2.common.dal.entity.HttpApiHourCountStat;
import com.quanzhi.auditapiv2.common.dal.entity.HttpApiIpHourCountStat;
import org.bson.Document;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Repository;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Repository("HttpApiHourCountStatDao")
public class HttpApiHourCountStatDaoImpl implements IHttpApiHourCountStatDao {

    @Autowired
    private MongoTemplate mongoTemplate;

    @Override
    public Map<String, Long[]> getHourStatByUriListAndDay(List<String> uriList, String day) {
        // 构建查询条件：匹配 uri 和 day
        Query query = new Query();
        query.addCriteria(Criteria.where("uri").in(uriList).and("day").is(day));

        // 执行查询，直接将结果映射到实体类 HttpApiHourCountStat
        List<HttpApiHourCountStat> results = mongoTemplate.find(query, HttpApiHourCountStat.class);

        // 将查询结果转换为 Map<String, Long[]> 格式
        Map<String, Long[]> hourStatMap = new HashMap<>();

        for (HttpApiHourCountStat stat : results) {
            String uri = stat.getUri(); // 获取 URI 字段
            Map<String, Map<String, Long>> dateStats = stat.getDateStats(); // 获取 dateStats 字段

            // 创建一个长度为 24 的 Long 数组，初始化为 0L
            Long[] hourStatsArray = new Long[24];
            Arrays.fill(hourStatsArray, 0L);

            if (dateStats != null) {
                // 遍历 dateStats，填充 hourStatsArray
                for (Map.Entry<String, Map<String, Long>> entry : dateStats.entrySet()) {
                    try {
                        int hour = Integer.parseInt(entry.getKey()); // 小时数（0-23）
                        if (hour >= 0 && hour < 24) { // 确保小时数在 0-23 范围内
                            Long amount = entry.getValue().get("amount"); // 获取统计值
                            hourStatsArray[hour] = amount != null ? amount : 0L; // 避免 null 值
                        }
                    } catch (NumberFormatException e) {
                        System.err.println("Invalid hour format: " + entry.getKey());
                    }
                }
            }

            // 将结果存入 hourStatMap
            hourStatMap.put(uri, hourStatsArray);
        }

        return hourStatMap;
    }

    @Override
    public List<HttpApiIpHourCountStat> getHourReqRspStat(List<String> uriList, String day) {
        Query query = new Query();
        query.addCriteria(Criteria.where("uri").in(uriList).and("day").is(day));
        return mongoTemplate.find(query, HttpApiIpHourCountStat.class);
    }
}
