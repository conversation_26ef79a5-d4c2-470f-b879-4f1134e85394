package com.quanzhi.auditapiv2.common.dal.dao;

import com.quanzhi.audit_core.common.model.HostAnalysisConfig;
import com.quanzhi.auditapiv2.common.dal.dao.base.INacosBaseDao;
import org.springframework.stereotype.Repository;

/**
 * 
 * 《域名解析配置持久层Nacos接口》
 * 
 * 
 * @Project: 
 * @Module ID:
 * @Comments: 
 * @JDK version used:      <JDK1.8> 
 * <AUTHOR> [<EMAIL>]
 * @since 2022-07-25-上午9:56:10
 */
@Repository
public interface IHostAnalysisConfigNacosDao extends INacosBaseDao<HostAnalysisConfig> {
}
