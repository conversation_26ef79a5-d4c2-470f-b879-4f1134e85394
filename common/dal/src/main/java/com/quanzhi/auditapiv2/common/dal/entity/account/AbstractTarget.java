package com.quanzhi.auditapiv2.common.dal.entity.account;

import com.quanzhi.metabase.core.model.dto.RiskLevelMatchDto;
import com.quanzhi.metabase.core.model.node.NodeMeta;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;
import org.bson.types.ObjectId;

import java.util.List;
import java.util.Map;
import java.util.Set;

@Data
@SuperBuilder
@AllArgsConstructor
@NoArgsConstructor
public abstract class AbstractTarget {
    private String id;
    private Set<String> uaTypes;
    private Set<String> rspDataLabelList;
    private Set<String> reqDataLabelList;
    private Set<String> appUriList;
    private Map<String, Set<String>> uaByUaType;
    private StatPoint statPoint;
    private long visitCnt;
    private String firstDate;
    private String lastDate;
    private RiskLevelMatchDto.Risk riskInfo;
    private String riskLevelName;
    private Integer riskLevel;
    private Long reviveTime;
    private Long rspDataLabelCnt;
    private Long updateTime;
    private List<NodeMeta> nodes;

    public void setId(ObjectId id) {
        this.id = id.toHexString();
    }
}
