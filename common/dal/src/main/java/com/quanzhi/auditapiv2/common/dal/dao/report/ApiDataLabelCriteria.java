package com.quanzhi.auditapiv2.common.dal.dao.report;

import com.quanzhi.auditapiv2.common.dal.base.Page;
import lombok.Data;

import java.util.List;

/**
 * @Author: HaoJun
 * @Date: 2020/9/14 3:03 下午
 */
@Data
public class ApiDataLabelCriteria extends Page {
    private List<String> reqDataLabels;
    private List<String> rspDataLabels;
    private List<String> featureLabels;
    private List<String> visitDomains;
    private List<String> terminals;
    private Long startDiscoverTimestamp;
    private Long endDiscoverTimestamp;
    private String dataLabel;
    private long startTimestamp;
    private long endTimestamp;
    private List<String> classifications;
    private String uri;
}
