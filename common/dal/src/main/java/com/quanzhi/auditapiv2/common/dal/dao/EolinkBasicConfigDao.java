package com.quanzhi.auditapiv2.common.dal.dao;

import com.quanzhi.auditapiv2.common.dal.entity.riskEolink.EolinkBasicConfigDto;

public interface EolinkBasicConfigDao {

    /**
     * 新增数据
     */
    EolinkBasicConfigDto saveBasicConfig(EolinkBasicConfigDto licenseConfigDto);

    /**
     * 修改
     * @param licenseConfigDto
     * @return
     */
    EolinkBasicConfigDto editBasicConfig(EolinkBasicConfigDto licenseConfigDto);

    EolinkBasicConfigDto findByProductId(String productId);

    void deleteByProductId(String productId);

}
