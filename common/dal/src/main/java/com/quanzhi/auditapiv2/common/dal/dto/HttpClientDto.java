package com.quanzhi.auditapiv2.common.dal.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Map;

/**
 *
 * 《HttpClient请求DTO》
 *
 *
 * @Project:
 * @Module ID:
 * @Comments:
 * @JDK version used:      <JDK1.8> 
 * <AUTHOR> [<EMAIL>]
 * @since 2020-04-08-上午9:56:10
 */
@Data
@ApiModel
public class HttpClientDto {

    /**
     * 请求地址
     */
    @ApiModelProperty(value = "请求地址", name = "url", required = true)
    private String url;

    /**
     * 请求参数
     */
    @ApiModelProperty(value = "请求参数", name = "params", required = false)
    private Map<String, Object> params;
}
