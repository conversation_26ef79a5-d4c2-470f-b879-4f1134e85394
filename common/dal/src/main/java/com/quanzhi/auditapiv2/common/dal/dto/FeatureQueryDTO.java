package com.quanzhi.auditapiv2.common.dal.dto;

import com.quanzhi.re.core.domain.entity.variable.Period;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

@Data
@NoArgsConstructor
@ApiModel("可选特征/变量查询")
public class FeatureQueryDTO implements Serializable {

    @ApiModelProperty("根据特征/变量type查询,例如：QUINTIN-五元组，API-API，APP-应用，MESSAGE-报文等")
    private List<String> types;
    @ApiModelProperty("根据特征/变量name查询")
    private List<String> names;
    @ApiModelProperty("根据特征/变量值类型kind查询，例如：String,List,Integer,Long,Map等")
    private List<String> kinds;

    private String subjectObj;

    private Period period;

    private String dataValue;

    /**
     * 这个主要是不同的配置需要的特征约束有点区别，比如终端类型是有基线的，但是基线只能在风险规则配置，API标签是不能配置的，所以需要区分是哪个配置
     */
    @ApiModelProperty("配置的规则，例如：APP_LABEL-应用标签，API_LABEL-API标签，API_FILTER-资产过滤，WEAKNESS_RULE-弱点规则，RISK_RULE-风险规则")
    private String category;
}
