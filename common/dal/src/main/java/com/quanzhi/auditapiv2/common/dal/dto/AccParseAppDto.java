package com.quanzhi.auditapiv2.common.dal.dto;

import com.quanzhi.metabase.core.model.ResourceEntity;
import com.quanzhi.metabase.core.model.http.HttpApiResource;
import com.quanzhi.metabase.core.model.http.HttpAppResource;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.io.Serializable;
import java.util.List;
import java.util.Set;

/**
 * @ClassName AccParseAppDto
 * @Description TODO
 * <AUTHOR>
 * @Date 2020/10/16 11:59
 **/
@Data
public class AccParseAppDto extends ResourceEntity implements Serializable {

    private List<String> tags;

    /**
     * 推荐解析配置数量
     */
    private long recommendCount;

    private String name;
    private String host;

    /**
     * 账号配置
     */
    private List<HttpApiResource.AccParseConfigs> userParseConfigs;

    /**
     * session配置
     */
    private List<HttpApiResource.AccParseConfigs> sessionParseConfigs;

    /**
     * 是否开启账号解析
     */
    private Boolean accParseEnable;

    /**
     * 已启用解析数量
     */
    private long enableReCount;

    /**
     * 是否关注
     */
    @ApiModelProperty(value = "是否关注", name = "followed")
    private Boolean followed;

    /**
     * 统计信息
     */
    @ApiModelProperty(value = "统计信息", name = "appStat")
    private HttpAppResource.AppStat appStat = new HttpAppResource.AppStat();

    /**
     * 是否启用IP快速关联
     */
    private Boolean isIpAssociatedEnable;

    // 应用下的账号列表
    private Set<String> accountList;

    // 应用下解析出账号的接口数
    private Long apiCntWithAccount;

    // 账号解析率
    private String accountRate;

    @Mapper
    public interface AccParseAppDtoMapper {

       AccParseAppDtoMapper INSTANCE = Mappers.getMapper(AccParseAppDtoMapper.class);

        AccParseAppDto convert(HttpAppResource httpAppResource);

        List<AccParseAppDto> convert(List<HttpAppResource> httpAppResources);

    }
}
