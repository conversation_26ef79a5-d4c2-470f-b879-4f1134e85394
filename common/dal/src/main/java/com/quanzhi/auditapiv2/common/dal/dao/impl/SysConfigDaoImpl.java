package com.quanzhi.auditapiv2.common.dal.dao.impl;

import com.quanzhi.auditapiv2.common.dal.dao.ISysConfigDao;
import com.quanzhi.auditapiv2.common.dal.dao.base.INacos;
import com.quanzhi.auditapiv2.common.dal.enums.NacosConfigEnum;
import com.quanzhi.auditapiv2.common.util.utils.ServiceException;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.formula.functions.T;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

@Repository("sysConfigDao")
@Slf4j
public class SysConfigDaoImpl implements ISysConfigDao {
    @Autowired
    protected INacos<T> nacosImpl;

    private String sysNacosDataId = NacosConfigEnum.SYS_CONFIG.getNacosBaseConfig().getDataId();

    private String sysNacosGroup =  NacosConfigEnum.SYS_CONFIG.getNacosBaseConfig().getGroup();

    @Override
    public String getPropertyValue (String property) {

        try {
            return nacosImpl.getPropertyValue(property,sysNacosDataId,sysNacosGroup);
        } catch (Exception e) {
            log.warn("getPropertyValue failed", e);
            throw new ServiceException("无信息");
        }

    }
}
