package com.quanzhi.auditapiv2.common.dal.entity;

import lombok.Data;

/**
 * @author: zhousong
 * @data: 2018/3/4
 * 定时任务运行状态类
 */
@Data
public class ScheduleJobStatus {

    /**
     * 任务名称
     */
    private String job;

    /**
     * 日期
     */
    private String date;

    /**
     * 最后一条处理事件的时间
     */
    private Long lastEventTime;

    /**
     * 开始时间
     */
    private Long startTime;

    /**
     * 结束时间
     */
    private Long endTime;

    /**
     * 花费时长
     */
    private Long cost;

    /**
     * 错误信息
     */
    private String errorMsg;

    /**
     * 状态
     */
    private Integer status;

    public enum StatusEnum {

        /**
         * 执行中
         */
        RUNNING(1),

        /**
         * 结束
         */
        STOP(2),

        /**
         * 异常退出
         */
        EXCEPTION(0);

        private int val;

        StatusEnum(int val) {
            this.val = val;
        }

        public int val() {
            return this.val;
        }
    }


}
