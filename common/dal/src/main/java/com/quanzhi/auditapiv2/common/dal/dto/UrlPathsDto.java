package com.quanzhi.auditapiv2.common.dal.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 *
 * 《应用结构dto》
 *
 *
 * @Project:
 * @Module ID:
 * @Comments:
 * @JDK version used:      <JDK1.8>
 * <AUTHOR> [<EMAIL>]
 * @since 2020-04-08-上午9:56:10
 */
@Data
@ApiModel
public class UrlPathsDto {

    /**
     * url节点
     */
    @ApiModelProperty(value = "url节点", name = "urlNode")
    private Integer urlNode;


    /**
     * 用于前端排序
     */
    @ApiModelProperty(value = "排序", name = "locateIndex")
    private Integer locateIndex;

    /**
     * url路径
     */
    @ApiModelProperty(value = "url路径", name = "urlPath")
    private String urlPath;

    /**
     * url子节点数量
     */
    @ApiModelProperty(value = "url子节点数量", name = "urlCount")
    private Long urlCount;

    
    /**
     * API ID
     */
    @ApiModelProperty(value = "API ID", name = "apiId")
    private String apiId;

    /**
     * API URI
     */
    @ApiModelProperty(value = "API URI", name = "apiUri")
    private String apiUri;
    /**
     * API URL
     */
    @ApiModelProperty(value = "API URL", name = "apiUrl")
    private String apiUrl;

    /**
     * 请求方式
     */
    @ApiModelProperty(value = "请求方式", name = "methods")
    private List<String> methods;

    /**
     * url路径与节点
     */
    private List<HttpApiSearchDto.UrlPaths> urlPathList;
}
