//package com.quanzhi.auditapiv2.common.dal.dto.weakness;
//
//import com.quanzhi.auditapiv2.common.dal.dto.ApiWeaknessDto;
//import com.quanzhi.metabase.core.model.http.HttpAppResource;
//import com.quanzhi.metabase.core.model.http.weakness.Sample;
//import io.swagger.annotations.ApiModelProperty;
//import io.swagger.annotations.ApiOperation;
//import lombok.Data;
//import org.mapstruct.Mapper;
//import org.mapstruct.Mapping;
//import org.mapstruct.Mappings;
//import org.mapstruct.factory.Mappers;
//
//import java.util.List;
//
///**
// * @Auther: yangzixian
// * @Date: 2021/12/1 09:58
// * @Description: 运营中心存储Dto
// */
//@Data
//public class WeaknessOperationDto {
//
//    private String id;
//    private String weaknessNumber;
//    private String sourceSystem;
//    private boolean delFlag;
//    private boolean userDelFlag;
//    private long createTime;
//    private long updateTime;
//    private String uri;
//    private String app;
//    private Integer modifyFlag;
//    private String host;
//    private String appUri;
//    private long earlyTimestamp;
//    private long lastTimestamp;
//    private long fixedTimestamp;
//    private String weaknessId;
//    private String name;
//    private int level;
//    private String levelName;
//    private List<Sample> samples;
//    private String state;
//    private String stateName;
//    private String oldState;
//    private String lastOperator;
////    private String apiList;
//    private ApiInfo httpApi;
//    private String category;
//    private String type;
//    private String suggestion;
//    private String solution;
//    private String description;
//
//    @Data
//    public static class ApiInfo {
//        private String uri;
//        private String app;
//        private String apiUrl;
//        private String appUri;
//        private String appId;
//        private List<HttpAppResource.Department> departments;
//        private int maxRspLabelValueCount;
//        private int rspLabelCount;
//        private List<String> visitDomains;
//        private List<String> visitDomainsValue;
//        private List<String> deployDomains;
//        private List<String> deployDomainsValue;
//        private List<String> classifications;
//        private List<String> classificationValue;
//        private List<String> rspDataLabels;
//        private List<String> rspDataLabelsValue;
//        private List<String> reqDataLabels;
//        private List<String> reqDataLabelsValue;
//        private List<String> dataLabels;
//        private List<String> featureLabels;
//        private List<String> featureLabelsValue;
//        private List<String> methods;
//        private List<String> terminals;
//    }
//
//    @Mapper
//    public interface WeaknessOperationDtoMapper {
//
//        WeaknessOperationDtoMapper INSTANCE = Mappers.getMapper(WeaknessOperationDtoMapper.class);
//
//        @Mappings({
//                @Mapping(source = "httpApiDto", target = "httpApi")
//        })
//        WeaknessOperationDto convert(ApiWeaknessDto apiWeaknessDto);
//
//    }
//
//}
