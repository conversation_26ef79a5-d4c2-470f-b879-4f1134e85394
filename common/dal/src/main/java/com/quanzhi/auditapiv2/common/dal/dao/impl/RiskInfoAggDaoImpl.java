package com.quanzhi.auditapiv2.common.dal.dao.impl;

import com.mongodb.client.MongoCursor;
import com.quanzhi.audit_core.common.model.RiskInfoAgg;
import com.quanzhi.audit_core.common.utils.DataUtil;
import com.quanzhi.auditapiv2.common.dal.dao.IRiskInfoAggDao;
import com.quanzhi.auditapiv2.common.dal.dto.RiskLevelMatchDto;
import com.quanzhi.auditapiv2.common.dal.dto.query.RiskInfoAggCriteriaDto;
import com.quanzhi.auditapiv2.common.util.utils.ConstantUtil;
import org.bson.Document;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.aggregation.Aggregation;
import org.springframework.data.mongodb.core.aggregation.AggregationOperation;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Repository;

import java.util.ArrayList;
import java.util.List;

import static org.springframework.data.mongodb.core.aggregation.Aggregation.newAggregationOptions;

/**
 * @ClassName RiskInfoAggDaoImpl
 * @Description TODO
 * <AUTHOR>
 * @Date 2020/11/23 11:31
 **/
@Repository
public class RiskInfoAggDaoImpl implements IRiskInfoAggDao {

    @Autowired
    private MongoTemplate mongoTemplate;

    private String collectionName = "riskInfo";

    @Override
    public List<RiskInfoAgg> getRiskInfoAggList(RiskInfoAggCriteriaDto riskInfoAggCriteriaDto) {
        Query query = new Query();

        //查询条件
        if (DataUtil.isNotEmpty(riskInfoAggCriteriaDto.getValue())) {
            query.addCriteria(Criteria.where("entities.value").regex((String) riskInfoAggCriteriaDto.getValue()));
        }
        if (DataUtil.isNotEmpty(riskInfoAggCriteriaDto.getDefinition())) {
            query.addCriteria(Criteria.where("policySnapshot.definition").is(riskInfoAggCriteriaDto.getDefinition()));
        }
        if (DataUtil.isNotEmpty(riskInfoAggCriteriaDto.getState())) {
            query.addCriteria(Criteria.where("state").is(riskInfoAggCriteriaDto.getState()));
        }
        if (DataUtil.isNotEmpty(riskInfoAggCriteriaDto.getLevel())) {
            query.addCriteria(Criteria.where("level").is(riskInfoAggCriteriaDto.getLevel()));
        }
        if (DataUtil.isNotEmpty(riskInfoAggCriteriaDto.getStartTime()) && DataUtil.isNotEmpty(riskInfoAggCriteriaDto.getEndTime())) {
            query.addCriteria(new Criteria().andOperator(
                    Criteria.where("riskTime").gte(riskInfoAggCriteriaDto.getStartTime()),
                    Criteria.where("riskTime").lte(riskInfoAggCriteriaDto.getEndTime())));
        }
        //排序
        if (DataUtil.isNotEmpty(riskInfoAggCriteriaDto.getField()) && DataUtil.isNotEmpty(riskInfoAggCriteriaDto.getSort())) {
            if (riskInfoAggCriteriaDto.getSort() == ConstantUtil.Sort.ASC) {
                query.with(Sort.by(Sort.Order.asc(riskInfoAggCriteriaDto.getField())));
            } else if (riskInfoAggCriteriaDto.getSort() == ConstantUtil.Sort.DESC) {
                query.with(Sort.by(Sort.Order.desc(riskInfoAggCriteriaDto.getField())));
            } else {
                query.with(Sort.by(Sort.Order.desc(riskInfoAggCriteriaDto.getField())));
            }
        }
        //分页
        query.limit(riskInfoAggCriteriaDto.getLimit());
        query.skip((riskInfoAggCriteriaDto.getPage() - 1) * riskInfoAggCriteriaDto.getLimit());
        return mongoTemplate.find(query, RiskInfoAgg.class);
    }

    @Override
    public Long getRiskInfoAggCount(RiskInfoAggCriteriaDto riskInfoAggCriteriaDto) {
        Query query = new Query();

        //查询条件
        if (DataUtil.isNotEmpty(riskInfoAggCriteriaDto.getValue())) {
            query.addCriteria(Criteria.where("entities.value").regex((String) riskInfoAggCriteriaDto.getValue()));
        }
        if (DataUtil.isNotEmpty(riskInfoAggCriteriaDto.getDefinition())) {
            query.addCriteria(Criteria.where("policySnapshot.definition").is(riskInfoAggCriteriaDto.getDefinition()));
        }
        if (DataUtil.isNotEmpty(riskInfoAggCriteriaDto.getState())) {
            query.addCriteria(Criteria.where("state").is(riskInfoAggCriteriaDto.getState()));
        }
        if (DataUtil.isNotEmpty(riskInfoAggCriteriaDto.getLevel())) {
            query.addCriteria(Criteria.where("level").is(riskInfoAggCriteriaDto.getLevel()));
        }
        if (DataUtil.isNotEmpty(riskInfoAggCriteriaDto.getStartTime()) && DataUtil.isNotEmpty(riskInfoAggCriteriaDto.getEndTime())) {
            query.addCriteria(new Criteria().andOperator(
                    Criteria.where("riskTime").gte(riskInfoAggCriteriaDto.getStartTime()),
                    Criteria.where("riskTime").lte(riskInfoAggCriteriaDto.getEndTime())));
        }
        return mongoTemplate.count(query, RiskInfoAgg.class);
    }

    @Override
    public long getRiskInfoAggCountByState(List<Integer> stateList, Integer level) {
        Criteria criteria = Criteria.where("state").in(stateList);
        if (level != null) {
            criteria.and("level").is(level);
        }
        return mongoTemplate.count(new Query(criteria), RiskInfoAgg.class);
    }

    // entityType: IP/ACCOUNT/API
    @Override
    public RiskLevelMatchDto.Risk getRiskInfo(String entity, String entityType) {
        List<AggregationOperation> operations = new ArrayList<>();
        operations.add(Aggregation.unwind("entities"));
        operations.add(Aggregation.match(Criteria.where("entities.type").is(entityType).and("entities.value").is(entity)));  // *************
//        operations.add(Aggregation.match(Criteria.where("entities.type").is("ACCOUNT").and("entities.value").is(entity))); // 叶文
//        operations.add(Aggregation.match(Criteria.where("entities.type").is("API").and("entities.value").is(entity))); // 叶文
        operations.add(
                Aggregation.group()
//                Aggregation.group("entities.value")
                        .min("firstTime").as("riskFirstTime")
                        .max("lastTime").as("riskLastTime")
                        .addToSet("policySnapshot.name").as("riskNames")
                        .addToSet("level").as("riskLevels")
                        .push("state").as("state"));
        operations.add(Aggregation.unwind("state"));
        operations.add(
                Aggregation.group("riskFirstTime", "riskLastTime", "riskNames", "riskLevels", "state")  // 2-已确认
                        .count().as("riskCnt")); // 风险数量

        Aggregation aggregation = Aggregation.newAggregation(operations).withOptions(newAggregationOptions().allowDiskUse(true).build());

        List<RiskLevelMatchDto.Risk> appAccountDtos = mongoTemplate.aggregate(aggregation, collectionName, RiskLevelMatchDto.Risk.class).getMappedResults();
        if (DataUtil.isNotEmpty(appAccountDtos)) {
            RiskLevelMatchDto.Risk riskResult = null;

            // 风险总量
            Long totalRiskCnt = 0L;

            // 已确认风险数量
            Long confirmRiskCount = 0L;

            for (RiskLevelMatchDto.Risk risk : appAccountDtos) {
                risk.setLevelNum();

                riskResult = risk;
                totalRiskCnt += risk.getRiskCnt();
                if (RiskInfoAgg.RiskStateEnum.HAS_HANDLE.getValue() == risk.getState()) {
                    confirmRiskCount += risk.getRiskCnt();
                }
            }

            riskResult.setTotalRiskCnt(totalRiskCnt);
            riskResult.setConfirmRiskCount(confirmRiskCount);

            return riskResult;
        }

        return null;
    }

    @Override
    public MongoCursor<Document> getRiskInfoAggCursor() throws Exception {

        Criteria criteria = new Criteria();

        criteria.and("state").in(0, 2);
        criteria.and("entities.type").is("IP");

        Query query = new Query(criteria);

        return mongoTemplate.getCollection("riskInfoAgg").find(query.getQueryObject()).noCursorTimeout(true).batchSize(100).cursor();
    }

    public Double getRiskDimension() {
        long levelLow = mongoTemplate.count(new Query().addCriteria(Criteria
                .where("level").is(1)
                .and("state").is(0)
        ), collectionName);
        long levelMid = mongoTemplate.count(new Query().addCriteria(Criteria
                .where("level").is(2)
                .and("state").is(0)
        ), collectionName);
        long levelHigh = mongoTemplate.count(new Query().addCriteria(Criteria
                .where("level").is(3)
                .and("state").is(0)
        ), collectionName);
        long count = mongoTemplate.count(new Query().addCriteria(Criteria.where("state").is(0)), collectionName);
        return count == 0L ? 0L : (levelHigh + levelMid * 0.5 + levelLow * 0.3) / count;
    }

}
