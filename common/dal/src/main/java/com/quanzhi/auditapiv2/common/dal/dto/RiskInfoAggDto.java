package com.quanzhi.auditapiv2.common.dal.dto;

import com.quanzhi.audit_core.common.model.RiskInfoAgg;
import lombok.Data;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.io.Serializable;
import java.util.List;

/**
 * @ClassName RiskInfoAggDto
 * @Description TODO
 * <AUTHOR>
 * @Date 2020/11/23 11:14
 **/
@Data
public class RiskInfoAggDto implements Serializable {

    private List<RiskInfoAgg.Entity> entities;

    private Integer level;

    private RiskInfoAgg.PolicySnapshot policySnapshot;


    /**
     * 异常时间
     */
    private Long riskTime;

    /**
     * 最近发现时间
     */
    private Long lastTime;

    /**
     * 异常状态
     * @see RiskStateEnum
     */
    private Integer state;

    /**
     * 导出异常时间字段
     */
    private String exportRiskTime;

    /**
     * 导出最近发现时间字段
     */
    private String exportLastTime;

    /**
     * 导出异常等级字段
     */
    private String exportLevel;

    /**
     * 导出异常状态字段
     */
    private String exportState;

    /**
     * 导出异常描述字段
     */
    private String exportDesc;

    /**
     * 导出异常主体字段
     */
    private String exportRiskEntity;

    /**
     * 导出异常规则字段
     */
    private String exportRiskrRule;

    @Mapper
    public interface RiskInfoDtoMapper {

        RiskInfoDtoMapper INSTANCE = Mappers.getMapper(RiskInfoDtoMapper.class);

        RiskInfoAggDto convert(RiskInfoAgg riskInfoAgg);

        List<RiskInfoAggDto> convert(List<RiskInfoAgg> riskInfoAggs);

    }

}

