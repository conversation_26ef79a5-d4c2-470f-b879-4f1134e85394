package com.quanzhi.auditapiv2.common.dal.entity;

import com.quanzhi.metabase.core.model.http.constant.LevelEnum;
import lombok.Data;

/**
 * @Auther: yangzixian
 * @Date: 2021/8/30 20:44
 * @Description:
 */
@Data
public class CountEntity implements Comparable<CountEntity> {
    private LevelEnum level;
    private String name;
    private String id;
    private String type;
    private String typeName;
    private String province;
    private String count;

    @Override
    public int compareTo(CountEntity o) {
        return Long.compare(Long.parseLong(o.count), Long.parseLong(count));
    }
}
