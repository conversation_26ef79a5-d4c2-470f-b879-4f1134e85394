package com.quanzhi.auditapiv2.common.dal.dao.impl;

import com.quanzhi.auditapiv2.common.dal.dao.IHttpApiSampleDao;
import com.quanzhi.auditapiv2.common.dal.dao.base.impl.MetabaseDaoImpl;
import com.quanzhi.metabase.core.model.http.HttpApiSample;
import com.quanzhi.metabase.core.model.query.MetabaseQuery;
import com.quanzhi.metabase.core.model.query.Predicate;
import com.quanzhi.metabase.core.model.query.Sort;
import com.quanzhi.metabase.core.model.query.SortOrder;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 
 * 《接口快照持久层接口实现》
 * 
 * 
 * @Project: 
 * @Module ID:
 * @Comments: 
 * @JDK version used:      <JDK1.8> 
 * <AUTHOR> [<EMAIL>]
 * @since 2020-04-08-上午9:56:10
 */
@Repository
public class HttpApiSampleDaoImpl extends MetabaseDaoImpl<HttpApiSample> implements IHttpApiSampleDao {

    /**
     * 接口快照id查询接口样例列表(分页)
     * @Comments:  <对此方法的描述，可以引用系统设计中的描述>
     * <AUTHOR> [<EMAIL>]
     * @since 2020-04-08 9:56
     * @param
     * @return
     */
    @Override
    public List<HttpApiSample> selectHttpApiSampleListByHttpApiSnapshotId(String httpApiSnapshotId, Integer page, Integer limit) throws Exception {

        MetabaseQuery metabaseQuery = new MetabaseQuery();

        //查询条件
        metabaseQuery.where("delFlag", Predicate.IS, false);
        metabaseQuery.where("snapshotApiId", Predicate.IS, httpApiSnapshotId);
        //分页
        metabaseQuery.skip((page - 1) * limit);
        metabaseQuery.limit(limit);
        //排序
        metabaseQuery.sort(Sort.by("timestamp", SortOrder.DESC));

        return metabaseClientTemplate.find(metabaseQuery, HttpApiSample.class);
    }

    /**
     * 接口快照id查询接口样例数量
     * @Comments:  <对此方法的描述，可以引用系统设计中的描述>
     * <AUTHOR> [<EMAIL>]
     * @since 2020-04-08 9:56
     * @param
     * @return
     */
    @Override
    public Long totalCount(String httpApiSnapshotId) throws Exception {

        MetabaseQuery metabaseQuery = new MetabaseQuery();

        //查询条件
        metabaseQuery.where("delFlag", Predicate.IS, false);
        metabaseQuery.where("snapshotApiId", Predicate.IS, httpApiSnapshotId);

        return metabaseClientTemplate.count(metabaseQuery, HttpApiSample.class);
    }
}
