package com.quanzhi.auditapiv2.common.dal.dao.directive;

import com.quanzhi.auditapiv2.common.dal.entity.directive.Directive;
import com.quanzhi.auditapiv2.common.dal.entity.directive.DirectiveLog;
import com.quanzhi.auditapiv2.common.dal.entity.directive.DirectiveLogSearchDto;
import com.quanzhi.auditapiv2.common.util.utils.DataUtil;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * @Author: yangzx
 * @Date: 2024/7/9 17:35
 */
@Repository
public class DirectiveDao {

    private final MongoTemplate mongoTemplate;

    private String collectionName = "directive";

    public DirectiveDao(MongoTemplate mongoTemplate) {
        this.mongoTemplate = mongoTemplate;
    }

    public Directive findById(String directiveId) {
        return mongoTemplate.findOne(new Query().addCriteria(Criteria.where("_id").is(directiveId)), Directive.class, collectionName);
    }

    public List<Directive> findError() {
        Sort dbSort = Sort.by(Sort.Direction.ASC, "firstTime");
        return mongoTemplate.find(new Query().addCriteria(Criteria
                .where("delFlag").is(false)
                .and("retry").is(true)
                .and("syncResult").is(false)
        ).with(dbSort), Directive.class, collectionName);
    }

    public void updateSyncResult(String id, Boolean syncResult) {
        Update update = new Update();
        update.set("syncResult", syncResult);
        mongoTemplate.updateFirst(new Query().addCriteria(Criteria.where("_id").is(id)), update, collectionName);
    }

}
