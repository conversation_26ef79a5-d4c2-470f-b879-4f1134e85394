package com.quanzhi.auditapiv2.common.dal.dto.weakness;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2021/9/1 9:25 上午
 */
@Data
@ApiModel("添加弱点")
public class WeaknessAddDto {
    @ApiModelProperty("接口uri")
    private String apiUri;
    @ApiModelProperty("弱点类型")
    private String type;
    @ApiModelProperty("弱点ID")
    private String weaknessId;
    @ApiModelProperty("弱点等级 1低 2中 3高")
    private int level;
    @ApiModelProperty("弱点名称")
    private String name;
    @ApiModelProperty("弱点特征")
    private String feature;
    @ApiModelProperty("弱点描述")
    private String desc;
    @ApiModelProperty("弱点处理意见")
    private String suggestion;
    @ApiModelProperty("弱点整改建议")
    private String solution;
    @ApiModelProperty("样例ID")
    private String sampleId;
    @ApiModelProperty("样例原始请求")
    private String reqRaw;
    @ApiModelProperty("样例原始返回")
    private String rspRaw;
}
