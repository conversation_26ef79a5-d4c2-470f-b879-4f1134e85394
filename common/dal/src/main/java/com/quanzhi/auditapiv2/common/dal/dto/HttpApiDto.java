package com.quanzhi.auditapiv2.common.dal.dto;

import com.quanzhi.metabase.core.model.Entity;
import com.quanzhi.metabase.core.model.ResourceEntity;
import com.quanzhi.metabase.core.model.http.*;
import com.quanzhi.metabase.core.model.http.api.ApiState;
import com.quanzhi.metabase.core.model.http.label.LabelStat;
import com.quanzhi.metabase.core.model.http.weakness.BriefWeaknesses;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.ArrayList;
import java.util.List;
import java.util.Set;

/**
 * @Author: HaoJun
 * @Date: 2020/3/23 11:23 上午
 * http接口资源
 */
@Data
@Entity(HttpResourceConstant.API)
@EqualsAndHashCode
@ApiModel("接口dto")
public class HttpApiDto extends ResourceEntity {

    /**
     * 接口url
     */
    @ApiModelProperty(value = "apiUrl", name = "apiUrl")
    private String apiUrl;

    /**
     * 五元组信息
     */
    @ApiModelProperty(value = "五元组信息", name = "net")
    private HttpApiSample.Net net;

    /**
     * 账号数量
     */
    @ApiModelProperty(value = "账号数量", name = "accountCount")
    private Long accountCount;

    /**
     * 接口路径
     */
    @ApiModelProperty(value = "接口路径", name = "apiPath")
    private String apiPath;

    /**
     * app uri
     */
    @ApiModelProperty(value = "appUri", name = "appUri")
    private String appUri;

    @ApiModelProperty(value = "应用名称", name = "appUri")
    private String appName;

    /**
     * 应用id
     */
    @ApiModelProperty(value = "应用id", name = "appId")
    private String appId;

    /**
     * 接口名称
     */
    @ApiModelProperty(value = "接口名称", name = "name")
    private String name;

    /**
     * 接口类型
     *
     * @see com.quanzhi.metabase.core.model.enums.ApiTypeEnum
     */
    @ApiModelProperty(value = "接口类型", name = "apiType")
    private Integer apiType;

    /**
     * 接口类型名称
     */
    @ApiModelProperty(value = "接口类型名称", name = "apiTypeName")
    private String apiTypeName;

    /**
     * 接口风险等级
     *
     * @see com.quanzhi.metabase.core.model.enums.ApiRiskLevelEnum
     */
    @ApiModelProperty(value = "接口风险等级", name = "apiRiskLevel")
    private Integer apiRiskLevel;

    /**
     * 接口风险等级名称
     */
    @ApiModelProperty(value = "接口风险等级名称", name = "apiRiskLeveleName")
    private String apiRiskLevelName;

    /**
     * 接口等级
     */
    @ApiModelProperty(value = "接口等级", name = "level")
    private String level;

    /**
     * 所属部门
     */
    @ApiModelProperty(value = "所属部门信息", name = "departments")
    private List<HttpAppResource.Department> departments;

    /**
     * 单次返回最大数据量
     */
    private Integer maxRspLabelValueCount = 0;

    /**
     * 返回数据标签数量
     */
    private Integer rspLabelCount = 0;
    private Integer reqLabelCount = 0;

    /**
     * 数据主体类型id列表
     */
    @Deprecated
    private List<String> dataTargetList = new ArrayList<String>();

    /**
     * 访问域列表
     */
    @ApiModelProperty(value = "访问域列表", name = "visitDomains")
    private List<String> visitDomains = new ArrayList<String>();

    /**
     * 访问域内容列表
     */
    @ApiModelProperty(value = "访问域内容列表", name = "visitDomainsValue")
    private List<String> visitDomainsValue = new ArrayList<String>();

    /**
     * 部署域id列表
     */
    @ApiModelProperty(value = "部署域id列表", name = "deployDomains")
    private List<String> deployDomains = new ArrayList<String>();

    /**
     * 部署域内容列表
     */
    @ApiModelProperty(value = "部署域内容列表", name = "deployDomainsValue")
    private List<String> deployDomainsValue = new ArrayList<String>();

    /**
     * 接口业务分类，这里注意根据状态展示
     */
    @ApiModelProperty(value = "接口业务分类", name = "classifications")
    private List<String> classifications = new ArrayList<String>();

    /**
     * 接口业务分类内容列表
     */
    @ApiModelProperty(value = "接口业务分类内容列表", name = "classificationValue")
    private List<String> classificationValue = new ArrayList<String>();

    /**
     * 低精度的业务分类
     */
    @ApiModelProperty(value = "低精度的业务分类", name = "lowClassifications")
    private List<String> lowClassifications = new ArrayList<String>();

    /**
     * 排除的分类
     */
    @ApiModelProperty(value = "排除的分类", name = "excludeClassifications")
    private List<String> excludeClassifications = new ArrayList<String>();

    /**
     * 返回数据标签id列表
     */
    @ApiModelProperty(value = "返回数据标签id列表", name = "rspDataLabels")
    private List<String> rspDataLabels = new ArrayList<String>();

    /**
     * 返回数据标签内容列表
     */
    @ApiModelProperty(value = "返回数据标签内容列表", name = "rspDataLabelsValue")
    private List<String> rspDataLabelsValue = new ArrayList<String>();

    /**
     * 请求数据标签id列表
     */
    @ApiModelProperty(value = "请求数据标签id列表", name = "reqDataLabels")
    private List<String> reqDataLabels = new ArrayList<String>();

    /**
     * 请求数据标签内容列表
     */
    @ApiModelProperty(value = "请求数据标签内容列表", name = "reqDataLabelsValue")
    private List<String> reqDataLabelsValue = new ArrayList<String>();

    /**
     * 数据标数量统计
     */
    @ApiModelProperty(value = "数据标数量统计", name = "labelStat")
    private LabelStat labelStat;

    /**
     * 低精度数据标签
     */
    @ApiModelProperty(value = "低精度数据标签", name = "lowDataLabels")
    private List<String> lowDataLabels = new ArrayList<String>();

    @ApiModelProperty(value = "低精度请求数据标签", name = "lowReqDataLabels")
    private List<String> lowReqDataLabels = new ArrayList<String>();

    @ApiModelProperty(value = "低精度响应数据标签", name = "lowRspDataLabels")
    private List<String> lowRspDataLabels = new ArrayList<String>();

    /**
     * 所有数据标签
     */
    @ApiModelProperty(value = "所有数据标签", name = "dataLabels")
    private List<String> dataLabels = new ArrayList<String>();

    /**
     * API标签
     */
    @ApiModelProperty(value = "API标签", name = "featureLabels")
    private List<String> featureLabels = new ArrayList<String>();

    /**
     * API标签
     */
    @ApiModelProperty(value = "API标签内容列表", name = "featureLabelValues")
    private List<String> featureLabelsValue = new ArrayList<String>();

    /**
     * 去除的API标签
     */
    @ApiModelProperty(value = "去除的API标签", name = "excludeFeatureLabels")
    private List<String> excludeFeatureLabels = new ArrayList<String>();

    /**
     * 排除的标签
     */
    @ApiModelProperty(value = "排除的请求标签", name = "excludeReqDataLabels")
    private List<String> excludeReqDataLabels = new ArrayList<String>();

    /**
     * 排除的标签
     */
    @ApiModelProperty(value = "排除的响应标签", name = "excludeRspDataLabels")
    private List<String> excludeRspDataLabels = new ArrayList<String>();

    /**
     * 推荐时间
     */
    @ApiModelProperty(value = "推荐时间", name = "recommendTime")
    private Long recommendTime;

    /**
     * 推荐状态
     *
     * @see HttpApiFlagComposite.RecommendFlag
     */
    @ApiModelProperty(value = "推荐状态", name = "recommendFlag")
    private Short recommendFlag;

    /**
     * 标签设置
     */
    @ApiModelProperty(value = "标签设置", name = "labelConfigs")
    private List<HttpApiResource.LabelConfig> labelConfigs = new ArrayList<HttpApiResource.LabelConfig>();

    /**
     * 接口存证开关
     */
    @ApiModelProperty(value = "接口存证开关", name = "evidence")
    private Boolean evidence;

    /**
     * 是否监控
     */
    @ApiModelProperty(value = "是否监控", name = "monitoring")
    private Boolean monitoring;

    /**
     * 样例信息列表
     */
    @ApiModelProperty(value = "样例信息列表", name = "samples")
    private List<HttpApiResource.Sample> samples = new ArrayList<HttpApiResource.Sample>();

    /**
     * 请求方式
     */
    @ApiModelProperty(value = "请求方式", name = "methods")
    private List<String> methods = new ArrayList<String>();

    /**
     * 推荐规则
     */
    @ApiModelProperty(value = "推荐规则", name = "rules")
    private List<String> rules = new ArrayList<String>();

    /**
     * 复合接口
     *
     * @see HttpApiFlagComposite.CompositeApiFlag
     * <p>
     * 这样设计可能过于复杂
     */
    @Deprecated
    private Short compositeFlag;

    /**
     * restful信息
     */
    @Deprecated
    private HttpApiResource.Restful restful;

    /**
     * when信息
     */
    @Deprecated
    private List<ConditionItem> when = new ArrayList<ConditionItem>();

    /**
     * 接口统计信息
     */
    @ApiModelProperty(value = "接口统计信息", name = "apiStat")
    private HttpApiResource.ApiStat apiStat;

    /**
     * 登陆信息
     */
    @ApiModelProperty(value = "登陆信息", name = "loginApiConfig")
    private LoginApiConfig loginApiConfig;

    /**
     * 类似账号
     */
    @ApiModelProperty(value = "类似账号", name = "accPageApiConfig")
    private LoginApiConfig accPageApiConfig;

    /**
     * 域名
     */
    @ApiModelProperty(value = "域名", name = "host")
    private String host;

    /**
     * 带端口号域名
     */
    @ApiModelProperty(value = "带端口号域名", name = "hostPort")
    private String hostPort;

    /**
     * 是否restful api
     *
     * @see HttpApiFlagComposite.RestfulFlag
     */
    @ApiModelProperty(value = "是否restful api", name = "restfulFlag")
    private Short restfulFlag;

    /**
     * 是否when-case api
     *
     * @see HttpApiFlagComposite.WhenFlag
     */
    @ApiModelProperty(value = "是否when-case api", name = "whenFlag")
    private Short whenFlag;

    /**
     * 接口请求类型
     */
    @ApiModelProperty(value = "接口请求类型", name = "reqContentTypes")
    private List<String> reqContentTypes = new ArrayList<String>();

    /**
     * 接口返回类型
     */
    @ApiModelProperty(value = "接口返回类型", name = "rspContentTypes")
    private List<String> rspContentTypes = new ArrayList<String>();

    /**
     * 父接口ID，如果该接口由拆分而来，该字段会存储一个父接口（whencase接口）ID
     */
    @ApiModelProperty(value = "父接口ID", name = "parentApiId")
    private String parentApiId;

    /**
     * 合并、拆分规则id
     */
    @ApiModelProperty(value = "合并、拆分规则id", name = "compositeRuleId")
    private String compositeRuleId;

    /**
     * 活跃时间
     */
    @ApiModelProperty(value = "活跃时间", name = "activeTime")
    private Long activeTime;

    /**
     * 发现时间
     */
    @ApiModelProperty(value = "发现时间", name = "discoverTime")
    private Long discoverTime;

    @Deprecated
    private Boolean largeFileFlag;

    /**
     * 上一个样例信息，该信息不会入库！
     */
    private HttpApiResource.Sample lastSample;

    @ApiModelProperty(value = "终端", name = "terminals")
    private List<String> terminals = new ArrayList<String>();

    /**
     * 弱点缩略信息
     */
    @ApiModelProperty(value = "弱点缩略信息", name = "briefWeaknesses")
    private BriefWeaknesses briefWeaknesses;

    /**
     * 风险缩略信息
     */
    @ApiModelProperty(value = "风险缩略信息", name = "briefRiskInfo")
    private BriefRiskInfo briefRiskInfo;

    @ApiModelProperty(value = "是否敏感API", name = "isSensitiveApi")
    private Boolean isSensitiveApi;

    @ApiModelProperty(value = "确认状态", name = "state")
    private ApiState state;

    @ApiModelProperty(value = "确认状态", name = "stateName")
    private String stateName;

    @ApiModelProperty(value = "备注", name = "remark")
    private String remark;


    @ApiModelProperty(value = "工单状态", name = "orderFlag")
    private Integer orderFlag;

    @ApiModelProperty(value = "工单状态名称", name = "orderFlagName")
    private String orderFlagName;

    private List<HttpApiResource.UrlPath> urlPaths;

    private StoreMonitor storeMonitor;

    @ApiModelProperty(value = "API生命状态", name = "apiLifeFlag")
    private List<Short> apiLifeFlag;

    @ApiModelProperty(value = "API生命状态名称", name = "apiLifeFlagValue")
    private List<String> apiLifeFlagValue;

    @ApiModelProperty(value = "流量来源", name = "flowSources")
    private List<String> flowSources;

    private Set<String> province;

    @ApiModelProperty(value = "上游ID", name = "upstreamId")
    private String upstreamId;

    @ApiModelProperty(value = "上游描述", name = "upstreamDesc")
    private String upstreamDesc;

    @ApiModelProperty(value = "部署ip", name = "deployIp")
    private String deployIp;

    @ApiModelProperty(value = "网关同步apiId", name = "apiId")
    private String apiId;

    @ApiModelProperty(value = "ai相关信息", name = "aiInfo")
    private HttpApiResource.AiInfo aiInfo;

    private List<HttpApiResource.AccParseConfigs> userParseConfigs;
    private List<HttpApiResource.AccParseConfigs> sessionParseConfigs;
    private List<HttpApiResource.AccParseConfigs> passwordParseConfigs;
    private List<HttpApiResource.AccParseConfigs> loginSuccessConfigs;
    private Boolean isSso;
    private Boolean accParseEnable;
    private List<String> apiFormats;
    private Boolean staffInfoParseEnable;
    private List<HttpApiResource.AccParseConfigs> staffDepartParseConfigs;
    private List<HttpApiResource.AccParseConfigs> staffNameParseConfigs;
    private List<HttpApiResource.AccParseConfigs> staffChineseParseConfigs;
    private List<HttpApiResource.AccParseConfigs> staffMobileParseConfigs;
    private List<HttpApiResource.AccParseConfigs> staffNickNameParseConfigs;
    private List<HttpApiResource.AccParseConfigs> staffIdCardParseConfigs;
    private List<HttpApiResource.AccParseConfigs> staffBankCardParseConfigs;
    private List<HttpApiResource.AccParseConfigs> staffEmailParseConfigs;
    private List<HttpApiResource.AccParseConfigs> staffIdParseConfigs;
    private List<HttpApiResource.AccParseConfigs> staffRoleParseConfigs;


    @Data
    public static final class BriefRiskInfo {
        /**
         * 风险事件个数
         */
        Integer count;

        /**
         * 高风险个数
         */
        private Integer highLevelNum;
        /**
         * 中风险个数
         */
        private Integer midLevelNum;
        /**
         * 低风险个数
         */
        private Integer lowLevelNum;
        /**
         * 涉及的风险名称
         */
        List<String> riskTypes;
    }


    @Mapper
    public interface HttpApiDtoMapper {

        HttpApiDtoMapper INSTANCE = Mappers.getMapper(HttpApiDtoMapper.class);

        HttpApiDto convert(HttpApiResource httpApiResource);

        HttpApiResource convert(HttpApiDto httpApiDto);

    }
}
