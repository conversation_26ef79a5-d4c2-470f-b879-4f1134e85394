package com.quanzhi.auditapiv2.common.dal.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 *
 * 《应用快照dto》
 *
 *
 * @Project:
 * @Module ID:
 * @Comments:
 * @JDK version used:      <JDK1.8>
 * <AUTHOR> [<EMAIL>]
 * @since 2020-04-08-上午9:56:10
 */
@Data
public class HttpAppSnapshotDto {

    @ApiModelProperty(value = "id", name = "id")
    private String id;

    /**
     * 应用id
     */
    @ApiModelProperty(value = "应用id", name = "appId")
    private String appId;

    /**
     * 数据标签
     */
    @ApiModelProperty(value = "数据标签", name = "dataLabels")
    private List<String> dataLabels = new ArrayList<String>();
    
    /**
     * 新增数据标签
     */
    @ApiModelProperty(value = "新增数据标签", name = "newDataLabels")
    private List<String> newDataLabels = new ArrayList<String>();
    
    /**
     * 失活数据标签
     */
    @ApiModelProperty(value = "失活数据标签", name = "inactiveDataLabels")
    private List<String> inactiveDataLabels = new ArrayList<String>();

    /**
     * 接口数量
     */
    @ApiModelProperty(value = "接口数量", name = "apiCount")
    private Long apiCount;

    /**
     * 新增接口数量
     */
    @ApiModelProperty(value = "新增接口数量", name = "newApiCount")
    private Integer newApiCount;

    /**
     * 失活接口数量
     */
    @ApiModelProperty(value = "失活接口数量", name = "inactiveApiCount")
    private Integer inactiveApiCount;
}
