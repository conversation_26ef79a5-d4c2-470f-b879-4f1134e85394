package com.quanzhi.auditapiv2.common.dal.dao.impl.workbenchConfig;

import com.quanzhi.auditapiv2.common.dal.dao.workbenchConfig.IWorkbenchConfigDao;
import com.quanzhi.auditapiv2.common.dal.entity.workbenchConfig.WorkbenchConfig;
import org.bson.types.ObjectId;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.stereotype.Repository;

/**
 * @Auther: yangzixian
 * @Date: 2021/12/30 10:30
 * @Description:
 */
@Repository
public class WorkbenchConfigDaoImpl implements IWorkbenchConfigDao {

    private final MongoTemplate mongoTemplate;

    public WorkbenchConfigDaoImpl(MongoTemplate mongoTemplate) {
        this.mongoTemplate = mongoTemplate;
    }

    private final String collectionName = "workbenchConfig";

    @Override
    public WorkbenchConfig selectWorkbenchConfig() {
        return mongoTemplate.findOne(new Query(), WorkbenchConfig.class, collectionName);
    }

    @Override
    public String editWorkbenchConfig(WorkbenchConfig workbenchConfig) {
        Update update = new Update();
        update.set("apiLevel", workbenchConfig.getApiLevel());
        update.set("weaknessLevel", workbenchConfig.getWeaknessLevel());
        update.set("riskLevel", workbenchConfig.getRiskLevel());
        update.set("showApi", workbenchConfig.isShowApi());
        update.set("showWeakness", workbenchConfig.isShowWeakness());
        update.set("showRisk", workbenchConfig.isShowRisk());
        update.set("updateTime", workbenchConfig.getUpdateTime());
        mongoTemplate.upsert(new Query().addCriteria(Criteria
                .where("_id").is(new ObjectId(workbenchConfig.getId()))), update, collectionName);
        return "修改成功！";
    }

}
