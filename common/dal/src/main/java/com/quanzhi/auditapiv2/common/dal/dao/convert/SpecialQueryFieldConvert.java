package com.quanzhi.auditapiv2.common.dal.dao.convert;

import com.quanzhi.audit_core.common.model.DataLabel;
import com.quanzhi.audit_core.common.model.RiskPolicy;
import com.quanzhi.audit_core.common.model.WeaknessRule;
import com.quanzhi.auditapiv2.common.dal.dao.IDataLabelNacosDao;
import com.quanzhi.auditapiv2.common.dal.dao.IWeaknessRuleNacosDao;
import com.quanzhi.auditapiv2.common.dal.dto.HttpApiSearchDto;
import com.quanzhi.auditapiv2.common.util.utils.DataUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * create at 2023/9/15 11:28
 * @description: 一些特殊查询字段转换
 **/
@Component
public class SpecialQueryFieldConvert {

    @Resource
    private IDataLabelNacosDao dataLabelNacosDao;

    @Resource
    private IWeaknessRuleNacosDao weaknessRuleNacosDaoImpl;

    @Resource
    private MongoTemplate mongoTemplate;


    public void specialQueryFieldConvert(Object searchObj) throws Exception {
        HttpApiSearchDto httpApiSearchDto = null;
        Map<String, Object> queryMap = null;
        if (searchObj instanceof HttpApiSearchDto) {
            httpApiSearchDto = (HttpApiSearchDto) searchObj;
        } else if (searchObj instanceof Map) {
            queryMap = (Map<String, Object>) searchObj;
        }
        //接口筛选
        if (httpApiSearchDto != null) {
            List<String> reqDataLabels = httpApiSearchDto.getReqDataLabels();
            List<String> rspDataLabels = httpApiSearchDto.getRspDataLabels();
            if ((DataUtil.isNotEmpty(reqDataLabels) && reqDataLabels.get(0).equals("ALL")) ||
                    (DataUtil.isNotEmpty(rspDataLabels) && rspDataLabels.get(0).equals("ALL"))
            ) {
                httpApiSearchDto.setDataLabelMap(getDataLabeMap());
            }
            List<String> weaknessNames = httpApiSearchDto.getWeaknessIds();
            if (DataUtil.isNotEmpty(weaknessNames) && weaknessNames.get(0).equals("ALL")) {
                httpApiSearchDto.setWeaknessMap(getWeaknessId2NameMap());
            }
            List<String> riskNames = httpApiSearchDto.getRiskIds();
            if (DataUtil.isNotEmpty(riskNames) && riskNames.get(0).equals("ALL")) {
                httpApiSearchDto.setRiskMap(getRiskPolicyIdMap());
            }
        }
        //应用筛选
        if (queryMap != null) {
            List<String> reqDataLabels = queryMap.get("reqDataLabels") != null ? (List<String>) queryMap.get("reqDataLabels") : new ArrayList<>();
            List<String> rspDataLabels = queryMap.get("rspDataLabels") != null ? (List<String>) queryMap.get("rspDataLabels") : new ArrayList<>();
            if ((DataUtil.isNotEmpty(reqDataLabels) && reqDataLabels.get(0).equals("ALL")) ||
                    (DataUtil.isNotEmpty(rspDataLabels) && rspDataLabels.get(0).equals("ALL"))
            ) {
                queryMap.put("dataLabelMap", getDataLabeMap());
            }
            List<String> weaknessNames = queryMap.get("weaknessIds") != null ? (List<String>) queryMap.get("weaknessIds") : new ArrayList<>();
            if (DataUtil.isNotEmpty(weaknessNames) && weaknessNames.get(0).equals("ALL")) {
                queryMap.put("weaknessMap", getWeaknessId2NameMap());
            }
            List<String> riskNames = queryMap.get("riskIds") != null ? (List<String>) queryMap.get("riskIds") : new ArrayList<>();
            if (DataUtil.isNotEmpty(riskNames) && riskNames.get(0).equals("ALL")) {
                queryMap.put("riskMap", getRiskPolicyIdMap());
            }
        }
    }

    private Map<String, String> getRiskPolicyIdMap() {
        Map<String, String> map = new HashMap<>();
        List<RiskPolicy> riskPolicies = mongoTemplate.find(new Query().addCriteria(Criteria.where("delFlag").is(false)), RiskPolicy.class);
        for (RiskPolicy riskPolicy : riskPolicies) {
            map.put(riskPolicy.getId(), riskPolicy.getName());
        }
        return map;
    }

    private Map<String, String> getWeaknessId2NameMap() {
        Map<String, String> map = new HashMap<>();
        List<WeaknessRule> list = weaknessRuleNacosDaoImpl.getAll();
        for (WeaknessRule weaknessRule : list) {
            map.put(weaknessRule.getId(), weaknessRule.getName());
        }
        return map;
    }

    private Map<String, String> getDataLabeMap() {
        Map<String, String> map = new HashMap<>();
        List<DataLabel> all = dataLabelNacosDao.getAll();
        for (DataLabel dataLabel : all) {
            if (dataLabel.getDelFlag() == null || (dataLabel.getDelFlag() != null && dataLabel.getDelFlag() == 0)) {
                map.put(dataLabel.getId(), dataLabel.getName());
            }
        }
        return map;
    }
}