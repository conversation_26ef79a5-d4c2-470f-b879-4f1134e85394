package com.quanzhi.auditapiv2.common.dal.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class XxlJobTaskListDto {

    /**
     * 任务创建时间
     */
    @ApiModelProperty(example="1589419746")
    private String createTime;

    /**
     * 任务具体内容 需要从xxljob列表中 executorParam中解析
     * 返回具体的标签、推荐策略、或者接口分类等
     */
    @ApiModelProperty(example="mobile")
    private String taskName;

    /**
     * 接口或者数据表
     */
    @ApiModelProperty(example="API")
    private String entity;

    /**
     * 任务开始时间
     */
    @ApiModelProperty(example="1589419746")
    private String startTime;

    /**
     * 任务结束时间
     */
    @ApiModelProperty(example="1589419746")
    private String endTime;

    /**
     * 任务状态
     */
    @ApiModelProperty(example="成功")
    private String status;

    /**
     * 任务执行参数
     */
    private String executorParam;
}
