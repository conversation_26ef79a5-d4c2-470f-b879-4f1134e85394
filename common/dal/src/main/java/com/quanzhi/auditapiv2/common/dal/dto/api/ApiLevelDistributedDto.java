package com.quanzhi.auditapiv2.common.dal.dto.api;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @Auther: yangzixian
 * @Date: 2021/8/30 11:01
 * @Description:
 */
@Data
@NoArgsConstructor
@ApiModel("API等级")
public class ApiLevelDistributedDto {

    @ApiModelProperty("高敏感API个数")
    private Long high = 0L;
    @ApiModelProperty("中敏感API个数")
    private Long mid = 0L;
    @ApiModelProperty("低敏感API个数")
    private Long low = 0L;
    @ApiModelProperty("非敏感API个数")
    private Long non = 0L;
    @ApiModelProperty("其他API个数")
    private Long other = 0L;
    @ApiModelProperty("API总个数")
    private Long totalCount = 0L;

}