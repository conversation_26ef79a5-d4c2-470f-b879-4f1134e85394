package com.quanzhi.auditapiv2.common.dal.entity.directive;

import lombok.Data;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;
import java.util.Map;

/**
 * @Author: yangzx
 * @Date: 2024/7/3 14:35
 */
@Data
public class RetryDto {

    private RetryInfo retryInfo;

    private List<RetryInfo> retryInfos;

    private DirectiveLogSearchDto directiveLogSearchDto;

    @Data
    public static class RetryInfo {

        private String directiveId;

        private String nodeId;

    }

}
