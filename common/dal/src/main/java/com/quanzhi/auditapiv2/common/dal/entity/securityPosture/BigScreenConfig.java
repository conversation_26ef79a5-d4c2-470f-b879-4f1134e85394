package com.quanzhi.auditapiv2.common.dal.entity.securityPosture;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * @Auther: yangzixian
 * @Date: 2021/10/21 15:53
 * @Description:
 */
@Data
@ApiModel("大屏配置")
public class BigScreenConfig {

    /**
     * 大屏ID
     */
    @ApiModelProperty(value = "大屏ID", name = "id")
    private String id;

    /**
     * 大屏名称
     */
    @ApiModelProperty(value = "大屏名称", name = "bigScreenName")
    private String bigScreenName;

    /**
     * 监控网段
     */
    @ApiModelProperty(value = "监控网段一", name = "networkOne")
    private network networkOne;

    /**
     * 监控网段
     */
    @ApiModelProperty(value = "监控网段二", name = "networkTwo")
    private network networkTwo;

    /**
     * 更新时间
     */
    @ApiModelProperty(value = "更新时间", name = "updateTime")
    private long updateTime;

    @Data
    public static final class network {
        /**
         * 网段名称
         */
        String name;
        /**
         * 关注应用
         */
        List<String> monitorDomain;
    }

}
