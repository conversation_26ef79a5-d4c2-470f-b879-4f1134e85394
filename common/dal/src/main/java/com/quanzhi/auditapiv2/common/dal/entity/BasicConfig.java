package com.quanzhi.auditapiv2.common.dal.entity;

import com.quanzhi.auditapiv2.common.dal.entity.tips.Tip;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 《基础配置》
 *
 * <AUTHOR> [<EMAIL>]
 * @Project:
 * @Module ID:
 * @Comments:
 * @JDK version used:      <JDK1.8>
 * @since 2020-04-08-上午9:56:10
 */
@Data
public class BasicConfig {

    @ApiModelProperty(value = "id", name = "id")
    private String id;

    /**
     * 产品名称
     */
    @ApiModelProperty(value = "产品名称", name = "name")
    private String name;

    @ApiModelProperty(value = "公司名称", name = "name")
    private String companyName;

    /**
     * 类型
     *
     * @see TypeEnum
     */
    @ApiModelProperty(value = "类型", name = "type")
    private Integer type;

    /**
     * 图片内容
     */
    @ApiModelProperty(value = "图片内容", name = "encoded")
    private String encoded;

    @ApiModelProperty(value = "通用配置值", name = "valueObj")
    private Object valueObj;

    /**
     * 更新时间
     */
    @ApiModelProperty(value = "更新时间", name = "updateTime")
    private Long updateTime;

    @ApiModelProperty(value = "首页警告状态", name = "homeWarningState")
    private Boolean homeWarningState;

    @ApiModelProperty(value = "首页警告网段配置状态", name = "homeWarningNetworkState")
    private Boolean homeWarningNetworkState = true;

    @ApiModelProperty(value = "首页警告账号配置状态", name = "homeWarningAccountState")
    private Boolean homeWarningAccountState = true;

    @ApiModelProperty(value = "首页警告同步方式管理状态", name = "homeWarningSubscribeSyncConfig")
    private Boolean homeWarningSubscribeSyncConfig = true;

    @ApiModelProperty(value = "首页警告时间", name = "homeWarningTime")
    private Long homeWarningTime;
    @ApiModelProperty(value = "警告提示", name = "tip")
    private Tip tip;

    public static enum TypeEnum {

        /**
         * product
         */
        PRODUCT(1),

        /**
         * logo
         */
        LOGO(2),

        /**
         * icon
         */
        ICON(3),

        /**
         * 普通数据
         */
        COMMON(4),
        /**
         * background
         */
        BACKGROUND(5),
        /**
         * system_version
         */
        SYSTEM_VERSION(6),
        /**
         * 请求类型
         */
        REQ_CONTENT_TYPE(7),
        /**
         * 返回类型
         */
        RSP_CONTENT_TYPE(8);


        private int val;

        TypeEnum(int val) {
            this.val = val;
        }

        public int value() {
            return this.val;
        }
    }
}
