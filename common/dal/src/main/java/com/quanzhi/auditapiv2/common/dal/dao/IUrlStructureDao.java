package com.quanzhi.auditapiv2.common.dal.dao;


import com.quanzhi.auditapiv2.common.dal.dao.base.IBaseDao;
import com.quanzhi.auditapiv2.common.dal.dao.common.BatchUpdateOption;
import com.quanzhi.auditapiv2.common.dal.entity.UrlStructure;
import org.springframework.data.mongodb.core.aggregation.Aggregation;
import org.springframework.data.mongodb.core.aggregation.TypedAggregation;

import java.util.List;

/**
 * @author: zhousong
 * @data: 2018/7/22
 */
public interface IUrlStructureDao extends IBaseDao<UrlStructure> {

    void batchUpdateByOption(List<BatchUpdateOption> options);

    void urlStructureTempory2UrlStructure();

    void dropCollection();

    /**
     * 更新进urlStructure表
     * @param options
     */
    void batchUpdateByOptionInUrlStructure(List<BatchUpdateOption> options);

    void batchUpdateByOption(List<BatchUpdateOption> options,Boolean updateSingleApp);

    <T> List<T> aggregateQuery(TypedAggregation aggregation, Class<T> entityClass);

    <T> List<T> aggregate(Aggregation aggregation, Class<T> entityClass, String collection);

}
