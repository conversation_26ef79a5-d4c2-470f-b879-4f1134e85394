package com.quanzhi.auditapiv2.common.dal.dto.docx;

import lombok.Data;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 *
 * 《敏感信息在URL中》
 *
 *
 * @Project:
 * @Module ID:
 * @Comments:
 * @JDK version used:      <JDK1.8>
 * <AUTHOR> [<EMAIL>]
 * @since 2020-08-28-下午16:52:18
 */
@Data
public class Sensitiveinurl extends ApiWeaknessExportWord {

    @Mapper
    public interface SensitiveinurlMapper {

        Sensitiveinurl.SensitiveinurlMapper INSTANCE = Mappers.getMapper(Sensitiveinurl.SensitiveinurlMapper.class);
        Sensitiveinurl convert(ApiWeaknessExportWord apiWeaknessExportWord);
    }
}
