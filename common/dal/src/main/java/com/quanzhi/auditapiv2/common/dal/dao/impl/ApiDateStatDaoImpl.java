package com.quanzhi.auditapiv2.common.dal.dao.impl;

import com.quanzhi.auditapiv2.common.dal.dao.IApiDataStatDao;
import com.quanzhi.auditapiv2.common.util.utils.DataUtil;
import com.quanzhi.metabase.core.model.http.HttpApiDateStat;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Repository;

/**
 * @Author: K, 小康
 * @Date: 2024/02/22/下午4:11
 * @Description:
 */
@Repository
public class ApiDateStatDaoImpl implements IApiDataStatDao {
    @Autowired
    private MongoTemplate mongoTemplate;

    private String collectionName = "httpApiDateStat";

    @Override
    public Integer dailyAmount(String uri,String date) {
        Query query = Query.query(Criteria.where("uri").is(uri).and("data").is(date));
        HttpApiDateStat httpApiDateStat = mongoTemplate.findOne(query, HttpApiDateStat.class, collectionName);
        if (DataUtil.isNotEmpty(httpApiDateStat)){
            return httpApiDateStat.getDailyAmount();
        }
        return null;
    }
}
