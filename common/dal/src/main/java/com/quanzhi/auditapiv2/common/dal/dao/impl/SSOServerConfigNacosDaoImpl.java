package com.quanzhi.auditapiv2.common.dal.dao.impl;

import com.quanzhi.auditapiv2.common.dal.dao.ISSOServerConfigNacosDao;
import com.quanzhi.auditapiv2.common.dal.dao.base.impl.NacosBaseDaoImpl;
import com.quanzhi.auditapiv2.common.dal.entity.SSOServerConfig;
import org.springframework.stereotype.Repository;

/**
 *
 * 《SSO服务配置持久层Nacos接口实现》
 *
 *
 * @Project:
 * @Module ID:
 * @Comments:
 * @JDK version used:      <JDK1.8>
 * <AUTHOR> [<EMAIL>]
 * @since 2022-10-12-上午9:56:10
 */
@Repository
public class SSOServerConfigNacosDaoImpl extends NacosBaseDaoImpl<SSOServerConfig> implements ISSOServerConfigNacosDao {
}
