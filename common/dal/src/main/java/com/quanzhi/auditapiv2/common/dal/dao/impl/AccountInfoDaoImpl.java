package com.quanzhi.auditapiv2.common.dal.dao.impl;

import cn.hutool.core.util.ReflectUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.nacos.api.config.annotation.NacosConfigListener;
import com.alibaba.nacos.api.config.annotation.NacosValue;
import com.mongodb.client.MongoCursor;
import com.quanzhi.audit_core.common.config.annotation.DynamicValue;
import com.quanzhi.audit_core.common.model.AssetLifeStateConfig;
import com.quanzhi.audit_core.common.model.RiskLevel;
import com.quanzhi.auditapiv2.common.dal.dao.AccountInfoDao;
import com.quanzhi.auditapiv2.common.dal.dao.IDataLabelNacosDao;
import com.quanzhi.auditapiv2.common.dal.dao.IRiskLevelDao;
import com.quanzhi.auditapiv2.common.dal.dao.convert.AssetLifeStateConfigConvert;
import com.quanzhi.auditapiv2.common.dal.dao.impl.common.Module;
import com.quanzhi.auditapiv2.common.dal.dao.impl.common.QueryAdapterRegistry;
import com.quanzhi.auditapiv2.common.dal.dto.AccountSearchDto;
import com.quanzhi.auditapiv2.common.dal.dto.common.CommonGroupDto;
import com.quanzhi.auditapiv2.common.dal.dto.query.AccountInfoCriteriaDto;
import com.quanzhi.auditapiv2.common.dal.entity.AccountInfo;
import com.quanzhi.auditapiv2.common.dal.entity.AccountInfoEntity;
import com.quanzhi.auditapiv2.common.dal.enums.AccountLifeFlagEnum;
import com.quanzhi.auditapiv2.common.util.utils.ConstantUtil;
import com.quanzhi.auditapiv2.common.util.utils.DataUtil;
import com.quanzhi.auditapiv2.common.util.utils.DateUtil;
import com.quanzhi.metabase.core.model.http.constant.AssetLifeStateEnum;
import lombok.extern.slf4j.Slf4j;
import org.bson.Document;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.BulkOperations;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.aggregation.Aggregation;
import org.springframework.data.mongodb.core.aggregation.AggregationOperation;
import org.springframework.data.mongodb.core.aggregation.GroupOperation;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.data.util.Pair;
import org.springframework.stereotype.Repository;

import java.lang.reflect.Field;
import java.util.*;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * @author: yangzixian
 * @date: 10/4/2023 11:09
 * @description:
 */
@Slf4j
@Repository
public class AccountInfoDaoImpl implements AccountInfoDao {

    public final MongoTemplate mongoTemplate;

    private final String collectionName = "accountInfo";

    @Autowired
    private IRiskLevelDao riskLevelDao;

    @Autowired
    private AssetLifeStateConfigConvert assetLifeStateConfigConvert;

    @NacosValue(value = "${auditapiv2.export.singleCount:100}", autoRefreshed = true)
    private Integer singleCount;

    @NacosValue(value = "${auditapiv2.export.maxCount:1000000}", autoRefreshed = true)
    private Integer maxCount;

    private AssetLifeStateConfig accountAssetLifeStateConfig;

    @Autowired
    public IDataLabelNacosDao dataLabelNacosDao;

    @Autowired
    public QueryAdapterRegistry queryAdapterRegistry;

    @DynamicValue(dataId = "common.asset.life.state.config.json", groupId = "common", typeClz = AssetLifeStateConfig.class)
    private List<AssetLifeStateConfig> assetLifeStateConfigs;

    @NacosConfigListener(dataId = "common.asset.life.state.config.json", groupId = "common", timeout = 30000)
    public void onLife(String msg) {
        if (this.assetLifeStateConfigs == null) {
            this.assetLifeStateConfigs = JSON.parseArray(msg, AssetLifeStateConfig.class);
        }
        this.getConfig(assetLifeStateConfigs);
    }

    public void getConfig(List<AssetLifeStateConfig> assetLifeStateConfigs) {
        this.accountAssetLifeStateConfig = assetLifeStateConfigs.stream().filter(e -> e.getAssetType() == AssetLifeStateConfig.AssetTypeEnum.ACCOUNT).findFirst().get();
    }

    // level : levelName
    private Map<Integer, String> riskLevelMap = new HashMap<>();

    @NacosConfigListener(dataId = "common.risk.level.config.json", groupId = "common", timeout = 30000)
    public void onMessage(String msg) {
        List<RiskLevel> riskLevelList = JSON.parseArray(msg, RiskLevel.class);

        getRiskLevelMap(riskLevelList);
    }

    private void getRiskLevelMap(List<RiskLevel> riskLevelList) {
        if (com.quanzhi.audit_core.common.utils.DataUtil.isNotEmpty(riskLevelList)) {
            for (RiskLevel riskLevel : riskLevelList) {
                if ("ACCOUNT".equals(riskLevel.getLevelType())) {
                    riskLevelMap.put(riskLevel.getLevel(), riskLevel.getLevelName());
                }
            }
        }
    }

    private static final List<String> FIELDS = new ArrayList<String>() {{
        add("account");
        add("staffDepart");
        add("staffRole");
        add("accountLifeFlag");
        add("relatedIpDistinctCnt");
        add("visitCnt");
        add("rspDataLabelList");
        add("firstDate");
        add("lastDate");
        add("staffEmail");
        add("staffBankCard");
        add("staffIdCard");
        add("staffMobile");
        add("staffChinese");
        add("staffNickName");
        add("staffId");
        add("maxRspDataDistinctCnt");
        add("maxRspDataDistinctCntDate");
        add("loginCnt");
        add("staffName");
    }};

    public AccountInfoDaoImpl(MongoTemplate mongoTemplate) {
        this.mongoTemplate = mongoTemplate;
    }


    @Override
    public long countApiAccountNum(String apiUri) {
        return mongoTemplate.count(new Query().addCriteria(Criteria.where("apiUriList").is(apiUri)), collectionName);
    }

    @Override
    public long countAppAccountNum(String appUri) {
        return mongoTemplate.count(new Query().addCriteria(Criteria.where("appUriList").is(appUri)), collectionName);
    }

    @Override
    public long countIpAccountNum(String ip) {
        return mongoTemplate.count(new Query().addCriteria(Criteria.where("relatedIpList").is(ip)), collectionName);
    }

    @Override
    public long getCount() {
        return mongoTemplate.count(new Query().addCriteria(Criteria.where("riskLevel").in(Arrays.asList(0, 1, 2, 3))), collectionName);
    }

    @Override
    public com.quanzhi.audit_core.common.model.AccountInfo getAccountInfoByAccount(String account) {
        return mongoTemplate.findOne(new Query().addCriteria(Criteria.where("account").is(account)), com.quanzhi.audit_core.common.model.AccountInfo.class, collectionName);
    }

    @Override
    public List<com.quanzhi.audit_core.common.model.AccountInfo> getAccountInfosByAccount(String account) {
        return mongoTemplate.find(new Query().addCriteria(Criteria.where("account").is(account)), com.quanzhi.audit_core.common.model.AccountInfo.class, collectionName);
    }

    @Override
    public List<AccountInfo> getAccountInfoList(AccountInfoCriteriaDto accountInfoCriteriaDto) {

        Query query = new Query();

        this.createCriteria(accountInfoCriteriaDto, query);

        if (DataUtil.isNotEmpty(accountInfoCriteriaDto.getField()) && DataUtil.isNotEmpty(accountInfoCriteriaDto.getSort())) {
            if (accountInfoCriteriaDto.getSort() == ConstantUtil.Sort.ASC) {
                query.with(Sort.by(Sort.Order.asc(accountInfoCriteriaDto.getField())));
            } else if (accountInfoCriteriaDto.getSort() == ConstantUtil.Sort.DESC) {
                query.with(Sort.by(Sort.Order.desc(accountInfoCriteriaDto.getField())));
            } else {
                query.with(Sort.by(Sort.Order.desc(accountInfoCriteriaDto.getField())));
            }
        }
        if (DataUtil.isNotEmpty(accountInfoCriteriaDto.getPage()) && DataUtil.isNotEmpty(accountInfoCriteriaDto.getLimit())) {
            query.skip((accountInfoCriteriaDto.getPage() - 1) * accountInfoCriteriaDto.getLimit());
            query.limit(accountInfoCriteriaDto.getLimit());
        } else {
            query.skip(0);
            query.limit(100000);
        }
        return mongoTemplate.find(query, AccountInfo.class);
    }

    @Override
    public List<AccountInfoEntity> getAccountInfoListFields(AccountInfoCriteriaDto accountInfoCriteriaDto, List<String> fields) {
        MongoCursor<AccountInfoEntity> cursor = getAccountInfoMongoCursor(accountInfoCriteriaDto);
        List<AccountInfoEntity> accountInfoList = new ArrayList<>();
        try {
            while (cursor.hasNext()) {
                AccountInfoEntity accountInfo = cursor.next();
                accountInfoList.add(accountInfo);
            }
        } catch (Exception e) {
            log.error("query accountInfo error", e);
        } finally {
            cursor.close();
        }

        return accountInfoList;
    }

    private MongoCursor<AccountInfoEntity> getAccountInfoMongoCursor(AccountInfoCriteriaDto accountInfoCriteriaDto) {
        Query query = new Query();
        this.createCriteria(accountInfoCriteriaDto, query);
        this.createSort(accountInfoCriteriaDto, query);
        this.appendPage(accountInfoCriteriaDto, query);
        List<Document> pipeline = new ArrayList<>();
        pipeline.add(new Document("$match", query.getQueryObject()));
        pipeline.add(new Document("$sort", query.getSortObject()));
        pipeline.add(new Document("$skip", query.getSkip()));
        pipeline.add(new Document("$limit", query.getLimit()));
        pipeline.add(new Document("$project", new Document("riskInfo.levelNumMap", false)));
        MongoCursor<AccountInfoEntity> cursor = mongoTemplate.getCollection(collectionName)
                .aggregate(pipeline, AccountInfoEntity.class)
                .batchSize(singleCount)
                .iterator();
        return cursor;
    }

    private MongoCursor<AccountInfoEntity> getAccountInfoMongoCursorNoSort(AccountInfoCriteriaDto accountInfoCriteriaDto) {
        Query query = new Query();
        this.createCriteria(accountInfoCriteriaDto, query);
        this.createSort(accountInfoCriteriaDto, query);
        this.appendPage(accountInfoCriteriaDto, query);
        List<Document> pipeline = new ArrayList<>();
        pipeline.add(new Document("$match", query.getQueryObject()));
//        pipeline.add(new Document("$sort", query.getSortObject()));
        pipeline.add(new Document("$skip", query.getSkip()));
        pipeline.add(new Document("$limit", query.getLimit()));
        pipeline.add(new Document("$project", new Document("riskInfo.levelNumMap", false)));
        MongoCursor<AccountInfoEntity> cursor = mongoTemplate.getCollection(collectionName)
                .aggregate(pipeline, AccountInfoEntity.class)
                .batchSize(500)
                .iterator();
        return cursor;
    }

    @Override
    public Iterator<AccountInfoEntity> cursor(AccountInfoCriteriaDto accountInfoCriteriaDto, List<String> fields) {
        return getAccountInfoMongoCursor(accountInfoCriteriaDto);
    }

    @Override
    public Iterator<AccountInfoEntity> cursorNoSort(AccountInfoCriteriaDto accountInfoCriteriaDto, List<String> fields) {
        return getAccountInfoMongoCursorNoSort(accountInfoCriteriaDto);
    }

    private void appendPage(AccountInfoCriteriaDto accountInfoCriteriaDto, Query query) {
        int page = 1;
        int limit = 100000;
        int skip = 0;
        if (DataUtil.isNotEmpty(accountInfoCriteriaDto.getPage())) {
            page = accountInfoCriteriaDto.getPage();
        }

        if (DataUtil.isNotEmpty(accountInfoCriteriaDto.getLimit())) {
            limit = accountInfoCriteriaDto.getLimit();
            skip = (page - 1) * limit;
        }

        query.skip(skip);
        query.limit(limit);
    }

    private void createSort(AccountInfoCriteriaDto accountInfoCriteriaDto, Query query) {
        if (DataUtil.isNotEmpty(accountInfoCriteriaDto.getField()) && DataUtil.isNotEmpty(accountInfoCriteriaDto.getSort())) {
            if (accountInfoCriteriaDto.getSort() == ConstantUtil.Sort.ASC) {
                query.with(Sort.by(Sort.Order.asc(accountInfoCriteriaDto.getField())));
            } else if (accountInfoCriteriaDto.getSort() == ConstantUtil.Sort.DESC) {
                query.with(Sort.by(Sort.Order.desc(accountInfoCriteriaDto.getField())));
            } else {
                query.with(Sort.by(Sort.Order.desc(accountInfoCriteriaDto.getField())));
            }
        }
    }

    @Override
    public Long getAccountInfoCount(AccountInfoCriteriaDto accountInfoCriteriaDto) {
        Query query = new Query();

        this.createCriteria(accountInfoCriteriaDto, query);

        return mongoTemplate.count(query, AccountInfo.class);
    }

    @Override
    public AccountInfo getAccountInfo(String account) {
        Query query = new Query();
        if (DataUtil.isNotEmpty(account)) {
            query.addCriteria(Criteria.where("account").is(account));
        }
        query.with(Sort.by(Sort.Order.desc("staffDepart")));
        query.fields().include("account");
        query.fields().include("staffDepart");
        return mongoTemplate.findOne(query, AccountInfo.class);
    }

    @Override
    public AccountInfo getAccount(String account) {
        Query query = new Query();
        if (DataUtil.isNotEmpty(account)) {
            query.addCriteria(Criteria.where("account").is(account));
        }
        return mongoTemplate.findOne(query, AccountInfo.class);
    }

    @Override
    public List<CommonGroupDto> accountGroup(String groupField) {
        // 初次访问，加载数据
        if (com.quanzhi.metabase.common.utils.DataUtil.isEmpty(riskLevelMap)) {
            List<RiskLevel> riskLevels = riskLevelDao.getAll();
            getRiskLevelMap(riskLevels);
        }

        List<CommonGroupDto> groupDtoList = new ArrayList<>();
        if (AccountSearchDto.GroupFieldEnum.accountLifeFlag.getName().equals(groupField)) {
            groupDtoList = getAccountLifeFlagGroupDtos();
        } else {
            groupDtoList = getCommonGroupDtos(groupField);
        }

        Map<String, Long> groupCountMap = new HashMap<>();
        List<CommonGroupDto> groupDtoListResult = new ArrayList<>();
        for (CommonGroupDto commonGroupDto : groupDtoList) {
            String groupId = commonGroupDto.getId();
            if (DataUtil.isEmpty(groupId)) {
                continue;
            }

            commonGroupDto.setName(groupId);
            groupCountMap.put(groupId, commonGroupDto.getCount());
        }

        if (AccountSearchDto.GroupFieldEnum.accountLifeFlag.getName().equals(groupField)) {
            // 设置初始生命周期
            for (AccountLifeFlagEnum accountLifeFlagEnum : AccountLifeFlagEnum.values()) {
                int flag = accountLifeFlagEnum.getLifeFlag();
                String desc = accountLifeFlagEnum.getDesc();
                String id = String.valueOf(flag);
                CommonGroupDto commonGroupDto = CommonGroupDto.builder()
                        .id(id)
                        .group(id)
                        .name(desc)
                        .count(0L)
                        .build();

                if (DataUtil.isNotEmpty(groupCountMap.get(id))) {
                    commonGroupDto.setCount(groupCountMap.get(id));
                }
                groupDtoListResult.add(commonGroupDto);
            }
        } else if (AccountSearchDto.GroupFieldEnum.riskLevel.getName().equals(groupField)) {
            // 设置初始风险等级
            for (Integer riskLevel : riskLevelMap.keySet()) {
                String desc = riskLevelMap.get(riskLevel);
                String id = String.valueOf(riskLevel);
                CommonGroupDto commonGroupDto = CommonGroupDto.builder()
                        .id(id)
                        .group(id)
                        .name(desc)
                        .count(0L)
                        .build();

                if (DataUtil.isNotEmpty(groupCountMap.get(id))) {
                    commonGroupDto.setCount(groupCountMap.get(id));
                }
                groupDtoListResult.add(commonGroupDto);
            }
            groupDtoListResult = groupDtoListResult.stream()
                    .sorted(Comparator.comparing(CommonGroupDto::getGroup))
                    .collect(Collectors.toList());
        }

        return groupDtoListResult;
    }

    @Override
    public long totalCount(AccountSearchDto accountSearchDto) {
        //查询条件
        Criteria criteria = getCriteria(accountSearchDto);

        return mongoTemplate.count(new Query(criteria), collectionName);
    }

    @Override
    public void save(List<AccountInfo> accountInfos) {
        BulkOperations accountOperations = mongoTemplate.bulkOps(BulkOperations.BulkMode.UNORDERED, collectionName);
        List<Pair<Query, Update>> updateList = new ArrayList<>();
        accountInfos.forEach(accountInfo -> {
            String account = accountInfo.getAccount();
            if (com.quanzhi.metabase.common.utils.DataUtil.isNotEmpty(account)) {
                Query query = Query.query(Criteria.where("account").is(account));
                Update update = getAccountUpdate(accountInfo);
                if (update != null) {
                    Pair<Query, Update> updatePair = Pair.of(query, update);
                    updateList.add(updatePair);
                }
            }
        });
        // 批量更新
        if (com.quanzhi.metabase.common.utils.DataUtil.isNotEmpty(updateList)) {
            accountOperations.upsert(updateList);
            accountOperations.execute();
        }
    }

    public static final Update getAccountUpdate(Object accountInfo) {
        // voucher是目标对象
        try {
            Update update = new Update();
            Field[] fields = ReflectUtil.getFields(accountInfo.getClass());
            //通过getDeclaredFields()方法获取对象类中的所有属性（含私有）
            for (Field field : fields) {
                //设置允许通过反射访问私有变量
                field.setAccessible(true);
                //获取字段的值
                Object value = field.get(accountInfo);
                //获取字段属性名称
                String name = field.getName();
                if (name.equals("id")) {
                    update.setOnInsert("_id", value);
                } else if (value != null) {
                    update.set(name, value);
                }
            }
            return update;
        } catch (Exception e) {
            log.error("反射取值异常：", e);
        }
        return null;
    }

    public List<com.quanzhi.audit_core.common.model.AccountInfo> top(int top) {
        return mongoTemplate.find(new Query().with(Sort.by(Sort.Direction.DESC, "visitCnt")).limit(top), com.quanzhi.audit_core.common.model.AccountInfo.class, collectionName);
    }

    @Override
    public void remove(AccountInfoCriteriaDto accountInfoCriteriaDto) {
        Query query = new Query();
        this.createCriteria(accountInfoCriteriaDto, query);
        mongoTemplate.remove(query, AccountInfo.class, collectionName);
    }

    @Override
    public void updateStrategyStatus(String account, Integer state) {
        Update update = new Update();
        update.set("strategyStatus", state);
        mongoTemplate.updateFirst(new Query().addCriteria(Criteria.where("account").is(account)), update, collectionName);
    }

    @Override
    public List<String> getAllAccount() {
        List<AccountInfo> accountInfos = mongoTemplate.find(new Query(), AccountInfo.class, collectionName);
        return accountInfos.stream().map(AccountInfo::getAccount).collect(Collectors.toList());
    }

    // 通用分组查询
    private List<CommonGroupDto> getCommonGroupDtos(String groupField) {
        List<AggregationOperation> list = new ArrayList<>();

        GroupOperation groupOperation = Aggregation.group(groupField).count().as("count");

        list.add(groupOperation);

        Aggregation aggregation = Aggregation.newAggregation(list);

        List<CommonGroupDto> groupDtoList = mongoTemplate.aggregate(aggregation, collectionName, CommonGroupDto.class).getMappedResults();
        return groupDtoList;
    }

    // 生命周期分组
    private List<CommonGroupDto> getAccountLifeFlagGroupDtos() {
        if (accountAssetLifeStateConfig == null) {
            this.getConfig(assetLifeStateConfigs);
        }

        List<CommonGroupDto> groupDtoList = new ArrayList<>();

        for (AssetLifeStateEnum assetLifeStateEnum : AssetLifeStateEnum.values()) {
            int flag = assetLifeStateEnum.getNum();
            String name = assetLifeStateEnum.getName();
            AssetLifeStateConfigConvert.CriteriaTag criteriaTag = assetLifeStateConfigConvert.getCriteria(AssetLifeStateConfig.AssetTypeEnum.ACCOUNT, assetLifeStateEnum);
            Criteria criteria = criteriaTag.getSpringCriteria();
            long count = mongoTemplate.count(new Query(criteria), collectionName);

            CommonGroupDto commonGroupDto = CommonGroupDto.builder().id(String.valueOf(flag)).name(name).count(count).build();

            groupDtoList.add(commonGroupDto);
        }

        return groupDtoList;
    }

    // 组装查询条件
    private Criteria getCriteria(AccountSearchDto accountSearchDto) {
        Criteria criteria = new Criteria();
        List<Criteria> criteriaList = new ArrayList<>();

        if (DataUtil.isNotEmpty(accountSearchDto)) {
            if (DataUtil.isNotEmpty(accountSearchDto.getAccountLifeFlag())) {
                Criteria lifeCriteria = convertQuery(accountSearchDto.getAccountLifeFlag());
                criteriaList.add(lifeCriteria);
            }

            if (DataUtil.isNotEmpty(accountSearchDto.getRiskLevel())) {
                Criteria riskCriteria = Criteria.where(AccountSearchDto.GroupFieldEnum.riskLevel.getName()).in(accountSearchDto.getRiskLevel());
                criteriaList.add(riskCriteria);
            }

            if (DataUtil.isNotEmpty(accountSearchDto.getAccount())) {
                criteriaList.add(Criteria.where("account").regex(Pattern.quote(accountSearchDto.getAccount())));
            }

            if (DataUtil.isNotEmpty(accountSearchDto.getNodeId())) {
                criteriaList.add(Criteria.where("nodes.nid").is(accountSearchDto.getNodeId()));
            }

            if (DataUtil.isNotEmpty(accountSearchDto.getAppUriList())) {
                Criteria appUri = Criteria.where("appUriList").in(DataUtil.regexStrEscape("httpapp:" + accountSearchDto.getAppUriList()));
                criteriaList.add(appUri);
            }
            if (DataUtil.isNotEmpty(accountSearchDto.getStaffChinese())) {
                Criteria staffChinese = Criteria.where("staffChinese").is(accountSearchDto.getStaffChinese());
                criteriaList.add(staffChinese);
            }
            if (DataUtil.isNotEmpty(accountSearchDto.getStaffDepart())) {
                Criteria staffDepart = Criteria.where("staffDepart").is(accountSearchDto.getStaffDepart());
                criteriaList.add(staffDepart);
            }
            if (DataUtil.isNotEmpty(accountSearchDto.getStaffRole())) {
                Criteria staffRole = Criteria.where("staffRole").is(accountSearchDto.getStaffRole());
                criteriaList.add(staffRole);
            }
            if (DataUtil.isNotEmpty(accountSearchDto.getStaffEmail())) {
                Criteria staffEmail = Criteria.where("staffEmail").is(accountSearchDto.getStaffEmail());
                criteriaList.add(staffEmail);
            }
            if (DataUtil.isNotEmpty(accountSearchDto.getRiskNamesOperator())
                    && DataUtil.isNotEmpty(accountSearchDto.getRiskNames())) {
                if (accountSearchDto.getRiskNames().contains("ALL")) {
                    Query query = new Query();
                    query.addCriteria(Criteria.where("delFlag").is(false));
                    query.fields().include("name");
                    // 风险事件全部筛选
                    List<Map> riskPolicy = mongoTemplate.find(query, Map.class, "riskPolicy");
                    List<String> riskNames = riskPolicy.stream().filter(i -> i != null && !i.equals("null") && !i.equals("")).map(i -> i.get("name").toString()).collect(Collectors.toList());
                    Criteria riskNamesNull = Criteria.where("riskInfo.riskNames").in(riskNames);
                    criteriaList.add(riskNamesNull);
                } else {
                    if (accountSearchDto.getRiskNamesOperator().equals("or")) {
                        Criteria riskNamesIn = Criteria.where("riskInfo.riskNames").in(accountSearchDto.getRiskNames());
                        criteriaList.add(riskNamesIn);
                    } else {
                        Criteria riskNamesIs = Criteria.where("riskInfo.riskNames").all(accountSearchDto.getRiskNames());
                        criteriaList.add(riskNamesIs);
                    }
                }
            }
            if (DataUtil.isNotEmpty(accountSearchDto.getRspDataLabelListOperator())
                    && DataUtil.isNotEmpty(accountSearchDto.getRspDataLabelList())) {
                List<String> dataLabelIds = new ArrayList<>();
                if (accountSearchDto.getRspDataLabelList().get(0).contains("ALL")) {
                    dataLabelIds = dataLabelNacosDao.getAll().stream().map(dataLabel -> dataLabel.getId()).collect(Collectors.toList());
                } else {
                    for (List<String> labels : accountSearchDto.getRspDataLabelList()) {
                        dataLabelIds.add(labels.get(1));
                    }
                }
                accountSearchDto.setUseRspDataLabelList(dataLabelIds);
                if (accountSearchDto.getRspDataLabelListOperator().equals("or")) {
                    Criteria rspDataLabelListIn = Criteria.where("rspDataLabelList").in(accountSearchDto.getUseRspDataLabelList());
                    criteriaList.add(rspDataLabelListIn);
                } else {
                    Criteria rspDataLabelListIs = Criteria.where("rspDataLabelList").all(accountSearchDto.getUseRspDataLabelList());
                    criteriaList.add(rspDataLabelListIs);
                }
            }
            if (DataUtil.isNotEmpty(accountSearchDto.getFirstDate())) {
                String startDate = DateUtil.format(accountSearchDto.getFirstDate().get(0), "yyyyMMdd");
                String endDate = DateUtil.format(accountSearchDto.getFirstDate().get(1), "yyyyMMdd");
                Criteria firstDate = Criteria.where("firstDate").gte(startDate).lt(endDate);
                criteriaList.add(firstDate);
            }
            if (DataUtil.isNotEmpty(accountSearchDto.getLastDate())) {
                String startDate = DateUtil.format(accountSearchDto.getLastDate().get(0), "yyyyMMdd");
                String endDate = DateUtil.format(accountSearchDto.getLastDate().get(1), "yyyyMMdd");
                Criteria lastDate = Criteria.where("lastDate").gte(startDate).lt(endDate);
                criteriaList.add(lastDate);
            }
        }

        if (criteriaList.size() > 0) {
            criteria.andOperator(criteriaList.toArray(new Criteria[criteriaList.size()]));
        }
        queryAdapterRegistry.module(Module.ACCOUNT).query(accountSearchDto, criteria);
        return criteria;
    }

    public Criteria convertQuery(Integer accountLifeFlag) {
        try {
                short lifeFlag = Short.parseShort(accountLifeFlag + "");
            AssetLifeStateEnum assetLifeStateEnum = AssetLifeStateEnum.valueOfNum(lifeFlag);
            AssetLifeStateConfigConvert.CriteriaTag criteriaTag = assetLifeStateConfigConvert.getCriteria(AssetLifeStateConfig.AssetTypeEnum.ACCOUNT, assetLifeStateEnum);
            Criteria mongoCriteria = criteriaTag.getSpringCriteria();

            return mongoCriteria;
        } catch (NumberFormatException e) {
            log.error("参数转换错误：", e);
        }

        return Criteria.where(AccountSearchDto.GroupFieldEnum.accountLifeFlag.getName()).is(accountLifeFlag);
    }

    private void createCriteria(AccountInfoCriteriaDto accountInfoCriteriaDto, Query query) {
        Criteria criteria = new Criteria();
        List<Criteria> andCriteriaList=new ArrayList<>();
        if (accountInfoCriteriaDto.getIds() != null && accountInfoCriteriaDto.getIds().size() > 0) {
            criteria.where("_id").in(accountInfoCriteriaDto.getIds());
        }
        if (DataUtil.isNotEmpty(accountInfoCriteriaDto.getDataLabel())) {
            if (DataUtil.isNotEmpty(accountInfoCriteriaDto.getFieldOperateMap())) {
                if ("and".equalsIgnoreCase(accountInfoCriteriaDto.getFieldOperateMap().get("rspDataLabelList"))) {
                    criteria.where("rspDataLabelList").all(accountInfoCriteriaDto.getDataLabel());
                } else if ("or".equalsIgnoreCase(accountInfoCriteriaDto.getFieldOperateMap().get("rspDataLabelList"))) {
                    criteria.where("rspDataLabelList").in(accountInfoCriteriaDto.getDataLabel());
                }
            } else {
                criteria.where("rspDataLabelList").in(accountInfoCriteriaDto.getDataLabel());
            }
        }
        if (DataUtil.isNotEmpty(accountInfoCriteriaDto.getAccount())) {
            criteria.where("account").regex(Pattern.quote(accountInfoCriteriaDto.getAccount()));
        }

        if (DataUtil.isNotEmpty(accountInfoCriteriaDto.getStaffDepart())) {
            criteria.where("staffDepart").is(accountInfoCriteriaDto.getStaffDepart());
        }
        if (DataUtil.isNotEmpty(accountInfoCriteriaDto.getNodeId())) {
            criteria.where("nodes").elemMatch(Criteria.where("nid").is(accountInfoCriteriaDto.getNodeId()));
        }
        if (DataUtil.isNotEmpty(accountInfoCriteriaDto.getStaffName())) {
            criteria.where("staffName").is(accountInfoCriteriaDto.getStaffName());
        }
        if (DataUtil.isNotEmpty(accountInfoCriteriaDto.getStaffEmail())) {
            criteria.where("staffEmail").is(accountInfoCriteriaDto.getStaffEmail());
        }
        if (DataUtil.isNotEmpty(accountInfoCriteriaDto.getStaffChinese())) {
            criteria.where("staffChinese").regex(Pattern.quote(accountInfoCriteriaDto.getStaffChinese()));
        }
        if (DataUtil.isNotEmpty(accountInfoCriteriaDto.getStaffRole())) {
            criteria.where("staffRole").regex(Pattern.quote(accountInfoCriteriaDto.getStaffRole()));
        }
        if (DataUtil.isNotEmpty(accountInfoCriteriaDto.getStartTime()) && DataUtil.isNotEmpty(accountInfoCriteriaDto.getEndTime())) {
            andCriteriaList.add(Criteria.where("firstDate")
                    .gte(accountInfoCriteriaDto.getStartTime())
                    .lte(accountInfoCriteriaDto.getEndTime()));
        }
        if (DataUtil.isNotEmpty(accountInfoCriteriaDto.getStartDate()) && DataUtil.isNotEmpty(accountInfoCriteriaDto.getEndDate())) {
            andCriteriaList.add(Criteria.where("lastDate")
                    .gte(accountInfoCriteriaDto.getStartDate())
                    .lte(accountInfoCriteriaDto.getEndDate()));
        }
        if (DataUtil.isNotEmpty(accountInfoCriteriaDto.getStartUpdateTime()) && DataUtil.isNotEmpty(accountInfoCriteriaDto.getEndUpdateTime())) {
            andCriteriaList.add(Criteria.where("updateTime").gte(accountInfoCriteriaDto.getStartUpdateTime()).lte(accountInfoCriteriaDto.getEndUpdateTime()));
        } else if (DataUtil.isNotEmpty(accountInfoCriteriaDto.getEndUpdateTime())) {
            criteria.where("updateTime").gte(accountInfoCriteriaDto.getEndUpdateTime());
        }

        if (DataUtil.isNotEmpty(accountInfoCriteriaDto.getRiskLevel())) {
            criteria.where("riskLevel").in(accountInfoCriteriaDto.getRiskLevel());
        }

        if (DataUtil.isNotEmpty(accountInfoCriteriaDto.getAppUri())) {
            query.addCriteria(Criteria.where("appUriList").regex(Pattern.quote(accountInfoCriteriaDto.getAppUri())));
        }

        if (DataUtil.isNotEmpty(accountInfoCriteriaDto.getRiskNames())) {
            if (DataUtil.isNotEmpty(accountInfoCriteriaDto.getFieldOperateMap())) {
                if ("and".equalsIgnoreCase(accountInfoCriteriaDto.getFieldOperateMap().get("riskNames"))) {
                    criteria.where("riskInfo.riskNames").all(accountInfoCriteriaDto.getRiskNames());
                } else if ("or".equalsIgnoreCase(accountInfoCriteriaDto.getFieldOperateMap().get("riskNames"))) {
                    criteria.where("riskInfo.riskNames").in(accountInfoCriteriaDto.getRiskNames());
                }
            } else {
                criteria.where("riskInfo.riskNames").in(accountInfoCriteriaDto.getRiskNames());
            }
        }
        if (DataUtil.isNotEmpty(accountInfoCriteriaDto.getAccountLifeFlag())) {
            Criteria accountLifeFlag = convertQuery(accountInfoCriteriaDto.getAccountLifeFlag());
            query.addCriteria(accountLifeFlag);
        }
        if(andCriteriaList.size()>0){
            criteria.andOperator(andCriteriaList);
        }
        queryAdapterRegistry.module(Module.ACCOUNT).query(accountInfoCriteriaDto, criteria);
        query.addCriteria(criteria);
    }
}
