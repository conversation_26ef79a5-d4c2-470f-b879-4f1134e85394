package com.quanzhi.auditapiv2.common.dal.dao;

import com.quanzhi.auditapiv2.common.dal.entity.RiskIndex;

/**
 * @Auther: yangzixian
 * @Date: 2021/8/30 16:34
 * @Description:
 */
public interface ISysRiskIndexDao {

    void saveRiskIndex(RiskIndex riskIndex);

    int getRiskIndex();

    /**
     * 根据日期获取系统风险指数
     * @param date
     * @return
     */
    RiskIndex getRiskIndexByDate(String date);

}
