package com.quanzhi.auditapiv2.common.dal.dao;

import com.quanzhi.auditapiv2.common.dal.dao.base.INacosBaseDao;
import com.quanzhi.auditapiv2.common.dal.entity.SSOServerConfig;
import org.springframework.stereotype.Repository;

/**
 *
 * 《SSO服务配置持久层Nacos接口》
 *
 *
 * @Project:
 * @Module ID:
 * @Comments:
 * @JDK version used:      <JDK1.8>
 * <AUTHOR> [<EMAIL>]
 * @since 2022-10-12-上午9:56:10
 */
@Repository
public interface ISSOServerConfigNacosDao extends INacosBaseDao<SSOServerConfig> {
}