package com.quanzhi.auditapiv2.common.dal.entity.syslog;

import lombok.extern.slf4j.Slf4j;

import javax.net.SocketFactory;
import java.io.BufferedWriter;
import java.io.IOException;
import java.io.OutputStreamWriter;
import java.net.Socket;
import java.nio.charset.StandardCharsets;


@Slf4j
public class TcpSyslogClient extends SyslogClient {

    private volatile Socket socket;

    private final Object lock;

    private BufferedWriter bufferedWriter;

    private volatile boolean isEffective;

    private volatile long lastCheckTimestamp;

    public TcpSyslogClient(String host, Integer port) throws IOException {
        super(host, port);
        this.lock = new Object();
        createSocket();
        this.lastCheckTimestamp = System.currentTimeMillis();
    }


    @Override
    public boolean effective() {
        if (this.socket == null || this.bufferedWriter == null) {
            return false;
        }
        if (isEffective) {
            //如果连接是可用的。那么5s内不会再次测试，直接返回上次检测的结果。
            if ((System.currentTimeMillis() - lastCheckTimestamp) > (SyslogConstant.SECOND * 5)) {
                isEffective = this.socket.isConnected() && !this.socket.isClosed() && !this.socket.isOutputShutdown();
                lastCheckTimestamp = System.currentTimeMillis();
            }
        } else {
            //如果连接不可用。那么下次检测，还是会触发检测。由于存在锁，会导致性能降低。不过。连接已经不可用，本身就需要重新不断检测。
            isEffective = this.socket.isConnected() && !this.socket.isClosed() && !this.socket.isOutputShutdown();
        }

        return isEffective;
    }

    @Override
    public void close() {
        if (this.socket == null) {
            return;
        }
        try {
            this.bufferedWriter.flush();
            this.socket.close();
        } catch (Exception e) {
            log.error(String.format("close socket %s:%d", this.host, this.port), e);
        } finally {
            this.socket = null;
        }
    }

    @Override
    public void flush() {
        try {
            this.bufferedWriter.flush();
        } catch (Exception e) {
            log.error(String.format("flush %s:%d", this.host, this.port), e);
        }

    }

    @Override
    public void write(char[] chars) throws IOException {
        if (!effective()) {
            createSocket();
        }
        try {
            this.bufferedWriter.write(chars);
            this.bufferedWriter.write(DelimiterSequence);
            this.bufferedWriter.flush();
        } catch (IOException ioe) {
            log.error(String.format("send to %s:%d error", this.host, this.port), ioe);
            throw new IOException();
        }
    }

    private void createSocket() throws IOException {
        synchronized (lock) {
            if (effective()) {
                return;
            }
            this.socket = SocketFactory.getDefault().createSocket(host, port);
            this.socket.setKeepAlive(true);
            if (this.socket.getSoLinger() != -1) {
                this.socket.setSoLinger(true, 30);
            }
            this.socket.setSoTimeout(SyslogConstant.MINUTE * 5);
            this.socket.setReuseAddress(true);
            this.socket.setSendBufferSize(SyslogConstant.MB * 10);
            this.socket.setReceiveBufferSize(SyslogConstant.MB * 10);
            this.bufferedWriter = new BufferedWriter(new OutputStreamWriter(socket.getOutputStream(), StandardCharsets.UTF_8));
        }
    }
}
