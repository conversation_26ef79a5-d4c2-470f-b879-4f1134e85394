package com.quanzhi.auditapiv2.common.dal.dao.impl.common;

import com.quanzhi.metabase.core.model.query.MetabaseQuery;


/**
 * <AUTHOR>
 * @date 2022/7/25 11:53 上午
 */
public interface QueryAdapter {

    default void query(Object query, MetabaseQuery metabaseQuery) {

    }

    default void query(Object query, org.springframework.data.mongodb.core.query.Criteria criteria) {

    }

    default void setModule(Module module) {

    }

}
