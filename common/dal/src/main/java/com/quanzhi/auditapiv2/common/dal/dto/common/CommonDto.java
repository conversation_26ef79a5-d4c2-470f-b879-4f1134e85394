package com.quanzhi.auditapiv2.common.dal.dto.common;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;

/**
 * <AUTHOR>
 * create at 2021/8/12 11:01 上午
 * @description: 通用返回
 **/
@Data
@Builder
@ApiModel
@AllArgsConstructor
public class CommonDto {

    @ApiModelProperty(value = "key", name = "key")
    private String key;

    @ApiModelProperty(value = "value", name = "value")
    private Object value;

}