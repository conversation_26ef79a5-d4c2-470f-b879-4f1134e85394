package com.quanzhi.auditapiv2.common.dal.dao.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.alibaba.nacos.api.annotation.NacosInjected;
import com.alibaba.nacos.api.config.ConfigService;
import com.alibaba.nacos.api.exception.NacosException;
import com.quanzhi.auditapiv2.common.dal.dao.INacosSingleKeyUpdateDao;
import com.quanzhi.auditapiv2.common.dal.dao.base.impl.NacosImpl;
import com.quanzhi.auditapiv2.common.dal.entity.NacosSingleKey;
import com.quanzhi.auditapiv2.common.dal.entity.NacosSingleKeyConfig;
import com.quanzhi.auditapiv2.common.dal.enums.NacosContentTypeEnum;
import com.quanzhi.auditapiv2.common.dal.enums.NacosSingleKeyConfigEnum;
import com.quanzhi.auditapiv2.common.util.utils.DataUtil;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2020/12/29 上午11:34
 */
@Repository
@Slf4j
public class NacosSingleKeyUpdateDaoImpl implements INacosSingleKeyUpdateDao {

    private static final long timeoutMs = 5000;

    @NacosInjected
    private ConfigService configService;

    @Autowired
    private NacosImpl nacosImpl;

    @Override
    public void updateNacosSingleKeys(List<NacosSingleKey> nacosSingleKeys) {

        if(DataUtil.isNotEmpty( nacosSingleKeys )) {

            // 如果是一个组的，就应该一次性更新

            Map<NacosKey, String> configMap = new HashMap<>();

            nacosSingleKeys.stream().forEach(nacosSingleKey -> {

                NacosSingleKeyConfig nacosSingleKeyConfig = NacosSingleKeyConfigEnum.getSingleKeyConfig( nacosSingleKey.getKey()  );

                if(nacosSingleKeyConfig != null) {

                    String group = nacosSingleKeyConfig.getGroup();
                    String dataId = nacosSingleKeyConfig.getDataId();
                    NacosContentTypeEnum type = NacosContentTypeEnum.valueOf(nacosSingleKeyConfig.getContentType());
                    NacosKey nacosKey = new NacosKey(group, dataId);

                    try {
                        String content = configMap.get(nacosKey);
                        if (content == null){
                            content = configService.getConfig(dataId, group, timeoutMs);
                        }
                        String path = nacosSingleKeyConfig.getMatchValue();
                        Object value = nacosSingleKey.getValue();


                        switch (type) {

                            case JSON:

                                Object originalVal = JSON.parse(content);

                                originalVal = this.updateJson(originalVal,path,value);

                                configMap.put(nacosKey, JSON.toJSONString(originalVal, SerializerFeature.PrettyFormat));

                                //configService.publishConfig(dataId,group, JSON.toJSONString( originalVal ));
                               break;

                            case JSONARRAY:

                                JSONArray jsonArray = JSONArray.parseArray(content);

                                jsonArray.stream().forEach(item ->{

                                    if( DataUtil.isNotEmpty( nacosSingleKeyConfig.getMatchKey()) &&  DataUtil.isNotEmpty( nacosSingleKeyConfig.getMatchKeyValue()) ) {

                                        if ( ((JSONObject) item).getString( nacosSingleKeyConfig.getMatchKey() ) != null
                                            && ((JSONObject) item).getString( nacosSingleKeyConfig.getMatchKey() ).equals(nacosSingleKeyConfig.getMatchKeyValue())){

                                            this.updateJson(item,path,value);
                                        }
                                    }
                                });
                                configMap.put(nacosKey, JSON.toJSONString(jsonArray, SerializerFeature.PrettyFormat));

                                //configService.publishConfig(dataId,group, JSON.toJSONString( JSON.toJSONString( jsonArray ) ));

                                break;

                            case PROPERTIES:

                                nacosImpl.publishPropertyValue(nacosSingleKeyConfig.getMatchValue(),String.valueOf( nacosSingleKey.getValue()),dataId,group);

                                break;

                                default:
                                    break;
                        }


                    } catch (NacosException e) {
                        log.error("获取nacos配置:【groupId:{}, dataId:{}】失败 \n {}", group, dataId, e.getErrMsg());
                    }
                }
            });

            for (Map.Entry<NacosKey, String> entry : configMap.entrySet()){
                try {
                    configService.publishConfig(entry.getKey().getDataId(),entry.getKey().getGroupId(), entry.getValue());
                } catch (NacosException e) {
                    log.error("更新nacos配置:【groupId:{}, dataId:{}】失败 \n {}", entry.getKey().getGroupId(), entry.getKey().getDataId(), e.getErrMsg());
                }
            }
        }
    }

    @Override
    public Map<String,Object> getNacosSingleKeyValus(List<String> nacosSingleKeys){

        Map<String,Object> results = new HashMap<>();

        if(DataUtil.isNotEmpty( nacosSingleKeys  )) {

            nacosSingleKeys.stream().forEach(nacosSingleKey -> {

                NacosSingleKeyConfig nacosSingleKeyConfig = NacosSingleKeyConfigEnum.getSingleKeyConfig( nacosSingleKey  );

                if(nacosSingleKeyConfig != null) {

                    String group = nacosSingleKeyConfig.getGroup();
                    String dataId = nacosSingleKeyConfig.getDataId();
                    NacosContentTypeEnum type = NacosContentTypeEnum.valueOf(nacosSingleKeyConfig.getContentType());

                    try {
                        String content = configService.getConfig(dataId, group, timeoutMs);

                        String path = nacosSingleKeyConfig.getMatchValue();

                        switch (type) {

                            case JSON:

                                Object originalVal = JSON.parse(content);

                                results.put(nacosSingleKey,this.getKeyValue(originalVal,path)) ;

                                break;

                            case JSONARRAY:

                                JSONArray jsonArray = JSONArray.parseArray(content);

                                jsonArray.stream().forEach(item ->{

                                    if( DataUtil.isNotEmpty( nacosSingleKeyConfig.getMatchKey()) &&  DataUtil.isNotEmpty( nacosSingleKeyConfig.getMatchKeyValue()) ) {

                                        if ( ((JSONObject) item).getString( nacosSingleKeyConfig.getMatchKey() ) != null
                                                && ((JSONObject) item).getString( nacosSingleKeyConfig.getMatchKey() ).equals(nacosSingleKeyConfig.getMatchKeyValue())){

                                            results.put(nacosSingleKey,this.getKeyValue(item,path)) ;
                                        }
                                    }
                                });

                                break;

                            case PROPERTIES:

                                results.put(nacosSingleKey,nacosImpl.getPropertyValue(nacosSingleKeyConfig.getMatchValue(),dataId,group)) ;

                                break;

                            default:
                                break;
                        }


                    } catch (NacosException e) {
                        log.error("获取nacos配置:【groupId:{}, dataId:{}】失败 \n {}", group, dataId, e.getErrMsg());
                    }
                }
            });

        }

        return results;
    }

    /**
     * 获取对象中某个key的值
     * @param originalObj
     * @param path
     * @return
     */
    private Object getKeyValue(Object originalObj,String path) {

        if(DataUtil.isNotEmpty( path ) ) {

            Object childObj = originalObj;

            String[] childPaths = path.split("\\.");

            for(int i = 0;i < childPaths.length;i++) {

                String childPath = childPaths[i];

                if(i == childPaths.length -1 ){

                    return ((JSONObject) childObj).get( childPath );
                }

                if(childPath.contains("[") && childPath.contains("]")) {

                    int arrIndex = Integer.valueOf( childPath.split("\\[")[1].split("\\]")[0] );

                    childPath = childPath.split("\\[")[0];

                    childObj = ((JSONObject) childObj).getJSONArray(childPath).get( arrIndex );

                } else {

                    childObj = ((JSONObject) childObj).get(childPath);
                }

            }
        }

        return null;
    }

    /**
     * 更新一个对象中的某个字段
     * @param originalObj
     * @param path
     * @param value
     * @return
     */
    private Object updateJson(Object originalObj,String path,Object value ) {

        if(DataUtil.isNotEmpty( path ) && DataUtil.isNotEmpty( value )) {

            Object childObj = originalObj;

            String[] childPaths = path.split("\\.");

            for(int i = 0;i < childPaths.length;i++) {

                String childPath = childPaths[i];

                if(i == childPaths.length -1 ){
                    continue;
                }

                if(childPath.contains("[") && childPath.contains("]")) {

                    int arrIndex = Integer.valueOf( childPath.split("\\[")[1].split("\\]")[0] );

                    childPath = childPath.split("\\[")[0];

                    childObj = ((JSONObject) childObj).getJSONArray(childPath).get( arrIndex );

                } else {

                    childObj = ((JSONObject) childObj).get(childPath);
                }

            }

            ((JSONObject)childObj).put( childPaths[childPaths.length -1], value );

        }

        return originalObj;
    }

    @Data
    @AllArgsConstructor
    class NacosKey{
        private String groupId;
        private String dataId;
    }
}
