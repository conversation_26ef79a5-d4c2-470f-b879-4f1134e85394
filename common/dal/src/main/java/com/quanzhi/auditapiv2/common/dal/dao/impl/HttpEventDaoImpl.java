package com.quanzhi.auditapiv2.common.dal.dao.impl;

import com.quanzhi.audit_core.common.model.FilterHttpEvent;
import com.quanzhi.audit_core.common.model.HttpEvent;
import com.quanzhi.auditapiv2.common.dal.dao.IHttpEventDao;
import com.quanzhi.auditapiv2.common.dal.dao.base.impl.MetabaseDaoImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 《过滤事件持久层接口实现》
 *
 * <AUTHOR> [<EMAIL>]
 * @Project:
 * @Module ID:
 * @Comments:
 * @JDK version used:      <JDK1.8>
 * @since 2020-04-08-上午9:56:10
 */
@Repository
public class HttpEventDaoImpl extends MetabaseDaoImpl<HttpEvent> implements IHttpEventDao {

    @Autowired
    MongoTemplate mongoTemplate;

    String tableName = "http_event_filter";

    /**
     * 查询过滤事件列表(分页)
     *
     * @param
     * @return
     * @Comments: <对此方法的描述，可以引用系统设计中的描述>
     * <AUTHOR> [<EMAIL>]
     * @since 2020-04-08 9:56
     */
    @Override
    public List<HttpEvent> selectHttpEventList(String eventFilterPluginId, Integer page, Integer limit) {

        //分页
        Pageable pageable = PageRequest.of(page - 1, limit);
        //排序
        Sort sort = Sort.by(Sort.Direction.DESC, "timestamp");

        return mongoTemplate.find(new Query(new Criteria()).with(pageable).with(sort), HttpEvent.class, tableName + "_" + eventFilterPluginId);
    }

    /**
     * 查询过滤事件数量
     *
     * @param
     * @return
     * @Comments: <对此方法的描述，可以引用系统设计中的描述>
     * <AUTHOR> [<EMAIL>]
     * @since 2020-04-08 9:56
     */
    @Override
    public Long totalCount(String eventFilterPluginId) throws Exception {

        return mongoTemplate.count(new Query(new Criteria()), HttpEvent.class, tableName + "_" + eventFilterPluginId);
    }

    /**
     * id查询过滤事件详情
     *
     * @param
     * @return
     * @Comments: <对此方法的描述，可以引用系统设计中的描述>
     * <AUTHOR> [<EMAIL>]
     * @since 2020-04-08 9:56
     */
    @Override
    public HttpEvent selectHttpEventById(String id, String eventFilterPluginId) throws Exception {

        Criteria criteria = Criteria.where("_id").is(id);

        return mongoTemplate.findOne(new Query(criteria), HttpEvent.class, tableName + "_" + eventFilterPluginId);
    }


    @Override
    public Long count(Query query, String collection) {
        return mongoTemplate.count(query, collection);
    }

    @Override
    public <T> List<T> query(Query query, Class<T> aClass, String collection) {
        return mongoTemplate.find(query, aClass, collection);
    }

    @Override
    public FilterHttpEvent getFilterHttpEventById(String httpEventId) {
        FilterHttpEvent filterHttpEvent = mongoTemplate.findById(httpEventId, FilterHttpEvent.class, "filterHttpEvent");
        return filterHttpEvent;
    }

    @Override
    public FilterHttpEvent findByUri(String uri) {
        return mongoTemplate.findOne(new Query(Criteria.where("uri").is(uri)), FilterHttpEvent.class, "filterHttpEvent");
    }
}
