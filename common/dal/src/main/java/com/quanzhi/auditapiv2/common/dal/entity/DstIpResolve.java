package com.quanzhi.auditapiv2.common.dal.entity;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;


@Data
public class DstIpResolve {

    @ApiModelProperty(value = "id", name = "id")
    private String id;

    /**
     * 优先级
     */
    @ApiModelProperty(value = "优先级", name = "priority")
    private Integer priority;

    /**
     * 字段名
     */
    @ApiModelProperty(value = "字段名", name = "filedName")
    private String filedName;

    /**
     * 解析方式
     * @see ResolveTypeEnum
     */
    @ApiModelProperty(value = "解析方式", name = "resolveType")
    private Integer resolveType;


    @ApiModelProperty(value = "内部IP段", name = "innerIpSegs")
    private List<IpSegment> innerIpSegs;

    /**
     * 内部IP段
     */
    @ApiModelProperty(value = "IP过滤段", name = "innerIpSegs")
    private List<String> filterIps;

    /**
     * 方向
     */
    @ApiModelProperty(value = "方向", name = "direction")
    private Integer direction;

    /**
     * 解析方式枚举类
     */
    public static enum ResolveTypeEnum {
        REQUEST_PARAM(0),
        REQUEST_BODY(1),
        REQUEST_COOKIE(2),
        REQUEST_HEADER(3),
        RESPONSE_BODY(4),
        RESPONSE_SETCOOKIE(5),
        RESPONSE_HEADER(6);

        private int val;

        ResolveTypeEnum(int val) {
            this.val = val;
        }

        public int value() {
            return this.val;
        }

        public static boolean exist(int val){
            for (DstIpResolve.ResolveTypeEnum resolveTypeEnum : DstIpResolve.ResolveTypeEnum.values()){

                if ( resolveTypeEnum.value() == val ) {
                    return true;
                }
            }

            return false;
        }
    }


    public static enum DirectionEnum {

        /**
         * 从前往后
         */
        POSITIVE(1),

        /**
         * 从后往前
         */
        REVERSE(0);

        private int val;

        DirectionEnum(int val) {
            this.val = val;
        }

        public int value() {
            return this.val;
        }
    }

    @Data
    public static class IpSegment {

        /**
         * 开始IP
         */
        @ApiModelProperty(value = "开始IP", name = "startIp")
        private String startIp;

        /**
         * 结束IP
         */
        @ApiModelProperty(value = "结束IP", name = "endIp")
        private String endIp;


        /**
         * 支持3种ip配置方式
         * 单IP 1
         * IP范围 2
         * 子网掩码 3
         */
        private Integer ipType;
    }
}
