package com.quanzhi.auditapiv2.common.dal.dto.api;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Data;

/**
 * <AUTHOR>
 * create at 2021/8/30 3:47 下午
 * @description:
 **/
@Data
@Builder
@ApiModel("API生命周期")
public class ApiLifeCycleDto {

    @ApiModelProperty("日期")
    private String date;

    @ApiModelProperty("新增API数量")
    private long addApiCount;

    @ApiModelProperty("活跃API数量")
    private long activeApiCount;

    @ApiModelProperty("失活API数量")
    private long deactivationApiCount;













}