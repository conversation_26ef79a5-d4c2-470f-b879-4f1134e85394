package com.quanzhi.auditapiv2.common.dal.entity.directive;

import lombok.Data;

import java.util.Map;

/**
 * @Author: yangzx
 * @Date: 2024/7/3 14:35
 */
@Data
public class DirectiveLog {

    private String id;

    private String directiveId;

    private Map<String, Boolean> result;

    private String remark;

//    private String url;

    private String state = "RUNNING";

//    private String directiveValue;

    private boolean delFlag = false;

    private long firstTime;

    private long lastTime;

}
