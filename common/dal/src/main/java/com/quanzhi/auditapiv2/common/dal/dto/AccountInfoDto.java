package com.quanzhi.auditapiv2.common.dal.dto;

import com.quanzhi.auditapiv2.common.util.utils.export.ExportProperty;
import com.quanzhi.metabase.core.model.dto.RiskLevelMatchDto;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * @ClassName AccountInfoDto
 * @Description TODO
 * <AUTHOR>
 * @Date 2020/11/17 14:00
 **/
@Data
public class AccountInfoDto implements Serializable {

    @ApiModelProperty(value = "账号")
    @ExportProperty("账号")
    private String account;
    private String dataLabel;
//    @ApiModelProperty(value = "单日去重数据量")
//    @ExportProperty("单日去重数据量")
//    private Long dataDistinctCnt;

    @ApiModelProperty("敏感数据标签")
    @ExportProperty("敏感数据标签")
    private String dataLabelName;

    @ApiModelProperty(value = "首次发现")
    @ExportProperty(("首次发现"))
    private String firstDate;
    
    @ApiModelProperty(value = "最近使用")
    @ExportProperty("最近使用")
    private String lastDate;

    /**
     * 登录次数
     *
     */
//    private Long loginCnt;
//
    /**
     * 访问量
     */
    private Long visitCnt;

    /**
     * 单次最大返回数据量
     */
//    private Long maxRspDataDistinctCnt;
//
//    /**
//     * 单次最大返回数据量时间
//     */
//    private String maxRspDataDistinctCntDate;
//
    /**
     * 关联IP去重数量
     */
    private Long relatedIpDistinctCnt;

    /**
     * 部门
     */
    private String staffDepart;

    /**
     * 英文账号
     */
    private String staffName;

    /**
     * 员工Id
     */
    private String staffId;

    //姓名
    private String staffChinese;

    /**
     * 邮箱
     */
    private String staffEmail;

    /**
     * 昵称
     */
    private String staffNickName;

    /**
     * 银行卡号
     */
    private String staffBankCard;

    /**
     * 手机号码
     */
    private String staffMobile;

    /**
     * 身份证号
     */
    private String staffIdCard;

    /**
     * 角色
     */
    private String staffRole;

    /**
     * 账号生命状态，需要转化
     */
    private String accountLifeFlag;

    // 复活时间
    private Long reviveTime;

    // ip对应的风险信息名称
    private RiskLevelMatchDto.Risk riskInfo;

    // 风险等级名称
    private String riskLevelName;

    private String app;
    //来源节点
    private String nodeId;
}
