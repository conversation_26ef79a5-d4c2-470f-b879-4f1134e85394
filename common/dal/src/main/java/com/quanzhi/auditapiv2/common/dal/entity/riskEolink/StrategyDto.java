package com.quanzhi.auditapiv2.common.dal.entity.riskEolink;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
public class StrategyDto {
    private String id;
    /**
     * 策略类型
     */
    @ApiModelProperty(value = "策略类型", name = "strategyType")
    @JsonProperty("strategy_type")
    private String strategyType;
    /**
     * 策略UUID，不填则自动生成
     */
    @ApiModelProperty(value = "策略UUID，不填则自动生成", name = "uuid")
    private String uuid;
    /**
     * 策略名称
     */
    @ApiModelProperty(value = "策略名称", name = "name")
    private String name;
    /**
     * 策略描述
     */
    @ApiModelProperty(value = "策略描述", name = "desc")
    private String desc;
    /**
     * 筛选条件列表
     */
    @ApiModelProperty(value = "筛选条件列表", name = "filters")
    private List<Filter> filters;

    @ApiModelProperty(value = "配置", name = "config")
    private StrategyConfig config;

    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间", name = "createTime")
    private Long createTime;

    /**
     * 修改时间
     */
    @ApiModelProperty(value = "修改时间", name = "updateTime")
    private Long updateTime;

    /**
     * 集群状态列表
     */
    @ApiModelProperty(value = "集群状态列表", name = "clusterStatuses")
    @JsonProperty("cluster_statuses")
    private List<StrategyDataDto.ClusterStatuses> clusterStatuses;

    /**
     * 状态
     */
    @ApiModelProperty(value = "状态", name = "status")
    private String status;


    @Data
    public static class Filter{
        private String name;
        private List<String> values;
    }
}
