package com.quanzhi.auditapiv2.common.dal.entity;

import com.quanzhi.auditapiv2.common.util.utils.DataUtil;
import com.quanzhi.auditapiv2.common.util.utils.DateFormat;
import lombok.Data;

@Data
public class UpdateLogModel {
    private String id;

    /**
     * 产品id
     */
    private String productId;

    /**
     * 产品名称
     */
    private String productName;

    /*
     * 上一版本版本号
     */
    private String oldVersion;

    /**
     * 上一版本升级说明
     */
    private String oldDesc;

    /**
     * 当前系统版本号
     */
    private String version;

    /**
     * 当前系统版本说明
     */
    private String desc;

    /**
     * 执行步骤
     */
    private String step;

    /**
     * 版本更新或回滚状态  失败/成功
     */
    private String isSuccess;

    /**
     * 失败原因/成功描述
     */
    private String reason;

    /**
     * 记录日志时间
     */
    private Long logTime;

    private String logTimeFormat;

    private String pkgId;

    public String getLogTimeFormat() {
        if (DataUtil.isEmpty(logTime)) {
            return "0000-00-00 00:00:00";
        } else {
            return DateFormat.stampToTime(logTime, DateFormat.FORMAT_YMDHMS);
        }
    }
}
