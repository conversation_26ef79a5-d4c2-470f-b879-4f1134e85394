//package com.quanzhi.auditapiv2.common.dal.dao.impl;
//
//import com.quanzhi.audit_core.common.model.RiskPolicy;
//import com.quanzhi.auditapiv2.common.dal.dao.IRiskPolicyDao;
//import com.quanzhi.auditapiv2.common.dal.dao.base.impl.MetabaseDaoImpl;
//import com.quanzhi.auditapiv2.common.util.utils.StringUtils;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.data.mongodb.core.MongoTemplate;
//import org.springframework.data.mongodb.core.query.Criteria;
//import org.springframework.data.mongodb.core.query.Query;
//import org.springframework.stereotype.Repository;
//
//import java.util.List;
//
///**
// * 
// * 《异常规则持久层接口实现》
// * 
// * 
// * @Project: 
// * @Module ID:
// * @Comments: 
// * @JDK version used:      <JDK1.8> 
// * <AUTHOR> [<EMAIL>]
// * @since 2020-04-08-上午9:56:10
// */
//@Repository
//public class RiskPolicyDaoImpl extends MetabaseDaoImpl<RiskPolicy> implements IRiskPolicyDao {
//
//    @Autowired
//    private MongoTemplate mongoTemplate;
//
//    private String collectionName = "riskPolicy";
//
//    /**
//     * 异常规则列表
//     * @Comments:  <对此方法的描述，可以引用系统设计中的描述>
//     * <AUTHOR> [<EMAIL>]
//     * @since 2020-04-08 9:56
//     * @param
//     * @return
//     */
//    @Override
//    public List<RiskPolicy> selectRiskPolicyList() throws Exception {
//
//        Criteria criteria = new Criteria();
//        
//        return mongoTemplate.find(new Query(criteria), RiskPolicy.class, collectionName);
//    }
//
//    /**
//     * id查询异常规则详情
//     * @Comments:  <对此方法的描述，可以引用系统设计中的描述>
//     * <AUTHOR> [<EMAIL>]
//     * @since 2020-04-08 9:56
//     * @param
//     * @return
//     */
//    @Override
//    public RiskPolicy selectRiskPolicyById(String id) throws Exception {
//
//        //查询条件
//        Criteria criteria = Criteria.where("_id").is(id);
//        
//        return mongoTemplate.findOne(new Query(criteria), RiskPolicy.class, collectionName);
//    }
//
//    @Override
//    public long countBy(RiskPolicy riskPolicy) {
//        Criteria criteria = new Criteria();
//        if (riskPolicy.getEnable() != null){
//            criteria.where("enable").is(riskPolicy.getEnable());
//        }
//        if (!StringUtils.isNullOrEmpty(riskPolicy.getLevel())){
//            criteria.where("level").is("3");
//        }
//        return mongoTemplate.count(new Query(criteria), RiskPolicy.class, collectionName);
//    }
//}
