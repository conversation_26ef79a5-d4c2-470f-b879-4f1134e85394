package com.quanzhi.auditapiv2.common.dal.dao.workbenchConfig;

import com.quanzhi.auditapiv2.common.dal.entity.workbenchConfig.WorkbenchConfig;

/**
 * @Auther: yangzixian
 * @Date: 2021/12/30 10:30
 * @Description:
 */
public interface IWorkbenchConfigDao {

    /**
     * 查询工作台配置
     * @return
     */
    WorkbenchConfig selectWorkbenchConfig();

    /**
     * 修改工作台配置
     * @param workbenchConfig
     * @return
     */
    String editWorkbenchConfig(WorkbenchConfig workbenchConfig);

}
