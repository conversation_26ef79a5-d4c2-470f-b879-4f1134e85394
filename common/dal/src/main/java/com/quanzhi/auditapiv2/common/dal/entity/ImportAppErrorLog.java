package com.quanzhi.auditapiv2.common.dal.entity;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 *
 * 《应用信息导入错误日志》
 *
 *
 * @Project:
 * @Module ID:
 * @Comments:
 * @JDK version used:      <JDK1.8>
 * <AUTHOR> [<EMAIL>]
 * @since 2021-09-06-上午9:56:10
 */
@Data
public class ImportAppErrorLog {

    /**
     * 域名
     */
    @ApiModelProperty(value = "域名", name = "host")
    private String host;

    /**
     * 应用名称
     */
    @ApiModelProperty(value = "应用名称", name = "appName")
    private String appName;

    /**
     * 所属部门
     */
    @ApiModelProperty(value = "所属部门", name = "updateProgress")
    private String department;

    /**
     * 联系人
     */
    @ApiModelProperty(value = "联系人", name = "name")
    private String name;

    /**
     * 联系电话
     */
    @ApiModelProperty(value = "联系电话", name = "phone")
    private String phone;

    /**
     * 错误信息
     */
    @ApiModelProperty(value = "错误信息", name = "errorMessage")
    private String errorMessage;

    private List<Property> properties;

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static final class Property{
        private String key;
        private String value;
    }
}
