package com.quanzhi.auditapiv2.common.dal.dao.impl.common;

import com.quanzhi.metabase.core.model.query.MetabaseQuery;
import org.springframework.stereotype.Component;

import java.util.*;

/**
 * <AUTHOR>
 * @date 2022/7/25 12:01 下午
 */
@Component
public class QueryAdapterRegistry implements QueryAdapter {

    private final Map<Module, Set<QueryAdapter>> adapterMap = new HashMap<>();

    private ThreadLocal<Module> moduleLocal = new ThreadLocal<>();

    public QueryAdapterRegistry(List<QueryAdapter> criteriaAdapters) {
        for (QueryAdapter adapter : criteriaAdapters) {
            ModuleMapper moduleMapper = adapter.getClass().getAnnotation(ModuleMapper.class);
            if (moduleMapper == null || moduleMapper.modules() == null) {
                continue;
            }
            for (Module module : moduleMapper.modules()) {
                Set<QueryAdapter> adapters = adapterMap.get(module);
                if (adapters == null) {
                    adapters = new LinkedHashSet<>();
                    adapterMap.put(module, adapters);
                }
                adapters.add(adapter);
            }
        }
    }

    public QueryAdapterRegistry module(Module module) {
        moduleLocal.set(module);
        return this;
    }


    @Override
    public void query(Object query, MetabaseQuery metabaseQuery) {
        query0(query, metabaseQuery, true);
    }

    @Override
    public void query(Object query, org.springframework.data.mongodb.core.query.Criteria criteria) {
        query0(query, criteria, false);
    }

    private void query0(Object query, Object criteria, boolean queryMetabase) {
        Module module = moduleLocal.get();
        if (module == null) {
            throw new IllegalStateException("call module(Module module) first");
        }
        try {
            Set<QueryAdapter> adapters = adapterMap.get(module);
            if (adapters != null) {
                for (QueryAdapter criteriaAdapter : adapters) {
                    criteriaAdapter.setModule(module);
                    if (queryMetabase) {
                        criteriaAdapter.query(query, (MetabaseQuery) criteria);
                    } else {
                        criteriaAdapter.query(query, (org.springframework.data.mongodb.core.query.Criteria) criteria);
                    }
                }
            }
        } finally {
            moduleLocal.remove();
        }
    }
}
