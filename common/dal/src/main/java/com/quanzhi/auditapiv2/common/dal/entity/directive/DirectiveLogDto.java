package com.quanzhi.auditapiv2.common.dal.entity.directive;

import com.quanzhi.auditapiv2.common.dal.entity.node.ClusterNode;
import lombok.Data;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

import java.util.Map;

/**
 * @Author: yangzx
 * @Date: 2024/7/3 14:35
 */
@Data
public class DirectiveLogDto {

    private String id;

    private ClusterNode result;

    private String directiveId;

    private Boolean syncResult;

    private String remark;

    private String logInfo;

    private String url;

    private String state;

    private String directiveValue;

    private boolean delFlag = false;

    private long firstTime;

    private long lastTime;

    @Mapper
    public interface DirectiveLogDtoMapper {

        DirectiveLogDto.DirectiveLogDtoMapper INSTANCE = Mappers.getMapper(DirectiveLogDto.DirectiveLogDtoMapper.class);

        @Mapping(target = "result", ignore = true)
        DirectiveLogDto convert(DirectiveLog directiveLog);

    }

}
