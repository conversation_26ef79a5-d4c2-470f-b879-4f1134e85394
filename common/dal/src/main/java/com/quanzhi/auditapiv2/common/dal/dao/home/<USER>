package com.quanzhi.auditapiv2.common.dal.dao.home;

import com.quanzhi.auditapiv2.common.dal.dto.api.ApiStatistics;
import com.quanzhi.auditapiv2.common.dal.dto.api.DataLabelTopDto;
import com.quanzhi.auditapiv2.common.dal.dto.api.ProblemOverviewDto;
import com.quanzhi.auditapiv2.common.dal.dto.securityPosture.SensitiveInfoDto;
import com.quanzhi.auditapiv2.common.dal.dto.securityPosture.StatisticsDto;
import com.quanzhi.auditapiv2.common.dal.dto.weakness.Top10WeaknessDto;
import com.quanzhi.auditapiv2.common.dal.dto.weakness.WeaknessDistributedDto;

import java.util.List;
import java.util.Map;

/**
 * @author: yangzixian
 * @date: 2022/8/9 10:44
 * @description:
 */
public interface HomeDao {

    void upsert(ApiStatistics apiStatistics,
                Top10WeaknessDto top10WeaknessDto,
                WeaknessDistributedDto bigScreenWeaknessDistribute,
                List<DataLabelTopDto> dataLabelTopDtoList,
                SensitiveInfoDto sensitiveInfo,
                StatisticsDto statisticsDto);

    void upsertMulti(Map<String, ApiStatistics> apiStatisticsMap,
                     Map<String, Top10WeaknessDto> top10WeaknessDtoMap,
                     Map<String, List<DataLabelTopDto>> dataLabelTopDtoListMap);

    ProblemOverviewDto getProblemOverview();

    WeaknessDistributedDto getWeaknessDistributed(String viewType);

    ApiStatistics getApiStatistics(String userGroupId);

    Top10WeaknessDto getTop10Weakness(String userGroupId);

    WeaknessDistributedDto getBigScreenWeaknessDistribute();

    SensitiveInfoDto getSensitiveInfo();

    StatisticsDto getSecurityPostureStatistics();

    DataLabelTopDto getDataLabelTop10(String viewType, String userGroupId);

}
