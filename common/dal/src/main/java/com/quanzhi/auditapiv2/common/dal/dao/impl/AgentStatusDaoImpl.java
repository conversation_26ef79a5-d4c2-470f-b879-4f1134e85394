package com.quanzhi.auditapiv2.common.dal.dao.impl;

import com.quanzhi.auditapiv2.common.dal.dao.AgentStatusDao;
import com.quanzhi.auditapiv2.common.dal.dao.base.impl.BaseDaoImpl;
import com.quanzhi.auditapiv2.common.dal.entity.AgentStatus;
import com.quanzhi.auditapiv2.common.util.utils.DataUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Repository;

import java.util.ArrayList;
import java.util.List;

@Repository
public class AgentStatusDaoImpl extends BaseDaoImpl<AgentStatus> implements AgentStatusDao {

    @Autowired
    MongoTemplate mongoTemplate;

    private static String collectionName = "agentStatus";

    /**
     * 获取时间范围内的Agent IP
     *
     * @param startTime
     * @param endTime
     * @return
     */
    @Override
    public List<AgentStatus> listIpByTime(long startTime, long endTime) {
        // 不包含后一分钟
        Criteria where = Criteria.where("insertTime").gte(startTime).lt(endTime);
        List<String> fieldList = new ArrayList<>();
        fieldList.add("agentIp");
        fieldList.add("gatewayIp");
        return mongoTemplate.find(getQueryWithField(where, fieldList), AgentStatus.class, collectionName);
    }

    /**
     * 分页获取Agent上报信息
     *
     * @param gatewayIp
     * @param agentIp
     * @param startTime
     * @param endTime
     * @param page
     * @param limit
     * @return
     */
    @Override
    public List<AgentStatus> listByPage(String gatewayIp, String agentIp, Long startTime, Long endTime, Integer page, Integer limit) {
        Criteria criteria = getAgentCriteria(gatewayIp, agentIp, startTime, endTime);
        Sort sort = Sort.by(Sort.Direction.DESC, "insertTime");
        Query query=new Query();
        query.addCriteria(criteria);
        query.with(sort);
        query.skip(page);
        query.limit(limit);
        return mongoTemplate.find(query, AgentStatus.class, collectionName);
    }

    @Override
    public Long countAgentInfo(String gatewayIp, String agentIp, Long startTime, Long endTime) {
        Criteria criteria = getAgentCriteria(gatewayIp, agentIp, startTime, endTime);
        return mongoTemplate.count(Query.query(criteria), collectionName);

    }

    @Override
    public List<AgentStatus> listLatest(long skip, long limit) {
        Sort sort = Sort.by(Sort.Direction.ASC, "insertTime");
        return mongoTemplate.find(Query.query(new Criteria()).with(sort).skip((int) skip).limit((int) limit),
                AgentStatus.class, collectionName);
    }

    @Override
    public AgentStatus getLatestOne(String gatewayIp, String agentIp) {
        Criteria criteria = Criteria.where("gatewayIp").is(gatewayIp).and("agentIp").is(agentIp);
        Sort sort = Sort.by(Sort.Direction.DESC, "insertTime");
        return mongoTemplate.findOne(Query.query(criteria).with(sort), AgentStatus.class, collectionName);
    }

    private Criteria getAgentCriteria(String gatewayIp, String agentIp, Long startTime, Long endTime) {
        Criteria criteria = new Criteria();
        if (DataUtil.isNotEmpty(gatewayIp)) {
            criteria = Criteria.where("gatewayIp").is(gatewayIp);
        }
        if (DataUtil.isNotEmpty(agentIp)) {
            if (DataUtil.isEmpty(criteria)) {
                criteria = Criteria.where("agentIp").is(agentIp);
            }
            criteria = criteria.and("agentIp").is(agentIp);
        }
        if (DataUtil.isNotEmpty(startTime) && DataUtil.isNotEmpty(endTime)) {
            if (DataUtil.isEmpty(criteria)) {
                criteria = Criteria.where("insertTime").gte(startTime).lte(endTime);
            }
            criteria = criteria.and("insertTime").gte(startTime).lte(endTime);
        } else {
            if (DataUtil.isNotEmpty(startTime)) {
                if (DataUtil.isEmpty(criteria)) {
                    criteria = Criteria.where("insertTime").gte(startTime);
                }
                criteria = criteria.and("insertTime").gte(startTime);
            }
            if (DataUtil.isNotEmpty(endTime)) {
                if (DataUtil.isEmpty(criteria)) {
                    criteria = Criteria.where("insertTime").lte(endTime);
                }
                criteria = criteria.and("insertTime").lte(endTime);
            }
        }
        return criteria;
    }
}
