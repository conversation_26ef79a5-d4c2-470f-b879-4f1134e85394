package com.quanzhi.auditapiv2.common.dal.dao.impl;

import com.quanzhi.auditapiv2.common.dal.dao.ISmartFilterRuleDao;
import com.quanzhi.auditapiv2.common.dal.dao.base.impl.BaseDaoImpl;
import com.quanzhi.auditapiv2.common.dal.dto.filter.SmartFilterCriteriaDTO;
import com.quanzhi.auditapiv2.common.util.utils.ConstantUtil;
import com.quanzhi.auditapiv2.common.util.utils.DataUtil;
import com.quanzhi.metabase.core.model.filter.SmartFilterRule;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.stereotype.Repository;

import java.lang.reflect.Field;
import java.util.List;

@Repository
public class SmartFilterRuleDaoImpl extends BaseDaoImpl<SmartFilterRule> implements ISmartFilterRuleDao {

    @Autowired
    private MongoTemplate mongoTemplate;

    private String collectionName = "smartFilterRule";

    /**
     * 查询智能过滤规则列表(分页)
     *
     * @param
     * @return
     * @Comments: <对此方法的描述，可以引用系统设计中的描述>
     * <AUTHOR> [<EMAIL>]
     * @since 2022-01-07 9:56
     */
    @Override
    public List<SmartFilterRule> selectSmartFilterRuleList(Integer page, Integer limit, String field, Integer sort, String host) throws Exception {

        //分页
        Pageable pageable = PageRequest.of(page - 1, limit);
        //排序
        Sort dbSort = null;
        if (DataUtil.isNotEmpty(field) && DataUtil.isNotEmpty(sort)) {
            if (sort == ConstantUtil.Sort.DESC) {
                dbSort = Sort.by(Sort.Direction.DESC, field);
            } else {
                dbSort = Sort.by(Sort.Direction.ASC, field);
            }
        } else {
            dbSort = Sort.by(Sort.Direction.DESC, "createTime");
        }
        Criteria criteria = Criteria.where("delFlag").is(false);
        if (DataUtil.isNotEmpty(host)) {
            criteria.and("host").regex(host);
        }

        return mongoTemplate.find(new Query().addCriteria(criteria).with(pageable).with(dbSort), SmartFilterRule.class, collectionName);
    }

    @Override
    public List<SmartFilterRule> selectSmartFilterRuleList(SmartFilterCriteriaDTO smartFilterCriteriaDTO) throws Exception {
        //分页
        Pageable pageable = PageRequest.of(smartFilterCriteriaDTO.getPage() - 1, smartFilterCriteriaDTO.getLimit());
        //排序
        Sort dbSort = null;
        if (DataUtil.isNotEmpty(smartFilterCriteriaDTO.getField()) && DataUtil.isNotEmpty(smartFilterCriteriaDTO.getSort())) {
            String field = smartFilterCriteriaDTO.getField();
            if (smartFilterCriteriaDTO.getSort() == ConstantUtil.Sort.DESC) {
                dbSort = Sort.by(Sort.Direction.DESC, field);
            } else {
                dbSort = Sort.by(Sort.Direction.ASC, field);
            }
        } else {
            dbSort = Sort.by(Sort.Direction.DESC, "createTime");
        }
        Query query = fillQuery(smartFilterCriteriaDTO);
        return mongoTemplate.find(query.with(pageable).with(dbSort), SmartFilterRule.class, collectionName);
    }

    /**
     * 查询智能过滤规则数量
     *
     * @param
     * @return
     * @Comments: <对此方法的描述，可以引用系统设计中的描述>
     * <AUTHOR> [<EMAIL>]
     * @since 2022-01-07 9:56
     */
    @Override
    public Long totalCount() throws Exception {

        return mongoTemplate.count(new Query().addCriteria(Criteria.where("delFlag").is(false)), SmartFilterRule.class, collectionName);
    }

    @Override
    public Long totalCount(SmartFilterCriteriaDTO smartFilterCriteriaDTO) {
        Query query = fillQuery(smartFilterCriteriaDTO);
        long count = mongoTemplate.count(query, SmartFilterRule.class, collectionName);
        return count;
    }

    private Query fillQuery(SmartFilterCriteriaDTO smartFilterCriteriaDTO) {
        Criteria criteria = new Criteria();
        criteria.and("delFlag").is(false);
        if (smartFilterCriteriaDTO != null) {
            if (DataUtil.isNotEmpty(smartFilterCriteriaDTO.getHost())) {
                criteria.and("host").regex(DataUtil.regexStrEscape(smartFilterCriteriaDTO.getHost()));
            }
            if (DataUtil.isNotEmpty(smartFilterCriteriaDTO.getResource())) {
                criteria.and("resourceList").regex(DataUtil.regexStrEscape(smartFilterCriteriaDTO.getResource()));
            }
        }
        return new Query(criteria);
    }

    /**
     * id查询智能过滤规则详情
     *
     * @param
     * @return
     * @Comments: <对此方法的描述，可以引用系统设计中的描述>
     * <AUTHOR> [<EMAIL>]
     * @since 2022-01-07 9:56
     */
    @Override
    public SmartFilterRule selectSmartFilterRuleById(String id) throws Exception {

        //查询条件
        Criteria criteria = Criteria.where("_id").is(id);

        return mongoTemplate.findOne(new Query(criteria), SmartFilterRule.class, collectionName);
    }

    /**
     * 编辑智能过滤规则
     *
     * @param
     * @return
     * @Comments: <对此方法的描述，可以引用系统设计中的描述>
     * <AUTHOR> [<EMAIL>]
     * @since 2022-01-07 9:56
     */
    @Override
    public SmartFilterRule updateSmartFilterRule(SmartFilterRule smartFilterRule) throws Exception {

        Query query = new Query(Criteria.where("_id").is(smartFilterRule.getId()));
        Update update = new Update();

        Field[] field = smartFilterRule.getClass().getDeclaredFields();
        for (int i = 0; i < field.length; i++) {

            //设置是否允许访问，不是修改原来的访问权限修饰词。
            field[i].setAccessible(true);
            //字段值不为空 放入map
            if (field[i].get(smartFilterRule) != null) {
                update.set(field[i].getName(), field[i].get(smartFilterRule));
            }
        }
        mongoTemplate.upsert(query, update, SmartFilterRule.class);

        return smartFilterRule;
    }
}