package com.quanzhi.auditapiv2.common.dal.dto.aggRisk;

import com.quanzhi.audit_core.common.risk.RiskInfo;
import com.quanzhi.auditapiv2.common.dal.entity.aggRisk.AggRiskInfo;
import com.quanzhi.metabase.core.model.ResourceEntity;
import com.quanzhi.metabase.core.model.http.HttpAppResource;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.*;

/**
 * @Author: yangzx
 * @Date: 2024/10/9 11:25
 */
@Data
public class AggRiskDto {

    @ApiModelProperty("风险数据库ID")
    private String id;

    @ApiModelProperty("风险id")
    private String operationId;

    @ApiModelProperty("风险名称")
    private String name;

    @ApiModelProperty("风险描述")
    private String desc;

    @ApiModelProperty("风险等级")
    private Integer level;

    @ApiModelProperty("风险等级名称")
    private String levelName;

    @ApiModelProperty("风险状态")
    private Integer state;

    @ApiModelProperty("应用部署域")
    private Set<String> deployDomains;

    @ApiModelProperty("风险状态名称")
    private String stateName;

    @ApiModelProperty("首次发现时间")
    private Long firstTime;

    @ApiModelProperty("最近活跃时间")
    private Long lastTime;

    @ApiModelProperty("风险主体")
    private List<RiskInfo.Entity> entities;

    @ApiModelProperty("主体类型")
    private String entityType;

    @ApiModelProperty("风险类型")
    private String type;

    @ApiModelProperty("关联应用")
    private String host;

    @ApiModelProperty("应用名称")
    private String appName;

    @ApiModelProperty("apiUri")
    private String apiUri;

    @ApiModelProperty("apiUrl")
    private String apiUrl;

    @ApiModelProperty("appUri")
    private String appUri;

    @ApiModelProperty("部门")
    private List<HttpAppResource.Department> departments;

    /**
     * 自定义字段
     */
    private List<Map<String,String>> customFields;

    @ApiModelProperty("ip")
    private String ip;

    @ApiModelProperty("account")
    private String account;

    @ApiModelProperty("置顶状态")
    private Boolean riskMark;

    @ApiModelProperty("节点信息")
    private List<ResourceEntity.Node> nodes = new ArrayList<>();

    @ApiModelProperty("备注")
    private String remark;

    @ApiModelProperty("整改建议")
    private String suggest;

    @ApiModelProperty("风险版本")
    private Integer version = 4;

//    @ApiModelProperty("关联异常")
//    private Set<String> riskIds;

    @ApiModelProperty("策略id")
    private String policyId;

    @ApiModelProperty("加白结果")
    private Map<String, String> whiteMap;

    @ApiModelProperty("弱密码账号&密码")
    private List<WeakUP> accountPasswordList;

    @ApiModelProperty("ai相关信息")
    private AggRiskInfo.AiInfo aiInfo;

    @Data
    public static class WeakUP {
        @ApiModelProperty("账号")
        private String account;

        @ApiModelProperty("密码")
        private String password;

        @ApiModelProperty("desensitizationKey")
        private String desensitizationKey;
    }

    @Mapper
    public interface AggRiskDtoMapper {

        AggRiskDto.AggRiskDtoMapper INSTANCE = Mappers.getMapper(AggRiskDto.AggRiskDtoMapper.class);

        AggRiskDto convert(AggRiskInfo aggRiskInfo);

    }

}
