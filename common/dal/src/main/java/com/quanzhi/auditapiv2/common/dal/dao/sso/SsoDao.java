package com.quanzhi.auditapiv2.common.dal.dao.sso;


import com.quanzhi.login.entity.CasConfig;
import com.quanzhi.login.entity.DingConfig;
import com.quanzhi.login.entity.LdapConfig;
import com.quanzhi.login.entity.Oauth2Config;

public interface SsoDao {

    void saveLdapConfig(LdapConfig ldapConfig);

    LdapConfig getLdapConfig();

    void saveOauth2Config(Oauth2Config oauth2Config);

    Oauth2Config getOauth2Config();

    void saveCasConfig(CasConfig casConfig);

    CasConfig getCasConfig();

    void saveDingConfig(DingConfig dingConfig);

    DingConfig getDingConfig();

}
