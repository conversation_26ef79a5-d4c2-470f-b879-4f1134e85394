package com.quanzhi.auditapiv2.common.dal.dto;

import com.quanzhi.re.core.domain.entity.variable.Period;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
@ApiModel("异常指标/变量查询时使用")
public class VariableQueryDTO implements Serializable {

    @ApiModelProperty("主键id,查询单个指标/变量时使用")
    private String id;

    /**
     * 变量中文名称，由客户自定义，不能重复，页面上填写规则是要进行选择
     */
    @ApiModelProperty("指标/变量中文名称")
    private String title;

    @ApiModelProperty("主体")
    private String entity;

    @ApiModelProperty("变量状态：true-启用,false-未启用")
    private Boolean enabled;

    @ApiModelProperty("主体类型,特征的字段名，多主体用_连接，如：IP_API")
    private String subjectObj;

    @ApiModelProperty("不包含的主体类型,特征的字段名，多主体用_连接，如：IP_API")
    private List<String> unSubjectObj;

    @ApiModelProperty("有/没有基线，true-有，false-没有")
    private Boolean hasBaseline;

    @ApiModelProperty("周期")
    private Period period;

    /**
     * 变量类别
     */
    @ApiModelProperty("变量类别：GENERAL-通用变量，累计变量-CUMULATIVE")
    private String category;

    @ApiModelProperty("变量name,后端生成")
    private String name;

    @ApiModelProperty("分页参数，页码，从1开始")
    private Long page = 1L;

    @ApiModelProperty("分页参数，每页条数")
    private Integer limit = 10;

    @ApiModelProperty("排序字段")
    private String field;

    @ApiModelProperty("排序方式：1-升序，2-降序")
    private Integer sort = 2;
}
