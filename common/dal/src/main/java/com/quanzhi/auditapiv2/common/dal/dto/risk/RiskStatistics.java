package com.quanzhi.auditapiv2.common.dal.dto.risk;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Data;

/**
 * @Author: HaoJun
 * @Date: 2021/6/28 4:37 下午
 */
@Data
@Builder
@ApiModel("风险简单统计")
public class RiskStatistics {
    @ApiModelProperty("总数")
    private long totalCount;
    @ApiModelProperty("高风险数量")
    private long highRiskCount;
}
