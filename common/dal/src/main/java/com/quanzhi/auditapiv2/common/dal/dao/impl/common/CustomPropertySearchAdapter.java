package com.quanzhi.auditapiv2.common.dal.dao.impl.common;

import com.alibaba.fastjson.JSONArray;
import com.quanzhi.auditapiv2.common.dal.dto.app.CustomPropertyDto;
import com.quanzhi.metabase.core.model.query.Criteria;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.regex.Pattern;

/**
 * <AUTHOR>
 * @date 2022/4/22 4:19 下午
 */
@Component
public class CustomPropertySearchAdapter {

    public Criteria getMetabaseCriteria(List<CustomPropertyDto> customProperties) {
        return getMetabaseCriteria(customProperties, "");
    }

    public Criteria getMetabaseCriteria(List<CustomPropertyDto> customProperties, String prefix) {
        if (customProperties == null || customProperties.size() == 0) return null;
        if (prefix == null || prefix.isEmpty()) {
            prefix = "";
        } else {
            prefix = prefix + ".";
        }
        Criteria criteria = new Criteria();
        for (CustomPropertyDto customProperty : customProperties) {
            List<Criteria> criteriaList = null;
            if(customProperty.getValue() instanceof Collection){
                criteriaList= Arrays.asList(Criteria.where("key").is(customProperty.getKey()),
                        Criteria.where("value").in(customProperty.getValue()));
            }else{
                criteriaList= Arrays.asList(Criteria.where("key").is(customProperty.getKey()),
                        Criteria.where("value").regex(Pattern.quote((String) customProperty.getValue())));
            }
            Criteria ele = Criteria.where(prefix + "departments.properties")
                    .elemMatch(criteriaList);
            criteria.andOperator(ele);
        }
        return criteria;
    }

    public org.springframework.data.mongodb.core.query.Criteria getMongoCriteria(List<CustomPropertyDto> customProperties) {
        if (customProperties == null || customProperties.size() == 0) return null;
        List<org.springframework.data.mongodb.core.query.Criteria> chain = new ArrayList<>();
        for (CustomPropertyDto customProperty : customProperties) {
            org.springframework.data.mongodb.core.query.Criteria ele;
            if(customProperty.getValue() instanceof Collection){
                ele= org.springframework.data.mongodb.core.query.Criteria.where("departments.properties")
                        .elemMatch(org.springframework.data.mongodb.core.query.Criteria.where("key")
                                .is(customProperty.getKey())
                                .and("value").in(customProperty.getValue()));
            }else{
                ele = org.springframework.data.mongodb.core.query.Criteria.where("departments.properties")
                        .elemMatch(org.springframework.data.mongodb.core.query.Criteria.where("key")
                                .is(customProperty.getKey())
                                .and("value").regex(Pattern.quote((String) customProperty.getValue())));
            }
            chain.add(ele);
        }
        org.springframework.data.mongodb.core.query.Criteria criteria
                = new org.springframework.data.mongodb.core.query.Criteria();
        criteria.andOperator(chain.toArray(new org.springframework.data.mongodb.core.query.Criteria[0]));
        return criteria;
    }

    public org.springframework.data.mongodb.core.query.Criteria getMongoCriteria(List<CustomPropertyDto> customProperties, String prefix) {
        if (customProperties == null || customProperties.size() == 0) return null;
        if (prefix == null || prefix.isEmpty()) {
            prefix = "";
        } else {
            prefix = prefix + ".";
        }
        List<org.springframework.data.mongodb.core.query.Criteria> chain = new ArrayList<>();
        for (CustomPropertyDto customProperty : customProperties) {
            org.springframework.data.mongodb.core.query.Criteria ele;
            if(customProperty.getValue() instanceof Collection){
                 ele = org.springframework.data.mongodb.core.query.Criteria.where(prefix + "departments.properties")
                        .elemMatch(org.springframework.data.mongodb.core.query.Criteria.where("key")
                                .is(customProperty.getKey())
                                .and("value").in(customProperty.getValue()));
            }else {
                ele = org.springframework.data.mongodb.core.query.Criteria.where(prefix + "departments.properties")
                        .elemMatch(org.springframework.data.mongodb.core.query.Criteria.where("key")
                                .is(customProperty.getKey())
                                .and("value").regex((String) customProperty.getValue()));
            }
            chain.add(ele);
        }
        org.springframework.data.mongodb.core.query.Criteria criteria
                = new org.springframework.data.mongodb.core.query.Criteria();
        criteria.andOperator(chain.toArray(new org.springframework.data.mongodb.core.query.Criteria[0]));
        return criteria;
    }

    public List<CustomPropertyDto> buildCustomProperty(Map<String, Object> map) {
        if (map == null || map.size() == 0) return Collections.emptyList();
        List<Map<String, Object>> list = (List<Map<String, Object>>) map.get("customProperties");
        if (list == null || list.size() == 0) return Collections.emptyList();
        List<CustomPropertyDto> customProperties = new java.util.ArrayList<>();
        for (Map<String, Object> param : list) {
            CustomPropertyDto customProperty = new CustomPropertyDto();
            customProperty.setKey((String) param.get("key"));
            if(param.get("value") instanceof Collection){
                customProperty.setValue(param.get("value"));
            }else{
                customProperty.setValue(com.quanzhi.auditapiv2.common.util.utils.DataUtil.regexStrEscape((String) param.get("value")));
            }
            if (customProperty.getKey() == null || customProperty.getValue() == null) continue;
            customProperties.add(customProperty);
        }
        return customProperties;
    }
}
