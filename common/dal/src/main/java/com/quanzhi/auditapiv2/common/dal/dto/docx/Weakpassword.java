package com.quanzhi.auditapiv2.common.dal.dto.docx;

import lombok.Data;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 *
 * 《登录弱密码》
 *
 *
 * @Project:
 * @Module ID:
 * @Comments:
 * @JDK version used:      <JDK1.8>
 * <AUTHOR> [<EMAIL>]
 * @since 2020-08-28-下午16:52:18
 */
@Data
public class Weakpassword extends ApiWeaknessExportWord {

    @Mapper
    public interface WeakpasswordMapper {

        Weakpassword.WeakpasswordMapper INSTANCE = Mappers.getMapper(Weakpassword.WeakpasswordMapper.class);
        Weakpassword convert(ApiWeaknessExportWord apiWeaknessExportWord);
    }
}
