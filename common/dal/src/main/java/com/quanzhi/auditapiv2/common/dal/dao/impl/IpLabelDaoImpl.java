package com.quanzhi.auditapiv2.common.dal.dao.impl;

import com.quanzhi.auditapiv2.common.dal.dao.IIpLabelDao;
import com.quanzhi.auditapiv2.common.dal.entity.ip.IpLabel;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * @Auther: yangzixian
 * @Date: 2021/9/1 11:37
 * @Description:
 */
@Repository
public class IpLabelDaoImpl implements IIpLabelDao {

    @Autowired
    private MongoTemplate mongoTemplate;

    private String collectionName = "ipLabels";


    @Override
    public String saveIpLabel(IpLabel ipLabel) {
        //检查是否重复
        if (mongoTemplate.exists(new Query().addCriteria(Criteria.where("name").is(ipLabel.getName())), collectionName)) {
            //重复，返回提示信息
            return "名为：" + ipLabel.getName() + "的标签已存在！";
        }
        mongoTemplate.insert(ipLabel, collectionName);
        return "保存成功！";
    }

    @Override
    public String deleteIpLabel(String id) {
        //检查是否存在
        if (mongoTemplate.exists(new Query().addCriteria(Criteria.where("_id").is(id).and("isBuiltIn").is(false)), collectionName)) {
            //存在则删除
            mongoTemplate.remove(new Query().addCriteria(Criteria.where("_id").is(id).and("isBuiltIn").is(false)), collectionName);
            return "删除成功！";
        }
        return "标签不存在或不可删除！";
    }

    @Override
    public String updateIpLabel(IpLabel ipLabel) {
        //检查是否存在同名标签
        if (mongoTemplate.exists(new Query().addCriteria(Criteria.where("name").is(ipLabel.getName())), collectionName)) {
            return "已存在相同名称标签！";
        } else {
            //更新标签信息
            Update update = new Update();
            update.set("name", ipLabel.getName());
            //存在则更新
            mongoTemplate.updateFirst(new Query().addCriteria(Criteria.where("_id").is(ipLabel.getId())), update, collectionName);
            return "更新成功！";
        }
    }

    @Override
    public List<IpLabel> selectIpLabelList() {
        return mongoTemplate.find(new Query().addCriteria(Criteria
                .where("delFlag").is(false)), IpLabel.class, collectionName);
    }

    @Override
    public List<IpLabel> selectIpLabelWithDel() {
        return mongoTemplate.find(new Query(), IpLabel.class, collectionName);
    }

    @Override
    public IpLabel selectIpLabelById(String id) {

        return mongoTemplate.findOne(new Query().addCriteria(Criteria.where("_id").is(id)), IpLabel.class, collectionName);

    }

}
