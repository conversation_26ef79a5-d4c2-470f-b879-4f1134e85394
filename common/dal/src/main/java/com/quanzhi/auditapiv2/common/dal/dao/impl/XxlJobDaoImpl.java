package com.quanzhi.auditapiv2.common.dal.dao.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.alibaba.nacos.api.config.annotation.NacosValue;
import com.quanzhi.audit_core.common.constant.Jobs;
import com.quanzhi.audit_core.common.model.ApiCleanJobParam;
import com.quanzhi.audit_core.common.model.ApiOfflineDiscoverParam;
import com.quanzhi.audit_core.common.model.JobParam;
import com.quanzhi.auditapiv2.common.dal.dao.IXxlJobDao;
import com.quanzhi.auditapiv2.common.dal.dao.schedule.Executor;
import com.quanzhi.auditapiv2.common.dal.dto.XxlJobTaskDto;
import com.quanzhi.auditapiv2.common.dal.dto.XxlJobTaskListDto;
import com.quanzhi.auditapiv2.common.dal.dto.filter.EnableFilterDto;
import com.quanzhi.auditapiv2.common.util.dto.ListOutputDto;
import com.quanzhi.auditapiv2.common.util.utils.DataUtil;
import com.quanzhi.auditapiv2.common.util.utils.ServiceException;
import com.quanzhi.metabase.core.domain.entity.CompositeRuleScheduleInfo;
import com.quanzhi.metabase.core.model.http.KeywordSplitRule;
import okhttp3.*;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.net.URLEncoder;
import java.util.*;

/**
 * @see ScheduleImpl
 */
@Deprecated
public class XxlJobDaoImpl implements IXxlJobDao {

    private Logger logger = LoggerFactory.getLogger(XxlJobDaoImpl.class);

    private OkHttpClient httpClient = new OkHttpClient();

    /**
     * xxljob 请求baseUrl
     */
    @NacosValue(value = "${job.config.request.url:http://192.168.0.155:30890/job-admin}")
    private String baseUrl;

    @NacosValue(value = "${xxl.job.executor.appname}", autoRefreshed = true)
    private String appname;

    /**
     * 执行一次任务
     */
    private String jobStart = "/openApi/jobStart";

    /**
     * 任务日志查询
     */
    private String pageJobLog = "/openApi/pageJobLog";


    private String pageJobLogByGroup = "/openApi/pageJobLogByGroup";


    @Override
    public void addTask(XxlJobTaskDto xxlJobTaskDto) {

        if (DataUtil.isNotEmpty(xxlJobTaskDto)) {

            if (xxlJobTaskDto.getEntity().equals(XxlJobTaskDto.EntityEnum.API.name())) {

                ApiCleanJobParam apiCleanJobParam = new ApiCleanJobParam();

                apiCleanJobParam.setCleanMethod(xxlJobTaskDto.getCleanMethod());
                apiCleanJobParam.setDataType(xxlJobTaskDto.getDataType());
                apiCleanJobParam.setDataItems(xxlJobTaskDto.getDataItems());
                apiCleanJobParam.setStartTimestamp(xxlJobTaskDto.getStartTime());
                apiCleanJobParam.setEndTimestamp(xxlJobTaskDto.getEndTime());
                apiCleanJobParam.setDataItemNames(xxlJobTaskDto.getDataItemNames());
                apiCleanJobParam.setUserId(xxlJobTaskDto.getUserId());

                String groupName = null;
                String jobHandler = null;
                switch (XxlJobTaskDto.TaskTypeEnum.valueOf(xxlJobTaskDto.getTaskType())) {

                    case CLEAN:

                        groupName = Jobs.Metabase.APP_METABASE;
                        jobHandler = Jobs.Metabase.JOB_CLEAN_API;

                        break;

                    case RERUN:
                        groupName = Jobs.Discover.APP_DISCOVER;
                        jobHandler = Jobs.Discover.JOB_DISCOVER_API;
                        break;

                    default:
                        break;
                }

                Map<String, String> httpRequestParam = new HashMap<>();
                httpRequestParam.put("groupName", groupName);
                httpRequestParam.put("jobHandler", jobHandler);

                if (xxlJobTaskDto.getTaskType().equals(XxlJobTaskDto.TaskTypeEnum.CLEAN.name())) {
                    httpRequestParam.put("param", JSON.toJSONString(apiCleanJobParam, SerializerFeature.IgnoreNonFieldGetter
                            , SerializerFeature.WriteNullListAsEmpty));
                } else if (xxlJobTaskDto.getTaskType().equals(XxlJobTaskDto.TaskTypeEnum.RERUN.name())) {
                    ApiOfflineDiscoverParam offlineDiscoverParam = new ApiOfflineDiscoverParam();
                    offlineDiscoverParam.setEndTimestamp(apiCleanJobParam.getEndTimestamp());
                    offlineDiscoverParam.setStartTimestamp(apiCleanJobParam.getStartTimestamp());
                    offlineDiscoverParam.setUserId(apiCleanJobParam.getUserId());
                    JobParam jobParam = new JobParam();
                    jobParam.setDataItemNames(apiCleanJobParam.getDataItemNames());
                    jobParam.setDataItems(apiCleanJobParam.getDataItems());
                    if (StringUtils.isNotEmpty(apiCleanJobParam.getDataType())) {
                        jobParam.setDataType(JobParam.DataType.valueOf(apiCleanJobParam.getDataType()));
                    }
                    offlineDiscoverParam.setJobParams(Collections.singletonList(jobParam));
                    httpRequestParam.put("param", JSON.toJSONString(offlineDiscoverParam, SerializerFeature.IgnoreNonFieldGetter
                            , SerializerFeature.WriteNullListAsEmpty));
                }
                String httpRequestUrl = baseUrl + jobStart;
                executePost(httpRequestUrl, httpRequestParam);
            }
        }
    }

    @Override
    public void addReturnTask(ApiOfflineDiscoverParam apiOfflineDiscoverParam) {

    }

    @Override
    public void addOfflineReportTask (String params) {

        Map<String,String> httpRequestParam = new HashMap<>();
        httpRequestParam.put("groupName",Jobs.Auditapiv2.APP_METABASE);
        httpRequestParam.put("jobHandler",Jobs.Auditapiv2.JOB_OFFLINE_REPORT);
        httpRequestParam.put("param",params);

        String httpRequestUrl = baseUrl + jobStart;

        executePost(httpRequestUrl,httpRequestParam);

    }

    @Override
    public ListOutputDto<XxlJobTaskListDto> getXxlJobList(
            Integer page,
            Integer limit,
            Long startTime,
            Long endTime,
            List<Executor> executors,
            String taskType
    ) {
        ListOutputDto<XxlJobTaskListDto> xxlJobTaskListDtoListOutputDto = new ListOutputDto<>();


        Map<String, String> xxlJobListSearchMap = new HashMap<>();

        try {

            xxlJobListSearchMap.put("start", Integer.toString((page - 1) * limit));
            xxlJobListSearchMap.put("page", limit.toString());
            xxlJobListSearchMap.put("startTime", startTime.toString());
            xxlJobListSearchMap.put("endTime", endTime.toString());
            Map<String, String> map = new HashMap<>();
            executors.forEach(e->map.put(e.getApp(), e.getExecutor()));
            xxlJobListSearchMap.put("groupNameAndjobHandler", URLEncoder.encode(JSON.toJSONString(map), "UTF-8"));
            xxlJobListSearchMap.put("logStatus", "0");

            String httpRequestUrl = baseUrl + pageJobLogByGroup;

            String params = "";
            for (Map.Entry<String, String> param : xxlJobListSearchMap.entrySet()) {
                params += param.getKey() + "=" + param.getValue() + "&";
            }
            httpRequestUrl += "?" + params;

            Response resp = executeGet(httpRequestUrl);

            JSONObject jsonObject = JSON.parseObject(resp.body().string());

            List<XxlJobTaskListDto> xxlJobTaskListDtos = new ArrayList<>();

            xxlJobTaskListDtoListOutputDto.setTotalCount(Long.valueOf(jsonObject.getString("recordsFiltered")));

            if (xxlJobTaskListDtoListOutputDto.getTotalCount() != 0L) {

                JSONArray xxlList = jsonObject.getJSONArray("data");

                if (DataUtil.isNotEmpty(xxlList)) {

                    for (Object xxlObject : xxlList) {

                        JSONObject xxlJsonObject = (JSONObject) xxlObject;

                        XxlJobTaskListDto xxlJobTaskListDto = new XxlJobTaskListDto();

                        xxlJobTaskListDto.setCreateTime(xxlJsonObject.getString("triggerTime"));
                        xxlJobTaskListDto.setEntity("API");
                        xxlJobTaskListDto.setStartTime(xxlJsonObject.getString("triggerTime"));

                        if (DataUtil.isNotEmpty(xxlJsonObject.getString("handleTime"))) {
                            xxlJobTaskListDto.setEndTime(xxlJsonObject.getString("handleTime"));
                        }


                        if (DataUtil.isNotEmpty(taskType) && taskType.equals(XxlJobTaskDto.TaskTypeEnum.CLEAN.name())) {

                            if (DataUtil.isNotEmpty(xxlJsonObject.getString("executorParam"))) {
                                ApiCleanJobParam apiCleanJobParam = JSON.parseObject(xxlJsonObject.getString("executorParam"), ApiCleanJobParam.class);
                                if (DataUtil.isNotEmpty(apiCleanJobParam.getDataItemNames())) {
                                    xxlJobTaskListDto.setTaskName(StringUtils.join(apiCleanJobParam.getDataItemNames(), ","));
                                }
                            }
                        } else if (DataUtil.isNotEmpty(taskType) && taskType.equals(XxlJobTaskDto.TaskTypeEnum.RERUN.name())) {
                            if (DataUtil.isNotEmpty(xxlJsonObject.getString("executorParam"))) {
                                String executorParam = xxlJsonObject.getString("executorParam");
                                JSONObject parseObject = JSONObject.parseObject(executorParam);
                                String jobParams = parseObject.getJSONArray("jobParams").get(0).toString();
                                ApiCleanJobParam apiCleanJobParam = JSON.parseObject(jobParams, ApiCleanJobParam.class);
                                if ("APP".equalsIgnoreCase(apiCleanJobParam.getDataType())) {
                                    xxlJobTaskListDto.setTaskName("指定应用");
                                }else if("URL".equalsIgnoreCase(apiCleanJobParam.getDataType())){
                                    xxlJobTaskListDto.setTaskName("指定API");
                                } else {
                                    xxlJobTaskListDto.setTaskName("全量");
                                }
                            }
                        }

                        if (xxlJsonObject.getString("handleCode").equals("0")) {
                            if (xxlJsonObject.getString("triggerCode").equals("200")
                                    || xxlJsonObject.getString("triggerCode").equals("0")) {
                                xxlJobTaskListDto.setStatus("进行中");
                            } else {
                                xxlJobTaskListDto.setStatus("失败");
                            }
                        } else if (xxlJsonObject.getString("handleCode").equals("200")) {
                            xxlJobTaskListDto.setStatus("成功");
                        } else {
                            xxlJobTaskListDto.setStatus("失败");
                        }
                        xxlJobTaskListDtos.add(xxlJobTaskListDto);
                    }
                }
            }

            xxlJobTaskListDtoListOutputDto.setRows(xxlJobTaskListDtos);
            return xxlJobTaskListDtoListOutputDto;

        } catch (Exception e) {

            logger.error("xxl list return failed ", e);
            throw new ServiceException("没有列表返回！");
        }

    }

    @Override
    public void triggerCompositeJob(String compositeRule) {

        Map<String, String> httpRequestParam = new HashMap<>();
        httpRequestParam.put("groupName", Jobs.Metabase.APP_METABASE);
        httpRequestParam.put("jobHandler", Jobs.Metabase.JOB_COMPOSITE);
        httpRequestParam.put("param", compositeRule);

        String httpRequestUrl = baseUrl + jobStart;
        executePost(httpRequestUrl, httpRequestParam);
    }

    @Override
    public void triggerCompositeBatchJob(CompositeRuleScheduleInfo compositeRuleScheduleInfo) {

    }

    @Override
    public void triggerKeywordSplitJob(KeywordSplitRule keywordSplitRule) {
        Map<String, String> httpRequestParam = new HashMap<>();
        httpRequestParam.put("groupName", Jobs.Metabase.APP_METABASE);
        httpRequestParam.put("jobHandler", "apiKeywordSplitJob");
        httpRequestParam.put("param", JSON.toJSONString(keywordSplitRule));

        String httpRequestUrl = baseUrl + jobStart;
        executePost(httpRequestUrl, httpRequestParam);
    }


    @Override
    public void triggerAppAutoMergeJob() {
        Map<String, String> httpRequestParam = new HashMap<>();
        httpRequestParam.put("groupName", Jobs.Metabase.APP_METABASE);
        httpRequestParam.put("jobHandler", "appAutoMergeJob");

        String httpRequestUrl = baseUrl + jobStart;
        executePost(httpRequestUrl, httpRequestParam);
    }

    @Override
    public void triggerApiAutoSplitJob() {
        Map<String, String> httpRequestParam = new HashMap<>();
        httpRequestParam.put("groupName", Jobs.Auditapiv2.APP_METABASE);
        httpRequestParam.put("jobHandler", "apiAutoSplitJob");

        String httpRequestUrl = baseUrl + jobStart;
        executePost(httpRequestUrl, httpRequestParam);
    }

    @Override
    public void triggerFilterRuleJob(EnableFilterDto enableFilterDto) {
        Map<String, String> httpRequestParam = new HashMap<>();
        httpRequestParam.put("groupName", Jobs.Auditapiv2.APP_METABASE);
        httpRequestParam.put("jobHandler", "filterRuleJob");
        httpRequestParam.put("param", JSON.toJSONString(enableFilterDto));

        String httpRequestUrl = baseUrl + jobStart;
        executePost(httpRequestUrl, httpRequestParam);
    }

    @Override
    public void triggerWeaknessJob() {

        Map<String, String> httpRequestParam = new HashMap<>();
        httpRequestParam.put("groupName", Jobs.Metabase.APP_METABASE);
        httpRequestParam.put("jobHandler", Jobs.Metabase.JOB_WEAKNESS);

        String httpRequestUrl = baseUrl + jobStart;
        executePost(httpRequestUrl, httpRequestParam);
    }

    @Override
    public void addHistoryDataCleanTask(Map<String, String> taskMap) {

        Map<String, String> httpRequestParam = new HashMap<>();
        httpRequestParam.put("groupName", Jobs.Metabase.APP_METABASE);
        httpRequestParam.put("jobHandler", Jobs.Metabase.JOB_HISTORY_DATA_CLEAN);
        httpRequestParam.put("param", JSON.toJSONString(taskMap));

        String httpRequestUrl = baseUrl + jobStart;

        executePost(httpRequestUrl, httpRequestParam);
    }

    @Override
    public void triggerEventFilterPluginJob(Map<String, String> plugins) {
        Map<String, String> httpRequestParam = new HashMap<>();
        httpRequestParam.put("groupName", appname);
        httpRequestParam.put("jobHandler", "eventFilterPluginJob");
        httpRequestParam.put("param", JSON.toJSONString(plugins));

        String httpRequestUrl = baseUrl + jobStart;

        executePost(httpRequestUrl, httpRequestParam);
    }


    @Override
    public ListOutputDto<XxlJobTaskListDto> getHistoryDataCleanJobList(
            Integer page, Integer limit, Long startTime, Long endTime,
            Map<String, String> groupNameAndjobHandler
    ) {
        ListOutputDto<XxlJobTaskListDto> xxlJobTaskListDtoListOutputDto = new ListOutputDto<>();

        JSONObject jsonObject = getHttpClientRsp(page, limit, startTime, endTime, groupNameAndjobHandler);

        List<XxlJobTaskListDto> xxlJobTaskListDtos = new ArrayList<>();

        if (DataUtil.isNotEmpty(jsonObject)) {

            xxlJobTaskListDtoListOutputDto.setTotalCount(Long.valueOf(Optional.ofNullable(jsonObject.getString("recordsFiltered")).orElse("0")));

            if (xxlJobTaskListDtoListOutputDto.getTotalCount() != 0L) {

                JSONArray xxlList = jsonObject.getJSONArray("data");

                if (DataUtil.isNotEmpty(xxlList)) {

                    for (Object xxlObject : xxlList) {

                        JSONObject xxlJsonObject = (JSONObject) xxlObject;

                        XxlJobTaskListDto xxlJobTaskListDto = new XxlJobTaskListDto();

                        xxlJobTaskListDto.setCreateTime(xxlJsonObject.getString("triggerTime"));
                        xxlJobTaskListDto.setStartTime(xxlJsonObject.getString("triggerTime"));

                        if (DataUtil.isNotEmpty(xxlJsonObject.getString("handleTime"))) {
                            xxlJobTaskListDto.setEndTime(xxlJsonObject.getString("handleTime"));
                        }


                        if (xxlJsonObject.getString("handleCode").equals("0")) {
                            xxlJobTaskListDto.setStatus("进行中");
                        } else if (xxlJsonObject.getString("handleCode").equals("200")) {
                            xxlJobTaskListDto.setStatus("成功");
                        } else {
                            xxlJobTaskListDto.setStatus("失败");
                        }

                        if (DataUtil.isNotEmpty(xxlJsonObject.getString("executorParam"))) {
                            xxlJobTaskListDto.setExecutorParam(xxlJsonObject.getString("executorParam"));
                        }

                        xxlJobTaskListDtos.add(xxlJobTaskListDto);
                    }
                }
            }
        }

        xxlJobTaskListDtoListOutputDto.setRows(xxlJobTaskListDtos);
        return xxlJobTaskListDtoListOutputDto;
    }

    private JSONObject getHttpClientRsp(Integer page, Integer limit, Long startTime, Long endTime, Map<String, String> groupNameAndjobHandler) {

        try {
            Map<String, String> xxlJobListSearchMap = new HashMap<>();
            xxlJobListSearchMap.put("start", Integer.toString((page - 1) * limit));
            xxlJobListSearchMap.put("page", limit.toString());
            xxlJobListSearchMap.put("startTime", startTime.toString());
            xxlJobListSearchMap.put("endTime", endTime.toString());
            xxlJobListSearchMap.put("groupNameAndjobHandler", URLEncoder.encode(JSON.toJSONString(groupNameAndjobHandler), "UTF-8"));
            xxlJobListSearchMap.put("logStatus", "0");

            String httpRequestUrl = baseUrl + pageJobLogByGroup;

            String params = "";
            for (Map.Entry<String, String> param : xxlJobListSearchMap.entrySet()) {
                params += param.getKey() + "=" + param.getValue() + "&";
            }
            httpRequestUrl += "?" + params;

            Response resp = executeGet(httpRequestUrl);

            JSONObject jsonObject = JSON.parseObject(resp.body().string());

            return jsonObject;
        } catch (Exception e) {

            logger.error("xxl rsp error", e);
            return null;
        }
    }

    /**
     * 执行httpClient post请求命令
     *
     * @param httpRequestUrl
     * @param jsonObject
     */
    private void executePost(String httpRequestUrl, Map<String, String> jsonObject) {

        FormBody.Builder FormBody = new FormBody.Builder();

        for (Map.Entry<String, String> param : jsonObject.entrySet()) {
            FormBody.add(param.getKey(), param.getValue());
        }

        Request request = new Request.Builder()
                .post(FormBody.build())
                .url(httpRequestUrl)
                .build();

        try {
            Response resp = httpClient.newCall(request).execute();
        } catch (IOException e) {

        }

    }

    /**
     * 执行httpClient get请求命令
     *
     * @param httpRequestUrl
     */
    private Response executeGet(String httpRequestUrl) {

        HttpUrl.Builder httpBuilder = HttpUrl.parse(httpRequestUrl).newBuilder();

        Request request = new Request.Builder().url(httpBuilder.build()).build();

        try {
            Response resp = httpClient.newCall(request).execute();

            return resp;
        } catch (IOException e) {

            throw new ServiceException("请求xxljob失败：", e);
        }

    }

    @Override
    public void triggerJob(String app, String executor, String params) {

    }
}
