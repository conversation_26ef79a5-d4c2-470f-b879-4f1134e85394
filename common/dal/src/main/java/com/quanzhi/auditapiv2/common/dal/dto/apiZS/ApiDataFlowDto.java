package com.quanzhi.auditapiv2.common.dal.dto.apiZS;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * api数据流图dto
 */

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@ApiModel("api数据流动关系")
public class ApiDataFlowDto {

    @ApiModelProperty("标签id、应用host")
    private String source;

    @ApiModelProperty("标签中文、应用名称")
    private String sourceName;

    @ApiModelProperty("起始节点类型")
    private String sourceType;

    @ApiModelProperty("标签类型")
    private TypeAndApiCntDto sourceExtraInfo;

    @ApiModelProperty("应用host、二级网段id")
    private String target;

    @ApiModelProperty("应用名称、二级网段名称")
    private String targetName;

    @ApiModelProperty("目标节点类型")
    private String targetType;

    @ApiModelProperty("、一级网段id")
    private String targetExtraInfo;

    @ApiModelProperty("该sourceName到targetName的API个数")
    private Integer apiCnt;

}
