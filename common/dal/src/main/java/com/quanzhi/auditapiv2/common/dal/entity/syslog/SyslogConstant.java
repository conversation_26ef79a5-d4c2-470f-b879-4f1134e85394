package com.quanzhi.auditapiv2.common.dal.entity.syslog;

public class SyslogConstant {

    public static final int KB = 1024;

    public static final int MB = 1024 * KB;

    public static final long GB = 1024 * MB;

    public static final int SECOND = 1000;

    public static final int MINUTE = 60 * SECOND;

    public static final long HOUR = 60 * MINUTE;

    public static final long DAY = 24 * HOUR;
    /**
     * 给数据添加updateTime字段
     * 为入库、更新的时间
     */
    public static final String UPDATE_TIME_FIELD = "updateTime";
    /**
     * nacos groupId
     */
    public static final String NACOS_GROUPID = "syncer";

    public static final String NACOS_GLOBAL_ID = "datasyncer.sourceHost.storeHost.global.json";

    public static final String CREATE_TABLE_SQL_DATAID = "ck.create.table.sql.json";

    public static final String CLICKHOUSE_GROUP = "clickhouse";
    /**
     * 服务名
     */
    public static final String SERVICE_NAME = "syncer_service";

    public static final String CLUSTER_NAME = "syncer-cluster";
    /**
     * 内存使用百分比超过该比例时，触发强制保存，清理缓存
     */
    public static final Double MEMORY_PERCENT = 0.95;
    /**
     * 当内存剩余小于200M时触发强制保存，清理缓存
     */
    public static final long MIN_MEMORY_LIMIT = 200 * MB;

    public static final String SAVE_TO_KEY = "qz_data_syncer_save_to_key";

    public static final String FROM_KEY = "qz_data_syncer_from_key";

    public static final String STORE_MODEL = "qz_data_syncer_store_model_key";

    public static final String DEFAULT_RETRY_TOPIC = "qz_data_syncer_retry_topic";
    /**
     * 数据同步开关配置dataId
     */
    public static final String SYNCER_SWITCH_DATAID = "switch.json";

    public static final int HTTP_SERVER_PORT = 8888;
    /**
     * 默认的全局配置，优先级很低，只有当什么都没有配置才会采用默认配置
     */
    public static  String DEFAULT_NACOS_SERVER = "nacos-server:8848";
    /**
     * metabase=nacos://nacos-server:8848/
     * metabaseRest=http://metabase-server:8082
     */
    public static String DEFAULT_METABASE_REST_SERVER = "http://metabase-server:8082";
    /**
     * crypt server
     */
    public static String CRYPT_SERVER_DEFAULT = "http://crypto-server:8090";
    /**
     * 产品使用默认命名空间
     */
    public static final String NAMESPACE_PUBLIC = "public";
    /**
     * clickhouse 的连接池的最大连接数
     */
    public static int CLICKHOUSE_MAX_CONNECT = 20;
}
