package com.quanzhi.auditapiv2.common.dal.dto.docx;

import lombok.Data;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 *
 * 《命令执行接口》
 *
 *
 * @Project:
 * @Module ID:
 * @Comments:
 * @JDK version used:      <JDK1.8>
 * <AUTHOR> [<EMAIL>]
 * @since 2020-08-28-下午16:52:18
 */
@Data
public class Shellapi extends ApiWeaknessExportWord {

    @Mapper
    public interface ShellapiMapper {

        Shellapi.ShellapiMapper INSTANCE = Mappers.getMapper(Shellapi.ShellapiMapper.class);
        Shellapi convert(ApiWeaknessExportWord apiWeaknessExportWord);
    }
}
