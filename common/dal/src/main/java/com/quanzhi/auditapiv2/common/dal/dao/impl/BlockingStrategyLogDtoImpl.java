package com.quanzhi.auditapiv2.common.dal.dao.impl;

import com.quanzhi.auditapiv2.common.dal.dao.IBlockingStrategyLogDto;
import com.quanzhi.auditapiv2.common.dal.entity.riskEolink.BlockingStrategyLog;
import com.quanzhi.auditapiv2.common.dal.entity.riskEolink.BlockingStrategyLogSearch;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.stereotype.Repository;

import java.time.Instant;
import java.util.ArrayList;
import java.util.List;

@Slf4j
@Repository
public class BlockingStrategyLogDtoImpl implements IBlockingStrategyLogDto{

    @Autowired
    private MongoTemplate mongoTemplate;

    private final String collectionName = "blockingStrategyLog";

    @Override
    public List<BlockingStrategyLog> selectBlockingStartegyLogList(BlockingStrategyLogSearch strategyLogSearch) {
        //查询条件
        Integer limit = strategyLogSearch.getLimit();
        Integer page = strategyLogSearch.getPage();
        Criteria criteria = new Criteria();
        List<Criteria> subCriteria = new ArrayList<>();
        // 构建查询条件
        if (strategyLogSearch.getContent() != null && !strategyLogSearch.getContent().isEmpty()) {
            subCriteria.add(Criteria.where("ip").regex(strategyLogSearch.getContent()));
            subCriteria.add(Criteria.where("riskId").regex(strategyLogSearch.getContent()));
            subCriteria.add(Criteria.where("api").regex(strategyLogSearch.getContent()));
            subCriteria.add(Criteria.where("net.srcIp").regex(strategyLogSearch.getContent()));
            subCriteria.add(Criteria.where("req.url").regex(strategyLogSearch.getContent()));
            subCriteria.add(Criteria.where("strategyName").regex(strategyLogSearch.getContent()));

        }
        if (!subCriteria.isEmpty()) {
            criteria.orOperator(subCriteria.toArray(new Criteria[subCriteria.size()]));
        }

        // 排序条件
        Sort sort = Sort.by(Sort.Direction.DESC, "updateTime");

        // 构建查询
        Query query = new Query(criteria).with(sort).limit(limit).skip((page - 1) * limit);

        // 执行查询并返回结果
        return mongoTemplate.find(query, BlockingStrategyLog.class, collectionName);
    }

    @Override
    public BlockingStrategyLog selectDetail(String blockId) {

        return mongoTemplate.findOne(new Query(Criteria.where("blockId").is(blockId)), BlockingStrategyLog.class, collectionName);
    }

    @Override
    public BlockingStrategyLog selectDetail(String blockId, String ip, String url) {

        return mongoTemplate.findOne(new Query(Criteria.where("blockId").is(blockId).and("net.srcIp").is(ip)), BlockingStrategyLog.class, collectionName);

    }

    @Override
    public BlockingStrategyLog insertStartegyLog(BlockingStrategyLog log) {
        Long time = System.currentTimeMillis();
        log.setUpdateTime(time);
        log.setCreateTime(time);
        if (log.getBlock()) {
            log.setSuccessCount(1);
            log.setFailCount(0);
        }else {
            log.setSuccessCount(0);
            log.setFailCount(1);
        }
        log.setTotalCount(1);
        return mongoTemplate.insert(log, collectionName);
    }

    @Override
    public void updateStartegyLog(BlockingStrategyLog log) {
        Criteria criteria = Criteria.where("blockId").is(log.getBlockId()).and("net.srcIp").is(log.getNet().getSrcIp());
        Query query = new Query(criteria);
        Update update = new Update();
        if (log.getBlock()) {
            update.set("successCount", log.getSuccessCount() + 1);
        }else {
            update.set("failCount", log.getFailCount() + 1);
        }
        update.set("totalCount", log.getTotalCount() + 1);
        update.set("updateTime", Instant.now().toEpochMilli());

        mongoTemplate.updateFirst(query, update, collectionName);
    }

    @Override
    public long selectCountStartegyLog(BlockingStrategyLogSearch strategyLogSearch) {
        Criteria criteria = new Criteria();
        List<Criteria> subCriteria = new ArrayList<>();
        // 构建查询条件
        if (strategyLogSearch.getContent() != null && !strategyLogSearch.getContent().isEmpty()) {
            subCriteria.add(Criteria.where("ip").regex(strategyLogSearch.getContent()));
            subCriteria.add(Criteria.where("riskId").regex(strategyLogSearch.getContent()));
            subCriteria.add(Criteria.where("api").regex(strategyLogSearch.getContent()));
            subCriteria.add(Criteria.where("strategyName").regex(strategyLogSearch.getContent()));

            subCriteria.add(Criteria.where("net.srcIp").regex(strategyLogSearch.getContent()));
            subCriteria.add(Criteria.where("req.url").regex(strategyLogSearch.getContent()));
        }
        if (!subCriteria.isEmpty()) {
            criteria.orOperator(subCriteria.toArray(new Criteria[0]));
        }
        return mongoTemplate.count(new Query(criteria), BlockingStrategyLog.class, collectionName);

    }
}
