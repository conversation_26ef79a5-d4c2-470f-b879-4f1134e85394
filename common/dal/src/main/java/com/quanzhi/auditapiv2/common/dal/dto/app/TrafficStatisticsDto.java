package com.quanzhi.auditapiv2.common.dal.dto.app;

import com.quanzhi.auditapiv2.common.dal.entity.TrafficStatistics;
import io.swagger.annotations.ApiModel;
import lombok.Builder;
import lombok.Data;

import java.util.List;

/**
 * @Auther: yangzixian
 * @Date: 2021/8/9 14:14
 * @Description:
 */
@Data
@Builder
@ApiModel("流量统计")
public class TrafficStatisticsDto {

    private List<TrafficStatistics> trafficStatisticsList;

    private TrafficStatistics todaytrafficStatistics;

}
