package com.quanzhi.auditapiv2.common.dal.dao;

import com.quanzhi.auditapiv2.common.dal.entity.securityPosture.VisitTrend;

import java.util.List;

/**
 * @Auther: yangzixian
 * @Date: 2021/10/18 19:32
 * @Description:
 */
public interface IVisitTrendDao {

    /**
     * 保存访问趋势
     * @param visitTrend
     */
    void saveVisitTrend(VisitTrend visitTrend);

    /**
     * 获取过去一分钟访问趋势间隔五秒
     * @param time
     * @return
     */
    List<VisitTrend> getVisitTrend(long time);

    /**
     * 删除超时数据
     */
    void clearData();

    /**
     * 获取最新的访问趋势
     * @return
     */
    VisitTrend selectVisitTrendLatest();

}
