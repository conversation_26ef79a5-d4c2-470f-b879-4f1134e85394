package com.quanzhi.auditapiv2.common.dal.dto;

import com.quanzhi.audit_core.common.model.NetworkSegment;
import com.quanzhi.audit_core.common.model.Position;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 *
 * 《网段dto》
 *
 *
 * @Project:
 * @Module ID:
 * @Comments:
 * @JDK version used:      <JDK1.8>
 * <AUTHOR> [<EMAIL>]
 * @since 2020-04-08-上午9:56:10
 */
@Data
public class NetworkSegmentDto {

    @ApiModelProperty(value = "id", name = "id")
    private String id;

    @ApiModelProperty(value = "网段下的ip", name = "ips")
    private String ips;

    @ApiModelProperty(value = "网段", name = "domain")
    private NetworkSegment.Domain domain;

    @ApiModelProperty(value = "地域", name = "position")
    private Position position;
}
