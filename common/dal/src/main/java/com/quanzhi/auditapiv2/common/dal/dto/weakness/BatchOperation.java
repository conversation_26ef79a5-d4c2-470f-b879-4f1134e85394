package com.quanzhi.auditapiv2.common.dal.dto.weakness;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;


/**
 * <AUTHOR>
 * @date 2021/9/1 10:04 上午
 */
@Data
@ApiModel("批量操作")
public class BatchOperation {

    @ApiModelProperty("操作类型")
    private BatchType batchType;

    @ApiModelProperty("值")
    private String batchValue;

    public enum BatchType{
        IGNORE_HOST, WHITENING_API, WHITENING_HOST
    }
}
