package com.quanzhi.auditapiv2.common.dal.entity;

import com.quanzhi.audit_core.common.model.ScopeMatchInfo;
import lombok.Data;

/**
 * <AUTHOR>
 * @Description 风险等级配置
 * @Version
 * @createTime 2023/2/20 4:58 PM
 */
@Data
public class FileSensitive {
    // 数据id
    private String id;

    // 等级名称
    private String levelName;

    // 等级描述
    private String levelDesc;


    // 匹配规则
    private ScopeMatchInfo scopeMatchInfo;

    // 启用状态: true-启用，false-关闭
    private Boolean enableFlag;
}
