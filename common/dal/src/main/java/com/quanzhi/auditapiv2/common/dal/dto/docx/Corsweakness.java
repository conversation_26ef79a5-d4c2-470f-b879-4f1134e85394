package com.quanzhi.auditapiv2.common.dal.dto.docx;

import lombok.Data;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 *
 * 《CORS配置错误》
 *
 *
 * @Project:
 * @Module ID:
 * @Comments:
 * @JDK version used:      <JDK1.8>
 * <AUTHOR> [<EMAIL>]
 * @since 2020-08-28-下午16:52:18
 */
@Data
public class Corsweakness extends ApiWeaknessExportWord {

    @Mapper
    public interface CorsweaknessMapper {

        Corsweakness.CorsweaknessMapper INSTANCE = Mappers.getMapper(Corsweakness.CorsweaknessMapper.class);
        Corsweakness convert(ApiWeaknessExportWord apiWeaknessExportWord);
    }
}
