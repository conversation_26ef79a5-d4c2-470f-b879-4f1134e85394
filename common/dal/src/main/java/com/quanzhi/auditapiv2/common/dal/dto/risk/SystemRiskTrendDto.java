package com.quanzhi.auditapiv2.common.dal.dto.risk;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Data;

/**
 * @Auther: yangzixian
 * @Date: 2021/8/9 14:14
 * @Description:
 */
@Data
@Builder
@ApiModel("风险趋势（总 and 确认）")
public class SystemRiskTrendDto {

    @ApiModelProperty("日期")
    private String date;

    @ApiModelProperty("总风险")
    private Integer totalViews;

    @ApiModelProperty("已确认风险")
    private Integer confirmViews;

}
