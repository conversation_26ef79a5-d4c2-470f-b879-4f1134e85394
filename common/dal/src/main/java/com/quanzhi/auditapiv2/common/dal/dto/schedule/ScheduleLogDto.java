package com.quanzhi.auditapiv2.common.dal.dto.schedule;

import com.quanzhi.audit.mix.schdule.domain.entity.Schedule;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @author: yangzixian
 * @date: 2023.08.21 11:16
 * @description:
 */
@Data
public class ScheduleLogDto {

    @ApiModelProperty(value = "id", name = "id")
    private String id;

    @ApiModelProperty(value = "开始执行时间", name = "startTime")
    private Long startTime;

    @ApiModelProperty(value = "结束执行时间", name = "endTime")
    private Long endTime;

    @ApiModelProperty(value = "执行状态", name = "state")
    private String state;

    @ApiModelProperty(value = "错误回执", name = "status")
    private String errorMsg;

    @ApiModelProperty(value = "定时任务信息", name = "schedule")
    private Schedule schedule;

}
