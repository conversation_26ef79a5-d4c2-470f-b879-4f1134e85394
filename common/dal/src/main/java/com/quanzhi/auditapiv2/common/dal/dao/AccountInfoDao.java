package com.quanzhi.auditapiv2.common.dal.dao;

import com.quanzhi.auditapiv2.common.dal.dto.AccountSearchDto;
import com.quanzhi.auditapiv2.common.dal.dto.common.CommonGroupDto;
import com.quanzhi.auditapiv2.common.dal.dto.query.AccountInfoCriteriaDto;
import com.quanzhi.auditapiv2.common.dal.entity.AccountInfo;
import com.quanzhi.auditapiv2.common.dal.entity.AccountInfoEntity;

import java.util.Iterator;
import java.util.List;

/**
 * @author: yangzixian
 * @date: 10/4/2023 11:09
 * @description:
 */
public interface AccountInfoDao {

    long countApiAccountNum(String apiUri);

    long countAppAccountNum(String appUri);

    long countIpAccountNum(String ip);

    long getCount();

    com.quanzhi.audit_core.common.model.AccountInfo getAccountInfoByAccount(String account);

    List<com.quanzhi.audit_core.common.model.AccountInfo> getAccountInfosByAccount(String account);

    List<AccountInfo> getAccountInfoList(AccountInfoCriteriaDto accountInfoCriteriaDto);

    List<AccountInfoEntity> getAccountInfoListFields(AccountInfoCriteriaDto accountInfoCriteriaDto, List<String> fields);

    Iterator<AccountInfoEntity> cursor(AccountInfoCriteriaDto accountInfoCriteriaDto, List<String> fields);

    Iterator<AccountInfoEntity> cursorNoSort(AccountInfoCriteriaDto accountInfoCriteriaDto, List<String> fields);

    Long getAccountInfoCount(AccountInfoCriteriaDto accountInfoCriteriaDto);

    /**
     * 这个接口不要用，只返回部分数据，注意后面返回部分数据的接口用特殊的方法名称
     *
     * @param account
     * @return
     */
    @Deprecated
    AccountInfo getAccountInfo(String account);

    AccountInfo getAccount(String account);


    List<CommonGroupDto> accountGroup(String groupField);

    long totalCount(AccountSearchDto accountSearchDto);

    void save(List<AccountInfo> accountInfos);

    List<com.quanzhi.audit_core.common.model.AccountInfo> top(int top);

    void remove(AccountInfoCriteriaDto accountInfoCriteriaDto);

    void updateStrategyStatus(String account, Integer state);

    List<String> getAllAccount();

}
