package com.quanzhi.auditapiv2.common.dal.dto.docx;

import com.quanzhi.metabase.core.model.ResourceEntity;
import lombok.Data;

/**
 *
 * 《接口弱点dto》
 *
 *
 * @Project:
 * @Module ID:
 * @Comments:
 * @JDK version used:      <JDK1.8>
 * <AUTHOR> [<EMAIL>]
 * @since 2020-08-28-下午16:52:18
 */
@Data
public class ApiWeaknessExportWord extends ResourceEntity {
    
    /**
     * 弱点名称
     */
    private String name;
    
    /**
     * 等级
     */
    private String level;
    
    /**
     * 状态
     */
    private String state;

    /**
     * 接口url
     */
    private String apiUrl;
    
    /**
     * 弱点描述
     */
    private String description;
    
    /**
     * 整改建议
     */
    private String solution;

    /**
     * 请求数据标签
     */
    private String reqDataLabels;

    /**
     * 返回数据标签
     */
    private String rspDataLabels;

    /**
     * 请求内容
     */
    private String reqBody;

    /**
     * 返回内容
     */
    private String rspBody;

    /**
     * 当前条数
     */
    private Integer number;

    /**
     * 总数
     */
    private Integer count;
}
