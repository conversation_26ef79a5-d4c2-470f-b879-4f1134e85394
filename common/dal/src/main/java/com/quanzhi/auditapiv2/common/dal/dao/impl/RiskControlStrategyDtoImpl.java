package com.quanzhi.auditapiv2.common.dal.dao.impl;

import com.quanzhi.auditapiv2.common.dal.dao.IRiskControlStrategyDto;
import com.quanzhi.auditapiv2.common.dal.entity.riskEolink.BlockingStrategyLog;
import com.quanzhi.auditapiv2.common.dal.entity.riskEolink.StrategyDataDto;
import com.quanzhi.auditapiv2.common.dal.entity.riskEolink.StrategyDto;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

@Service
public class RiskControlStrategyDtoImpl implements IRiskControlStrategyDto {

    @Autowired
    private MongoTemplate mongoTemplate;

    private final String collectionName = "riskStrategy";

    @Override
    public List<StrategyDataDto> getListStrategy(Integer page, Integer limit, Integer sorts, String sortField, String name, String strategyType) {
        Criteria criteria = new Criteria();
        if (name != null) {
            criteria.orOperator(Criteria.where("name").regex(name));
        }
        if (strategyType != null) {
            criteria.orOperator(Criteria.where("strategyType").regex(strategyType));
        }
        Sort sort = null;
        if (sorts != null && sorts.equals(0)) {
            sort = Sort.by(Sort.Direction.ASC, sortField);
        }else if (sorts != null && sorts.equals(1)){
            sort = Sort.by(Sort.Direction.DESC, sortField);
        }else {
            sort = Sort.by(Sort.Direction.ASC, "updateTime");
        }

        return mongoTemplate.find(new Query(criteria).with(sort).limit(limit).skip((page - 1) * limit), StrategyDataDto.class, collectionName);

    }

    @Override
    public long getCountStrategy(String name) {
        Criteria criteria = new Criteria();
        if (name != null) {
            criteria.orOperator(Criteria.where("name").regex(name));
        }

        return mongoTemplate.count(new Query(criteria), collectionName);

    }

    @Override
    public void addStrategy(StrategyDto strategyDto) {
        if(strategyDto.getUuid() != null) {
            Criteria criteria = Criteria.where("uuid").is(strategyDto.getUuid());
            StrategyDto strategyDto1 = mongoTemplate.findOne(new Query(criteria), StrategyDto.class, collectionName);
            if (strategyDto1 == null) {
                long time = System.currentTimeMillis();
                List<StrategyDataDto.ClusterStatuses> clusterStatuses = new ArrayList<>();
                if (strategyDto.getClusterStatuses() != null && strategyDto.getClusterStatuses().size() > 0) {
                    strategyDto.setClusterStatuses(strategyDto.getClusterStatuses());
                }else {
                    StrategyDataDto.ClusterStatuses clusterStatuses1 = new StrategyDataDto.ClusterStatuses();
                    clusterStatuses1.setName("目前是测试状态");
                    clusterStatuses1.setStatus("offline");
                    clusterStatuses.add(clusterStatuses1);
                    strategyDto.setClusterStatuses(clusterStatuses);
                }
                strategyDto.setUpdateTime(time);
                strategyDto.setCreateTime(time);
                mongoTemplate.insert(strategyDto, collectionName);
            }
        }
    }

    @Override
    public void deleteStrategy(String uuid) {
        Criteria criteria = Criteria.where("uuid").is(uuid);
        mongoTemplate.findAllAndRemove(new Query(criteria), StrategyDto.class, collectionName);
    }

    @Override
    public StrategyDataDto editStrategy(StrategyDto strategyDto) {
        Query query = new Query(Criteria.where("_id").is(strategyDto.getId()));
        Update update = new Update();
        update.set("email", strategyDto.getName());
        update.set("desc", strategyDto.getDesc());
        update.set("filters", strategyDto.getFilters());
        update.set("config", strategyDto.getConfig());
        update.set("clusterStatuses", strategyDto.getClusterStatuses());
        update.set("updateTime", System.currentTimeMillis());
        mongoTemplate.updateFirst(query, update, StrategyDataDto.class);
        return null;
    }

    @Override
    public StrategyDto getStrategy(String blockId) {
        Criteria criteria = Criteria.where("uuid").is(blockId);
        return mongoTemplate.findOne(new Query(criteria), StrategyDto.class, collectionName);
    }
}
