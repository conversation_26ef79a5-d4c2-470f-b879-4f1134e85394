package com.quanzhi.auditapiv2.common.dal.dto.weakness;

import com.quanzhi.auditapiv2.common.dal.entity.weakness.WeaknessDateStatistics;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * <AUTHOR>
 * @date 2021/8/30 2:04 下午
 */
@Data
@ApiModel("弱点日趋势")
public class WeakDateStatisticsDto {
    @ApiModelProperty("日期，格式为yyMMdd")
    private String date;
    @ApiModelProperty("新发现弱点")
    private long newCount;
    @ApiModelProperty("新增已修复弱点")
    private long newFixedCount;
    @ApiModelProperty("新增待修复弱点")
    private long newPendingFixCount;

    @Mapper
    public interface WeakDateStatisticsConverter{
        WeakDateStatisticsConverter INSTANCE = Mappers.getMapper(WeakDateStatisticsConverter.class);
        WeakDateStatisticsDto convert(WeaknessDateStatistics weaknessDateStatistics);
    }
}
