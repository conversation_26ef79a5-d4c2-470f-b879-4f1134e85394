package com.quanzhi.auditapiv2.common.dal.dao.impl;

import com.mongodb.BasicDBObject;
import com.mongodb.client.MongoCursor;
import com.quanzhi.auditapiv2.common.dal.dao.IGatewayMonitorDataDao;
import com.quanzhi.auditapiv2.common.dal.dao.IIndexDefine;
import com.quanzhi.auditapiv2.common.dal.entity.GatewayMonitorData;
import com.quanzhi.auditapiv2.common.util.utils.DataUtil;
import org.bson.Document;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.BulkOperations;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.aggregation.Aggregation;
import org.springframework.data.mongodb.core.query.BasicQuery;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.List;

@Repository("gatewayMonitorDataDao")
public class GatewayMonitorDataDaoImpl implements IGatewayMonitorDataDao, IIndexDefine {

    @Resource
    private MongoTemplate mongoTemplate;

    private final String collectionName = "gatewayMonitorData";

    @Override
    public List<GatewayMonitorData> getList(Long startTime, Long endTime) {
        Criteria where = Criteria.where("time").gte(startTime).lte(endTime);
        return mongoTemplate.find(new Query(where), GatewayMonitorData.class, collectionName);
    }

    @Override
    public List<GatewayMonitorData> getList(Long endTime) {
        Criteria where = Criteria.where("time").lte(endTime);
        Aggregation aggregation = Aggregation.newAggregation(
                Aggregation.match(where),
                Aggregation.sort(Sort.Direction.DESC, "time"),
                Aggregation.limit(1)
        );
        return mongoTemplate.aggregate(aggregation, collectionName, GatewayMonitorData.class).getMappedResults();
    }

    @Override
    public List<GatewayMonitorData> getList(Long startTime, Long endTime, Integer start, Integer limit) {
        Criteria where = Criteria.where("time").gte(startTime).lte(endTime);
        Aggregation aggregation = Aggregation.newAggregation(
                Aggregation.match(where),
                Aggregation.skip(start),
                Aggregation.limit(limit)
        );
        return mongoTemplate.aggregate(aggregation, collectionName, GatewayMonitorData.class).getMappedResults();
    }

    @Override
    public Long getCount(Long start, Long end) {
        Criteria where = Criteria.where("time").gte(start).lte(end);
        return mongoTemplate.count(new Query(where), collectionName);
    }

    @Override
    public Long countByIpAndTime(String ip, Long startTime, Long endTime) {
        Criteria criteria = new Criteria();
        if (DataUtil.isNotEmpty(ip)) {
            criteria = Criteria.where("ip").is(ip);
        }
        if (DataUtil.isNotEmpty(startTime) && DataUtil.isNotEmpty(endTime)) {
            if (DataUtil.isEmpty(criteria)) {
                criteria = Criteria.where("time").gte(startTime).lte(endTime);
            }
            criteria = criteria.and("time").gte(startTime).lte(endTime);
        } else {
            if (DataUtil.isNotEmpty(startTime)) {
                if (DataUtil.isEmpty(criteria)) {
                    criteria = Criteria.where("time").gte(startTime);
                }
                criteria = criteria.and("time").gte(startTime);
            }
            if (DataUtil.isEmpty(endTime)) {
                if (DataUtil.isEmpty(criteria)) {
                    criteria = Criteria.where("time").lte(endTime);
                }
                criteria = criteria.and("time").lte(endTime);
            }
        }
        return mongoTemplate.count(Query.query(criteria), collectionName);

    }

    @Override
    public GatewayMonitorData getGatewayDataByTime(Long startTime, Long endTime) {
        Criteria where = Criteria.where("time").gte(startTime).lte(endTime);
        return mongoTemplate.findOne(new Query(where), GatewayMonitorData.class, collectionName);
    }

    @Override
    public  MongoCursor<Document> getCursor(Long start, Long end) {

        Criteria criteria = new Criteria();
        criteria = Criteria.where("time").gte(start).lte(end);

        Document fieldObject = new Document();
        fieldObject.put("time",true);
        fieldObject.put("gatewayData",true);
        fieldObject.put("ok",true);
        fieldObject.put("errorMsg",true);

        BasicQuery query = new BasicQuery(criteria.getCriteriaObject(), fieldObject);

        return mongoTemplate.getCollection(collectionName).find(query.getQueryObject()).iterator();
    }

    @Override
    public MongoCursor<Document> getCursor(String ip, Long startTime, Long endTime) {
        Query query=new Query();
        Criteria criteria = new Criteria();
        if (DataUtil.isNotEmpty(ip)) {
            criteria = Criteria.where("ip").is(ip);
        }
        if (DataUtil.isNotEmpty(startTime) && DataUtil.isNotEmpty(endTime)) {
            if (DataUtil.isEmpty(criteria)) {
                criteria = Criteria.where("time").gte(startTime).lte(endTime);
            }
            criteria = criteria.and("time").gte(startTime).lte(endTime);
        } else {
            if (DataUtil.isNotEmpty(startTime)) {
                if (DataUtil.isEmpty(criteria)) {
                    criteria = Criteria.where("time").gte(startTime);
                }
                criteria = criteria.and("time").gte(startTime);
            }
            if (DataUtil.isEmpty(endTime)) {
                if (DataUtil.isEmpty(criteria)) {
                    criteria = Criteria.where("time").lte(endTime);
                }
                criteria = criteria.and("time").lte(endTime);
            }
        }
        query.addCriteria(criteria);
        query.with(Sort.by(Sort.Order.asc("time")));

        return mongoTemplate.getCollection(collectionName).find(query.getQueryObject()).sort(query.getSortObject()).iterator();
    }

    /**
     * 根据网关IP获取最新监控信息
     *
     * @param gatewayIp
     * @param endTime
     * @return
     */
    @Override
    public GatewayMonitorData getLatestByIp(String gatewayIp, Long endTime) {
        Criteria criteria = new Criteria();
        if (DataUtil.isNotEmpty(gatewayIp)) {
            criteria = Criteria.where("ip").is(gatewayIp);
        }
        if (DataUtil.isNotEmpty(endTime)) {
            if (DataUtil.isEmpty(criteria)) {
                criteria = Criteria.where("time").lte(endTime);
            }
            criteria = criteria.and("time").lte(endTime);
        }
        Sort sort = Sort.by(Sort.Direction.DESC, "time");
        return mongoTemplate.findOne(Query.query(criteria).with(sort), GatewayMonitorData.class, collectionName);
    }

    @Override
    public void insertGatewayMonitorData(List<GatewayMonitorData> gateway) {
        BulkOperations ops = mongoTemplate.bulkOps(BulkOperations.BulkMode.UNORDERED, collectionName);
        ops.insert(gateway);
        ops.execute();
//        mongoTemplate.bulkOps(gateway , collectionName);
    }

    @Override
    public void createIndex() {
        mongoTemplate.getCollection(collectionName).createIndex(new BasicDBObject("time", 1));
    }


    public String getCollectionName() {
        return collectionName;
    }
}
