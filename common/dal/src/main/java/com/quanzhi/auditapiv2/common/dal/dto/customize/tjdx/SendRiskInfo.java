package com.quanzhi.auditapiv2.common.dal.dto.customize.tjdx;

import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * @Author: K, 小康
 * @Date: 2024/02/27/上午9:42
 * @Description:
 */
@Data
public class SendRiskInfo {
    private Integer orgCode;
    private String batchId;
    private Long sendTimestamp;
    private String commitDeviceIp;
    private String commitDeviceNo;
    private List<RiskInfoData> dataList;
    @Data
    public static class RiskInfoData{
        private Integer syncType = 1;//新增
        private String responseStatus;
        private String accessPort;
        private String userAgentValue;
        private String operationType;
        private String cookie;//新增
        private String referrer;
        private String execResult;
        private String requestBody;
        private String responseBody;
        private String fileName;
        private Integer execTime=0;
        private String logIds;//新增
        private Integer riskAggType=2;//新增
        private String id;
        private String riskName;//新增
        private Integer riskLevel;//新增
        private Integer riskOneType;
        private Integer riskTwoType;
        private String riskTypeOther;//非必填
        private String disposalSuggest;//非必填
        private String riskSource = "0";//新增
        private List<Source> sourceList;//新增
        private Integer detectionType = 1;//新增
        private String securityCapabilityId; //非必填
        private String riskDesc;//新增
        private String srcIp;//非必填
        private String destIp;//非必填
        private String accessTime;// Date转String
        private Integer isImportcoreData = 0;//新增
        private Integer isUserInfo = 0;//新增
        private Integer isUserSensitiveData = 0;//新增
        private List<ImpactAsset> impactAssets;
    }
    @Data
    public static class Source{
        private String sourceType;
        private String sourceInfo;
    }
    @Data
    public static class ImpactAsset{
        private Integer type;
        private String assetId;
    }
}
