package com.quanzhi.auditapiv2.common.dal.dto.docx;

import lombok.Data;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 *
 * 《数据库查询接口》
 *
 *
 * @Project:
 * @Module ID:
 * @Comments:
 * @JDK version used:      <JDK1.8>
 * <AUTHOR> [<EMAIL>]
 * @since 2020-08-28-下午16:52:18
 */
@Data
public class Dboperatorapi extends ApiWeaknessExportWord {

    @Mapper
    public interface DboperatorapiMapper {

        Dboperatorapi.DboperatorapiMapper INSTANCE = Mappers.getMapper(Dboperatorapi.DboperatorapiMapper.class);
        Dboperatorapi convert(ApiWeaknessExportWord apiWeaknessExportWord);
    }
}
