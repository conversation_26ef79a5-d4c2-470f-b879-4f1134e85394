package com.quanzhi.auditapiv2.common.dal.dao;

import com.quanzhi.auditapiv2.common.dal.dao.base.IBaseDao;
import com.quanzhi.auditapiv2.common.dal.entity.ImportAppErrorLog;

import java.util.List;

/**
 *
 * 《应用信息导入日志持久层接口》
 *
 *
 * @Project:
 * @Module ID:
 * @Comments:
 * @JDK version used:      <JDK1.8> 
 * <AUTHOR> [<EMAIL>]
 * @since 2021-09-06-上午9:56:10
 */
public interface IImportAppErrorLogDao extends IBaseDao<ImportAppErrorLog> {

    /**
     * 查询应用信息导入错误日志列表(分页)
     * @Comments:  <对此方法的描述，可以引用系统设计中的描述>
     * <AUTHOR> [<EMAIL>]
     * @since 2021-09-06 9:56
     * @param
     * @return
     */
    List<ImportAppErrorLog> selectImportAppErrorLogList(Integer page, Integer limit) throws Exception;

    /**
     * 查询应用信息导入错误日志数量
     * @Comments:  <对此方法的描述，可以引用系统设计中的描述>
     * <AUTHOR> [<EMAIL>]
     * @since 2021-09-06 9:56
     * @param
     * @return
     */
    Long totalCount() throws Exception;

    /**
     * 新增应用信息导入错误日志
     * @Comments:  <对此方法的描述，可以引用系统设计中的描述>
     * <AUTHOR> [<EMAIL>]
     * @since 2021-09-06 9:56
     * @param
     * @return
     */
    ImportAppErrorLog insertImportAppErrorLog(ImportAppErrorLog importAppErrorLog) throws Exception;

    /**
     * 删除应用信息导入错误日志表
     * @Comments:  <对此方法的描述，可以引用系统设计中的描述>
     * <AUTHOR> [<EMAIL>]
     * @since 2021-09-06 9:56
     * @param
     * @return
     */
    void dropTable() throws Exception;
}
