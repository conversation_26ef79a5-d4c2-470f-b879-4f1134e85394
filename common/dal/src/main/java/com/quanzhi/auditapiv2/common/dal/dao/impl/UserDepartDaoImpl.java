package com.quanzhi.auditapiv2.common.dal.dao.impl;

import com.alibaba.fastjson.JSON;
import com.quanzhi.audit_core.common.config.annotation.DynamicValue;
import com.quanzhi.audit_core.common.model.StaffInfo;
import com.quanzhi.auditapiv2.common.dal.dao.IUserDepartDao;
import com.quanzhi.auditapiv2.common.dal.dao.base.impl.MetabaseDaoImpl;
import com.quanzhi.auditapiv2.common.dal.dto.UserDepartCriteriaDto;
import com.quanzhi.auditapiv2.common.dal.entity.PrimaryNameConfig;
import com.quanzhi.auditapiv2.common.dal.enums.PrimaryNameEnum;
import com.quanzhi.auditapiv2.common.util.utils.ConstantUtil;
import com.quanzhi.auditapiv2.common.util.utils.DataUtil;
import com.quanzhi.metabase.common.utils.DateFormat;
import com.quanzhi.metabase.core.model.http.UserDepart;
import com.quanzhi.metabase.core.model.query.MetabaseQuery;
import com.quanzhi.metabase.core.model.query.Predicate;
import com.quanzhi.metabase.core.model.query.Sort;
import com.quanzhi.metabase.core.model.query.SortOrder;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.stereotype.Repository;

import java.util.ArrayList;
import java.util.List;
import java.util.regex.Pattern;

/**
 * @ClassName UserDepartImpl
 * @Description TODO
 * <AUTHOR>
 * @Date 2020/10/16 17:13
 **/
@Repository
@Slf4j
public class UserDepartDaoImpl extends MetabaseDaoImpl<UserDepart> implements IUserDepartDao {
    private final String account_collectionName = "accountInfo";
    private final String userDepart_collectionName = "userDepart";

    @DynamicValue(dataId = "common.searchPriority.json", groupId = "common", typeClz = PrimaryNameConfig.class)
    private PrimaryNameConfig primaryNameConfig;

    @Autowired
    private MongoTemplate mongoTemplate;

    @Override
    public int saveUserDepart(UserDepart userDepart, String account) {
        Update update=new Update();

        Query query = new Query();
        Query queryAccount = new Query();
        if (DataUtil.isNotEmpty(userDepart.getStaffId())) {
            query.addCriteria(Criteria.where("staffId").is(userDepart.getStaffId()));
            queryAccount.addCriteria(Criteria.where("staffId").is(userDepart.getStaffId()));

            update.set("staffId", userDepart.getStaffId());
        } else {
            String staffId = "";
            if (DataUtil.isNotEmpty(userDepart.getStaffChinese())){
                Query userDepartQuery = new Query(Criteria.where("staffChinese").is(userDepart.getStaffChinese()));
                UserDepart userDepart1 = mongoTemplate.findOne(userDepartQuery, UserDepart.class, userDepart_collectionName);
                if (userDepart1 != null){
                    staffId = userDepart1.getStaffId();
                }

                if (DataUtil.isNotEmpty(staffId)) {
                    query.addCriteria(Criteria.where("staffId").is(staffId));
                }
            }

            if (DataUtil.isNotEmpty(account)){
                queryAccount.addCriteria(Criteria.where("account").is(account));
            }

            if (DataUtil.isEmpty(staffId)){
                staffId = userDepart.getStaffChinese();
            }
            update.set("staffId", staffId);
        }

        if (DataUtil.isNotEmpty(userDepart.getStaffDepart())) {
            update.set("staffDepart", userDepart.getStaffDepart());
        }

        if (DataUtil.isNotEmpty(userDepart.getStaffNickName())) {
            update.set("staffNickName", userDepart.getStaffNickName());
        }

        if (DataUtil.isNotEmpty(userDepart.getStaffName())) {
            update.set("staffName", userDepart.getStaffName());
        }

        if (DataUtil.isNotEmpty(userDepart.getStaffChinese())) {
            update.set("staffChinese", userDepart.getStaffChinese());
        }

//        if (DataUtil.isNotEmpty(userDepart.getStaffIdCard())) {
//            update.set("staffIdCard", userDepart.getStaffIdCard());
//        }

//        if (DataUtil.isNotEmpty(userDepart.getStaffBankCard())) {
//            update.set("staffBankCard", userDepart.getStaffBankCard());
//        }

        if (DataUtil.isNotEmpty(userDepart.getStaffIdCard())) {
            update.set("staffIdCard", "");
        }

        if (DataUtil.isNotEmpty(userDepart.getStaffBankCard())) {
            update.set("staffBankCard", "");
        }

        if (DataUtil.isNotEmpty(userDepart.getStaffEmail())) {
            update.set("staffEmail", userDepart.getStaffEmail());
        }

        if (DataUtil.isNotEmpty(userDepart.getFirstDate())) {
            update.set("firstDate", userDepart.getFirstDate());
        }

        if (DataUtil.isNotEmpty(userDepart.getStaffMobile())) {
            update.set("staffMobile", userDepart.getStaffMobile());
        }

        if (DataUtil.isNotEmpty(userDepart.getLastDate())) {
            update.set("lastDate", userDepart.getLastDate());
        }

        if (DataUtil.isNotEmpty(userDepart.getStaffRole())) {
            update.set("staffRole", userDepart.getStaffRole());
        }

        update.set("updateTime", System.currentTimeMillis());

        mongoTemplate.upsert(query, update, UserDepart.class);

        String account_match = this.getPrimaryAccount(userDepart);
        if (DataUtil.isNotEmpty(account_match)) {
            mongoTemplate.upsert(queryAccount, update, account_collectionName);
        }

        return 1;
    }

    @Override
    public List<UserDepart> getUserDepartList(UserDepartCriteriaDto userDepartCriteriaDto) {
        MetabaseQuery metabaseQuery=new MetabaseQuery();
//        metabaseQuery.where("delFlag",Predicate.IS,false);
        if(DataUtil.isNotEmpty(userDepartCriteriaDto.getStaffId())){
            metabaseQuery.where("staffId", Predicate.IS, userDepartCriteriaDto.getStaffId());
        }
        if(DataUtil.isNotEmpty(userDepartCriteriaDto.getStaffDepart())){
            metabaseQuery.where("staffDepart",Predicate.IS, userDepartCriteriaDto.getStaffDepart());
        }
        if(DataUtil.isNotEmpty(userDepartCriteriaDto.getStaffName())){
            metabaseQuery.where("staffName",Predicate.IS, userDepartCriteriaDto.getStaffName());
        }
        if(DataUtil.isNotEmpty(userDepartCriteriaDto.getStaffNickName())){
            metabaseQuery.where("staffNickName",Predicate.IS, userDepartCriteriaDto.getStaffNickName());
        }
        if(DataUtil.isNotEmpty(userDepartCriteriaDto.getStaffBankCard())){
            metabaseQuery.where("staffBankCard",Predicate.IS, userDepartCriteriaDto.getStaffBankCard());
        }
        if(DataUtil.isNotEmpty(userDepartCriteriaDto.getStaffChinese())){
            metabaseQuery.where("staffChinese",Predicate.REGEX, Pattern.quote(userDepartCriteriaDto.getStaffChinese()));
        }
        if(DataUtil.isNotEmpty(userDepartCriteriaDto.getStaffEmail())){
            metabaseQuery.where("staffEmail",Predicate.IS, userDepartCriteriaDto.getStaffEmail());
        }
        if(DataUtil.isNotEmpty(userDepartCriteriaDto.getStaffIdCard())){
            metabaseQuery.where("staffIdCard",Predicate.IS, userDepartCriteriaDto.getStaffIdCard());
        }
        if(DataUtil.isNotEmpty(userDepartCriteriaDto.getStaffMobile())){
            metabaseQuery.where("staffMobile",Predicate.IS, userDepartCriteriaDto.getStaffMobile());
        }
        if (DataUtil.isNotEmpty(userDepartCriteriaDto.getStaffRole())){
            metabaseQuery.where("staffRole",Predicate.IS, userDepartCriteriaDto.getStaffRole());
        }
        if(userDepartCriteriaDto.getPage()!=null&&userDepartCriteriaDto.getLimit()!=null){
            //分页
            metabaseQuery.setSkip( (userDepartCriteriaDto.getPage().intValue() -1) * userDepartCriteriaDto.getLimit().intValue());
            metabaseQuery.limit(userDepartCriteriaDto.getLimit().intValue());
        }
        //排序
        if(DataUtil.isNotEmpty(userDepartCriteriaDto.getField()) && DataUtil.isNotEmpty(userDepartCriteriaDto.getSort())) {

            if(userDepartCriteriaDto.getSort().intValue()== ConstantUtil.Sort.ASC) {
                metabaseQuery.sort(Sort.by(userDepartCriteriaDto.getField(), SortOrder.ASC));
            }else if(userDepartCriteriaDto.getSort().intValue() == ConstantUtil.Sort.DESC) {
                metabaseQuery.sort(Sort.by(userDepartCriteriaDto.getField(), SortOrder.DESC));
            }else {
                metabaseQuery.sort(Sort.by("staffId", SortOrder.DESC));
            }
        }
        else{
            metabaseQuery.sort(Sort.by("staffId", SortOrder.DESC));
        }
        return metabaseClientTemplate.find(metabaseQuery,UserDepart.class);
    }

    @Override
    public UserDepart getUserDepartById(String staffId) {
        MetabaseQuery metabaseQuery=new MetabaseQuery();
        if(DataUtil.isNotEmpty(staffId)){
            metabaseQuery.where("staffId",Predicate.IS,staffId);
        }else{
            log.error("员工Id不能为空");
            return null;
        }
        return metabaseClientTemplate.findOne(metabaseQuery,UserDepart.class);
    }

    @Override
    public Long getCountByCriteria(UserDepartCriteriaDto userDepartCriteriaDto) {
        MetabaseQuery metabaseQuery=new MetabaseQuery();
//        metabaseQuery.where("delFlag",Predicate.IS,false);
        if(DataUtil.isNotEmpty(userDepartCriteriaDto.getStaffId())){
            metabaseQuery.where("staffId", Predicate.IS, userDepartCriteriaDto.getStaffId());
        }
        if(DataUtil.isNotEmpty(userDepartCriteriaDto.getStaffDepart())){
            metabaseQuery.where("staffDepart",Predicate.IS, userDepartCriteriaDto.getStaffDepart());
        }
        if(DataUtil.isNotEmpty(userDepartCriteriaDto.getStaffName())){
            metabaseQuery.where("staffName",Predicate.IS, userDepartCriteriaDto.getStaffName());
        }
        if(DataUtil.isNotEmpty(userDepartCriteriaDto.getStaffNickName())){
            metabaseQuery.where("staffNickName",Predicate.IS, userDepartCriteriaDto.getStaffNickName());
        }
        if(DataUtil.isNotEmpty(userDepartCriteriaDto.getStaffBankCard())){
            metabaseQuery.where("staffBankCard",Predicate.IS, userDepartCriteriaDto.getStaffBankCard());
        }
        if(DataUtil.isNotEmpty(userDepartCriteriaDto.getStaffChinese())){
            metabaseQuery.where("staffChinese",Predicate.REGEX, Pattern.quote(userDepartCriteriaDto.getStaffChinese()));
        }
        if(DataUtil.isNotEmpty(userDepartCriteriaDto.getStaffEmail())){
            metabaseQuery.where("staffEmail",Predicate.IS, userDepartCriteriaDto.getStaffEmail());
        }
        if(DataUtil.isNotEmpty(userDepartCriteriaDto.getStaffIdCard())){
            metabaseQuery.where("staffIdCard",Predicate.IS, userDepartCriteriaDto.getStaffIdCard());
        }
        if(DataUtil.isNotEmpty(userDepartCriteriaDto.getStaffMobile())){
            metabaseQuery.where("staffMobile",Predicate.IS, userDepartCriteriaDto.getStaffMobile());
        }
        if (DataUtil.isNotEmpty(userDepartCriteriaDto.getStaffRole())){
            metabaseQuery.where("staffRole",Predicate.IS, userDepartCriteriaDto.getStaffRole());
        }
        return metabaseClientTemplate.count(metabaseQuery,UserDepart.class);
    }

    @Override
    public void updateById(UserDepart userDepart) {

        Query query=new Query();
        if(DataUtil.isNotEmpty(userDepart.getStaffId())){
            query.addCriteria(Criteria.where("staffId").is(userDepart.getStaffId()));
        }
        Update update=new Update();
        update.set("staffId",userDepart.getStaffId());
        update.set("staffDepart",userDepart.getStaffDepart());
        update.set("staffNickName",userDepart.getStaffNickName());
        update.set("staffName",userDepart.getStaffName());
        update.set("staffChinese",userDepart.getStaffChinese());
//        update.set("staffIdCard",userDepart.getStaffIdCard());
//        update.set("staffBankCard",userDepart.getStaffBankCard());
        update.set("staffIdCard", "");
        update.set("staffBankCard", "");
        update.set("staffEmail",userDepart.getStaffEmail());
        update.set("firstDate",userDepart.getFirstDate());
        update.set("staffMobile",userDepart.getStaffMobile());
        update.set("lastDate",userDepart.getLastDate());
        update.set("staffRole",userDepart.getStaffRole());
        update.set("updateTime", System.currentTimeMillis());

        String day = DateFormat.getCurTimeFormat(DateFormat.FORMAT_YMD);
        update.set("firstDate", day);
        update.set("lastDate", day);

        mongoTemplate.upsert(query,update,UserDepart.class);
    }

    @Override
    public UserDepart getUserDepart(String account, List<String> fields) {
        if (DataUtil.isEmpty(account) || DataUtil.isEmpty(fields)){
            return null;
        }

        List<Criteria> criteriaList = new ArrayList<>();
        for (String field : fields){
            criteriaList.add(Criteria.where(field).is(account));
        }

        Criteria criteria = new Criteria().orOperator(criteriaList.toArray(criteriaList.toArray(new Criteria[criteriaList.size()])));
        Query query=new Query(criteria);

        return mongoTemplate.findOne(query, UserDepart.class, collectionName);
    }

    /**
     * 获取主名称定义的账号信息
     * - 根据优先级提取数据
     */
    private String getPrimaryAccount(UserDepart userDepart) {
        StaffInfo staffInfo = JSON.parseObject(JSON.toJSONString(userDepart), StaffInfo.class);
        String firstAccount = getRealAccount(staffInfo, primaryNameConfig.getFirst());
        if (StringUtils.isNotEmpty(firstAccount)) {
            return firstAccount;
        }

        String secondAccount = getRealAccount(staffInfo, primaryNameConfig.getSecond());
        if (StringUtils.isNotEmpty(secondAccount)) {
            return secondAccount;
        }

        String thirdAccount = getRealAccount(staffInfo, primaryNameConfig.getThird());
        if (StringUtils.isNotEmpty(thirdAccount)) {
            return thirdAccount;
        }

        String forthAccount = getRealAccount(staffInfo, primaryNameConfig.getForth());
        if (StringUtils.isNotEmpty(forthAccount)) {
            return forthAccount;
        }

        String fifthAccount = getRealAccount(staffInfo, primaryNameConfig.getFifth());
        if (StringUtils.isNotEmpty(fifthAccount)) {
            return fifthAccount;
        }

        return null;
    }

    private String getRealAccount(StaffInfo staffInfo, String key) {
        if (StringUtils.isEmpty(key)) {
            return null;
        }
        String account = null;
        if (PrimaryNameEnum.staffName.name().equals(key)) {
            account = staffInfo.getStaffName();
        } else if (PrimaryNameEnum.staffChinese.name().equals(key)) {
            account = staffInfo.getStaffChinese();
        } else if (PrimaryNameEnum.staffId.name().equals(key)) {
            account = staffInfo.getStaffIdCard();
        } else if (PrimaryNameEnum.staffMobile.name().equals(key)) {
            account = staffInfo.getStaffIdCard();
        } else if (PrimaryNameEnum.staffNickName.name().equals(key)) {
            account = staffInfo.getStaffNickName();
        }
        return account;
    }
}
