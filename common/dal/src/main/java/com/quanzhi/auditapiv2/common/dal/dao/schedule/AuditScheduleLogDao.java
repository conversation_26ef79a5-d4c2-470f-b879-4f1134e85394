package com.quanzhi.auditapiv2.common.dal.dao.schedule;

import com.quanzhi.audit.mix.schdule.domain.entity.Schedule;
import com.quanzhi.auditapiv2.common.dal.dto.schedule.ScheduleLogDto;
import com.quanzhi.auditapiv2.common.dal.dto.schedule.ScheduleSearchDto;
import com.quanzhi.auditapiv2.common.util.utils.ConstantUtil;
import com.quanzhi.auditapiv2.common.util.utils.DataUtil;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * @author: yangzixian
 * @date: 2023.08.21 10:52
 * @description:
 */
@Repository
public class AuditScheduleLogDao {

    private final MongoTemplate mongoTemplate;

    private final String COLLECTION_NAME = "scheduleLog";

    public AuditScheduleLogDao(MongoTemplate mongoTemplate) {
        this.mongoTemplate = mongoTemplate;
    }

    public List<ScheduleLogDto> listScheduleLogsByKey(ScheduleSearchDto scheduleSearchDto) {
        //分页
        Pageable pageable = PageRequest.of(scheduleSearchDto.getPage() - 1, scheduleSearchDto.getLimit());
        //排序
        Sort dbSort = null;
        if (DataUtil.isNotEmpty(scheduleSearchDto.getField()) && DataUtil.isNotEmpty(scheduleSearchDto.getSort())) {

            if (scheduleSearchDto.getSort() == ConstantUtil.Sort.ASC) {
                dbSort = Sort.by(Sort.Direction.ASC, scheduleSearchDto.getField(), "_id");
            } else if (scheduleSearchDto.getSort() == ConstantUtil.Sort.DESC) {
                dbSort = Sort.by(Sort.Direction.DESC, scheduleSearchDto.getField(), "_id");
            } else {
                dbSort = Sort.by(Sort.Direction.DESC, scheduleSearchDto.getField(), "_id");
            }
        } else {
            dbSort = Sort.by(Sort.Direction.DESC, "_id");
        }
        return mongoTemplate.find(new Query().addCriteria(Criteria.where("schedule.executor").is(scheduleSearchDto.getExecutor())).with(dbSort).with(pageable), ScheduleLogDto.class, COLLECTION_NAME);
    }

    public Long countScheduleLogs(ScheduleSearchDto scheduleSearchDto) {
        return mongoTemplate.count(new Query().addCriteria(Criteria.where("schedule.executor").is(scheduleSearchDto.getExecutor())), COLLECTION_NAME);
    }

    public Long countScheduleSuccess(String key) {
        return mongoTemplate.count(new Query().addCriteria(Criteria.where("schedule.executor").is(key).and("state").is("SUCCESS")), COLLECTION_NAME);
    }

    public Long countScheduleFail(String key) {
        return mongoTemplate.count(new Query().addCriteria(Criteria.where("schedule.executor").is(key).and("state").is("FAIL")), COLLECTION_NAME);
    }

    public Long countScheduleRun(String key) {
        return mongoTemplate.count(new Query().addCriteria(Criteria.where("schedule.executor").is(key).and("state").is("RUNNING")), COLLECTION_NAME);
    }

}
