package com.quanzhi.auditapiv2.common.dal.dataobject;

import com.quanzhi.audit.mix.permission.domain.Condition;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.index.Indexed;
import org.springframework.data.mongodb.core.mapping.Document;

import java.io.Serializable;
import java.util.Collections;
import java.util.List;

/**
 * @Description: 用户角色实体类
 * @Author: danniel.yu
 * @Date: 2020-05-11 15:24
 */
@Data
@Document
public class SysRole implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 角色id
     */
    @Id
    private String id;

    /**
     * 角色名称
     */
    @Indexed(unique = true, background = true)
    private String roleName;

//    /**
//     * 角色标识
//     */
//    private String roleSign;

    /**
     * 备注
     */
    private String remark;

    //该角色对应的菜单
  //  private List<String> menuIdList;
//
//    private List<String> resourceIdList;
//
//    //该角色对应的权限
//    private List<String> permsList;
//
//    private List<SysMenu> menuList;

    //该角色对应的资产权限Id列表
    @ApiModelProperty("该角色对应的资产权限Id列表")
    @Deprecated
    private List<String> assetsIdList;

    //该角色对应的脆弱性权限Id列表
    @ApiModelProperty("该角色对应的脆弱性权限Id列表")
    @Deprecated
    private List<String> frailtyIdList;

    //该角色对应的报告权限Id列表
    @ApiModelProperty("该角色对应的报告权限Id列表")
    @Deprecated
    private List<String> reportIdList;

    //该角色对应的数据权限Id列表
    @ApiModelProperty("该角色对应的数据权限Id列表")
    @Deprecated
    private List<String> dataIdList;

    /**
     * 角色类型（1-系统内置角色，2-自定义角色）
     */
    private Integer type;

    /**
     * 状态（0-禁用，1-正常）
     */
    private Integer status;

    /**
     * 创建者id
     */
    private String userIdCreate;

    /**
     * 创建者name
     */
    private String userNameCreate;


    //assets   frailty 、report、data
    /**
     * 创建时间
     */
    private Long gmtCreate;

    /**
     * 修改时间
     */
    private Long gmtModified;

    /**
     * 环境标识，与配置文件中保持一致：1 - 默认使用api产品，2 - 使用出境工具的网关配置
     */
    @Deprecated
    private Integer envFlag;

    /**
     * 是否展示
     */
    @ApiModelProperty(value = "是否展示", name = "showFlag")
    private Boolean showFlag;


    /**
     * 菜单权限
     */
   private List<Condition> menuConditions;
    /**
     * 功能权限
     */
   private List<Condition> funcConditions;
    /**
     * 操作权限
     */
   private List<Condition> actionConditions;



    public static SysRole createSSORole() {//todo 权限
        SysRole sysRole = new SysRole();
        sysRole.setRemark("拥有所有产品页面的查看权限，除去角色管理和审计日志");
        sysRole.setRoleName("数据安全管理员");
        sysRole.setAssetsIdList(Collections.singletonList("1"));
        sysRole.setFrailtyIdList(Collections.singletonList("4"));
        sysRole.setReportIdList(Collections.singletonList("7"));
        sysRole.setDataIdList(Collections.singletonList("11"));
        sysRole.setType(2);
        sysRole.setShowFlag(true);
        sysRole.setEnvFlag(0);
        sysRole.setStatus(1);
        sysRole.setGmtCreate(System.currentTimeMillis());
        return sysRole;
    }

//    private Set<Menu> menus;//该角色对应的菜单
//
//    private Set<Auth> auths;//该角色对应的权限

    /**
     * 角色类型枚举类
     */
    public static enum TypeEnum {

        /**
         * 系统内置角色
         */
        SYSTEM(1),

        /**
         * 自定义角色
         */
        CUSTOM(2);

        private int val;

        TypeEnum(int val) {
            this.val = val;
        }

        public int value() {
            return this.val;
        }
    }

    /**
     * 状态枚举类
     */
    public static enum StatusEnum {

        /**
         * 禁用
         */
        DISABLED(0),

        /**
         * 正常
         */
        RESTORE(1);

        private int val;

        StatusEnum(int val) {
            this.val = val;
        }

        public int value() {
            return this.val;
        }
    }

}
