package com.quanzhi.auditapiv2.common.dal.dao;

import com.quanzhi.auditapiv2.common.dal.entity.ExportTaskModel;
import com.quanzhi.auditapiv2.common.dal.entity.ExportTaskStatus;
import com.quanzhi.auditapiv2.common.util.entity.ExportTaskType;

import java.util.List;

/**
 * 导出任务数据管理
 * <AUTHOR>
 * @date 2019-12-08
 * @time 12:04
 */
public interface IExportTaskDao {
    /**
     * 获取第一个尚未执行的任务
     * @return
     */
    ExportTaskModel findFirstTask();

    /**
     * 获取指定id的任务
     * @param taskId
     * @return
     */
    ExportTaskModel getTask(String taskId);

    Boolean taskNameExist (String taskId,String taskName);

    void updateTaskName (String taskId,String taskName);

    /**
     * 根据任务类型和任务状态查询任务
     * @param taskType
     * @param taskStatus
     * @return
     */
    List<ExportTaskModel> getTaskByTypeStatus(ExportTaskType taskType, ExportTaskStatus taskStatus);

    /**
     * 插入一个新任务
     * @param task
     * @return
     */
    int insertTask(ExportTaskModel task);

    /**
     * 移除指定的任务
     * @param taskId
     * @return
     */
    void removeTask(String taskId);

    /**
     * 分页返回任务
     * @param page
     * @param size
     * @return
     */
    List<ExportTaskModel> pageTask(ExportTaskType taskType, ExportTaskStatus taskStatus, int page, int size, String sort);

    Integer pageTaskCount(ExportTaskType taskType, ExportTaskStatus taskStatus);
    /**
     * 变更任务状态
     * @param taskId
     * @param status
     * @param errMsg
     */
    void setStatusAndMsg(String taskId, ExportTaskStatus status, String errMsg);

    /**
     * 启动任务
     * @param taskId
     */
    void setStartTask(String taskId);

    /**
     * 变更结果文件列表
     * @param taskId
     */
    void setResultFiles(String taskId, List<String> resultFiles);

    /**
     * 更新任务状态
     * @param taskId
     * @param status
     * @param errMsg
     * @param resultFiles
     */
    void setTerminatedTask(String taskId, ExportTaskStatus status, String errMsg, List<String> resultFiles,ExportTaskModel taskModel );

    void setTerminatedTask(String taskId, ExportTaskStatus status, String errMsg, List<String> resultFiles);

    /**
     * 获取所有任务数
     * @return
     */
    Long getTaskTotalCount();

    void updateExportTypeName(String taskId,String taskTypeName);
}
