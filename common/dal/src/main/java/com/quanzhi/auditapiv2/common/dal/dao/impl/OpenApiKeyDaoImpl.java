package com.quanzhi.auditapiv2.common.dal.dao.impl;

import com.quanzhi.auditapiv2.common.dal.dao.IOpenApiKeyDao;
import com.quanzhi.auditapiv2.common.dal.dao.base.impl.BaseDaoImpl;
import com.quanzhi.auditapiv2.common.dal.entity.OpenApiKey;
import org.joda.time.LocalDate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Repository;

import java.util.UUID;

/**
 * 《开放接口密钥持久层接口实现》
 *
 * <AUTHOR> [<EMAIL>]
 * @Project:
 * @Module ID:
 * @Comments:
 * @JDK version used:      <JDK1.8>
 * @since 2021-08-17-上午9:09:10
 */
@Repository
public class OpenApiKeyDaoImpl extends BaseDaoImpl<OpenApiKey> implements IOpenApiKeyDao {

    @Autowired
    MongoTemplate mongoTemplate;

    String collectionName = "openApiKey";

    /**
     * id查询开放接口密钥详情
     *
     * @param
     * @return
     * @Comments: <对此方法的描述，可以引用系统设计中的描述>
     * <AUTHOR> [<EMAIL>]
     * @since 2021-08-17 9:09
     */
    @Override
    public OpenApiKey selectOpenApiKeyById(String useKey) {

        //查询条件
        Criteria criteria = Criteria.where("useKey").is(useKey);

        return mongoTemplate.findOne(new Query(criteria), OpenApiKey.class, collectionName);
    }

    @Override
    public OpenApiKey selectOpenApiKeyByAccessKey(String accessKey) {
        //查询条件
        Criteria criteria = Criteria.where("_id").is(accessKey);

        return mongoTemplate.findOne(new Query(criteria), OpenApiKey.class, collectionName);
    }

    /**
     * 新增开放接口密钥
     *
     * @param
     * @return
     * @Comments: <对此方法的描述，可以引用系统设计中的描述>
     * <AUTHOR> [<EMAIL>]
     * @since 2021-08-17 9:09
     */
    @Override
    public OpenApiKey insertOpenApiKey(OpenApiKey openApiKey) {

        return mongoTemplate.insert(openApiKey, collectionName);
    }

    @Override
    public Long count() {
        return mongoTemplate.count(new Query(), collectionName);
    }

    @Override
    public OpenApiKey findOne() {
        return mongoTemplate.findOne(new Query(), OpenApiKey.class, collectionName);
    }

    @Override
    public OpenApiKey getOrCreateClusterOpenAPI(String userKey) {
        OpenApiKey openApiKey = selectOpenApiKeyById(userKey);
        if (openApiKey != null) {
            return openApiKey;
        }
        openApiKey = new OpenApiKey();
        openApiKey.setId(UUID.randomUUID().toString());
        openApiKey.setUseKey(userKey);
        openApiKey.setKey(UUID.randomUUID().toString());
        openApiKey.setDeadline(0L);
        openApiKey.setCreateDate(System.currentTimeMillis());
        mongoTemplate.save(openApiKey);
        return openApiKey;
    }
}
