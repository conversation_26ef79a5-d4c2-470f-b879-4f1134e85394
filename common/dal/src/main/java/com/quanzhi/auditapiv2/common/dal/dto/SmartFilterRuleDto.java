package com.quanzhi.auditapiv2.common.dal.dto;


import com.quanzhi.metabase.core.model.filter.SmartFilterRule;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 *
 * 《智能过滤规则DTO》
 *
 *
 * @Project:
 * @Module ID:
 * @Comments:
 * @JDK version used:      <JDK1.8> 
 * <AUTHOR> [<EMAIL>]
 * @since 2022-01-07-上午9:56:10
 */
@Data
@ApiModel
public class SmartFilterRuleDto {

    /**
     * id
     */
    @ApiModelProperty(value = "id", name = "id")
    private String id;
    
    /**
     * 过滤规则所属应用
     */
    @ApiModelProperty(value = "过滤规则所属应用", name = "host")
    private String host;


    /**
     * 生效范围
     */
    private List<String> effectScopes;

    /**
     * 过滤时判断的目标
     */
    @ApiModelProperty(value = "过滤时判断的目标", name = "target")
    private String target;

    /**
     * 针对过滤目标的计算
     */
    @ApiModelProperty(value = "针对过滤目标的计算", name = "targetOp")
    private String targetOp;

    /**
     * 针对过滤目标和结果比较的运算操作，默认为eq
     */
    @ApiModelProperty(value = "针对过滤目标和结果比较的运算操作，默认为eq", name = "compareOp")
    private String compareOp;

    /**
     * 针对过滤目标计算后，对结果的比较值
     */
    @ApiModelProperty(value = "针对过滤目标计算后，对结果的比较值", name = "compareValue")
    private String compareValue;

    /**
     * 匹配过滤规则的百分比*100
     */
    @ApiModelProperty(value = "匹配过滤规则的百分比*100", name = "matchRate")
    private Integer matchRate;

    /**
     * 是否启用规则
     */
    @ApiModelProperty(value = "是否启用规则", name = "enable")
    private Boolean enable;

    /**
     * 是否删除
     */
    @ApiModelProperty(value = "是否删除", name = "delFlag")
    private Boolean delFlag;

    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间", name = "createTime")
    private Long createTime;

    /**
     * 更新时间
     */
    @ApiModelProperty(value = "更新时间", name = "updateTime")
    private Long updateTime;


    @ApiModelProperty(value = "已过滤日志数", name = "filterCount")
    private Long filterCount;


    @ApiModelProperty(value = "真正要匹配的过滤内容", name = "filterCount")
    private List<String> resourceList;

    @ApiModelProperty(value = "匹配到的接口uri", name = "filterCount")
    private List<String> matchApiUris;

    @ApiModelProperty(value = "匹配接口数", name = "filterCount")
    private Integer matchApiCount;

    @ApiModelProperty(value = "描述", name = "desc")
    private String desc;

    public String getRuleKey() {
        return String.format("host:{},target:{},targetOp:{},compareOp:{},compareValue{}", host, target, targetOp, compareOp, compareValue);
    }
    
    @Mapper
    public interface SmartFilterRuleDtoMapper {

        SmartFilterRuleDto.SmartFilterRuleDtoMapper INSTANCE = Mappers.getMapper(SmartFilterRuleDto.SmartFilterRuleDtoMapper.class);
        SmartFilterRuleDto convert(SmartFilterRule smartFilterRule);
    }
}

