package com.quanzhi.auditapiv2.common.dal.dto.gateway;

import com.quanzhi.auditapiv2.common.dal.dto.GatewayDto;
import lombok.Data;

/**
 * <AUTHOR>
 * @description
 * @date 2023/8/17 16:49
 */

@Data
public class GatewayConfigInfoDto {

    private String id;

    //网关名称
    private String name;

    //状态
    private Integer status;

    //网关ip
    private String gatewayIp;

    //modes
    private String modes;

    //网关版本
    private String version;


    //近7天最高速率
    private String maxFlowFor7d;

    //近7天平均速率
    private String avgFlowFor7d;

    //近7天最高QPS
    private String maxQPSFor7d;

    //近7天平均QPS
    private String avgQPSFor7d;
}
