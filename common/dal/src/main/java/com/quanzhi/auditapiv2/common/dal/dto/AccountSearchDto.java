package com.quanzhi.auditapiv2.common.dal.dto;

import com.quanzhi.auditapiv2.common.dal.dto.query.BaseSearchDto;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @description: ip查询条件
 **/
@Data
@ApiModel
public class AccountSearchDto extends BaseSearchDto {

    @ApiModelProperty(value = "账号状态", name = "accountLifeFlag")
    private Integer accountLifeFlag;

    private String nodeId;

    @ApiModelProperty(value = "风险等级", name = "riskLevel")
    private List<Integer> riskLevel;

    @ApiModelProperty(value = "账号", name = "account")
    private String account;

    @ApiModelProperty(value = "应用", name = "appUriList")
    private String appUriList;

    @ApiModelProperty(value = "姓名", name = "staffChinese")
    private String staffChinese;

    @ApiModelProperty(value = "部门", name = "staffDepart")
    private String staffDepart;

    @ApiModelProperty(value = "角色", name = "staffRole")
    private String staffRole;

    @ApiModelProperty(value = "邮箱", name = "staffEmail")
    private String staffEmail;

    private String riskNamesOperator;

    private String rspDataLabelListOperator;

    @ApiModelProperty(value = "风险事件", name = "riskNames")
    private List<String> riskNames;

    @ApiModelProperty(value = "返回数据标签", name = "rspDataLabelList")
    private List<List<String>> rspDataLabelList;

    private List<String> useRspDataLabelList;

    @ApiModelProperty(value = "首次发现时间", name = "firstDate")
    private List<Long> firstDate;

    @ApiModelProperty(value = "最新活跃时间", name = "lastDate")
    private List<Long> lastDate;

    @ApiModelProperty(value = "权限条件", name = "dataPermissionMap")
    private Map<String,Object> dataPermissionMap;


    /**
     * 分组字段枚举
     */
    public enum GroupFieldEnum {

        accountLifeFlag("accountLifeFlag"),

        riskLevel("riskLevel");

        String name;

        GroupFieldEnum(String name) {

            this.name = name;
        }

        public String getName() {
            return name;
        }

    }

}