package com.quanzhi.auditapiv2.common.dal.dao;

import com.quanzhi.auditapiv2.common.dal.entity.securityPosture.BigScreenConfig;
import org.springframework.stereotype.Repository;

/**
 * @Auther: yangzixian
 * @Date: 2021/10/21 16:09
 * @Description:
 */
@Repository
public interface IBigScreenConfigDao {

    /**
     * 保存大屏配置
     * @param bigScreenConfig
     */
    void saveBigScreenConfig(BigScreenConfig bigScreenConfig);

    /**
     * 获取大屏配置
     * @return
     */
    BigScreenConfig selectBigScreenConfig();

}
