package com.quanzhi.auditapiv2.common.dal.dao;

import com.quanzhi.auditapiv2.common.dal.dao.base.IBaseDao;
import com.quanzhi.auditapiv2.common.dal.entity.CustomFields;

import java.util.List;

/**
 * 《接口弱点持久层接口》
 *
 * <AUTHOR> [<EMAIL>]
 * @Project:
 * @Module ID:
 * @Comments:
 * @JDK version used:      <JDK1.8>
 * @since 2020-08-28-下午16:52:18
 */
public interface ICustomFieldsDao extends IBaseDao<CustomFields> {

    List<CustomFields> findCustomFieldsByType(String type);

}
