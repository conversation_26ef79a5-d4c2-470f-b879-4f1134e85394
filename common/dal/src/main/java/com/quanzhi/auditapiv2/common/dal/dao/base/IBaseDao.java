package com.quanzhi.auditapiv2.common.dal.dao.base;

import com.mongodb.BasicDBObject;
//import com.mongodb.DBCursor;
import com.mongodb.client.FindIterable;
import com.quanzhi.auditapiv2.common.dal.dao.common.BatchUpdateOption;
import com.quanzhi.auditapiv2.common.util.dto.AggregationDto;
import com.quanzhi.auditapiv2.common.util.dto.AggregationListDto;
import com.quanzhi.auditapiv2.common.util.dto.GroupDto;
import com.quanzhi.auditapiv2.common.util.dto.ListOutputDto;
import com.quanzhi.metabase.core.model.query.MetabaseQuery;
import com.quanzhi.metabase.core.model.query.ResourceUpdates;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Update;

import java.util.List;
import java.util.Set;

/**
 * Created by she<PERSON><PERSON> on 2017/11/16.
 */
public interface IBaseDao<T> {
    Object getFilterId(String id) throws Exception;

    List<Object> getFilterIds(List<String> ids) throws Exception;

    /**
     * 保存
     *
     * @param object
     */
    void save(T object);

   T saveRt(T object);

    /**
     * 批量保存
     *
     * @param objects
     */
    void batchSave(List<T> objects);

    void batchSave(List<T> objects, String collectionName);

    /**
     * 更新
     *
     * @param object
     */
    void update(T object);

    /**
     * 更新
     *
     * @param criteria
     * @param object
     */
    void update(T criteria, T object);

    /**
     * 更新多个
     *
     * @param criteria
     * @param object
     */
    void updateMulti(T criteria, T object);

    void updateMulti(Criteria criteria, Update update);

    /**
     * 更新或插入
     *
     * @param criteria
     * @param object
     */
    void upsert(T criteria, T object);

    /**
     * 更新或插入
     *
     * @param id
     * @param object
     */
    void upsert(String id, T object);

    /**
     * 根据ID，获取记录
     *
     * @param id
     * @return
     */
    T getById(String id);

    T getById(String id, String collectionName);

    /**
     * 根据ID列表，获取所有记录
     *
     * @param ids
     * @return
     */
    List<T> getByIds(List<String> ids);

    void batchUpsert(List<T> objs, String collectionName, boolean upsert, boolean multi);

    /**
     * 根据查询条件，获取一个匹配的记录
     *
     * @param criteria
     * @return
     */
    T getByCriteria(T criteria);

    T getByCriteriaAndCollectionName(T criteria, String collectionName);

    Boolean isExists(Criteria criteria);

    /**
     * 判断文档是否存在
     *
     * @param criteria
     * @return
     */
    Boolean isExists(T criteria);

    /**
     * 判断文档是否存在
     *
     * @param criteria
     * @return
     */
    Boolean isExists(T criteria, T fuzzy);

    T getByCriteria(Criteria criteria);

    /**
     * 根据查询条件，并排除特定ID，获取一个匹配的记录
     *
     * @param criteria
     * @return
     */
    T getByCriteria(T criteria, String excludeId);

    /**
     * 根据查询条件，并排除特定ID，获取一个所有匹配的记录
     *
     * @param criteria
     * @param excludeId
     * @return
     */
    List<T> getAllByCriteria(T criteria, String excludeId);

    List<T> getAllByCriteria(T criteria);

    List<T> getAllByCriteria(Criteria criteria);

    List<T> getLimitByCriteria(Criteria criteria, Integer skip, Integer limit);

    List<T> getAllByCriteria(T criteria, T fuzzy);

    List<T> getAllByCriteria(T criteria, T fuzzy, List<String> fields);

    List<T> getAllByCriteria(T criteria, T fuzzy, List<String> fields, String collectionName);

    T getByCriteria(T criteria, T fuzzy);

    /**
     * 根据查询条件，返回所有匹配的记录，但只返回相应的字段
     *
     * @param criteria
     * @param fields
     * @return
     */
    List<T> getAllByCriteria(T criteria, List<String> fields);

    List<T> getAllByCriteria(Criteria criteria, List<String> fields);

    /**
     * 根据查询条件，获取多个匹配的记录
     *
     * @param criteria
     * @return
     */
    List<T> getListByCriteria(T criteria);

    /**
     * 根据查询条件，获取匹配的记录数
     *
     * @param criteria
     * @return
     */
    Long getCountByCriteria(T criteria);

    Long getCountByCriteria(T criteria, T fuzzy);

    Long getCountByCriteria(T criteria, T fuzzy, Criteria startDate, Criteria endDate);

    Long getCountByCriteria(T criteria, T fuzzy, String collectionName);

    /**
     * 获取所有记录
     * 慎用，数据库记录少的时候可以用
     */
    List<T> getAll();

    /**
     * 获取记录总数
     */
    Long getCount();

    Long getCount(String collectionName);

    /**
     * 批量更新
     * 根据更新对象的ID进行更新，只更新不为null的字段
     * 如果有对象不存在id，那么会抛出异常
     *
     * @param objs
     */
    void batchUpdate(List<T> objs);

    void batchUpdate(List<T> objs, String collectionName);

    void batchUpdateByOption(List<BatchUpdateOption> options);

    void batchUpdateByOption(List<BatchUpdateOption> options, String collectionName);

    void batchHardDelete(List<String> ids);

    /**
     * 根据查询条件，分页获取
     * 按照updateTime字段降序排列
     *
     * @param page
     * @param limit
     * @param criteria
     * @param fuzzy    模糊匹配字段
     * @return
     */
    ListOutputDto<T> page(Integer page, Integer limit, T criteria, T fuzzy, Sort sort);

    ListOutputDto<T> page(String collectionName, Integer page, Integer limit, T criteria, T fuzzy, Sort sort);

    ListOutputDto<T> page(String collectionName, Integer page, Integer limit, Criteria criteria, Criteria fuzzy, Sort sort);

    ListOutputDto<T> page(Integer page, Integer limit, Criteria criteria, Criteria fuzzy, Sort sort);

    ListOutputDto<T> page(Integer page, Integer limit, T criteria, T fuzzy);

    ListOutputDto<T> page(Integer page, Integer limit, T criteria);

    ListOutputDto<T> page(Integer page, Integer limit, T criteria, Sort sort);

    List<T> getAllByStart(Integer start, Integer limit, T criteria, Sort sort);

    List<T> getAllByStart(Integer start, Integer limit, T criteria);

    List<T> getAllByStart(Integer start, Integer limit, T criteria, T fuzzy);

    T getOneByStart(Integer start, T criteria);

    /**
     * 分页查询
     *
     * @param page
     * @param limit
     * @return
     */
    ListOutputDto<T> page(Integer page, Integer limit);

    /**
     * 软删除
     * 将delFlag置为1
     *
     * @param id
     */
    void softDelete(String id);

    /**
     * 批量软删除
     *
     * @param ids
     */
    void batchSoftDelete(List<String> ids);

    /**
     * 批量忽略
     *
     * @param ids
     */
    void batchIgnore(List<String> ids);

    /**
     * 批量取消忽略
     *
     * @param ids
     */
    void batchCancelIgnore(List<String> ids);

    /**
     * 物理删除
     *
     * @param id
     */
    void hardDelete(String id);

    /**
     * 先根据id物理删除，然后再保存
     *
     * @param obj
     */
    void deleteSave(T obj);

    /**
     * 删除表
     *
     * @param collectionName
     */
    void dropCollection(String collectionName);

    /**
     * 根据查询条件，获取游标
     *
     * @param criteria
     * @return
     */
    FindIterable getCursor(Criteria criteria);

    FindIterable getCursor(Criteria criteria, String collectionName);

    FindIterable getCursor(Criteria criteria, Integer skip, String collectionName);

    FindIterable getCursor(Criteria criteria, Sort sort);

    /**
     * 获取事件所有日期
     *
     * @return
     */
    Set<String> getApiEventDates();

    /**
     * 设置字段值为null
     *
     * @param filedName
     */
    void setFieldNull(String id, String filedName);

    /**
     * 限定最大总记录数，分页获取记录
     *
     * @param criteria
     * @param fuzzy
     * @param page
     * @param limit
     * @param maxTotalCnt
     * @return
     */
    ListOutputDto pageLimitTotalCnt(T criteria, T fuzzy, Integer page, Integer limit, Sort sort, Integer maxTotalCnt);

    /**
     * 关闭游标
     *
     * @param cursor
     */
//    void closeCursor(DBCursor cursor);

    /**
     * 删除所有记录
     */
    void removeAll();

    void removeByIds(List<String> ids);

    /**
     * 获取索引位置的记录
     *
     * @param criteria
     * @param sort
     * @param index
     * @return
     */
    T findOneByIndex(Criteria criteria, Sort sort, int index);

    /**
     * 根据条件获取记录数
     *
     * @param criteria
     * @return
     */
    long getCount(Criteria criteria);

    // ===============================================================================

    /**
     * 保存
     *
     * @param collectionName 传入对象名与表名一致时，可以不传
     * @param object
     */
    <T> T save(String collectionName, T object);

    /**
     * 更新或插入
     *
     * @param collectionName 传入对象名与表名一致时，可以不传
     * @param criteria
     * @param object
     */
    void upsert(String collectionName, T criteria, T fuzzy, T object);

    /**
     * 删除记录
     *
     * @param collectionName
     * @param ids
     */
    void removeByIds(String collectionName, List<String> ids);

    List<T> getListByIds(String collectionName, List<String> idList);

    T findOneByIndex(String collectionName, T criteria, T fuzzy, Sort sort, int index);

    Long countByCriteria(String collectionName, T criteria, T fuzzy);

    List<T> pageByCriteria(String collectionName, T criteria, T fuzzy, Sort sort, Pageable pageable);

    T findOneByIndex(String collectionName, Criteria where, Sort sort, int index);

    Long countByCriteria(String collectionName, Criteria where);

    List<T> pageByCriteria(String collectionName, Criteria where, Sort sort, Pageable pageable);

    public List<T> getFieldsByCriteria(String collectionName, BasicDBObject fieldsObject, Criteria where, Sort sort, Pageable pageable);

    void delete(String collectionName, T criteria, T fuzzy);

    void delete(String collectionName, Criteria criteria);

    // ====分装查询条件进行查询======================================================================================

    /**
     * 使用MetabaseQuery查找多个
     *
     * @param query 查询条件
     * @return
     */
    List<T> find(MetabaseQuery query, String collectionName);

    long count(MetabaseQuery query, String collectionName);

    ListOutputDto<T> page(MetabaseQuery query, String collectionName);

    Boolean existBy(MetabaseQuery query, String collectionName);

    /**
     * 使用MetabaseQuery查找单个
     *
     * @param query 查询条件
     * @return
     */
    T findOne(MetabaseQuery query, String collectionName);

    /**
     * 通过ID进行更新
     *
     * @param id
     * @param updates
     */
    void update(String id, ResourceUpdates updates, String collectionName);

    /**
     * 使用query进行更新
     *
     * @param query
     * @param updates
     * @param upsert  如果查询不到，会进行插入操作
     */
    void update(MetabaseQuery query, ResourceUpdates updates, boolean upsert, String collectionName);

    void delete(MetabaseQuery query, String collectionName);

    /**
     * 分组查询
     *
     * @param
     * @return
     * @Comments: <对此方法的描述，可以引用系统设计中的描述>
     * <AUTHOR> [<EMAIL>]
     * @since 2021-08-10 9:56
     */
    List<AggregationDto> group(Criteria criteria, GroupDto groupDto, String field, Integer sort, Integer page, Integer limit) throws Exception;


    List<AggregationDto> group(Criteria criteria, GroupDto groupDto, String collection,String field, Integer sort, Integer page, Integer limit) throws Exception;



 List<AggregationListDto> groupList(Criteria criteria, GroupDto groupDto, String field, Integer sort, Integer page, Integer limit) throws Exception;

    <T> List<T> findDistinctByField(String field, String collection, Class<T> resultClass);
}
