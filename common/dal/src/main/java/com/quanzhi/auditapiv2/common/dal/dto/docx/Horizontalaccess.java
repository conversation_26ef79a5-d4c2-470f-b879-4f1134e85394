package com.quanzhi.auditapiv2.common.dal.dto.docx;

import lombok.Data;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 *
 * 《接口参数可遍历》
 *
 *
 * @Project:
 * @Module ID:
 * @Comments:
 * @JDK version used:      <JDK1.8>
 * <AUTHOR> [<EMAIL>]
 * @since 2020-08-28-下午16:52:18
 */
@Data
public class Horizontalaccess extends ApiWeaknessExportWord {

    @Mapper
    public interface HorizontalaccessMapper {

        Horizontalaccess.HorizontalaccessMapper INSTANCE = Mappers.getMapper(Horizontalaccess.HorizontalaccessMapper.class);
        Horizontalaccess convert(ApiWeaknessExportWord apiWeaknessExportWord);
    }
}
