package com.quanzhi.auditapiv2.common.dal.dto.sample;

import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * create at 2024/2/1 14:29
 * @description: 样例查询dto
 **/
@Data
public class SampleSearchDto {


    private String sampleId;

    private String uri;

    private String apiUrl;
    /**
     * lable | weakness
     */
    private String type;
    /**
     * 是否脱敏
     */
    private Boolean desensitize;

    private List<String> reqDataLabels;

    private List<String> rspDataLabels;

    private Integer compositeType;

    private Integer page;
    private Integer limit;
    private List<String> fields;

}