package com.quanzhi.auditapiv2.common.dal.dto.task;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 描述: 任务运行信息DTO
 *
 * @author: danniel_yu
 * @date: 2020/11/23 14:43
 */
@Data
public class FlinkTaskInfo {
    private static final long serialVersionUID = -1573733410446289380L;

    /**
     * 任务ID
     */
    @ApiModelProperty(value = "任务ID", name = "jobId")
    private String jobId;

    /**
     * 任务名称
     */
    @ApiModelProperty(value = "任务名称", name = "jobName")
    private String jobName;

    /**
     * 任务开始时间
     */
    @ApiModelProperty(value = "任务开始时间", name = "startTime")
    private Long startTime;

    /**
     * 任务最近更新时间
     */
    @ApiModelProperty(value = "任务最近更新时间", name = "lastUpdateTime")
    private Long lastUpdateTime;

    /**
     * 任务结束时间
     */
    @ApiModelProperty(value = "任务结束时间", name = "endTime")
    private Long endTime;

    /**
     * 数据库类型
     */
    @ApiModelProperty(value = "任务状态", name = "status")
    private String status;

}
