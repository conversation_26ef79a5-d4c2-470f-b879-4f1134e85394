package com.quanzhi.auditapiv2.common.dal.dao.api;

import com.quanzhi.auditapiv2.common.dal.dao.ApiStructDao;
import com.quanzhi.auditapiv2.common.dal.dao.base.impl.MetabaseDaoImpl;
import com.quanzhi.metabase.core.model.http.api.ApiStruct;
import org.springframework.stereotype.Repository;

/**
 * <AUTHOR>
 * @date 2022/1/6 5:18 下午
 */
@Repository
public class ApiStructDaoImpl extends MetabaseDaoImpl<ApiStruct> implements ApiStructDao {
    @Override
    public ApiStruct getApiStruct(String uri) {
        return metabaseClientTemplate.findByUri(uri, ApiStruct.class);
    }
}
