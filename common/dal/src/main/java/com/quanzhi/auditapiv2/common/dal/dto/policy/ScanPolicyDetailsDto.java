package com.quanzhi.auditapiv2.common.dal.dto.policy;

import com.alibaba.fastjson.JSONObject;
import com.quanzhi.auditapiv2.common.dal.entity.policy.ScanPolicyConfig;
import com.quanzhi.auditapiv2.common.dal.entity.policy.ScanPolicyModule;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * @Author: Linlm
 * @Description:
 * @Date: Created in 2020/6/2 下午4:18
 */
@Data
public class ScanPolicyDetailsDto implements Serializable {
    private static final long serialVersionUID = -2670863477388995380L;

    /**
     * 策略code: 唯一编号
     */
    private String code;

    /**
     * 策略名称
     */
    private String name;

    /**
     * 策略描述：检查内容
     */
    private String description;

    /**
     * 策略版本
     */
    private String version;

    /**
     * 策略模块
     */
    private List<ScanPolicyModule> modules;

    /**
     * 专用工具、基础工具
     */
    private String type;

    /**
     * 运行配置：json串
     */
    private JSONObject params;

    /**
     * 工具配置
     */
    private ScanPolicyConfig policyConfig;
}
