package com.quanzhi.auditapiv2.common.dal.entity.riskEolink;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.quanzhi.metabase.core.model.http.HttpApiResource;
import lombok.Data;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 同步API网关数据
 */
@Data
public class SyncGatewayDto {
    private String uri;
    private String id;
    @JsonProperty("api_url")
    private String apiUrl;
    @JsonProperty("flow_sources")
    private List<String> flowSources;
    private List<String> methods;
    @JsonProperty("update_time")
    private String updateTime;

    private String gatewayApiId;

    public static SyncGatewayDto convertToDto(HttpApiResource httpApiResource) {
        SyncGatewayDto dto = new SyncGatewayDto();
        dto.setUri(httpApiResource.getUri()); // 将 apiUri 映射到 uri
        dto.setId(httpApiResource.getId());
        dto.setApiUrl(httpApiResource.getApiUrl());
        dto.setFlowSources(httpApiResource.getFlowSources());
        dto.setMethods(httpApiResource.getMethods());
        dto.setUpdateTime(String.valueOf(httpApiResource.getUpdateTime()));
        return dto;
    }
}
