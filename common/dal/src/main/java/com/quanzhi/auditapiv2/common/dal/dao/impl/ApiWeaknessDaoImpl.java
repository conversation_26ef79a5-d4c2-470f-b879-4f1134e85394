package com.quanzhi.auditapiv2.common.dal.dao.impl;

import com.quanzhi.auditapiv2.common.dal.dao.IApiWeaknessDao;
import com.quanzhi.auditapiv2.common.dal.dao.IDataLabelNacosDao;
import com.quanzhi.auditapiv2.common.dal.dao.base.impl.MetabaseDaoImpl;
import com.quanzhi.auditapiv2.common.dal.dao.impl.common.CustomPropertySearchAdapter;
import com.quanzhi.auditapiv2.common.dal.dao.impl.common.Module;
import com.quanzhi.auditapiv2.common.dal.dao.impl.common.QueryAdapterRegistry;
import com.quanzhi.auditapiv2.common.dal.dto.ApiWeaknessDto;
import com.quanzhi.auditapiv2.common.dal.dto.RiskLevelMatchDto;
import com.quanzhi.auditapiv2.common.dal.dto.common.CommonGroupDto;
import com.quanzhi.auditapiv2.common.dal.entity.weakness.WeaknessDistribute;
import com.quanzhi.auditapiv2.common.dal.enums.DBOperatorEnum;
import com.quanzhi.auditapiv2.common.dal.enums.WeaknessLevelEnum;
import com.quanzhi.auditapiv2.common.dal.enums.WeaknessType;
import com.quanzhi.auditapiv2.common.util.dto.AggregationDto;
import com.quanzhi.auditapiv2.common.util.dto.GroupDto;
import com.quanzhi.auditapiv2.common.util.utils.ConstantUtil;
import com.quanzhi.auditapiv2.common.util.utils.DataUtil;
import com.quanzhi.metabase.core.model.http.HttpResourceConstant;
import com.quanzhi.metabase.core.model.http.report.HttpWeaknessDateTotalStat;
import com.quanzhi.metabase.core.model.http.weakness.ApiWeakness;
import com.quanzhi.metabase.core.model.http.weakness.State;
import com.quanzhi.metabase.core.model.query.*;
import com.quanzhi.metabase.service.dto.JoinDto;
import org.bson.Document;
import org.bson.types.ObjectId;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.aggregation.Aggregation;
import org.springframework.data.mongodb.core.aggregation.AggregationOperation;
import org.springframework.data.mongodb.core.aggregation.AggregationResults;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Repository;

import java.lang.reflect.Field;
import java.util.*;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import static org.springframework.data.mongodb.core.aggregation.Aggregation.newAggregationOptions;

/**
 * 《接口弱点持久层接口实现》
 *
 * <AUTHOR> [<EMAIL>]
 * @Project:
 * @Module ID:
 * @Comments:
 * @JDK version used:      <JDK1.8>
 * @since 2020-08-28-下午16:52:18
 */
@Repository
public class ApiWeaknessDaoImpl extends MetabaseDaoImpl<ApiWeakness> implements IApiWeaknessDao {

    @Autowired
    private MongoTemplate mongoTemplate;

    @Autowired
    private CustomPropertySearchAdapter customPropertySearchAdapter;

    @Autowired
    private QueryAdapterRegistry queryAdapterRegistry;

    private final IDataLabelNacosDao dataLabelNacosDao;

    private String collectionName = "httpApiWeakness";

    private String tableName = "httpApi";

    private String appTable = "httpApp";

    private String as = "apiList";

    public ApiWeaknessDaoImpl(IDataLabelNacosDao dataLabelNacosDao) {
        this.dataLabelNacosDao = dataLabelNacosDao;
    }

    @Override
    public List<ApiWeakness> selectNewestWeaknessByUpdateTime(String updateTime) {
//        return mongoTemplate.find(new Query().addCriteria(
//                        org.springframework.data.mongodb.core.query.Criteria.where("updateTime").gte(Long.parseLong(updateTime)).and("delFlag").is(false)).limit(1000)
//                .with(org.springframework.data.domain.Sort.by(org.springframework.data.domain.Sort.Order.desc("updateTime"))), ApiWeakness.class, collectionName);
        return mongoTemplate.find(new Query().addCriteria(
                org.springframework.data.mongodb.core.query.Criteria.where("updateTime").gte(Long.parseLong(updateTime))), ApiWeakness.class, collectionName);
    }

    @Override
    public Boolean checkApiWeaknessStatus(String id) {
        List<ApiWeakness> apiWeaknesses = mongoTemplate.find(new Query().addCriteria(
                org.springframework.data.mongodb.core.query.Criteria.where("_id").is(new ObjectId(id))
        ), ApiWeakness.class, collectionName);
        return DataUtil.isNotEmpty(apiWeaknesses);
    }

    @Override
    public List<CommonGroupDto> selectWeaknessIdGroup() throws Exception {
        Aggregation aggregation = Aggregation.newAggregation(
                Aggregation.match(org.springframework.data.mongodb.core.query.Criteria
                        .where("state").in(State.NEW, State.REPAIRING, State.REOPEN, State.FIXED)
                        .and("delFlag").is(false)),
                Aggregation.unwind("weaknessId"),
                Aggregation.group("weaknessId", "name", "type")
                        .first("weaknessId").as("otherId")
                        .first("type").as("group")
                        .first("name").as("name")
                        .count().as("count")
        );
        return mongoTemplate.aggregate(aggregation, collectionName, CommonGroupDto.class).getMappedResults();
    }

    @Override
    public List<CommonGroupDto> groupWeakness(String field) throws Exception {
        Aggregation aggregation = Aggregation.newAggregation(
                Aggregation.match(org.springframework.data.mongodb.core.query.Criteria
                        .where("state").in(State.NEW, State.REPAIRING, State.REOPEN, State.FIXED)
                        .and("delFlag").is(false)),
                Aggregation.unwind(field),
                Aggregation.group(field)
                        .first("weaknessId").as("otherId")
                        .first(field).as("group")
        );
        return mongoTemplate.aggregate(aggregation, collectionName, CommonGroupDto.class).getMappedResults();
    }

    @Override
    public Map<String, List<String>> getWeaknessIdsByWeaknessType() {
        List<String> other = mongoTemplate.findDistinct(new Query().addCriteria(org.springframework.data.mongodb.core.query.Criteria
                .where("weaknessType").is(WeaknessType.OTHER.name())
        ), "weaknessId", collectionName, String.class);
        List<String> top10 = mongoTemplate.findDistinct(new Query().addCriteria(org.springframework.data.mongodb.core.query.Criteria
                .where("weaknessType").is(WeaknessType.OWASP_API_SECURITY_TOP10.name())
        ), "weaknessId", collectionName, String.class);
        Map<String, List<String>> result = new HashMap<>();
        result.put(WeaknessType.OTHER.name(), other);
        result.put(WeaknessType.OWASP_API_SECURITY_TOP10.name(), top10);
        return result;
    }

    /**
     * 查询接口弱点列表(分页)
     *
     * @param
     * @param showFields
     * @return
     * @Comments: <对此方法的描述，可以引用系统设计中的描述>
     * <AUTHOR> [<EMAIL>]
     * @since 2020-08-28 16:52
     */
    @Override
    public List<ApiWeakness> selectApiWeaknessList(Map<String, Object> map, String field, Integer sort, Integer page, Integer limit, String category, List<String> showFields) throws Exception {
        MetabaseQuery metabaseQuery = getMetabaseQuery(map);
        if (DataUtil.isNotEmpty(category)) {
            metabaseQuery.where("type", Predicate.IS, category);
        }
        //分页
        metabaseQuery.skip((page - 1) * limit);
        metabaseQuery.limit(limit);
        //排序
        if (DataUtil.isNotEmpty(field) && DataUtil.isNotEmpty(sort)) {

            if (sort == ConstantUtil.Sort.ASC) {
                metabaseQuery.sort(Sort.by(field, SortOrder.ASC), Sort.by("_id", SortOrder.ASC));
            } else if (sort == ConstantUtil.Sort.DESC) {
                metabaseQuery.sort(Sort.by(field, SortOrder.DESC), Sort.by("_id", SortOrder.DESC));
            } else {
                metabaseQuery.sort(Sort.by("earlyTimestamp", SortOrder.DESC), Sort.by("_id", SortOrder.DESC));
            }
        } else {
            metabaseQuery.sort(Sort.by("earlyTimestamp", SortOrder.DESC), Sort.by("_id", SortOrder.DESC));
        }
        if (DataUtil.isNotEmpty(showFields)) {
            metabaseQuery.fields(showFields);
        }
        return metabaseClientTemplate.find(metabaseQuery, ApiWeakness.class);
    }

    @Override
    public List<ApiWeakness> selectApiWeaknessListNoSort(Map<String, Object> map, Integer page, Integer limit) throws Exception {
        MetabaseQuery metabaseQuery = getMetabaseQuery(map);
        //分页
        metabaseQuery.skip((page - 1) * limit);
        metabaseQuery.limit(limit);
        queryAdapterRegistry.module(Module.WEAKNESS).query(map, metabaseQuery);
        return metabaseClientTemplate.find(metabaseQuery, ApiWeakness.class);
    }

    /**
     * 查询接口弱点列表(分页 多类型)
     *
     * @param
     * @param showFields
     * @return
     * @Comments: <对此方法的描述，可以引用系统设计中的描述>
     * <AUTHOR> [<EMAIL>]
     * @since 2020-08-28 16:52
     */
    @Override
    public List<ApiWeakness> selectApiWeaknessListWithMoreType(Map<String, Object> map, String field, Integer sort, Integer page, Integer limit, List<String> categorys, List<String> showFields) throws Exception {
        MetabaseQuery metabaseQuery = getMetabaseQuery(map);

        if (DataUtil.isNotEmpty(categorys)) {
            metabaseQuery.where("type", Predicate.IN, categorys);
        }

        //分页
        metabaseQuery.skip((page - 1) * limit);
        metabaseQuery.limit(limit);
        //排序
        if (DataUtil.isNotEmpty(field) && DataUtil.isNotEmpty(sort)) {

            if (sort == ConstantUtil.Sort.ASC) {
                metabaseQuery.sort(Sort.by(field, SortOrder.ASC), Sort.by("_id", SortOrder.ASC));
            } else if (sort == ConstantUtil.Sort.DESC) {
                metabaseQuery.sort(Sort.by(field, SortOrder.DESC), Sort.by("_id", SortOrder.DESC));
            } else {
                metabaseQuery.sort(Sort.by("earlyTimestamp", SortOrder.DESC), Sort.by("_id", SortOrder.DESC));
            }
        } else {
            metabaseQuery.sort(Sort.by("earlyTimestamp", SortOrder.DESC), Sort.by("_id", SortOrder.DESC));
        }
        if (DataUtil.isNotEmpty(showFields)) {
            metabaseQuery.fields(showFields);
        }
        queryAdapterRegistry.module(Module.WEAKNESS).query(map, metabaseQuery);
        return metabaseClientTemplate.find(metabaseQuery, ApiWeakness.class);
    }


    /**
     * 新发现弱点
     *
     * @param startTime
     * @param endTime
     * @param page
     * @param limit
     * @param field
     * @param sort
     * @return
     */
    @Override
    public List<ApiWeaknessDto> getNewWeaknessList(Long startTime, Long endTime, Integer page, Integer limit, String field, Integer sort) {
        MetabaseQuery metabaseQuery = new MetabaseQuery();

        //关联表
        List<JoinTable> joinTableList = new ArrayList<JoinTable>() {
            {
                JoinTable joinTable = new JoinTable();
                joinTable.setFrom(tableName);
                joinTable.setLocalField("uri");
                joinTable.setForeignField("uri");
                joinTable.setAs(as);
                add(joinTable);
            }
        };
        //查询条件
        metabaseQuery.where("delFlag", Predicate.IS, false);
        if (DataUtil.isNotEmpty(startTime) && DataUtil.isNotEmpty(endTime)) {

            Criteria criteria = new Criteria();
            criteria = criteria.andOperator(
                    Criteria.where("earlyTimestamp").gte(startTime),
                    Criteria.where("earlyTimestamp").lte(endTime));
            metabaseQuery.getCriteria().add(criteria);
        }

        //指定返回字段
        metabaseQuery.setFields(new ArrayList<String>() {
            {
                add("id");
                add("appUri");
                add("uri");
                add("earlyTimestamp");
                add("name");
                add("level");
                add("state");
                add("stateName");
                add("oldState");
                add("apiList");
                add("delFlag");
            }
        });

        //分页
        metabaseQuery.skip((page - 1) * limit);
        metabaseQuery.limit(limit);
        //排序
        if (DataUtil.isNotEmpty(field) && DataUtil.isNotEmpty(sort)) {

            if (sort == ConstantUtil.Sort.ASC) {
                metabaseQuery.sort(Sort.by(field, SortOrder.ASC), Sort.by("_id", SortOrder.ASC));
            } else if (sort == ConstantUtil.Sort.DESC) {
                metabaseQuery.sort(Sort.by(field, SortOrder.DESC), Sort.by("_id", SortOrder.DESC));
            } else {
                metabaseQuery.sort(Sort.by("earlyTimestamp", SortOrder.DESC), Sort.by("_id", SortOrder.DESC));
            }
        } else {
            metabaseQuery.sort(Sort.by("earlyTimestamp", SortOrder.DESC), Sort.by("_id", SortOrder.DESC));
        }

        JoinDto joinDto = new JoinDto();
        joinDto.setJoinTableList(joinTableList);
        joinDto.setMetabaseQuery(metabaseQuery);
        joinDto.setCollection(collectionName);

        return metabaseClientTemplate.join(joinDto, ApiWeaknessDto.class);
    }


    /**
     * 已修复弱点
     *
     * @param startTime
     * @param endTime
     * @param page
     * @param limit
     * @param field
     * @param sort
     * @return
     */
    @Override
    public List<ApiWeaknessDto> getFixedWeaknessList(Long startTime, Long endTime, Integer page, Integer limit, String field, Integer sort) {
        MetabaseQuery metabaseQuery = new MetabaseQuery();

        //关联表
        List<JoinTable> joinTableList = new ArrayList<JoinTable>() {
            {
                JoinTable joinTable = new JoinTable();
                joinTable.setFrom(tableName);
                joinTable.setLocalField("uri");
                joinTable.setForeignField("uri");
                joinTable.setAs(as);
                add(joinTable);
            }
        };
        //查询条件
        metabaseQuery.where("delFlag", Predicate.IS, false);
        metabaseQuery.where("state", Predicate.IS, State.FIXED);
        if (DataUtil.isNotEmpty(startTime) && DataUtil.isNotEmpty(endTime)) {

            Criteria criteria = new Criteria();
            criteria = criteria.andOperator(
                    Criteria.where("fixedTimestamp").gte(startTime),
                    Criteria.where("fixedTimestamp").lte(endTime));
            metabaseQuery.getCriteria().add(criteria);
        }

        //指定返回字段
        metabaseQuery.setFields(new ArrayList<String>() {
            {
                add("id");
                add("appUri");
                add("uri");
                add("earlyTimestamp");
                add("name");
                add("level");
                add("state");
                add("stateName");
                add("oldState");
                add("apiList");
                add("delFlag");
            }
        });

        //分页
        metabaseQuery.skip((page - 1) * limit);
        metabaseQuery.limit(limit);
        //排序
        if (DataUtil.isNotEmpty(field) && DataUtil.isNotEmpty(sort)) {

            if (sort == ConstantUtil.Sort.ASC) {
                metabaseQuery.sort(Sort.by(field, SortOrder.ASC), Sort.by("_id", SortOrder.ASC));
            } else if (sort == ConstantUtil.Sort.DESC) {
                metabaseQuery.sort(Sort.by(field, SortOrder.DESC), Sort.by("_id", SortOrder.DESC));
            } else {
                metabaseQuery.sort(Sort.by("earlyTimestamp", SortOrder.DESC), Sort.by("_id", SortOrder.DESC));
            }
        } else {
            metabaseQuery.sort(Sort.by("earlyTimestamp", SortOrder.DESC), Sort.by("_id", SortOrder.DESC));
        }

        JoinDto joinDto = new JoinDto();
        joinDto.setJoinTableList(joinTableList);
        joinDto.setMetabaseQuery(metabaseQuery);
        joinDto.setCollection(collectionName);

        return metabaseClientTemplate.join(joinDto, ApiWeaknessDto.class);
    }


    /**
     * 待修复弱点
     *
     * @param startTime
     * @param endTime
     * @param page
     * @param limit
     * @param field
     * @param sort
     * @return
     */
    @Override
    public List<ApiWeaknessDto> getPepairingList(Long startTime, Long endTime, Integer page, Integer limit, String field, Integer sort) {
        MetabaseQuery metabaseQuery = new MetabaseQuery();

        //关联表
        List<JoinTable> joinTableList = new ArrayList<JoinTable>() {
            {
                JoinTable joinTable = new JoinTable();
                joinTable.setFrom(tableName);
                joinTable.setLocalField("uri");
                joinTable.setForeignField("uri");
                joinTable.setAs(as);
                add(joinTable);
            }
        };
        //查询条件
        metabaseQuery.where("delFlag", Predicate.IS, false);
        metabaseQuery.where("state", Predicate.IS, State.REPAIRING);
        if (DataUtil.isNotEmpty(startTime) && DataUtil.isNotEmpty(endTime)) {

            Criteria criteria = new Criteria();
            criteria = criteria.andOperator(
                    Criteria.where("earlyTimestamp").gte(startTime),
                    Criteria.where("earlyTimestamp").lte(endTime));
            metabaseQuery.getCriteria().add(criteria);
        }

        //指定返回字段
        metabaseQuery.setFields(new ArrayList<String>() {
            {
                add("id");
                add("appUri");
                add("uri");
                add("earlyTimestamp");
                add("name");
                add("level");
                add("state");
                add("stateName");
                add("oldState");
                add("apiList");
                add("delFlag");
            }
        });

        //分页
        metabaseQuery.skip((page - 1) * limit);
        metabaseQuery.limit(limit);
        //排序
        if (DataUtil.isNotEmpty(field) && DataUtil.isNotEmpty(sort)) {

            if (sort == ConstantUtil.Sort.ASC) {
                metabaseQuery.sort(Sort.by(field, SortOrder.ASC), Sort.by("_id", SortOrder.ASC));
            } else if (sort == ConstantUtil.Sort.DESC) {
                metabaseQuery.sort(Sort.by(field, SortOrder.DESC), Sort.by("_id", SortOrder.DESC));
            } else {
                metabaseQuery.sort(Sort.by("earlyTimestamp", SortOrder.DESC), Sort.by("_id", SortOrder.DESC));
            }
        } else {
            metabaseQuery.sort(Sort.by("earlyTimestamp", SortOrder.DESC), Sort.by("_id", SortOrder.DESC));
        }

        JoinDto joinDto = new JoinDto();
        joinDto.setJoinTableList(joinTableList);
        joinDto.setMetabaseQuery(metabaseQuery);
        joinDto.setCollection(collectionName);

        return metabaseClientTemplate.join(joinDto, ApiWeaknessDto.class);
    }

    /**
     * 查询接口弱点列表(分页)
     *
     * @param
     * @return
     * @Comments: <对此方法的描述，可以引用系统设计中的描述>
     * <AUTHOR> [<EMAIL>]
     * @since 2020-08-28 16:52
     */
    @Override
    public List<ApiWeakness> selectActiveApiWeaknessList(Integer page, Integer limit) throws Exception {

        List<State> list = new ArrayList();
        list.add(State.NEW);
        list.add(State.SUSPEND);
        list.add(State.REPAIRING);
        list.add(State.FIXED);

        MetabaseQuery metabaseQuery = new MetabaseQuery();
        metabaseQuery.where("state", Predicate.IN, list);
        metabaseQuery.where("delFlag", Predicate.IS, false);
        //分页
        metabaseQuery.skip((page - 1) * limit);
        metabaseQuery.limit(limit);
        //排序
        metabaseQuery.sort(Sort.by("earlyTimestamp", SortOrder.DESC), Sort.by("_id", SortOrder.DESC));

        return metabaseClientTemplate.find(metabaseQuery, ApiWeakness.class);
    }

    @Override
    public Long getCount(Long startTime, Long endTime, Long startDate, Long endDate, State state) {
        MetabaseQuery metabaseQuery = new MetabaseQuery();
        metabaseQuery.where("delFlag", Predicate.IS, false);
        if (DataUtil.isNotEmpty(state)) {
            metabaseQuery.where("state", Predicate.IS, state);
        }
        if (DataUtil.isNotEmpty(startTime) && DataUtil.isNotEmpty(endTime)) {

            Criteria criteria = new Criteria();
            criteria = criteria.andOperator(
                    Criteria.where("earlyTimestamp").gte(startTime),
                    Criteria.where("earlyTimestamp").lte(endTime));
            metabaseQuery.getCriteria().add(criteria);
        }
        if (DataUtil.isNotEmpty(startDate) && DataUtil.isNotEmpty(endDate)) {

            Criteria criteria = new Criteria();
            criteria = criteria.andOperator(
                    Criteria.where("fixedTimestamp").gte(startDate),
                    Criteria.where("fixedTimestamp").lte(endDate));
            metabaseQuery.getCriteria().add(criteria);
        }
        return metabaseClientTemplate.count(metabaseQuery, ApiWeakness.class);
    }

    @Override
    public long countByLevelArray(List<Integer> weaknessLevel) {
        return mongoTemplate.count(new Query().addCriteria(org.springframework.data.mongodb.core.query.Criteria
                .where("state").is("NEW")
                .and("delFlag").is(false)
                .and("level").in(weaknessLevel)), collectionName);
    }

    /**
     * 查询接口弱点列表
     *
     * @param
     * @return
     * @Comments: <对此方法的描述，可以引用系统设计中的描述>
     * <AUTHOR> [<EMAIL>]
     * @since 2020-08-28 16:52
     */
    @Override
    public List<ApiWeakness> selectApiWeaknessList(Map<String, Object> map) throws Exception {

        MetabaseQuery metabaseQuery = new MetabaseQuery();

        //查询条件
        metabaseQuery.where("delFlag", Predicate.IS, false);
        if (DataUtil.isNotEmpty(map)) {
            for (String key : map.keySet()) {

                Object value = map.get(key);
                if (DataUtil.isNotEmpty(value)) {

                    if ("weaknessId".equals(key)) {
                        metabaseQuery.where(key, Predicate.IS, value);
                    } else if ("host".equals(key)) {
                        metabaseQuery.where(key, Predicate.IS, value);
                    } else if ("appUri".equals(key)) {
                        metabaseQuery.where(key, Predicate.IS, value);
                    } else if ("uri".equals(key)) {
                        metabaseQuery.where(key, Predicate.IS, value);
                    } else if ("apiUrl".equals(key)) {
                        metabaseQuery.where(as + "." + key, Predicate.IS, value);
                    } else if ("name".equals(key)) {
                        metabaseQuery.where(key, Predicate.IS, value);
                    } else if ("state".equals(key)) {
                        metabaseQuery.where(key, Predicate.IS, value);
                    } else if ("id".equals(key)) {
                        metabaseQuery.where("_id", Predicate.IS, value);
                    }
                }
            }
            if (DataUtil.isNotEmpty(map.get("startDate")) && DataUtil.isNotEmpty(map.get("endDate"))) {

                Criteria criteria = new Criteria();
                criteria = criteria.andOperator(
                        Criteria.where("earlyTimestamp").gte(map.get("startDate")),
                        Criteria.where("earlyTimestamp").lte(map.get("endDate")));
                metabaseQuery.getCriteria().add(criteria);
            }
        }
        return metabaseClientTemplate.find(metabaseQuery, ApiWeakness.class);
    }

    @Override
    public List<CommonGroupDto> groupByMap(Map<String, Object> map) {
        String field = new ArrayList<>(map.keySet()).get(0);
        String value = (String) map.get(field);
        Aggregation aggregation = Aggregation.newAggregation(
                Aggregation.match(org.springframework.data.mongodb.core.query.Criteria.where(field).is(value)),
                Aggregation.unwind(field),
                Aggregation.group(field)
                        .first("weaknessId").as("otherId")
                        .first("type").as("group")
                        .first("name").as("name")
        );
        return mongoTemplate.aggregate(aggregation, collectionName, CommonGroupDto.class).getMappedResults();
    }

    /**
     * 查询忽略接口弱点列表
     *
     * @param
     * @return
     * @Comments: <对此方法的描述，可以引用系统设计中的描述>
     * <AUTHOR> [<EMAIL>]
     * @since 2020-08-28 16:52
     */
    @Override
    public List<ApiWeakness> selectIgnoreApiWeaknessList(Map<String, Object> map) throws Exception {

        MetabaseQuery metabaseQuery = new MetabaseQuery();

        //查询条件
        metabaseQuery.where("delFlag", Predicate.IS, true);
        if (DataUtil.isNotEmpty(map)) {
            for (String key : map.keySet()) {

                Object value = map.get(key);
                if (DataUtil.isNotEmpty(value)) {

                    if ("weaknessId".equals(key)) {
                        metabaseQuery.where(key, Predicate.IS, value);
                    } else if ("host".equals(key)) {
                        metabaseQuery.where(key, Predicate.IS, value);
                    } else if ("appUri".equals(key)) {
                        metabaseQuery.where(key, Predicate.IS, value);
                    } else if ("uri".equals(key)) {
                        metabaseQuery.where(key, Predicate.IS, value);
                    } else if ("apiUrl".equals(key)) {
                        metabaseQuery.where(as + "." + key, Predicate.IS, value);
                    } else if ("name".equals(key)) {
                        metabaseQuery.where(key, Predicate.IS, value);
                    } else if ("state".equals(key)) {
                        metabaseQuery.where(key, Predicate.IS, value);
                    }
                }
            }
            if (DataUtil.isNotEmpty(map.get("startDate")) && DataUtil.isNotEmpty(map.get("endDate"))) {

                Criteria criteria = new Criteria();
                criteria = criteria.andOperator(
                        Criteria.where("earlyTimestamp").gte(map.get("startDate")),
                        Criteria.where("earlyTimestamp").lte(map.get("endDate")));
                metabaseQuery.getCriteria().add(criteria);
            }
        }
        return metabaseClientTemplate.find(metabaseQuery, ApiWeakness.class);
    }

    /**
     * 查询接口弱点数量
     *
     * @param
     * @return
     * @Comments: <对此方法的描述，可以引用系统设计中的描述>
     * <AUTHOR> [<EMAIL>]
     * @since 2020-08-28 16:52
     */
    @Override
    public Long totalCount(Map<String, Object> map, List<String> category) throws Exception {

        MetabaseQuery metabaseQuery = getMetabaseQuery(map);

        if (DataUtil.isNotEmpty(category)) {
            if (category.size() == 1) {
                metabaseQuery.where("type", Predicate.IS, category.get(0));
            } else {
                metabaseQuery.where("type", Predicate.IN, category);
            }
        }
        queryAdapterRegistry.module(Module.WEAKNESS).query(map, metabaseQuery);
        return metabaseClientTemplate.count(metabaseQuery, ApiWeakness.class);
    }

    /**
     * 发现时间查询接口弱点数量
     *
     * @param
     * @return
     * @Comments: <对此方法的描述，可以引用系统设计中的描述>
     * <AUTHOR> [<EMAIL>]
     * @since 2020-08-28 16:52
     */
    @Override
    public Long totalCountByEarlyTimestamp(Long startDate, Long endDate) throws Exception {

        MetabaseQuery metabaseQuery = new MetabaseQuery();

        //查询条件
        metabaseQuery.where("delFlag", Predicate.IS, false);
        if (DataUtil.isNotEmpty(startDate) && DataUtil.isNotEmpty(endDate)) {

            Criteria criteria = new Criteria();
            criteria = criteria.andOperator(
                    Criteria.where("earlyTimestamp").gte(startDate),
                    Criteria.where("earlyTimestamp").lte(endDate));
            metabaseQuery.getCriteria().add(criteria);
        }

        return metabaseClientTemplate.count(metabaseQuery, ApiWeakness.class);
    }

    /**
     * id查询接口弱点详情
     *
     * @param
     * @return
     * @Comments: <对此方法的描述，可以引用系统设计中的描述>
     * <AUTHOR> [<EMAIL>]
     * @since 2020-08-28 16:52
     */
    @Override
    public ApiWeakness selectApiWeaknessById(String id) throws Exception {

        return metabaseClientTemplate.findOne(id, ApiWeakness.class);
    }

    @Override
    public List<ApiWeakness> selectApiWeaknessByIds(List<String> ids, Integer sort, String field) throws Exception {

        MetabaseQuery metabaseQuery = new MetabaseQuery();
        metabaseQuery.where("_id", Predicate.IN, ids);

        //排序
        if (DataUtil.isNotEmpty(field) && DataUtil.isNotEmpty(sort)) {

            if (sort == ConstantUtil.Sort.ASC) {
                metabaseQuery.sort(Sort.by(field, SortOrder.ASC), Sort.by("_id", SortOrder.ASC));
            } else if (sort == ConstantUtil.Sort.DESC) {
                metabaseQuery.sort(Sort.by(field, SortOrder.DESC), Sort.by("_id", SortOrder.DESC));
            } else {
                metabaseQuery.sort(Sort.by("earlyTimestamp", SortOrder.DESC), Sort.by("_id", SortOrder.DESC));
            }
        } else {
            metabaseQuery.sort(Sort.by("earlyTimestamp", SortOrder.DESC), Sort.by("_id", SortOrder.DESC));
        }

        return metabaseClientTemplate.find(metabaseQuery, ApiWeakness.class);
    }

    @Override
    public ApiWeakness getApiWeaknessByOperationId(String operationId) throws Exception {
        MetabaseQuery metabaseQuery = new MetabaseQuery();
        metabaseQuery.where("operationId", Predicate.IS, operationId);
        return metabaseClientTemplate.findOne(metabaseQuery, ApiWeakness.class);
    }

    /**
     * 查询去重后的域名
     *
     * @param
     * @return
     * @Comments: <对此方法的描述，可以引用系统设计中的描述>
     * <AUTHOR> [<EMAIL>]
     * @since 2020-08-28 16:52
     */
    @Override
    public List<AggregationResult> selectHostList(String host, Integer page, Integer limit) throws Exception {

        MetabaseQuery metabaseQuery = new MetabaseQuery();
        metabaseQuery.where("delFlag", Predicate.IS, false);
        if (DataUtil.isNotEmpty(host)) {
            metabaseQuery.where("host", Predicate.REGEX, host);
        }
        //分页
        metabaseQuery.skip((page - 1) * limit);
        metabaseQuery.limit(limit);
        //排序
        metabaseQuery.sort(Sort.by("resultAlias", SortOrder.DESC), Sort.by("_id", SortOrder.DESC));

        MetabaseGroupOperation metabaseGroupOperation = new MetabaseGroupOperation();
        String[] groupFields = {"host"};
        metabaseGroupOperation.setGroupFields(groupFields);
        metabaseGroupOperation.setAggrType(AggrationType.COUNT);

        return metabaseClientTemplate.aggregate(metabaseQuery, metabaseGroupOperation, ApiWeakness.class);
    }

    /**
     * host查询接口弱点数量
     *
     * @param
     * @return
     * @Comments: <对此方法的描述，可以引用系统设计中的描述>
     * <AUTHOR> [<EMAIL>]
     * @since 2020-08-28 16:52
     */
    @Override
    public Long totalCountByHost(String host) throws Exception {

        MetabaseQuery metabaseQuery = new MetabaseQuery();

        //查询条件
        metabaseQuery.where("delFlag", Predicate.IS, false);
        if (DataUtil.isNotEmpty(host)) {
            metabaseQuery.where("host", Predicate.IS, host);
        }

        return metabaseClientTemplate.count(metabaseQuery, ApiWeakness.class);
    }

    /**
     * 弱点规则id查询弱点数量
     *
     * @param
     * @return
     * @Comments: <对此方法的描述，可以引用系统设计中的描述>
     * <AUTHOR> [<EMAIL>]
     * @since 2020-08-28 16:52
     */
    @Override
    public Long totalCountByWeaknessId(String weaknessId) throws Exception {

        MetabaseQuery metabaseQuery = new MetabaseQuery();

        //查询条件
        metabaseQuery.where("delFlag", Predicate.IS, false);
        if (DataUtil.isNotEmpty(weaknessId)) {
            metabaseQuery.where("weaknessId", Predicate.IS, weaknessId);
        }

        return metabaseClientTemplate.count(metabaseQuery, ApiWeakness.class);
    }

    @Override
    public Long totalCountByWeaknessType(String type) throws Exception {
        return mongoTemplate.count(new Query().addCriteria(org.springframework.data.mongodb.core.query.Criteria.where("type").is(type)
                .and("state").in(State.NEW, State.REPAIRING, State.REOPEN, State.FIXED)
                .and("delFlag").is(false)), ApiWeakness.class);
    }

    @Override
    public List<CommonGroupDto> groupByLevelAndType(String type, List<String> appUris) throws Exception {
        org.springframework.data.mongodb.core.query.Criteria criteria = org.springframework.data.mongodb.core.query.Criteria.where("type").is(type)
                .and("state").in(State.NEW, State.REPAIRING, State.REOPEN, State.FIXED)
                .and("delFlag").is(false);

        if (DataUtil.isNotEmpty(appUris)) {
            criteria.and("appUri").in(appUris);
        }

        Aggregation aggregation = Aggregation.newAggregation(
                Aggregation.match(
                        criteria
                ),
                Aggregation.group("level")
                        .first("level").as("id")
                        .count().as("count")
        );

        return mongoTemplate.aggregate(aggregation, collectionName, CommonGroupDto.class).getMappedResults();
    }

    @Override
    public List<CommonGroupDto> groupByLevelAndType(Map<String, Object> map) throws Exception {
        org.springframework.data.mongodb.core.query.Criteria criteria = getMongoCriteria(map);
        Aggregation aggregation = Aggregation.newAggregation(
                Aggregation.match(
                        criteria
                ),
                Aggregation.group("level")
                        .first("level").as("id")
                        .count().as("count")
        );
        return mongoTemplate.aggregate(aggregation, collectionName, CommonGroupDto.class).getMappedResults();
    }

    @Override
    public List<CommonGroupDto> groupByIds(List<String> ids, List<String> category) throws Exception {
        Aggregation aggregation = Aggregation.newAggregation(
                Aggregation.match(
                        org.springframework.data.mongodb.core.query.Criteria.where("_id").in(ids).and("type").in(category)
                ),
                Aggregation.group("level")
                        .first("level").as("id")
                        .count().as("count")
        );
        return mongoTemplate.aggregate(aggregation, collectionName, CommonGroupDto.class).getMappedResults();
    }

    @Override
    public Long totalCountByUpdateTime(String updateTime) throws Exception {
        return mongoTemplate.count(new Query().addCriteria(
                org.springframework.data.mongodb.core.query.Criteria.where("updateTime").gt(Long.parseLong(updateTime)).and("delFlag").is(false)), ApiWeakness.class, collectionName);
    }

    /**
     * 编辑接口弱点
     *
     * @param
     * @return
     * @Comments: <对此方法的描述，可以引用系统设计中的描述>
     * <AUTHOR> [<EMAIL>]
     * @since 2020-08-28 16:52
     */
    @Override
    public ApiWeakness updateApiWeakness(ApiWeakness apiWeakness) throws Exception {

        ResourceUpdates resourceUpdates = new ResourceUpdates();
        Field[] field = apiWeakness.getClass().getDeclaredFields();
        for (int i = 0; i < field.length; i++) {

            //设置是否允许访问，不是修改原来的访问权限修饰词。
            field[i].setAccessible(true);
            //字段值不为空 放入map
            if (field[i].get(apiWeakness) != null) {
                resourceUpdates.set(field[i].getName(), field[i].get(apiWeakness));
            }
        }
        if (DataUtil.isNotEmpty(apiWeakness.getDelFlag())) {
            resourceUpdates.set("delFlag", apiWeakness.getDelFlag());
            resourceUpdates.set("userDelFlag", apiWeakness.getDelFlag());
        }
        metabaseClientTemplate.update(apiWeakness.getId(), resourceUpdates, ApiWeakness.class);

        return apiWeakness;
    }

    @Override
    public List<HttpWeaknessDateTotalStat> getNewWeakCount(Long startTime, Long endTime) {
        MetabaseQuery metabaseQuery = new MetabaseQuery();
        metabaseQuery.where("status", Predicate.IS, HttpWeaknessDateTotalStat.Status.NEW);
        if (DataUtil.isNotEmpty(startTime) && DataUtil.isNotEmpty(endTime)) {
            Criteria criteria = new Criteria();
            criteria = criteria.andOperator(
                    Criteria.where("date").gte(startTime),
                    Criteria.where("date").lte(endTime));
            metabaseQuery.getCriteria().add(criteria);
        }
        return metabaseClientTemplate.find(metabaseQuery, HttpWeaknessDateTotalStat.class);
    }

    @Override
    public List<HttpWeaknessDateTotalStat> getFixedWeakCount(Long startTime, Long endTime) {
        MetabaseQuery metabaseQuery = new MetabaseQuery();
        metabaseQuery.where("status", Predicate.IS, HttpWeaknessDateTotalStat.Status.FIXED);
        if (DataUtil.isNotEmpty(startTime) && DataUtil.isNotEmpty(endTime)) {
            Criteria criteria = new Criteria();
            criteria = criteria.andOperator(
                    Criteria.where("date").gte(startTime),
                    Criteria.where("date").lte(endTime));
            metabaseQuery.getCriteria().add(criteria);
        }
        return metabaseClientTemplate.find(metabaseQuery, HttpWeaknessDateTotalStat.class);
    }

    @Override
    public List<HttpWeaknessDateTotalStat> getPepairingWeakCount(Long startTime, Long endTime) {
        MetabaseQuery metabaseQuery = new MetabaseQuery();
        metabaseQuery.where("status", Predicate.IS, HttpWeaknessDateTotalStat.Status.REPAIRING);
        if (DataUtil.isNotEmpty(startTime) && DataUtil.isNotEmpty(endTime)) {
            Criteria criteria = new Criteria();
            criteria = criteria.andOperator(
                    Criteria.where("date").gte(startTime),
                    Criteria.where("date").lte(endTime));
            metabaseQuery.getCriteria().add(criteria);
        }
        return metabaseClientTemplate.find(metabaseQuery, HttpWeaknessDateTotalStat.class);
    }

    /**
     * 返回每个弱点等级的数量
     *
     * @param startTime
     * @param endTime
     * @return
     */
    @Override
    public Map<Integer, Long> getNewLevelAmount(Long startTime, Long endTime, Long startDate, Long endDate, State state) {
        MetabaseQuery metabaseQuery1 = new MetabaseQuery();
        metabaseQuery1.where("delFlag", Predicate.IS, false);
        metabaseQuery1.where("level", Predicate.IS, 1);
        if (DataUtil.isNotEmpty(state)) {
            metabaseQuery1.where("state", Predicate.IS, state);
        }
        if (DataUtil.isNotEmpty(startTime) && DataUtil.isNotEmpty(endTime)) {

            Criteria criteria = new Criteria();
            criteria = criteria.andOperator(
                    Criteria.where("earlyTimestamp").gte(startTime),
                    Criteria.where("earlyTimestamp").lte(endTime));
            metabaseQuery1.getCriteria().add(criteria);
        }
        if (DataUtil.isNotEmpty(startDate) && DataUtil.isNotEmpty(endDate)) {

            Criteria criteria = new Criteria();
            criteria = criteria.andOperator(
                    Criteria.where("fixedTimestamp").gte(startDate),
                    Criteria.where("fixedTimestamp").lte(endDate));
            metabaseQuery1.getCriteria().add(criteria);
        }
        Map<Integer, Long> map = new HashMap<>();
        Long count1 = metabaseClientTemplate.count(metabaseQuery1, ApiWeakness.class);
        MetabaseQuery metabaseQuery2 = new MetabaseQuery();
        metabaseQuery2.where("delFlag", Predicate.IS, false);
        if (DataUtil.isNotEmpty(state)) {
            metabaseQuery2.where("state", Predicate.IS, state);
        }
        metabaseQuery2.where("level", Predicate.IS, 2);
        if (DataUtil.isNotEmpty(startTime) && DataUtil.isNotEmpty(endTime)) {

            Criteria criteria = new Criteria();
            criteria = criteria.andOperator(
                    Criteria.where("earlyTimestamp").gte(startTime),
                    Criteria.where("earlyTimestamp").lte(endTime));
            metabaseQuery2.getCriteria().add(criteria);
        }
        if (DataUtil.isNotEmpty(startDate) && DataUtil.isNotEmpty(endDate)) {

            Criteria criteria = new Criteria();
            criteria = criteria.andOperator(
                    Criteria.where("fixedTimestamp").gte(startDate),
                    Criteria.where("fixedTimestamp").lte(endDate));
            metabaseQuery2.getCriteria().add(criteria);
        }
        Long count2 = metabaseClientTemplate.count(metabaseQuery2, ApiWeakness.class);
        MetabaseQuery metabaseQuery3 = new MetabaseQuery();
        metabaseQuery3.where("delFlag", Predicate.IS, false);
        if (DataUtil.isNotEmpty(state)) {
            metabaseQuery3.where("state", Predicate.IS, state);
        }
        metabaseQuery3.where("level", Predicate.IS, 3);
        if (DataUtil.isNotEmpty(startTime) && DataUtil.isNotEmpty(endTime)) {

            Criteria criteria = new Criteria();
            criteria = criteria.andOperator(
                    Criteria.where("earlyTimestamp").gte(startTime),
                    Criteria.where("earlyTimestamp").lte(endTime));
            metabaseQuery3.getCriteria().add(criteria);
        }
        if (DataUtil.isNotEmpty(startDate) && DataUtil.isNotEmpty(endDate)) {

            Criteria criteria = new Criteria();
            criteria = criteria.andOperator(
                    Criteria.where("fixedTimestamp").gte(startDate),
                    Criteria.where("fixedTimestamp").lte(endDate));
            metabaseQuery3.getCriteria().add(criteria);
        }
        Long count3 = metabaseClientTemplate.count(metabaseQuery3, ApiWeakness.class);
        map.put(1, count1);
        map.put(2, count2);
        map.put(3, count3);
        return map;
    }

    /**
     * 获取新弱点口令类数量
     *
     * @param startTime
     * @param endTime
     * @param map
     * @return
     */
    @Override
    public Map<String, Long> getPasswordAmount(Long startTime, Long endTime, Long startDate, Long endDate, Map<String, List<String>> map, State state) {
        MetabaseQuery metabaseQuery = new MetabaseQuery();
        Map<String, Long> maps = new HashMap<>();
        metabaseQuery.where("delFlag", Predicate.IS, false);
        if (DataUtil.isNotEmpty(state)) {
            metabaseQuery.where("state", Predicate.IS, state);
        }
        metabaseQuery.where("weaknessId", Predicate.IN, map.get("password"));
        if (DataUtil.isNotEmpty(startTime) && DataUtil.isNotEmpty(endTime)) {

            Criteria criteria = new Criteria();
            criteria = criteria.andOperator(
                    Criteria.where("earlyTimestamp").gte(startTime),
                    Criteria.where("earlyTimestamp").lte(endTime));
            metabaseQuery.getCriteria().add(criteria);
        }
        if (DataUtil.isNotEmpty(startDate) && DataUtil.isNotEmpty(endDate)) {

            Criteria criteria = new Criteria();
            criteria = criteria.andOperator(
                    Criteria.where("fixedTimestamp").gte(startDate),
                    Criteria.where("fixedTimestamp").lte(endDate));
            metabaseQuery.getCriteria().add(criteria);
        }
        Long count = metabaseClientTemplate.count(metabaseQuery, ApiWeakness.class);
        maps.put("password", count);
        return maps;
    }

    @Override
    public Map<String, Long> getIdentityAmount(Long startTime, Long endTime, Long startDate, Long endDate, Map<String, List<String>> map, State state) {
        MetabaseQuery metabaseQuery = new MetabaseQuery();
        Map<String, Long> maps = new HashMap<>();
        metabaseQuery.where("delFlag", Predicate.IS, false);
        if (DataUtil.isNotEmpty(state)) {
            metabaseQuery.where("state", Predicate.IS, state);
        }
        metabaseQuery.where("weaknessId", Predicate.IN, map.get("identity"));
        if (DataUtil.isNotEmpty(startTime) && DataUtil.isNotEmpty(endTime)) {

            Criteria criteria = new Criteria();
            criteria = criteria.andOperator(
                    Criteria.where("earlyTimestamp").gte(startTime),
                    Criteria.where("earlyTimestamp").lte(endTime));
            metabaseQuery.getCriteria().add(criteria);
        }
        if (DataUtil.isNotEmpty(startDate) && DataUtil.isNotEmpty(endDate)) {

            Criteria criteria = new Criteria();
            criteria = criteria.andOperator(
                    Criteria.where("fixedTimestamp").gte(startDate),
                    Criteria.where("fixedTimestamp").lte(endDate));
            metabaseQuery.getCriteria().add(criteria);
        }
        Long count = metabaseClientTemplate.count(metabaseQuery, ApiWeakness.class);
        maps.put("identity", count);
        return maps;
    }

    @Override
    public Map<String, Long> getAuthorizationAmount(Long startTime, Long endTime, Long startDate, Long endDate, Map<String, List<String>> map, State state) {
        MetabaseQuery metabaseQuery = new MetabaseQuery();
        Map<String, Long> maps = new HashMap<>();
        metabaseQuery.where("delFlag", Predicate.IS, false);
        if (DataUtil.isNotEmpty(state)) {
            metabaseQuery.where("state", Predicate.IS, state);
        }
        metabaseQuery.where("weaknessId", Predicate.IN, map.get("authorization"));
        if (DataUtil.isNotEmpty(startTime) && DataUtil.isNotEmpty(endTime)) {

            Criteria criteria = new Criteria();
            criteria = criteria.andOperator(
                    Criteria.where("earlyTimestamp").gte(startTime),
                    Criteria.where("earlyTimestamp").lte(endTime));
            metabaseQuery.getCriteria().add(criteria);
        }
        if (DataUtil.isNotEmpty(startDate) && DataUtil.isNotEmpty(endDate)) {

            Criteria criteria = new Criteria();
            criteria = criteria.andOperator(
                    Criteria.where("fixedTimestamp").gte(startDate),
                    Criteria.where("fixedTimestamp").lte(endDate));
            metabaseQuery.getCriteria().add(criteria);
        }
        Long count = metabaseClientTemplate.count(metabaseQuery, ApiWeakness.class);
        maps.put("authorization", count);
        return maps;
    }

    @Override
    public Map<String, Long> getDataflowAmount(Long startTime, Long endTime, Long startDate, Long endDate, Map<String, List<String>> map, State state) {
        MetabaseQuery metabaseQuery = new MetabaseQuery();
        Map<String, Long> maps = new HashMap<>();
        metabaseQuery.where("delFlag", Predicate.IS, false);
        if (DataUtil.isNotEmpty(state)) {
            metabaseQuery.where("state", Predicate.IS, state);
        }
        metabaseQuery.where("weaknessId", Predicate.IN, map.get("dataflow"));
        if (DataUtil.isNotEmpty(startTime) && DataUtil.isNotEmpty(endTime)) {

            Criteria criteria = new Criteria();
            criteria = criteria.andOperator(
                    Criteria.where("earlyTimestamp").gte(startTime),
                    Criteria.where("earlyTimestamp").lte(endTime));
            metabaseQuery.getCriteria().add(criteria);
        }
        if (DataUtil.isNotEmpty(startDate) && DataUtil.isNotEmpty(endDate)) {

            Criteria criteria = new Criteria();
            criteria = criteria.andOperator(
                    Criteria.where("fixedTimestamp").gte(startDate),
                    Criteria.where("fixedTimestamp").lte(endDate));
            metabaseQuery.getCriteria().add(criteria);
        }
        Long count = metabaseClientTemplate.count(metabaseQuery, ApiWeakness.class);
        maps.put("dataflow", count);
        return maps;
    }

    @Override
    public List<Map<String, Long>> getTypeGroup(Map<String, Object> map, Map<String, List<String>> weaknessTypeIds) {
        Map<String, Long> maps = new HashMap<>();
        List<Map<String, Long>> counts = new ArrayList<>();
        if (DataUtil.isNotEmpty(weaknessTypeIds)) {
            for (Map.Entry<String, List<String>> entry : weaknessTypeIds.entrySet()) {
                Long typeCount = getTypeCount(map, entry.getValue());
                maps.put(entry.getKey(), typeCount);
            }
            counts.add(maps);
        }
        return counts;
    }

    @Override
    public RiskLevelMatchDto.Weakness getWeakness(String uri) {
        List<AggregationOperation> operations = new ArrayList<>();
        operations.add(Aggregation.match(org.springframework.data.mongodb.core.query.Criteria.where("uri").is(uri))); // httpapi:http://47.com/tags/post.jsp
        operations.add(
                Aggregation.group()
//                Aggregation.group("uri")
                        .addToSet("name").as("weaknessNames")  // 弱点名称
                        .addToSet("level").as("weaknessLevels")); // 弱点等级

        Aggregation aggregation = Aggregation.newAggregation(operations).withOptions(newAggregationOptions().allowDiskUse(true).build());

        List<RiskLevelMatchDto.Weakness> appAccountDtos = mongoTemplate.aggregate(aggregation, collectionName, RiskLevelMatchDto.Weakness.class).getMappedResults();
        if (DataUtil.isNotEmpty(appAccountDtos)) {
            RiskLevelMatchDto.Weakness weakness = appAccountDtos.get(0);
            weakness.setLevelNum();
            return weakness;
        }

        return null;
    }

    @Override
    public void updateWeaknessAppName(String uri, String name) {
        List<ApiWeakness> apiWeaknesses = metabaseClientTemplate.find(new MetabaseQuery().where("appUri", Predicate.IS, uri).fields(Collections.singletonList("_id")), ApiWeakness.class);
        if (apiWeaknesses == null) {
            return;
        }
        for (ApiWeakness a : apiWeaknesses) {
            metabaseClientTemplate.update(a.getId(), ResourceUpdates.create().set("api.appName", name), ApiWeakness.class);
        }
    }

    @Override
    public List<ApiWeakness> selectByAppUri(String appUri, List<String> fields) {
        return metabaseClientTemplate.find(new MetabaseQuery().where("appUri", Predicate.IS, appUri).fields(fields), ApiWeakness.class);
    }

    private Long getTypeCount(Map<String, Object> map, List<String> weaknessTypeIds) {
        MetabaseQuery metabaseQuery = new MetabaseQuery();

        //关联表
        List<JoinTable> joinTableList = new ArrayList<JoinTable>() {
            {
                JoinTable joinTable = new JoinTable();
                joinTable.setFrom(tableName);
                joinTable.setLocalField("uri");
                joinTable.setForeignField("uri");
                joinTable.setAs(as);
                add(joinTable);
            }
        };

        //查询条件
        metabaseQuery.where("delFlag", Predicate.IS, false);
        if (DataUtil.isNotEmpty(map)) {
            List<String> dataLabelIds = new ArrayList<>();
            if (map.containsKey("reqDataLabels") || map.containsKey("rspDataLabels")) {
                dataLabelIds = dataLabelNacosDao.getAll().stream().map(dataLabel -> dataLabel.getId()).collect(Collectors.toList());
            }
            for (String key : map.keySet()) {

                Object value = map.get(key);
                if (DataUtil.isNotEmpty(value)) {

                    if ("weaknessId".equals(key)) {
                        metabaseQuery.where(key, Predicate.IS, value);
                    } else if ("host".equals(key)) {
                        metabaseQuery.where(key, Predicate.IS, value);
                    } else if ("appUri".equals(key)) {
                        metabaseQuery.where(key, Predicate.IS, value);
                    } else if ("uri".equals(key)) {
                        metabaseQuery.where(key, Predicate.REGEX, value);
                    } else if ("apiUrl".equals(key)) {
                        metabaseQuery.where(as + "." + key, Predicate.REGEX, DataUtil.regexStrEscape(value.toString()));
                    } else if ("name".equals(key)) {
                        metabaseQuery.where(key, Predicate.IS, value);
                    } else if ("state".equals(key)) {
                        metabaseQuery.where(key, Predicate.IS, value);
                    } else if ("classifications".equals(key)) {
                        metabaseQuery.where(as + "." + key, Predicate.ALL, value);
                    } else if ("reqDataLabels".equals(key)) {
                        List<String> labels = (List<String>) value;
                        if (labels.size() > 0 && labels.get(0).equals("ALL")) {
                            labels = dataLabelIds;
                        }
                        metabaseQuery.where(as + "." + key, Predicate.ALL, labels);
                    } else if ("rspDataLabels".equals(key)) {
                        List<String> labels = (List<String>) value;
                        if (labels.size() > 0 && labels.get(0).equals("ALL")) {
                            labels = dataLabelIds;
                        }
                        metabaseQuery.where(as + "." + key, Predicate.ALL, labels);
                    } else if ("visitDomains".equals(key)) {
                        metabaseQuery.where(as + "." + key, Predicate.ALL, value);
                    } else if ("terminals".equals(key)) {
                        metabaseQuery.where(as + "." + key, Predicate.ALL, value);
                    }
                }
            }
            if (DataUtil.isNotEmpty(map.get("startDate")) && DataUtil.isNotEmpty(map.get("endDate"))) {

                Criteria criteria = new Criteria();
                criteria = criteria.andOperator(
                        Criteria.where("earlyTimestamp").gte(map.get("startDate")),
                        Criteria.where("earlyTimestamp").lte(map.get("endDate")));
                metabaseQuery.getCriteria().add(criteria);
            }
        }
        if (DataUtil.isNotEmpty(weaknessTypeIds)) {
            metabaseQuery.where("weaknessId", Predicate.IN, weaknessTypeIds);
        }

        JoinDto joinDto = new JoinDto();
        joinDto.setJoinTableList(joinTableList);
        joinDto.setMetabaseQuery(metabaseQuery);
        joinDto.setCollection(collectionName);

        return metabaseClientTemplate.count(joinDto);
    }

    /**
     * 查询接口弱点分组
     *
     * @param
     * @return
     * @Comments: <对此方法的描述，可以引用系统设计中的描述>
     * <AUTHOR> [<EMAIL>]
     * @since 2020-08-28 16:52
     */
    @Override
    public List<AggregationResult> selectApiWeaknessGroup(Map<String, Object> map, Integer sort, String[] groupFields, String category) throws Exception {

        MetabaseQuery metabaseQuery = getMetabaseQuery(map);

        if (DataUtil.isNotEmpty(category)) {
            metabaseQuery.where("type", Predicate.IS, category);
        }

        //展开字段
        for (String groupField : groupFields) {
            if ("api.departments.department".equals(groupField)) {
                metabaseQuery.setUnwind("api.departments");
            } else if ("api.terminals".equals(groupField)) {
                metabaseQuery.setUnwind("api.terminals");
            } else if ("api.visitDomains".equals(groupField)) {
                metabaseQuery.setUnwind("api.visitDomains");
            } else if ("api.deployDomains".equals(groupField)) {
                metabaseQuery.setUnwind("api.deployDomains");
            } else if ("api.methods".equals(groupField)) {
                metabaseQuery.setUnwind("api.methods");
            } else if ("api.reqContentTypes".equals(groupField)) {
                metabaseQuery.setUnwind("api.reqContentTypes");
            } else if ("api.rspContentTypes".equals(groupField)) {
                metabaseQuery.setUnwind("api.rspContentTypes");
            } else if ("api.appName".equals(groupField)) {
                metabaseQuery.setUnwind("api.appName");
            }
        }

        //排序
        if (DataUtil.isNotEmpty(sort)) {

            if (sort == ConstantUtil.Sort.ASC) {
                metabaseQuery.sort(Sort.by("resultAlias", SortOrder.ASC), Sort.by("_id", SortOrder.ASC));
            } else if (sort == ConstantUtil.Sort.DESC) {
                metabaseQuery.sort(Sort.by("resultAlias", SortOrder.DESC), Sort.by("_id", SortOrder.DESC));
            } else {
                metabaseQuery.sort(Sort.by("resultAlias", SortOrder.DESC), Sort.by("_id", SortOrder.DESC));
            }
        } else {
            metabaseQuery.sort(Sort.by("resultAlias", SortOrder.DESC), Sort.by("_id", SortOrder.DESC));
        }

        MetabaseGroupOperation metabaseGroupOperation = new MetabaseGroupOperation();
        metabaseGroupOperation.setGroupFields(groupFields);
        metabaseGroupOperation.setAggrType("COUNT");

        return metabaseClientTemplate.aggregate(metabaseQuery, metabaseGroupOperation, ApiWeakness.class);
    }

    @Override
    public List<AggregationResult> selectApiWeaknessGroup(Map<String, Object> map, Integer sort, String[] groupFields, List<String> categorys) throws Exception {

        MetabaseQuery metabaseQuery = getMetabaseQuery(map);

        if (DataUtil.isNotEmpty(categorys)) {
            metabaseQuery.where("type", Predicate.IN, categorys);
        }

        //展开字段
        for (String groupField : groupFields) {
            if ("api.departments.department".equals(groupField)) {
                metabaseQuery.setUnwind("api.departments");
            } else if ("api.terminals".equals(groupField)) {
                metabaseQuery.setUnwind("api.terminals");
            } else if ("api.visitDomains".equals(groupField)) {
                metabaseQuery.setUnwind("api.visitDomains");
            } else if ("api.deployDomains".equals(groupField)) {
                metabaseQuery.setUnwind("api.deployDomains");
            } else if ("api.methods".equals(groupField)) {
                metabaseQuery.setUnwind("api.methods");
            } else if ("api.reqContentTypes".equals(groupField)) {
                metabaseQuery.setUnwind("api.reqContentTypes");
            } else if ("api.rspContentTypes".equals(groupField)) {
                metabaseQuery.setUnwind("api.rspContentTypes");
            } else if ("api.appName".equals(groupField)) {
                metabaseQuery.setUnwind("api.appName");
            }
        }

        //排序
        if (DataUtil.isNotEmpty(sort)) {

            if (sort == ConstantUtil.Sort.ASC) {
                metabaseQuery.sort(Sort.by("resultAlias", SortOrder.ASC), Sort.by("_id", SortOrder.ASC));
            } else if (sort == ConstantUtil.Sort.DESC) {
                metabaseQuery.sort(Sort.by("resultAlias", SortOrder.DESC), Sort.by("_id", SortOrder.DESC));
            } else {
                metabaseQuery.sort(Sort.by("resultAlias", SortOrder.DESC), Sort.by("_id", SortOrder.DESC));
            }
        } else {
            metabaseQuery.sort(Sort.by("resultAlias", SortOrder.DESC), Sort.by("_id", SortOrder.DESC));
        }

        MetabaseGroupOperation metabaseGroupOperation = new MetabaseGroupOperation();
        metabaseGroupOperation.setGroupFields(groupFields);
        metabaseGroupOperation.setAggrType("COUNT");
        return metabaseClientTemplate.aggregate(metabaseQuery, metabaseGroupOperation, ApiWeakness.class);
    }

    /**
     * 弱点类型环形图
     *
     * @param
     * @return
     * @Comments: <对此方法的描述，可以引用系统设计中的描述>
     * <AUTHOR> [<EMAIL>]
     * @since 2021-08-31 16:52
     */
    @Override
    public List<AggregationDto> weaknessTypeDonutChartByAppUri(String appUri, GroupDto groupDto) throws Exception {

        org.springframework.data.mongodb.core.query.Criteria criteria = new org.springframework.data.mongodb.core.query.Criteria();
        criteria.and("appUri").is(appUri);
        criteria.and("state").in(State.NEW, State.REPAIRING, State.REOPEN, State.FIXED);
        criteria.and("delFlag").is(false);

        return this.group(criteria, groupDto, null, null, null, collectionName);
    }

    @Override
    public List<Map> groupExposeData() {
        List<AggregationOperation> operations = new ArrayList<>();
        operations.add(Aggregation.match(org.springframework.data.mongodb.core.query.Criteria
                .where("type")
                .is("dataflow")
                .and("delFlag")
                .is(false)
                .and("state")
                .in(State.NEW, State.REPAIRING, State.REOPEN, State.FIXED)));
        operations.add(Aggregation.group("level", "weaknessId")
                .count()
                .as("count")
                .first("name")
                .as("name")
                .first("type")
                .as("type"));
        Aggregation aggregation = Aggregation.newAggregation(operations);
        AggregationResults<Map> aggregationResults = mongoTemplate.aggregate(aggregation,
                HttpResourceConstant.API_WEAKNESS,
                Map.class);
        return aggregationResults.getMappedResults();
    }

    @Override
    public Double getWeaknessDimension() {
        long levelLow = mongoTemplate.count(new Query().addCriteria(org.springframework.data.mongodb.core.query.Criteria
                .where("level").is(WeaknessLevelEnum.LOW_LEVEL.getValue())
                .and("state").in("NEW", "REPAIRING")
                .and("delFlag").is(false)
        ), collectionName);
        long levelMid = mongoTemplate.count(new Query().addCriteria(org.springframework.data.mongodb.core.query.Criteria
                .where("level").is(WeaknessLevelEnum.MEDIUM_LEVEL.getValue())
                .and("state").in("NEW", "REPAIRING")
                .and("delFlag").is(false)
        ), collectionName);
        long levelHigh = mongoTemplate.count(new Query().addCriteria(org.springframework.data.mongodb.core.query.Criteria
                .where("level").is(WeaknessLevelEnum.HIGH_LEVEL.getValue())
                .and("state").in("NEW", "REPAIRING")
                .and("delFlag").is(false)
        ), collectionName);
        long count = mongoTemplate.count(new Query(org.springframework.data.mongodb.core.query.Criteria.where("delFlag").is(false).and("state").in("NEW", "REPAIRING")), collectionName);
        return count == 0L ? 0L : (levelHigh + levelMid * 0.5 + levelLow * 0.3) / count;
    }

//    @Override
//    public Long count() {
//        return mongoTemplate.count(new Query().addCriteria(org.springframework.data.mongodb.core.query.Criteria
//                .where("state").in(State.NEW.name(), State.SUSPEND.name(), State.REPAIRING.name(), State.VERIFYING.name(), State.FIXED.name())
//                .and("delFlag").is(false)
//        ), collectionName);
//    }

    @Override
    public List<ApiWeakness> selectBy(String appUri, String weaknessId, List<String> fields) {
        return metabaseClientTemplate.find(new MetabaseQuery()
                        .where("weaknessId", Predicate.IS, weaknessId)
                        .where("appUri", Predicate.IS, appUri)
                        .fields(fields),
                ApiWeakness.class);
    }

    @Override
    public boolean exist(String apiUri, String weaknessId) {
        return metabaseClientTemplate.existBy(new MetabaseQuery().where("weaknessId", Predicate.IS, weaknessId)
                .where("uri", Predicate.IS, apiUri), ApiWeakness.class);
    }

    @Override
    public void update(String id, Map<String, Object> update) {
        metabaseClientTemplate.update(id, ResourceUpdates.create(update), ApiWeakness.class);
    }

    @Override
    public void update(MetabaseQuery query, ResourceUpdates resourceUpdates) {
        metabaseClientTemplate.update(query, resourceUpdates, false, ApiWeakness.class);
    }


    @Override
    public List<Long> getStatistics() {
        List<Long> result = new ArrayList<>();
        long totalWeaknessEvent = mongoTemplate.count(new Query().addCriteria(org.springframework.data.mongodb.core.query.Criteria
                .where("delFlag").is(false)
                .and("state").in(State.NEW, State.REPAIRING, State.REOPEN, State.FIXED)
        ), collectionName);
        result.add(totalWeaknessEvent);
        long totalHighLevelWeaknessEvent = mongoTemplate.count(new Query().addCriteria(org.springframework.data.mongodb.core.query.Criteria
                .where("delFlag").is(false)
                .and("level").is(WeaknessLevelEnum.HIGH_LEVEL.getValue())
                .and("state").in(State.NEW, State.REPAIRING, State.REOPEN, State.FIXED)
        ), collectionName);
        result.add(totalHighLevelWeaknessEvent);
        return result;
    }

    @Override
    public List<ApiWeakness> select(long startCreateTime) {
        return metabaseClientTemplate
                .find(new MetabaseQuery().where("createTime",
                                Predicate.GTE,
                                startCreateTime).limit(1000),
                        ApiWeakness.class);
    }

    @Override
    public List<WeaknessDistribute> getWeaknessDistribute() {
        Aggregation aggregation = Aggregation.newAggregation(
                Aggregation.match(
                        getValidWeaknessCriteria()
                ),
                Aggregation.group("weaknessId")
                        .first("weaknessId").as("id")
                        .first("name").as("name")
                        .count().as("count")
        );
        return mongoTemplate.aggregate(aggregation, collectionName, WeaknessDistribute.class).getMappedResults();
    }

    private org.springframework.data.mongodb.core.query.Criteria getValidWeaknessCriteria() {
        return org.springframework.data.mongodb.core.query.Criteria
                .where("delFlag").is(false)
                .and("state").in(State.NEW, State.REPAIRING, State.REOPEN, State.FIXED);
    }

    private org.springframework.data.mongodb.core.query.Criteria getValidWeaknessCriteriaInternet() {
        return org.springframework.data.mongodb.core.query.Criteria
                .where("delFlag").is(false)
                .and("state").in(State.NEW, State.REPAIRING, State.REOPEN, State.FIXED)
                .and("api.visitDomains").regex("互联网-");
    }

    @Override
    public long getWeaknessCount(Boolean flag) {
        if (flag) {
            return mongoTemplate.count(new Query(getValidWeaknessCriteriaInternet()), ApiWeakness.class, HttpResourceConstant.API_WEAKNESS);
        } else {
            return mongoTemplate.count(new Query(getValidWeaknessCriteria()), ApiWeakness.class, HttpResourceConstant.API_WEAKNESS);
        }
    }

    @Override
    public List<WeaknessDistribute> getWeaknessDistributed(boolean flag) {
        Aggregation aggregation = null;
        if (flag) {
            aggregation = Aggregation.newAggregation(
                    Aggregation.match(
                            org.springframework.data.mongodb.core.query.Criteria
                                    .where("api.visitDomains").regex("互联网-")
                                    .and("state").in(State.NEW, State.REPAIRING, State.REOPEN, State.FIXED)
                                    .and("delFlag").is(false)
                    ),
                    Aggregation.group("weaknessId")
                            .first("name").as("name")
                            .first("weaknessId").as("id")
                            .count().as("count")
            );
        } else {
            aggregation = Aggregation.newAggregation(
                    Aggregation.match(
                            org.springframework.data.mongodb.core.query.Criteria
                                    .where("delFlag").is(false)
                                    .and("state").in(State.NEW, State.REPAIRING, State.REOPEN, State.FIXED)
                    ),
                    Aggregation.group("weaknessId")
                            .first("name").as("name")
                            .first("weaknessId").as("id")
                            .count().as("count")
            );
        }
        return mongoTemplate.aggregate(aggregation, collectionName, WeaknessDistribute.class).getMappedResults();
    }

    @Override
    public MetabaseQuery getMetabaseQuery(Map<String, Object> map) throws Exception {

        MetabaseQuery metabaseQuery = new MetabaseQuery();
        if (!map.containsKey("needDeleted") || Boolean.FALSE.equals(map.get("needDeleted"))) {
            metabaseQuery.where("delFlag", Predicate.IS, false);
        }
        if (DataUtil.isNotEmpty(map)) {
            List<String> dataLabelIds = new ArrayList<>();
            if (map.containsKey("reqDataLabels") || map.containsKey("rspDataLabels")) {
                dataLabelIds = dataLabelNacosDao.getAll().stream().map(dataLabel -> dataLabel.getId()).collect(Collectors.toList());
            }
            for (String key : map.keySet()) {

                Object value = map.get(key);
                if (DataUtil.isNotEmpty(value)) {

                    if ("weaknessId".equals(key)) {
                        metabaseQuery.where(key, Predicate.IN, value);
                    } else if ("appName".equals(key)) {
                        //API3.0 根据应用名称筛选
                        Pattern pattern = Pattern.compile(com.quanzhi.auditapiv2.common.util.utils.DataUtil.regexStrEscape(String.valueOf(value)), Pattern.CASE_INSENSITIVE);
                        metabaseQuery.where("api.appName", Predicate.REGEX, pattern);
                    } else if ("operationId".equals(key)) {
                        metabaseQuery.where(key, Predicate.REGEX, value);
                    } else if ("host".equals(key)) {
                        //API3.0 根据应用筛选
                        if (DataUtil.isNotEmpty(map.get("hostIgnoreCase")) && (boolean) map.get("hostIgnoreCase")) {
                            Pattern pattern = Pattern.compile(com.quanzhi.auditapiv2.common.util.utils.DataUtil.regexStrEscape(String.valueOf(value)), Pattern.CASE_INSENSITIVE);
                            metabaseQuery.where(key, Predicate.REGEX, pattern);
                        } else {
                            metabaseQuery.where(key, Predicate.REGEX, Pattern.quote(String.valueOf(value)));
                        }
                    } else if ("appUri".equals(key)) {
                        metabaseQuery.where(key, Predicate.IS, String.valueOf(value));
                    } else if ("uris".equals(key)) {
                        metabaseQuery.where("uri", Predicate.IN, value);
                    } else if ("uri".equals(key)) {
                        metabaseQuery.where(key, Predicate.IS, String.valueOf(value));
                    } else if ("apiUrl".equals(key)) {
                        //API3.3 路径或url筛选，默认不忽略大小写
                        String apiUrl = String.valueOf(value);
                        apiUrl = DataUtil.regexStrEscapeExceptStartCharacter(apiUrl);
                        apiUrl = apiUrl.startsWith("http") ? "^" + apiUrl : apiUrl;
                        Pattern pattern = Pattern.compile(apiUrl);
                        metabaseQuery.where("api." + key, Predicate.REGEX, pattern);
                        Document document = new Document();
                        document.put("api.apiUrl", 1);
                        metabaseQuery.setHint(document.toJson());
                    } else if ("level".equals(key)) {
                        metabaseQuery.where(key, Predicate.IN, value);
                    } else if ("state".equals(key)) {
                        metabaseQuery.where(key, Predicate.IN, value);
                    } else if ("classifications".equals(key)) {
                        if (map.containsKey("fieldOperateMap")) {
                            Map<String, String> fieldOperateMap = (Map<String, String>) map.get("fieldOperateMap");
                            if (fieldOperateMap.containsKey("classifications")) {
                                String classificationsOperator = fieldOperateMap.get("classifications");
                                if (DBOperatorEnum.OR.operator().equals(classificationsOperator)) {
                                    metabaseQuery.where("api." + key, Predicate.IN, value);
                                } else {
                                    metabaseQuery.where("api." + key, Predicate.ALL, value);
                                }
                            } else {
                                metabaseQuery.where("api." + key, Predicate.IN, value);
                            }
                        } else {
                            metabaseQuery.where("api." + key, Predicate.IN, value);
                        }
                    } else if ("featureLabels".equals(key)) {
                        if (map.containsKey("fieldOperateMap")) {
                            Map<String, String> fieldOperateMap = (Map<String, String>) map.get("fieldOperateMap");
                            if (fieldOperateMap.containsKey("featureLabels")) {
                                String classificationsOperator = fieldOperateMap.get("featureLabels");
                                if (DBOperatorEnum.OR.operator().equals(classificationsOperator)) {
                                    metabaseQuery.where("api." + key, Predicate.IN, value);
                                } else {
                                    metabaseQuery.where("api." + key, Predicate.ALL, value);
                                }
                            } else {
                                metabaseQuery.where("api." + key, Predicate.IN, value);
                            }
                        } else {
                            metabaseQuery.where("api." + key, Predicate.IN, value);
                        }
                    } else if ("reqDataLabels".equals(key)) {
                        List<String> labels = (List<String>) value;
                        if (labels.size() > 0 && labels.get(0).equals("ALL")) {
                            labels = dataLabelIds;
                        }
                        if (map.containsKey("fieldOperateMap")) {
                            Map<String, String> fieldOperateMap = (Map<String, String>) map.get("fieldOperateMap");
                            if (fieldOperateMap.containsKey("reqDataLabels")) {
                                String classificationsOperator = fieldOperateMap.get("reqDataLabels");
                                if (DBOperatorEnum.OR.operator().equals(classificationsOperator)) {
                                    metabaseQuery.where("api." + key, Predicate.IN, labels);
                                } else {
                                    metabaseQuery.where("api." + key, Predicate.ALL, labels);
                                }
                            } else {
                                metabaseQuery.where("api." + key, Predicate.IN, labels);
                            }
                        } else {
                            metabaseQuery.where("api." + key, Predicate.IN, labels);
                        }
                    } else if ("rspDataLabels".equals(key)) {
                        List<String> labels = (List<String>) value;
                        if (labels.size() > 0 && labels.get(0).equals("ALL")) {
                            labels = dataLabelIds;
                        }
                        if (map.containsKey("fieldOperateMap")) {
                            Map<String, String> fieldOperateMap = (Map<String, String>) map.get("fieldOperateMap");
                            if (fieldOperateMap.containsKey("rspDataLabels")) {
                                String classificationsOperator = fieldOperateMap.get("rspDataLabels");
                                if (DBOperatorEnum.OR.operator().equals(classificationsOperator)) {
                                    metabaseQuery.where("api." + key, Predicate.IN, labels);
                                } else {
                                    metabaseQuery.where("api." + key, Predicate.ALL, labels);
                                }
                            } else {
                                metabaseQuery.where("api." + key, Predicate.IN, labels);
                            }
                        } else {
                            metabaseQuery.where("api." + key, Predicate.IN, labels);
                        }
                    } else if ("visitDomains".equals(key)) {
                        if (map.containsKey("fieldOperateMap")) {
                            Map<String, String> fieldOperateMap = (Map<String, String>) map.get("fieldOperateMap");
                            if (fieldOperateMap.containsKey("visitDomains")) {
                                String classificationsOperator = fieldOperateMap.get("visitDomains");
                                if (DBOperatorEnum.OR.operator().equals(classificationsOperator)) {
                                    metabaseQuery.where("api." + key, Predicate.IN, value);
                                } else {
                                    metabaseQuery.where("api." + key, Predicate.ALL, value);
                                }
                            } else {
                                metabaseQuery.where("api." + key, Predicate.IN, value);
                            }
                        } else {
                            metabaseQuery.where("api." + key, Predicate.IN, value);
                        }
                    } else if ("deployDomains".equals(key)) {
                        if (map.containsKey("fieldOperateMap")) {
                            Map<String, String> fieldOperateMap = (Map<String, String>) map.get("fieldOperateMap");
                            if (fieldOperateMap.containsKey("deployDomains")) {
                                String classificationsOperator = fieldOperateMap.get("deployDomains");
                                if (DBOperatorEnum.OR.operator().equals(classificationsOperator)) {
                                    metabaseQuery.where("api." + key, Predicate.IN, value);
                                } else {
                                    metabaseQuery.where("api." + key, Predicate.ALL, value);
                                }
                            } else {
                                metabaseQuery.where("api." + key, Predicate.IN, value);
                            }
                        } else {
                            metabaseQuery.where("api." + key, Predicate.IN, value);
                        }
                    } else if ("terminals".equals(key)) {
                        if (map.containsKey("fieldOperateMap")) {
                            Map<String, String> fieldOperateMap = (Map<String, String>) map.get("fieldOperateMap");
                            if (fieldOperateMap.containsKey("terminals")) {
                                String terminalsOperator = fieldOperateMap.get("terminals");
                                if (DBOperatorEnum.OR.operator().equals(terminalsOperator)) {
                                    metabaseQuery.where("api." + key, Predicate.IN, value);
                                } else {
                                    metabaseQuery.where("api." + key, Predicate.ALL, value);
                                }
                            } else {
                                metabaseQuery.where("api." + key, Predicate.IN, value);
                            }
                        } else {
                            metabaseQuery.where("api." + key, Predicate.IN, value);
                        }
                    } else if ("methods".equals(key)) {
                        metabaseQuery.where("api." + key, Predicate.ALL, value);
                    } else if ("rspContentTypes".equals(key)) {
                        //API3.0 返回类型筛选支持多选
                        metabaseQuery.where("api." + key, Predicate.IN, value);
                    } else if ("departments".equals(key)) {
                        metabaseQuery.where("api." + key + ".department", Predicate.IS, value);
                    } else if ("orderFlag".equals(key)) {
                        metabaseQuery.where("orderFlag", Predicate.IS, value);
                    } else if ("id".equals(key)) {
                        metabaseQuery.where("_id", Predicate.IS, value);
                    } else if ("ids".equals(key)) {
                        metabaseQuery.where("_id", Predicate.IN, value);
                    } else if ("apiType".equals(key)) {
                        //API3.0 api类型筛选 支持多选
                        metabaseQuery.where("api." + key, Predicate.IN, value);
                    } else if ("uaTypes".equals(key)) {
                        //API3.0 终端类型筛选 支持多选
                        metabaseQuery.where("api." + key, Predicate.IN, value);
                    } else if ("nodeId".equals(key)) {
                        metabaseQuery.where("nodes.nid", Predicate.IS, value);
                    }
                }
            }
            if (DataUtil.isNotEmpty(map.get("lastTimestampStart")) && DataUtil.isNotEmpty(map.get("lastTimestampEnd"))) {
                //API3.0 最近活跃时间筛选
                Criteria criteria = new Criteria();
                criteria = criteria.andOperator(
                        Criteria.where("lastTimestamp").gte(map.get("lastTimestampStart")),
                        Criteria.where("lastTimestamp").lte(map.get("lastTimestampEnd")));
                metabaseQuery.getCriteria().add(criteria);
            }
            if (DataUtil.isNotEmpty(map.get("earlyTimestampStart")) && DataUtil.isNotEmpty(map.get("earlyTimestampEnd"))) {
                //API3.0 首次发现时间筛选
                Criteria criteria = new Criteria();
                criteria = criteria.andOperator(
                        Criteria.where("earlyTimestamp").gte(map.get("earlyTimestampStart")),
                        Criteria.where("earlyTimestamp").lte(map.get("earlyTimestampEnd")));
                metabaseQuery.getCriteria().add(criteria);
            }
            if (DataUtil.isNotEmpty(map.get("startTime")) && DataUtil.isNotEmpty(map.get("endTime"))) {

                Criteria criteria = new Criteria();
                criteria = criteria.andOperator(
                        Criteria.where("fixedTimestamp").gte(map.get("startTime")),
                        Criteria.where("fixedTimestamp").lte(map.get("endTime")));
                metabaseQuery.getCriteria().add(criteria);
            }
            if (DataUtil.isNotEmpty(map.get("endUpdateTime"))) {
                //API3.0 最近活跃时间筛选
                Criteria criteria = new Criteria();
                criteria = criteria.andOperator(
                        Criteria.where("updateTime").lte(map.get("endUpdateTime")));
                metabaseQuery.getCriteria().add(criteria);
            }
            if (DataUtil.isNotEmpty(map.get("startUpdateTime"))) {
                Criteria criteria = new Criteria();
                criteria = criteria.andOperator(
                        Criteria.where("updateTime").gte(map.get("startUpdateTime")));
                metabaseQuery.getCriteria().add(criteria);
            }
            Criteria customPropertyCriteria = customPropertySearchAdapter
                    .getMetabaseCriteria(customPropertySearchAdapter
                            .buildCustomProperty(map), "api");
            if (customPropertyCriteria != null) {
                metabaseQuery.getCriteria().add(customPropertyCriteria);
            }

            if (DataUtil.isNotEmpty(map.get("isAssetAuthorization")) && (boolean) map.get("isAssetAuthorization") == true) {

                if (DataUtil.isNotEmpty(map.get("appUriSet")) || DataUtil.isNotEmpty(map.get("departmentSet"))) {

                    Criteria criteria = new Criteria();
                    Criteria criteria1 = null;
                    Criteria criteria2 = null;

                    if (DataUtil.isNotEmpty(map.get("appUriSet"))) {
                        criteria1 = Criteria.where("appUri").in((List<String>) map.get("appUriSet"));
                    }

                    if (DataUtil.isNotEmpty(map.get("departmentSet"))) {
                        criteria2 = Criteria.where("api.departments.department").in(map.get("departmentSet"));
                    }

                    if (DataUtil.isNotEmpty(criteria1) && DataUtil.isEmpty(criteria2)) {
                        criteria = criteria.orOperator(criteria1);
                    } else if (DataUtil.isEmpty(criteria1) && DataUtil.isNotEmpty(criteria2)) {
                        criteria = criteria.orOperator(criteria2);
                    } else if (DataUtil.isNotEmpty(criteria1) && DataUtil.isNotEmpty(criteria2)) {
                        criteria = criteria.orOperator(criteria1, criteria2);
                    }

                    metabaseQuery.getCriteria().add(criteria);
                } else {
                    metabaseQuery.where("_id", Predicate.IS, "");
                }
            }
        }
        queryAdapterRegistry.module(Module.WEAKNESS).query(map, metabaseQuery);
        return metabaseQuery;
    }

    @Override
    public org.springframework.data.mongodb.core.query.Criteria getMongoCriteria(Map<String, Object> map) throws Exception {

        org.springframework.data.mongodb.core.query.Criteria criteria = customPropertySearchAdapter
                .getMongoCriteria(customPropertySearchAdapter
                        .buildCustomProperty(map), "api");

        if (criteria == null) criteria = new org.springframework.data.mongodb.core.query.Criteria();

//        org.springframework.data.mongodb.core.query.Criteria criteria = new org.springframework.data.mongodb.core.query.Criteria();
        Boolean delFlag = false;
        if (DataUtil.isNotEmpty(map)) {
            List<String> dataLabelIds = new ArrayList<>();
            if (map.containsKey("reqDataLabels") || map.containsKey("rspDataLabels")) {
                dataLabelIds = dataLabelNacosDao.getAll().stream().map(dataLabel -> dataLabel.getId()).collect(Collectors.toList());
            }
            List<org.springframework.data.mongodb.core.query.Criteria> andCriteriaList = new ArrayList<>();
            for (String key : map.keySet()) {

                Object value = map.get(key);
                if (DataUtil.isNotEmpty(value)) {

                    if ("weaknessId".equals(key)) {
                        criteria.and(key).in(value instanceof String ? Arrays.asList((String) value) : (List<String>) value);
                    } else if ("category".equals(key)) {
                        criteria.and("type").in(value instanceof String ? Arrays.asList((String) value) : (List<String>) value);
                    } else if ("appName".equals(key)) {
                        //API3.0 根据应用名称筛选
                        criteria.and("api.appName").regex(String.valueOf(value));
                    } else if ("operationId".equals(key)) {
                        criteria.and(key).regex((String) value);
                    } else if ("host".equals(key)) {
                        //API3.0 根据应用筛选
                        if (DataUtil.isNotEmpty(map.get("hostIgnoreCase")) && (boolean) map.get("hostIgnoreCase")) {
                            Pattern pattern = Pattern.compile(com.quanzhi.auditapiv2.common.util.utils.DataUtil.regexStrEscape(String.valueOf(value)), Pattern.CASE_INSENSITIVE);
                            criteria.and(key).regex(pattern);
                        } else {
                            criteria.and(key).regex(Pattern.quote(String.valueOf(value)));
                        }
                    } else if ("appUri".equals(key)) {
                        criteria.and(key).is(String.valueOf(value));
                    } else if ("uri".equals(key)) {
                        criteria.and(key).is(String.valueOf(value));
                    } else if ("apiUrl".equals(key)) {
                        //API3.0 路径或url筛选，默认忽略大小写
                        String apiUrl = String.valueOf(value);
                        apiUrl = DataUtil.regexStrEscapeExceptStartCharacter(apiUrl);
                        apiUrl = apiUrl.startsWith("http") ? "^" + apiUrl : apiUrl;
                        Pattern pattern = Pattern.compile(apiUrl, Pattern.CASE_INSENSITIVE);
                        criteria.and("api." + key).regex(pattern);
                    } else if ("level".equals(key)) {
                        criteria.and(key).in(value instanceof String ? Arrays.asList((String) value) : (List<String>) value);
                    } else if ("state".equals(key)) {
                        criteria.and(key).in(value instanceof String ? Arrays.asList((String) value) : (List<String>) value);
                    } else if ("classifications".equals(key)) {
                        if (map.containsKey("fieldOperateMap")) {
                            Map<String, String> fieldOperateMap = (Map<String, String>) map.get("fieldOperateMap");
                            if (fieldOperateMap.containsKey("classifications")) {
                                String classificationsOperator = fieldOperateMap.get("classifications");
                                if (DBOperatorEnum.OR.operator().equals(classificationsOperator)) {
                                    criteria.and("api." + key).in(value instanceof String ? Arrays.asList((String) value) : (List<String>) value);
                                } else {
                                    criteria.and("api." + key).all(value instanceof String ? Arrays.asList((String) value) : (List<String>) value);
                                }
                            } else {
                                criteria.and("api." + key).in(value instanceof String ? Arrays.asList((String) value) : (List<String>) value);
                            }
                        } else {
                            criteria.and("api." + key).in(value instanceof String ? Arrays.asList((String) value) : (List<String>) value);
                        }
                    } else if ("featureLabels".equals(key)) {
                        if (map.containsKey("fieldOperateMap")) {
                            Map<String, String> fieldOperateMap = (Map<String, String>) map.get("fieldOperateMap");
                            if (fieldOperateMap.containsKey("featureLabels")) {
                                String classificationsOperator = fieldOperateMap.get("featureLabels");
                                if (DBOperatorEnum.OR.operator().equals(classificationsOperator)) {
                                    criteria.and("api." + key).in(value instanceof String ? Arrays.asList((String) value) : (List<String>) value);
                                } else {
                                    criteria.and("api." + key).all(value instanceof String ? Arrays.asList((String) value) : (List<String>) value);
                                }
                            } else {
                                criteria.and("api." + key).in(value instanceof String ? Arrays.asList((String) value) : (List<String>) value);
                            }
                        } else {
                            criteria.and("api." + key).in(value instanceof String ? Arrays.asList((String) value) : (List<String>) value);
                        }
                    } else if ("reqDataLabels".equals(key)) {
                        List<String> labels = (List<String>) value;
                        if (labels.size() > 0 && labels.get(0).equals("ALL")) {
                            labels = dataLabelIds;
                        }
                        if (map.containsKey("fieldOperateMap")) {
                            Map<String, String> fieldOperateMap = (Map<String, String>) map.get("fieldOperateMap");
                            if (fieldOperateMap.containsKey("reqDataLabels")) {
                                String classificationsOperator = fieldOperateMap.get("reqDataLabels");
                                if (DBOperatorEnum.OR.operator().equals(classificationsOperator)) {
                                    criteria.and("api." + key).in(labels);
                                } else {
                                    criteria.and("api." + key).all(labels);
                                }
                            } else {
                                criteria.and("api." + key).in(labels);
                            }
                        } else {
                            criteria.and("api." + key).in(labels);
                        }
                    } else if ("rspDataLabels".equals(key)) {
                        List<String> labels = (List<String>) value;
                        if (labels.size() > 0 && labels.get(0).equals("ALL")) {
                            labels = dataLabelIds;
                        }
                        if (map.containsKey("fieldOperateMap")) {
                            Map<String, String> fieldOperateMap = (Map<String, String>) map.get("fieldOperateMap");
                            if (fieldOperateMap.containsKey("rspDataLabels")) {
                                String classificationsOperator = fieldOperateMap.get("rspDataLabels");
                                if (DBOperatorEnum.OR.operator().equals(classificationsOperator)) {
                                    criteria.and("api." + key).in(labels);
                                } else {
                                    criteria.and("api." + key).all(labels);
                                }
                            } else {
                                criteria.and("api." + key).in(labels);
                            }
                        } else {
                            criteria.and("api." + key).in(labels);
                        }
                    } else if ("visitDomains".equals(key)) {
                        if (map.containsKey("fieldOperateMap")) {
                            Map<String, String> fieldOperateMap = (Map<String, String>) map.get("fieldOperateMap");
                            if (fieldOperateMap.containsKey("visitDomains")) {
                                String classificationsOperator = fieldOperateMap.get("visitDomains");
                                if (DBOperatorEnum.OR.operator().equals(classificationsOperator)) {
                                    criteria.and("api." + key).in(value instanceof String ? Arrays.asList((String) value) : (List<String>) value);
                                } else {
                                    criteria.and("api." + key).all(value instanceof String ? Arrays.asList((String) value) : (List<String>) value);
                                }
                            } else {
                                criteria.and("api." + key).in(value instanceof String ? Arrays.asList((String) value) : (List<String>) value);
                            }
                        } else {
                            criteria.and("api." + key).in(value instanceof String ? Arrays.asList((String) value) : (List<String>) value);
                        }
                    } else if ("deployDomains".equals(key)) {
                        if (map.containsKey("fieldOperateMap")) {
                            Map<String, String> fieldOperateMap = (Map<String, String>) map.get("fieldOperateMap");
                            if (fieldOperateMap.containsKey("deployDomains")) {
                                String classificationsOperator = fieldOperateMap.get("deployDomains");
                                if (DBOperatorEnum.OR.operator().equals(classificationsOperator)) {
                                    criteria.and("api." + key).in(value instanceof String ? Arrays.asList((String) value) : (List<String>) value);
                                } else {
                                    criteria.and("api." + key).all(value instanceof String ? Arrays.asList((String) value) : (List<String>) value);
                                }
                            } else {
                                criteria.and("api." + key).in(value instanceof String ? Arrays.asList((String) value) : (List<String>) value);
                            }
                        } else {
                            criteria.and("api." + key).in(value instanceof String ? Arrays.asList((String) value) : (List<String>) value);
                        }
                    } else if ("terminals".equals(key)) {
                        if (map.containsKey("fieldOperateMap")) {
                            Map<String, String> fieldOperateMap = (Map<String, String>) map.get("fieldOperateMap");
                            if (fieldOperateMap.containsKey("terminals")) {
                                String terminalsOperator = fieldOperateMap.get("terminals");
                                if (DBOperatorEnum.OR.operator().equals(terminalsOperator)) {
                                    criteria.and("api." + key).in(value instanceof String ? Arrays.asList((String) value) : (List<String>) value);
                                } else {
                                    criteria.and("api." + key).all(value instanceof String ? Arrays.asList((String) value) : (List<String>) value);
                                }
                            } else {
                                criteria.and("api." + key).in(value instanceof String ? Arrays.asList((String) value) : (List<String>) value);
                            }
                        } else {
                            criteria.and("api." + key).in(value instanceof String ? Arrays.asList((String) value) : (List<String>) value);
                        }
                    } else if ("methods".equals(key)) {
                        criteria.and("api." + key).all(value instanceof String ? Arrays.asList((String) value) : (List<String>) value);
                    } else if ("rspContentTypes".equals(key)) {
                        //API3.0 返回类型筛选支持多选
                        criteria.and("api." + key).in(value instanceof String ? Arrays.asList((String) value) : (List<String>) value);
                    } else if ("departments".equals(key)) {
                        criteria.and("api." + key + ".department").is(value);
                    } else if ("orderFlag".equals(key)) {
                        criteria.and("orderFlag").is(value);
                    } else if ("id".equals(key)) {
                        criteria.and("_id").is(value);
                    } else if ("ids".equals(key)) {
                        criteria.and("_id").in(value instanceof String ? Arrays.asList((String) value) : (List<String>) value);
                    } else if ("apiType".equals(key)) {
                        //API3.0 api类型筛选 支持多选
                        criteria.and("api." + key).in(value instanceof String ? Arrays.asList((String) value) : (List<String>) value);
                    } else if ("uaTypes".equals(key)) {
                        //API3.0 终端类型筛选 支持多选
                        criteria.and("api." + key).in(value instanceof String ? Arrays.asList((String) value) : (List<String>) value);
                    } else if ("delFlag".equals(key)) {
                        criteria.and("delFlag").is(value);
                        delFlag = true;
                    } else if ("nodeId".equals(key)) {
                        criteria.and("nodes.nid").is(value);
                    }
                }
            }

            if (!delFlag) {
                criteria.and("delFlag").is(false);
            }
            map.remove("delFlag");
            if (DataUtil.isNotEmpty(map.get("lastTimestampStart")) && DataUtil.isNotEmpty(map.get("lastTimestampEnd"))) {
                //API3.0 最近活跃时间筛选
                org.springframework.data.mongodb.core.query.Criteria criteria1 = new org.springframework.data.mongodb.core.query.Criteria();
                org.springframework.data.mongodb.core.query.Criteria criteria2 = new org.springframework.data.mongodb.core.query.Criteria();
                criteria1.and("lastTimestamp").gte(map.get("lastTimestampStart"));
                criteria2.and("lastTimestamp").lte(map.get("lastTimestampEnd"));
                andCriteriaList.add(criteria1);
                andCriteriaList.add(criteria2);
            }
            if (DataUtil.isNotEmpty(map.get("earlyTimestampStart")) && DataUtil.isNotEmpty(map.get("earlyTimestampEnd"))) {
                //API3.0 首次发现时间筛选
                org.springframework.data.mongodb.core.query.Criteria criteria1 = new org.springframework.data.mongodb.core.query.Criteria();
                org.springframework.data.mongodb.core.query.Criteria criteria2 = new org.springframework.data.mongodb.core.query.Criteria();
                criteria1.and("earlyTimestamp").gte(map.get("earlyTimestampStart"));
                criteria2.and("earlyTimestamp").lte(map.get("earlyTimestampEnd"));
                andCriteriaList.add(criteria1);
                andCriteriaList.add(criteria2);
            }
            if (DataUtil.isNotEmpty(map.get("startTime")) && DataUtil.isNotEmpty(map.get("endTime"))) {
                org.springframework.data.mongodb.core.query.Criteria criteria1 = new org.springframework.data.mongodb.core.query.Criteria();
                org.springframework.data.mongodb.core.query.Criteria criteria2 = new org.springframework.data.mongodb.core.query.Criteria();
                criteria1.and("fixedTimestamp").gte(map.get("startTime"));
                criteria2.and("fixedTimestamp").lte(map.get("endTime"));
                andCriteriaList.add(criteria1);
                andCriteriaList.add(criteria2);
            }
            if (DataUtil.isNotEmpty(map.get("endUpdateTime")) && DataUtil.isNotEmpty(map.get("startUpdateTime"))) {
                //API3.0 最近活跃时间筛选
                org.springframework.data.mongodb.core.query.Criteria criteria1 = new org.springframework.data.mongodb.core.query.Criteria();
                org.springframework.data.mongodb.core.query.Criteria criteria2 = new org.springframework.data.mongodb.core.query.Criteria();
                criteria1.and("updateTime").gte(map.get("startUpdateTime"));
                criteria2.and("updateTime").lte(map.get("endUpdateTime"));
                andCriteriaList.add(criteria1);
                andCriteriaList.add(criteria2);
                criteria.andOperator(
                        org.springframework.data.mongodb.core.query.Criteria.where("updateTime").lte(map.get("endUpdateTime")));
            }

            if (DataUtil.isNotEmpty(map.get("isAssetAuthorization")) && (boolean) map.get("isAssetAuthorization") == true) {

                if (DataUtil.isNotEmpty(map.get("appUriSet")) || DataUtil.isNotEmpty(map.get("departmentSet"))) {

                    org.springframework.data.mongodb.core.query.Criteria criteria1 = null;
                    org.springframework.data.mongodb.core.query.Criteria criteria2 = null;

                    if (DataUtil.isNotEmpty(map.get("appUriSet"))) {
                        criteria1 = new org.springframework.data.mongodb.core.query.Criteria();
                        criteria1.and("appUri").in(map.get("appUriSet"));
                    }

                    if (DataUtil.isNotEmpty(map.get("departmentSet"))) {
                        criteria2 = new org.springframework.data.mongodb.core.query.Criteria();
                        criteria2.and("api.departments.department").in(map.get("departmentSet"));
                    }

                    if (DataUtil.isNotEmpty(criteria1) && DataUtil.isEmpty(criteria2)) {
                        criteria.orOperator(criteria1);
                    } else if (DataUtil.isEmpty(criteria1) && DataUtil.isNotEmpty(criteria2)) {
                        criteria.orOperator(criteria2);
                    } else if (DataUtil.isNotEmpty(criteria1) && DataUtil.isNotEmpty(criteria2)) {
                        criteria.orOperator(criteria1, criteria2);
                    }
                } else {
                    criteria.and("_id").is("");
                }
            }
            if (!andCriteriaList.isEmpty()) {
                criteria.andOperator(andCriteriaList.toArray(new org.springframework.data.mongodb.core.query.Criteria[andCriteriaList.size()]));
            }
        }
        queryAdapterRegistry.module(Module.WEAKNESS).query(map, criteria);
        return criteria;
    }

    public void updateWeaknessOrderFlag(MetabaseQuery metabaseQuery, ResourceUpdates resourceUpdates) {
        metabaseClientTemplate.update(metabaseQuery, resourceUpdates, false, ApiWeakness.class);
    }


}
