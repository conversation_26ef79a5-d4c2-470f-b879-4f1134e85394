package com.quanzhi.auditapiv2.common.dal.dto;

import lombok.Data;

import java.io.Serializable;

/**
 * @ClassName UserDepartDto
 * @Description TODO
 * <AUTHOR>
 * @Date 2020/10/16 17:24
 **/
@Data
public class UserDepartCriteriaDto implements Serializable {

    /**
     * 部门
     */
    private String staffDepart;

    /**
     * 昵称
     */
    private String staffNickName;

    /**
     * 姓名
     */
    private String staffName;

    /**
     * 中文名称
     */
    private String staffChinese;

    /**
     * 身份证号
     */
    private String staffIdCard;

    /**
     * 银行卡号
     */
    private String staffBankCard;

    /**
     * 邮箱
     */
    private String staffEmail;

    /**
     * 员工id
     */
    private String staffId;

    private String firstDate;

    private String lastDate;

    private String staffRole;

    /**
     * 手机号
     */
    private String staffMobile;

    private Integer page;

    private Integer limit;

    private String field;

    private Integer sort;

    /**
     * 导出字段
     */
    private String titles;
}
