package com.quanzhi.auditapiv2.common.dal.dao.impl;

import com.quanzhi.auditapiv2.common.dal.dao.RiskIpStrategyDao;
import com.quanzhi.auditapiv2.common.dal.entity.riskEolink.RiskIpStrategyDto;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Repository;

@Repository
public class RiskIpStrategyDaoImpl implements RiskIpStrategyDao {

    @Autowired
    private MongoTemplate mongoTemplate;

    private String collectionName = "riskIpStrategy";

    @Override
    public RiskIpStrategyDto save(RiskIpStrategyDto dto) {
        return mongoTemplate.save(dto, collectionName);
    }

    @Override
    public RiskIpStrategyDto findByIp(String ip) {
        Criteria criteria = Criteria.where("ip").is(ip);

        return mongoTemplate.findOne(new Query(criteria), RiskIpStrategyDto.class, collectionName);
    }

    @Override
    public RiskIpStrategyDto findByAccount(String account) {
        Criteria criteria = Criteria.where("account").is(account);

        return mongoTemplate.findOne(new Query(criteria), RiskIpStrategyDto.class, collectionName);
    }

    @Override
    public RiskIpStrategyDto findByUuid(String uuid) {
        Criteria criteria = Criteria.where("strategyUuid").is(uuid);

        return mongoTemplate.findOne(new Query(criteria), RiskIpStrategyDto.class, collectionName);

    }

    @Override
    public RiskIpStrategyDto update(RiskIpStrategyDto dto) {
        return null;
    }

    @Override
    public void deleteByIp(String ip) {
        Criteria criteria = Criteria.where("ip").is(ip);

        mongoTemplate.remove(new Query(criteria), collectionName);
    }

    @Override
    public void deleteByStrategyId(String strategyId) {
        Criteria criteria = Criteria.where("strategyId").is(strategyId);

        mongoTemplate.remove(new Query(criteria), collectionName);
    }
}
