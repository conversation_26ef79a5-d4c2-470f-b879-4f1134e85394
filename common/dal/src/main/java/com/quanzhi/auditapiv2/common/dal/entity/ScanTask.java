package com.quanzhi.auditapiv2.common.dal.entity;

import com.alibaba.fastjson.JSONObject;
import com.quanzhi.auditapiv2.common.util.dto.ListOutputDto;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 *
 * 《报告任务》
 *
 *
 * @Project:
 * @Module ID:
 * @Comments:
 * @JDK version used:      <JDK1.8> 
 * <AUTHOR> [<EMAIL>]
 * @since 2019-07-23-上午9:09:10
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ScanTask {


    private String id;

    /**
     * 任务code
     */
    @ApiModelProperty(value = "任务code", name = "code")
    private String code;

    /**
     * 任务模板名称
     */
    @ApiModelProperty(value = "任务模板名称", name = "name")
    private String name;

    /**
     * 任务描述
     */
    @ApiModelProperty(value = "任务描述", name = "description")
    private String description;

    /**
     * 报表插件运行需要的入参，以json字符串存储
     */
    @ApiModelProperty(value = "报表插件运行需要的入参，以json字符串存储", name = "params_json")
    private JSONObject paramsJson;

    private String params;

    /**
     * 报表插件元数据信息，以json字符串存储
     */
    @ApiModelProperty(value = "报表插件元数据信息，以json字符串存储", name = "meta_json")
    private JSONObject metaJson;

    /**
     * 保留字段，为后续功能的扩展，目前没有用到
     * 0-初始化，1-已开始，2-已停止
     */
    private Integer status;

    /**
     * 定时任务的cron表达式
     */
    @ApiModelProperty(value = "报表定时运行需要的cron表达式", name = "cron")
    private String cron;

    @ApiModelProperty(value = "插入时间", name = "insertTime")
    private Long insertTime;
    @ApiModelProperty(value = "更新时间", name = "updateTime")
    private Long updateTime;

    /**
     * 任务版本
     */
    @ApiModelProperty(value = "任务版本", name = "version")
    private String version;

    /**
     * 策略code
     */
    @ApiModelProperty(value = "策略code", name = "policyCode")
    private String policyCode;

    /**
     * 策略code
     */
    @ApiModelProperty(value = "0单次运行，1周期运行", name = "policy")
    private Integer policy;

    /**
     * 插件解压路径
     */
    private String archivePath;

    /**
     * 解压文件hash值 用于判断该文件是否为最新的文件
     */
    private String fileHash;

    /**
     * 单次运行数据开始时间
     */
    private String scan_startDate;

    /**
     * 单次运行数据结束时间
     */
    private String scan_endDate;

    /**
     * 查询报表列表同时返回对应记录
     */
    private ListOutputDto<ScanTaskResult> scanTaskResultListOutputDto;

    /**
     * 下一次执行任务名
     */
    private String lastScanTaskResultName;

    /**
     * 报表类型
     */
    private ReportType reportType;


    public enum ReportType{
        PYTHON, AMIS
    }
    /**
     * amis报告模板数据
     */
    private String amisReportJson;
}
