package com.quanzhi.auditapiv2.common.dal.dao.impl;

import com.quanzhi.auditapiv2.common.dal.dao.ITrafficStatisticsDao;
import com.quanzhi.auditapiv2.common.dal.entity.TrafficStatistics;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.aggregation.Aggregation;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * @author: yang<PERSON>xian
 * @date: 21/3/2023 18:03
 * @description:
 */
@Repository
public class TrafficStatisticsDaoImpl implements ITrafficStatisticsDao {

    private final MongoTemplate mongoTemplate;

    private String collectionName = "trafficStatistics";

    public TrafficStatisticsDaoImpl(MongoTemplate mongoTemplate) {
        this.mongoTemplate = mongoTemplate;
    }

    @Override
    public List<TrafficStatistics> getTrafficStatisticsList(String startDate, String endDate) {
        Aggregation aggregation = Aggregation.newAggregation(
                Aggregation.match(Criteria.where("date").gte(startDate).lte(endDate)),
                Aggregation.group("date")
                           .first("date").as("date")
                           .sum("count").as("count")
        );
        return mongoTemplate.aggregate(aggregation, collectionName, TrafficStatistics.class).getMappedResults();
    }

    @Override
    public TrafficStatistics getTrafficStatisticByDate(String date) {
        return mongoTemplate.findOne(new Query().addCriteria(Criteria.where("date").is(date)), TrafficStatistics.class, collectionName);
    }

}
