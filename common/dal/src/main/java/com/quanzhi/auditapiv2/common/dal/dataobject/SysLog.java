package com.quanzhi.auditapiv2.common.dal.dataobject;

import com.alibaba.fastjson.JSONObject;
import com.quanzhi.audit_core.common.model.Position;
import lombok.Data;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

import java.io.Serializable;

/**
 * @Description:
 * @Author: danniel.yu
 * @Date: 2020-05-11 15:24
 */
@Data
@Document(collection = "SysLog")
public class SysLog implements Serializable {
    private static final long serialVersionUID = 1L;

//    /**
//     * 日志id
//     */
//    private ObjectId id;

    /**
     * 日志id
     */
    @Id
    private String id;

    /**
     * 操作用户id
     */
    private String userId;

    /**
     * 操作用户
     */
    private String username;

    /**
     * 操作用户类型(1-本地用户，2-API安全运营中心用户)
     * 
     * @see UserTypeEnum
     */
    private Integer userType;

    /**
     * 操作用户类型名称
     */
    private String userTypeName;

    /**
     * 操作
     */
    private String operation;

    /**
     * 方法
     */
    private String method;

    /**
     * 参数
     */
    private String params;

    /**
     * 耗时
     */
    private Long time;

    /**
     * 操作ip地址
     */
    private String ip;

    /**
     * 创建时间
     */
    private Long gmtCreate;

    /**
     * 地域
     */
    private String region;

    private Position position;

    /**
     * 所属模块
     * @see ModuleEnum
     */
    private String module;

    private String url;

    private Object[] args;

    private JSONObject response;

    private String type;

    /**
     * 操作描述
     */
    private String description;

    /**
     * 操作用户类型
     */
    public static enum UserTypeEnum {

        /**
         * 本地用户
         */
        LOCAL(1, "本地用户"),

        /**
         * API安全运营中心用户
         */
        OPERATION(2, "API安全运营中心用户");

        private Integer code;

        private String name;

        UserTypeEnum(int code, String name) {
            
            this.code = code;
            this.name = name;
        }

        public int getCode() {
            
            return this.code;
        }

        public String getName() {
            
            return this.name;
        }
    }


    public enum ModuleEnum {

        OPEN_API("数据开放"),

        LOGIN_PAGE("登录页面"),

        USER_PAGE("修改个人信息页面"),

        HOME("首页"),

        DISCOVER_APP("发现-应用"),

        APP_FEATURE("应用-应用画像"),

        DISCOVER_API("发现-API"),

        API_FEATURE("API-API画像"),

        DISCOVER_IP("发现-IP"),

        DISCOVER_ACCOUNT("发现-账号"),

        DISCOVER_FILE("发现-文件"),

        FILE_FEATURE("文件-文件画像"),

        OPERATION_WEAKNESS("运营-弱点"),

        OPERATION_RISK("运营-风险"),

        ANALYSIS_EVENT_TRACE("分析-数据溯源"),

        ANALYSIS_BODY_TRACE("分析-主体溯源"),

        REPORT_OPERATION("报告-运营报告"),

        REPORT_OFFLINE("报告-离线报告"),

        ORIGIN_IP_CONFIG("源IP解析配置"),

        DST_IP_CONFIG("源IP解析配置"),

        RESOURCE_FILTER("资产过滤"),

        EVENT_FILLTER("事件过滤"),

        NETWORD_CONFIG("网段配置"),

        REGION_CONFIG("地域配置"),

        NOTIFY_MANAGE("通知管理"),

        SUBSCRIBE_CONFIG("订阅管理"),

        DATA_SYSLOG_CONFIG("数据同步配置"),

        EXPORT_MANAGE("数据导出管理"),

        DATA_LABEL("数据标签"),

        DATA_CLEAN("数据清理"),

        RECOMMEND_POLICY("推荐策略"),

        SAVE_POLICY("留存策略"),

        WEAKNESS_RULE("弱点规则"),

        RISK_POLICY("风险策略"),

        ACCOUNT_ANALYSIS_CONFIG("账号解析配置"),

        ACCOUNT_DEPART_CONFIG("账号架构配置"),

        RESOUECE_DEFINITION("资产定义"),

        RESOURCE_CLASSIFICATION("资产分类"),

        RESOURCE_FEATURELABEL("资产标签"),

        RESOURCE_LEVEL("资产分级"),

        USTYPE_CONFIG("终端配置"),

        BASIC_CONFIG("基础配置"),

        SYS_LINCENSE("系统授权"),

        SYS_UPDATE("系统升级"),

        JOB_MANAGE("任务管理"),

        SYS_CLEAN("系统清理"),

        PLUGIN_MANAGE("插件管理"),


        CONFIG_GUIDE("配置向导"),

        TOOL_CONFIG("运维工具"),

        TOOL_GW("配置工具"),

        USER_MANAGE("用户管理"),

        ROLE_MANAGE("角色管理"),

        SELF_INFO("个人信息"),

        AUDIT_LOG("审计日志"),

        API_SAMPLE("API-API样例");

        private String name;

        private ModuleEnum(String name) {
            this.name = name;
        }

        public String val() {
            return this.name;
        }
    }
}
