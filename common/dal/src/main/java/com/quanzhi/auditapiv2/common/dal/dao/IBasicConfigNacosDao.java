package com.quanzhi.auditapiv2.common.dal.dao;

import com.quanzhi.auditapiv2.common.dal.dao.base.INacosBaseDao;
import com.quanzhi.auditapiv2.common.dal.entity.BasicConfig;
import com.quanzhi.auditapiv2.common.dal.entity.VersionConfigModel;
import org.springframework.stereotype.Repository;

/**
 * 
 * 《基础配置持久层Nacos接口》
 * 
 * 
 * @Project: 
 * @Module ID:
 * @Comments: 
 * @JDK version used:      <JDK1.8> 
 * <AUTHOR> [<EMAIL>]
 * @since 2020-04-08-上午9:56:10
 */
@Repository
public interface IBasicConfigNacosDao extends INacosBaseDao<BasicConfig> {
    VersionConfigModel getVersionConfig();
    VersionConfigModel getVersionByName(String name);

    void saveVersionConfig(VersionConfigModel versionConfigModel) throws Exception;
}
