package com.quanzhi.auditapiv2.common.dal.dto;

import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@NoArgsConstructor
public class RuleGroupDTO implements Serializable {

    private String id;

    /**
     * 标识是哪个规则的类别: APP_LABEL-应用标签，API_LABEL-API标签，WEAKNESS_RULE-弱点规则
     */
    private String category;

    /**
     * 内置-BUILT，自定义-CUSTOM
     */
    private String type;

    /**
     * 类别名称
     */
    private String groupName;

    /**
     * group-id，新增时通过md5生成
     */
    private String group;

    private Long createTime;

    private Long updateTime;

    private Boolean delFlag = false;
}
