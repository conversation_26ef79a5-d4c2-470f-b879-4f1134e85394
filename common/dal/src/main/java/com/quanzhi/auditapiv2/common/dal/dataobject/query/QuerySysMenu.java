package com.quanzhi.auditapiv2.common.dal.dataobject.query;

import com.quanzhi.auditapiv2.common.dal.common.entity.QueryBase;
import lombok.Data;
import org.bson.types.ObjectId;

/**
 * @Description:
 * @Author: danniel.yu
 * @Date: 2020-05-11 15:23
 */
@Data
public class QuerySysMenu  extends QueryBase {
    /**
     * 菜单id
     */
    private ObjectId id;

    /**
     * 菜单名称
     */
    private String name;

    /**
     * 菜单url
     */
    private String url;

    /**
     * 类型(0：目录   1：菜单   2：按钮)
     */
    private Integer type;

}
