package com.quanzhi.auditapiv2.common.dal.dao.impl;

import com.alibaba.fastjson.JSONObject;
import com.mongodb.client.MongoCursor;
import com.quanzhi.audit_core.common.model.AccountInfo;
import com.quanzhi.audit_core.common.model.AssetLifeStateConfig;
import com.quanzhi.auditapiv2.common.dal.dao.IHttpAppDao;
import com.quanzhi.auditapiv2.common.dal.dao.base.impl.MetabaseDaoImpl;
import com.quanzhi.auditapiv2.common.dal.dao.convert.AssetLifeStateConfigConvert;
import com.quanzhi.auditapiv2.common.dal.dao.convert.SpecialQueryFieldConvert;
import com.quanzhi.auditapiv2.common.dal.dao.impl.common.CustomPropertySearchAdapter;
import com.quanzhi.auditapiv2.common.dal.dao.impl.common.Module;
import com.quanzhi.auditapiv2.common.dal.dao.impl.common.QueryAdapterRegistry;
import com.quanzhi.auditapiv2.common.dal.dto.app.AppAccountDto;
import com.quanzhi.auditapiv2.common.dal.dto.app.AppRelationDto;
import com.quanzhi.auditapiv2.common.dal.dto.query.HttpAppCriteriaDto;
import com.quanzhi.auditapiv2.common.dal.dto.query.QueryHelper;
import com.quanzhi.auditapiv2.common.dal.enums.DBOperatorEnum;
import com.quanzhi.auditapiv2.common.dal.enums.TagsEnum;
import com.quanzhi.auditapiv2.common.util.dto.AggregationDto;
import com.quanzhi.auditapiv2.common.util.dto.GroupDto;
import com.quanzhi.auditapiv2.common.util.utils.ConstantUtil;
import com.quanzhi.auditapiv2.common.util.utils.DataUtil;
import com.quanzhi.auditapiv2.common.util.utils.UrlUtil;
import com.quanzhi.metabase.core.model.enums.AssetFlagEnum;
import com.quanzhi.metabase.core.model.http.*;
import com.quanzhi.metabase.core.model.http.app.HttpAppIcon;
import com.quanzhi.metabase.core.model.http.constant.AssetLifeStateEnum;
import com.quanzhi.metabase.core.model.http.constant.LevelEnum;
import com.quanzhi.metabase.core.model.query.*;
import lombok.extern.slf4j.Slf4j;
import org.bson.Document;
import org.bson.types.ObjectId;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.aggregation.Aggregation;
import org.springframework.data.mongodb.core.aggregation.AggregationOperation;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.stereotype.Repository;
import org.springframework.util.CollectionUtils;

import java.lang.reflect.Field;
import java.util.*;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import static org.springframework.data.mongodb.core.aggregation.Aggregation.newAggregationOptions;

/**
 * 《应用持久层接口实现》
 *
 * <AUTHOR> [<EMAIL>]
 * @Project:
 * @Module ID:
 * @Comments:
 * @JDK version used:      <JDK1.8>
 * @since 2020-04-08-上午9:56:10
 */
@Repository
@Slf4j
public class HttpAppDaoImpl extends MetabaseDaoImpl<HttpAppResource> implements IHttpAppDao {

    @Autowired
    private MongoTemplate mongoTemplate;
    @Autowired
    public CustomPropertySearchAdapter customPropertySearchAdapter;
    @Autowired
    public QueryAdapterRegistry queryAdapterRegistry;
    @Autowired
    public AssetLifeStateConfigConvert assetLifeStateConfigConvert;
    @Autowired
    public SpecialQueryFieldConvert specialQueryFieldConvert;

    private String collectionName = "httpApp";

    /**
     * 查询应用列表(分页)
     *
     * @param
     * @return
     * @Comments: <对此方法的描述，可以引用系统设计中的描述>
     * <AUTHOR> [<EMAIL>]
     * @since 2020-04-08 9:56
     */
    @Override
    public List<HttpAppResource> selectHttpAppList(Map<String, Object> map, String field, Integer sort, Integer page, Integer limit) throws Exception {

        MetabaseQuery metabaseQuery = getMetabaseQuery(map);

        if (DataUtil.isNotEmpty(page) && DataUtil.isNotEmpty(field) && DataUtil.isNotEmpty(sort) && DataUtil.isNotEmpty(limit)) {
            //分页field
            metabaseQuery.skip((page - 1) * limit);
            metabaseQuery.limit(limit);
            //排序
            if (DataUtil.isNotEmpty(field) && DataUtil.isNotEmpty(sort)) {

                if (sort == ConstantUtil.Sort.ASC) {
                    metabaseQuery.sort(Sort.by("followed", SortOrder.DESC), Sort.by(field, SortOrder.ASC));
                } else if (sort == ConstantUtil.Sort.DESC) {
                    metabaseQuery.sort(Sort.by("followed", SortOrder.DESC), Sort.by(field, SortOrder.DESC));
                } else {
                    metabaseQuery.sort(Sort.by("followed", SortOrder.DESC), Sort.by("appStat.apiCount", SortOrder.DESC));
                }
            } else {
                metabaseQuery.sort(Sort.by("followed", SortOrder.DESC), Sort.by("appStat.apiCount", SortOrder.DESC));
            }
        }
        return metabaseClientTemplate.find(metabaseQuery, HttpAppResource.class);
    }

    @Override
    public List<HttpAppResource> selectHttpAppListByFeatureLabel(String featureLabel) throws Exception {
        MetabaseQuery metabaseQuery = new MetabaseQuery();
        metabaseQuery.where("delFlag", Predicate.IS, false);
        metabaseQuery.where("featureLabels", Predicate.IS, featureLabel);

        return metabaseClientTemplate.find(metabaseQuery, HttpAppResource.class);
    }

    @Override
    public List<HttpAppResource> selectHttpAppListByDataLabel(String dataLabel) throws Exception {
        MetabaseQuery metabaseQuery = new MetabaseQuery();
        metabaseQuery.where("delFlag", Predicate.IS, false);
        metabaseQuery.where("dataLabels", Predicate.IS, dataLabel);

        return metabaseClientTemplate.find(metabaseQuery, HttpAppResource.class);
    }

    @Override
    public void updateHttpAppByFeatureLabel(String label) throws Exception {
        List<HttpAppResource> httpAppResources = this.selectHttpAppListByFeatureLabel(label);
        for (HttpAppResource httpAppResource : httpAppResources) {
            httpAppResource.getFeatureLabels().remove(label);
            Update update = new Update();
            update.set("featureLabels", httpAppResource.getFeatureLabels());
            mongoTemplate.upsert(new Query().addCriteria(org.springframework.data.mongodb.core.query.Criteria
                    .where("_id").is(httpAppResource.getId())), update, collectionName);
        }
    }

    @Override
    public void updateHttpAppByDataLabel(String label) throws Exception {
        List<HttpAppResource> httpAppResources = this.selectHttpAppListByDataLabel(label);
        for (HttpAppResource httpAppResource : httpAppResources) {
            httpAppResource.getDataLabels().remove(label);
            Update update = new Update();
            update.set("dataLabels", httpAppResource.getDataLabels());
            mongoTemplate.upsert(new Query().addCriteria(org.springframework.data.mongodb.core.query.Criteria
                    .where("_id").is(httpAppResource.getId())), update, collectionName);
        }
    }

    /**
     * 查询应用数量
     *
     * @param
     * @return
     * @Comments: <对此方法的描述，可以引用系统设计中的描述>
     * <AUTHOR> [<EMAIL>]
     * @since 2020-04-08 9:56
     */
    @Override
    public Long totalCount(Map<String, Object> map) throws Exception {

        MetabaseQuery metabaseQuery = getMetabaseQuery(map);

        return metabaseClientTemplate.count(metabaseQuery, HttpAppResource.class);
    }

    /**
     * 查询应用分组
     *
     * @param
     * @return
     * @Comments: <对此方法的描述，可以引用系统设计中的描述>
     * <AUTHOR> [<EMAIL>]
     * @since 2020-04-08 9:56
     */
    @Override
    public List<AggregationResult> selectHttpAppGroup(Map<String, Object> map, Integer sort, String[] groupFields) throws Exception {

        MetabaseQuery metabaseQuery = getMetabaseQuery(map);
        if ("appLifeFlag".equals(groupFields[0])) {
            //应用状态分组单独处理
            List<AggregationResult> results = new ArrayList<>();
            //新增
            AggregationResult newAppResult = new AggregationResult();
            newAppResult.setId(String.valueOf(AssetLifeStateEnum.NEW.getNum()));
            map.put("appLifeFlag", Arrays.asList(AssetLifeStateEnum.NEW.getNum()));
            metabaseQuery = getMetabaseQuery(map);
            long newAppcount = getCount(metabaseQuery);
            newAppResult.setResultAlias(newAppcount);
            results.add(newAppResult);

            //活跃
            AggregationResult activeAppResult = new AggregationResult();
            activeAppResult.setId(String.valueOf(AssetLifeStateEnum.ACTIVE.getNum()));
            map.put("appLifeFlag", Arrays.asList(AssetLifeStateEnum.ACTIVE.getNum()));
            metabaseQuery = getMetabaseQuery(map);
            long activeAppcount = getCount(metabaseQuery);
            activeAppResult.setResultAlias(activeAppcount);
            results.add(activeAppResult);

            //失活
            AggregationResult inactiveAppResult = new AggregationResult();
            inactiveAppResult.setId(String.valueOf(AssetLifeStateEnum.INACTIVE.getNum()));
            map.put("appLifeFlag", Arrays.asList(AssetLifeStateEnum.INACTIVE.getNum()));
            metabaseQuery = getMetabaseQuery(map);
            long inactiveAppcount = getCount(metabaseQuery);
            inactiveAppResult.setResultAlias(inactiveAppcount);
            results.add(inactiveAppResult);

            //复活
            AggregationResult reviveAppResult = new AggregationResult();
            reviveAppResult.setId(String.valueOf(AssetLifeStateEnum.REVIVE.getNum()));
            map.put("appLifeFlag", Arrays.asList(AssetLifeStateEnum.REVIVE.getNum()));
            metabaseQuery = getMetabaseQuery(map);
            long reviveAppcount = getCount(metabaseQuery);
            reviveAppResult.setResultAlias(reviveAppcount);
            results.add(reviveAppResult);

            return results;
        }
        //展开字段
        for (String groupField : groupFields) {

            if ("featureLabels".equals(groupField)) {
                metabaseQuery.setUnwind("featureLabels");
            } else if ("departments.department".equals(groupField)) {
                metabaseQuery.setUnwind("departments");
            } else if ("visitDomains".equals(groupField)) {
                metabaseQuery.setUnwind("visitDomains");
            } else if ("deployDomains".equals(groupField)) {
                metabaseQuery.setUnwind("deployDomains");
            } else if ("flowSources".equals(groupField)) {
                metabaseQuery.setUnwind("flowSources");
            } else if ("appLifeFlag".equals(groupField)) {
                metabaseQuery.setUnwind("appLifeFlag");
            } else if ("provinces".equals(groupField)) {
                metabaseQuery.setUnwind("provinces");
            }
        }

        //排序
        if (DataUtil.isNotEmpty(sort)) {

            if (sort == ConstantUtil.Sort.ASC) {
                metabaseQuery.sort(Sort.by("resultAlias", SortOrder.ASC));
            } else if (sort == ConstantUtil.Sort.DESC) {
                metabaseQuery.sort(Sort.by("resultAlias", SortOrder.DESC));
            } else {
                metabaseQuery.sort(Sort.by("resultAlias", SortOrder.DESC));
            }
        } else {
            metabaseQuery.sort(Sort.by("resultAlias", SortOrder.DESC));
        }

        MetabaseGroupOperation metabaseGroupOperation = new MetabaseGroupOperation();
        metabaseGroupOperation.setGroupFields(groupFields);
        metabaseGroupOperation.setAggrType("COUNT");

        return metabaseClientTemplate.aggregate(metabaseQuery, metabaseGroupOperation, HttpAppResource.class);
    }

    @Override
    public Long getCount(Long startTime, Long endTime) {
        MetabaseQuery metabaseQuery = new MetabaseQuery();
        metabaseQuery.where("delFlag", Predicate.IS, false);
        if (DataUtil.isNotEmpty(startTime) && DataUtil.isNotEmpty(endTime)) {
            Criteria criteria = new Criteria().andOperator(
                    Criteria.where("discoverTime").gte(startTime),
                    Criteria.where("discoverTime").lte(endTime));
            metabaseQuery.getCriteria().add(criteria);
        }
        return metabaseClientTemplate.count(metabaseQuery, HttpAppResource.class);
    }

    /**
     * id查询应用详情
     *
     * @param
     * @return
     * @Comments: <对此方法的描述，可以引用系统设计中的描述>
     * <AUTHOR> [<EMAIL>]
     * @since 2020-04-08 9:56
     */
    @Override
    public HttpAppResource selectHttpAppById(String id) throws Exception {

        return metabaseClientTemplate.findOne(id, HttpAppResource.class);
    }

    /**
     * appUri查询应用详情
     *
     * @param
     * @return
     * @Comments: <对此方法的描述，可以引用系统设计中的描述>
     * <AUTHOR> [<EMAIL>]
     * @since 2020-04-08 9:56
     */
    @Override
    public HttpAppResource selectHttpAppByAppUri(String appUri) throws Exception {

        MetabaseQuery metabaseQuery = new MetabaseQuery();
        metabaseQuery.where("uri", Predicate.IN, appUri);
        metabaseQuery = andDelFlag(metabaseQuery);

        return metabaseClientTemplate.findOne(metabaseQuery, HttpAppResource.class);
    }

    @Override
    public HttpAppResource selectHttpAppByUri(String appUri) throws Exception {
        MetabaseQuery metabaseQuery = new MetabaseQuery();
        metabaseQuery.where("uri", Predicate.IS, appUri);
        metabaseQuery = andDelFlag(metabaseQuery);

        return metabaseClientTemplate.findOne(metabaseQuery, HttpAppResource.class);
    }

    /**
     * hosts查询详情
     *
     * @param
     * @return
     * @Comments: <对此方法的描述，可以引用系统设计中的描述>
     * <AUTHOR> [<EMAIL>]
     * @since 2020-04-08 9:56
     */
    @Override
    public HttpAppResource selectHttpAppByHosts(String host) throws Exception {

        MetabaseQuery metabaseQuery = new MetabaseQuery();
        metabaseQuery.where("hosts", Predicate.IN, host);
        metabaseQuery = andDelFlag(metabaseQuery);

        return metabaseClientTemplate.findOne(metabaseQuery, HttpAppResource.class);
    }

    @Override
    public HttpAppResource selectHttpAppByHost(String host) throws Exception {
        MetabaseQuery metabaseQuery = new MetabaseQuery().alive();
        metabaseQuery.where("host", Predicate.IS, host);
        return metabaseClientTemplate.findOne(metabaseQuery, HttpAppResource.class);
    }

    /**
     * 应用名称查询详情
     *
     * @param
     * @return
     * @Comments: <对此方法的描述，可以引用系统设计中的描述>
     * <AUTHOR> [<EMAIL>]
     * @since 2020-04-08 9:56
     */
    @Override
    public HttpAppResource selectHttpAppByName(String name) throws Exception {

        MetabaseQuery metabaseQuery = new MetabaseQuery();
        //查询条件
        metabaseQuery.where("name", Predicate.IS, name);
        metabaseQuery = andDelFlag(metabaseQuery);

        return metabaseClientTemplate.findOne(metabaseQuery, HttpAppResource.class);
    }

    /**
     * parentAppId查询应用详情
     *
     * @param
     * @return
     * @Comments: <对此方法的描述，可以引用系统设计中的描述>
     * <AUTHOR> [<EMAIL>]
     * @since 2020-04-08 9:56
     */
    @Override
    public List<HttpAppResource> selectHttpAppByParentAppId(String parentAppId) throws Exception {

        MetabaseQuery metabaseQuery = new MetabaseQuery();
        //查询条件
        metabaseQuery.where("parentAppId", Predicate.IS, parentAppId);
        metabaseQuery = andDelFlag(metabaseQuery);

        return metabaseClientTemplate.findOne(metabaseQuery, HttpAppResource.class);
    }

    /**
     * 编辑应用
     *
     * @param
     * @return
     * @Comments: <对此方法的描述，可以引用系统设计中的描述>
     * <AUTHOR> [<EMAIL>]
     * @since 2020-04-08 9:56
     */
    @Override
    public HttpAppResource updateHttpApp(HttpAppResource httpAppResource) throws Exception {

        ResourceUpdates resourceUpdates = new ResourceUpdates();
        Field[] field = httpAppResource.getClass().getDeclaredFields();
        for (int i = 0; i < field.length; i++) {

            //设置是否允许访问，不是修改原来的访问权限修饰词。
            field[i].setAccessible(true);
            //字段值不为空 放入map
            if (field[i].get(httpAppResource) != null) {
                resourceUpdates.set(field[i].getName(), field[i].get(httpAppResource));
            }
        }
        if (DataUtil.isNotEmpty(httpAppResource.getDelFlag())) {
            resourceUpdates.set("delFlag", httpAppResource.getDelFlag());
            resourceUpdates.set("userDelFlag", httpAppResource.getDelFlag());
        }
        metabaseClientTemplate.update(httpAppResource.getId(), resourceUpdates, HttpAppResource.class);

        return httpAppResource;
    }


    @Override
    public void update(String id, Map<String, Object> updates) {
        metabaseClientTemplate.update(id, ResourceUpdates.create(updates), HttpAppResource.class);
    }

    @Override
    public List<AggregationResult> getHttpApiVisits(MetabaseQuery query, MetabaseGroupOperation metabaseGroupOperation, Class<HttpAppDateStat> httpAppDateStatClass) {
        return aggregate(query, metabaseGroupOperation, httpAppDateStatClass);
    }

    @Override
    public List<HttpAppResource> getNewAppList(Integer page, Integer limit, String field, Integer sort, Long startTime, Long endTime) {
        MetabaseQuery query = new MetabaseQuery();
        //查询条件
        query.where("delFlag", Predicate.IS, false);
        //分页
        query.setSkip((page - 1) * limit);
        query.limit(limit);
        if (DataUtil.isNotEmpty(startTime) && DataUtil.isNotEmpty(endTime)) {
            Criteria criteria = new Criteria();
            criteria = criteria.andOperator(
                    Criteria.where("discoverTime").gte(startTime),
                    Criteria.where("discoverTime").lte(endTime));
            query.getCriteria().add(criteria);
        }
        //排序
        if (DataUtil.isNotEmpty(field) && DataUtil.isNotEmpty(sort)) {

            if (sort == ConstantUtil.Sort.ASC) {
                query.sort(Sort.by(field, SortOrder.ASC));
            } else if (sort == ConstantUtil.Sort.DESC) {
                query.sort(Sort.by(field, SortOrder.DESC));
            } else {
                query.sort(Sort.by("discoverTime", SortOrder.DESC), Sort.by("followed", SortOrder.DESC));
            }
        } else {
            query.sort(Sort.by("discoverTime", SortOrder.DESC), Sort.by("followed", SortOrder.DESC));
        }
        return metabaseClientTemplate.find(query, HttpAppResource.class);
    }

    /**
     * 推荐解析和已启用解析数量
     *
     * @param uri
     * @param isParse 1:已启用数量
     * @return
     */
    @Override
    public Long countByUriAndIsParse(String uri, List<String> tags, int isParse) throws Exception {

        MetabaseQuery metabaseQuery = new MetabaseQuery();
        metabaseQuery.where("uri", Predicate.IS, uri);
        metabaseQuery.where("delFlag", Predicate.IS, false);
        if (!CollectionUtils.isEmpty(tags)) {
            metabaseQuery.where("tags", Predicate.IN, tags);
        }
        if (isParse == 1) {
            metabaseQuery.where("accParseEnable", Predicate.IS, true);
        }
        return metabaseClientTemplate.count(metabaseQuery, HttpApiResource.class);
    }

    @Override
    public List<HttpAppResource> getAccParseAppList(HttpAppCriteriaDto httpAppCriteriaDto) {
        Query queryDB = getAccParseAppQuery(httpAppCriteriaDto);

        //分页
        queryDB.skip((httpAppCriteriaDto.getPage().intValue() - 1) * httpAppCriteriaDto.getLimit().intValue());
        queryDB.limit(httpAppCriteriaDto.getLimit().intValue());

        org.springframework.data.domain.Sort sort = null;

        //排序
        if (DataUtil.isNotEmpty(httpAppCriteriaDto.getField()) && DataUtil.isNotEmpty(httpAppCriteriaDto.getSort())) {
            if (httpAppCriteriaDto.getSort().intValue() == ConstantUtil.Sort.ASC) {
                sort = org.springframework.data.domain.Sort.by(org.springframework.data.domain.Sort.Direction.ASC, httpAppCriteriaDto.getField());
            } else if (httpAppCriteriaDto.getSort().intValue() == ConstantUtil.Sort.DESC) {
                sort = org.springframework.data.domain.Sort.by(org.springframework.data.domain.Sort.Direction.DESC, httpAppCriteriaDto.getField());
            } else {
                sort = org.springframework.data.domain.Sort.by(org.springframework.data.domain.Sort.Direction.DESC, "appStat.totalVisits");
            }
        } else {
            sort = org.springframework.data.domain.Sort.by(org.springframework.data.domain.Sort.Direction.DESC, "appStat.totalVisits");
        }

        queryDB.with(sort);

        return mongoTemplate.find(queryDB, HttpAppResource.class, collectionName);
    }

    private Query getAccParseAppQuery(HttpAppCriteriaDto httpAppCriteriaDto) {
        Query queryDB = new Query();

        queryDB.addCriteria(org.springframework.data.mongodb.core.query.Criteria.where("delFlag").is(false));

        if (DataUtil.isNotEmpty(httpAppCriteriaDto.getUri())) {
            queryDB.addCriteria(org.springframework.data.mongodb.core.query.Criteria.where("uri").regex(Pattern.quote(httpAppCriteriaDto.getUri())));
        }
        if (DataUtil.isNotEmpty(httpAppCriteriaDto.getAppUriList())) {
            queryDB.addCriteria(org.springframework.data.mongodb.core.query.Criteria.where("uri").in(httpAppCriteriaDto.getAppUriList()));
        }
        if (DataUtil.isNotEmpty(httpAppCriteriaDto.getFollowed())) {
            queryDB.addCriteria(org.springframework.data.mongodb.core.query.Criteria.where("followed").is(httpAppCriteriaDto.getFollowed()));
        }
        if (DataUtil.isNotEmpty(httpAppCriteriaDto.getIsIpAssociatedEnable())) {
            queryDB.addCriteria(org.springframework.data.mongodb.core.query.Criteria.where("isIpAssociatedEnable").is(httpAppCriteriaDto.getIsIpAssociatedEnable()));
        }

        return queryDB;
    }

    @Override
    public Long countByHttpAppCriDto(HttpAppCriteriaDto httpAppCriteriaDto) {
        Query queryDB = getAccParseAppQuery(httpAppCriteriaDto);
        return mongoTemplate.count(queryDB, collectionName);
    }

    /**
     * 批量开启或关闭快速关联
     *
     * @param uris
     * @return
     */
    @Override
    public void batchUpdateByIds(List<String> uris, int type) {
        MetabaseQuery metabaseQuery = new MetabaseQuery();
        if (DataUtil.isNotEmpty(uris)) {
            metabaseQuery.where("uri", Predicate.IN, uris);
        }
        ResourceUpdates updates = new ResourceUpdates();
        if (type == 1) {
            updates.set("isIpAssociatedEnable", true);
        } else {
            updates.set("isIpAssociatedEnable", false);
        }
        List<BatchUpdates> batchUpdates = new ArrayList<>();
        BatchUpdates batchUpdate = new BatchUpdates();
        batchUpdate.setQuery(metabaseQuery);
        batchUpdate.setUpdates(updates);
        batchUpdates.add(batchUpdate);
        metabaseClientTemplate.update(batchUpdates, true, HttpAppResource.class);
    }

    /**
     * 批量开启或关闭快速关联
     *
     * @param uris
     * @return
     */
    @Override
    public void batchUpdateByUris(List<String> uris, int type, String target) {

        List<BatchUpdates> batchUpdates = new ArrayList<>();
        for (String uri : uris) {
            MetabaseQuery metabaseQuery = new MetabaseQuery();
            if (DataUtil.isNotEmpty(uris)) {
                metabaseQuery.where("uri", Predicate.IS, uri);
            }
            ResourceUpdates updates = getResourceUpdates(type, target);
            BatchUpdates batchUpdate = new BatchUpdates();
            batchUpdate.setQuery(metabaseQuery);
            batchUpdate.setUpdates(updates);
            batchUpdates.add(batchUpdate);
        }
        metabaseClientTemplate.update(batchUpdates, false, HttpAppResource.class);
    }

    private ResourceUpdates getResourceUpdates(int type, String target) {
        ResourceUpdates updates = new ResourceUpdates();
        if (target.equals("accParseEnable")) {
            String[] tags = new String[]{TagsEnum.accountParse.name(), TagsEnum.confirmed.name()};
            updates.set("tags", tags);
            if (type == 1) {
                updates.set("accParseEnable", true);
            } else {
                updates.set("accParseEnable", false);
            }
        } else if (target.equals("isIpAssociatedEnable")) {
            if (type == 1) {
                updates.set("isIpAssociatedEnable", true);
            } else {
                updates.set("isIpAssociatedEnable", false);
            }
        }
        return updates;
    }

    @Override
    public void saveAppAccParse(HttpAppResource httpAppResource, int parse) throws Exception {

        HttpAppResource httpAppResource1 = null;
        if (DataUtil.isNotEmpty(httpAppResource)) {
            if (DataUtil.isNotEmpty(httpAppResource.getUri())) {
                httpAppResource1 = this.getHttpAppResourceByUri(httpAppResource.getUri());
            }
            //追加并去重
            if (parse == 1) {
                //账号配置
                Set<HttpApiResource.AccParseConfigs> userParseConfigsSet = new HashSet<>();
                if (DataUtil.isNotEmpty(httpAppResource.getUserParseConfigs())) {
                    for (HttpApiResource.AccParseConfigs userParseConfigs : httpAppResource.getUserParseConfigs()) {
                        userParseConfigsSet.add(userParseConfigs);
                    }
                }
                if (DataUtil.isNotEmpty(httpAppResource1.getUserParseConfigs())) {
                    for (HttpApiResource.AccParseConfigs userParseConfigs1 : httpAppResource1.getUserParseConfigs()) {
                        userParseConfigsSet.add(userParseConfigs1);
                    }
                }
                List<HttpApiResource.AccParseConfigs> userParseConfigsList = new ArrayList<>(userParseConfigsSet);
                httpAppResource1.setUserParseConfigs(userParseConfigsList);

                //session配置
                Set<HttpApiResource.AccParseConfigs> sessionParseConfigsSet = new HashSet<>();
                if (DataUtil.isNotEmpty(httpAppResource.getSessionParseConfigs())) {
                    for (HttpApiResource.AccParseConfigs sessionParseConfigs : httpAppResource.getSessionParseConfigs()) {
                        sessionParseConfigsSet.add(sessionParseConfigs);
                    }
                }
                if (DataUtil.isNotEmpty(httpAppResource1.getSessionParseConfigs())) {
                    for (HttpApiResource.AccParseConfigs sessionParseConfigs : httpAppResource1.getSessionParseConfigs()) {
                        sessionParseConfigsSet.add(sessionParseConfigs);
                    }
                }
                List<HttpApiResource.AccParseConfigs> sessionParseConfigsList = new ArrayList<>(sessionParseConfigsSet);
                httpAppResource1.setSessionParseConfigs(sessionParseConfigsList);

            }

            //覆盖
            else {
                if (DataUtil.isNotEmpty(httpAppResource.getUserParseConfigs())) {
                    httpAppResource1.setUserParseConfigs(httpAppResource.getUserParseConfigs());
                } else {
                    httpAppResource1.setUserParseConfigs(new ArrayList<>());
                }
                if (DataUtil.isNotEmpty(httpAppResource.getSessionParseConfigs())) {
                    httpAppResource1.setSessionParseConfigs(httpAppResource.getSessionParseConfigs());
                } else {
                    httpAppResource1.setSessionParseConfigs(new ArrayList<>());
                }
            }

            if (DataUtil.isNotEmpty(httpAppResource.getAccParseEnable())) {
                httpAppResource1.setAccParseEnable(httpAppResource.getAccParseEnable());
            } else {
                httpAppResource1.setAccParseEnable(true);
            }

            if (DataUtil.isNotEmpty(httpAppResource.getIsIpAssociatedEnable())) {
                httpAppResource1.setIsIpAssociatedEnable(httpAppResource.getIsIpAssociatedEnable());
            } else {
                httpAppResource1.setIsIpAssociatedEnable(false);
            }

            if (DataUtil.isNotEmpty(httpAppResource.getTags())) {
                httpAppResource1.setTags(httpAppResource.getTags());
            } else {
                List<String> tags = new ArrayList<>();
                tags.add(TagsEnum.accountParse.name());
                httpAppResource1.setTags(tags);
            }

            metabaseClientTemplate.save(httpAppResource1);
        }
    }

    @Override
    public void saveHttpApp(List<HttpAppResource> httpAppResourceList) {
//        for (HttpAppResource httpAppResource : httpAppResourceList) {
//            mongoTemplate.save(httpAppResource, collectionName);
//        }

        metabaseClientTemplate.saveAll(httpAppResourceList);
    }

    @Override
    public HttpAppResource getHttpAppResourceByUri(String uri) {
        MetabaseQuery metabaseQuery = new MetabaseQuery();
        metabaseQuery.where("delFlag", Predicate.IS, false);
        if (DataUtil.isNotEmpty(uri)) {
            metabaseQuery.where("uri", Predicate.IS, uri);
        }
        return metabaseClientTemplate.findOne(metabaseQuery, HttpAppResource.class);
    }

    @Override
    @Cacheable("AppRelation")
    public AppRelationDto getAppRelation(List<String> appUris) {
        //境外访问境外
        org.springframework.data.mongodb.core.query.Criteria criteria_Abroad2Abroad = org.springframework.data.mongodb.core.query.Criteria
                .where("delFlag").is(false)
                .and("visitDomains").is("互联网-境外")
                .and("deployDomains").is("互联网-境外");
        org.springframework.data.mongodb.core.query.Criteria criteria_Abroad2Territory = org.springframework.data.mongodb.core.query.Criteria
                .where("delFlag").is(false)
                .and("visitDomains").is("互联网-境外")
                .and("deployDomains").is("互联网-境内");
        org.springframework.data.mongodb.core.query.Criteria criteria_AbroadToLocal = org.springframework.data.mongodb.core.query.Criteria
                .where("delFlag").is(false)
                .and("visitDomains").is("互联网-境外")
                .and("deployDomains").regex("局域网-");
        org.springframework.data.mongodb.core.query.Criteria criteria_LocalToLocal = org.springframework.data.mongodb.core.query.Criteria
                .where("delFlag").is(false)
                .and("visitDomains").regex("局域网-")
                .and("deployDomains").regex("局域网-");
        org.springframework.data.mongodb.core.query.Criteria criteria_LocalToTerritory = org.springframework.data.mongodb.core.query.Criteria
                .where("delFlag").is(false)
                .and("visitDomains").regex("局域网-")
                .and("deployDomains").is("互联网-境内");
        org.springframework.data.mongodb.core.query.Criteria criteria_LocalToAbroad = org.springframework.data.mongodb.core.query.Criteria
                .where("delFlag").is(false)
                .and("visitDomains").regex("局域网-")
                .and("deployDomains").is("互联网-境外");
        org.springframework.data.mongodb.core.query.Criteria criteria_TerritoryToTerritory = org.springframework.data.mongodb.core.query.Criteria
                .where("delFlag").is(false)
                .and("visitDomains").is("互联网-境内")
                .and("deployDomains").is("互联网-境内");
        org.springframework.data.mongodb.core.query.Criteria criteria_TerritoryToAbroad = org.springframework.data.mongodb.core.query.Criteria
                .where("delFlag").is(false)
                .and("visitDomains").is("互联网-境内")
                .and("deployDomains").is("互联网-境外");
        org.springframework.data.mongodb.core.query.Criteria criteria_TerritoryToLocal = org.springframework.data.mongodb.core.query.Criteria
                .where("delFlag").is(false)
                .and("visitDomains").is("互联网-境内")
                .and("deployDomains").regex("局域网-");

        if (DataUtil.isNotEmpty(appUris)) {
            criteria_Abroad2Abroad.and("uri").in(appUris);
            criteria_Abroad2Territory.and("uri").in(appUris);
            criteria_AbroadToLocal.and("uri").in(appUris);
            criteria_LocalToLocal.and("uri").in(appUris);
            criteria_LocalToTerritory.and("uri").in(appUris);
            criteria_LocalToAbroad.and("uri").in(appUris);
            criteria_TerritoryToTerritory.and("uri").in(appUris);
            criteria_TerritoryToAbroad.and("uri").in(appUris);
            criteria_TerritoryToLocal.and("uri").in(appUris);
        }

        Long AbroadToAbroad = mongoTemplate.count(new Query().addCriteria(criteria_Abroad2Abroad), collectionName);
        //境外访问境内
        Long AbroadToTerritory = mongoTemplate.count(new Query().addCriteria(criteria_Abroad2Territory), collectionName);
        //境外访问局域网
        Long AbroadToLocal = mongoTemplate.count(new Query().addCriteria(criteria_AbroadToLocal), collectionName);

        //局域网访问局域网
        Long LocalToLocal = mongoTemplate.count(new Query().addCriteria(criteria_LocalToLocal), collectionName);
        //局域网访问境内
        Long LocalToTerritory = mongoTemplate.count(new Query().addCriteria(criteria_LocalToTerritory), collectionName);
        //局域网访问境外
        Long LocalToAbroad = mongoTemplate.count(new Query().addCriteria(criteria_LocalToAbroad), collectionName);

        //境内访问境内
        Long TerritoryToTerritory = mongoTemplate.count(new Query().addCriteria(criteria_TerritoryToTerritory), collectionName);
        //境内访问境外
        Long TerritoryToAbroad = mongoTemplate.count(new Query().addCriteria(criteria_TerritoryToAbroad), collectionName);
        //境内访问局域网
        Long TerritoryToLocal = mongoTemplate.count(new Query().addCriteria(criteria_TerritoryToLocal), collectionName);

        return AppRelationDto.builder()
                .AbroadToTerritory(AbroadToTerritory)
                .AbroadToAbroad(AbroadToAbroad)
                .AbroadToLocal(AbroadToLocal)
                .LocalToAbroad(LocalToAbroad)
                .LocalToLocal(LocalToLocal)
                .LocalToTerritory(LocalToTerritory)
                .TerritoryToAbroad(TerritoryToAbroad)
                .TerritoryToLocal(TerritoryToLocal)
                .TerritoryToTerritory(TerritoryToTerritory).build();
    }

    /**
     * 敏感应用Top10
     *
     * @param
     * @return
     * @Comments: <对此方法的描述，可以引用系统设计中的描述>
     * <AUTHOR> [<EMAIL>]
     * @since 2020-04-08 9:56
     */
    @Override
    public List<HttpAppResource> sensitiveAppTop10(Map<String, Object> map, String field) throws Exception {

        MetabaseQuery metabaseQuery = getMetabaseQuery(map);

        //分页
        metabaseQuery.limit(10);
        //排序
        if (DataUtil.isNotEmpty(field)) {

            metabaseQuery.sort(Sort.by(field, SortOrder.DESC), Sort.by("host", SortOrder.ASC));
        }
        return metabaseClientTemplate.find(metabaseQuery, HttpAppResource.class);
    }

    /**
     * 应用分组查询(不走matabase)
     *
     * @param
     * @return
     * @Comments: <对此方法的描述，可以引用系统设计中的描述>
     * <AUTHOR> [<EMAIL>]
     * @since 2021-09-02 9:56
     */
    @Override
    public List<AggregationDto> group(Map<String, Object> map, GroupDto groupDto, Integer sort, Integer page, Integer limit) throws Exception {

        org.springframework.data.mongodb.core.query.Criteria criteria = new org.springframework.data.mongodb.core.query.Criteria();

        //查询条件
        if (DataUtil.isNotEmpty(map)) {
            for (String key : map.keySet()) {

                Object value = map.get(key);
                if (DataUtil.isNotEmpty(value)) {

                    if ("delFlag".equals(key)) {
                        criteria.and(key).is(value);
                    }
                }
            }
        }

        return this.group(criteria, groupDto, sort, page, limit, collectionName);
    }

    /**
     * 时间戳转日期分组(不走matabase)
     *
     * @param
     * @return
     * @Comments: <对此方法的描述，可以引用系统设计中的描述>
     * <AUTHOR> [<EMAIL>]
     * @since 2021-09-02 9:56
     */
    @Override
    public List<AggregationDto> group() throws Exception {

        List<AggregationOperation> operations = new ArrayList<>();

        //查询条件
        org.springframework.data.mongodb.core.query.Criteria criteria = new org.springframework.data.mongodb.core.query.Criteria();
        criteria.and("delFlag").is(false);
        operations.add(Aggregation.match(criteria));
        //返回字段
        operations.add(Aggregation.project("date").andExpression("{$dateToString:{format:'%Y%m%d',date:{$add:{new java.util.Date(0l),'$discoverTime',28800000l}}}}").as("date"));
        //分组
        operations.add(Aggregation.group("date").count().as("resultAlias"));
        //排序
        operations.add(Aggregation.sort(org.springframework.data.domain.Sort.Direction.DESC, "_id"));
        //每页显示条数
        operations.add(Aggregation.limit(30));

        Aggregation aggregation = Aggregation.newAggregation(operations).withOptions(newAggregationOptions().allowDiskUse(true).build());

        return mongoTemplate.aggregate(aggregation, collectionName, AggregationDto.class).getMappedResults();
    }

    @Override
    public List<Long> getStatistics() {
        List<Long> result = new ArrayList<>();
        long totalApp = mongoTemplate.count(new Query().addCriteria(org.springframework.data.mongodb.core.query.Criteria
                .where("delFlag").is(false)
        ), collectionName);
        result.add(totalApp);
        long totalHighLevelApp = mongoTemplate.count(new Query().addCriteria(org.springframework.data.mongodb.core.query.Criteria
                .where("delFlag").is(false)
                .and("level").is("高敏感")
        ), collectionName);
        result.add(totalHighLevelApp);
        return result;
    }

    @Override
    public List<String> selectAllAppByNetwork(String network) {
        return mongoTemplate.findDistinct(new Query().addCriteria(org.springframework.data.mongodb.core.query.Criteria
                .where("delFlag").is(false)
                .and("deployDomains").is(network)
        ), "host", collectionName, String.class);
    }

    @Override
    public List<HttpAppResource> selectAppByHost(List<String> host) {
        return mongoTemplate.find(new Query().addCriteria(org.springframework.data.mongodb.core.query.Criteria
                .where("delFlag").is(false)
                .and("host").in(host)
        ), HttpAppResource.class, collectionName);
    }

    @Override
    public long getAppRelationWithVisitAndDeploy(String visitDomain, String deployDomain) {
        return mongoTemplate.count(new Query().addCriteria(org.springframework.data.mongodb.core.query.Criteria
                .where("delFlag").is(false)
                .and("visitDomains").is(visitDomain)
                .and("deployDomains").is(deployDomain)
        ), collectionName);
    }

    /**
     * 拼入delFlag查询条件
     *
     * @param
     * @return
     * @Comments: <对此方法的描述，可以引用系统设计中的描述>
     * <AUTHOR> [<EMAIL>]
     * @since 2020-04-08 9:56
     */
    private MetabaseQuery andDelFlag(MetabaseQuery metabaseQuery) throws Exception {

        List<Boolean> list = new ArrayList<Boolean>();
        list.add(true);
        list.add(false);
        metabaseQuery.where("delFlag", Predicate.IN, list);

        return metabaseQuery;
    }

    private MetabaseQuery getMetabaseQuery(Map<String, Object> map) {

        MetabaseQuery metabaseQuery = new MetabaseQuery();
        //且或map
        Map<String, String> fieldOperateMap = map.get("fieldOperateMap") == null ? new HashMap<>() : (Map<String, String>) map.get("fieldOperateMap");
        //查询条件
        if (DataUtil.isNotEmpty(map)) {
            try {
                specialQueryFieldConvert.specialQueryFieldConvert(map);
            } catch (Exception e) {
                log.error("specialQueryFieldConvert error", e);
            }
            for (String key : map.keySet()) {

                Object value = map.get(key);
                if (value instanceof String) {
                    value = String.valueOf(value).trim();
                }
                if (DataUtil.isNotEmpty(value)) {
                    if ("host".equals(key)) {
                        boolean ignoreCase = map.get("hostIgnoreCase") == null ? false : (boolean) map.get("hostIgnoreCase");
                        String host = DataUtil.regexStrEscape(String.valueOf(value).trim());
                        if (Boolean.TRUE.equals(ignoreCase)) {
                            metabaseQuery.where(key, Predicate.REGEX, Pattern.compile(host, Pattern.CASE_INSENSITIVE));
                        } else {
                            metabaseQuery.where(key, Predicate.REGEX, Pattern.compile(host));
                        }
                    } else if ("name".equals(key)) {
                        String name = String.valueOf(value);
                        if (name.startsWith("http")) {
                            // 写了全限定名称，这里将其修改为hots
                            name = UrlUtil.getUrlHost(name);
                        }
                        metabaseQuery.where(key, Predicate.REGEX, Pattern.compile(DataUtil.regexStrEscape(name), Pattern.CASE_INSENSITIVE));
                    } else if ("featureLabels".equals(key)) {
                        metabaseQuery.where(key, Predicate.ALL, value);
                    } else if ("appClassifications".equals(key)) {
                        metabaseQuery.where(key, Predicate.ALL, value);
                    } else if ("visitDomains".equals(key)) {
                        if (DBOperatorEnum.OR.operator().equals(fieldOperateMap.get(key))) {
                            metabaseQuery.where(key, Predicate.IN, value);
                        } else {
                            metabaseQuery.where(key, Predicate.ALL, value);
                        }
                    } else if ("deployDomains".equals(key)) {
                        if (DBOperatorEnum.OR.operator().equals(fieldOperateMap.get(key))) {
                            metabaseQuery.where(key, Predicate.IN, value);
                        } else {
                            metabaseQuery.where(key, Predicate.ALL, value);
                        }
                    } else if ("deployIps".equals(key)) {
                        String deployIp = DataUtil.regexStrEscape(String.valueOf(value));
                        metabaseQuery.where(key, Predicate.REGEX, deployIp);
                    } else if ("reqDataLabels".equals(key)) {
                        List<String> reqDataLabels = (List<String>) value;
                        Map<String, String> dataLabelMap = (Map<String, String>) map.get("dataLabelMap");
                        boolean isAll = reqDataLabels.get(0).equals("ALL");
                        List<String> allDataLabels = isAll ? dataLabelMap.keySet().stream().collect(Collectors.toList()) : new ArrayList<>();
                        if (DBOperatorEnum.OR.operator().equals(fieldOperateMap.get(key))) {
                            metabaseQuery.where(key, Predicate.IN, isAll ? allDataLabels : reqDataLabels);
                        } else {
                            metabaseQuery.where(key, Predicate.ALL, isAll ? allDataLabels : reqDataLabels);
                        }
                    } else if ("rspDataLabels".equals(key)) {
                        List<String> rspDataLabels = (List<String>) value;
                        Map<String, String> dataLabelMap = (Map<String, String>) map.get("dataLabelMap");
                        boolean isAll = rspDataLabels.get(0).equals("ALL");
                        List<String> allDataLabels = isAll ? dataLabelMap.keySet().stream().collect(Collectors.toList()) : new ArrayList<>();
                        if (DBOperatorEnum.OR.operator().equals(fieldOperateMap.get(key))) {
                            metabaseQuery.where(key, Predicate.IN, isAll ? allDataLabels : rspDataLabels);
                        } else {
                            metabaseQuery.where(key, Predicate.ALL, isAll ? allDataLabels : rspDataLabels);
                        }
                    } else if ("followed".equals(key)) {
                        metabaseQuery.where(key, Predicate.IS, value);
                    } else if ("uri".equals(key)) {
                        metabaseQuery.where(key, Predicate.IS, value);
                    } else if ("provinces".equals(key)) {
                        metabaseQuery.where(key, Predicate.IN, value);
                    } else if ("departments.department".equals(key)) {
                        metabaseQuery.where(key, Predicate.REGEX, Pattern.quote(String.valueOf(value)));
                    } else if ("restfulFlag".equals(key)) {
                        if (HttpApiFlagComposite.RestfulFlag.RESTFUL_CANCELED == (short) value) {
                            metabaseQuery.where(key, Predicate.NE, value);
                        } else {
                            metabaseQuery.where(key, Predicate.IS, value);
                        }
                    } else if ("sensitiveAppFlag".equals(key)) {
                        if (value instanceof String) {
                            if (String.valueOf(value).equals("1")) {
                                metabaseQuery.where("appStat.sensitiveApiCount", Predicate.GT, 0);
                            } else if (String.valueOf(value).equals("0")) {
                                metabaseQuery.where("appStat.sensitiveApiCount", Predicate.LTE, 0);
                            }
                        }
                    } else if ("delFlag".equals(key)) {
                        metabaseQuery.where("delFlag", Predicate.IS, value);
                    } else if ("appStat.apiCount_gte".equals(key)) {
                        metabaseQuery.where("appStat.apiCount", Predicate.GTE, value);
                    } else if ("appStat.allTypeApiCount_gte".equals(key)) {
                        metabaseQuery.where("appStat.allTypeApiCount", Predicate.GTE, value);
                    } else if ("id".equals(key)) {
                        metabaseQuery.where("_id", Predicate.IS, value);
                    } else if ("ids".equals(key)) {
                        metabaseQuery.where("_id", Predicate.IN, value);
                    } else if ("orderFlag".equals(key)) {
                        metabaseQuery.where("orderFlag", Predicate.IS, value);
                    } else if ("flowSource".equals(key)) {
                        String flowSource = DataUtil.regexStrEscape(String.valueOf(value));
                        metabaseQuery.where("flowSources", Predicate.REGEX, flowSource);
                    } else if ("appLifeFlag".equals(key)) {
                        // metabaseQuery.where("appLifeFlag", Predicate.IN, value);
                        List<Object> values = (List<Object>) value;
                        short lifeFlag = Short.parseShort(values.get(0) + "");
                        AssetLifeStateEnum assetLifeStateEnum = AssetLifeStateEnum.valueOfNum(lifeFlag);
                        AssetLifeStateConfigConvert.CriteriaTag criteriaTag = assetLifeStateConfigConvert.getCriteria(AssetLifeStateConfig.AssetTypeEnum.APP, assetLifeStateEnum);
                        metabaseQuery.getCriteria().add(criteriaTag.getMetabaseCriteria());
                    } else if ("weaknessIds".equals(key)) {
                        String operate = fieldOperateMap.get("weaknessIds");
                        List<String> values = (List<String>) value;
                        Map<String, String> weaknessMap = (Map<String, String>) map.get("weaknessMap");
                        boolean isAll = values.get(0).equals("ALL");
                        List<String> allWeakness = isAll ? weaknessMap.keySet().stream().collect(Collectors.toList()) : new ArrayList<>();
                        if (DBOperatorEnum.OR.operator().equals(operate)) {
                            metabaseQuery.where("appStat.weaknessIds", Predicate.IN, isAll ? allWeakness : values);
                        } else {
                            metabaseQuery.where("appStat.weaknessIds", Predicate.ALL, isAll ? allWeakness : values);
                        }
                    } else if ("riskIds".equals(key)) {
                        String operate = fieldOperateMap.get("riskIds");
                        List<String> values = (List<String>) value;
                        Map<String, String> riskMap = (Map<String, String>) map.get("riskMap");
                        boolean isAll = values.get(0).equals("ALL");
                        List<String> allRisks = isAll ? riskMap.keySet().stream().collect(Collectors.toList()) : new ArrayList<>();
                        if (DBOperatorEnum.OR.operator().equals(operate)) {
                            metabaseQuery.where("appStat.riskIds", Predicate.IN, isAll ? allRisks : values);
                        } else {
                            metabaseQuery.where("appStat.riskIds", Predicate.ALL, isAll ? allRisks : values);
                        }
                    } else if ("showInvalid".equals(key)) {
                        if (Boolean.TRUE.equals(value)) {//展示无效应用
                        } else {
                            metabaseQuery.where("assetFlag", Predicate.NE, AssetFlagEnum.INVALID.getCode());
                        }
                    } else if ("nodeId".equals(key)) {
                        metabaseQuery.where("nodes.nid", Predicate.IS, value);
                    }
                }
            }

            if (DataUtil.isNotEmpty(map.get("isAssetAuthorization")) && (boolean) map.get("isAssetAuthorization") == true) {

                if (DataUtil.isNotEmpty(map.get("appUriSet")) || DataUtil.isNotEmpty(map.get("departmentSet"))) {

                    Criteria criteria = new Criteria();
                    Criteria criteria1 = null;
                    Criteria criteria2 = null;

                    if (DataUtil.isNotEmpty(map.get("appUriSet"))) {
                        criteria1 = Criteria.where("uri").in(map.get("appUriSet"));
                    }

                    if (DataUtil.isNotEmpty(map.get("departmentSet"))) {
                        criteria2 = Criteria.where("departments.department").in(map.get("departmentSet"));
                    }

                    if (DataUtil.isNotEmpty(criteria1) && DataUtil.isEmpty(criteria2)) {
                        criteria = criteria.orOperator(criteria1);
                    } else if (DataUtil.isEmpty(criteria1) && DataUtil.isNotEmpty(criteria2)) {
                        criteria = criteria.orOperator(criteria2);
                    } else if (DataUtil.isNotEmpty(criteria1) && DataUtil.isNotEmpty(criteria2)) {
                        criteria = criteria.orOperator(criteria1, criteria2);
                    }

                    metabaseQuery.getCriteria().add(criteria);
                } else {
                    metabaseQuery.where("_id", Predicate.IS, "");
                }
            }

            if (DataUtil.isNotEmpty(map.get("level")) || DataUtil.isNotEmpty(map.get("isSensitiveApp"))) {

                Criteria criteria = new Criteria();

                if (DataUtil.isNotEmpty(map.get("level"))) {

                    Criteria criteria1 = Criteria.where("level").in(map.get("level"));
                    criteria = criteria.andOperator(criteria1);
                }

                if (DataUtil.isNotEmpty(map.get("isSensitiveApp"))) {

                    Criteria criteria2 = null;

                    if (String.valueOf(map.get("isSensitiveApp")).equals("true")) {

                        criteria2 = Criteria.where("level").in(new ArrayList<String>() {
                            {
                                add(LevelEnum.HIGH.getKey());
                                add(LevelEnum.MIDDLE.getKey());
                                add(LevelEnum.LOW.getKey());
                            }
                        });
                    } else if (String.valueOf(map.get("isSensitiveApp")).equals("false")) {

                        criteria2 = Criteria.where("level").is(LevelEnum.NON.getKey());
                    }
                    criteria = criteria.andOperator(criteria2);
                }

                metabaseQuery.getCriteria().add(criteria);
            }
            //首次发现时间
            if (DataUtil.isNotEmpty(map.get("discoverTimeStart")) && DataUtil.isNotEmpty(map.get("discoverTimeEnd"))) {
                Criteria criteria = new Criteria();
                criteria = criteria.andOperator(
                        Criteria.where("discoverTime").gte(map.get("discoverTimeStart")),
                        Criteria.where("discoverTime").lte(map.get("discoverTimeEnd")));
                metabaseQuery.getCriteria().add(criteria);
            }
            //最近活跃时间
            if (DataUtil.isNotEmpty(map.get("activeTimeStart")) && DataUtil.isNotEmpty(map.get("activeTimeStart"))) {
                Criteria criteria = new Criteria();
                criteria = criteria.andOperator(
                        Criteria.where("activeTime").gte(map.get("activeTimeStart")),
                        Criteria.where("activeTime").lte(map.get("activeTimeEnd")));
                metabaseQuery.getCriteria().add(criteria);
            }
            if (map.get("showFields") != null) {
                metabaseQuery.fields((List<String>) map.get("showFields"));
            }
            Criteria customPropertyCriteria = customPropertySearchAdapter
                    .getMetabaseCriteria(customPropertySearchAdapter
                            .buildCustomProperty(map));
            if (customPropertyCriteria != null) {
                metabaseQuery.getCriteria().add(customPropertyCriteria);
            }
            queryAdapterRegistry.module(Module.APP).query(map, metabaseQuery);
        }
        return metabaseQuery;
    }

    @Override
    public org.springframework.data.mongodb.core.query.Criteria getCriteria(Map<String, Object> map) {

        org.springframework.data.mongodb.core.query.Criteria criteria = new org.springframework.data.mongodb.core.query.Criteria();
        //且或map
        Map<String, String> fieldOperateMap = map.get("fieldOperateMap") == null ? new HashMap<>() : (Map<String, String>) map.get("fieldOperateMap");
        //查询条件
        if (DataUtil.isNotEmpty(map)) {
            try {
                specialQueryFieldConvert.specialQueryFieldConvert(map);
            } catch (Exception e) {
                log.error("specialQueryFieldConvert error", e);
            }
            for (String key : map.keySet()) {

                Object value = map.get(key);
                if (DataUtil.isNotEmpty(value)) {
                    if ("host".equals(key)) {
                        boolean ignoreCase = map.get("hostIgnoreCase") == null ? false : (boolean) map.get("hostIgnoreCase");
                        if (Boolean.TRUE.equals(ignoreCase)) {
                            criteria.and(key).regex(Pattern.compile(String.valueOf(value), Pattern.CASE_INSENSITIVE));
                        } else {
                            criteria.and(key).regex(Pattern.quote(String.valueOf(value)));
                        }
                    } else if ("name".equals(key)) {
                        criteria.and(key).regex(Pattern.compile(DataUtil.regexStrEscape(String.valueOf(value)), Pattern.CASE_INSENSITIVE));
                    } else if ("featureLabels".equals(key)) {
                        criteria.and(key).is(value);
                    } else if ("appClassifications".equals(key)) {
                        criteria.and(key).all(value);
                    } else if ("visitDomains".equals(key)) {
                        List<String> list = (List<String>) value;
                        if (DBOperatorEnum.OR.operator().equals(fieldOperateMap.get(key))) {
                            criteria.and(key).in(list);
                        } else {
                            criteria.and(key).all(list);
                        }
                    } else if ("deployDomains".equals(key)) {
                        List<String> list = (List<String>) value;
                        if (DBOperatorEnum.OR.operator().equals(fieldOperateMap.get(key))) {
                            criteria.and(key).in(list);
                        } else {
                            criteria.and(key).all(list);
                        }
                    } else if ("deployIps".equals(key)) {
                        criteria.and(key).regex(Pattern.quote(String.valueOf(value)));
                    } else if ("reqDataLabels".equals(key)) {
                        List<String> reqDataLabels = (List<String>) value;
                        Map<String, String> dataLabelMap = (Map<String, String>) map.get("dataLabelMap");
                        boolean isAll = reqDataLabels.get(0).equals("ALL");
                        List<String> allDataLabels = isAll ? dataLabelMap.keySet().stream().collect(Collectors.toList()) : new ArrayList<>();
                        if (DBOperatorEnum.OR.operator().equals(fieldOperateMap.get(key))) {
                            criteria.and(key).in(isAll ? allDataLabels : reqDataLabels);
                        } else {
                            criteria.and(key).all(isAll ? allDataLabels : reqDataLabels);
                        }
                    } else if ("rspDataLabels".equals(key)) {
                        List<String> rspDataLabels = (List<String>) value;
                        Map<String, String> dataLabelMap = (Map<String, String>) map.get("dataLabelMap");
                        boolean isAll = rspDataLabels.get(0).equals("ALL");
                        List<String> allDataLabels = isAll ? dataLabelMap.keySet().stream().collect(Collectors.toList()) : new ArrayList<>();
                        if (DBOperatorEnum.OR.operator().equals(fieldOperateMap.get(key))) {
                            criteria.and(key).in(isAll ? allDataLabels : rspDataLabels);
                        } else {
                            criteria.and(key).all(isAll ? allDataLabels : rspDataLabels);
                        }
                    } else if ("followed".equals(key)) {
                        criteria.and(key).is(value);
                    } else if ("uri".equals(key)) {
                        criteria.and(key).is(value);
                    } else if ("departments.department".equals(key)) {
                        criteria.and(key).regex(Pattern.quote(String.valueOf(value)));
                    } else if ("restfulFlag".equals(key)) {
                        if (HttpApiFlagComposite.RestfulFlag.RESTFUL_CANCELED == (short) value) {
                            criteria.and(key).ne(value);
                        } else {
                            criteria.and(key).is(value);
                        }
                    } else if ("sensitiveAppFlag".equals(key)) {
                        if (value instanceof String) {
                            if (String.valueOf(value).equals("1")) {
                                criteria.and("appStat.sensitiveApiCount").gt(0);
                            } else if (String.valueOf(value).equals("0")) {
                                criteria.and("appStat.sensitiveApiCount").lte(0);
                            }
                        }
                    } else if ("delFlag".equals(key)) {
                        criteria.and("delFlag").is(value);
                    } else if ("appStat.apiCount_gte".equals(key)) {
                        criteria.and("appStat.apiCount").gte(value);
                    } else if ("appStat.allTypeApiCount_gte".equals(key)) {
                        criteria.and("appStat.allTypeApiCount").gte(value);
                    } else if ("id".equals(key)) {
                        criteria.and("_id").is(value);
                    } else if ("ids".equals(key)) {
                        List<String> exportIds = (List<String>) value;
                        List<ObjectId> ids = new ArrayList<>(exportIds.size());
                        exportIds.forEach(id -> ids.add(new ObjectId(id)));
                        criteria.and("_id").in(ids);
                    } else if ("orderFlag".equals(key)) {
                        criteria.and("orderFlag").is(value);
                    } else if ("flowSource".equals(key)) {
                        criteria.and("flowSources").is(value);
                    } else if ("appLifeFlag".equals(key)) {
                        // criteria.and("appLifeFlag", Predicate.IN, value);
                        List<Object> values = (List<Object>) value;
                        short lifeFlag = Short.parseShort(values.get(0) + "");
                        AssetLifeStateEnum assetLifeStateEnum = AssetLifeStateEnum.valueOfNum(lifeFlag);
                        org.springframework.data.mongodb.core.query.Criteria mongoCriteria = assetLifeStateConfigConvert.getMongoCriteria(AssetLifeStateConfig.AssetTypeEnum.APP, assetLifeStateEnum);
                        criteria.orOperator(mongoCriteria);
                    } else if ("weaknessIds".equals(key)) {
                        String operate = fieldOperateMap.get("weaknessIds");
                        List<String> values = (List<String>) value;
                        Map<String, String> weaknessMap = (Map<String, String>) map.get("weaknessMap");
                        boolean isAll = values.get(0).equals("ALL");
                        List<String> allWeakness = isAll ? weaknessMap.keySet().stream().collect(Collectors.toList()) : new ArrayList<>();
                        if (DBOperatorEnum.OR.operator().equals(operate)) {
                            criteria.and("appStat.weaknessIds").in(isAll ? allWeakness : values);
                        } else {
                            criteria.and("appStat.weaknessIds").all(isAll ? allWeakness : values);
                        }
                    } else if ("riskIds".equals(key)) {
                        String operate = fieldOperateMap.get("riskIds");
                        List<String> values = (List<String>) value;
                        Map<String, String> riskMap = (Map<String, String>) map.get("riskMap");
                        boolean isAll = values.get(0).equals("ALL");
                        List<String> allRisks = isAll ? riskMap.keySet().stream().collect(Collectors.toList()) : new ArrayList<>();
                        if (DBOperatorEnum.OR.operator().equals(operate)) {
                            criteria.and("appStat.riskIds").in(isAll ? allRisks : values);
                        } else {
                            criteria.and("appStat.riskIds").all(isAll ? allRisks : values);
                        }
                    } else if ("showInvalid".equals(key)) {
                        if (Boolean.TRUE.equals(value)) {//展示无效应用
                        } else {
                            criteria.and("assetFlag").ne(AssetFlagEnum.INVALID.getCode());
                        }
                    } else if ("nodeId".equals(key)) {
                        criteria.and("nodes.nid").is(value);
                    }
                }
            }

            if (DataUtil.isNotEmpty(map.get("isAssetAuthorization")) && (boolean) map.get("isAssetAuthorization") == true) {

                if (DataUtil.isNotEmpty(map.get("appUriSet")) || DataUtil.isNotEmpty(map.get("departmentSet"))) {

                    org.springframework.data.mongodb.core.query.Criteria criteria1 = new org.springframework.data.mongodb.core.query.Criteria();
                    org.springframework.data.mongodb.core.query.Criteria criteria2 = new org.springframework.data.mongodb.core.query.Criteria();

                    if (DataUtil.isNotEmpty(map.get("appUriSet"))) {
                        criteria1.and("uri").in(map.get("appUriSet"));
                    }

                    if (DataUtil.isNotEmpty(map.get("departmentSet"))) {
                        criteria2.and("departments.department").in(map.get("departmentSet"));
                    }

                    if (DataUtil.isNotEmpty(criteria1) && DataUtil.isEmpty(criteria2)) {
                        criteria.orOperator(criteria1);
                    } else if (DataUtil.isEmpty(criteria1) && DataUtil.isNotEmpty(criteria2)) {
                        criteria.orOperator(criteria2);
                    } else if (DataUtil.isNotEmpty(criteria1) && DataUtil.isNotEmpty(criteria2)) {
                        criteria.orOperator(criteria1, criteria2);
                    }
                } else {
                    criteria.and("_id").is("");
                }
            }

            if (DataUtil.isNotEmpty(map.get("level")) || DataUtil.isNotEmpty(map.get("isSensitiveApp"))) {

                if (DataUtil.isNotEmpty(map.get("level"))) {

                    org.springframework.data.mongodb.core.query.Criteria criteria1 = org.springframework.data.mongodb.core.query.Criteria.where("level").in((Collection<?>) map.get("level"));
                    criteria.andOperator(criteria1);
                }

                if (DataUtil.isNotEmpty(map.get("isSensitiveApp"))) {

                    org.springframework.data.mongodb.core.query.Criteria criteria2 = new org.springframework.data.mongodb.core.query.Criteria();

                    if (String.valueOf(map.get("isSensitiveApp")).equals("true")) {

                        criteria2.and("level").in(new ArrayList<String>() {
                            {
                                add(LevelEnum.HIGH.getKey());
                                add(LevelEnum.MIDDLE.getKey());
                                add(LevelEnum.LOW.getKey());
                            }
                        });
                    } else if (String.valueOf(map.get("isSensitiveApp")).equals("false")) {

                        criteria2.and("level").is(LevelEnum.NON.getKey());
                    }
                    criteria.andOperator(criteria2);
                }
            }
            //首次发现时间
            if (DataUtil.isNotEmpty(map.get("discoverTimeStart")) && DataUtil.isNotEmpty(map.get("discoverTimeEnd"))) {
                criteria.andOperator(
                        org.springframework.data.mongodb.core.query.Criteria.where("discoverTime").gte(map.get("discoverTimeStart")),
                        org.springframework.data.mongodb.core.query.Criteria.where("discoverTime").lte(map.get("discoverTimeEnd")));
            }
            //最近活跃时间
            if (DataUtil.isNotEmpty(map.get("activeTimeStart")) && DataUtil.isNotEmpty(map.get("activeTimeStart"))) {
                criteria.andOperator(
                        org.springframework.data.mongodb.core.query.Criteria.where("activeTime").gte(map.get("activeTimeStart")),
                        org.springframework.data.mongodb.core.query.Criteria.where("activeTime").lte(map.get("activeTimeEnd")));
            }
            org.springframework.data.mongodb.core.query.Criteria mongoCriteria = customPropertySearchAdapter
                    .getMongoCriteria(customPropertySearchAdapter
                            .buildCustomProperty(map));
            if (mongoCriteria != null) {
                criteria.orOperator(mongoCriteria);
            }
        }
        queryAdapterRegistry.module(Module.APP).query(map, criteria);
        return criteria;
    }

    @Override
    public void updateAppOrderFlag(MetabaseQuery metabaseQuery, ResourceUpdates resourceUpdates) {
        metabaseClientTemplate.update(metabaseQuery, resourceUpdates, false, HttpAppResource.class);
    }

    /**
     * 查询全部应用uri
     *
     * @param
     * @return
     * @Comments: <对此方法的描述，可以引用系统设计中的描述>
     * <AUTHOR> [<EMAIL>]
     * @since 2020-04-08 9:56
     */
    @Override
    public List<HttpAppResource> selectHttpAppUriList() throws Exception {

        MetabaseQuery metabaseQuery = new MetabaseQuery();
        metabaseQuery = andDelFlag(metabaseQuery);

        List<String> list = new ArrayList<String>();
        list.add("uri");

        metabaseQuery.fields(list);

        return metabaseClientTemplate.find(metabaseQuery, HttpAppResource.class);
    }

    @Override
    public AppAccountDto aggrAccountByApp(String appUri) {
        if (DataUtil.isNotEmpty(appUri)) {
            String collection = "accountInfo"; // accountInfo
            AppAccountDto accountDto = new AppAccountDto();
            Query query = new Query(org.springframework.data.mongodb.core.query.Criteria.where("appUriList")
                    .in(appUri))
                    .with(org.springframework.data.domain.Sort.by(org.springframework.data.domain.Sort.Direction.DESC, "updateTime"));
            query.limit(10);
            query.fields().include("account");
            List<AccountInfo> results = mongoTemplate.find(query, AccountInfo.class, collection);
            accountDto.setAccountList(results.stream().map(AccountInfo::getAccount).collect(Collectors.toSet()));
            return accountDto;
        }
        return null;
    }

    @Override
    public MongoCursor<Document> getHttpAppList(Map<String, Object> map, int batchSize) {
        MetabaseQuery metabaseQuery = getMetabaseQuery(map);
        Query query = QueryHelper.toQuery(metabaseQuery);
        query.fields().include("host");
        return mongoTemplate.getCollection("httpApp")
                .find(query.getQueryObject())
                .batchSize(batchSize).noCursorTimeout(true).iterator();
    }

    @Override
    public List<HttpAppResource> getHttpApisByUris(Set<String> appUriSet) {
        Query queryDB = new Query();
        queryDB.addCriteria(org.springframework.data.mongodb.core.query.Criteria.where("uri").in(appUriSet));

        return mongoTemplate.find(queryDB, HttpAppResource.class, collectionName);
    }


    @Override
    public List<Short> getAssetLifeFlag(JSONObject jsonObject) {
        List<Short> assetLifeState = assetLifeStateConfigConvert.getAssetLifeState(jsonObject);
        return assetLifeState;
    }

    @Override
    public String findAppIcon(String uri) {
        Query queryDB = new Query();
        queryDB.addCriteria(org.springframework.data.mongodb.core.query.Criteria.where("uri").is(uri));
        HttpAppIcon appIcon = mongoTemplate.findOne(queryDB, HttpAppIcon.class);
        return appIcon == null ? null : appIcon.getIconData();
    }


    @Override
    public Long count(Query query, String collection) {
        long count = mongoTemplate.count(query, collection);
        return count;
    }

    @Override
    public List<String> getCustomDepartments() {
        List<String> result=new ArrayList<>();
        Query query = Query.query(org.springframework.data.mongodb.core.query.Criteria.where("departments.properties.key").is("部门"));
        query.fields().include("departments");
        List<HttpAppResource> appResources = mongoTemplate.find(query, HttpAppResource.class,"httpApp");
        for(HttpAppResource httpAppResource:appResources){
            List<HttpAppResource.Department> departments = httpAppResource.getDepartments();
            for(HttpAppResource.Department department:departments){
                List<Pair<String>> properties = department.getProperties();
                for(Pair<String> pair:properties){
                    if(pair.getKey().equals("部门")){
                        result.add(pair.getValue());
                    }
                }
            }
        }
        return result.stream().distinct().collect(Collectors.toList());
    }
}
