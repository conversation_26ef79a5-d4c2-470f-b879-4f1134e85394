package com.quanzhi.auditapiv2.common.dal.dao;

import com.quanzhi.auditapiv2.common.dal.dao.base.IBaseDao;
import com.quanzhi.auditapiv2.common.dal.entity.GatewayEthDataInfo;

import java.util.List;


/**
 * <AUTHOR>
 * @description  网关管理网口配置相关
 * @date 2023/8/24 18:47
 */
public interface IGatewayEthDataInfoDao extends IBaseDao<GatewayEthDataInfo> {

    List<GatewayEthDataInfo> selectListByItem(Long start, Long end, String gatewayIp, String ethName);

    List<GatewayEthDataInfo> groupGatewayIpAndMaxTimeAndEthName();
}
