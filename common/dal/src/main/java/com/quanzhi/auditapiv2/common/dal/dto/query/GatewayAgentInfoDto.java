package com.quanzhi.auditapiv2.common.dal.dto.query;

import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @description 网关探针类
 * @date 2023/4/3 19:46
 */
@Data
public class GatewayAgentInfoDto {

    private String id;
    /**
     * 网关别名
     */
    private String name;

    /**
     * agent 服务端ip
     */
    private String gatewayIp;

    /**
     * agent采集端ip
     */
    private String agentIp;

    /**
     * agent采集端端口
     */
    private Integer agentPort;

    /**
     * 7天内网络平均速率
     */
    private BigDecimal avgBandWidth7d;

    /**
     * 7天内网络最高速率
     */
    private Long maxBandWidth7d;

    /**
     * agent客户端自身cpu使用百分比
     */
    private BigDecimal cpuUsage;

    /**
     * agent客户端自身内存使用百分比
     */
    private BigDecimal memUsage;

    /**
     * 启用禁用
     */
    private short enable;

    /**
     * 运行状态
     */
    private Integer status;

    private String errorMsg;

    /**
     * 网口列表
     */
    private List<String> eths;

    /**
     * 创建时间
     */
    private String createTime;

    private List<String> checkedPorts;

}
