package com.quanzhi.auditapiv2.common.dal.entity.aggRisk;

import com.quanzhi.audit_core.common.risk.PolicySnapshot;
import com.quanzhi.audit_core.common.risk.RiskInfo;
import com.quanzhi.metabase.core.model.ResourceEntity;
import com.quanzhi.metabase.core.model.http.HttpAppResource;
import com.quanzhi.metabase.core.model.http.weakness.ApiWeakness;
import lombok.Data;

import java.io.Serializable;
import java.util.*;

/**
 * @Author: yangzx
 * @Date: 2024/10/10 11:11
 */
@Data
public class AggRiskInfo {

    private String id;

    private String apiUri;

    private String apiUrl;

    private String appName;

    private String appUri;

    private String host;

    private String ip;

    private String account;

    private List<RiskInfo.Entity> entities = new ArrayList<>();

    private String entityType;

    private List<HttpAppResource.Department> departments = new ArrayList<>();

    private String date;

    private Integer level;

    private String levelName;

    private Long firstTime;

    private Long lastTime;

    private String operationId;

    private String name;

    private String desc;

    private Integer state;

    private String stateName;

    private Set<String> deployDomains = new HashSet<>();

    private String type;

    private Boolean riskMark;

    private List<ResourceEntity.Node> nodes = new ArrayList<>();

    private String remark;

    private String suggest;

    private String policyId;

    private PolicySnapshot policySnapshot;

//    private RiskInfo.PolicySnapshotV2 policySnapshotV2;

    private Integer version = 4;

    private Set<String> riskIds = new HashSet<>();

    private Long riskNum;

    /**
     * AI信息
     */
    private AiInfo aiInfo;


    @Data
    public static final class AiInfo implements Serializable {
        /**
         * ai研判理由
         */
        private String riskOptReason;

    }




    public enum RiskStateEnum {

        /**
         * 待确认
         */
        NOT_HANDLE(0, "待确认"),

        /**
         * 已忽略
         */
        HAS_IGNORE(1, "已忽略"),

        /**
         * 已确认
         */
        HAS_HANDLE(2, "已确认");

        private Integer state;

        private String name;

        RiskStateEnum(Integer state, String name) {

            this.state = state;
            this.name = name;
        }

        public Integer getState() {
            return this.state;
        }

        public String getName() {
            return this.name;
        }

        public static RiskStateEnum getRiskStateEnum(Integer state) {

            for (RiskStateEnum riskStateEnum : RiskStateEnum.values()) {
                if (riskStateEnum.getState().equals(state)) {
                    return riskStateEnum;
                }
            }
            return null;
        }
    }

    public enum RiskLevelEnum {

        /**
         * 低风险
         */
        LOW(1, "低危"),

        /**
         * 中风险
         */
        MEDIUM(2, "中危"),

        /**
         * 高风险
         */
        HIGH(3, "高危");

        private Integer level;

        private String name;

        RiskLevelEnum(Integer level, String name) {

            this.level = level;
            this.name = name;
        }

        public Integer getLevel() {
            return this.level;
        }

        public String getName() {
            return this.name;
        }

        public static RiskLevelEnum getRiskLevelEnum(Integer level) {

            for (RiskLevelEnum riskLevelEnum : RiskLevelEnum.values()) {
                if (riskLevelEnum.getLevel().equals(level)) {
                    return riskLevelEnum;
                }
            }
            return null;
        }
    }

}
