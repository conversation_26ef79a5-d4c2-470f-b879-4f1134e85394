//package com.quanzhi.auditapiv2.common.dal.dao.impl;
//
//import com.mongodb.BasicDBObject;
//import com.mongodb.DBCollection;
//import com.mongodb.MongoClient;
//import com.mongodb.MongoNamespace;
//import com.mongodb.client.MongoCollection;
//import com.mongodb.client.MongoDatabase;
//import com.mongodb.client.model.RenameCollectionOptions;
//import com.quanzhi.auditapiv2.common.dal.dao.IIndexDefine;
//import com.quanzhi.auditapiv2.common.dal.dao.IUrlStructureDao;
//import com.quanzhi.auditapiv2.common.dal.dao.base.impl.BaseDaoImpl;
//import com.quanzhi.auditapiv2.common.dal.dao.common.BatchUpdateOption;
//import com.quanzhi.auditapiv2.common.dal.entity.UrlStructure;
//import org.slf4j.Logger;
//import org.slf4j.LoggerFactory;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.data.mongodb.core.aggregation.Aggregation;
//import org.springframework.data.mongodb.core.aggregation.AggregationResults;
//import org.springframework.data.mongodb.core.aggregation.TypedAggregation;
//import org.springframework.stereotype.Repository;
//
//import java.util.List;
//
//@Repository
//public class UrlStructureDaoImpl extends BaseDaoImpl<UrlStructure> implements IUrlStructureDao, IIndexDefine {
//
//    private Logger logger = LoggerFactory.getLogger(UrlStructureDaoImpl.class);
//
//    private String collectionTemporaryName = "urlStructureTemporary";
//
//    private String collectionName = "urlStructure";
//
//    @Autowired
//    private MongoClient mongoClient;
//
//    private DBCollection collection;
//
//    @Override
//    public void batchUpdateByOption(List<BatchUpdateOption> options) {
//
//        if(collection == null) {
//            collection = mongoClient.getDB("audit").getCollection(collectionTemporaryName);
//        }
//
//        bathUpdate(collection, this.collectionTemporaryName, options);
//    }
//
//    @Override
//    public void batchUpdateByOptionInUrlStructure(List<BatchUpdateOption> options) {
//
//        if(collection == null) {
//            collection = mongoClient.getDB("audit").getCollection(collectionName);
//        }
//
//        bathUpdate(collection, this.collectionName, options);
//    }
//
//
//    @Override
//    public void dropCollection () {
//        mongoTemplate.dropCollection(collectionTemporaryName);
//
//        mongoTemplate.createCollection(collectionTemporaryName);
//
//        mongoTemplate.getCollection(collectionTemporaryName).createIndex(new BasicDBObject("appUri", 1).append("level", 1));
//
//        mongoTemplate.getCollection(collectionTemporaryName).createIndex(new BasicDBObject("appUri", 1).append("preUrl", 1));
//
//        mongoTemplate.getCollection(collectionTemporaryName).createIndex(new BasicDBObject("url", 1).append("appId", 1));
//
//        mongoTemplate.getCollection(collectionTemporaryName).createIndex(new BasicDBObject("appId", 1).append("preUrl", 1));
//
//        mongoTemplate.getCollection(collectionTemporaryName).createIndex(new BasicDBObject("host", 1).append("level", 1));
//
//        mongoTemplate.getCollection(collectionTemporaryName).createIndex(new BasicDBObject("appId", 1).append("nodeType", 1).append("preUrls", 1));
//
//        mongoTemplate.getCollection(collectionTemporaryName).createIndex(new BasicDBObject("appUri", 1).append("nodeType", 1).append("url", 1));
//
//    }
//
//    /**
//     * 将重新生成的 urlStructureTemporary 表重命名为 UrlStructure
//     */
//    @Override
//    public void urlStructureTempory2UrlStructure () {
//
//        try {
//
//            logger.info("开始替换urlStructure表");
//            mongoTemplate.dropCollection(collectionName);
//
//            MongoDatabase db = mongoTemplate.getDb();
//            String dbName = db.getName();
//
//            MongoCollection dbCollection = db.getCollection(collectionTemporaryName);
//
//            dbCollection.renameCollection(new MongoNamespace(dbName + "." + collectionName), new RenameCollectionOptions().dropTarget(true));
//
//            logger.info("替换urlStructure表成功");
//
//        } catch (Exception e) {
//
//            logger.error("替换urlStructure表失败",e);
//        }
//
//    }
//
//    @Override
//    public void createIndex() {
//        mongoTemplate.getCollection(getCollectionName()).createIndex(new BasicDBObject("appId", 1).append("preUrl", 1));
//    }
//
//    @Override
//    public void batchUpdateByOption(List<BatchUpdateOption> options,Boolean updateSingleApp) {
//
//        if(collection == null) {
//            collection = mongoClient.getDB("audit").getCollection( updateSingleApp ? collectionName : collectionTemporaryName);
//        }
//
//        bathUpdate(collection, updateSingleApp ? this.collectionName : this.collectionTemporaryName, options);
//    }
//
//    @Override
//    public <T> List<T> aggregateQuery(TypedAggregation aggregation, Class<T> entityClass) {
//        AggregationResults results = mongoTemplate.aggregate(aggregation, "urlStructure", entityClass);
//        return results.getMappedResults();
//    }
//
//    @Override
//    public <T> List<T> aggregate(Aggregation aggregation, Class<T> entityClass, String collection) {
//        AggregationResults results = mongoTemplate.aggregate(aggregation, collection, entityClass);
//        return results.getMappedResults();
//    }
//}
