package com.quanzhi.auditapiv2.common.dal.dto.aggRisk;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;
import java.util.Map;

/**
 * @Author: yangzx
 * @Date: 2024/10/9 11:25
 */
@Data
public class RiskV2OperatorDto {

    @ApiModelProperty("应用")
    private String host;

    @ApiModelProperty(value = "是否忽略大小写", name = "hostIgnoreCase")
    private Boolean hostIgnoreCase;

    @ApiModelProperty("风险ID")
    private String operationId;

    @ApiModelProperty("节点id")
    private String nodeId;

    @ApiModelProperty("应用名称")
    private String appName;

    @ApiModelProperty("接口或应用")
    private String apiOrAppUri;

    @ApiModelProperty("风险名称")
    private List<String> name;

    @ApiModelProperty("风险类型")
    private List<String> type;

    @ApiModelProperty("风险等级")
    private List<Integer> level;

    @ApiModelProperty("风险状态")
    private List<Integer> state;

    @ApiModelProperty("风险主体")
    private String entityValue;

    @ApiModelProperty("主体类型")
    private String entitiesType;

    @ApiModelProperty("备注")
    private String remark;

    @ApiModelProperty("置顶状态")
    private Boolean riskMark;

    @ApiModelProperty("首次发现时间开始")
    private Long firstTimeStart;

    @ApiModelProperty("首次发现时间结束")
    private Long firstTimeEnd;

    @ApiModelProperty("最近活跃时间开始")
    private Long lastTimeStart;

    @ApiModelProperty("最近活跃时间结束")
    private Long lastTimeEnd;

    @ApiModelProperty("排序方式")
    private Integer sort;

    @ApiModelProperty("排序字段")
    private String sortField;

    @ApiModelProperty("每页显示数量")
    private Integer limit = 10;

    @ApiModelProperty("页码")
    private Integer page = 1;

    @ApiModelProperty("分组字段")
    private String groupField;

    @ApiModelProperty("批量操作id")
    private List<String> ids;

    @ApiModelProperty("操作id")
    private String id;

    @ApiModelProperty("操作人")
    private String operateName;

    @ApiModelProperty("批量忽略")
    private List<String> ignoreList;

    @ApiModelProperty("加白")
    private Map<String, String> whiteMap;

    @ApiModelProperty("策略id")
    private String policyId;

    @ApiModelProperty("风险id")
    private String aggRiskId;

    @ApiModelProperty("权限条件")
    private Map<String,Object> dataPermissionMap;

    /**
     * 分组字段枚举
     */
    public enum GroupFieldEnum {

        RISK_NAME("name"),

        RISK_LEVEL("level"),

        RISK_STATE("state"),

        RISK_TYPE("type"),

        RISK_ENTITY_TYPE("entitiesType"),

        MARK_STATE("riskMark");

        String name;

        GroupFieldEnum(String name) {

            this.name = name;
        }

        public String getName() {
            return name;
        }

    }

    @Mapper
    public interface RiskV2OperatorDtoMapper {

        RiskV2OperatorDto.RiskV2OperatorDtoMapper INSTANCE = Mappers.getMapper(RiskV2OperatorDto.RiskV2OperatorDtoMapper.class);

        RiskV2OperatorDto convert(AggRiskOperatorDto aggRiskOperatorDto);
    }

}
