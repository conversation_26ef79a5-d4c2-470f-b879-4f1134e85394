package com.quanzhi.auditapiv2.common.dal.entity;

import com.quanzhi.auditapiv2.common.dal.enums.NacosSingleKeyConfigEnum;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2020/12/29 上午11:36
 */
@NoArgsConstructor
@Data
public class NacosSingleKey {

    /**
     * @see NacosSingleKeyConfigEnum
     */
    private String key;

    private Object value;

    private Object otherField;

    public NacosSingleKey(String key, Object value) {

        this.key = key;
        this.value = value;
    }
    public NacosSingleKey(String key, Object value, Object otherField) {

        this.key = key;
        this.value = value;
        this.otherField = otherField;
    }
}
