package com.quanzhi.auditapiv2.common.dal.dto.policy;

import com.alibaba.fastjson.JSONObject;
import com.quanzhi.auditapiv2.common.dal.dto.BaseDto;
import com.quanzhi.auditapiv2.common.dal.entity.policy.ScanPolicyConfig;
import com.quanzhi.auditapiv2.common.dal.entity.policy.ScanPolicyModule;
import com.quanzhi.auditapiv2.common.util.utils.SimpleTransformer;
import lombok.Data;
//import org.bson.BSON;

import java.util.List;

/**
 * @Author: Linlm
 * @Description: 工具配置
 * @Date: Created in 2020/6/1 下午1:32
 */
@Data
public class ScanPolicyDto extends BaseDto {
    private static final long serialVersionUID = -6168526881475480007L;
//
//    static {
//        BSON.addEncodingHook(ScanPolicyDto.class, new SimpleTransformer());
//    }

    /**
     * 策略code: 唯一编号
     */
    private String code;

    /**
     * 策略名称
     */
    private String name;

    /**
     * 策略描述：检查内容
     */
    private String description;

    /**
     * 策略版本
     */
    private String version;

    /**
     * 策略模块
     */
    private List<ScanPolicyModule> modules;

    /**
     * 专用工具、基础工具
     */
    private String type;

    /**
     * 运行配置：json串
     */
    private JSONObject params;

    /**
     * 配置添加时间
     */
    private Long createTime;

    /**
     * 配置修改时间
     */
    private Long updateTime;

    /**
     * 工具的删除标记
     */
    private Boolean delFlag;

    /**
     * 工具配置
     */
    private ScanPolicyConfig policyConfig;
}
