package com.quanzhi.auditapiv2.common.dal.dto.home;

import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * @Author: yangzx
 * @Date: 2024/10/9 10:25
 */
@Data
@Builder
public class DataFlowInfoDto {

    @ApiModelProperty("标签访问分布 key标签id value标签当月访问量分布")
    private Map<String, Map<String, Long>> dataLabelVisit;

}
