package com.quanzhi.auditapiv2.common.dal.dto.docx;

import lombok.Data;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 *
 * 《脱敏策略不一致》
 *
 *
 * @Project:
 * @Module ID:
 * @Comments:
 * @JDK version used:      <JDK1.8>
 * <AUTHOR> [<EMAIL>]
 * @since 2020-08-28-下午16:52:18
 */
@Data
public class Fakeantidata extends ApiWeaknessExportWord {

    @Mapper
    public interface FakeantidataMapper {

        Fakeantidata.FakeantidataMapper INSTANCE = Mappers.getMapper(Fakeantidata.FakeantidataMapper.class);
        Fakeantidata convert(ApiWeaknessExportWord apiWeaknessExportWord);
    }
}
