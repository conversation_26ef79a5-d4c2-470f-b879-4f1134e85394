//package com.quanzhi.auditapiv2.common.dal.dao;
//
//import com.quanzhi.audit_core.common.model.RiskPolicy;
//import com.quanzhi.auditapiv2.common.dal.dao.base.IMetabaseDao;
//
//import java.util.List;
//
///**
// * 
// * 《异常规则持久层接口》
// * 
// * 
// * @Project: 
// * @Module ID:
// * @Comments: 
// * @JDK version used:      <JDK1.8> 
// * <AUTHOR> [<EMAIL>]
// * @since 2020-04-08-上午9:56:10
// */
//public interface IRiskPolicyDao extends IMetabaseDao<RiskPolicy> {
//
//	/**
//	 * 查询异常规则列表(分页)
//	 * @Comments:  <对此方法的描述，可以引用系统设计中的描述>
//	 * <AUTHOR> [<EMAIL>]
//	 * @since 2020-04-08 9:56
//	 * @param
//	 * @return
//	 */
//	List<RiskPolicy> selectRiskPolicyList() throws Exception;
//
//	/**
//	 * id查询异常规则详情
//	 * @Comments:  <对此方法的描述，可以引用系统设计中的描述>
//	 * <AUTHOR> [<EMAIL>]
//	 * @since 2020-04-08 9:56
//	 * @param
//	 * @return
//	 */
//	RiskPolicy selectRiskPolicyById(String id) throws Exception;
//
//	long countBy(RiskPolicy riskPolicy);
//
//}
