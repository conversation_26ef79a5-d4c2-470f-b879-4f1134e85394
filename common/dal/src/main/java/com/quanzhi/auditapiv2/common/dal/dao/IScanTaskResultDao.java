package com.quanzhi.auditapiv2.common.dal.dao;

import com.quanzhi.auditapiv2.common.dal.dao.base.IBaseDao;
import com.quanzhi.auditapiv2.common.dal.entity.ScanTaskResult;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;

import java.util.List;

/**
 *
 * 《报告任务结果持久层接口》
 *
 *
 * @Project:
 * @Module ID:
 * @Comments:
 * @JDK version used:      <JDK1.8> 
 * <AUTHOR> [<EMAIL>]
 * @since 2019-07-23-上午9:09:10
 */
public interface IScanTaskResultDao extends IBaseDao<ScanTaskResult> {

	List<ScanTaskResult> selectScanTaskResultList(String taskCode, Integer state, Integer page, Integer limit);

	List<ScanTaskResult> selectScanTaskResultList(String taskCode, String name, Integer state, Integer page, Integer limit);

	Long totalCount(String taskCode, Integer state);

	Long totalCount(String taskCode, String name, Integer state);

	ScanTaskResult selectScanTaskResultById(String id);

	ScanTaskResult insertScanTaskResult(ScanTaskResult result);

	boolean exists(Query query);

	void deleteByTaskId(String taskId);

	List<ScanTaskResult> findAndRemoveByTaskId(String taskId);

	List<ScanTaskResult> findNotStoppedByTaskId(String taskId);

	List<ScanTaskResult> findNotStoppedByTaskCode(String taskCode);

	ScanTaskResult getEarliestScanTaskResultId(String taskCode);

	void updateResultByStatus(String resultId, int status);

	void updateResultByStatus(String resultId, Update update);

	void update(Query query,Update update);
}
