package com.quanzhi.auditapiv2.common.dal.dto.task;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 描述: 任务运行日志记录DTO
 *
 * @author: danniel_yu
 * @date: 2020/11/25 13:42
 */
@Data
public class SparkTaskRecord {
    /**
     * 任务ID
     */
    @ApiModelProperty(value = "任务ID", name = "taskId")
    private String taskId;

    /**
     * 任务名称
     */
    @ApiModelProperty(value = "任务名称", name = "name")
    private String name;

    /**
     * 任务创建时间
     */
    @ApiModelProperty(value = "任务创建时间", name = "createTime")
    private Long createTime;

    /**
     * 任务结束时间
     */
    @ApiModelProperty(value = "任务结束时间", name = "endTime")
    private Long endTime;

    /**
     * 任务状态
     */
    @ApiModelProperty(value = "任务状态", name = "state")
    private String state;


}
