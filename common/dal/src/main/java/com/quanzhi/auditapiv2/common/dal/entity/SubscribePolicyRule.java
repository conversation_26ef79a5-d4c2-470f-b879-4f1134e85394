package com.quanzhi.auditapiv2.common.dal.entity;

import com.quanzhi.auditapiv2.common.util.utils.DataUtil;
import lombok.Data;

import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * @date 2020/8/14 下午5:02
 */
@Data
public class SubscribePolicyRule {

    private String id;

    /**
     * 订阅类型
     *
     * @see TypeEnum
     */
    private String type;

    /**
     * 订阅名称
     */
    private String name;

    /**
     * 订阅范围
     *
     * @see ScopeEnum
     */
    private String scope;

    /**
     * 自定义字段
     */
    private String scopeField;

    /**
     * 自定义字段内容
     */
    private String scopeValue;

    /**
     * 范围值
     */
    private List<String> scopeList;

    /**
     * 订阅条件
     */
    private List<SubscribeCondition> conditionList;

    /**
     * 订阅规则
     */
    private String expr;

    /**
     * 订阅用途
     *
     * @see OutputEnum
     */
    private List<String> output;

    /**
     * 订阅状态
     */
    private Boolean enabled;

    /**
     * 订阅周期
     *
     * @see CycleEnum
     */
    private String cycle;

    /**
     * 执行时间
     *
     * @see CycleEnum
     */
    private String cron;

    /**
     * 单次执行时间
     */
    private Long startTime;

    /**
     * 关联定时任务名称
     */
    private String scheduleName;

    /**
     * syslog 配置id
     */
    private String sysLogConfigId;

    /**
     * kafka 配置id
     */
    private String kafkaConfigId;

    /**
     * email 配置id
     */
    private String emailGroup;

    /**
     * soc 配置id
     */
    private String socConfigId;

    /**
     * webhook 配置id
     */
    private String webhookConfigId;

    /**
     * 站内通知开关
     */
    private Boolean systemNotify;

    /**
     * 是否使用自定义插件推送数据
     */
    private Boolean useCustomPlugin;
    /**
     * 插件id
     */
    private String pluginId;
    /**
     * 插件名称
     */
    private List<String> pluginNames;

    /**
     * 通知对象
     */
    private List<String> notifyObject;

    /**
     * 自定义选择字段
     */
    private Set<String> customFields;

    public enum CycleEnum {
        /**
         * 实时增量同步
         */
        REALTIME_INCREMENT,
        /**
         * 每日全量同步
         */
        FULL_DAILY
    }

    public enum ScopeEnum {
        APP, APICLASSIFICATION, VISITDOMAIN, CUSTOM
    }

    public enum OutputEnum {
        SYSLOG, KAFKA, PRODUCT, EMAIL, SYSTEM,WEBHOOK
    }

    public enum TypeEnum {
        /**
         * 资产
         */
        RESOURCE,

        /**
         * 弱点
         */
        WEAKNESS,

        /**
         * 异常
         */
        RISK,

        /**
         * 事件
         */
        EVENT,

        /**
         * 应用
         */
        APP,

        /**
         * 操作日志
         */
        AUDIT_LOG
    }

    /**
     * 内部功能订阅topic
     */
    private String topic;

    @Data
    public static class SubscribeCondition {

        private String seq;

        private String policy;

        private String operate;

        private String content;
    }

    private String operateName;

    private Long createTime;

    private Long updateTime;

    /**
     * 保存时检验必要字段
     *
     * @param subscribePolicyRule
     * @return
     */
    public static void checkNecessary(SubscribePolicyRule subscribePolicyRule) throws EntityNecessaryLackException {

        if (DataUtil.isEmpty(subscribePolicyRule.getName())) {
            throw new EntityNecessaryLackException("缺少name");
        }

        if ((subscribePolicyRule.useCustomPlugin == null || Boolean.FALSE.equals(subscribePolicyRule.useCustomPlugin)) && DataUtil.isEmpty(subscribePolicyRule.getOutput())) {
            throw new EntityNecessaryLackException("缺少订阅方式");
        }

        if (DataUtil.isEmpty(subscribePolicyRule.getConditionList()) && !CycleEnum.FULL_DAILY.name().equals(subscribePolicyRule.getCycle()) && Boolean.FALSE.equals(subscribePolicyRule.useCustomPlugin)) {
            throw new EntityNecessaryLackException("缺少订阅条件");
        }

        if (DataUtil.isEmpty(subscribePolicyRule.getCycle())) {
            throw new EntityNecessaryLackException("缺少订阅周期");
        }

    }
}
