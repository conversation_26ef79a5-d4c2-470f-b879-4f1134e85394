package com.quanzhi.auditapiv2.common.dal.dao;

import com.quanzhi.auditapiv2.common.dal.dao.base.IBaseDao;
import com.quanzhi.auditapiv2.common.dal.entity.GatewayEthConfig;
import com.quanzhi.auditapiv2.common.dal.entity.GatewaySSLPem;

import java.util.List;

/**
 * <AUTHOR>
 * @description  网关管理网口配置相关
 * @date 2023/8/24 18:47
 */
public interface IGatewayEthConfigDao extends IBaseDao<GatewayEthConfig> {


    List<GatewayEthConfig> selectEthConfigPage(String gatewayIp, Integer page, Integer limit);

    Long count(String gatewayIp);

    GatewayEthConfig findByGatewayIpAndEthName(String gatewayIp, String ethName);

    List<GatewayEthConfig> findByGatewayIp(String gatewayIp);
}
