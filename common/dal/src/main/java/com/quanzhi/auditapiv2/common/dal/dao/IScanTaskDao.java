package com.quanzhi.auditapiv2.common.dal.dao;

import com.quanzhi.auditapiv2.common.dal.dao.base.IBaseDao;
import com.quanzhi.auditapiv2.common.dal.dto.ResultFilterRuleDto;
import com.quanzhi.auditapiv2.common.dal.entity.ScanTask;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;

import java.util.List;

/**
 *
 * 《报告任务持久层接口》
 *
 *
 * @Project:
 * @Module ID:
 * @Comments:
 * @JDK version used:      <JDK1.8> 
 * <AUTHOR> [<EMAIL>]
 * @since 2019-07-23-上午9:09:10
 */
public interface IScanTaskDao extends IBaseDao<ScanTask> {

	List<ScanTask> selectScanTaskList();

	ScanTask selectScanTaskById(String id);

	ScanTask insert(ScanTask scanTask);

	ScanTask findOneByQuery(Query query);

	ScanTask findByCode(String code);

	ScanTask getScanTaskByFileHash(String fileHash);

	void updateScanTaskByTaskCode(Query query, Update update);

	void addTaskFilters(String taskCode, String scanRule,List<ResultFilterRuleDto> filters);

    void deleteByName(String name);
}
