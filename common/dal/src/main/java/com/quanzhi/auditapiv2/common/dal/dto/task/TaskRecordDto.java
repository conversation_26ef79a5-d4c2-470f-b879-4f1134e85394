package com.quanzhi.auditapiv2.common.dal.dto.task;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 描述: 任务运行日志记录DTO
 *
 * @author: danniel_yu
 * @date: 2020/11/23 14:44
 */
@Data
public class TaskRecordDto {
    /**
     * 任务ID
     */
    @ApiModelProperty(value = "任务ID", name = "taskId")
    private String taskId;

    /**
     * 任务名称
     */
    @ApiModelProperty(value = "任务名称", name = "taskName")
    private String taskName;

    /**
     * 任务开始时间
     */
    @ApiModelProperty(value = "任务开始时间", name = "startTime")
    private Long startTime;

    /**
     * 任务最近更新时间
     */
    @ApiModelProperty(value = "任务最近更新时间", name = "lastUpdateTime")
    private Long lastUpdateTime;

    /**
     * 任务结束时间
     */
    @ApiModelProperty(value = "任务结束时间", name = "endTime")
    private Long endTime;

    /**
     * 任务状态
     */
    @ApiModelProperty(value = "任务状态", name = "taskStatus")
    private String taskStatus;


}
