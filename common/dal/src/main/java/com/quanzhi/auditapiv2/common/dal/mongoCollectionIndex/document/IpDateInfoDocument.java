package com.quanzhi.auditapiv2.common.dal.mongoCollectionIndex.document;

import com.quanzhi.auditapiv2.common.dal.mongoCollectionIndex.MongoCollectionConstant;
import org.springframework.data.mongodb.core.index.CompoundIndex;
import org.springframework.data.mongodb.core.index.CompoundIndexes;
import org.springframework.data.mongodb.core.index.Indexed;
import org.springframework.data.mongodb.core.mapping.Document;

import java.util.Set;

/**
 * <AUTHOR>
 * @date 2020/11/16 上午11:27
 */

@Document(collection = MongoCollectionConstant.IP_DATE_INFO)
@CompoundIndexes({
        @CompoundIndex(name = "date_ip", def = "{date:-1,ip:-1}",background = true),
        // 文件概览查询排序
        @CompoundIndex(name = "date_downloadFileDistinctCnt", def = "{date:-1,downloadFileDistinctCnt:-1}",background = true),
        // IP概览：单事件最大去重数据量TOP10
        @CompoundIndex(name = "date_maxRspDataDistinctCnt_ip", def = "{date:-1,maxRspDataDistinctCnt:-1,ip:-1}",background = true),
        // IP概览：单日最大去重数据量TOP10
        @CompoundIndex(name = "date_rspDataDistinctCnt_ip", def = "{date:-1,rspDataDistinctCnt:-1,ip:-1}",background = true)
})
public class IpDateInfoDocument {
    @Indexed(background = true)
    private Long updateTime;

    @Indexed(background = true)
    private Set<String> accessDomainIds;

    @Indexed(background = true)
    private Long visitCnt;
}
