package com.quanzhi.auditapiv2.common.dal.dao.impl;


import com.quanzhi.auditapiv2.common.dal.dao.IAuditLogDao;
import com.quanzhi.auditapiv2.common.dal.dao.base.impl.BaseDaoImpl;
import com.quanzhi.auditapiv2.common.dal.dto.AuditLogSearchDto;
import com.quanzhi.auditapiv2.common.dal.entity.AuditLogModel;
import com.quanzhi.auditapiv2.common.util.utils.DataUtil;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.aggregation.Aggregation;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.List;

/**
 * Created by shenji<PERSON> on 2018/1/3.
 */
@Repository("auditLogDao")
public class AuditLogDaoImpl extends BaseDaoImpl<AuditLogModel> implements IAuditLogDao {

    @Resource
    private MongoTemplate mongoTemplate;

    private final String collectionName = "auditLog";

    @Override
    public Criteria getCriteriaBySearch(AuditLogSearchDto searchDto) {
        Criteria where = null;
        if (DataUtil.isNotEmpty(searchDto.getStartTime()) && DataUtil.isNotEmpty(searchDto.getEndTime())) {
            if (DataUtil.isNotEmpty(where)) {
                where.and("logTime").gte(searchDto.getStartTime()).lte(searchDto.getEndTime());
            } else {
                where = Criteria.where("logTime").gte(searchDto.getStartTime()).lte(searchDto.getEndTime());
            }
        } else {
            if (DataUtil.isNotEmpty(searchDto.getStartTime())) {
                if (DataUtil.isNotEmpty(where)) {
                    where.and("logTime").gte(searchDto.getStartTime());
                } else {
                    where = Criteria.where("logTime").gte(searchDto.getStartTime());
                }
            }
            if (DataUtil.isNotEmpty(searchDto.getEndTime())) {
                if (DataUtil.isNotEmpty(where)) {
                    where.and("logTime").lte(searchDto.getEndTime());
                } else {
                    where = Criteria.where("logTime").lte(searchDto.getEndTime());
                }
            }
        }
        if (DataUtil.isNotEmpty(searchDto.getLog())) {
            if (DataUtil.isNotEmpty(where)) {
                where.and("log").regex(searchDto.getLog());
            } else {
                where = Criteria.where("log").regex(searchDto.getLog());
            }
        }
        if (DataUtil.isNotEmpty(searchDto.getIp())) {
            if (DataUtil.isNotEmpty(where)) {
                where.and("ip").is(searchDto.getIp());
            } else {
                where = Criteria.where("ip").is(searchDto.getIp());
            }
        }
        if (DataUtil.isNotEmpty(searchDto.getUsername())) {
            if (DataUtil.isNotEmpty(where)) {
                where.and("username").is(searchDto.getUsername());
            } else {
                where = Criteria.where("username").is(searchDto.getUsername());
            }
        }
        if (DataUtil.isNotEmpty(searchDto.getUserAgent())) {
            if (DataUtil.isNotEmpty(where)) {
                where.and("userAgent").regex(searchDto.getUserAgent());
            } else {
                where = Criteria.where("userAgent").regex(searchDto.getUserAgent());
            }
        }

        return where;
    }

    @Override
    public Long getCount(AuditLogSearchDto searchDto) {
        Criteria where = getCriteriaBySearch(searchDto);
        Query query = DataUtil.isNotEmpty(where) ? new Query(where) : new Query();
        return mongoTemplate.count(query, AuditLogModel.class, collectionName);
    }

    @Override
    public List<AuditLogModel> getList(AuditLogSearchDto searchDto, Integer start, Integer limit) {
        Criteria where = getCriteriaBySearch(searchDto);
        Aggregation aggregation = null;
        if (DataUtil.isNotEmpty(where)) {
            aggregation = Aggregation.newAggregation(
                    Aggregation.match(where),
                    Aggregation.sort(Sort.Direction.DESC, "_id"),
                    Aggregation.skip(start),
                    Aggregation.limit(limit)
            );
        } else {
            aggregation = Aggregation.newAggregation(
                    Aggregation.sort(Sort.Direction.DESC, "_id"),
                    Aggregation.skip(start),
                    Aggregation.limit(limit)
            );
        }
        return mongoTemplate.aggregate(aggregation, collectionName, AuditLogModel.class).getMappedResults();
    }

    @Override
    public void insertLog(AuditLogModel auditLogModel) {
        mongoTemplate.insert(auditLogModel, collectionName);
    }
}
