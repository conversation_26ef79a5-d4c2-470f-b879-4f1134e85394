package com.quanzhi.auditapiv2.common.dal.dao.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.nacos.api.config.annotation.NacosConfigListener;
import com.quanzhi.audit_core.common.model.RiskLevel;
import com.quanzhi.audit_core.common.utils.DataUtil;
import com.quanzhi.auditapiv2.common.dal.dao.IDataLabelNacosDao;
import com.quanzhi.auditapiv2.common.dal.dao.IIpInfoDao;
import com.quanzhi.auditapiv2.common.dal.dao.IRiskLevelDao;
import com.quanzhi.auditapiv2.common.dal.dao.impl.common.Module;
import com.quanzhi.auditapiv2.common.dal.dao.impl.common.QueryAdapterRegistry;
import com.quanzhi.auditapiv2.common.dal.dto.IpSearchDetailDto;
import com.quanzhi.auditapiv2.common.dal.dto.IpSearchDto;
import com.quanzhi.auditapiv2.common.dal.dto.common.CommonGroupDto;
import com.quanzhi.auditapiv2.common.dal.dto.query.IpInfoCriteriaDto;
import com.quanzhi.auditapiv2.common.dal.dto.query.Operator;
import com.quanzhi.auditapiv2.common.dal.entity.IpInfo;
import com.quanzhi.auditapiv2.common.dal.enums.IpExportTitleEnum;
import com.quanzhi.auditapiv2.common.util.utils.ConstantUtil;
import com.quanzhi.auditapiv2.common.util.utils.DateUtil;
import com.quanzhi.auditapiv2.common.util.utils.ReflectUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.bson.Document;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.BulkOperations;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.aggregation.Aggregation;
import org.springframework.data.mongodb.core.aggregation.AggregationOperation;
import org.springframework.data.mongodb.core.aggregation.GroupOperation;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.data.util.Pair;
import org.springframework.stereotype.Repository;

import java.util.*;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * @ClassName IpInfoDaoImpl
 * @Description TODO
 * <AUTHOR>
 * @Date 2020/11/21 15:45
 **/
@Repository
public class IpInfoDaoImpl implements IIpInfoDao {
    private final String collectionName = "ipInfo";

    @Autowired
    public MongoTemplate mongoTemplate;

    @Autowired
    private IRiskLevelDao riskLevelDao;

    @Autowired
    public IDataLabelNacosDao dataLabelNacosDao;

    @Autowired
    public QueryAdapterRegistry queryAdapterRegistry;


    private final List<String> defaultNetworkList = Arrays.asList("局域网-其他", "互联网-境内", "互联网-境外");

    // level : levelName
    private Map<Integer, String> riskLevelMap = new HashMap<>();

    @NacosConfigListener(dataId = "common.risk.level.config.json", groupId = "common", timeout = 30000)
    public void onMessage(String msg) {
        List<RiskLevel> riskLevelList = JSON.parseArray(msg, RiskLevel.class);

        getRiskLevelMap(riskLevelList);
    }

    private void getRiskLevelMap(List<RiskLevel> riskLevelList) {
        if (DataUtil.isNotEmpty(riskLevelList)) {
            for (RiskLevel riskLevel : riskLevelList) {
                if ("IP".equals(riskLevel.getLevelType())) {
                    riskLevelMap.put(riskLevel.getLevel(), riskLevel.getLevelName());
                }
            }
        }
    }

    @Override
    public List<com.quanzhi.audit_core.common.model.IpInfo> getIpInfoList(IpInfoCriteriaDto ipInfoCriteriaDto) {
        Query query = new Query();

        //查询条件
        if (DataUtil.isNotEmpty(ipInfoCriteriaDto.getIp())) {
            query.addCriteria(Criteria.where("ip").regex("^" + Pattern.quote(ipInfoCriteriaDto.getIp())));
        }
        if (DataUtil.isNotEmpty(ipInfoCriteriaDto.getThreatLabels())) {
            query.addCriteria(Criteria.where("threatLabels").in(ipInfoCriteriaDto.getThreatLabels()));
        }
        if (DataUtil.isNotEmpty(ipInfoCriteriaDto.getCity())) {
            query.addCriteria(Criteria.where("city").is(ipInfoCriteriaDto.getCity()));
        }
        if (DataUtil.isNotEmpty(ipInfoCriteriaDto.getDate())) {
            query.addCriteria(Criteria.where("date").is(ipInfoCriteriaDto.getDate()));
        }
        if (DataUtil.isNotEmpty(ipInfoCriteriaDto.getStartTime()) && DataUtil.isNotEmpty(ipInfoCriteriaDto.getEndTime())) {
            query.addCriteria(Criteria.where("firstDate")
                    .gte(ipInfoCriteriaDto.getStartTime())
                    .lte(ipInfoCriteriaDto.getEndTime()));
        }
        if (DataUtil.isNotEmpty(ipInfoCriteriaDto.getStartDate()) && DataUtil.isNotEmpty(ipInfoCriteriaDto.getEndDate())) {
            query.addCriteria(Criteria.where("lastDate")
                    .gte(ipInfoCriteriaDto.getStartDate())
                    .lte(ipInfoCriteriaDto.getEndDate()));
        }

        //排序
        if (DataUtil.isNotEmpty(ipInfoCriteriaDto.getField()) && DataUtil.isNotEmpty(ipInfoCriteriaDto.getSort())) {
            if (ipInfoCriteriaDto.getSort() == ConstantUtil.Sort.ASC) {
                query.with(Sort.by(Sort.Order.asc(ipInfoCriteriaDto.getField())));
            } else if (ipInfoCriteriaDto.getSort() == ConstantUtil.Sort.DESC) {
                query.with(Sort.by(Sort.Order.desc(ipInfoCriteriaDto.getField())));
            } else {
                query.with(Sort.by(Sort.Order.desc(ipInfoCriteriaDto.getField())));
            }
        }

        //分页
        if (DataUtil.isNotEmpty(ipInfoCriteriaDto.getPage()) && DataUtil.isNotEmpty(ipInfoCriteriaDto.getLimit())) {
            query.skip((ipInfoCriteriaDto.getPage() - 1) * ipInfoCriteriaDto.getLimit());
            query.limit(ipInfoCriteriaDto.getLimit());
        } else {
            query.skip(0);
            query.limit(10);
        }
        if (DataUtil.isNotEmpty(ipInfoCriteriaDto.getNodeId())) {
            query.addCriteria(Criteria.where("nodes").elemMatch(Criteria.where("nid").is(ipInfoCriteriaDto.getNodeId())));
        }

        return mongoTemplate.find(query, com.quanzhi.audit_core.common.model.IpInfo.class);
    }

    @Override
    public List<Document> getIpInfoListFields(IpInfoCriteriaDto ipInfoCriteriaDto, List<String> fields) {
        Criteria criteria = this.getCriteria(ipInfoCriteriaDto);
        Query query = new Query(criteria);

        if (DataUtil.isNotEmpty(ipInfoCriteriaDto.getField()) && DataUtil.isNotEmpty(ipInfoCriteriaDto.getSort())) {
            if (ipInfoCriteriaDto.getSort() == ConstantUtil.Sort.ASC) {
                query.with(Sort.by(Sort.Order.asc(ipInfoCriteriaDto.getField())));
            } else if (ipInfoCriteriaDto.getSort() == ConstantUtil.Sort.DESC) {
                query.with(Sort.by(Sort.Order.desc(ipInfoCriteriaDto.getField())));
            } else {
                query.with(Sort.by(Sort.Order.desc(ipInfoCriteriaDto.getField())));
            }
        }

        if (DataUtil.isNotEmpty(ipInfoCriteriaDto.getPage()) && DataUtil.isNotEmpty(ipInfoCriteriaDto.getLimit())) {
            query.skip((ipInfoCriteriaDto.getPage() - 1) * ipInfoCriteriaDto.getLimit());
            query.limit(ipInfoCriteriaDto.getLimit());
        } else {
            query.skip(0);
            query.limit(100000);
        }

        if (DataUtil.isNotEmpty(fields)) {
            fields.stream().forEach(field -> {
                query.fields().include(field);
            });
        } else {
            for (IpExportTitleEnum ipExportTitleEnum : IpExportTitleEnum.values()) {
                query.fields().include(ipExportTitleEnum.getTitle());
            }
        }

        return mongoTemplate.find(query, Document.class, collectionName);
    }

    @Override
    public List<com.quanzhi.audit_core.common.model.IpInfo> getIpInfoListByAccount(String account) {
        return mongoTemplate.find(new Query().addCriteria(Criteria.where("relatedAccountList").is(account)), com.quanzhi.audit_core.common.model.IpInfo.class);
    }

    @Override
    public Long getCount() {
        return mongoTemplate.count(new Query().addCriteria(Criteria.where("riskLevel").in(Arrays.asList(0, 1, 2, 3))), com.quanzhi.audit_core.common.model.IpInfo.class);
    }

    @Override
    public Long getIpInfoCount(IpInfoCriteriaDto ipInfoCriteriaDto) {
        Criteria criteria = this.getCriteria(ipInfoCriteriaDto);
        Query query = new Query(criteria);

        return mongoTemplate.count(query, collectionName);
    }

    @Override
    public com.quanzhi.audit_core.common.model.IpInfo getIpInfoByIp(String ip) {
        return mongoTemplate.findOne(new Query().addCriteria(Criteria.where("ip").is(ip)), com.quanzhi.audit_core.common.model.IpInfo.class);
    }

    @Override
    public IpInfo findByIP(String ip) {
        return mongoTemplate.findOne(new Query().addCriteria(Criteria.where("ip").is(ip)), IpInfo.class);
    }

    @Override
    public void update(Query query, Update update) {
        mongoTemplate.upsert(query, update, com.quanzhi.audit_core.common.model.IpInfo.class);
    }

    @Override
    public List<CommonGroupDto> ipGroup(String groupField) {
        // 初次访问，加载数据
        if (com.quanzhi.metabase.common.utils.DataUtil.isEmpty(riskLevelMap)) {
            List<RiskLevel> riskLevels = riskLevelDao.getAll();
            getRiskLevelMap(riskLevels);
        }

        List<AggregationOperation> list = new ArrayList<>();
        if (IpSearchDto.GroupFieldEnum.accessDomainIds.getName().equals(groupField)) {
            list.add(Aggregation.unwind(groupField));
        }

        GroupOperation groupOperation = Aggregation.group(groupField).count().as("count");

        list.add(groupOperation);

        Aggregation aggregation = Aggregation.newAggregation(list);
        List<CommonGroupDto> groupDtoList = mongoTemplate.aggregate(aggregation, collectionName, CommonGroupDto.class).getMappedResults();

        Map<String, Long> groupCountMap = new HashMap<>();
        List<CommonGroupDto> groupDtoListResult = new ArrayList<>();
        for (CommonGroupDto commonGroupDto : groupDtoList) {
            String groupId = commonGroupDto.getId();
            if (DataUtil.isEmpty(groupId)) {
                continue;
            }

            commonGroupDto.setName(groupId);
            groupCountMap.put(groupId, commonGroupDto.getCount());
        }

        if (IpSearchDto.GroupFieldEnum.accessDomainIds.getName().equals(groupField)) {
            // 设置网段
            for (String id : defaultNetworkList) {
                CommonGroupDto commonGroupDto = CommonGroupDto.builder()
                        .id(id)
                        .group(id)
                        .name(id)
                        .count(0L)
                        .build();

                if (DataUtil.isNotEmpty(groupCountMap.get(id))) {
                    commonGroupDto.setCount(groupCountMap.get(id));
                }
                groupDtoListResult.add(commonGroupDto);
            }
        } else if (IpSearchDto.GroupFieldEnum.riskLevel.getName().equals(groupField)) {
            // 设置初始风险等级
            for (Integer riskLevel : riskLevelMap.keySet()) {
                String desc = riskLevelMap.get(riskLevel);
                String id = String.valueOf(riskLevel);
                CommonGroupDto commonGroupDto = CommonGroupDto.builder()
                        .id(id)
                        .group(id)
                        .name(desc)
                        .count(0L)
                        .build();

                if (DataUtil.isNotEmpty(groupCountMap.get(id))) {
                    commonGroupDto.setCount(groupCountMap.get(id));
                }
                groupDtoListResult.add(commonGroupDto);
            }
            groupDtoListResult = groupDtoListResult.stream()
                    .sorted(Comparator.comparing(CommonGroupDto::getGroup))
                    .collect(Collectors.toList());
        }

        return groupDtoListResult;
    }

    @Override
    public long totalCount(IpSearchDto ipSearchDto) {
        //查询条件
        Criteria criteria = getCriteria(ipSearchDto);

        return mongoTemplate.count(new Query(criteria), collectionName);
    }

    @Override
    public long totalCount(IpSearchDetailDto ipSearchDetailDto) {
        //查询条件
        Criteria criteria = getCriteria(ipSearchDetailDto);
        return mongoTemplate.count(new Query(criteria), collectionName);
    }

    @Override
    public void save(List<IpInfo> ipInfoInfos) {
        BulkOperations accountOperations = mongoTemplate.bulkOps(BulkOperations.BulkMode.UNORDERED, collectionName);
        List<Pair<Query, Update>> updateList = new ArrayList<>();
        ipInfoInfos.forEach(ipInfoInfo -> {
            String ip = ipInfoInfo.getIp();
            if (com.quanzhi.metabase.common.utils.DataUtil.isNotEmpty(ip)) {
                Query query = Query.query(Criteria.where("ip").is(ip));
                Update update = AccountInfoDaoImpl.getAccountUpdate(ipInfoInfo);
                if (update != null) {
                    Pair<Query, Update> updatePair = Pair.of(query, update);
                    updateList.add(updatePair);
                }
            }
        });
        // 批量更新
        if (com.quanzhi.metabase.common.utils.DataUtil.isNotEmpty(updateList)) {
            accountOperations.upsert(updateList);
            accountOperations.execute();
        }
    }

    // 组装查询条件
    private Criteria getCriteria(IpSearchDetailDto ipSearchDetailDto) {
        Criteria criteria = new Criteria();
        if (DataUtil.isNotEmpty(ipSearchDetailDto)) {
            if (DataUtil.isNotEmpty(ipSearchDetailDto.getRiskLevel())) {
                criteria.and(IpSearchDetailDto.FieldEnum.riskLevel.getName()).in(ipSearchDetailDto.getRiskLevel());
            }
            if (DataUtil.isNotEmpty(ipSearchDetailDto.getNodeId())) {
                criteria.and(IpSearchDetailDto.FieldEnum.nid.getName()).is(ipSearchDetailDto.getNodeId());
            }
            if (DataUtil.isNotEmpty(ipSearchDetailDto.getIp())) {
                criteria.and(IpSearchDetailDto.FieldEnum.ip.getName()).regex(Pattern.compile(DataUtil.regexStrEscape(String.valueOf(ipSearchDetailDto.getIp())), Pattern.CASE_INSENSITIVE));
            }
            if (DataUtil.isNotEmpty(ipSearchDetailDto.getAppList())) {
                criteria.and(IpSearchDetailDto.FieldEnum.appUriList.getName()).in(ipSearchDetailDto.getAppList());
            }
            if (DataUtil.isNotEmpty(ipSearchDetailDto.getCity())) {
                Criteria criteria1 = Criteria.where(IpSearchDetailDto.FieldEnum.city.getName()).regex(Pattern.compile(DataUtil.regexStrEscape(String.valueOf(ipSearchDetailDto.getCity())), Pattern.CASE_INSENSITIVE));
                Criteria criteria2 = Criteria.where(IpSearchDetailDto.FieldEnum.country.getName()).regex(Pattern.compile(DataUtil.regexStrEscape(String.valueOf(ipSearchDetailDto.getCity())), Pattern.CASE_INSENSITIVE));
                Criteria criteria3 = Criteria.where(IpSearchDetailDto.FieldEnum.province.getName()).regex(Pattern.compile(DataUtil.regexStrEscape(String.valueOf(ipSearchDetailDto.getCity())), Pattern.CASE_INSENSITIVE));
                criteria.orOperator(criteria1, criteria2, criteria3);
            }
            if (DataUtil.isNotEmpty(ipSearchDetailDto.getBlockFlag())) {
                criteria.and(IpSearchDetailDto.FieldEnum.blockFlag.getName()).is(ipSearchDetailDto.getBlockFlag());
            }
            if (DataUtil.isNotEmpty(ipSearchDetailDto.getRiskNames())) {
                if (ipSearchDetailDto.getRiskNames().contains("ALL")) {
                    Query query = new Query();
                    query.addCriteria(Criteria.where("delFlag").is(false));
                    query.fields().include("name");
                    // 风险事件全部筛选
                    List<Map> riskPolicy = mongoTemplate.find(query, Map.class, "riskPolicy");
                    List<String> riskNames = riskPolicy.stream().filter(i -> i != null && !i.equals("null") && !i.equals("")).map(i -> i.get("name").toString()).collect(Collectors.toList());
                    criteria.and(IpSearchDetailDto.FieldEnum.riskNames.getName()).in(riskNames);
                } else {
                    if (IpSearchDetailDto.FieldEnum.and.getName().equals(ipSearchDetailDto.getRiskNamesOperator())) {
                        criteria.and(IpSearchDetailDto.FieldEnum.riskNames.getName()).all(ipSearchDetailDto.getRiskNames());
                    } else {
                        criteria.and(IpSearchDetailDto.FieldEnum.riskNames.getName()).in(ipSearchDetailDto.getRiskNames());
                    }
                }
            }
            if (DataUtil.isNotEmpty(ipSearchDetailDto.getUaTypes())) {
                if (IpSearchDetailDto.FieldEnum.and.getName().equals(ipSearchDetailDto.getUaTypesOperator())) {
                    criteria.and(IpSearchDetailDto.FieldEnum.uaTypes.getName()).all(ipSearchDetailDto.getUaTypes());
                } else {
                    criteria.and(IpSearchDetailDto.FieldEnum.uaTypes.getName()).in(ipSearchDetailDto.getUaTypes());
                }
            }
            if (DataUtil.isNotEmpty(ipSearchDetailDto.getRspDataLabelList())) {
                if (ipSearchDetailDto.getRspDataLabelList().contains("ALL")) {
                    List<String> dataLabelIds = dataLabelNacosDao.getAll().stream().map(dataLabel -> dataLabel.getId()).collect(Collectors.toList());
                    ipSearchDetailDto.setRspDataLabelList(dataLabelIds);
                }
                if (IpSearchDetailDto.FieldEnum.and.getName().equals(ipSearchDetailDto.getRspDataLabelListOperator())) {
                    criteria.and(IpSearchDetailDto.FieldEnum.rspDataLabelList.getName()).all(ipSearchDetailDto.getRspDataLabelList());
                } else {
                    criteria.and(IpSearchDetailDto.FieldEnum.rspDataLabelList.getName()).in(ipSearchDetailDto.getRspDataLabelList());
                }
            }
            if (com.quanzhi.auditapiv2.common.util.utils.DataUtil.isNotEmpty(ipSearchDetailDto.getFirstDate())) {
                String startDate = DateUtil.format(ipSearchDetailDto.getFirstDate().get(0), "yyyyMMdd");
                String endDate = DateUtil.format(ipSearchDetailDto.getFirstDate().get(1), "yyyyMMdd");
                criteria.and(IpSearchDetailDto.FieldEnum.firstDate.getName())
                        .gte(startDate)
                        .lte(endDate);
            }
            if (com.quanzhi.auditapiv2.common.util.utils.DataUtil.isNotEmpty(ipSearchDetailDto.getLastDate())) {
                String startDate = DateUtil.format(ipSearchDetailDto.getLastDate().get(0), "yyyyMMdd");
                String endDate = DateUtil.format(ipSearchDetailDto.getLastDate().get(1), "yyyyMMdd");
                criteria.and(IpSearchDetailDto.FieldEnum.lastDate.getName())
                        .gte(startDate)
                        .lte(endDate);
            }
            queryAdapterRegistry.module(Module.IP).query(ipSearchDetailDto,criteria);
        }
        return criteria;
    }

    // 组装查询条件
    private Criteria getCriteria(IpSearchDto ipSearchDto) {

        Criteria criteria = new Criteria();

        if (DataUtil.isNotEmpty(ipSearchDto)) {
            if (DataUtil.isNotEmpty(ipSearchDto.getAccessDomainIds())) {
                criteria.and(IpSearchDto.GroupFieldEnum.accessDomainIds.getName()).in(ipSearchDto.getAccessDomainIds());
            }

            if (DataUtil.isNotEmpty(ipSearchDto.getRiskLevel())) {
                criteria.and(IpSearchDto.GroupFieldEnum.riskLevel.getName()).is(ipSearchDto.getRiskLevel());
            }
        }

        return criteria;
    }

    // 组装导出查询条件
    public Criteria getCriteria(IpInfoCriteriaDto dto) {
        Criteria criteria = new Criteria();

        List<Criteria> criteriaList = new ArrayList<>();

        // 统一处理 且、或 查询
        Map<String, String> fieldOperateMap = dto.getFieldOperateMap();
        if (DataUtil.isNotEmpty(fieldOperateMap)) {
            for (String field : fieldOperateMap.keySet()) {
                Object value = ReflectUtils.getValueRecursive(dto, field);

                if (DataUtil.isNotEmpty(value)) {
                    List list = (List) value;
                    String operator = fieldOperateMap.get(field).toUpperCase();

                    // field 转换
                    if ("riskNames".equals(field)) {
                        field = "riskInfo.riskNames";
                    }
                    if (Operator.AND.name().equalsIgnoreCase(operator)) {
                        Criteria andCriteria = Criteria.where(field).all(list);
                        criteriaList.add(andCriteria);
                    } else if (Operator.OR.name().equalsIgnoreCase(operator)) {
                        Criteria orCriteria = Criteria.where(field).in(list);
                        criteriaList.add(orCriteria);
                    }
                }
            }
        }

        if (CollectionUtils.isNotEmpty(dto.getExportIps())) {
            criteriaList.add(Criteria.where("ip").in(dto.getExportIps()));
        }

        // 统一处理精确查询
        List<String> isFieldList = Arrays.asList("riskLevel", "blockFlag", "date");
        for (String field : isFieldList) {
            Object value = ReflectUtils.getValueRecursive(dto, field);
            if (DataUtil.isNotEmpty(value)) {
                if ("riskLevel".equals(field)) {
                    Criteria inCriteria = Criteria.where(field).in((List) value);
                    criteriaList.add(inCriteria);
                } else {
                    Criteria isCriteria = Criteria.where(field).is(value);
                    criteriaList.add(isCriteria);
                }
            }
        }

        // 统一处理模糊查询
        List<String> regexFieldList = Arrays.asList("ip", "country", "province", "city");
        for (String field : regexFieldList) {
            Object value = ReflectUtils.getValueRecursive(dto, field);
            if (DataUtil.isNotEmpty(value)) {
                Criteria regexCriteria = Criteria.where(field).regex(value.toString());
                criteriaList.add(regexCriteria);
            }
        }

        // 地域模糊查询
        String area = dto.getArea();
        if (DataUtil.isNotEmpty(area)) {
            Criteria orCriteria = new Criteria();
            orCriteria.orOperator(
                    Criteria.where("country").regex(area),
                    Criteria.where("province").regex(area),
                    Criteria.where("city").regex(area)
            );
            criteriaList.add(orCriteria);
        }

        if (DataUtil.isNotEmpty(dto.getStartTime()) && DataUtil.isNotEmpty(dto.getEndTime())) {
            criteriaList.add(Criteria.where("firstDate")
                    .gte(dto.getStartTime())
                    .lte(dto.getEndTime()));
        }
        if (DataUtil.isNotEmpty(dto.getStartDate()) && DataUtil.isNotEmpty(dto.getEndDate())) {
            criteriaList.add(Criteria.where("lastDate")
                    .gte(dto.getStartDate())
                    .lte(dto.getEndDate()));
        }
        if (CollectionUtils.isNotEmpty(dto.getAccessDomainIds())) {
            criteriaList.add(Criteria.where("accessDomainIds").in(dto.getAccessDomainIds()));
        }
        if (DataUtil.isNotEmpty(dto.getNodeId())) {
            criteriaList.add(Criteria.where("nodes.nid").is(dto.getNodeId()));
        }
        if (criteriaList.size() > 0) {
            criteria.andOperator(criteriaList.toArray(new Criteria[criteriaList.size()]));
        }
        queryAdapterRegistry.module(Module.IP).query(dto,criteria);
        return criteria;
    }
}
