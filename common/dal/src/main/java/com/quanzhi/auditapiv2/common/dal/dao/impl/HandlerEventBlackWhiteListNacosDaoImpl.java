package com.quanzhi.auditapiv2.common.dal.dao.impl;

import com.quanzhi.audit_core.common.model.HandlerEventBlackWhiteList;
import com.quanzhi.auditapiv2.common.dal.dao.IHandlerEventBlackWhiteListNacosDao;
import com.quanzhi.auditapiv2.common.dal.dao.base.impl.NacosBaseDaoImpl;
import org.springframework.stereotype.Repository;

/**
 * 
 * 《事件流量过滤持久层Nacos接口实现》
 * 
 * 
 * @Project: 
 * @Module ID:
 * @Comments: 
 * @JDK version used:      <JDK1.8> 
 * <AUTHOR> [<EMAIL>]
 * @since 2020-04-08-上午9:56:10
 */
@Repository
public class HandlerEventBlackWhiteListNacosDaoImpl extends NacosBaseDaoImpl<HandlerEventBlackWhiteList> implements IHandlerEventBlackWhiteListNacosDao {
}
