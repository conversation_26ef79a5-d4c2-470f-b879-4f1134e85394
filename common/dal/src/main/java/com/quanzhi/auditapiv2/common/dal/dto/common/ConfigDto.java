package com.quanzhi.auditapiv2.common.dal.dto.common;


import com.quanzhi.auditapiv2.common.dal.entity.common.QuickConfig;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;


/**
 * <AUTHOR>
 * @date 2021/8/30 10:42 上午
 */
@Data
@ApiModel
public class ConfigDto {
    @ApiModelProperty("数据库存储主键ID，删除的时候必传")
    private String id;
    @ApiModelProperty("分组，快速筛选group固定传\"quickFilter\"")
    private String group;
    @ApiModelProperty("数据键，快速筛选按照不同类型的筛选传不同的key，前端自己定")
    private String key;
    @ApiModelProperty("数据")
    private String value;

    @Mapper
    public interface ConfigMapper {

        ConfigMapper INSTANCE = Mappers.getMapper(ConfigMapper.class);

        ConfigDto convert(QuickConfig quickConfig);

        QuickConfig convert(ConfigDto quickConfig);

    }
}
