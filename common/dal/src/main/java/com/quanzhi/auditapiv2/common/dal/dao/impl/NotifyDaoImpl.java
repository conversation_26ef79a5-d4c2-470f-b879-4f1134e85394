package com.quanzhi.auditapiv2.common.dal.dao.impl;

import com.quanzhi.auditapiv2.common.dal.dao.INotifyDao;
import com.quanzhi.auditapiv2.common.dal.dao.base.impl.BaseDaoImpl;
import com.quanzhi.auditapiv2.common.dal.entity.Notify;
import com.quanzhi.auditapiv2.common.util.utils.ConstantUtil;
import com.quanzhi.auditapiv2.common.util.utils.DataUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.stereotype.Repository;

import java.lang.reflect.Field;
import java.util.List;
import java.util.Map;

/**
 *
 * 《通知持久层接口实现》
 *
 *
 * @Project:
 * @Module ID:
 * @Comments:
 * @JDK version used:      <JDK1.8> 
 * <AUTHOR> [<EMAIL>]
 * @since 2020-04-08-上午9:56:10
 */
@Repository
public class NotifyDaoImpl extends BaseDaoImpl<Notify> implements INotifyDao {

    @Autowired
    private MongoTemplate mongoTemplate;

    private String collectionName = "notify";

    /**
     * 查询通知记录列表(分页)
     * @Comments:  <对此方法的描述，可以引用系统设计中的描述>
     * <AUTHOR> [<EMAIL>]
     * @since 2020-04-08 9:56
     * @param
     * @return
     */
    @Override
    public List<Notify> selectNotifyList(Map<String, Object> map, String field, Integer sort, Integer page, Integer limit) throws Exception {

        Criteria criteria = new Criteria();

        if(DataUtil.isNotEmpty(map)) {
            for (String key : map.keySet()) {

                Object value = map.get(key);
                if (DataUtil.isNotEmpty(value)) {
                    if ("title".equals(key)) {
                        criteria.and(key).regex((String) value);
                    } else if ("type".equals(key)) {
                        criteria.and(key).is(value);
                    } else if ("code".equals(key)) {
                        criteria.and(key).is(value);
                    } else if ("methods".equals(key)) {
                        criteria.and(key).is(value);
                    } else if ("readState".equals(key)) {
                        criteria.and(key).is(value);
                    } else if ("ip".equals(key)) {
                        criteria.and(key).is((String) value);
                    } else if ("notifyType".equals(key)) {
                        criteria.and("notifySource").is((String) value);
                    }
                }
            }
            if(DataUtil.isNotEmpty(map.get("startDate")) && DataUtil.isNotEmpty(map.get("endDate"))) {

                criteria.andOperator(
                        Criteria.where("notifyTime").gte(map.get("startDate")),
                        Criteria.where("notifyTime").lte(map.get("endDate"))
                );
            }
        }
        //分页
        Pageable pageable = PageRequest.of(page - 1, limit);
        //排序
        Sort dbSort = null;
        if(DataUtil.isNotEmpty(field) && DataUtil.isNotEmpty(sort)) {

            if(sort == ConstantUtil.Sort.ASC) {
                dbSort = Sort.by(Sort.Direction.ASC, field);
            }else if(sort == ConstantUtil.Sort.DESC) {
                dbSort = Sort.by(Sort.Direction.DESC, field);
            }else {
                dbSort = Sort.by(Sort.Direction.DESC, "notifyTime");
            }
        }else {
            dbSort = Sort.by(Sort.Direction.DESC, "notifyTime");
        }

        return mongoTemplate.find(new Query(criteria).with(pageable).with(dbSort), Notify.class, collectionName);
    }

    /**
     * 查询通知记录数量
     * @Comments:  <对此方法的描述，可以引用系统设计中的描述>
     * <AUTHOR> [<EMAIL>]
     * @since 2020-04-08 9:56
     * @param
     * @return
     */
    @Override
    public Long totalCount(Map<String, Object> map) throws Exception {

        Criteria criteria = new Criteria();

        if(DataUtil.isNotEmpty(map)) {
            for (String key : map.keySet()) {

                Object value = map.get(key);
                if (DataUtil.isNotEmpty(value)) {

                    if ("title".equals(key)) {
                        criteria.and(key).regex((String) value);
                    } else if ("type".equals(key)) {
                        criteria.and(key).is(value);
                    } else if ("methods".equals(key)) {
                        criteria.and(key).is(value);
                    } else if ("readState".equals(key)) {
                        criteria.and(key).is(value);
                    } else if ("ip".equals(key)) {
                        criteria.and(key).is((String) value);
                    }
                }
            }
            if(DataUtil.isNotEmpty(map.get("startDate")) && DataUtil.isNotEmpty(map.get("endDate"))) {

                criteria.andOperator(
                        Criteria.where("notifyTime").gte(map.get("startDate")),
                        Criteria.where("notifyTime").lte(map.get("endDate"))
                );
            }
        }

        return mongoTemplate.count(new Query(criteria), Notify.class, collectionName);
    }

    /**
     * 查询通知记录列表
     * @Comments:  <对此方法的描述，可以引用系统设计中的描述>
     * <AUTHOR> [<EMAIL>]
     * @since 2020-04-08 9:56
     * @param
     * @return
     */
    @Override
    public List<Notify> selectNotifyList(Map<String, Object> map) throws Exception {

        Criteria criteria = new Criteria();

        if(DataUtil.isNotEmpty(map)) {
            for (String key : map.keySet()) {

                Object value = map.get(key);
                if (DataUtil.isNotEmpty(value)) {
                    if ("title".equals(key)) {
                        criteria.and(key).regex((String) value);
                    } else if ("type".equals(key)) {
                        criteria.and(key).is(value);
                    } else if ("code".equals(key)) {
                        criteria.and(key).is(value);
                    } else if ("methods".equals(key)) {
                        criteria.and(key).is(value);
                    } else if ("readState".equals(key)) {
                        criteria.and(key).is(value);
                    } else if ("ip".equals(key)) {
                        criteria.and(key).is((String) value);
                    }
                }
            }
            if(DataUtil.isNotEmpty(map.get("startDate")) && DataUtil.isNotEmpty(map.get("endDate"))) {

                criteria.andOperator(
                        Criteria.where("notifyTime").gte(map.get("startDate")),
                        Criteria.where("notifyTime").lte(map.get("endDate"))
                );
            }
        }

        return mongoTemplate.find(new Query(criteria), Notify.class, collectionName);
    }

    /**
     * 去重查询通知记录
     * @Comments:  <对此方法的描述，可以引用系统设计中的描述>
     * <AUTHOR> [<EMAIL>]
     * @since 2020-04-08 9:56
     * @param
     * @return
     */
    @Override
    public List<String> selectDistinctNotifyList(Map<String, Object> map) throws Exception {

        Criteria criteria = new Criteria();

        if(DataUtil.isNotEmpty(map)) {
            for (String key : map.keySet()) {

                Object value = map.get(key);
                if (DataUtil.isNotEmpty(value)) {
                    if ("title".equals(key)) {
                        criteria.and(key).regex((String) value);
                    } else if ("type".equals(key)) {
                        criteria.and(key).is(value);
                    } else if ("code".equals(key)) {
                        criteria.and(key).is(value);
                    } else if ("methods".equals(key)) {
                        criteria.and(key).is(value);
                    } else if ("readState".equals(key)) {
                        criteria.and(key).is(value);
                    } else if ("ip".equals(key)) {
                        criteria.and(key).is((String) value);
                    }
                }
            }
            if(DataUtil.isNotEmpty(map.get("startDate")) && DataUtil.isNotEmpty(map.get("endDate"))) {

                criteria.andOperator(
                        Criteria.where("notifyTime").gte(map.get("startDate")),
                        Criteria.where("notifyTime").lte(map.get("endDate"))
                );
            }
        }

        return mongoTemplate.findDistinct(new Query(criteria), "content", collectionName, Notify.class, String.class);
    }

    /**
     * id查询通知记录详情
     * @Comments:  <对此方法的描述，可以引用系统设计中的描述>
     * <AUTHOR> [<EMAIL>]
     * @since 2020-04-08 9:56
     * @param
     * @return
     */
    @Override
    public Notify selectNotifyById(String id) throws Exception {

        //查询条件
        Criteria criteria = Criteria.where("_id").is(id);

        return mongoTemplate.findOne(new Query(criteria), Notify.class, collectionName);
    }

    /**
     * 新增通知记录
     * @Comments:  <对此方法的描述，可以引用系统设计中的描述>
     * <AUTHOR> [<EMAIL>]
     * @since 2020-04-08 9:56
     * @param
     * @return
     */
    @Override
    public Notify insertNotify(Notify notify) throws Exception {
        if (notify.getIp() == null) {
            notify.setIp(System.getenv("KUBE_HOST_IP"));
        }
        return mongoTemplate.insert(notify, collectionName);
    }

    /**
     * 编辑通知记录
     * @Comments:  <对此方法的描述，可以引用系统设计中的描述>
     * <AUTHOR> [<EMAIL>]
     * @since 2020-04-08 9:56
     * @param
     * @return
     */
    @Override
    public Notify updateNotify(Notify notify) throws Exception {

        Query query = new Query(Criteria.where("_id").is(notify.getId()));
        Update update = new Update();
        Field[] field = notify.getClass().getDeclaredFields();
        for(int i=0; i<field.length; i++){

            //设置是否允许访问，不是修改原来的访问权限修饰词。
            field[i].setAccessible(true);
            //字段值不为空 放入map
            if(field[i].get(notify) != null) {
                update.set(field[i].getName(), field[i].get(notify));
            }
        }
        mongoTemplate.upsert(query, update, Notify.class);

        return notify;
    }

    /**
     * 一键已读
     * @Comments:  <对此方法的描述，可以引用系统设计中的描述>
     * <AUTHOR> [<EMAIL>]
     * @since 2020-04-08 9:56
     * @param
     * @return
     */
    @Override
    public void readAll() throws Exception {

        Long dateTime = System.currentTimeMillis();

        Query query = new Query(Criteria.where("readState").is(Notify.ReadStateEnum.UNREAD));
        Update update = new Update();
        update.set("readState", Notify.ReadStateEnum.READ);
        update.set("readTime", dateTime);
        mongoTemplate.updateMulti(query, update, Notify.class);
    }
}
