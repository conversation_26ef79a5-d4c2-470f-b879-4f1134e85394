package com.quanzhi.auditapiv2.common.dal.dto.docx;

import lombok.Data;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 *
 * 《重要文件泄漏》
 *
 *
 * @Project:
 * @Module ID:
 * @Comments:
 * @JDK version used:      <JDK1.8>
 * <AUTHOR> [<EMAIL>]
 * @since 2020-08-28-下午16:52:18
 */
@Data
public class Importantfileleak extends ApiWeaknessExportWord {

    @Mapper
    public interface ImportantfileleakMapper {

        Importantfileleak.ImportantfileleakMapper INSTANCE = Mappers.getMapper(Importantfileleak.ImportantfileleakMapper.class);
        Importantfileleak convert(ApiWeaknessExportWord apiWeaknessExportWord);
    }
}
