package com.quanzhi.auditapiv2.common.dal.entity.account;

import com.quanzhi.audit_core.common.model.StaffInfo;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.data.mongodb.core.mapping.Document;
import java.util.Set;

@EqualsAndHashCode(callSuper = true)
@Data
@Document("accountMonthInfo")
public class AccountMonthStat extends AbstractMonthStat{
    private String account;
    private Set<String> apiUriList;
    private StaffInfo staffInfo;
    private Set<String> relatedIpList;

    @Override
    public String getKey() {
        return account;
    }
}
