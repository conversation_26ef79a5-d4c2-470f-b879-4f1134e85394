package com.quanzhi.auditapiv2.common.dal.dao.impl;

import com.quanzhi.audit_core.common.model.EventFilterPlugin;
import com.quanzhi.auditapiv2.common.dal.dao.IEventFilterPluginNacosDao;
import com.quanzhi.auditapiv2.common.dal.dao.base.impl.NacosBaseDaoImpl;
import org.springframework.stereotype.Repository;

/**
 * 
 * 《事件插件过滤持久层Nacos接口实现》
 * 
 * 
 * @Project: 
 * @Module ID:
 * @Comments: 
 * @JDK version used:      <JDK1.8> 
 * <AUTHOR> [<EMAIL>]
 * @since 2020-04-08-上午9:56:10
 */
@Repository
public class EventFilterPluginNacosDaoImpl extends NacosBaseDaoImpl<EventFilterPlugin> implements IEventFilterPluginNacosDao {
}
