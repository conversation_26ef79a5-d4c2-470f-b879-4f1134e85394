package com.quanzhi.auditapiv2.common.dal.dto.api;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Data;

/**
 * @Author: HaoJun
 * @Date: 2021/6/28 4:37 下午
 */
@Data
@Builder
@ApiModel("接口简单统计")
public class ApiStatistics {

    private String id;

    private String type;

    private Long totalCount;
    private Long highLevelCount;
    private Long internetApiCount;
    private Long internetHighLevelApiCount;
    private Long todayApi;
    private Long todayInternetApi;
    private Long todayHighLevelApi;
    private Long todayWeaknessApi;

    @ApiModelProperty("总API")
    private Long totalApi;
    @ApiModelProperty("新增API")
    private Long newApi;
    @ApiModelProperty("总应用")
    private Long totalApp;
    @ApiModelProperty("新增应用")
    private Long newApp;
    @ApiModelProperty("总弱点")
    private Long totalWeakness;
    @ApiModelProperty("新增弱点")
    private Long newWeakness;
    @ApiModelProperty("总风险")
    private Long totalRisk;
    @ApiModelProperty("新增风险")
    private Long newRisk;

}
