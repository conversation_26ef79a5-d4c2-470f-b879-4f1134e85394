package com.quanzhi.auditapiv2.common.dal.entity;

import lombok.Data;

/**
 * <AUTHOR>
 * create at 2022/12/27 11:44 上午
 * @description: 管控策略日志
 **/
@Data
public class ControlStrategyLog {

    private String id;

    /**
     * 日志详情
     */
    private String logDetail;
    /**
     * 操作类型
     */
    private OperateType operateType;
    /**
     * 阻断结果
     */
    private OperateResult operateResult;

    private Long createTime;
    private Long updateTime;


    public enum OperateType{
        BLOCK,UNBLOCK
    }

    public enum OperateResult {
        SUCCESS, FAIL
    }


}