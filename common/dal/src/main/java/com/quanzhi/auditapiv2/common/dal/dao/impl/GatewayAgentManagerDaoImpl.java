package com.quanzhi.auditapiv2.common.dal.dao.impl;

import com.quanzhi.auditapiv2.common.dal.dao.IGatewayAgentManagerDao;
import com.quanzhi.auditapiv2.common.dal.dao.base.impl.BaseDaoImpl;
import com.quanzhi.auditapiv2.common.dal.entity.GatewayAgentManagerStatus;
import com.quanzhi.auditapiv2.common.dal.entity.GatewayConfig;
import com.quanzhi.metabase.core.model.query.MetabaseQuery;
import com.quanzhi.metabase.core.model.query.Predicate;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @date 2023/4/10 15:26
 */
@Repository("gatewayAgentManagerDao")
public class GatewayAgentManagerDaoImpl extends BaseDaoImpl<GatewayAgentManagerStatus> implements IGatewayAgentManagerDao {

    @Autowired
    MongoTemplate mongoTemplate;

    private static String collectionName = "gatewayAgentManagerStatus";


    @Override
    public List<GatewayAgentManagerStatus> findByName(String name) {
        MetabaseQuery query = new MetabaseQuery();
        query.where("name", Predicate.REGEX, name);
        List<GatewayAgentManagerStatus> gatewayAgentManagerStatuses = find(query, collectionName);
        return gatewayAgentManagerStatuses;
    }

    @Override
    public List<GatewayAgentManagerStatus> findByAgentIp(String agentIp) {
        MetabaseQuery query = new MetabaseQuery();
        query.where("agentIp", Predicate.IS, agentIp);
        List<GatewayAgentManagerStatus> gatewayAgentManagerStatuses = find(query, collectionName);
        return gatewayAgentManagerStatuses;
    }

    @Override
    public List<GatewayAgentManagerStatus> findByGatewayIp(String gatewayIp) {
        MetabaseQuery query = new MetabaseQuery();
        query.where("gatewayIp", Predicate.IS, gatewayIp);
        List<GatewayAgentManagerStatus> gatewayAgentManagerStatuses = find(query, collectionName);
        return gatewayAgentManagerStatuses;
    }

    @Override
    public List<GatewayAgentManagerStatus> findByAgentIp(List<String> agentIp) {
        MetabaseQuery query = new MetabaseQuery();
        query.where("agentIp", Predicate.IN, agentIp);
        return find(query, collectionName);
    }

    @Override
    public List<GatewayAgentManagerStatus> findByFullName(String name) {
        MetabaseQuery query = new MetabaseQuery();
        query.where("name", Predicate.IS, name);
        List<GatewayAgentManagerStatus> gatewayAgentManagerStatuses = find(query, collectionName);
        return gatewayAgentManagerStatuses;
    }

    @Override
    public GatewayAgentManagerStatus findByAgentIpAndPort(String gatewayIp, String agentIp, Integer agentPort) {
        MetabaseQuery query = new MetabaseQuery();
        query.where("gatewayIp", Predicate.IS, gatewayIp);
        query.where("agentIp", Predicate.IS, agentIp);
        query.where("agentPort", Predicate.IS, agentPort);
        GatewayAgentManagerStatus gatewayAgentManagerStatus = findOne(query, collectionName);
        return gatewayAgentManagerStatus;
    }

    @Override
    public Long getAgentInfoByNameCount(String name) {
        MetabaseQuery query = new MetabaseQuery();
        if (StringUtils.isNotEmpty(name)) {
            query.where("name", Predicate.REGEX, name);
        }
        return count(query, collectionName);
    }

    @Override
    public void deleteById(String id) {
        MetabaseQuery query = new MetabaseQuery();
        if (StringUtils.isNotEmpty(id)) {
            query.where("_id", Predicate.IS, id);
            delete(query,collectionName);
        }
    }

    @Override
    public void deleteByAgentIp(String agentIp) {
        MetabaseQuery query = new MetabaseQuery();
        if (StringUtils.isNotEmpty(agentIp)) {
            query.where("agentIp", Predicate.IS, agentIp);
            delete(query,collectionName);
        }
    }

    @Override
    public void deleteByGatewayIp(String gatewayIp) {
        MetabaseQuery query = new MetabaseQuery();
        if (StringUtils.isNotEmpty(gatewayIp)) {
            query.where("gatewayIp", Predicate.IS, gatewayIp);
            delete(query,collectionName);
        }
    }

    @Override
    public List<String> getAllGatewayIp() {
        List<String> gatewayIpList = findDistinctByField("gatewayIp", collectionName, String.class);
        return gatewayIpList;
    }

    @Override
    public List<GatewayAgentManagerStatus> getPageByGatewayIpAndName(String gatewayIp, String agentName,
                                                                     Integer page, Integer limit) {
        Criteria criteria = new Criteria();
        criteria.and("gatewayIp").is(gatewayIp).and("name").regex(agentName);
        Sort sort = Sort.by(Sort.Direction.DESC, "createTime");
        return mongoTemplate.find(new Query(new Criteria()).skip((page-1) * limit).limit(limit).with(sort), GatewayAgentManagerStatus.class, collectionName);
    }

    @Override
    public Long getPageByGatewayIpAndNameCount(String gatewayIp, String agentName) {
        Criteria criteria = new Criteria();
        if(StringUtils.isNotEmpty(gatewayIp)){
            criteria.and("gatewayIp").is(gatewayIp);
        }
        if(StringUtils.isNotEmpty(agentName)){
            criteria.and("name").is(agentName);
        }
        return mongoTemplate.count(new Query(new Criteria()),collectionName);
    }

    @Override
    public List<GatewayAgentManagerStatus> getPageByGatewayIp(String gatewayIp, String agentName) {
        Criteria criteria = new Criteria();
        if(StringUtils.isNotEmpty(gatewayIp)){
            criteria.and("gatewayIp").is(gatewayIp);
        }
        if(StringUtils.isNotEmpty(agentName)){
            criteria.and("name").regex(agentName);
        }
        Sort sort = Sort.by(Sort.Direction.DESC, "createTime");
        return mongoTemplate.find(new Query(criteria).with(sort),GatewayAgentManagerStatus.class,collectionName);
    }
}
