package com.quanzhi.auditapiv2.common.dal.dao;

import com.quanzhi.auditapiv2.common.dal.dao.base.IBaseDao;
import com.quanzhi.auditapiv2.common.dal.dto.ResultFilterRuleDto;
import com.quanzhi.auditapiv2.common.dal.entity.ScanTool;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;

import java.util.List;

/**
 *
 * 《报告任务持久层接口》
 *
 *
 * @Project:
 * @Module ID:
 * @Comments:
 * @JDK version used:      <JDK1.8>
 * <AUTHOR> [<EMAIL>]
 * @since 2019-07-23-上午9:09:10
 */
public interface IScanToolDao extends IBaseDao<ScanTool> {

    List<ScanTool> selectScanToolList();

    ScanTool selectScanToolById(String id);

    ScanTool insert(ScanTool scanTool);

    ScanTool findOneByQuery(Query query);

    ScanTool findByCode(String code);

    ScanTool getScanToolByFileHash(String fileHash);

    void updateScanToolByToolCode(Query query, Update update);

    void addToolFilters(String taskCode, String scanRule,List<ResultFilterRuleDto> filters);
}
