package com.quanzhi.auditapiv2.common.dal.entity.aggRisk;

import com.quanzhi.metabase.core.model.ResourceEntity;
import lombok.Data;

import java.util.List;

/**
 * @Author: yangzx
 * @Date: 2024/10/10 11:11
 */
@Data
public class AggRiskOperationLog {

    private String id;

    private String riskId;

    private String operateName;

    private String remark;

    private Integer oldState;

    private Integer newState;

    private Long operateTime;

}
