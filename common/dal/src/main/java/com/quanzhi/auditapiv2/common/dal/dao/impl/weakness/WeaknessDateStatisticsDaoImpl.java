package com.quanzhi.auditapiv2.common.dal.dao.impl.weakness;

import com.quanzhi.auditapiv2.common.dal.dao.weakness.WeaknessDateStatisticsDao;
import com.quanzhi.auditapiv2.common.dal.entity.weakness.WeaknessDateStatistics;
import com.quanzhi.metabase.core.model.http.HttpResourceConstant;
import com.quanzhi.metabase.core.model.http.weakness.State;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Repository;

import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/8/31 5:07 下午
 */
@Repository
public class WeaknessDateStatisticsDaoImpl implements WeaknessDateStatisticsDao {

    private final MongoTemplate mongoTemplate;

    public WeaknessDateStatisticsDaoImpl(MongoTemplate mongoTemplate) {
        this.mongoTemplate = mongoTemplate;
    }

    @Override
    public WeaknessDateStatistics getOne(String date) {
        return mongoTemplate.findOne(new Query(Criteria.where("date").is(date)), WeaknessDateStatistics.class);
    }

    @Override
    public void save(WeaknessDateStatistics weaknessDateStatistics) {
        mongoTemplate.save(weaknessDateStatistics);
    }

    @Override
    public List<WeaknessDateStatistics> list(String startDate, String endDate) {
        return mongoTemplate.find(new Query(new Criteria().andOperator(Criteria.where("date")
                .gte(startDate),Criteria.where("date")
                .lte(endDate))).with(Sort.by(Sort.Order.asc("date"))), WeaknessDateStatistics.class);
    }

    @Override
    public WeaknessDateStatistics countByWeakness(Date date) {
        WeaknessDateStatistics weaknessDateStatistics = new WeaknessDateStatistics();
        SimpleDateFormat df = new SimpleDateFormat("yyMMdd");
        weaknessDateStatistics.setDate(df.format(date));
        Criteria criteria = new Criteria();
        long startTimestamp = date.getTime();
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.add(Calendar.DAY_OF_MONTH, 1);
        long endTimestamp = calendar.getTime().getTime();
        criteria.andOperator(Criteria.where("createTime").gte(startTimestamp),
                Criteria.where("createTime").lt(endTimestamp),
                Criteria.where("state").ne(State.IGNORED.name()),
                Criteria.where("state").ne(State.SUSPECT.name()),
                Criteria.where("delFlag").is(false));
        weaknessDateStatistics
                .setNewCount(mongoTemplate
                        .count(new Query(criteria), HttpResourceConstant.API_WEAKNESS));
        criteria = new Criteria();
        criteria.andOperator(Criteria.where("fixedTimestamp").gte(startTimestamp),
                Criteria.where("fixedTimestamp").lt(endTimestamp),
                Criteria.where("state").is(State.FIXED.name()),
                Criteria.where("delFlag").is(false));
        weaknessDateStatistics.setNewFixedCount(mongoTemplate
                .count(new Query(criteria), HttpResourceConstant.API_WEAKNESS));

        criteria = new Criteria();
        criteria.andOperator(Criteria.where("confirmTimestamp").gte(startTimestamp),
                Criteria.where("confirmTimestamp").lt(endTimestamp),
                Criteria.where("state").ne(State.IGNORED.name()),
                Criteria.where("state").ne(State.SUSPECT.name()),
                Criteria.where("delFlag").is(false));
        weaknessDateStatistics.setNewPendingFixCount(mongoTemplate
                .count(new Query(criteria), HttpResourceConstant.API_WEAKNESS));
        return weaknessDateStatistics;
    }

    @Override
    public long count() {
        return mongoTemplate.estimatedCount(WeaknessDateStatistics.class);
    }
}
