package com.quanzhi.auditapiv2.common.dal.dao.impl.home;

import com.quanzhi.auditapiv2.common.dal.dao.home.HomeDao;
import com.quanzhi.auditapiv2.common.dal.dto.api.ApiStatistics;
import com.quanzhi.auditapiv2.common.dal.dto.api.DataLabelTopDto;
import com.quanzhi.auditapiv2.common.dal.dto.api.ProblemOverviewDto;
import com.quanzhi.auditapiv2.common.dal.dto.securityPosture.SensitiveInfoDto;
import com.quanzhi.auditapiv2.common.dal.dto.securityPosture.StatisticsDto;
import com.quanzhi.auditapiv2.common.dal.dto.weakness.Top10WeaknessDto;
import com.quanzhi.auditapiv2.common.dal.dto.weakness.WeaknessDistributedDto;
import com.quanzhi.auditapiv2.common.util.utils.DataUtil;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Repository;

import java.util.Collections;
import java.util.List;
import java.util.Map;

/**
 * @author: yangzixian
 * @date: 2022/8/9 10:44
 * @description:
 */
@Repository
public class HomeDaoImpl implements HomeDao {

    private final MongoTemplate mongoTemplate;
    private final String collectionName = "homeInfo";

    public HomeDaoImpl(MongoTemplate mongoTemplate) {
        this.mongoTemplate = mongoTemplate;
    }

    @Override
    public void upsert(ApiStatistics apiStatistics, Top10WeaknessDto top10WeaknessDto, WeaknessDistributedDto bigScreenWeaknessDistribute,
                       List<DataLabelTopDto> dataLabelTopDtoList, SensitiveInfoDto sensitiveInfo, StatisticsDto statisticsDto) {
        if (DataUtil.isNotEmpty(apiStatistics)) {
            mongoTemplate.remove(new Query().addCriteria(Criteria.where("type").is("apiStatistics")), ApiStatistics.class, collectionName);
            mongoTemplate.save(apiStatistics, collectionName);
        }

        if (DataUtil.isNotEmpty(top10WeaknessDto)) {
            mongoTemplate.remove(new Query().addCriteria(Criteria.where("type").is("top10Weakness")), Top10WeaknessDto.class, collectionName);
            mongoTemplate.save(top10WeaknessDto, collectionName);
        }

        if (DataUtil.isNotEmpty(bigScreenWeaknessDistribute)) {
            mongoTemplate.remove(new Query().addCriteria(Criteria.where("type").is("bigScreenWeaknessDistribute")), WeaknessDistributedDto.class, collectionName);
            mongoTemplate.save(bigScreenWeaknessDistribute, collectionName);
        }

        if (DataUtil.isNotEmpty(dataLabelTopDtoList)) {
            for (DataLabelTopDto dataLabelTopDto : dataLabelTopDtoList) {
                mongoTemplate.remove(new Query().addCriteria(Criteria.where("type").is(dataLabelTopDto.getType()).and("viewType").is(dataLabelTopDto.getViewType())), DataLabelTopDto.class, collectionName);
                mongoTemplate.save(dataLabelTopDto, collectionName);
            }
        }

        if (DataUtil.isNotEmpty(sensitiveInfo)) {
            mongoTemplate.remove(new Query().addCriteria(Criteria.where("type").is("sensitiveInfo")), SensitiveInfoDto.class, collectionName);
            mongoTemplate.save(sensitiveInfo, collectionName);
        }

        if (DataUtil.isNotEmpty(statisticsDto)) {
            mongoTemplate.remove(new Query().addCriteria(Criteria.where("type").is("securityPostureStatistics")), StatisticsDto.class, collectionName);
            mongoTemplate.save(statisticsDto, collectionName);
        }

    }

    @Override
    public void upsertMulti(Map<String, ApiStatistics> apiStatisticsMap, Map<String, Top10WeaknessDto> top10WeaknessDtoMap, Map<String, List<DataLabelTopDto>> dataLabelTopDtoListMap) {
        if (DataUtil.isNotEmpty(apiStatisticsMap)) {
            for (String groupId : apiStatisticsMap.keySet()) {
                mongoTemplate.remove(new Query().addCriteria(Criteria.where("type").is("apiStatistics")), ApiStatistics.class, collectionName + "_" + groupId);
                mongoTemplate.save(apiStatisticsMap.get(groupId), collectionName + "_" + groupId);
            }
        }

        if (DataUtil.isNotEmpty(top10WeaknessDtoMap)) {
            for (String groupId : top10WeaknessDtoMap.keySet()) {
                mongoTemplate.remove(new Query().addCriteria(Criteria.where("type").is("top10Weakness")), Top10WeaknessDto.class, collectionName + "_" + groupId);
                mongoTemplate.save(top10WeaknessDtoMap.get(groupId), collectionName + "_" + groupId);
            }
        }

        if (DataUtil.isNotEmpty(dataLabelTopDtoListMap)) {
            for (String groupId : dataLabelTopDtoListMap.keySet()) {
                List<DataLabelTopDto> dataLabelTopDtos = dataLabelTopDtoListMap.get(groupId);
                for (DataLabelTopDto dataLabelTopDto : dataLabelTopDtos) {
                    mongoTemplate.remove(new Query().addCriteria(Criteria.where("type").is(dataLabelTopDto.getType()).and("viewType").is(dataLabelTopDto.getViewType())), DataLabelTopDto.class, collectionName + "_" + groupId);
                    mongoTemplate.save(dataLabelTopDto, collectionName + "_" + groupId);
                }
            }
        }
    }

    @Override
    public ProblemOverviewDto getProblemOverview() {
        return mongoTemplate.findOne(new Query().addCriteria(Criteria.where("type").is("problemOverview")), ProblemOverviewDto.class, collectionName);
    }

    @Override
    public WeaknessDistributedDto getWeaknessDistributed(String viewType) {
        WeaknessDistributedDto weaknessDistributedDto = mongoTemplate.findOne(new Query().addCriteria(Criteria.where("type").is("weaknessDistribute")
                .and("viewType").is(viewType)), WeaknessDistributedDto.class, collectionName);
        List<WeaknessDistributedDto.weaknessInfo> weaknessDistributed = weaknessDistributedDto.getWeaknessDistributed();
        Collections.sort(weaknessDistributed);
        weaknessDistributedDto.setWeaknessDistributed(weaknessDistributed);
        return weaknessDistributedDto;
    }

    @Override
    public ApiStatistics getApiStatistics(String userGroupId) {
        return mongoTemplate.findOne(new Query().addCriteria(Criteria.where("type").is("apiStatistics")), ApiStatistics.class, collectionName + "_" + userGroupId);
    }

    @Override
    public Top10WeaknessDto getTop10Weakness(String userGroupId) {
        return mongoTemplate.findOne(new Query().addCriteria(Criteria.where("type").is("top10Weakness")), Top10WeaknessDto.class, collectionName + "_" + userGroupId);
    }

    @Override
    public WeaknessDistributedDto getBigScreenWeaknessDistribute() {
        return mongoTemplate.findOne(new Query().addCriteria(Criteria.where("type").is("bigScreenWeaknessDistribute")), WeaknessDistributedDto.class, collectionName);
    }

    @Override
    public SensitiveInfoDto getSensitiveInfo() {
        return mongoTemplate.findOne(new Query().addCriteria(Criteria.where("type").is("sensitiveInfo")), SensitiveInfoDto.class, collectionName);
    }

    @Override
    public StatisticsDto getSecurityPostureStatistics() {
        return mongoTemplate.findOne(new Query().addCriteria(Criteria.where("type").is("securityPostureStatistics")), StatisticsDto.class, collectionName);
    }

    @Override
    public DataLabelTopDto getDataLabelTop10(String viewType, String userGroupId) {
        return mongoTemplate.findOne(new Query().addCriteria(Criteria.where("type").is("dataLabelTop10").and("viewType").is(viewType)), DataLabelTopDto.class, collectionName + "_" + userGroupId);
    }

}
