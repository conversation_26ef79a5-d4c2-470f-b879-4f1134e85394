package com.quanzhi.auditapiv2.common.dal.dto.filter;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class AssetFilterDTO implements Serializable {

    /**
     * 唯一标识
     */
    private String id;

    /**
     * 资源类型
     */
    private List<String> type;

    /**
     * 资源内容
     */
    private String resource;

    /**
     * 作用位置
     *  - handler: 审计
     *  - gwhw: 网关
     */
    private List<String> positions;

    /**
     * 规则类型
     */
    private String ruleType;
    /**
     * 规则类型集合
     */
    private List<String> ruleTypes;

    /**
     * 作用范围
     */
    private String effectScope;

    /**
     * 开关
     */
    private Boolean openFlag;
    /**
     * 页面查询key
     */
    private String queryKey;
    /**
     * 规则名称
     */
    private String name;

    private Integer page;

    private Integer limit;
    /**
     * 配置类型
     * @see com.quanzhi.audit_core.common.model.AssetFilterConfig.ConfigTypeEnum
     */
    private String configType;

}
