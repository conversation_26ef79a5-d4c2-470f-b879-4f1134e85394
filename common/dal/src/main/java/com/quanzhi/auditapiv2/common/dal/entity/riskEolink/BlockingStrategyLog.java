package com.quanzhi.auditapiv2.common.dal.entity.riskEolink;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.quanzhi.audit_core.common.model.FileInfo;
import com.quanzhi.audit_core.common.model.HttpEvent;
import com.quanzhi.audit_core.common.model.Net;
import com.quanzhi.metabase.core.domain.entity.CompositeApiChangedEvent;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * eolink阻断策略日志
 */

@Data
public class BlockingStrategyLog{

    @ApiModelProperty(value = "id", name = "id")
    private String id;

    @ApiModelProperty(value = "部署 IP", name = "deployIp")
    @JsonProperty("deploy_ip")
    private String deployIp;

    @ApiModelProperty(value = "上游id", name = "upstreamId")
    @JsonProperty("upstream_id")
    private String upstreamId;

    @ApiModelProperty(value = "上游描述", name = "upstreamDesc")
    @JsonProperty("upstream_desc")
    private String upstreamDesc;

    /**
     * 是否阻断
     */
    @ApiModelProperty(value = "是否阻断", name = "block")
    private Boolean block;

    private HttpEvent.HttpRequest req;

    private HttpEvent.HttpResponse rsp;

    private Net net;

    private FileInfo file;

    @JsonProperty("unique_id")
    private HttpEvent.UniqueId uniqueId;

    /**
     * 策略名称
     */
    @ApiModelProperty(value = "策略名称", name = "strategyName")
    private String strategyName;

    /**
     * 策略类型
     */
    @ApiModelProperty(value = "策略类型", name = "strategyType")
    private String strategyType;

    /**
     * 策略ID
     */
    @ApiModelProperty(value = "策略ID", name = "blockId")
    private String blockId;

    @ApiModelProperty(value = "成功数量", name = "successCount")
    private Integer successCount;

    @ApiModelProperty(value = "失败数量", name = "failCount")
    private Integer failCount;

    @ApiModelProperty(value = "总数量", name = "totalCount")
    private Integer totalCount;

    @ApiModelProperty(value = "修改时间", name = "updateTime")
    private Long updateTime;


    @ApiModelProperty(value = "创建时间", name = "createTime")
    private Long createTime;

    private Boolean delFlag;

    public BlockingStrategyLogResult toResult() {
        BlockingStrategyLogResult dto = new BlockingStrategyLogResult();
        dto.setId(this.id);
        dto.setDeployIp(this.deployIp);
        dto.setUpstreamId(this.upstreamId);
        dto.setUpstreamDesc(this.upstreamDesc);
        dto.setBlockId(this.blockId);
        dto.setStrategyName(this.strategyName);
        dto.setStrategyType(this.strategyType);
        dto.setSuccessCount(this.successCount);
        dto.setFailCount(this.failCount);
        dto.setTotalCount(this.totalCount);
        if (this.net != null) {
            dto.setIp(this.net.getDstIp());
        }
        if (this.req != null) {
            dto.setApi(this.req.getUrl());
        }
        //todo 风险id
//        dto.setRiskId(this.age);
        dto.setUpdateTime(this.updateTime);
        dto.setCreateTime(this.createTime);
        return dto;
    }
}
