package com.quanzhi.auditapiv2.common.dal.dao.impl;

import com.quanzhi.audit_core.common.model.HostAnalysisConfigV2;
import com.quanzhi.auditapiv2.common.dal.dao.IHostAnalysisConfigV2NacosDao;
import com.quanzhi.auditapiv2.common.dal.dao.base.impl.NacosBaseDaoImpl;
import org.springframework.stereotype.Repository;

/**
 * 《域名解析配置V2持久层Nacos接口实现》
 *
 * <AUTHOR> [<EMAIL>]
 */
@Repository
public class HostAnalysisConfigV2NacosDaoImpl extends NacosBaseDaoImpl<HostAnalysisConfigV2> implements IHostAnalysisConfigV2NacosDao {
}
