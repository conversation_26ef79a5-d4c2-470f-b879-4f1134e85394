package com.quanzhi.auditapiv2.common.dal.dao.report;

import com.quanzhi.auditapiv2.common.util.dto.ListOutputDto;
import com.quanzhi.metabase.core.model.http.HttpApiResource;
import com.quanzhi.metabase.core.model.http.report.ApiLabelDateStat;

import java.util.List;

/**
 * @Author: HaoJun
 * @Date: 2020/9/14 2:55 下午
 */
public interface ApiDataLabelReportDao {

    ListOutputDto<HttpApiResource> listNewLabelApi(ApiDataLabelCriteria criteria);

    ListOutputDto<HttpApiResource> listMissingLabelApi(ApiDataLabelCriteria criteria);

    List<ApiLabelDateStat> listStat(DataLabelStatCriteria criteriaDto);

}
