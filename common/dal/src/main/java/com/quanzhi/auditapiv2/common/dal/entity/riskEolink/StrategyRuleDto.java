package com.quanzhi.auditapiv2.common.dal.entity.riskEolink;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
public class StrategyRuleDto {
    @ApiModelProperty(value = "策略指标", name = "rules")
    private List<StrategyIndicators> rules;
    /**
     *
     */
    @ApiModelProperty(value = "指标规则", name = "indicatorsRules")
    private String indicatorsRules;
    @Data
    public static class StrategyIndicators{
        @ApiModelProperty(value = "指标类型", name = "type")
        private String type;
        @ApiModelProperty(value = "指标内容", name = "content")
        private List<String> content;
    }
}
