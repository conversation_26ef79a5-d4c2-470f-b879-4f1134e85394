package com.quanzhi.auditapiv2.common.dal.dto.weakness;

import io.swagger.annotations.ApiModel;
import lombok.Data;

import java.util.Map;

@Data
@ApiModel("弱点测试")
public class WeaknessTestDto {
    /**
     * 发送测试的请求IP的地址
     */
    private String ip;
    /**
     * 请求路径
     */
    private String url;
    /**
     * 请求方式
     */
    private String method;
    /**
     * 请求头
     */
    private Map<String, String> header;

    private String params;

    private String body;

}
