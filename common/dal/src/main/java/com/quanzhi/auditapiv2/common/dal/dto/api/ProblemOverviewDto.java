package com.quanzhi.auditapiv2.common.dal.dto.api;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * @Auther: yangzixian
 * @Date: 2021/8/30 14:07
 * @Description:
 */
@Data
@ApiModel("问题概览")
public class ProblemOverviewDto {

    private String id;

    private String type;

    @ApiModelProperty("API数量")
    private Long apiNum;

    @ApiModelProperty("弱点数量")
    private Long weaknessNum;

    @ApiModelProperty("风险数量")
    private Long riskNum;

    @ApiModelProperty("是否展示API")
    private boolean showApi;

    @ApiModelProperty("是否展示弱点")
    private boolean showWeakness;

    @ApiModelProperty("是否展示风险")
    private boolean showRisk;

    @ApiModelProperty("API等级范围")
    private List<String> apiLevel;

    @ApiModelProperty("弱点等级范围")
    private List<Integer> weaknessLevel;

    @ApiModelProperty("风险等级范围")
    private List<Integer> riskLevel;

}
