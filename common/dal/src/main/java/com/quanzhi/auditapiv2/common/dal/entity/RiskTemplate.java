package com.quanzhi.auditapiv2.common.dal.entity;

import com.alibaba.fastjson.JSONObject;
import lombok.Data;

import java.util.List;

@Data
public class RiskTemplate {
    private List<Template> templates;
    private List<JSONObject> operators;
    private List<JSONObject> featureDescriptions;
    private List<JSONObject> entities;
    private List<JSONObject> cycles;

    @Data
    public static class Template {
        private String cycle;
        private String topic;
        private List<String> entities;
        private List<Feature> features;
    }
    @Data
    public static class Feature {
        private String name;
        private String type;
        private String path;
        private String var;
        private String dateType;
        private List<String> operators;
        private boolean enable;
        private String udf;
        private List<String> descriptions;
        private Baseline baseline;
        private List<Options> options;
    }
    @Data
    public static class Options {
        private String keyed;
        private String name;
        private String classify;
        private String baseline;
        private String type;
        private String time;
        private String entity;
    }
    @Data
    public static class Baseline {
        private String type;
        private String time;
        private String entity;
    }
}
