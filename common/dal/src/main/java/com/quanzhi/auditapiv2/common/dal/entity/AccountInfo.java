package com.quanzhi.auditapiv2.common.dal.entity;

import com.quanzhi.auditapiv2.common.dal.entity.account.AbstractTarget;
import com.quanzhi.metabase.core.model.http.HttpResourceConstant;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;
import org.springframework.data.mongodb.core.mapping.Document;

import java.util.Set;

/**
 * <AUTHOR>
 * @date 2020/10/21 下午5:57
 */
@EqualsAndHashCode(callSuper = true)
@Data
@SuperBuilder
@Document(HttpResourceConstant.ACCOUNT_INFO)
@AllArgsConstructor
@NoArgsConstructor
public class AccountInfo extends AbstractTarget {
    private String account;
    private Set<String> apiUriList;
    private String staffDepart;
    private String staffNickName;
    private String staffName;
    private String staffChinese;
    private String staffIdCard;
    private String staffBankCard;
    private String staffEmail;
    private String staffId;
    private String staffMobile;
    private String staffRole;
    private Set<String> relatedIpList;
    private Long relatedIpDistinctCnt;
    private Integer strategyStatus = 0;

    public String getAppUri() {
        return this.getAppUriList().stream().findAny().orElse(null);
    }
}
