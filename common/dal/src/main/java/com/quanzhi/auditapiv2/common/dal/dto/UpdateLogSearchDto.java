package com.quanzhi.auditapiv2.common.dal.dto;

import lombok.Data;

@Data
public class UpdateLogSearchDto {
    /**
     * 上一版本版本号
     */
    private String oldVersion;

    /**
     * 上一版本升级说明
     */
    private String oldDesc;

    /**
     * 当前系统版本号
     */
    private String version;

    /**
     * 当前系统版本说明
     */
    private String desc;

    /**
     * 执行步骤
     */
    private String step;

    /**
     * 版本更新或回滚状态  失败/成功
     */
    private String isSuccess;

    private String keyword;

    private Long startTime;

    private Long endTime;

    /**
     * 升级包id
     */
    private String pkgId;
}
