package com.quanzhi.auditapiv2.common.dal.entity;

import lombok.Data;

/**
 * @author: yangzixian
 * @date: 2/4/2023 12:32
 * @description:
 */
@Data
public class TestResultEntity {

    private Boolean testNacosResult;

//    private String testNacosError;

    private Boolean testMongoResult;

//    private String testMongoError;

//    private Boolean testConvertResult;

//    private String testConvertError;

    private Boolean testKafkaResult;

//    private String testKafkaError;

    private Boolean testSyslogResult;

//    private String testSyslogError;

    private Boolean testWebhookResult;

    private String errorMsg;

}
