package com.quanzhi.auditapiv2.common.dal.dao.impl;

import com.alibaba.nacos.api.annotation.NacosInjected;
import com.alibaba.nacos.api.config.ConfigService;
import com.alibaba.nacos.api.exception.NacosException;
import com.quanzhi.auditapiv2.common.dal.dao.ISimpleNacosDao;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Repository;

/**
 * <AUTHOR>
 * @date 2021/2/2 下午3:16
 */
@Repository
@Slf4j
public class SimpleNacosDaoImpl implements ISimpleNacosDao {

    private static final long timeoutMs = 5000;

    @NacosInjected
    private ConfigService configService;

    @Override
    public String getContent(String dataId,String group) {

        try {

            return configService.getConfig(dataId, group, timeoutMs);

        } catch (NacosException e) {

            log.error("获取nacos配置:【groupId:{}, dataId:{}】失败 \n {}", group, dataId, e.getErrMsg());
            return null;
        }
    }

    @Override
    public Boolean publishContent(String dataId,String group,String content) {

        try {
            return configService.publishConfig(dataId,group,content);

        }catch (NacosException e) {

            log.error("publish nacos配置:【groupId:{}, dataId:{}】失败 \n {}", group, dataId, e.getErrMsg());
            return false;
        }
    }
}
