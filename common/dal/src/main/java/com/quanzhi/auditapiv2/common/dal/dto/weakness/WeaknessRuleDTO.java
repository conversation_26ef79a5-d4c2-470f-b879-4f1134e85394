package com.quanzhi.auditapiv2.common.dal.dto.weakness;

import com.quanzhi.audit_core.common.model.PluginPolicy;
import com.quanzhi.audit_core.common.model.ScopeMatchInfo;
import com.quanzhi.re.core.domain.entity.po.MatchRule;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @class WeaknessRuleDTO
 * @created 2024/8/12 15:27
 * @desc
 **/
@Data
@ApiModel("弱点规则")
public class WeaknessRuleDTO {

    private String id;

    private String name;

    @ApiModelProperty("弱点规则的类别")
    private String type;

    /**
     * 等级
     */
    @ApiModelProperty("等级，高-3，中-2，低-1")
    private Integer level;

    @ApiModelProperty("弱点规则的类别名称，比如：Web安全缺陷、身份认证缺陷等")
    private String typeName;

    private List<PluginPolicy> pluginPolicies;

    @ApiModelProperty("策略：条件和逻辑表达式")
    private MatchRule matchRule;

    @ApiModelProperty("规则状态：true-开，false-关")
    private Boolean enable;

    @ApiModelProperty("旧的内置规则的作用域，需要升级成新的形式")
    private MatchRule scopeRule;

    /**
     * 规则描述
     */
    private String description;

    /**
     * 0:内置，1:自定义
     */
    private Integer mode;

    private ScopeMatchInfo scopeMatchInfo;

    /**
     * 新的规则也加了黑名单
     */
    private List<String> appBlackList;
    private List<String> apiBlackList;
}
