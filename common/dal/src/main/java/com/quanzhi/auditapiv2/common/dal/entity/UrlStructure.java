package com.quanzhi.auditapiv2.common.dal.entity;

import lombok.Data;

import java.util.List;
import java.util.Set;

/**
 * @author: zhousong
 * @data: 2018/7/22
 * url结构
 */
@Data
public class UrlStructure {

    /**
     * ID
     */
    private String id;

    /**
     * apiId 接口的id
     */
    private String apiId;

    /**
     * 层级
     */
    private Integer level;

    /**
     * 接口等级
     */
    private Set<String> apiLevel;

    /**
     * 应用的id
     */
    private String appId;

    /**
     * 域名
     */
    private String host;

    /**
     * 路径
     */
    private String path;

    /**
     * url前缀
     */
    private String preUrl;

    /**
     * url前缀列表
     */
    private List<String> preUrls;

    /**
     * url = preUrl + path
     */
    private String url;

    /**
     * 节点类型
     * @see NodeTypeEnum
     */
    private Integer nodeType;
    
    /**
     * 访问域列表
     */
    private Set<String> visitDomains;

    /**
     * 接口请求类型
     */
    private Set<String> reqContentTypes;

    /**
     * 接口返回类型
     */
    private Set<String> rspContentTypes;

    private Set<String> methods;

    private String apiName;

    private Set<String> featureLabels;

    private Set<String> classifications;

    private Set<String> reqDataLabels;

    private Set<String> rspDataLabels;
    
    /**
     * 设备终端
     */
    private Set<String> terminals;

    /**
     * 发现时间
     */
    private Long discoverTime;

    private Integer childCount;

    public enum NodeTypeEnum {

        /**
         * 协议节点
         */
        PROTOCOL(0),

        /**
         * 域名节点
         */
        HOST(1),

        /**
         * 非叶子节点
         * 代表url前缀
         */
        NON_LEAF(2),

        /**
         * 叶子节点
         * 代表完整的url
         */
        LEAF(3);

        private int val;

        NodeTypeEnum(int val) {
            this.val = val;
        }

        public Integer value() {
            return this.val;
        }
    }
}
