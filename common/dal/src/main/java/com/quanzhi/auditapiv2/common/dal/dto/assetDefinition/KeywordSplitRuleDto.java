package com.quanzhi.auditapiv2.common.dal.dto.assetDefinition;


import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
/**
 * <AUTHOR>
 * create at 2022/1/7 2:25 下午
 * @description:
 **/
@Data
@ApiModel
public class KeywordSplitRuleDto {

    private String id;

    @ApiModelProperty("拆分应用范围(ALL|具体的host)")
    private String host;

    @ApiModelProperty("关键词")
    private String keyword;

    private Boolean delFlag;

    @ApiModelProperty("新增时间")
    private Long createTime;

    @ApiModelProperty("执行时间")
    private Long executeTime;

    @ApiModelProperty("任务是否结束")
    private boolean finish;

    private Long updateTime;

    private Integer page;

    private Integer limit;

}