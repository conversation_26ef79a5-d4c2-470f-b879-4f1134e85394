package com.quanzhi.auditapiv2.common.dal.entity.riskEolink;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 威胁IP与策略信息关联
 */
@Data
public class RiskIpStrategyDto {
    @ApiModelProperty(value = "id", name = "id")
    private String id;
    @ApiModelProperty(value = "威胁IP", name = "ip")
    private String ip;
    @ApiModelProperty(value = "威胁账号", name = "account")
    private String account;
    @ApiModelProperty(value = "威胁IPId 暂不使用", name = "ipId")
    private String ipId;
    @ApiModelProperty(value = "策略id", name = "strategyUuid")
    private String strategyUuid;
    @ApiModelProperty(value = "策略id", name = "strategyId")
    private String strategyId;
    @ApiModelProperty(value = "管控状态 0未管控 1已管控", name = "status")
    private Integer status = 0;
    @ApiModelProperty(value = "管控类型", name = "strategyType")
    private String strategyType;
    @ApiModelProperty(value = "阻断时间/限流次数", name = "count")
    private Integer count;

    @ApiModelProperty(value = "是否生效与次IP在API的其他风险", name = "isAll")
    private Boolean isAll;
    private String updateTime;
    private String createTime;
}
