package com.quanzhi.auditapiv2.common.dal.dao.impl;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.nacos.api.config.annotation.NacosValue;
import com.mongodb.client.MongoCursor;
import com.quanzhi.audit_core.common.model.AssetLifeStateConfig;
import com.quanzhi.auditapiv2.common.dal.dao.IHttpApiDao;
import com.quanzhi.auditapiv2.common.dal.dao.base.impl.MetabaseDaoImpl;
import com.quanzhi.auditapiv2.common.dal.dao.convert.AssetLifeStateConfigConvert;
import com.quanzhi.auditapiv2.common.dal.dao.convert.SpecialQueryFieldConvert;
import com.quanzhi.auditapiv2.common.dal.dao.impl.common.CustomPropertySearchAdapter;
import com.quanzhi.auditapiv2.common.dal.dao.impl.common.Module;
import com.quanzhi.auditapiv2.common.dal.dao.impl.common.QueryAdapterRegistry;
import com.quanzhi.auditapiv2.common.dal.dto.ApiAccParseCriteriaDto;
import com.quanzhi.auditapiv2.common.dal.dto.HttpApiSearchDto;
import com.quanzhi.auditapiv2.common.dal.dto.api.ApiStatistics;
import com.quanzhi.auditapiv2.common.dal.dto.query.QueryHelper;
import com.quanzhi.auditapiv2.common.dal.entity.CountEntity;
import com.quanzhi.auditapiv2.common.dal.entity.riskEolink.SyncGatewayDto;
import com.quanzhi.auditapiv2.common.dal.enums.DBOperatorEnum;
import com.quanzhi.auditapiv2.common.dal.enums.ProductTypeEnum;
import com.quanzhi.auditapiv2.common.dal.enums.TagsEnum;
import com.quanzhi.auditapiv2.common.util.dto.AggregationDto;
import com.quanzhi.auditapiv2.common.util.dto.GroupDto;
import com.quanzhi.auditapiv2.common.util.utils.ConstantUtil;
import com.quanzhi.auditapiv2.common.util.utils.DataUtil;
import com.quanzhi.auditapiv2.common.util.utils.ServiceException;
import com.quanzhi.metabase.core.model.http.ApiAccParseRelation;
import com.quanzhi.metabase.core.model.http.HttpApiFlagComposite;
import com.quanzhi.metabase.core.model.http.HttpApiResource;
import com.quanzhi.metabase.core.model.http.HttpResourceConstant;
import com.quanzhi.metabase.core.model.http.api.ApiState;
import com.quanzhi.metabase.core.model.http.constant.AssetLifeStateEnum;
import com.quanzhi.metabase.core.model.http.report.HttpApiDateTotalStat;
import com.quanzhi.metabase.core.model.http.weakness.State;
import com.quanzhi.metabase.core.model.query.*;
import lombok.extern.slf4j.Slf4j;
import org.bson.Document;
import org.bson.types.ObjectId;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.aggregation.*;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.stereotype.Repository;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import static org.springframework.data.mongodb.core.aggregation.Aggregation.newAggregationOptions;

@Slf4j
@Repository("httpApiDao")
public class HttpApiDaoImpl extends MetabaseDaoImpl<HttpApiResource> implements IHttpApiDao {

    @Autowired
    private MongoTemplate mongoTemplate;

    @Autowired
    public CustomPropertySearchAdapter customPropertySearchAdapter;

    @Autowired
    public QueryAdapterRegistry queryAdapterRegistry;

    @Autowired
    public AssetLifeStateConfigConvert assetLifeStateConfigConvert;

    @Autowired
    public SpecialQueryFieldConvert specialQueryFieldConvert;

    private String collectionName = "httpApi";
    private String collectionName_weak = "httpApiWeakness";
    private String collectionName_app = "httpApp";
    private String collectionName_risk = "aggRiskInfo";

    public static final String PARSE_CONFIG_USER = "userParseConfig_";
    public static final String PARSE_CONFIG_SESSION = "sessionParseConfig_";
    public static final String PARSE_CONFIG_PWD = "passwordParseConfig_";
    public static final String PARSE_CONFIG_LOGIN = "loginSuccessConfig_";

    public static final String PARSE_CONFIG_UNABLE = "unable_";
    public static final String PARSE_CONFIG_USER_UNABLE = PARSE_CONFIG_USER + PARSE_CONFIG_UNABLE;
    public static final String PARSE_CONFIG_SESSION_UNABLE = PARSE_CONFIG_SESSION + PARSE_CONFIG_UNABLE;
    public static final String PARSE_CONFIG_PWD_UNABLE = PARSE_CONFIG_PWD + PARSE_CONFIG_UNABLE;
    public static final String PARSE_CONFIG_LOGIN_UNABLE = PARSE_CONFIG_LOGIN + PARSE_CONFIG_UNABLE;

    @NacosValue(value = "${product.type:api}", autoRefreshed = true)
    private String productType;

    @Override
    public String updateApiState(String id, String apiState, String remark) {
        Update update = new Update();
        update.set("state", apiState);
        update.set("remark", remark);
        mongoTemplate.upsert(new Query().addCriteria(org.springframework.data.mongodb.core.query.Criteria.where("_id").is(new ObjectId(id))), update, collectionName);
        return "修改成功！";
    }

    @Override
    public List<HttpApiResource> selectHttpApiListByFeatureLabel(String featureLabel) throws Exception {
        MetabaseQuery metabaseQuery = new MetabaseQuery();
        metabaseQuery.where("delFlag", Predicate.IS, false);
        metabaseQuery.where("featureLabels", Predicate.IS, featureLabel);

        return metabaseClientTemplate.find(metabaseQuery, HttpApiResource.class);
    }

    @Override
    public List<HttpApiResource> selectHttpApiListByDataLabel(String dataLabel) throws Exception {
        MetabaseQuery metabaseQuery = new MetabaseQuery();
        metabaseQuery.where("delFlag", Predicate.IS, false);
        metabaseQuery.where("dataLabels", Predicate.IS, dataLabel);

        return metabaseClientTemplate.find(metabaseQuery, HttpApiResource.class);
    }

    @Override
    public void updateHttpApiByFeatureLabel(String label) throws Exception {
        List<HttpApiResource> httpApiResources = this.selectHttpApiListByFeatureLabel(label);
        for (HttpApiResource httpApiResource : httpApiResources) {
            httpApiResource.getFeatureLabels().remove(label);
            Update update = new Update();
            update.set("featureLabels", httpApiResource.getFeatureLabels());
            mongoTemplate.upsert(new Query().addCriteria(org.springframework.data.mongodb.core.query.Criteria.where("_id").is(httpApiResource.getId())), update, collectionName);
        }
    }

    @Override
    public void updateHttpApiByDataLabel(String label) throws Exception {
        List<HttpApiResource> httpApiResources = this.selectHttpApiListByDataLabel(label);
        for (HttpApiResource httpApiResource : httpApiResources) {
            httpApiResource.getDataLabels().remove(label);
            Update update = new Update();
            update.set("dataLabels", httpApiResource.getDataLabels());
            mongoTemplate.upsert(new Query().addCriteria(org.springframework.data.mongodb.core.query.Criteria.where("_id").is(httpApiResource.getId())), update, collectionName);
        }
    }

    @Override
    public long getSearchCount(HttpApiSearchDto httpApiSearchDto) {

        MetabaseQuery query = new MetabaseQuery();
        query = fillMetabaseQuery(query, httpApiSearchDto);
        return getCount(query);
    }


    @Override
    public MetabaseQuery convertQuery(HttpApiSearchDto apiSearchDto) {
        MetabaseQuery query = new MetabaseQuery();
        query = fillMetabaseQuery(query, apiSearchDto);
        return query;
    }

    @Override
    public long countUseDataApi() {
        return mongoTemplate.count(new Query().addCriteria(org.springframework.data.mongodb.core.query.Criteria.where("visitDomains").regex("互联网-")
                .and("state").is(ApiState.UNCONFIRMED.name())
                .and("delFlag").is(false)), collectionName);
    }

    @Override
    public Long getCount(Long startTime, Long endTime, String type) {
        MetabaseQuery metabaseQuery = new MetabaseQuery();
        metabaseQuery.where("delFlag", Predicate.IS, false);
        Criteria criteria = new Criteria();
        if (DataUtil.isNotEmpty(startTime) && DataUtil.isNotEmpty(endTime)) {
            if (type.equals("1")) {
                criteria = criteria.andOperator(Criteria.where("discoverTime").gte(startTime), Criteria.where("discoverTime").lte(endTime));
            } else {
                criteria = criteria.andOperator(Criteria.where("activeTime").gte(startTime), Criteria.where("activeTime").lte(endTime));
            }
            metabaseQuery.getCriteria().add(criteria);
        }
        return metabaseClientTemplate.count(metabaseQuery, HttpApiResource.class);
    }

    @Override
    public List<HttpApiResource> getHttpApis(HttpApiSearchDto httpApiSearchDto) {
        MetabaseQuery query = new MetabaseQuery();
        query = fillMetabaseQuery(query, httpApiSearchDto);

        return getList(query);
    }

    @Override
    public List<HttpApiResource> getHttpApisByUris(String appUri, Set<String> apiUriSet, Set<String> relateIdSet) {
        List<org.springframework.data.mongodb.core.query.Criteria> criteriaList = new ArrayList<>();

        org.springframework.data.mongodb.core.query.Criteria criteriaBase = new org.springframework.data.mongodb.core.query.Criteria();

        if (DataUtil.isNotEmpty(appUri)) {
            org.springframework.data.mongodb.core.query.Criteria acc = org.springframework.data.mongodb.core.query.Criteria.where("tags").is(TagsEnum.accountParse.name());
            org.springframework.data.mongodb.core.query.Criteria enable = org.springframework.data.mongodb.core.query.Criteria.where("accParseEnable").is(true);
            org.springframework.data.mongodb.core.query.Criteria parse = new org.springframework.data.mongodb.core.query.Criteria();
            parse.orOperator(acc, enable);

            criteriaBase.and("appUri").is(appUri).andOperator(parse);
            criteriaList.add(criteriaBase);
        }

        if (DataUtil.isNotEmpty(apiUriSet)) {
            criteriaList.add(org.springframework.data.mongodb.core.query.Criteria.where("uri").in(apiUriSet));
        }

        if (DataUtil.isNotEmpty(relateIdSet)) {
            criteriaList.add(org.springframework.data.mongodb.core.query.Criteria.where("apiAccParseRelations.relateId").in(relateIdSet));
        }

        if (criteriaList.size() > 0) {
            org.springframework.data.mongodb.core.query.Criteria criteria = new org.springframework.data.mongodb.core.query.Criteria();
            criteria.orOperator(criteriaList.toArray(new org.springframework.data.mongodb.core.query.Criteria[criteriaList.size()]));

            AggregationOperation matchOperation = Aggregation.match(criteria);
            LimitOperation limitOperation = Aggregation.limit(100);
//            GroupOperation groupOperation = Aggregation.group("_id")
//                    .first("_id").as("id")
//                    .push("apiAccParseRelations").as("apiAccParseRelations");

            List<AggregationOperation> operationList = new ArrayList<>();
            if (DataUtil.isNotEmpty(relateIdSet)) {
                // "$unwind":{"path":"$apiAccParseRelations", preserveNullAndEmptyArrays: true}
                UnwindOperation unwind = Aggregation.unwind("apiAccParseRelations", true);
                operationList.add(unwind);
            }
            operationList.add(matchOperation);
//            operationList.add(groupOperation);
            operationList.add(limitOperation);

            Aggregation aggregation = Aggregation.newAggregation(operationList);
            AggregationResults<Document> results_ = mongoTemplate.aggregate(aggregation, collectionName, Document.class);
            List<Document> results = results_.getMappedResults();
            if (DataUtil.isNotEmpty(results)) {
                List<HttpApiResource> list = new ArrayList<>();
                for (Document document : results) {
                    HttpApiResource httpApiResource = JSONObject.parseObject(JSONObject.toJSONString(document), HttpApiResource.class);
                    ObjectId objectId = (ObjectId) document.get("_id");
                    httpApiResource.setId(objectId.toString());
                    list.add(httpApiResource);
                }
                return list;
            }

            // todo 带上unwind时，查询报错了，不知道为啥，先用转换的方式
//            AggregationResults<HttpApiResource> resultss = mongoTemplate.aggregate(aggregation, collectionName, HttpApiResource.class);
//            return results.getMappedResults();
        }

        return new ArrayList<>();
    }

    @Override
    public List<HttpApiResource> getHttpApis(Integer page, Integer limit, String field, Integer sort, HttpApiSearchDto httpApiSearchDto) {
        MetabaseQuery query = new MetabaseQuery();
        if (DataUtil.isNotEmpty(page) && DataUtil.isNotEmpty(limit) && DataUtil.isNotEmpty(field) && DataUtil.isNotEmpty(sort)) {
            query.setSkip((page - 1) * limit);
            query.limit(limit);
            //排序
            if (DataUtil.isNotEmpty(field) && DataUtil.isNotEmpty(sort)) {
                if (sort == ConstantUtil.Sort.ASC) {
                    query.sort(Sort.by(field, SortOrder.ASC));
                } else if (sort == ConstantUtil.Sort.DESC) {
                    query.sort(Sort.by(field, SortOrder.DESC));
                }
            }
        }
        query = fillMetabaseQuery(query, httpApiSearchDto);
        return getList(query);
    }

    @Override
    public List<HttpApiResource> getHttpApisFields(Integer page, Integer limit, String field, Integer sort, HttpApiSearchDto httpApiSearchDto, List<String> fields) {
        MetabaseQuery query = new MetabaseQuery();
        query.setSkip((page - 1) * limit);
        query.limit(limit);
        query = fillMetabaseQuery(query, httpApiSearchDto);

        //排序
        if (DataUtil.isNotEmpty(field) && DataUtil.isNotEmpty(sort)) {
            if (sort == ConstantUtil.Sort.ASC) {
                query.sort(Sort.by(field, SortOrder.ASC));
            } else if (sort == ConstantUtil.Sort.DESC) {
                query.sort(Sort.by(field, SortOrder.DESC));
            }
        }
        if (fields != null && fields.size() > 0) {
            query.setFields(fields);
        }
        return getList(query);
    }

    private MetabaseQuery fillMetabaseQuery(MetabaseQuery metabaseQuery, HttpApiSearchDto httpApiSearchDto) {

        if (DataUtil.isEmpty(httpApiSearchDto)) {
            return metabaseQuery;
        }
        try {
            specialQueryFieldConvert.specialQueryFieldConvert(httpApiSearchDto);
        } catch (Exception e) {
            log.error("specialQueryFieldConvert error:{}", e);
        }
        Map<String, String> fieldOperateMap = httpApiSearchDto.getFieldOperateMap();
        if (DataUtil.isNotEmpty(httpApiSearchDto.getId())) {
            metabaseQuery.where("_id", Predicate.IS, httpApiSearchDto.getId());
        }
        if (DataUtil.isNotEmpty(httpApiSearchDto.getIds())) {
            metabaseQuery.where("_id", Predicate.IN, httpApiSearchDto.getIds());
        }
        if (DataUtil.isNotEmpty(httpApiSearchDto.getProvince())) {
            metabaseQuery.where("provinces", Predicate.IN, httpApiSearchDto.getProvince());
        }

        if (DataUtil.isNotEmpty(httpApiSearchDto.getApiSearch())) {
            String value = httpApiSearchDto.getApiSearch();
            value = DataUtil.regexStrEscape(value);
            value = value.startsWith("http") ? "^" + value : value;
            //metabaseQuery.where("apiUrl", Predicate.REGEX, Pattern.compile(DataUtil.regexStrEscape(String.valueOf(value)), Pattern.CASE_INSENSITIVE));
            metabaseQuery.where("apiUrl", Predicate.REGEX, value);
            Document document = new Document();
            document.put("apiUrl", 1);
            metabaseQuery.setHint(document.toJson());
           /* Criteria criteria1 = Criteria.where("apiUrl").regex(Pattern.compile(DataUtil.regexStrEscape(String.valueOf(value)), Pattern.CASE_INSENSITIVE));
            Criteria criteria2 = Criteria.where("name").regex(Pattern.compile(DataUtil.regexStrEscape(String.valueOf(value)), Pattern.CASE_INSENSITIVE));
            Criteria criteria = new Criteria();
            criteria.orOperator(criteria1, criteria2);
            metabaseQuery.getCriteria().add(criteria);*/
        }
        /**
         * api生命状态
         */
        if (DataUtil.isNotEmpty(httpApiSearchDto.getApiLifeFlag())) {
            //metabaseQuery.where("apiLifeFlag", Predicate.IN, httpApiSearchDto.getApiLifeFlag());
            Short lifeFlag = httpApiSearchDto.getApiLifeFlag().get(0);
            AssetLifeStateEnum assetLifeStateEnum = AssetLifeStateEnum.valueOfNum(lifeFlag);
            AssetLifeStateConfigConvert.CriteriaTag criteriaTag = assetLifeStateConfigConvert.getCriteria(AssetLifeStateConfig.AssetTypeEnum.API, assetLifeStateEnum);
            metabaseQuery.getCriteria().add(criteriaTag.getMetabaseCriteria());
        }
        /*  *//**
         * 风险名称
         *//*
        if (DataUtil.isNotEmpty(httpApiSearchDto.getRiskNames())) {
            String operate = fieldOperateMap.get("riskNames");
            boolean isAll = httpApiSearchDto.getRiskNames().get(0).equals("ALL");
            List<String> allRisk = isAll ? httpApiSearchDto.getRiskMap().values().stream().collect(Collectors.toList()) : new ArrayList<>();
            if (DBOperatorEnum.OR.operator().equals(operate)) {
                metabaseQuery.where("apiStat.riskNames", Predicate.IN, isAll ? allRisk : httpApiSearchDto.getRiskNames());
            } else {
                metabaseQuery.where("apiStat.riskNames", Predicate.ALL, isAll ? allRisk : httpApiSearchDto.getRiskNames());
            }
        }*/
        /**
         * 风险名称ids
         */
        if (DataUtil.isNotEmpty(httpApiSearchDto.getRiskIds())) {
            String operate = fieldOperateMap.get("riskIds");
            boolean isAll = httpApiSearchDto.getRiskIds().get(0).equals("ALL");
            List<String> allRisk = isAll ? httpApiSearchDto.getRiskMap().keySet().stream().collect(Collectors.toList()) : new ArrayList<>();
            if (DBOperatorEnum.OR.operator().equals(operate)) {
                metabaseQuery.where("apiStat.riskIds", Predicate.IN, isAll ? allRisk : httpApiSearchDto.getRiskIds());
            } else {
                metabaseQuery.where("apiStat.riskIds", Predicate.ALL, isAll ? allRisk : httpApiSearchDto.getRiskIds());
            }
        }
        /*    *//**
         * 弱点名称
         *//*
        if (DataUtil.isNotEmpty(httpApiSearchDto.getWeaknessNames())) {
            String operate = fieldOperateMap.get("weaknessNames");
            boolean isAll = httpApiSearchDto.getWeaknessNames().get(0).equals("ALL");
            List<String> allWeakness = isAll ? httpApiSearchDto.getWeaknessMap().values().stream().collect(Collectors.toList()) : new ArrayList<>();
            if (DBOperatorEnum.OR.operator().equals(operate)) {
                metabaseQuery.where("apiStat.weaknessNames", Predicate.IN, isAll ? allWeakness : httpApiSearchDto.getWeaknessNames());
            } else {
                metabaseQuery.where("apiStat.weaknessNames", Predicate.ALL, isAll ? allWeakness : httpApiSearchDto.getWeaknessNames());
            }
        }*/
        /**
         * 弱点名称ids
         */
        if (DataUtil.isNotEmpty(httpApiSearchDto.getWeaknessIds())) {
            String operate = fieldOperateMap.get("weaknessIds");
            boolean isAll = httpApiSearchDto.getWeaknessIds().get(0).equals("ALL");
            List<String> allWeakness = isAll ? httpApiSearchDto.getWeaknessMap().keySet().stream().collect(Collectors.toList()) : new ArrayList<>();
            if (DBOperatorEnum.OR.operator().equals(operate)) {
                metabaseQuery.where("apiStat.weaknessIds", Predicate.IN, isAll ? allWeakness : httpApiSearchDto.getWeaknessIds());
            } else {
                metabaseQuery.where("apiStat.weaknessIds", Predicate.ALL, isAll ? allWeakness : httpApiSearchDto.getWeaknessIds());
            }
        }
        /**
         * 流量来源
         */
        if (DataUtil.isNotEmpty(httpApiSearchDto.getFlowSource())) {
            metabaseQuery.where("flowSources", Predicate.IS, httpApiSearchDto.getFlowSource());
        }
        /**
         * 备注
         */
        if (DataUtil.isNotEmpty(httpApiSearchDto.getRemark())) {
            metabaseQuery.where("remark", Predicate.REGEX, httpApiSearchDto.getRemark());
        }
        if (DataUtil.isNotEmpty(httpApiSearchDto.getUri())) {
            metabaseQuery.where("uri", Predicate.IS, httpApiSearchDto.getUri());
        }
        if (DataUtil.isNotEmpty(httpApiSearchDto.getUris())) {
            metabaseQuery.where("uri", Predicate.IN, httpApiSearchDto.getUris());
        }

        if (DataUtil.isNotEmpty(httpApiSearchDto.getApiUrl())) {
            metabaseQuery.where("apiUrl", Predicate.REGEX, DataUtil.regexStrEscape(httpApiSearchDto.getApiUrl()));
        }

        if (DataUtil.isNotEmpty(httpApiSearchDto.getRegexApiUr())) {
            metabaseQuery.where("apiUrl", Predicate.REGEX, DataUtil.regexStrEscape(httpApiSearchDto.getApiUrl()));
        }

        if (DataUtil.isNotEmpty(httpApiSearchDto.getAppUri())) {
            metabaseQuery.where("appUri", Predicate.IS, httpApiSearchDto.getAppUri());
        }

        if (DataUtil.isNotEmpty(httpApiSearchDto.getApiName())) {
            metabaseQuery.where("name", Predicate.REGEX, DataUtil.regexStrEscape(httpApiSearchDto.getApiName()));
        }

        if (DataUtil.isNotEmpty(httpApiSearchDto.getHost())) {
            String host = httpApiSearchDto.getHost();
            if (Boolean.TRUE.equals(httpApiSearchDto.getHostIgnoreCase())) {
                Pattern pattern = Pattern.compile(host, Pattern.CASE_INSENSITIVE);
                metabaseQuery.where("host", Predicate.REGEX, pattern);
            } else {
                metabaseQuery.where("host", Predicate.REGEX, DataUtil.regexStrEscape(host));
            }
            Document document = new Document();
            document.put("host", 1);
            metabaseQuery.setHint(document.toJson());
        }

        if (DataUtil.isNotEmpty(httpApiSearchDto.getAppName())) {
            String appName = httpApiSearchDto.getAppName();
            metabaseQuery.where("appName", Predicate.REGEX, appName);
            Document document = new Document();
            document.put("appName", 1);
            metabaseQuery.setHint(document.toJson());
        }

        if (DataUtil.isNotEmpty(httpApiSearchDto.getUrlNode()) && httpApiSearchDto.getUrlNode() != 0 && DataUtil.isNotEmpty(httpApiSearchDto.getUrlPathList())) {

            for (HttpApiSearchDto.UrlPaths urlPaths : httpApiSearchDto.getUrlPathList()) {

                Criteria criteria = new Criteria();

                List<Criteria> criteriaList = Arrays.asList(Criteria.where("node").is(urlPaths.getUrlNode()), Criteria.where("path").is(urlPaths.getUrlPath()));
                Criteria ele = Criteria.where("urlPaths").elemMatch(criteriaList);
                criteria.andOperator(ele);
                metabaseQuery.getCriteria().add(criteria);
            }
        }
        //接口类型
        if (DataUtil.isNotEmpty(httpApiSearchDto.getApiType())) {
            metabaseQuery.where("apiType", Predicate.IN, httpApiSearchDto.getApiType());
        }
        //接口格式
        if (DataUtil.isNotEmpty(httpApiSearchDto.getApiFormats())) {
            metabaseQuery.where("apiFormats", Predicate.IN, httpApiSearchDto.getApiFormats());
        }
        //接口状态
        if (DataUtil.isNotEmpty(httpApiSearchDto.getState())) {
            metabaseQuery.where("state", Predicate.IS, httpApiSearchDto.getState());
        }
        //API风险等级
        if (DataUtil.isNotEmpty(httpApiSearchDto.getApiRiskLevel())) {
            metabaseQuery.where("apiRiskLevel", Predicate.IN, httpApiSearchDto.getApiRiskLevel());
        }
        //等级
        if (DataUtil.isNotEmpty(httpApiSearchDto.getLevel()) && DataUtil.isEmpty(httpApiSearchDto.getIsSensitiveApi())) {
            metabaseQuery.where("level", Predicate.IN, httpApiSearchDto.getLevel());
        }
        //是否敏感API
        if (DataUtil.isNotEmpty(httpApiSearchDto.getIsSensitiveApi()) && DataUtil.isEmpty(httpApiSearchDto.getLevel())) {
            if (httpApiSearchDto.getIsSensitiveApi()) {
                metabaseQuery.where("level", Predicate.IN, HttpApiSearchDto.sensitiveLevelList);
            } else {
                metabaseQuery.where("level", Predicate.IN, HttpApiSearchDto.noSensitiveLevelList);
            }
        }
        //是否敏感API 和等级
        if (DataUtil.isNotEmpty(httpApiSearchDto.getLevel()) && DataUtil.isNotEmpty(httpApiSearchDto.getIsSensitiveApi())) {
            Criteria criteria = new Criteria();
            Criteria criteria1 = new Criteria();
            Criteria criteria2 = new Criteria();

            criteria1 = Criteria.where("level").in(httpApiSearchDto.getLevel());
            if (httpApiSearchDto.getIsSensitiveApi()) {
                criteria2 = Criteria.where("level").in(HttpApiSearchDto.sensitiveLevelList);
            } else {
                criteria2 = Criteria.where("level").in(HttpApiSearchDto.noSensitiveLevelList);
            }
            criteria = criteria.andOperator(criteria1, criteria2);
            metabaseQuery.getCriteria().add(criteria);
        }


        if (DataUtil.isNotEmpty(httpApiSearchDto.getRestfulFlag())) {
            if (HttpApiFlagComposite.RestfulFlag.RESTFUL_CANCELED == httpApiSearchDto.getRestfulFlag()) {
                metabaseQuery.where("restfulFlag", Predicate.NE, httpApiSearchDto.getRestfulFlag());
            } else {
                metabaseQuery.where("restfulFlag", Predicate.IS, httpApiSearchDto.getRestfulFlag());
            }
        }

        if (DataUtil.isNotEmpty(httpApiSearchDto.getWhenFlag())) {
            metabaseQuery.where("whenFlag", Predicate.IS, httpApiSearchDto.getWhenFlag());
        }

        if (DataUtil.isNotEmpty(httpApiSearchDto.getLowClassifications())) {
            metabaseQuery.where("lowClassifications", Predicate.ALL, httpApiSearchDto.getLowClassifications());
        }

        if (DataUtil.isNotEmpty(httpApiSearchDto.getDataLabels())) {
            metabaseQuery.where("dataLabels", Predicate.ALL, httpApiSearchDto.getDataLabels());
        }

        if (DataUtil.isNotEmpty(httpApiSearchDto.getLowDataLabels())) {
            metabaseQuery.where("lowDataLabels", Predicate.ALL, httpApiSearchDto.getLowDataLabels());
        }

        if (DataUtil.isNotEmpty(httpApiSearchDto.getReqDataLabels())) {
            String operate = fieldOperateMap.get("reqDataLabels");
            boolean reqIsSingle = DataUtil.isNotEmpty(httpApiSearchDto.getReqIsSingle()) && Boolean.TRUE.equals(httpApiSearchDto.getReqIsSingle());
            boolean isAll = httpApiSearchDto.getReqDataLabels().get(0).equals("ALL");
            List<String> allDataLabels = isAll ? httpApiSearchDto.getDataLabelMap().keySet().stream().collect(Collectors.toList()) : new ArrayList<>();
            if (DBOperatorEnum.OR.operator().equals(operate)) {
                if (reqIsSingle) {
                    metabaseQuery.where("samples.reqDataLabels", Predicate.ALL, isAll ? allDataLabels : httpApiSearchDto.getReqDataLabels());
                } else {
                    metabaseQuery.where("reqDataLabels", Predicate.IN, isAll ? allDataLabels : httpApiSearchDto.getReqDataLabels());
                }
            } else {
                if (reqIsSingle) {
                    metabaseQuery.where("samples.reqDataLabels", Predicate.ALL, isAll ? allDataLabels : httpApiSearchDto.getReqDataLabels());
                } else {
                    metabaseQuery.where("reqDataLabels", Predicate.ALL, isAll ? allDataLabels : httpApiSearchDto.getReqDataLabels());
                }
            }
        }

        if (DataUtil.isNotEmpty(httpApiSearchDto.getLowReqDataLabels())) {
            metabaseQuery.where("lowReqDataLabels", Predicate.ALL, httpApiSearchDto.getLowReqDataLabels());
        }

        if (DataUtil.isNotEmpty(httpApiSearchDto.getRspDataLabels())) {
            String operate = fieldOperateMap.get("rspDataLabels");
            boolean rspIsSingle = DataUtil.isNotEmpty(httpApiSearchDto.getRspIsSingle()) && Boolean.TRUE.equals(httpApiSearchDto.getRspIsSingle());
            boolean isAll = httpApiSearchDto.getRspDataLabels().get(0).equals("ALL");
            List<String> allDataLabels = isAll ? httpApiSearchDto.getDataLabelMap().keySet().stream().collect(Collectors.toList()) : new ArrayList<>();
            if (DBOperatorEnum.OR.operator().equals(operate)) {
                if (rspIsSingle) {
                    metabaseQuery.where("samples.rspDataLabels", Predicate.ALL, isAll ? allDataLabels : httpApiSearchDto.getRspDataLabels());
                } else {
                    metabaseQuery.where("rspDataLabels", Predicate.IN, isAll ? allDataLabels : httpApiSearchDto.getRspDataLabels());
                }
            } else {
                if (rspIsSingle) {
                    metabaseQuery.where("samples.rspDataLabels", Predicate.ALL, isAll ? allDataLabels : httpApiSearchDto.getRspDataLabels());
                } else {
                    metabaseQuery.where("rspDataLabels", Predicate.ALL, isAll ? allDataLabels : httpApiSearchDto.getRspDataLabels());
                }
            }
        }

        if (DataUtil.isNotEmpty(httpApiSearchDto.getLowRspDataLabels())) {
            metabaseQuery.where("lowRspDataLabels", Predicate.ALL, httpApiSearchDto.getLowRspDataLabels());
        }

        if (DataUtil.isNotEmpty(httpApiSearchDto.getDelFlag())) {
            metabaseQuery.where("delFlag", Predicate.IS, httpApiSearchDto.getDelFlag());
        }

        if (DataUtil.isNotEmpty(httpApiSearchDto.getVisitDomains())) {
            String operate = fieldOperateMap.get("visitDomains");
            if (DBOperatorEnum.OR.operator().equals(operate)) {
                metabaseQuery.where("visitDomains", Predicate.IN, httpApiSearchDto.getVisitDomains());
            } else {
                metabaseQuery.where("visitDomains", Predicate.ALL, httpApiSearchDto.getVisitDomains());
            }
        }

        if (DataUtil.isNotEmpty(httpApiSearchDto.getDeployDomains())) {
            String operate = fieldOperateMap.get("deployDomains");
            if (DBOperatorEnum.OR.operator().equals(operate)) {
                metabaseQuery.where("deployDomains", Predicate.IN, httpApiSearchDto.getDeployDomains());
            } else {
                metabaseQuery.where("deployDomains", Predicate.ALL, httpApiSearchDto.getDeployDomains());
            }
        }

        if (DataUtil.isNotEmpty(httpApiSearchDto.getReqContentTypes())) {
            metabaseQuery.where("reqContentTypes", Predicate.IN, httpApiSearchDto.getReqContentTypes());
        }

        if (DataUtil.isNotEmpty(httpApiSearchDto.getRspContentTypes())) {
            metabaseQuery.where("rspContentTypes", Predicate.IN, httpApiSearchDto.getRspContentTypes());
        }

        if (DataUtil.isNotEmpty(httpApiSearchDto.getTerminals())) {
            String operate = fieldOperateMap.get("terminals");
            if (DBOperatorEnum.OR.operator().equals(operate)) {
                metabaseQuery.where("terminals", Predicate.IN, httpApiSearchDto.getTerminals());
            } else {
                metabaseQuery.where("terminals", Predicate.ALL, httpApiSearchDto.getTerminals());
            }
        }

        if (DataUtil.isNotEmpty(httpApiSearchDto.getDiscoverTimeStart()) && DataUtil.isNotEmpty(httpApiSearchDto.getDiscoverTimeEnd())) {

            Criteria criteria = new Criteria();
            criteria = criteria.andOperator(Criteria.where("discoverTime").gte(httpApiSearchDto.getDiscoverTimeStart()), Criteria.where("discoverTime").lte(httpApiSearchDto.getDiscoverTimeEnd()));
            metabaseQuery.getCriteria().add(criteria);
        }
        if (DataUtil.isNotEmpty(httpApiSearchDto.getActiveTimeStart()) && DataUtil.isNotEmpty(httpApiSearchDto.getActiveTimeEnd())) {

            Criteria criteria = new Criteria();
            criteria = criteria.andOperator(Criteria.where("activeTime").gte(httpApiSearchDto.getActiveTimeStart()), Criteria.where("activeTime").lte(httpApiSearchDto.getActiveTimeEnd()));
            metabaseQuery.getCriteria().add(criteria);
        }

        if (DataUtil.isNotEmpty(httpApiSearchDto.getRecommendFlag())) {
            metabaseQuery.where("recommendFlag", Predicate.IS, httpApiSearchDto.getRecommendFlag());
        }

        if (DataUtil.isNotEmpty(httpApiSearchDto.getMethods())) {
            metabaseQuery.where("methods", Predicate.IN, httpApiSearchDto.getMethods());
        }

        if (DataUtil.isNotEmpty(httpApiSearchDto.getOrderFlag())) {
            metabaseQuery.where("orderFlag", Predicate.IS, httpApiSearchDto.getOrderFlag());
        }

        if (DataUtil.isNotEmpty(httpApiSearchDto.getRegexVisitDomains())) {
            metabaseQuery.where("visitDomains", Predicate.REGEX, httpApiSearchDto.getRegexVisitDomains());
        }
        if (DataUtil.isNotEmpty(httpApiSearchDto.getNodeId())) {
            metabaseQuery.where("nodes.nid", Predicate.IS, httpApiSearchDto.getNodeId());
        }
        if (DataUtil.isNotEmpty(httpApiSearchDto.getIsApiWeakness())) {
            if (httpApiSearchDto.getIsApiWeakness()) {
                metabaseQuery.where("briefWeaknesses.count", Predicate.GT, 0);
            } else {
                Criteria criteria1 = Criteria.where("briefWeaknesses").is(null);
                Criteria criteria2 = Criteria.where("briefWeaknesses.count").is(0);
                Criteria criteria = new Criteria();
                criteria.orOperator(criteria1, criteria2);
                metabaseQuery.getCriteria().add(criteria);
            }
        }

        if (DataUtil.isNotEmpty(httpApiSearchDto.getFeatureLabels()) || DataUtil.isNotEmpty(httpApiSearchDto.getClassifications())) {

            Criteria criteria = new Criteria();
            Criteria criteria1 = null;
            Criteria criteria2 = null;

            if (DataUtil.isNotEmpty(httpApiSearchDto.getFeatureLabels())) {
                String operate = fieldOperateMap.get("featureLabels");
                if (DBOperatorEnum.OR.operator().equals(operate)) {
                    criteria1 = Criteria.where("featureLabels").in(httpApiSearchDto.getFeatureLabels());
                } else {
                    metabaseQuery.where("featureLabels", Predicate.ALL, httpApiSearchDto.getFeatureLabels());
                }
            }

            if (DataUtil.isNotEmpty(httpApiSearchDto.getClassifications())) {
                String operate = fieldOperateMap.get("classifications");
                if (DBOperatorEnum.OR.operator().equals(operate)) {
                    criteria2 = Criteria.where("classifications").in(httpApiSearchDto.getClassifications());
                } else {
                    metabaseQuery.where("classifications", Predicate.ALL, httpApiSearchDto.getClassifications());
                }
            }

            if (DataUtil.isNotEmpty(criteria1) && DataUtil.isEmpty(criteria2)) {
                criteria = criteria.orOperator(criteria1);
            } else if (DataUtil.isEmpty(criteria1) && DataUtil.isNotEmpty(criteria2)) {
                criteria = criteria.orOperator(criteria2);
            } else if (DataUtil.isNotEmpty(criteria1) && DataUtil.isNotEmpty(criteria2)) {
                criteria = criteria.orOperator(criteria1, criteria2);
            }

            metabaseQuery.getCriteria().add(criteria);
        }

        //所属部门
        if (DataUtil.isNotEmpty(httpApiSearchDto.getDepartment())) {
            metabaseQuery.where("departments.department", Predicate.IS, httpApiSearchDto.getDepartment());
        }

        if (DataUtil.isNotEmpty(httpApiSearchDto.getIsAssetAuthorization()) && httpApiSearchDto.getIsAssetAuthorization()) {

            if (DataUtil.isNotEmpty(httpApiSearchDto.getAppUriSet()) || DataUtil.isNotEmpty(httpApiSearchDto.getDepartmentSet())) {

                Criteria criteria = new Criteria();

                if (DataUtil.isNotEmpty(httpApiSearchDto.getAppUriSet())) {

                    Criteria criteria1 = Criteria.where("appUri").in(httpApiSearchDto.getAppUriSet());
                    criteria = criteria.orOperator(criteria1);
                }

                if (DataUtil.isNotEmpty(httpApiSearchDto.getDepartmentSet())) {

                    Criteria criteria2 = Criteria.where("departments.department").in(httpApiSearchDto.getDepartmentSet());
                    criteria = criteria.orOperator(criteria2);
                }

                metabaseQuery.getCriteria().add(criteria);
            } else {
                metabaseQuery.where("_id", Predicate.IS, "");
            }
        }
        if (httpApiSearchDto.getTotalVisitsGt() != null || httpApiSearchDto.getTotalVisitsLt() != null) {
            Criteria criteria = Criteria.where("apiStat.totalVisits");
            if (httpApiSearchDto.getTotalVisitsGt() != null) {
                criteria.gt(httpApiSearchDto.getTotalVisitsGt());
            }
            if (httpApiSearchDto.getTotalVisitsLt() != null) {
                criteria.lt(httpApiSearchDto.getTotalVisitsLt());
            }
            metabaseQuery.getCriteria().add(criteria);
        }
        if (!CollectionUtils.isEmpty(httpApiSearchDto.getShowFields())) {
            metabaseQuery.fields(httpApiSearchDto.getShowFields());
        }
        Criteria customPropertyCriteria = customPropertySearchAdapter.getMetabaseCriteria(httpApiSearchDto.getCustomProperties());
        if (customPropertyCriteria != null) {
            metabaseQuery.getCriteria().add(customPropertyCriteria);
        }
        queryAdapterRegistry.module(Module.API).query(httpApiSearchDto, metabaseQuery);
        return metabaseQuery;
    }

    @Override
    public org.springframework.data.mongodb.core.query.Criteria getCriteria(HttpApiSearchDto httpApiSearchDto) {

        org.springframework.data.mongodb.core.query.Criteria criteria = new org.springframework.data.mongodb.core.query.Criteria();

        try {
            specialQueryFieldConvert.specialQueryFieldConvert(httpApiSearchDto);
        } catch (Exception e) {
            log.error("specialQueryFieldConvert error:{}", e);
        }

        List<org.springframework.data.mongodb.core.query.Criteria> orCriteriaList = new ArrayList<>();
        List<org.springframework.data.mongodb.core.query.Criteria> andCriteriaList = new ArrayList<>();
        Map<String, String> fieldOperateMap = httpApiSearchDto.getFieldOperateMap();
        if (DataUtil.isNotEmpty(httpApiSearchDto)) {

            if (DataUtil.isNotEmpty(httpApiSearchDto.getApiSearch())) {
                String value = httpApiSearchDto.getApiSearch();
                /*org.springframework.data.mongodb.core.query.Criteria orCriteria = new org.springframework.data.mongodb.core.query.Criteria();
                org.springframework.data.mongodb.core.query.Criteria criteria1 = new org.springframework.data.mongodb.core.query.Criteria();
                org.springframework.data.mongodb.core.query.Criteria criteria2 = new org.springframework.data.mongodb.core.query.Criteria();
                criteria1.and("apiUrl").regex(Pattern.compile(DataUtil.regexStrEscape(String.valueOf(value)), Pattern.CASE_INSENSITIVE));
                criteria2.and("name").regex(Pattern.compile(DataUtil.regexStrEscape(String.valueOf(value)), Pattern.CASE_INSENSITIVE));
                andCriteriaList.add(orCriteria.orOperator(criteria1, criteria2));*/
                value = DataUtil.regexStrEscapeExceptStartCharacter(value);

                criteria.and("apiUrl").regex(value);
            }

            if (DataUtil.isNotEmpty(httpApiSearchDto.getId())) {
                criteria.and("_id").is(httpApiSearchDto.getId());
            }
            if (DataUtil.isNotEmpty(httpApiSearchDto.getIds())) {
                List<ObjectId> ids = new ArrayList<>(httpApiSearchDto.getIds().size());
                httpApiSearchDto.getIds().forEach(id -> ids.add(new ObjectId(id)));
                criteria.and("_id").in(ids);
            }

            if (DataUtil.isNotEmpty(httpApiSearchDto.getHost())) {
                String host = httpApiSearchDto.getHost();
                if (Boolean.TRUE.equals(httpApiSearchDto.getHostIgnoreCase())) {
                    Pattern pattern = Pattern.compile(host, Pattern.CASE_INSENSITIVE);
                    criteria.and("host").regex(pattern);
                } else {
                    criteria.and("host").regex(DataUtil.regexStrEscape(host));
                }
            }
            if (DataUtil.isNotEmpty(httpApiSearchDto.getHostRegex())) {
                Pattern pattern = Pattern.compile(httpApiSearchDto.getHostRegex());
                criteria.and("host").regex(pattern);
            }
            if (DataUtil.isNotEmpty(httpApiSearchDto.getAppName())) {
                criteria.and("appName").regex(httpApiSearchDto.getAppName());
            }

            if (DataUtil.isNotEmpty(httpApiSearchDto.getUrlNode()) && httpApiSearchDto.getUrlNode() != 0 && DataUtil.isNotEmpty(httpApiSearchDto.getUrlPathList())) {

                for (HttpApiSearchDto.UrlPaths urlPaths : httpApiSearchDto.getUrlPathList()) {

                    org.springframework.data.mongodb.core.query.Criteria where = org.springframework.data.mongodb.core.query.Criteria.where("node").is(urlPaths.getUrlNode());
                    where.and("path").is(urlPaths.getUrlPath());

                    org.springframework.data.mongodb.core.query.Criteria elemMatch = org.springframework.data.mongodb.core.query.Criteria.where("urlPaths").elemMatch(where);

                    andCriteriaList.add(elemMatch);
                }
            }

            /**
             * 风险名称ids
             */
            if (DataUtil.isNotEmpty(httpApiSearchDto.getRiskIds())) {
                String operate = fieldOperateMap.get("riskIds");
                boolean isAll = httpApiSearchDto.getRiskIds().get(0).equals("ALL");
                List<String> allRisk = isAll ? httpApiSearchDto.getRiskMap().keySet().stream().collect(Collectors.toList()) : new ArrayList<>();
                if (DBOperatorEnum.OR.operator().equals(operate)) {
                    criteria.and("apiStat.riskIds").in(isAll ? allRisk : httpApiSearchDto.getRiskIds());
                } else {
                    criteria.and("apiStat.riskIds").all(isAll ? allRisk : httpApiSearchDto.getRiskIds());
                }
            }
            /**
             * 弱点名称ids
             */
            if (DataUtil.isNotEmpty(httpApiSearchDto.getWeaknessIds())) {
                String operate = fieldOperateMap.get("weaknessIds");
                boolean isAll = httpApiSearchDto.getWeaknessIds().get(0).equals("ALL");
                List<String> allWeakness = isAll ? httpApiSearchDto.getWeaknessMap().keySet().stream().collect(Collectors.toList()) : new ArrayList<>();
                if (DBOperatorEnum.OR.operator().equals(operate)) {
                    criteria.and("apiStat.weaknessIds").in(isAll ? allWeakness : httpApiSearchDto.getWeaknessIds());
                } else {
                    criteria.and("apiStat.weaknessIds").all(isAll ? allWeakness : httpApiSearchDto.getWeaknessIds());
                }
            }

            /**
             * 流量来源
             */
            if (DataUtil.isNotEmpty(httpApiSearchDto.getFlowSource())) {
                criteria.and("flowSources").is(httpApiSearchDto.getFlowSource());
            }
            /**
             * 多节点id
             */
            if (DataUtil.isNotEmpty(httpApiSearchDto.getNodeId())) {
                criteria.and("nodes.nid").is(httpApiSearchDto.getNodeId());
            }
            /**
             * 备注
             */
            if (DataUtil.isNotEmpty(httpApiSearchDto.getRemark())) {
                criteria.and("remark").regex(httpApiSearchDto.getRemark());
            }
            if (DataUtil.isNotEmpty(httpApiSearchDto.getApiType())) {
                criteria.and("apiType").in(httpApiSearchDto.getApiType());
            }
            if (DataUtil.isNotEmpty(httpApiSearchDto.getApiFormats())) {
                criteria.and("apiFormats").in(httpApiSearchDto.getApiFormats());
            }

            if (DataUtil.isNotEmpty(httpApiSearchDto.getApiRiskLevel())) {
                criteria.and("apiRiskLevel").in(httpApiSearchDto.getApiRiskLevel());
            }

            if (DataUtil.isNotEmpty(httpApiSearchDto.getAppUri())) {
                criteria.and("appUri").is(httpApiSearchDto.getAppUri());
            }
            if (DataUtil.isNotEmpty(httpApiSearchDto.getDelFlag())) {
                criteria.and("delFlag").is(httpApiSearchDto.getDelFlag());
            }
            if (DataUtil.isNotEmpty(httpApiSearchDto.getApiUrl())) {
                criteria.and("apiUrl").regex(httpApiSearchDto.getApiUrl());
            }
            //请求数据标签
            if (DataUtil.isNotEmpty(httpApiSearchDto.getReqDataLabels())) {
                String operator = fieldOperateMap.get("reqDataLabels");
                boolean reqIsSingle = DataUtil.isNotEmpty(httpApiSearchDto.getReqIsSingle()) && Boolean.TRUE.equals(httpApiSearchDto.getReqIsSingle());
                boolean isAll = httpApiSearchDto.getReqDataLabels().get(0).equals("ALL");
                List<String> allDataLabels = isAll ? httpApiSearchDto.getDataLabelMap().keySet().stream().collect(Collectors.toList()) : new ArrayList<>();
                if (DBOperatorEnum.OR.operator().equals(operator)) {
                    if (reqIsSingle) {
                        criteria.and("samples.reqDataLabels").all(isAll ? allDataLabels : httpApiSearchDto.getReqDataLabels());
                    } else {
                        criteria.and("reqDataLabels").in(isAll ? allDataLabels : httpApiSearchDto.getReqDataLabels());
                    }
                } else {
                    if (reqIsSingle) {
                        criteria.and("samples.reqDataLabels").all(isAll ? allDataLabels : httpApiSearchDto.getReqDataLabels());
                    } else {
                        criteria.and("reqDataLabels").all(isAll ? allDataLabels : httpApiSearchDto.getReqDataLabels());
                    }
                }
            }
            //返回数据标签
            if (DataUtil.isNotEmpty(httpApiSearchDto.getRspDataLabels())) {
                String operator = fieldOperateMap.get("rspDataLabels");
                boolean rspIsSingle = DataUtil.isNotEmpty(httpApiSearchDto.getRspIsSingle()) && Boolean.TRUE.equals(httpApiSearchDto.getRspIsSingle());
                boolean isAll = httpApiSearchDto.getRspDataLabels().get(0).equals("ALL");
                List<String> allDataLabels = isAll ? httpApiSearchDto.getDataLabelMap().keySet().stream().collect(Collectors.toList()) : new ArrayList<>();
                if (DBOperatorEnum.OR.operator().equals(operator)) {
                    if (rspIsSingle) {
                        criteria.and("samples.rspDataLabels").all(isAll ? allDataLabels : httpApiSearchDto.getRspDataLabels());
                    } else {
                        criteria.and("rspDataLabels").in(isAll ? allDataLabels : httpApiSearchDto.getRspDataLabels());
                    }
                } else {
                    if (rspIsSingle) {
                        criteria.and("samples.rspDataLabels").all(isAll ? allDataLabels : httpApiSearchDto.getRspDataLabels());
                    } else {
                        criteria.and("rspDataLabels").all(isAll ? allDataLabels : httpApiSearchDto.getRspDataLabels());
                    }
                }
            }
            //请求方法
            if (DataUtil.isNotEmpty(httpApiSearchDto.getMethods())) {
                criteria.and("methods").in(httpApiSearchDto.getMethods());
            }
            if (DataUtil.isNotEmpty(httpApiSearchDto.getProvince())) {
                criteria.and("provinces").in(httpApiSearchDto.getProvince());
            }
            //访问域
            if (DataUtil.isNotEmpty(httpApiSearchDto.getVisitDomains())) {
                String operator = fieldOperateMap.get("visitDomains");
                if (DBOperatorEnum.OR.operator().equals(operator)) {
                    criteria.and("visitDomains").in(httpApiSearchDto.getVisitDomains());
                } else {
                    criteria.and("visitDomains").all(httpApiSearchDto.getVisitDomains());
                }
            }
            //部署域
            if (DataUtil.isNotEmpty(httpApiSearchDto.getDeployDomains())) {
                String operator = fieldOperateMap.get("deployDomains");
                if (DBOperatorEnum.OR.operator().equals(operator)) {
                    criteria.and("deployDomains").in(httpApiSearchDto.getDeployDomains());
                } else {
                    criteria.and("deployDomains").all(httpApiSearchDto.getDeployDomains());
                }
            }
            //请求类型
            if (DataUtil.isNotEmpty(httpApiSearchDto.getReqContentTypes())) {
                criteria.and("reqContentTypes").in(httpApiSearchDto.getReqContentTypes());
            }
            //返回类型
            if (DataUtil.isNotEmpty(httpApiSearchDto.getRspContentTypes())) {
                criteria.and("rspContentTypes").in(httpApiSearchDto.getRspContentTypes());
            }
            //访问终端
            if (DataUtil.isNotEmpty(httpApiSearchDto.getTerminals())) {
                String operator = fieldOperateMap.get("terminals");
                if (DBOperatorEnum.OR.operator().equals(operator)) {
                    criteria.and("terminals").in(httpApiSearchDto.getTerminals());
                } else {
                    criteria.and("terminals").all(httpApiSearchDto.getTerminals());
                }
            }
            //所属部门
            if (DataUtil.isNotEmpty(httpApiSearchDto.getDepartment())) {
                criteria.and("departments.department").is(httpApiSearchDto.getDepartment());
            }
            //等级
            if (DataUtil.isNotEmpty(httpApiSearchDto.getLevel()) && DataUtil.isEmpty(httpApiSearchDto.getIsSensitiveApi())) {
                criteria.and("level").in(httpApiSearchDto.getLevel());
            }
            //接口状态
            if (DataUtil.isNotEmpty(httpApiSearchDto.getState())) {
                criteria.and("state").is(httpApiSearchDto.getState());
            }
            //日志记录
            if (DataUtil.isNotEmpty(httpApiSearchDto.getRecommendFlag())) {
                criteria.and("recommendFlag").is(httpApiSearchDto.getRecommendFlag());
            }
            //敏感API
            if (DataUtil.isNotEmpty(httpApiSearchDto.getIsSensitiveApi()) && DataUtil.isEmpty(httpApiSearchDto.getLevel())) {
                if (httpApiSearchDto.getIsSensitiveApi()) {
                    criteria.and("level").in(HttpApiSearchDto.sensitiveLevelList);
                } else {
                    criteria.and("level").in(HttpApiSearchDto.noSensitiveLevelList);
                }
            }

            //敏感API 和 等级
            if (DataUtil.isNotEmpty(httpApiSearchDto.getLevel()) && DataUtil.isNotEmpty(httpApiSearchDto.getIsSensitiveApi())) {
                org.springframework.data.mongodb.core.query.Criteria criteria1 = new org.springframework.data.mongodb.core.query.Criteria();
                org.springframework.data.mongodb.core.query.Criteria criteria2 = new org.springframework.data.mongodb.core.query.Criteria();
                criteria1.and("level").is(httpApiSearchDto.getLevel());
                if (httpApiSearchDto.getIsSensitiveApi()) {
                    criteria2.and("level").in(HttpApiSearchDto.sensitiveLevelList);
                } else {
                    criteria2.and("level").in(HttpApiSearchDto.noSensitiveLevelList);
                }
                andCriteriaList.add(criteria1);
                andCriteriaList.add(criteria2);
            }

            if (DataUtil.isNotEmpty(httpApiSearchDto.getIsApiWeakness())) {
                if (httpApiSearchDto.getIsApiWeakness()) {
                    criteria.and("briefWeaknesses.count").gt(0);
                } else {
                    org.springframework.data.mongodb.core.query.Criteria orCriteria = new org.springframework.data.mongodb.core.query.Criteria();
                    org.springframework.data.mongodb.core.query.Criteria criteria1 = new org.springframework.data.mongodb.core.query.Criteria();
                    org.springframework.data.mongodb.core.query.Criteria criteria2 = new org.springframework.data.mongodb.core.query.Criteria();
                    criteria1.and("briefWeaknesses").is(null);
                    criteria2.and("briefWeaknesses.count").is(0);
                    andCriteriaList.add(orCriteria.orOperator(criteria1, criteria2));
                }
            }

            //发现时间
            if (DataUtil.isNotEmpty(httpApiSearchDto.getDiscoverTimeStart()) && DataUtil.isNotEmpty(httpApiSearchDto.getDiscoverTimeEnd())) {
                org.springframework.data.mongodb.core.query.Criteria criteria1 = new org.springframework.data.mongodb.core.query.Criteria();
                org.springframework.data.mongodb.core.query.Criteria criteria2 = new org.springframework.data.mongodb.core.query.Criteria();
                criteria1.and("discoverTime").gte(httpApiSearchDto.getDiscoverTimeStart());
                criteria2.and("discoverTime").lte(httpApiSearchDto.getDiscoverTimeEnd());
                andCriteriaList.add(criteria1);
                andCriteriaList.add(criteria2);
            }
            //活跃时间
            if (DataUtil.isNotEmpty(httpApiSearchDto.getActiveTimeStart()) && DataUtil.isNotEmpty(httpApiSearchDto.getActiveTimeEnd())) {
                org.springframework.data.mongodb.core.query.Criteria criteria1 = new org.springframework.data.mongodb.core.query.Criteria();
                org.springframework.data.mongodb.core.query.Criteria criteria2 = new org.springframework.data.mongodb.core.query.Criteria();
                criteria1.and("activeTime").gte(httpApiSearchDto.getActiveTimeStart());
                criteria2.and("activeTime").lte(httpApiSearchDto.getActiveTimeEnd());
                andCriteriaList.add(criteria1);
                andCriteriaList.add(criteria2);
            }
            /**
             * api生命状态
             */
            if (DataUtil.isNotEmpty(httpApiSearchDto.getApiLifeFlag())) {
                Short lifeFlag = httpApiSearchDto.getApiLifeFlag().get(0);
                AssetLifeStateEnum assetLifeStateEnum = AssetLifeStateEnum.valueOfNum(lifeFlag);
                AssetLifeStateConfigConvert.CriteriaTag criteriaTag = assetLifeStateConfigConvert.getCriteria(AssetLifeStateConfig.AssetTypeEnum.API, assetLifeStateEnum);
                andCriteriaList.add(criteriaTag.getSpringCriteria());
            }

            if (DataUtil.isNotEmpty(httpApiSearchDto.getFeatureLabels()) || DataUtil.isNotEmpty(httpApiSearchDto.getClassifications())) {

                org.springframework.data.mongodb.core.query.Criteria orCriteria = new org.springframework.data.mongodb.core.query.Criteria();
                org.springframework.data.mongodb.core.query.Criteria criteria1 = null;
                org.springframework.data.mongodb.core.query.Criteria criteria2 = null;

                if (DataUtil.isNotEmpty(httpApiSearchDto.getFeatureLabels())) {
                    String operator = fieldOperateMap.get("featureLabels");
                    if (DBOperatorEnum.OR.operator().equals(operator)) {
                        criteria1 = new org.springframework.data.mongodb.core.query.Criteria();
                        criteria1.and("featureLabels").in(httpApiSearchDto.getFeatureLabels());
                    } else {
                        criteria.and("featureLabels").all(httpApiSearchDto.getFeatureLabels());
                    }
                }

                if (DataUtil.isNotEmpty(httpApiSearchDto.getClassifications())) {
                    String operator = fieldOperateMap.get("classifications");
                    if (DBOperatorEnum.OR.operator().equals(operator)) {
                        criteria2 = new org.springframework.data.mongodb.core.query.Criteria();
                        criteria2.and("classifications").in(httpApiSearchDto.getClassifications());
                    } else {
                        criteria.and("classifications").all(httpApiSearchDto.getClassifications());
                    }
                }

                if (DataUtil.isNotEmpty(criteria1) && DataUtil.isEmpty(criteria2)) {
                    andCriteriaList.add(orCriteria.orOperator(criteria1));
                } else if (DataUtil.isEmpty(criteria1) && DataUtil.isNotEmpty(criteria2)) {
                    andCriteriaList.add(orCriteria.orOperator(criteria2));
                } else if (DataUtil.isNotEmpty(criteria1) && DataUtil.isNotEmpty(criteria2)) {
                    andCriteriaList.add(orCriteria.orOperator(criteria1, criteria2));
                }
            }

            if (DataUtil.isNotEmpty(httpApiSearchDto.getIsAssetAuthorization()) && httpApiSearchDto.getIsAssetAuthorization()) {

                if (DataUtil.isNotEmpty(httpApiSearchDto.getAppUriSet()) || DataUtil.isNotEmpty(httpApiSearchDto.getDepartmentSet())) {

                    org.springframework.data.mongodb.core.query.Criteria orCriteria = new org.springframework.data.mongodb.core.query.Criteria();
                    org.springframework.data.mongodb.core.query.Criteria criteria1 = null;
                    org.springframework.data.mongodb.core.query.Criteria criteria2 = null;

                    if (DataUtil.isNotEmpty(httpApiSearchDto.getAppUriSet())) {

                        criteria1 = new org.springframework.data.mongodb.core.query.Criteria();
                        criteria1.and("appUri").in(httpApiSearchDto.getAppUriSet());
                    }

                    if (DataUtil.isNotEmpty(httpApiSearchDto.getDepartmentSet())) {

                        criteria2 = new org.springframework.data.mongodb.core.query.Criteria();
                        criteria2.and("departments.department").in(httpApiSearchDto.getDepartmentSet());
                    }

                    if (DataUtil.isNotEmpty(criteria1) && DataUtil.isEmpty(criteria2)) {
                        andCriteriaList.add(orCriteria.orOperator(criteria1));
                    } else if (DataUtil.isEmpty(criteria1) && DataUtil.isNotEmpty(criteria2)) {
                        andCriteriaList.add(orCriteria.orOperator(criteria2));
                    } else if (DataUtil.isNotEmpty(criteria1) && DataUtil.isNotEmpty(criteria2)) {
                        andCriteriaList.add(orCriteria.orOperator(criteria1, criteria2));
                    }
                } else {
                    criteria.and("_id").is("");
                }
            }

            if (orCriteriaList.size() > 0) {
                criteria.orOperator(orCriteriaList.toArray(new org.springframework.data.mongodb.core.query.Criteria[orCriteriaList.size()]));
            }
        }
        org.springframework.data.mongodb.core.query.Criteria mongoCriteria = customPropertySearchAdapter.getMongoCriteria(httpApiSearchDto.getCustomProperties());
        if (mongoCriteria != null) {
            andCriteriaList.add(mongoCriteria);
        }
        if (andCriteriaList.size() > 0) {
            criteria.andOperator(andCriteriaList.toArray(new org.springframework.data.mongodb.core.query.Criteria[andCriteriaList.size()]));
        }
        queryAdapterRegistry.module(Module.API).query(httpApiSearchDto, criteria);
        return criteria;
    }

    @Override
    public long getApiInfoCount(List<String> classifications, List<String> featureLabels, List<String> labels) {

        MetabaseQuery query = new MetabaseQuery();
        query.where("delFlag", Predicate.IS, false);

        if (DataUtil.isNotEmpty(classifications)) {
            query.where("classifications", Predicate.IN, classifications);
        }

        if (DataUtil.isNotEmpty(featureLabels)) {
            query.where("featureLabels", Predicate.IN, featureLabels);
        }

        if (DataUtil.isNotEmpty(labels)) {
            query.where("dataLabels", Predicate.IN, labels);
        }

        return getCount(query);
    }

    @Override
    public List<AggregationResult> getHttpApiVisits(MetabaseQuery query, MetabaseGroupOperation metabaseGroupOperation, Class<?> clz) {
        return aggregate(query, metabaseGroupOperation, clz);
    }

    @Override
    public List<HttpApiResource> selectHttpApiByAppUri(String appUri, List<String> fields) throws Exception {

        return selectHttpApiByAppUri(appUri, fields, 0);
    }

    @Override
    public List<HttpApiResource> selectHttpApiByAppUri(String appUri, List<String> fields, int limit) throws Exception {
        MetabaseQuery metabaseQuery = new MetabaseQuery();
        //查询条件
        metabaseQuery.where("appUri", Predicate.IS, appUri).fields(fields).alive();
        if (limit > 0) {
            metabaseQuery.limit(limit);
        }
        return metabaseClientTemplate.find(metabaseQuery, HttpApiResource.class);
    }

    @Override
    public List<HttpApiResource> selectHttpApiByHost(String host, List<String> fields) throws Exception {

        MetabaseQuery metabaseQuery = new MetabaseQuery();
        //查询条件
        metabaseQuery.where("host", Predicate.IS, host).fields(fields);

        return metabaseClientTemplate.find(metabaseQuery, HttpApiResource.class);
    }

    @Override
    public HttpApiResource selectHttpApiByUri(String uri) throws Exception {
        Query queryDB = new Query();
        queryDB.addCriteria(org.springframework.data.mongodb.core.query.Criteria.where("delFlag").is(false));
        queryDB.addCriteria(org.springframework.data.mongodb.core.query.Criteria.where("uri").is(uri));

        return mongoTemplate.findOne(queryDB, HttpApiResource.class, collectionName);
    }

    @Override
    public HttpApiResource selectHttpApiByUri(String uri, List<String> fields) throws Exception {
        Query queryDB = new Query();
        queryDB.addCriteria(org.springframework.data.mongodb.core.query.Criteria.where("delFlag").is(false));
        queryDB.addCriteria(org.springframework.data.mongodb.core.query.Criteria.where("uri").is(uri));
        if (fields != null) {
            for (String field : fields) {
                queryDB.fields().include(field);
            }
        }
        return mongoTemplate.findOne(queryDB, HttpApiResource.class, collectionName);
    }

    @Override
    public HttpApiResource selectHttpApiByApiUrl(String apiUrl) {
        Query queryDB = new Query();
        queryDB.addCriteria(org.springframework.data.mongodb.core.query.Criteria.where("delFlag").is(false));
        queryDB.addCriteria(org.springframework.data.mongodb.core.query.Criteria.where("apiUrl").is(apiUrl));

        return mongoTemplate.findOne(queryDB, HttpApiResource.class, collectionName);
    }

    @Override
    public List<HttpApiResource> getHttpNewApis(Integer page, Integer limit, String field, Integer sort, Long startTime, Long endTime) {
        MetabaseQuery query = new MetabaseQuery();
        //查询条件
        query.where("delFlag", Predicate.IS, false);
        //分页
        query.setSkip((page - 1) * limit);
        query.limit(limit);
        if (DataUtil.isNotEmpty(startTime) && DataUtil.isNotEmpty(endTime)) {
            Criteria criteria = new Criteria();
            criteria = criteria.andOperator(Criteria.where("discoverTime").gte(startTime), Criteria.where("discoverTime").lte(endTime));
            query.getCriteria().add(criteria);
        }
        //排序
        if (DataUtil.isNotEmpty(field) && DataUtil.isNotEmpty(sort)) {

            if (sort == ConstantUtil.Sort.ASC) {
                query.sort(Sort.by(field, SortOrder.ASC));
            } else if (sort == ConstantUtil.Sort.DESC) {
                query.sort(Sort.by(field, SortOrder.DESC));
            } else {
                query.sort(Sort.by("discoverTime", SortOrder.DESC));
            }
        } else {
            query.sort(Sort.by("discoverTime", SortOrder.DESC));
        }
        return metabaseClientTemplate.find(query, HttpApiResource.class);
    }

    @Override
    public List<HttpApiResource> getHttpActiveApis(Integer page, Integer limit, String field, Integer sort, Long startTime, Long endTime) {
        MetabaseQuery query = new MetabaseQuery();
        //查询条件
        query.where("delFlag", Predicate.IS, false);
        //分页
        query.setSkip((page - 1) * limit);
        query.limit(limit);
        if (DataUtil.isNotEmpty(startTime) && DataUtil.isNotEmpty(endTime)) {
            Criteria criteria = new Criteria();
            criteria = criteria.andOperator(Criteria.where("activeTime").gte(startTime), Criteria.where("activeTime").lte(endTime));
            query.getCriteria().add(criteria);
        }
        //排序
        if (DataUtil.isNotEmpty(field) && DataUtil.isNotEmpty(sort)) {

            if (sort == ConstantUtil.Sort.ASC) {
                query.sort(Sort.by(field, SortOrder.ASC));
            } else if (sort == ConstantUtil.Sort.DESC) {
                query.sort(Sort.by(field, SortOrder.DESC));
            } else {
                query.sort(Sort.by("discoverTime", SortOrder.DESC));
            }
        } else {
            query.sort(Sort.by("discoverTime", SortOrder.DESC));
        }
        return metabaseClientTemplate.find(query, HttpApiResource.class);
    }

    @Override
    public List<HttpApiDateTotalStat> getActiveApiCount(Long startTime, Long endTime) {
        MetabaseQuery metabaseQuery = new MetabaseQuery();
        metabaseQuery.where("type", Predicate.IS, HttpApiDateTotalStat.Type.ALL);
        if (DataUtil.isNotEmpty(startTime) && DataUtil.isNotEmpty(endTime)) {
            Criteria criteria = new Criteria();
            criteria = criteria.andOperator(Criteria.where("date").gte(startTime), Criteria.where("date").lte(endTime));
            metabaseQuery.getCriteria().add(criteria);
        }
        return metabaseClientTemplate.find(metabaseQuery, HttpApiDateTotalStat.class);
    }

    @Override
    public List<HttpApiDateTotalStat> getActiveSensitiveApiCount(Long startTime, Long endTime) {
        MetabaseQuery metabaseQuery = new MetabaseQuery();
        metabaseQuery.where("type", Predicate.IS, HttpApiDateTotalStat.Type.RECOMMEND);
        if (DataUtil.isNotEmpty(startTime) && DataUtil.isNotEmpty(endTime)) {
            Criteria criteria = new Criteria();
            criteria = criteria.andOperator(Criteria.where("date").gte(startTime), Criteria.where("date").lte(endTime));
            metabaseQuery.getCriteria().add(criteria);
        }
        return metabaseClientTemplate.find(metabaseQuery, HttpApiDateTotalStat.class);
    }

    @Override
    public void saveAccParseConfig(ApiAccParseCriteriaDto apiAccParseCriteriaDto, int type) throws Exception {
        HttpApiResource httpApiResource1 = null;
        if (DataUtil.isNotEmpty(apiAccParseCriteriaDto)) {
            if (DataUtil.isNotEmpty(apiAccParseCriteriaDto.getUri())) {
                httpApiResource1 = this.selectHttpApiByUri(apiAccParseCriteriaDto.getUri());
            }

            if (httpApiResource1 == null) {
                return;
            }

            this.createCurParseConfig(apiAccParseCriteriaDto, type, httpApiResource1);

            metabaseClientTemplate.save(httpApiResource1);
        }
    }

    // 组装当前配置
    private void createCurParseConfig(ApiAccParseCriteriaDto apiAccParseCriteriaDto, int type, HttpApiResource httpApiResource1) {
        //追加并去重
        if (type == 1) {
            //账号配置
            Set<HttpApiResource.AccParseConfigs> userParseConfigsSet = new HashSet<>();
            if (DataUtil.isNotEmpty(apiAccParseCriteriaDto.getUserParseConfigs())) {
                for (HttpApiResource.AccParseConfigs userParseConfigs : apiAccParseCriteriaDto.getUserParseConfigs()) {
                    userParseConfigsSet.add(userParseConfigs);
                }
            }
            if (DataUtil.isNotEmpty(httpApiResource1.getUserParseConfigs())) {
                for (HttpApiResource.AccParseConfigs userParseConfigs1 : httpApiResource1.getUserParseConfigs()) {
                    userParseConfigsSet.add(userParseConfigs1);
                }
            }
            List<HttpApiResource.AccParseConfigs> userParseConfigsList = new ArrayList<>(userParseConfigsSet);
            httpApiResource1.setUserParseConfigs(userParseConfigsList);

            //session配置
            Set<HttpApiResource.AccParseConfigs> sessionParseConfigsSet = new HashSet<>();
            if (DataUtil.isNotEmpty(apiAccParseCriteriaDto.getSessionParseConfigs())) {
                for (HttpApiResource.AccParseConfigs sessionParseConfigs : apiAccParseCriteriaDto.getSessionParseConfigs()) {
                    sessionParseConfigsSet.add(sessionParseConfigs);
                }
            }
            if (DataUtil.isNotEmpty(httpApiResource1.getSessionParseConfigs())) {
                for (HttpApiResource.AccParseConfigs sessionParseConfigs : httpApiResource1.getSessionParseConfigs()) {
                    sessionParseConfigsSet.add(sessionParseConfigs);
                }
            }
            List<HttpApiResource.AccParseConfigs> sessionParseConfigsList = new ArrayList<>(sessionParseConfigsSet);
            httpApiResource1.setSessionParseConfigs(sessionParseConfigsList);

            //密码配置
            Set<HttpApiResource.AccParseConfigs> passwordParseConfigsSet = new HashSet<>();
            if (DataUtil.isNotEmpty(apiAccParseCriteriaDto.getPasswordParseConfigs())) {
                for (HttpApiResource.AccParseConfigs passwordParseConfigs : apiAccParseCriteriaDto.getPasswordParseConfigs()) {
                    passwordParseConfigsSet.add(passwordParseConfigs);
                }
            }
            if (DataUtil.isNotEmpty(httpApiResource1.getPasswordParseConfigs())) {
                for (HttpApiResource.AccParseConfigs passwordParseConfigs : httpApiResource1.getPasswordParseConfigs()) {
                    passwordParseConfigsSet.add(passwordParseConfigs);
                }
            }
            List<HttpApiResource.AccParseConfigs> passwordParseConfigsList = new ArrayList<>(passwordParseConfigsSet);
            httpApiResource1.setPasswordParseConfigs(passwordParseConfigsList);

            //登录凭证配置
            Set<HttpApiResource.AccParseConfigs> loginSuccessConfigsHashSet = new HashSet<>();
            if (DataUtil.isNotEmpty(apiAccParseCriteriaDto.getLoginSuccessConfigs())) {
                for (HttpApiResource.AccParseConfigs loginSuccessConfigs : apiAccParseCriteriaDto.getLoginSuccessConfigs()) {
                    loginSuccessConfigsHashSet.add(loginSuccessConfigs);
                }
            }
            if (DataUtil.isNotEmpty(httpApiResource1.getLoginSuccessConfigs())) {
                for (HttpApiResource.AccParseConfigs loginSuccessConfigs : httpApiResource1.getLoginSuccessConfigs()) {
                    loginSuccessConfigsHashSet.add(loginSuccessConfigs);
                }
            }
            List<HttpApiResource.AccParseConfigs> loginSuccessConfigsList = new ArrayList<>(loginSuccessConfigsHashSet);
            httpApiResource1.setLoginSuccessConfigs(loginSuccessConfigsList);

            //组织架构配置
            //部门
            Set<HttpApiResource.AccParseConfigs> staffDepartParseConfigsHashSet = new HashSet<>();
            if (DataUtil.isNotEmpty(apiAccParseCriteriaDto.getStaffDepartParseConfigs())) {
                for (HttpApiResource.AccParseConfigs staffDepartParseConfigs : apiAccParseCriteriaDto.getStaffDepartParseConfigs()) {
                    staffDepartParseConfigsHashSet.add(staffDepartParseConfigs);
                }
            }
            if (DataUtil.isNotEmpty(httpApiResource1.getStaffDepartParseConfigs())) {
                for (HttpApiResource.AccParseConfigs staffDepartParseConfigs : httpApiResource1.getStaffDepartParseConfigs()) {
                    staffDepartParseConfigsHashSet.add(staffDepartParseConfigs);
                }
            }
            List<HttpApiResource.AccParseConfigs> staffDepartParseConfigsList = new ArrayList<>(staffDepartParseConfigsHashSet);
            httpApiResource1.setStaffDepartParseConfigs(staffDepartParseConfigsList);

            //姓名
            Set<HttpApiResource.AccParseConfigs> staffNameParseConfigsHashSet = new HashSet<>();
            if (DataUtil.isNotEmpty(apiAccParseCriteriaDto.getStaffNameParseConfigs())) {
                for (HttpApiResource.AccParseConfigs staffNameParseConfigs : apiAccParseCriteriaDto.getStaffNameParseConfigs()) {
                    staffNameParseConfigsHashSet.add(staffNameParseConfigs);
                }
            }
            if (DataUtil.isNotEmpty(httpApiResource1.getStaffNameParseConfigs())) {
                for (HttpApiResource.AccParseConfigs staffNameParseConfigs : httpApiResource1.getStaffNameParseConfigs()) {
                    staffNameParseConfigsHashSet.add(staffNameParseConfigs);
                }
            }
            List<HttpApiResource.AccParseConfigs> staffNameParseConfigsList = new ArrayList<>(staffNameParseConfigsHashSet);
            httpApiResource1.setStaffNameParseConfigs(staffNameParseConfigsList);

            //
            Set<HttpApiResource.AccParseConfigs> staffChineseParseConfigsHashSet = new HashSet<>();
            if (DataUtil.isNotEmpty(apiAccParseCriteriaDto.getStaffChineseParseConfigs())) {
                for (HttpApiResource.AccParseConfigs staffChineseParseConfigs : apiAccParseCriteriaDto.getStaffChineseParseConfigs()) {
                    staffChineseParseConfigsHashSet.add(staffChineseParseConfigs);
                }
            }
            if (DataUtil.isNotEmpty(httpApiResource1.getStaffChineseParseConfigs())) {
                for (HttpApiResource.AccParseConfigs staffChineseParseConfigs : httpApiResource1.getStaffChineseParseConfigs()) {
                    staffChineseParseConfigsHashSet.add(staffChineseParseConfigs);
                }
            }
            List<HttpApiResource.AccParseConfigs> staffChineseParseConfigsList = new ArrayList<>(staffChineseParseConfigsHashSet);
            httpApiResource1.setStaffChineseParseConfigs(staffChineseParseConfigsList);

            //身份证
            Set<HttpApiResource.AccParseConfigs> staffIdCardParseConfigsHashSet = new HashSet<>();
            if (DataUtil.isNotEmpty(apiAccParseCriteriaDto.getStaffIdCardParseConfigs())) {
                for (HttpApiResource.AccParseConfigs staffIdCardParseConfigs : apiAccParseCriteriaDto.getStaffIdCardParseConfigs()) {
                    staffIdCardParseConfigsHashSet.add(staffIdCardParseConfigs);
                }
            }
            if (DataUtil.isNotEmpty(httpApiResource1.getStaffChineseParseConfigs())) {
                for (HttpApiResource.AccParseConfigs staffIdCardParseConfigs : httpApiResource1.getStaffIdCardParseConfigs()) {
                    staffIdCardParseConfigsHashSet.add(staffIdCardParseConfigs);
                }
            }
            List<HttpApiResource.AccParseConfigs> staffIdCardParseConfigsList = new ArrayList<>(staffIdCardParseConfigsHashSet);
            httpApiResource1.setStaffIdCardParseConfigs(staffIdCardParseConfigsList);

            //银行卡
            Set<HttpApiResource.AccParseConfigs> staffBankCardParseConfigsHashSet = new HashSet<>();
            if (DataUtil.isNotEmpty(apiAccParseCriteriaDto.getStaffBankCardParseConfigs())) {
                for (HttpApiResource.AccParseConfigs staffBankCardParseConfigs : apiAccParseCriteriaDto.getStaffBankCardParseConfigs()) {
                    staffBankCardParseConfigsHashSet.add(staffBankCardParseConfigs);
                }
            }
            if (DataUtil.isNotEmpty(httpApiResource1.getStaffChineseParseConfigs())) {
                for (HttpApiResource.AccParseConfigs staffBankCardParseConfigs : httpApiResource1.getStaffBankCardParseConfigs()) {
                    staffBankCardParseConfigsHashSet.add(staffBankCardParseConfigs);
                }
            }
            List<HttpApiResource.AccParseConfigs> staffBankCardParseConfigsList = new ArrayList<>(staffBankCardParseConfigsHashSet);
            httpApiResource1.setStaffBankCardParseConfigs(staffBankCardParseConfigsList);

            //邮箱
            Set<HttpApiResource.AccParseConfigs> staffEmailParseConfigsHashSet = new HashSet<>();
            if (DataUtil.isNotEmpty(apiAccParseCriteriaDto.getStaffEmailParseConfigs())) {
                for (HttpApiResource.AccParseConfigs staffEmailParseConfigs : apiAccParseCriteriaDto.getStaffEmailParseConfigs()) {
                    staffEmailParseConfigsHashSet.add(staffEmailParseConfigs);
                }
            }
            if (DataUtil.isNotEmpty(httpApiResource1.getStaffChineseParseConfigs())) {
                for (HttpApiResource.AccParseConfigs staffEmailParseConfigs : httpApiResource1.getStaffEmailParseConfigs()) {
                    staffEmailParseConfigsHashSet.add(staffEmailParseConfigs);
                }
            }
            List<HttpApiResource.AccParseConfigs> staffEmailParseConfigsList = new ArrayList<>(staffEmailParseConfigsHashSet);
            httpApiResource1.setStaffEmailParseConfigs(staffEmailParseConfigsList);

            //员工id
            Set<HttpApiResource.AccParseConfigs> staffIdParseConfigsHashSet = new HashSet<>();
            if (DataUtil.isNotEmpty(apiAccParseCriteriaDto.getStaffIdParseConfigs())) {
                for (HttpApiResource.AccParseConfigs staffIdParseConfigs : apiAccParseCriteriaDto.getStaffIdParseConfigs()) {
                    staffIdParseConfigsHashSet.add(staffIdParseConfigs);
                }
            }
            if (DataUtil.isNotEmpty(httpApiResource1.getStaffChineseParseConfigs())) {
                for (HttpApiResource.AccParseConfigs staffIdParseConfigs : httpApiResource1.getStaffIdParseConfigs()) {
                    staffIdParseConfigsHashSet.add(staffIdParseConfigs);
                }
            }
            List<HttpApiResource.AccParseConfigs> staffIdParseConfigsList = new ArrayList<>(staffIdParseConfigsHashSet);
            httpApiResource1.setStaffIdParseConfigs(staffIdParseConfigsList);

            if (httpApiResource1.getIsSso() == null || !httpApiResource1.getIsSso()) {
                httpApiResource1.setIsSso(apiAccParseCriteriaDto.getIsSso());
            }

            if (httpApiResource1.getIsIpAssociatedEnable() == null || !httpApiResource1.getIsIpAssociatedEnable()) {
                httpApiResource1.setIsIpAssociatedEnable(apiAccParseCriteriaDto.getResolveIpFlag());
            }
        }
        //覆盖
        else {
            if (apiAccParseCriteriaDto.getParseType() == ApiAccParseCriteriaDto.ParseType.ACCOUNT) {
                if (DataUtil.isNotEmpty(apiAccParseCriteriaDto.getUserParseConfigs())) {
                    httpApiResource1.setUserParseConfigs(apiAccParseCriteriaDto.getUserParseConfigs());
                } else {
                    httpApiResource1.setUserParseConfigs(new ArrayList<>());
                }
                if (DataUtil.isNotEmpty(apiAccParseCriteriaDto.getLoginSuccessConfigs())) {
                    httpApiResource1.setLoginSuccessConfigs(apiAccParseCriteriaDto.getLoginSuccessConfigs());
                } else {
                    httpApiResource1.setLoginSuccessConfigs(new ArrayList<>());
                }

                if (DataUtil.isNotEmpty(apiAccParseCriteriaDto.getPasswordParseConfigs())) {
                    httpApiResource1.setPasswordParseConfigs(apiAccParseCriteriaDto.getPasswordParseConfigs());
                } else {
                    httpApiResource1.setPasswordParseConfigs(new ArrayList<>());
                }

                if (DataUtil.isNotEmpty(apiAccParseCriteriaDto.getSessionParseConfigs())) {
                    httpApiResource1.setSessionParseConfigs(apiAccParseCriteriaDto.getSessionParseConfigs());
                } else {
                    httpApiResource1.setSessionParseConfigs(new ArrayList<>());
                }
            }

            if (DataUtil.isNotEmpty(apiAccParseCriteriaDto.getStaffDepartParseConfigs())) {
                httpApiResource1.setStaffDepartParseConfigs(apiAccParseCriteriaDto.getStaffDepartParseConfigs());
            } else {
                httpApiResource1.setStaffDepartParseConfigs(new ArrayList<>());
            }

            if (DataUtil.isNotEmpty(apiAccParseCriteriaDto.getStaffNameParseConfigs())) {
                httpApiResource1.setStaffNameParseConfigs(apiAccParseCriteriaDto.getStaffNameParseConfigs());
            } else {
                httpApiResource1.setStaffNameParseConfigs(new ArrayList<>());
            }

            if (DataUtil.isNotEmpty(apiAccParseCriteriaDto.getStaffIdParseConfigs())) {
                httpApiResource1.setStaffIdParseConfigs(apiAccParseCriteriaDto.getStaffIdParseConfigs());
            } else {
                httpApiResource1.setStaffIdParseConfigs(new ArrayList<>());
            }

            if (DataUtil.isNotEmpty(apiAccParseCriteriaDto.getStaffChineseParseConfigs())) {
                httpApiResource1.setStaffChineseParseConfigs(apiAccParseCriteriaDto.getStaffChineseParseConfigs());
            } else {
                httpApiResource1.setStaffChineseParseConfigs(new ArrayList<>());
            }

            if (DataUtil.isNotEmpty(apiAccParseCriteriaDto.getStaffEmailParseConfigs())) {
                httpApiResource1.setStaffEmailParseConfigs(apiAccParseCriteriaDto.getStaffEmailParseConfigs());
            } else {
                httpApiResource1.setStaffEmailParseConfigs(new ArrayList<>());
            }

            if (DataUtil.isNotEmpty(apiAccParseCriteriaDto.getStaffIdCardParseConfigs())) {
                httpApiResource1.setStaffIdCardParseConfigs(apiAccParseCriteriaDto.getStaffIdCardParseConfigs());
            } else {
                httpApiResource1.setStaffIdCardParseConfigs(new ArrayList<>());
            }

            if (DataUtil.isNotEmpty(apiAccParseCriteriaDto.getStaffBankCardParseConfigs())) {
                httpApiResource1.setStaffBankCardParseConfigs(apiAccParseCriteriaDto.getStaffBankCardParseConfigs());
            } else {
                httpApiResource1.setStaffBankCardParseConfigs(new ArrayList<>());
            }

            if (DataUtil.isNotEmpty(apiAccParseCriteriaDto.getStaffRoleParseConfigs())) {
                httpApiResource1.setStaffRoleParseConfigs(apiAccParseCriteriaDto.getStaffRoleParseConfigs());
            } else {
                httpApiResource1.setStaffRoleParseConfigs(new ArrayList<>());
            }

            if (DataUtil.isNotEmpty(apiAccParseCriteriaDto.getStaffNickNameParseConfigs())) {
                httpApiResource1.setStaffNickNameParseConfigs(apiAccParseCriteriaDto.getStaffNickNameParseConfigs());
            } else {
                httpApiResource1.setStaffNickNameParseConfigs(new ArrayList<>());
            }

            if (DataUtil.isNotEmpty(apiAccParseCriteriaDto.getStaffMobileParseConfigs())) {
                httpApiResource1.setStaffMobileParseConfigs(apiAccParseCriteriaDto.getStaffMobileParseConfigs());
            } else {
                httpApiResource1.setStaffMobileParseConfigs(new ArrayList<>());
            }
        }
        if (apiAccParseCriteriaDto.getParseType() == ApiAccParseCriteriaDto.ParseType.ACCOUNT) {
            if (DataUtil.isNotEmpty(apiAccParseCriteriaDto.getAccParseEnable())) {
                httpApiResource1.setAccParseEnable(apiAccParseCriteriaDto.getAccParseEnable());
            } else {
                httpApiResource1.setAccParseEnable(true);
            }

            if (DataUtil.isNotEmpty(apiAccParseCriteriaDto.getIsSso())) {
                httpApiResource1.setIsSso(apiAccParseCriteriaDto.getIsSso());
            }
        }
        if (httpApiResource1.getTags() == null) {
            httpApiResource1.setTags(new ArrayList<>());
        }
        if (apiAccParseCriteriaDto.getTags() != null) {
            httpApiResource1.getTags().addAll(apiAccParseCriteriaDto.getTags());
        }
        httpApiResource1.setTags(httpApiResource1.getTags().stream().distinct().collect(Collectors.toList()));

        if (DataUtil.isNotEmpty(apiAccParseCriteriaDto.getStaffInfoParseEnable())) {
            httpApiResource1.setStaffInfoParseEnable(apiAccParseCriteriaDto.getStaffInfoParseEnable());
        } else {
            httpApiResource1.setStaffInfoParseEnable(true);
        }
        if (apiAccParseCriteriaDto.getParseType() == ApiAccParseCriteriaDto.ParseType.ACCOUNT) {
            // 保存账号解析配置关系
            this.saveAccParseRelation(httpApiResource1, apiAccParseCriteriaDto);
        }
    }

    @Override
    public void createApiParse(ApiAccParseCriteriaDto apiAccParseCriteriaDto, HttpApiResource httpApiResource, int parseType) {
        this.createCurParseConfig(apiAccParseCriteriaDto, parseType, httpApiResource);
    }

    @Override
    public String saveHttpApiList(List<HttpApiResource> httpApiResourceList) {
        try {
//            for (HttpApiResource httpApiResource : httpApiResourceList) {
//                mongoTemplate.save(httpApiResource, collectionName);
//            }

            metabaseClientTemplate.saveAll(httpApiResourceList);

            return "success";
        } catch (Exception e) {
            log.error("");
            return "fail";
        }
    }

    @Override
    public void initRiskNum() {
        Update update = new Update();
        update.set("briefRiskInfo.count", 0);
        update.set("briefRiskInfo.highLevelNum", 0);
        update.set("briefRiskInfo.midLevelNum", 0);
        update.set("briefRiskInfo.lowLevelNum", 0);
        mongoTemplate.updateMulti(new Query(), update, collectionName);
    }

    @Override
    public void batchUpdateByIds(List<String> uris, int type) {
        MetabaseQuery metabaseQuery = new MetabaseQuery();
        if (DataUtil.isNotEmpty(uris)) {
            metabaseQuery.where("uri", Predicate.IN, uris);
        }
        ResourceUpdates updates = new ResourceUpdates();
        if (type == 1) {
            updates.set("accParseEnable", true);
        } else {
            updates.set("accParseEnable", false);
        }
        List<BatchUpdates> batchUpdates = new ArrayList<>();
        BatchUpdates batchUpdate = new BatchUpdates();
        batchUpdate.setQuery(metabaseQuery);
        batchUpdate.setUpdates(updates);
        batchUpdates.add(batchUpdate);
        metabaseClientTemplate.update(batchUpdates, true, HttpApiResource.class);
    }

    @Override
    public void batchUpdateByIds(List<String> uris, int type, String target) {
        try {
            if (DataUtil.isEmpty(uris)) {
                throw new ServiceException("uris参数缺失");
            }

            List<BatchUpdates> batchUpdates = new ArrayList<>();

            for (String uri : uris) {
                MetabaseQuery metabaseQuery = new MetabaseQuery();
                if (DataUtil.isNotEmpty(uris)) {
                    metabaseQuery.where("uri", Predicate.IS, uri);
                }

                ResourceUpdates resourceUpdates = getResourceUpdates(type, target);

                BatchUpdates batchUpdate = new BatchUpdates();
                batchUpdate.setQuery(metabaseQuery);
                batchUpdate.setUpdates(resourceUpdates);

                batchUpdates.add(batchUpdate);
            }

            metabaseClientTemplate.update(batchUpdates, false, HttpApiResource.class);

//            List<org.springframework.data.util.Pair<Query, Update>> updateList = new ArrayList<>();
//            uris.forEach(uri -> {
//                HttpApiResource httpApiResource = null;
//                try {
//                    httpApiResource = this.selectHttpApiByUri(uri);
//                } catch (Exception e) {
//                    log.error("查询数据失败：", e);
//                }
//
//                Query queryDB = new Query();
//                queryDB.addCriteria(org.springframework.data.mongodb.core.query.Criteria.where("uri").is(uri));
//
//                Update update = getUpdate(type, target);
//                List<String> tags = Optional.ofNullable(httpApiResource.getTags()).orElse(new ArrayList<>());
//                if (!tags.contains("confirmed")) {
//                    tags.add("confirmed");
//                    update.set("tags", tags);
//                }
//
//                org.springframework.data.util.Pair<Query, Update> updatePair = org.springframework.data.util.Pair.of(queryDB, update);
//                updateList.add(updatePair);
//            });
//
//            BulkOperations bulkOperations = mongoTemplate.bulkOps(BulkOperations.BulkMode.UNORDERED, collectionName);
//            bulkOperations.updateMulti(updateList);
//            bulkOperations.execute();
        } catch (Exception e) {
            log.error("批量更新失败：", e);
            throw new ServiceException("批量更新失败");
        }
    }

    private Update getUpdate(int type, String target) {
        Update updates = new Update();
        if (target.equals("isSso")) {
            if (type == 1) {
                updates.set("isSso", true);
            } else {
                updates.set("isSso", false);
            }
        } else if (target.equals("accParseEnable")) {
            if (type == 1) {
                updates.set("accParseEnable", true);
            } else {
                updates.set("accParseEnable", false);
            }
        } else if (target.equals("staffInfoParseEnable")) {
            if (type == 1) {
                updates.set("staffInfoParseEnable", true);
            } else {
                updates.set("staffInfoParseEnable", false);
            }
        }

        return updates;
    }

    private ResourceUpdates getResourceUpdates(int type, String target) {
        ResourceUpdates updates = new ResourceUpdates();
        if (target.equals("isSso")) {
            if (type == 1) {
                updates.set("isSso", true);
            } else {
                updates.set("isSso", false);
            }
        } else if (target.equals("accParseEnable")) {
            if (type == 1) {
                updates.set("accParseEnable", true);
            } else {
                updates.set("accParseEnable", false);
            }
        } else if (target.equals("staffInfoParseEnable")) {
            if (type == 1) {
                updates.set("staffInfoParseEnable", true);
            } else {
                updates.set("staffInfoParseEnable", false);
            }
        }


        return updates;
    }

    @Override
    public List<HttpApiResource> getAccParseApiList(ApiAccParseCriteriaDto apiAccParseCriteriaDto) {
        Query queryDB = getAccParseApiQuery(apiAccParseCriteriaDto);

        //分页
        queryDB.skip((apiAccParseCriteriaDto.getPage().intValue() - 1) * apiAccParseCriteriaDto.getLimit().intValue());
        queryDB.limit(apiAccParseCriteriaDto.getLimit().intValue());

        org.springframework.data.domain.Sort sort = null;

        //排序
        if (DataUtil.isNotEmpty(apiAccParseCriteriaDto.getField()) && DataUtil.isNotEmpty(apiAccParseCriteriaDto.getSort())) {
            if (apiAccParseCriteriaDto.getSort().intValue() == ConstantUtil.Sort.ASC) {
                sort = org.springframework.data.domain.Sort.by(org.springframework.data.domain.Sort.Direction.ASC, apiAccParseCriteriaDto.getField());
            } else if (apiAccParseCriteriaDto.getSort().intValue() == ConstantUtil.Sort.DESC) {
                sort = org.springframework.data.domain.Sort.by(org.springframework.data.domain.Sort.Direction.DESC, apiAccParseCriteriaDto.getField());
            } else {
                sort = org.springframework.data.domain.Sort.by(org.springframework.data.domain.Sort.Direction.DESC, "apiStat.totalVisits");
            }
        } else {
            sort = org.springframework.data.domain.Sort.by(org.springframework.data.domain.Sort.Direction.DESC, "apiStat.totalVisits");
        }

        queryDB.with(sort);

        return mongoTemplate.find(queryDB, HttpApiResource.class, collectionName);
    }

    @Override
    public List<HttpApiResource> getAccParseAPIList(String appUri) {
        return null;
    }

    private Query getAccParseApiQuery(ApiAccParseCriteriaDto apiAccParseCriteriaDto) {
        Query queryDB = new Query();

        queryDB.addCriteria(org.springframework.data.mongodb.core.query.Criteria.where("delFlag").is(false));

        if (DataUtil.isNotEmpty(apiAccParseCriteriaDto.getUri())) {
            if (apiAccParseCriteriaDto.getUri().startsWith("httpapi:")) {
                queryDB.addCriteria(org.springframework.data.mongodb.core.query.Criteria.where("uri").is(apiAccParseCriteriaDto.getUri()));
            } else {
                String value = apiAccParseCriteriaDto.getUri();
                value = DataUtil.regexStrEscapeExceptStartCharacter(value);
                value = value.startsWith("http") ? "^" + value : value;
                queryDB.addCriteria(org.springframework.data.mongodb.core.query.Criteria.where("apiUrl").regex(value));
            }
        }
        if (DataUtil.isNotEmpty(apiAccParseCriteriaDto.getAccParseEnable())) {
            queryDB.addCriteria(org.springframework.data.mongodb.core.query.Criteria.where("accParseEnable").is(apiAccParseCriteriaDto.getAccParseEnable()));
        }
        if (DataUtil.isNotEmpty(apiAccParseCriteriaDto.getAppUri())) {
            queryDB.addCriteria(org.springframework.data.mongodb.core.query.Criteria.where("appUri").is(apiAccParseCriteriaDto.getAppUri()));
        }
        if (DataUtil.isNotEmpty(apiAccParseCriteriaDto.getUserParseConfigs())) {
            queryDB.addCriteria(org.springframework.data.mongodb.core.query.Criteria.where("userParseConfigs").all(apiAccParseCriteriaDto.getUserParseConfigs()));
        }
        if (DataUtil.isNotEmpty(apiAccParseCriteriaDto.getSessionParseConfigs())) {
            queryDB.addCriteria(org.springframework.data.mongodb.core.query.Criteria.where("sessionParseConfigs").all(apiAccParseCriteriaDto.getSessionParseConfigs()));
        }
        if (DataUtil.isNotEmpty(apiAccParseCriteriaDto.getPasswordParseConfigs())) {
            queryDB.addCriteria(org.springframework.data.mongodb.core.query.Criteria.where("passwordParseConfigs").all(apiAccParseCriteriaDto.getPasswordParseConfigs()));
        }
        if (DataUtil.isNotEmpty(apiAccParseCriteriaDto.getLoginSuccessConfigs())) {
            queryDB.addCriteria(org.springframework.data.mongodb.core.query.Criteria.where("loginSuccessConfigs").all(apiAccParseCriteriaDto.getLoginSuccessConfigs()));
        }
        if (DataUtil.isNotEmpty(apiAccParseCriteriaDto.getTags())) {
            List<org.springframework.data.mongodb.core.query.Criteria> criteriaList = new ArrayList<>();
            org.springframework.data.mongodb.core.query.Criteria tagCriteria = org.springframework.data.mongodb.core.query.Criteria.where("tags").in(apiAccParseCriteriaDto.getTags());
            criteriaList.add(tagCriteria);

            if (DataUtil.isNotEmpty(apiAccParseCriteriaDto.getRelateId())) {
                org.springframework.data.mongodb.core.query.Criteria ralateCriteria = org.springframework.data.mongodb.core.query.Criteria.where("apiAccParseRelation.relateId").is(apiAccParseCriteriaDto.getRelateId());
                criteriaList.add(ralateCriteria);
            }

            org.springframework.data.mongodb.core.query.Criteria orCriteria = new org.springframework.data.mongodb.core.query.Criteria();
            orCriteria.orOperator(criteriaList.toArray(new org.springframework.data.mongodb.core.query.Criteria[criteriaList.size()]));

            queryDB.addCriteria(orCriteria);
        }

        return queryDB;
    }

    @Override
    public Long getAccParseApiCount(ApiAccParseCriteriaDto apiAccParseCriteriaDto) {
        Query queryDB = getAccParseApiQueryOne(apiAccParseCriteriaDto);
        return mongoTemplate.count(queryDB, collectionName);
    }

    @Override
    public Long getAppParseCount(String appUri, List<String> tags, int parse) {
        MetabaseQuery metabaseQuery = new MetabaseQuery();
        metabaseQuery.where("delFlag", Predicate.IS, false);
        metabaseQuery.where("appUri", Predicate.IS, appUri);
        metabaseQuery.where("tags", Predicate.IN, tags);
        if (parse == 1) {
            metabaseQuery.where("accParseEnable", Predicate.IS, true);
        }
        return metabaseClientTemplate.count(metabaseQuery, HttpApiResource.class);
    }

    @Override
    public void deleteConfig(String uri) throws Exception {

        HttpApiResource httpApiResource = this.selectHttpApiByUri(uri);

        if (httpApiResource.getTags() != null) {

            Iterator<String> it_b = httpApiResource.getTags().iterator();
            while (it_b.hasNext()) {
                String a = it_b.next();
                if (a.equals(TagsEnum.staffInfo.name())) {
                    it_b.remove();
                }
            }

            if (!httpApiResource.getTags().contains(TagsEnum.confirmed.name())) {

                httpApiResource.getTags().add(TagsEnum.confirmed.name());
            }

        } else {

            httpApiResource.setTags(Arrays.asList(TagsEnum.confirmed.name()));
        }


        httpApiResource.setStaffNickNameParseConfigs(new ArrayList<>());
        httpApiResource.setStaffMobileParseConfigs(new ArrayList<>());
        httpApiResource.setStaffInfoParseEnable(false);
        httpApiResource.setStaffBankCardParseConfigs(new ArrayList<>());
        httpApiResource.setStaffDepartParseConfigs(new ArrayList<>());
        httpApiResource.setStaffIdCardParseConfigs(new ArrayList<>());
        httpApiResource.setStaffEmailParseConfigs(new ArrayList<>());
        httpApiResource.setStaffIdParseConfigs(new ArrayList<>());
        httpApiResource.setStaffChineseParseConfigs(new ArrayList<>());
        httpApiResource.setStaffNameParseConfigs(new ArrayList<>());
        httpApiResource.setStaffRoleParseConfigs(new ArrayList<>());
        metabaseClientTemplate.save(httpApiResource);
    }

    @Override
    public void update(String id, Map<String, Object> updates) {
        metabaseClientTemplate.update(id, ResourceUpdates.create(updates), HttpApiResource.class);
    }

    @Override
    public void update(MetabaseQuery query, ResourceUpdates resourceUpdates) {
        metabaseClientTemplate.update(query, resourceUpdates, false, HttpApiResource.class);
    }

    @Override
    public List<HttpApiResource> selectHttpApiByRegexApiUrl(String apiUrl) {
        MetabaseQuery metabaseQuery = new MetabaseQuery().alive();
        metabaseQuery.where("apiUrl", Predicate.REGEX, apiUrl);
        List<HttpApiResource> list = metabaseClientTemplate.find(metabaseQuery, HttpApiResource.class);
        return list;
    }

    @Override
    public ApiStatistics getApiStatistics(long startTime, long endTime, List<String> appUris) {
        org.springframework.data.mongodb.core.query.Criteria criteria_allApi = org.springframework.data.mongodb.core.query.Criteria.where("delFlag").is(false);
        org.springframework.data.mongodb.core.query.Criteria criteria_timeApi = org.springframework.data.mongodb.core.query.Criteria.where("delFlag").is(false).and("discoverTime").gte(startTime).lte(endTime);
        org.springframework.data.mongodb.core.query.Criteria criteria_allApp = org.springframework.data.mongodb.core.query.Criteria.where("delFlag").is(false);
        org.springframework.data.mongodb.core.query.Criteria criteria_timeApp = org.springframework.data.mongodb.core.query.Criteria.where("delFlag").is(false).and("discoverTime").gte(startTime).lte(endTime);
        org.springframework.data.mongodb.core.query.Criteria criteria_allRisk = org.springframework.data.mongodb.core.query.Criteria.where("state").in(Arrays.asList(0, 2));
        org.springframework.data.mongodb.core.query.Criteria criteria_timeRisk = org.springframework.data.mongodb.core.query.Criteria.where("state").in(Arrays.asList(0, 2)).and("firstTime").gte(startTime).lte(endTime);
        org.springframework.data.mongodb.core.query.Criteria criteria_allWeak = org.springframework.data.mongodb.core.query.Criteria.where("delFlag").is(false).and("state").in(State.NEW.name(), State.REPAIRING.name(), State.FIXED.name(), State.REOPEN.name());
        org.springframework.data.mongodb.core.query.Criteria criteria_timeWeak = org.springframework.data.mongodb.core.query.Criteria.where("delFlag").is(false).and("state").in(State.NEW.name(), State.REPAIRING.name(), State.FIXED.name(), State.REOPEN.name()).and("earlyTimestamp").gte(startTime).lte(endTime);

        if (DataUtil.isNotEmpty(appUris)) {
            criteria_allApi.and("appUri").in(appUris);
            criteria_timeApi.and("appUri").in(appUris);
            criteria_allApp.and("uri").in(appUris);
            criteria_timeApp.and("uri").in(appUris);
            criteria_allRisk.and("appUri").in(appUris);
            criteria_timeRisk.and("appUri").in(appUris);
            criteria_allWeak.and("appUri").in(appUris);
            criteria_timeWeak.and("appUri").in(appUris);
        }

        //统计总API个数
        long totalApi = mongoTemplate.count(new Query().addCriteria(criteria_allApi), collectionName);
        //统计新增API个数
        long newApi = mongoTemplate.count(new Query().addCriteria(criteria_timeApi), collectionName);
        //统计总APP个数
        long totalApp = mongoTemplate.count(new Query().addCriteria(criteria_allApp), collectionName_app);
        //统计新增APP个数
        long newApp = mongoTemplate.count(new Query().addCriteria(criteria_timeApp), collectionName_app);
        //统计总RISK个数
        long totalRisk = mongoTemplate.count(new Query().addCriteria(criteria_allRisk), collectionName_risk);
        //统计新增RISK个数
        long newRisk = mongoTemplate.count(new Query().addCriteria(criteria_timeRisk), collectionName_risk);
        //统计总WEAKNESS个数
        long totalWeakness = mongoTemplate.count(new Query().addCriteria(criteria_allWeak), collectionName_weak);
        //统计新增WEAKNESS个数
        long newWeakness = mongoTemplate.count(new Query().addCriteria(criteria_timeWeak), collectionName_weak);
        return ApiStatistics.builder().totalApi(totalApi).newApi(newApi).totalRisk(totalRisk).newRisk(newRisk).totalWeakness(totalWeakness).newWeakness(newWeakness).totalApp(totalApp).newApp(newApp).build();
    }

    @Override
    public Long countApi(String type) {
        long count = 0L;
        if (type.equals("internetApi")) {
            count = mongoTemplate.count(new Query().addCriteria(org.springframework.data.mongodb.core.query.Criteria.where("visitDomains").regex("互联网-")
                    .and("delFlag").is(false)), collectionName);
        } else if (type.equals("fullApi")) {
            count = mongoTemplate.count(new Query().addCriteria(org.springframework.data.mongodb.core.query.Criteria.where("delFlag").is(false)), collectionName);
        } else if (type.equals("weaknessApi")) {
            count = mongoTemplate.count(new Query().addCriteria(org.springframework.data.mongodb.core.query.Criteria.where("delFlag").is(false).and("briefWeaknesses.count").gt(0)), collectionName);
        } else if (type.equals("weaknessApiWithInternet")) {
            count = mongoTemplate.count(new Query().addCriteria(org.springframework.data.mongodb.core.query.Criteria.where("delFlag").is(false).and("visitDomains").regex("互联网-").and("briefWeaknesses.count").gt(0)), collectionName);
        }
        return count;
    }

    @Override
    public Double getAssetDimension() {
        long count1 = mongoTemplate.count(new Query().addCriteria(org.springframework.data.mongodb.core.query.Criteria
                //可互联网访问且返回中携带敏感数据
                .where("visitDomains").regex("互联网-")
                .and("level").in("高敏感", "中敏感", "低敏感")
                .and("delFlag").is(false)), collectionName);
        long count2 = mongoTemplate.count(new Query().addCriteria(org.springframework.data.mongodb.core.query.Criteria.where("level").in("高敏感", "中敏感", "低敏感").and("delFlag").is(false)), collectionName);
        return count2 == 0L ? 0 : count1 * 1.0 / count2;
    }

    @Override
    public List<AggregationDto> group(HttpApiSearchDto httpApiSearchDto, GroupDto groupDto, Integer sort, Integer page, Integer limit) throws Exception {
        try {
            specialQueryFieldConvert.specialQueryFieldConvert(httpApiSearchDto);
        } catch (Exception e) {
            log.error("specialQueryFieldConvert error:{}", e);
        }
        org.springframework.data.mongodb.core.query.Criteria criteria = getCriteria(httpApiSearchDto);

        return this.group(criteria, groupDto, sort, page, limit, collectionName);
    }

    @Override
    public AggregationResults<CountEntity> getDataLabelTop10(String type, String source, List<String> appUris) {
        org.springframework.data.mongodb.core.query.Criteria criteria = org.springframework.data.mongodb.core.query.Criteria.where("delFlag").is(false);
        org.springframework.data.mongodb.core.query.Criteria criteria_int = org.springframework.data.mongodb.core.query.Criteria
                .where("visitDomains").regex("互联网-")
                .and("delFlag").is(false);

        if (DataUtil.isNotEmpty(appUris)) {
            criteria.and("appUri").in(appUris);
            criteria_int.and("appUri").in(appUris);
        }

        Aggregation aggregation = null;
        if (type.equals("fullApi")) {
            aggregation = Aggregation.newAggregation(Aggregation.match(criteria), Aggregation.unwind(source), Aggregation.group(source).first(source).as("name").count().as("count"), Aggregation.sort(org.springframework.data.domain.Sort.Direction.DESC, "count").and(org.springframework.data.domain.Sort.Direction.ASC, "_id"), Aggregation.limit(10));
        } else if (type.equals("internetApi")) {
            aggregation = Aggregation.newAggregation(Aggregation.match(criteria_int), Aggregation.unwind(source), Aggregation.group(source).first(source).as("name").count().as("count"), Aggregation.sort(org.springframework.data.domain.Sort.Direction.DESC, "count").and(org.springframework.data.domain.Sort.Direction.ASC, "_id"));
        }
        return mongoTemplate.aggregate(aggregation, collectionName, CountEntity.class);
    }


    @Override
    public AggregationResults<CountEntity> getDataLabelAggWithProvince(Long startTime, String type, String source, List<String> appUris) {
        org.springframework.data.mongodb.core.query.Criteria criteria = org.springframework.data.mongodb.core.query.Criteria.where("delFlag").is(false)
                .and("activeTime").gt(startTime);
        org.springframework.data.mongodb.core.query.Criteria criteria_int = org.springframework.data.mongodb.core.query.Criteria
                .where("visitDomains").regex("互联网-")
                .and("delFlag").is(false)
                .and("activeTime").gt(startTime);

        if (DataUtil.isNotEmpty(appUris)) {
            criteria.and("appUri").in(appUris);
            criteria_int.and("appUri").in(appUris);
        }

        Aggregation aggregation = null;
        if (type.equals("fullApi")) {
            aggregation = Aggregation.newAggregation(
                    Aggregation.match(criteria),
                    Aggregation.unwind(source),
                    Aggregation.unwind("provinces"),
                    Aggregation.group(source, "provinces")
                            .first(source).as("name")
                            .first("provinces").as("province")
                            .count().as("count"),
                    Aggregation.sort(org.springframework.data.domain.Sort.Direction.DESC, "count").and(org.springframework.data.domain.Sort.Direction.ASC, "_id")
            );
        } else if (type.equals("internetApi")) {
            aggregation = Aggregation.newAggregation(
                    Aggregation.match(criteria_int),
                    Aggregation.unwind(source),
                    Aggregation.unwind("provinces"),
                    Aggregation.group(source, "provinces")
                            .first(source).as("name")
                            .first("provinces").as("province")
                            .count().as("count"),
                    Aggregation.sort(org.springframework.data.domain.Sort.Direction.DESC, "count").and(org.springframework.data.domain.Sort.Direction.ASC, "_id")
            );
        }

        return mongoTemplate.aggregate(aggregation, collectionName, CountEntity.class);
    }

    @Override
    public AggregationResults<CountEntity> getApiLevel() {
        Aggregation aggregation = Aggregation.newAggregation(Aggregation.match(org.springframework.data.mongodb.core.query.Criteria.where("delFlag").is(false)), Aggregation.group("level").first("level").as("name").count().as("count"));
        return mongoTemplate.aggregate(aggregation, collectionName, CountEntity.class);
    }

    @Override
    @Cacheable("SensitiveInfo")
    public List<CountEntity> getSensitiveInfo() {
        Aggregation aggregation = Aggregation.newAggregation(Aggregation.match(org.springframework.data.mongodb.core.query.Criteria.where("delFlag").is(false)), Aggregation.unwind("rspDataLabels"), Aggregation.group("rspDataLabels").count().as("count"));
        return mongoTemplate.aggregate(aggregation, collectionName, CountEntity.class).getMappedResults();
    }

    @Override
    public List<Long> getStatistics() {
        List<Long> result = new ArrayList<>();
        long totalApi = mongoTemplate.count(new Query().addCriteria(org.springframework.data.mongodb.core.query.Criteria.where("delFlag").is(false)), collectionName);
        result.add(totalApi);
        long totalHighLevelApi = mongoTemplate.count(new Query().addCriteria(org.springframework.data.mongodb.core.query.Criteria.where("delFlag").is(false).and("level").is("高敏感")), collectionName);
        result.add(totalHighLevelApi);
        return result;
    }

    @Override
    public Long count(Query query, String collection) {
        return mongoTemplate.count(query, collection);
    }

    @Override
    public <T> List<T> find(Query query, Class<T> entityClass, String collectionName) {
        return mongoTemplate.find(query, entityClass, collectionName);
    }

    @Override
    public void updateByApiUri(String apiUri, Integer riskNum, List<Integer> riskLevels) {
        Update update = new Update();
        update.set("briefRiskInfo.count", riskNum);
        update.set("briefRiskInfo.riskLevels", riskLevels);
        Query query = new Query().addCriteria(org.springframework.data.mongodb.core.query.Criteria.where("uri").is(apiUri));
        mongoTemplate.upsert(query, update, collectionName);
    }

    /**
     * 应用结构分组查询
     *
     * @param
     * @return
     * @Comments: <对此方法的描述，可以引用系统设计中的描述>
     * <AUTHOR> [<EMAIL>]
     * @since 2022-111-17 9:56
     */
    @Override
    public List<AggregationDto> urlPathsGroup(HttpApiSearchDto httpApiSearchDto, Integer sort, Integer page, Integer limit, Integer node) throws Exception {
        String oldHost = "";

        if (productType.equals(ProductTypeEnum.tzyh.name())) {
            oldHost = oldHostQuery(httpApiSearchDto);
        }
        List<AggregationOperation> operations = new ArrayList<>();
        try {
            specialQueryFieldConvert.specialQueryFieldConvert(httpApiSearchDto);
        } catch (Exception e) {
            log.error("specialQueryFieldConvert error:{}", e);
        }
        org.springframework.data.mongodb.core.query.Criteria criteria = getCriteria(httpApiSearchDto);
        if (productType.equals(ProductTypeEnum.tzyh.name())) {
            return oldHostUrlPath(criteria, node, oldHost);
        }
        //查询条件
        if (DataUtil.isNotEmpty(criteria)) {
            operations.add(Aggregation.match(criteria));
        }
        //限制字段
        operations.add(Aggregation.project("urlPaths").andExpression("{$arrayElemAt : {'$urlPaths', " + node + "}}").as("groupUrlPaths"));
        //展开字段
        operations.add(Aggregation.unwind("groupUrlPaths"));
        //分组
        GroupOperation groupOperation = Aggregation.group("groupUrlPaths.path");
        GroupOperation.GroupOperationBuilder groupOperationBuilder = groupOperation.count();
        operations.add(groupOperationBuilder.as("resultAlias"));
        //排序
        if (DataUtil.isNotEmpty(sort)) {

            if (sort == ConstantUtil.Sort.ASC) {
                operations.add(Aggregation.sort(org.springframework.data.domain.Sort.Direction.ASC, "resultAlias").and(org.springframework.data.domain.Sort.by(org.springframework.data.domain.Sort.Direction.ASC, "_id")));
            } else if (sort == ConstantUtil.Sort.DESC) {
                operations.add(Aggregation.sort(org.springframework.data.domain.Sort.Direction.DESC, "resultAlias").and(org.springframework.data.domain.Sort.by(org.springframework.data.domain.Sort.Direction.ASC, "_id")));
            } else {
                operations.add(Aggregation.sort(org.springframework.data.domain.Sort.Direction.DESC, "resultAlias").and(org.springframework.data.domain.Sort.by(org.springframework.data.domain.Sort.Direction.ASC, "_id")));
            }
        }
        //当前页
        if (DataUtil.isNotEmpty(page) && DataUtil.isNotEmpty(limit)) {
            operations.add(Aggregation.skip((page - 1) * limit));
        }
        //每页显示条数
        if (DataUtil.isNotEmpty(limit)) {

            operations.add(Aggregation.limit(limit));
        }

        Aggregation aggregation = Aggregation.newAggregation(operations).withOptions(newAggregationOptions().allowDiskUse(true).build());

        return mongoTemplate.aggregate(aggregation, collectionName, AggregationDto.class).getMappedResults();
    }

    /**
     * 用合并前的旧应用替换新应用
     *
     * @param criteria
     * @param node
     * @return
     */
    private String oldHostQuery(HttpApiSearchDto httpApiSearchDto) {
        String res = "";
        if (DataUtil.isNotEmpty(httpApiSearchDto.getUrlPathList())) {
            List<HttpApiSearchDto.UrlPaths> newList = new ArrayList<>();
            for (HttpApiSearchDto.UrlPaths urlPath : httpApiSearchDto.getUrlPathList()) {
                HttpApiSearchDto.UrlPaths newUrlPath = new HttpApiSearchDto.UrlPaths();
                newUrlPath.setUrlPath(urlPath.getUrlPath());
                newUrlPath.setUrlNode(urlPath.getUrlNode());
                newList.add(newUrlPath);
                if (newUrlPath.getUrlNode() == 1) {
                    String appUri = httpApiSearchDto.getAppUri();
                    String path = newUrlPath.getUrlPath();
                    int index = appUri.indexOf("@QZKJMGC@");
                    String host = appUri.substring(0, index).replaceFirst("httpapp:", "");
                    if ((!host.contains(":") && !path.contains(":")) || (host.contains(":") && path.contains(":"))) {
                        res = path;
                        newUrlPath.setUrlPath(host);
                    } else if (host.contains(":") && !path.contains(":")) {
                        String[] split = host.split(":");
                        res = split[0];
                        newUrlPath.setUrlPath(split[0]);
                    } else {
                        String[] split = path.split(":");
                        res = split[0];
                        String urlPathRes = path.replaceFirst(split[0], host);
                        newUrlPath.setUrlPath(urlPathRes);
                    }
                }
            }
            httpApiSearchDto.setUrlPathList(newList);
        }
        return res;
    }


    /**
     * 用合并前的旧应用替换新应用
     *
     * @param criteria
     * @param node
     * @return
     */
    private List<AggregationDto> oldHostUrlPath(org.springframework.data.mongodb.core.query.Criteria criteria, Integer node, String oldHostValue) {
        if (DataUtil.isNotEmpty(oldHostValue)) {
            criteria.and("oldHost").is(oldHostValue);
        }
        List<HttpApiResource> httpApiResources = mongoTemplate.find(new Query(criteria), HttpApiResource.class, collectionName);
        List<AggregationDto> aggregationDtoList = new ArrayList<>();
        Map<String, Integer> map = new HashMap<>();
        for (HttpApiResource httpApiResource : httpApiResources) {
            for (HttpApiResource.UrlPath urlPath : httpApiResource.getUrlPaths()) {
                Integer urlPathNode = urlPath.getNode();
                if (node.equals(urlPathNode - 1)) {
                    String urlPathPath = urlPath.getPath();
                    if (node == 0) {
                        String oldHost = httpApiResource.getOldHost();
                        if (DataUtil.isNotEmpty(oldHost) && !oldHost.equals(httpApiResource.getHost())) {
                            if ((urlPathPath.contains(":") && oldHost.contains(":")) || (!urlPathPath.contains(":") && !oldHost.contains(":"))) {
                                urlPathPath = oldHost;
                            } else if (!urlPathPath.contains(":") && oldHost.contains(":")) {
                                String[] split = oldHost.split(":");
                                urlPathPath = split[0];
                            } else {
                                String[] split = urlPathPath.split(":");
                                urlPathPath = oldHost + ":" + split[1];
                            }
                        }
                    }
                    Integer count = map.getOrDefault(urlPathPath, 0);
                    map.put(urlPathPath, count + 1);
                }
            }

        }
        for (String key : map.keySet()) {
            AggregationDto aggregationDto = new AggregationDto();
            aggregationDto.setId(key);
            aggregationDto.setResultAlias(map.get(key));
            aggregationDtoList.add(aggregationDto);
        }
        return aggregationDtoList;
    }

    public List<AggregationDto> compositeUrlPathsGroup(HttpApiSearchDto httpApiSearchDto, Integer sort, Integer page, Integer limit, Integer node) throws Exception {
        List<AggregationOperation> operations = new ArrayList<>();
        org.springframework.data.mongodb.core.query.Criteria criteria = new org.springframework.data.mongodb.core.query.Criteria();
        List<org.springframework.data.mongodb.core.query.Criteria> andCriteriaList = new ArrayList<>();
        if (DataUtil.isNotEmpty(httpApiSearchDto.getUrlNode()) && httpApiSearchDto.getUrlNode() != 0 && DataUtil.isNotEmpty(httpApiSearchDto.getUrlPathList())) {
            for (HttpApiSearchDto.UrlPaths urlPaths : httpApiSearchDto.getUrlPathList()) {
                org.springframework.data.mongodb.core.query.Criteria where = org.springframework.data.mongodb.core.query.Criteria.where("node").is(urlPaths.getUrlNode());
                where.and("path").is(urlPaths.getUrlPath());
                org.springframework.data.mongodb.core.query.Criteria elemMatch = org.springframework.data.mongodb.core.query.Criteria.where("data.urlPaths").elemMatch(where);
                andCriteriaList.add(elemMatch);
            }
        }
        if (!andCriteriaList.isEmpty()) {
            criteria.andOperator(andCriteriaList.toArray(new org.springframework.data.mongodb.core.query.Criteria[andCriteriaList.size()]));
        }
        if (DataUtil.isNotEmpty(httpApiSearchDto.getAppUri())) {
            criteria.and("data.appUri").is(httpApiSearchDto.getAppUri());
        }
        if (DataUtil.isNotEmpty(httpApiSearchDto.getHostRegex())) {
            Pattern pattern = Pattern.compile(httpApiSearchDto.getHostRegex());
            criteria.and("data.host").regex(pattern);
        }
        //查询条件
        if (DataUtil.isNotEmpty(criteria)) {
            operations.add(Aggregation.match(criteria));
        }
        //限制字段
        operations.add(Aggregation.project("data.urlPaths").andExpression("{$arrayElemAt : {'$data.urlPaths', " + node + "}}").as("groupUrlPaths"));
        //展开字段
        operations.add(Aggregation.unwind("groupUrlPaths"));
        //分组
        GroupOperation groupOperation = Aggregation.group("groupUrlPaths.path");
        GroupOperation.GroupOperationBuilder groupOperationBuilder = groupOperation.count();
        operations.add(groupOperationBuilder.as("resultAlias"));
        //排序
        if (DataUtil.isNotEmpty(sort)) {
            if (sort == ConstantUtil.Sort.ASC) {
                operations.add(Aggregation.sort(org.springframework.data.domain.Sort.Direction.ASC, "resultAlias").and(org.springframework.data.domain.Sort.by(org.springframework.data.domain.Sort.Direction.ASC, "_id")));
            } else if (sort == ConstantUtil.Sort.DESC) {
                operations.add(Aggregation.sort(org.springframework.data.domain.Sort.Direction.DESC, "resultAlias").and(org.springframework.data.domain.Sort.by(org.springframework.data.domain.Sort.Direction.ASC, "_id")));
            } else {
                operations.add(Aggregation.sort(org.springframework.data.domain.Sort.Direction.DESC, "resultAlias").and(org.springframework.data.domain.Sort.by(org.springframework.data.domain.Sort.Direction.ASC, "_id")));
            }
        }
        //当前页
        if (DataUtil.isNotEmpty(page) && DataUtil.isNotEmpty(limit)) {
            operations.add(Aggregation.skip((page - 1) * limit));
        }
        //每页显示条数
        if (DataUtil.isNotEmpty(limit)) {
            operations.add(Aggregation.limit(limit));
        }
        Aggregation aggregation = Aggregation.newAggregation(operations).withOptions(newAggregationOptions().allowDiskUse(true).build());
        return mongoTemplate.aggregate(aggregation, HttpResourceConstant.API_COMPOSITE_BACKUP, AggregationDto.class).getMappedResults();
    }

    /**
     * @param httpApiResource, apiAccParseCriteriaDto
     * @return void
     * <AUTHOR>
     * @date 2023/3/7 10:34 AM
     * @Description 补充账号解析配置关系
     * @Since
     */
    private void saveAccParseRelation(HttpApiResource httpApiResource, ApiAccParseCriteriaDto apiAccParseCriteriaDto) {
        ApiAccParseRelation apiAccParseRelation = new ApiAccParseRelation();
        apiAccParseRelation.setLoginType(apiAccParseCriteriaDto.getLoginType());
        apiAccParseRelation.setResolveIpFlag(apiAccParseCriteriaDto.getResolveIpFlag());
        apiAccParseRelation.setRelateOrder(apiAccParseCriteriaDto.getRelateOrder());
        apiAccParseRelation.setRelateId(apiAccParseCriteriaDto.getRelateId());
        apiAccParseRelation.setApiResolveFlag(apiAccParseCriteriaDto.getApiResolveFlag());

        List<String> parseLocations = new ArrayList<>();

        //账号配置
        List<HttpApiResource.AccParseConfigs> userParseConfigs = apiAccParseCriteriaDto.getUserParseConfigs();

        // session配置
        List<HttpApiResource.AccParseConfigs> sessionParseConfigs = apiAccParseCriteriaDto.getSessionParseConfigs();

        // 密码配置
        List<HttpApiResource.AccParseConfigs> passwordParseConfigs = apiAccParseCriteriaDto.getPasswordParseConfigs();

        // 登录凭证配置
        List<HttpApiResource.AccParseConfigs> loginSuccessConfigs = apiAccParseCriteriaDto.getLoginSuccessConfigs();

        if (DataUtil.isNotEmpty(userParseConfigs)) {
            for (HttpApiResource.AccParseConfigs accParseConfig : userParseConfigs) {
                String parseLocation = PARSE_CONFIG_USER + accParseConfig.getId();
                parseLocations.add(parseLocation);
            }
        }

        if (DataUtil.isNotEmpty(sessionParseConfigs)) {
            for (HttpApiResource.AccParseConfigs accParseConfig : sessionParseConfigs) {
                String parseLocation = PARSE_CONFIG_SESSION + accParseConfig.getId();
                parseLocations.add(parseLocation);
            }
        }

        if (DataUtil.isNotEmpty(passwordParseConfigs)) {
            for (HttpApiResource.AccParseConfigs accParseConfig : passwordParseConfigs) {
                String parseLocation = PARSE_CONFIG_PWD + accParseConfig.getId();
                parseLocations.add(parseLocation);
            }
        }

        if (DataUtil.isNotEmpty(loginSuccessConfigs)) {
            for (HttpApiResource.AccParseConfigs accParseConfig : loginSuccessConfigs) {
                String parseLocation = PARSE_CONFIG_LOGIN + accParseConfig.getId();
                parseLocations.add(parseLocation);
            }
        }

        if (DataUtil.isNotEmpty(parseLocations)) {
            apiAccParseRelation.setParseLocations(parseLocations);

            List<ApiAccParseRelation> apiAccParseRelations = httpApiResource.getApiAccParseRelations();
            if (apiAccParseRelations == null) {
                apiAccParseRelations = new ArrayList<>();
            }
            apiAccParseRelations.add(apiAccParseRelation);

            httpApiResource.setApiAccParseRelations(apiAccParseRelations);
        }
    }

    @Override
    public MongoCursor<Document> getHttpApiList(HttpApiSearchDto httpApiSearchDto, int batchSize) {
        MetabaseQuery metabaseQuery = new MetabaseQuery();
        metabaseQuery = fillMetabaseQuery(metabaseQuery, httpApiSearchDto);
        Query query = QueryHelper.toQuery(metabaseQuery);
        query.fields().include("apiUrl");
        return mongoTemplate.getCollection("httpApi").find(query.getQueryObject()).batchSize(batchSize).noCursorTimeout(true).iterator();
    }

    @Override
    public List<HttpApiResource> getHttpApiList(Set<String> apiUriSet, String relateId) {
        List<org.springframework.data.mongodb.core.query.Criteria> criteriaList = new ArrayList<>();

        org.springframework.data.mongodb.core.query.Criteria criteriaBase = new org.springframework.data.mongodb.core.query.Criteria();

        if (DataUtil.isNotEmpty(apiUriSet)) {
            criteriaList.add(org.springframework.data.mongodb.core.query.Criteria.where("uri").in(apiUriSet));
        }

        if (DataUtil.isNotEmpty(relateId)) {
            criteriaList.add(org.springframework.data.mongodb.core.query.Criteria.where("apiAccParseRelations.relateId").in(Arrays.asList(relateId)));
        }

        if (criteriaList.size() > 0) {
            org.springframework.data.mongodb.core.query.Criteria criteria = new org.springframework.data.mongodb.core.query.Criteria();
            criteria.orOperator(criteriaList.toArray(new org.springframework.data.mongodb.core.query.Criteria[criteriaList.size()]));

            AggregationOperation matchOperation = Aggregation.match(criteria);
            LimitOperation limitOperation = Aggregation.limit(100);

            List<AggregationOperation> operationList = new ArrayList<>();
            operationList.add(matchOperation);
            operationList.add(limitOperation);

            Aggregation aggregation = Aggregation.newAggregation(operationList);
            AggregationResults<HttpApiResource> results_ = mongoTemplate.aggregate(aggregation, collectionName, HttpApiResource.class);
            List<HttpApiResource> results = results_.getMappedResults();

            return results;
        }

        return new ArrayList<>();
    }

    @Override
    public List<HttpApiResource> getAccParseApiOne(ApiAccParseCriteriaDto apiAccParseCriteriaDto) {
        Query queryDB = getAccParseApiQueryOne(apiAccParseCriteriaDto);
        queryDB.skip(0);
        queryDB.limit(1);
        return mongoTemplate.find(queryDB, HttpApiResource.class, collectionName);
    }

    @Override
    public List<HttpApiResource> getAccParseAPIs(String appUri) {
        return metabaseClientTemplate.find(new MetabaseQuery().where("appUri", Predicate.IS, appUri).where("accParseEnable", Predicate.IS, true).where("tags", Predicate.IN, Arrays.asList(TagsEnum.accountParse.name(), TagsEnum.confirmed.name())).alive().limit(1000), HttpApiResource.class);
    }

    @Override
    public List<HttpApiResource> getApiResourceByIds(List<String> ids) {
        Query queryDB = new Query();

        queryDB.addCriteria(org.springframework.data.mongodb.core.query.Criteria.where("_id").in(ids));

        return mongoTemplate.find(queryDB, HttpApiResource.class, collectionName);
    }

    @Override
    public List<SyncGatewayDto> getApiResourceByIdsGateway(List<String> ids) {
        Query queryDB = new Query();

        queryDB.addCriteria(org.springframework.data.mongodb.core.query.Criteria.where("_id").in(ids));

        return mongoTemplate.find(queryDB, SyncGatewayDto.class, collectionName);
    }

    @Override
    public void updateByGatewayId(String id) {


        Query queryDB = new Query();

        queryDB.addCriteria(org.springframework.data.mongodb.core.query.Criteria.where("_id").is(id));

        Update update = new Update();

        update.set("gatewayApiId", id);

        mongoTemplate.upsert(queryDB, update, collectionName);
    }

    @Override
    public List<HttpApiResource> findByUri(String uri) {
        Query queryDB = new Query();

        queryDB.addCriteria(org.springframework.data.mongodb.core.query.Criteria.where("uri").is(uri));

        return mongoTemplate.find(queryDB, HttpApiResource.class, collectionName);
    }

    @Override
    public HttpApiResource findOneByUri(String uri) {
        Query queryDB = new Query();

        queryDB.addCriteria(org.springframework.data.mongodb.core.query.Criteria.where("uri").is(uri));

        return mongoTemplate.findOne(queryDB, HttpApiResource.class, collectionName);
    }


    @Override
    public List<HttpApiResource> getApiResourceByProvince(List<String> province) {
        Query queryDB = new Query();

        queryDB.addCriteria(org.springframework.data.mongodb.core.query.Criteria.where("delFlag").is(false).and("provinces").in(province));
        return mongoTemplate.find(queryDB, HttpApiResource.class, collectionName);

    }

    @Override
    public List<HttpApiResource> findAll() {
        Query queryDB = new Query();

        queryDB.addCriteria(org.springframework.data.mongodb.core.query.Criteria.where("delFlag").is(false));

        return mongoTemplate.find(queryDB, HttpApiResource.class, collectionName);
    }

    @Override
    public void updateProvince(String uri, List<String> province) {

        Update update = new Update();
        update.set("province", province);
        Query query = new Query().addCriteria(org.springframework.data.mongodb.core.query.Criteria.where("uri").is(uri).and("delFlag").is(false));
        mongoTemplate.upsert(query, update, collectionName);
    }

    @Override
    public List<String> getAppUriByProvince(List<String> province) {
        MatchOperation matchOperation = Aggregation.match(
                org.springframework.data.mongodb.core.query.Criteria.where("delFlag").is(true)
                        .and("province").in(province)
        );

        GroupOperation groupOperation = Aggregation.group("appUri");

        ProjectionOperation projectionOperation = Aggregation.project("appUri")
                .andExclude("_id");

        Aggregation aggregation = Aggregation.newAggregation(matchOperation, groupOperation, projectionOperation);

        return mongoTemplate.aggregate(aggregation, "collection", String.class).getMappedResults();
    }

    private Query getAccParseApiQueryOne(ApiAccParseCriteriaDto apiAccParseCriteriaDto) {
        Query queryDB = new Query();

        queryDB.addCriteria(org.springframework.data.mongodb.core.query.Criteria.where("delFlag").is(false));

        String uri = apiAccParseCriteriaDto.getUri();
        if (DataUtil.isNotEmpty(uri)) {
            if (!uri.startsWith("httpapi:")) {
                uri = "httpapi:" + uri;
            }
            queryDB.addCriteria(org.springframework.data.mongodb.core.query.Criteria.where("uri").is(uri));
        }
        if (DataUtil.isNotEmpty(apiAccParseCriteriaDto.getAccParseEnable())) {
            queryDB.addCriteria(org.springframework.data.mongodb.core.query.Criteria.where("accParseEnable").is(apiAccParseCriteriaDto.getAccParseEnable()));
        }
        if (DataUtil.isNotEmpty(apiAccParseCriteriaDto.getAppUri())) {
            queryDB.addCriteria(org.springframework.data.mongodb.core.query.Criteria.where("appUri").is(apiAccParseCriteriaDto.getAppUri()));
        }
        if (DataUtil.isNotEmpty(apiAccParseCriteriaDto.getUserParseConfigs())) {
            queryDB.addCriteria(org.springframework.data.mongodb.core.query.Criteria.where("userParseConfigs").all(apiAccParseCriteriaDto.getUserParseConfigs()));
        }
        if (DataUtil.isNotEmpty(apiAccParseCriteriaDto.getSessionParseConfigs())) {
            queryDB.addCriteria(org.springframework.data.mongodb.core.query.Criteria.where("sessionParseConfigs").all(apiAccParseCriteriaDto.getSessionParseConfigs()));
        }
        if (DataUtil.isNotEmpty(apiAccParseCriteriaDto.getPasswordParseConfigs())) {
            queryDB.addCriteria(org.springframework.data.mongodb.core.query.Criteria.where("passwordParseConfigs").all(apiAccParseCriteriaDto.getPasswordParseConfigs()));
        }
        if (DataUtil.isNotEmpty(apiAccParseCriteriaDto.getLoginSuccessConfigs())) {
            queryDB.addCriteria(org.springframework.data.mongodb.core.query.Criteria.where("loginSuccessConfigs").all(apiAccParseCriteriaDto.getLoginSuccessConfigs()));
        }
        if (DataUtil.isNotEmpty(apiAccParseCriteriaDto.getTags())) {
            List<org.springframework.data.mongodb.core.query.Criteria> criteriaList = new ArrayList<>();
            org.springframework.data.mongodb.core.query.Criteria tagCriteria = org.springframework.data.mongodb.core.query.Criteria.where("tags").in(apiAccParseCriteriaDto.getTags());
            criteriaList.add(tagCriteria);

            if (DataUtil.isNotEmpty(apiAccParseCriteriaDto.getRelateId())) {
                org.springframework.data.mongodb.core.query.Criteria ralateCriteria = org.springframework.data.mongodb.core.query.Criteria.where("apiAccParseRelation.relateId").is(apiAccParseCriteriaDto.getRelateId());
                criteriaList.add(ralateCriteria);
            }

            org.springframework.data.mongodb.core.query.Criteria orCriteria = new org.springframework.data.mongodb.core.query.Criteria();
            orCriteria.orOperator(criteriaList.toArray(new org.springframework.data.mongodb.core.query.Criteria[criteriaList.size()]));

            queryDB.addCriteria(orCriteria);
        }

        return queryDB;

    }
}

