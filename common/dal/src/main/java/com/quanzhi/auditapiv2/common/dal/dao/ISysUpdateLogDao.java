package com.quanzhi.auditapiv2.common.dal.dao;

import com.quanzhi.auditapiv2.common.dal.dao.base.IBaseDao;
import com.quanzhi.auditapiv2.common.dal.entity.SysUpdateLog;

import java.util.List;

/**
 *
 * 《系统升级日志持久层接口》
 *
 *
 * @Project:
 * @Module ID:
 * @Comments:
 * @JDK version used:      <JDK1.8> 
 * <AUTHOR> [<EMAIL>]
 * @since 2020-04-08-上午9:56:10
 */
public interface ISysUpdateLogDao extends IBaseDao<SysUpdateLog> {

    /**
     * 查询系统升级日志列表(分页)
     * @Comments:  <对此方法的描述，可以引用系统设计中的描述>
     * <AUTHOR> [<EMAIL>]
     * @since 2020-04-08 9:56
     * @param
     * @return
     */
    List<SysUpdateLog> selectSysUpdateLogList(Integer page, Integer limit) throws Exception;

    /**
     * 查询系统升级日志数量
     * @Comments:  <对此方法的描述，可以引用系统设计中的描述>
     * <AUTHOR> [<EMAIL>]
     * @since 2020-04-08 9:56
     * @param
     * @return
     */
    Long totalCount() throws Exception;

    /**
     * id查询系统升级日志
     * @Comments:  <对此方法的描述，可以引用系统设计中的描述>
     * <AUTHOR> [<EMAIL>]
     * @since 2020-04-08 9:56
     * @param
     * @return
     */
    SysUpdateLog selectSysUpdateLogById(String id) throws Exception;

    /**
     * 查询进行中状态升级日志
     * @Comments:  <对此方法的描述，可以引用系统设计中的描述>
     * <AUTHOR> [<EMAIL>]
     * @since 2020-04-08 9:56
     * @param
     * @return
     */
    SysUpdateLog selectSysUpdateLog() throws Exception;

    /**
     * 新增系统升级日志
     * @Comments:  <对此方法的描述，可以引用系统设计中的描述>
     * <AUTHOR> [<EMAIL>]
     * @since 2020-04-08 9:56
     * @param
     * @return
     */
    SysUpdateLog insertSysUpdateLog(SysUpdateLog sysUpdateLog) throws Exception;

    /**
     * 编辑系统升级日志
     * @Comments:  <对此方法的描述，可以引用系统设计中的描述>
     * <AUTHOR> [<EMAIL>]
     * @since 2020-04-08 9:56
     * @param
     * @return
     */
    SysUpdateLog updateSysUpdateLog(SysUpdateLog sysUpdateLog) throws Exception;
}
