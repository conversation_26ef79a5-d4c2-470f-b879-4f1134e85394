package com.quanzhi.auditapiv2.common.dal.dto.query;

import com.quanzhi.auditapiv2.common.dal.dto.ExporTitleFieldDto;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * @ClassName ApiAccountInfoDto
 * @Description TODO
 * <AUTHOR>
 * @Date 2020/11/22 23:26
 **/
@Data
public class ApiAccountInfoCriteriaDto implements Serializable {

    /**
     * 账号
     */
    private String account;

    /**
     * 账号标签
     */
    private List<String> threatLabels;

    /**
     * 首次发现日期
     */
    private String firstDate;

    /**
     * 最后发现日期
     */
    private String lastDate;

    /**
     * 发现时间范围查询
     */
    private String startTime;

    private String endTime;

    /**
     * 最近活跃时间范围查询
     */
    private String startDate;

    private String endDate;

    private Integer page;

    private Integer limit;

    private String field;

    private Integer sort;

    private List<ExporTitleFieldDto> list;

    /**
     * oa帐号  (唯品会新增字段)
     */
    private String applicant_id;
    /**
     * 数据内容 (唯品会新增字段)
     */
    private String tags;
    /**
     * 导出原因 (唯品会新增字段)
     */
    private String reasons;
    /**
     * 使用范围 (唯品会新增字段)
     */
    private String usage;

}
