package com.quanzhi.auditapiv2.common.dal.dao.impl;

import com.quanzhi.auditapiv2.common.dal.dao.IHttpAppSnapshotDao;
import com.quanzhi.auditapiv2.common.dal.dao.base.impl.MetabaseDaoImpl;
import com.quanzhi.metabase.core.model.http.HttpAppSnapshot;
import com.quanzhi.metabase.core.model.query.MetabaseQuery;
import com.quanzhi.metabase.core.model.query.Predicate;
import com.quanzhi.metabase.core.model.query.Sort;
import com.quanzhi.metabase.core.model.query.SortOrder;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 
 * 《应用快照持久层接口实现》
 * 
 * 
 * @Project: 
 * @Module ID:
 * @Comments: 
 * @JDK version used:      <JDK1.8> 
 * <AUTHOR> [<EMAIL>]
 * @since 2020-04-08-上午9:56:10
 */
@Repository
public class HttpAppSnapshotDaoImpl extends MetabaseDaoImpl<HttpAppSnapshot> implements IHttpAppSnapshotDao {

    /**
     * 查询应用快照列表(分页)
     * @Comments:  <对此方法的描述，可以引用系统设计中的描述>
     * <AUTHOR> [<EMAIL>]
     * @since 2020-04-08 9:56
     * @param
     * @return
     */
    @Override
    public List<HttpAppSnapshot> selectHttpAppSnapshotList(String appId, Integer page, Integer limit) throws Exception {

        MetabaseQuery metabaseQuery = new MetabaseQuery();

        //查询条件
        metabaseQuery.where("delFlag", Predicate.IS, false);
        metabaseQuery.where("appId", Predicate.IS, appId);
        //分页
        metabaseQuery.skip((page - 1) * limit);
        metabaseQuery.limit(limit);
        //排序
        metabaseQuery.sort(Sort.by("startTimestamp", SortOrder.DESC));

        return metabaseClientTemplate.find(metabaseQuery, HttpAppSnapshot.class);
    }

    /**
     * 查询应用快照数量
     * @Comments:  <对此方法的描述，可以引用系统设计中的描述>
     * <AUTHOR> [<EMAIL>]
     * @since 2020-04-08 9:56
     * @param
     * @return
     */
    @Override
    public Long totalCount(String appId) {

        MetabaseQuery metabaseQuery = new MetabaseQuery();

        //查询条件
        metabaseQuery.where("delFlag", Predicate.IS, false);
        metabaseQuery.where("appId", Predicate.IS, appId);

        return metabaseClientTemplate.count(metabaseQuery, HttpAppSnapshot.class);
    }

    /**
     * id查询应用接口快照详情
     * @Comments:  <对此方法的描述，可以引用系统设计中的描述>
     * <AUTHOR> [<EMAIL>]
     * @since 2020-04-08 9:56
     * @param
     * @return
     */
    @Override
    public HttpAppSnapshot selectHttpAppSnapshotByAppId(String appId, Long startDate, Long endDate) throws Exception {

       MetabaseQuery metabaseQuery = new MetabaseQuery();

       //查询条件
       metabaseQuery.where("delFlag", Predicate.IS, false);
       metabaseQuery.where("appId", Predicate.IS, appId);
       metabaseQuery.where("startTimestamp", Predicate.GTE, startDate);
       metabaseQuery.where("endTimestamp", Predicate.LTE, endDate);

        return metabaseClientTemplate.findOne(metabaseQuery, HttpAppSnapshot.class);
    }
}
