package com.quanzhi.auditapiv2.common.dal.entity;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 《开放接口密钥》
 *
 * <AUTHOR> [<EMAIL>]
 * @Project:
 * @Module ID:
 * @Comments:
 * @JDK version used:      <JDK1.8>
 * @since 2021-08-17-上午9:09:10
 */
@Data
public class OpenApiKey {

    private String id;

    private String useKey;

    /**
     * 密钥
     */
    @ApiModelProperty(value = "密钥", name = "key")
    private String key;

    @ApiModelProperty(value = "密钥过期时间", name = "key")
    private Long deadline;

    private Integer state;

    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间", name = "createDate")
    private Long createDate;
}
