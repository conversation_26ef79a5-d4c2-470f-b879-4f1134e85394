package com.quanzhi.auditapiv2.common.dal.entity;

import com.quanzhi.metabase.core.model.event.ApiWeaknessPayload;
import com.quanzhi.metabase.core.model.http.weakness.ApiWeakness;
import com.quanzhi.metabase.core.model.http.weakness.State;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;
import org.mapstruct.factory.Mappers;

/**
 * 《接口弱点日志》
 *
 * <AUTHOR> [<EMAIL>]
 * @Project:
 * @Module ID:
 * @Comments:
 * @JDK version used:      <JDK1.8>
 * @since 2020-08-28-下午16:52:18
 */
@Data
public class ApiWeaknessLog {

    @ApiModelProperty(value = "id", name = "id")
    private String id;

    /**
     * 弱点id
     */
    @ApiModelProperty(value = "接口弱点id", name = "apiWeaknessId")
    private String apiWeaknessId;

    /**
     * 状态
     */
    @ApiModelProperty(value = "状态", name = "state")
    private State state;

    /**
     * 状态名称
     */
    @ApiModelProperty(value = "状态名称", name = "stateName")
    private String stateName;

    /**
     * 旧状态
     */
    @ApiModelProperty(value = "旧状态", name = "oldState")
    private State oldState;

    /**
     * 旧状态名称
     */
    @ApiModelProperty(value = "旧状态名称", name = "oldStateName")
    private String oldStateName;

    /**
     * 上次操作者
     */
    @ApiModelProperty(value = "上次操作者", name = "lastOperator")
    private String lastOperator;

    /**
     * 首次发现时间
     */
    @ApiModelProperty(value = "首次发现时间", name = "earlyTimestamp")
    private Long earlyTimestamp;

    /**
     * 更新时间
     */
    @ApiModelProperty(value = "更新时间", name = "updateTime")
    private Long updateTime;

    /**
     * 处理意见
     */
    @ApiModelProperty(value = "处理意见", name = "suggestion")
    private String suggestion;

    @Mapper
    public interface ApiWeaknessPayloadMapper {

        ApiWeaknessPayloadMapper INSTANCE = Mappers.getMapper(ApiWeaknessPayloadMapper.class);

        @Mappings({
                @Mapping(target = "id", ignore = true)
        })
        ApiWeaknessLog convert(ApiWeaknessPayload apiWeaknessPayload);

        @Mappings({
                @Mapping(target = "apiWeaknessId", source = "id"),
                @Mapping(target = "id", ignore = true)
        })
        ApiWeaknessLog convert(ApiWeakness apiWeakness);
    }
}
