package com.quanzhi.auditapiv2.common.dal.dao.impl.api;

import com.quanzhi.auditapiv2.common.dal.dao.impl.common.Module;
import com.quanzhi.auditapiv2.common.dal.dao.impl.common.ModuleMapper;
import com.quanzhi.auditapiv2.common.dal.dao.impl.common.QueryAdapter;
import com.quanzhi.metabase.core.model.query.Criteria;
import com.quanzhi.metabase.core.model.query.MetabaseQuery;
import com.quanzhi.metabase.core.model.query.Predicate;

import java.util.Map;

/**
 * <AUTHOR>
 * @date 2022/7/25 2:11 下午
 */
@ModuleMapper(modules = {Module.WEAKNESS, Module.APP})
public class AppTimestampQueryAdapter implements QueryAdapter {
    @Override
    public void query(Object query, MetabaseQuery metabaseQuery) {
        if (!(query instanceof Map)) {
            return;
        }
        Map<String, Object> map = (Map<String, Object>) query;
        if (map.get("createTimestamp") != null) {
            long timestamp = Long.valueOf(map.get("createTimestamp").toString());
            metabaseQuery.where("createTime", Predicate.GTE, timestamp);
        }
        if (map.get("updateTimestamp") != null) {
            long timestamp = Long.valueOf(map.get("updateTimestamp").toString());
            metabaseQuery.where("updateTime", Predicate.GTE, timestamp);
        }
        if(map.get("startUpdateTime")!=null&&map.get("endUpdateTime")!=null){
            long startUpdateTime = Long.valueOf(map.get("startUpdateTime").toString());
            long endUpdateTime = Long.valueOf(map.get("endUpdateTime").toString());
            Criteria criteria=new Criteria();
            criteria.andOperator(Criteria.where("updateTime").gte(startUpdateTime),Criteria.where("updateTime").lte(endUpdateTime));
            metabaseQuery.getCriteria().add(criteria);
        }else if(map.get("endUpdateTime")!=null){
            long endUpdateTime = Long.valueOf(map.get("endUpdateTime").toString());
            metabaseQuery.where("updateTime", Predicate.LTE, endUpdateTime);
        }
    }

    @Override
    public void query(Object query, org.springframework.data.mongodb.core.query.Criteria criteria) {

    }
}
