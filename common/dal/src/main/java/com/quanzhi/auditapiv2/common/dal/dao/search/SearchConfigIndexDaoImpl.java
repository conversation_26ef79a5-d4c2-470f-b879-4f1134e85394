package com.quanzhi.auditapiv2.common.dal.dao.search;

import com.quanzhi.auditapiv2.common.dal.dao.SearchConfigIndexDao;
import com.quanzhi.auditapiv2.common.dal.entity.search.KeywordMapping;
import com.quanzhi.auditapiv2.common.dal.entity.search.SearchConfigIndex;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.context.event.ApplicationReadyEvent;
import org.springframework.context.event.EventListener;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.index.IndexOperations;
import org.springframework.data.mongodb.core.index.IndexResolver;
import org.springframework.data.mongodb.core.index.MongoPersistentEntityIndexResolver;
import org.springframework.data.mongodb.core.mapping.MongoMappingContext;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Repository;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023/2/20 3:28 下午
 */
@Repository
@Slf4j
public class SearchConfigIndexDaoImpl implements SearchConfigIndexDao {

    private final MongoTemplate mongoTemplate;

    private final MongoMappingContext mongoMappingContext;

    public SearchConfigIndexDaoImpl(MongoTemplate mongoTemplate, MongoMappingContext mongoMappingContext) {
        this.mongoTemplate = mongoTemplate;
        this.mongoMappingContext = mongoMappingContext;
    }

    @EventListener(ApplicationReadyEvent.class)
    public void onEvent(ApplicationReadyEvent event) {
        try {
            IndexOperations indexOps = mongoTemplate.indexOps(SearchConfigIndex.class);
            IndexResolver resolver = new MongoPersistentEntityIndexResolver(mongoMappingContext);
            resolver.resolveIndexFor(SearchConfigIndex.class).forEach(indexOps::ensureIndex);
        } catch (Exception e) {
            log.error("SearchConfigIndexDaoImpl onEvent error", e);
        }
    }

    @Override
    public void save(SearchConfigIndex searchConfigIndex) {
        mongoTemplate.save(searchConfigIndex);
    }

    @Override
    public SearchConfigIndex findByType(String type) {
        return mongoTemplate.findOne(Query.query(Criteria.where("type").is(type)), SearchConfigIndex.class);
    }

    @Override
    public List<SearchConfigIndex> list(String q) {
        return list(q, Collections.emptyList());
    }

    @Override
    public List<SearchConfigIndex> list(String q, List<KeywordMapping.Type> types) {
        Criteria criteria = Criteria.where("key").regex(Pattern.compile(q, Pattern.CASE_INSENSITIVE));
        if (types != null && !types.isEmpty()) {
            criteria.and("type").in(types.stream().map(Enum::name).collect(Collectors.toList()));
        }
        return mongoTemplate.find(Query.query(Criteria.where("keywords").elemMatch(criteria)), SearchConfigIndex.class);
    }

    @Override
    public List<SearchConfigIndex> list(List<String> keywords, List<KeywordMapping.Type> types) {
        Criteria criteria = new Criteria();
        List<Criteria> andCriteriaArray = new ArrayList<>();
        if (types != null && !types.isEmpty()) {
            andCriteriaArray.add(Criteria.where("type").in(types.stream().map(Enum::name).collect(Collectors.toList())));
        }
        List<Criteria> orCriteriaArray = new ArrayList<>();
        for (String k : keywords) {
            orCriteriaArray.add(Criteria.where("key").regex(Pattern.compile(Pattern.quote(k), Pattern.CASE_INSENSITIVE)));
        }
        andCriteriaArray.add(new Criteria().orOperator(orCriteriaArray));
        criteria.andOperator(andCriteriaArray);
        return mongoTemplate.find(Query.query(Criteria.where("keywords").elemMatch(criteria)), SearchConfigIndex.class);
    }

    @Override
    public long count() {
        return mongoTemplate.count(new Query(), SearchConfigIndex.class);
    }

    @Override
    public void clear() {
        mongoTemplate.remove(new Query(), SearchConfigIndex.class);
    }
}
