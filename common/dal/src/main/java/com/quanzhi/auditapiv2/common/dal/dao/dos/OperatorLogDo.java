package com.quanzhi.auditapiv2.common.dal.dao.dos;

import lombok.Data;

import java.util.Map;

@Data
public class OperatorLogDo {

    private String traceId;

    /**
     * 日志记录类型，web页面请求：request等
     */
    private String logType;

    private Long timeStamp;

    private String url;

    private String userId;

    private Map<String, Object> reqParams;


    private Integer resultStatus;

    /**
     * 错误信息
     */
    private String errorMsg;
}
