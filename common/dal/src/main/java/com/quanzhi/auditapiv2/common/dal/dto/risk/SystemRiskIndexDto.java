package com.quanzhi.auditapiv2.common.dal.dto.risk;

import com.quanzhi.auditapiv2.common.dal.entity.RiskIndex;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.io.Serializable;

/**
 * @Auther: yangzixian
 * @Date: 2021/8/30 10:22
 * @Description:
 */
@Data
@ApiModel("系统风险指数")
public class SystemRiskIndexDto implements Serializable {

    @ApiModelProperty("系统风险级别(0-健康，1-低风险，2-中风险，3-高风险)")
    private Integer systemRiskLevel;

    @ApiModelProperty("系统风险指数")
    private Integer systemRiskIndex;

    @ApiModelProperty("系统风险趋势(up-上涨，mid-无变化，down-下降)")
    private String systemRiskIndexTrend;

    @Mapper
    public interface SystemRiskIndexDtoMapper {

        SystemRiskIndexDto.SystemRiskIndexDtoMapper INSTANCE = Mappers.getMapper(SystemRiskIndexDto.SystemRiskIndexDtoMapper.class);

        SystemRiskIndexDto convert(RiskIndex riskIndex);
    }

}
