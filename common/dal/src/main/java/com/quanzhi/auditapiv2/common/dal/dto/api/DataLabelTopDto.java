package com.quanzhi.auditapiv2.common.dal.dto.api;

import com.quanzhi.auditapiv2.common.dal.entity.CountEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Data;

import java.util.List;

/**
 * @Auther: yangzixian
 * @Date: 2021/8/30 10:56
 * @Description:
 */
@Data
@Builder
@ApiModel("数据标签榜单")
public class DataLabelTopDto {

    @ApiModelProperty("数据标签列表")
    private List<CountEntity> dataLabelInfoList;

    private String type;

    private String viewType;

}
