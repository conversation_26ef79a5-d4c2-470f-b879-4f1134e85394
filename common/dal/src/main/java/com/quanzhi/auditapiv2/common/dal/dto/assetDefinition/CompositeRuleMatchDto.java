package com.quanzhi.auditapiv2.common.dal.dto.assetDefinition;

import com.quanzhi.metabase.core.model.http.CompositeRule;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * create at 2021/12/31 5:07 下午
 * @description: 资产定义规则匹配dto
 **/
@Data
@ApiModel
public class CompositeRuleMatchDto {


    /**
     * @see CompositeRule.RscTypeEnum
     */
    @ApiModelProperty("资源类型(1:应用 2:接口)")
    private Integer rscType;

    @ApiModelProperty("规则定义")
    private String rule;

    @ApiModelProperty("带正则rule")
    private String regexRule;

    @ApiModelProperty("规则id")
    private String compositeRuleId;

    @ApiModelProperty("参数正则的映射集")
    private List<CompositeRule.RegexParam> regexParams;

    @ApiModelProperty("应用指定路径合并信息")
    private CompositeRule.PathMergeInfo pathMergeInfo;

    private Integer page;

    private Integer limit;

}