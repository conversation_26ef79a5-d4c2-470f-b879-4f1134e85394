package com.quanzhi.auditapiv2.common.dal.dto.customize.tjdx;

import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * @Author: K, 小康
 * @Date: 2024/02/23/上午10:20
 * @Description:
 */
@Data
public class SendApiVisit {
    private Integer orgCode;
    private String batchId;
    private Long sendTimestamp;
    private String commitDeviceIp;
    private String commitDeviceNo;
    private List<ApiVisitData> dataList;
    @Data
    public static class ApiVisitData{
        private String id;
        private String orgId;
        private Long responseTime;//Date转成Long
        private String responseStatus;
        private String requestBody;
        private String responseBody;
        private String accessIp;
        private String appAddress;
        private String appName;
        private String apiUrl;
        private String apiName;
        private String accessPort;
        private String accessMac;//非必填
        private String appAccount;//非必填
        private String appPort;
        private String clientAddress;
        private String appMac;//非必填;
        private String userAgentValue;
        private String operationType;
        private String cookie;
        private String referer;
        private String execResult;
        private Date accessTime;
        private String appIp;
        private List<SensitiveInfo> sensitiveInfo;
    }
    @Data
    public static class SensitiveInfo{
        private Integer count;
        private String sensitiveDataLabName;
        private String sensitiveLie;//非必填
        private String value;//非必填
        private String transferFileUrl;//非必填
        private String transferFileContent;//非必填
    }
}
