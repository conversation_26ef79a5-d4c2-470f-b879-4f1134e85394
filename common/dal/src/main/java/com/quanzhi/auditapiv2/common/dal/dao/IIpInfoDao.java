package com.quanzhi.auditapiv2.common.dal.dao;

import com.quanzhi.auditapiv2.common.dal.dto.IpSearchDetailDto;
import com.quanzhi.auditapiv2.common.dal.dto.IpSearchDto;
import com.quanzhi.auditapiv2.common.dal.dto.common.CommonGroupDto;
import com.quanzhi.auditapiv2.common.dal.dto.query.IpInfoCriteriaDto;
import com.quanzhi.auditapiv2.common.dal.entity.IpInfo;
import org.bson.Document;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;

import java.util.List;

/**
 * @ClassName IIpInfoDao
 * @Description TODO
 * <AUTHOR>
 * @Date 2020/11/21 15:33
 **/
public interface IIpInfoDao {

    List<com.quanzhi.audit_core.common.model.IpInfo> getIpInfoList(IpInfoCriteriaDto ipInfoCriteriaDto);

    List<Document> getIpInfoListFields(IpInfoCriteriaDto ipInfoCriteriaDto, List<String> fields);

    List<com.quanzhi.audit_core.common.model.IpInfo> getIpInfoListByAccount(String account);

    Long getIpInfoCount(IpInfoCriteriaDto ipInfoCriteriaDto);

    Long getCount();

    com.quanzhi.audit_core.common.model.IpInfo getIpInfoByIp(String ip);

    IpInfo findByIP(String ip);

    void update(Query query, Update update);

    List<CommonGroupDto> ipGroup(String groupField);

    long totalCount(IpSearchDto ipSearchDto);

    long totalCount(IpSearchDetailDto ipSearchDetailDto);

    void save(List<IpInfo> ipInfoInfos);
}
