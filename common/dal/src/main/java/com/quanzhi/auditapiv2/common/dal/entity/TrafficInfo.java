package com.quanzhi.auditapiv2.common.dal.entity;

import lombok.Data;

import java.util.List;

@Data
public class TrafficInfo {
    private List<NetworkInfo> networkInfos;
    private String lag;
    private String produceSpeed;

    @Data
    public static  class NetworkInfo{
        private String ip;
        private List<Network> networks;
    }

    @Data
    public static class Network{
        // 网口
        private String ethName;
        // 流量大小
        private long rxSpeed;
        // 丢包率
        private double packetLossRate;;
    }
}
