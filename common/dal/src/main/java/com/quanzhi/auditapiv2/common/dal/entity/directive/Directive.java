package com.quanzhi.auditapiv2.common.dal.entity.directive;

import com.quanzhi.auditapiv2.common.dal.entity.node.ClusterNode;
import lombok.Data;
import org.springframework.http.MediaType;

import java.util.HashMap;
import java.util.Map;

/**
 * @Author: yangzx
 * @Date: 2024/7/3 14:34
 */
@Data
public class Directive {

    private String id;

    private String url;

    private String method;

    private String type;

    private String remark;

    private ClusterNode node;

    private Map<String, String> headers=new HashMap<>();

    private Map<String, Object> requestParams=new HashMap<>();

    private Map<String,Object> multipartformDataParams=new HashMap<>();

    private Object requestBody;

    private boolean syncResult; //同步结果

    private boolean needNotify;

    private boolean retry;

    private String directiveValue;

    private boolean delFlag = false;

    private long firstTime;

    private long lastTime;

}
