package com.quanzhi.auditapiv2.common.dal.dao;

import com.quanzhi.auditapiv2.common.dal.entity.riskEolink.AddStrategyDto;

import java.util.List;

public interface IStrategyInfoDto {


    AddStrategyDto save(AddStrategyDto dto);

    AddStrategyDto update(AddStrategyDto dto);

    void updateStatus(String id, Integer status);

    List<AddStrategyDto> listRiskStrategy(String api, String app);

    AddStrategyDto findById(String id);

    AddStrategyDto findByUuid(String uuid);

    List<AddStrategyDto> listRiskStrategyByPage(Integer page, Integer limit, Integer sort, String sortField, String name, String strategyType);

    long selectCount(String name, String strategyType);

    boolean deleteById(String id);

    void deleteUpdateDelFlag(String id);

    List<AddStrategyDto> selectStrategyByRisk(String riskName, Integer riskLevel);

    AddStrategyDto selectStrategyByAccount(String account);
}
