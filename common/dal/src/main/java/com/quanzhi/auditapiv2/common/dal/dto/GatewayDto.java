package com.quanzhi.auditapiv2.common.dal.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 *
 * 《网关dto》
 *
 *
 * @Project:
 * @Module ID:
 * @Comments:
 * @JDK version used:      <JDK1.8>
 * <AUTHOR> [<EMAIL>]
 * @since 2020-04-08-上午9:56:10
 */
@Data
public class GatewayDto {

    @ApiModelProperty(value = "id", name = "id")
    private String id;

    /**
     * 网关IP
     */
    @ApiModelProperty(value = "网关IP", name = "ip")
    private String ip;

    /**
     * 部署方式
     */
    @ApiModelProperty(value = "部署方式", name = "type")
    private String type;

    /**
     * 配置时间
     */
    @ApiModelProperty(value = "配置时间", name = "timestamp")
    private Long timestamp;

    /**
     * 更新时间
     */
    @ApiModelProperty(value = "更新时间", name = "updateTime")
    private Long updateTime;

    /**
     * 网关版本
     */
    @ApiModelProperty(value = "网关版本", name = "version")
    private GatewayVersion version;

    /**
     * 网关授权状态
     */
    @ApiModelProperty(value = "网关授权状态", name = "licenseConfig")
    private LicenseConfig licenseConfig;

    @Data
    public static class GatewayVersion {

        /**
         * 产品编号
         */
        @ApiModelProperty(value = "产品编号", name = "productId")
        private String productId;

        /**
         * 产品名称
         */
        @ApiModelProperty(value = "产品名称", name = "productName")
        private String productName;

        /**
         * 产品序列
         */
        @ApiModelProperty(value = "产品序列", name = "productSerial")
        private String productSerial;

        /**
         * 旧版本号
         */
        @ApiModelProperty(value = "旧版本号", name = "oldVersion")
        private String oldVersion;

        /**
         * 旧版本说明
         */
        @ApiModelProperty(value = "旧版本说明", name = "oldDesc")
        private String oldDesc;

        /**
         * 版本号
         */
        @ApiModelProperty(value = "版本号", name = "version")
        private String version;

        /**
         * 版本说明
         */
        @ApiModelProperty(value = "版本说明", name = "desc")
        private String desc;

        /**
         * 时间
         */
        @ApiModelProperty(value = "时间", name = "time")
        private Long time;
    }

    @Data
    public static class LicenseConfig {

        /**
         * 产品编号
         */
        @ApiModelProperty(value = "产品编号", name = "productId")
        private String productId;

        /**
         * 产品名称
         */
        @ApiModelProperty(value = "产品名称", name = "productName")
        private String productName;

        /**
         * 版本号
         */
        @ApiModelProperty(value = "版本号", name = "version")
        private String version;

        /**
         * 版本类型
         */
        @ApiModelProperty(value = "版本类型", name = "versionType")
        private String versionType;

        /**
         * 产品序列
         */
        @ApiModelProperty(value = "产品序列", name = "productSerial")
        private String productSerial;

        /**
         * 开始时间(时间戳)
         */
        @ApiModelProperty(value = "开始时间(时间戳)", name = "startTime")
        private String startTime;

        /**
         * 结束时间(时间戳)
         */
        @ApiModelProperty(value = "结束时间(时间戳)", name = "endTime")
        private String endTime;

        /**
         * 签发时间(时间戳)
         */
        @ApiModelProperty(value = "签发时间(时间戳)", name = "issued")
        private String issued;

        /**
         * 当前授权文件状态
         * 0:未授权
         * 1:已授权
         * 2:已过期
         * 3:无法连接IP
         * @see LicenseStateEnum
         */
        @ApiModelProperty(value = "当前授权文件状态", name = "state")
        private Integer state;

        /**
         * 错误码
         */
        @ApiModelProperty(value = "错误码", name = "errorCode")
        private String errorCode;

        @ApiModelProperty(value = "授权流量大小")
        private String flow_rate;
    }

    public static enum LicenseStateEnum {

        /**
         * 未授权
         */
        NO(0),

        /**
         * 已授权
         */
        YES(1),

        /**
         * 已过期
         */
        OVERDUE(2),

        /**
         * 无法连接IP
         */
        ERROR(3);

        private int val;

        LicenseStateEnum(int val) {
            this.val = val;
        }

        public int value() {
            return this.val;
        }
    }
}
