package com.quanzhi.auditapiv2.common.dal.entity;

import lombok.Data;

/**
 * agent信息
 */
@Data
public class Agent {
    /**
     * agentIp
     */
    private String ip;
    /**
     * 网关IP
     */
    private String gatewayIp;
    /**
     * 添加时间
     */
    private Long createTime;
    /**
     * 更新时间
     */
    private Long updateTime;
    /**
     * Agent状态
     * 0: 关闭
     * 1: 启用
     * default: 1
     */
    private Integer status;
}
