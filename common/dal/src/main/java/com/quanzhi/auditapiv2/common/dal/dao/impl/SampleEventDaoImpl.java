package com.quanzhi.auditapiv2.common.dal.dao.impl;

import com.quanzhi.auditapiv2.common.dal.dao.ISampleEventDao;
import com.quanzhi.auditapiv2.common.dal.dao.base.impl.MetabaseDaoImpl;
import com.quanzhi.auditapiv2.common.dal.dto.sample.SampleSearchDto;
import com.quanzhi.auditapiv2.common.util.utils.ConstantUtil;
import com.quanzhi.auditapiv2.common.util.utils.DataUtil;
import com.quanzhi.auditapiv2.common.util.utils.UrlUtil;
import com.quanzhi.metabase.core.model.http.CompositeRule;
import com.quanzhi.metabase.core.model.http.HttpApiSample;
import com.quanzhi.metabase.core.model.http.SampleErrorEvent;
import com.quanzhi.metabase.core.model.query.MetabaseQuery;
import com.quanzhi.metabase.core.model.query.Predicate;
import com.quanzhi.metabase.core.model.query.Sort;
import com.quanzhi.metabase.core.model.query.SortOrder;
import lombok.extern.slf4j.Slf4j;
import org.docx4j.wml.P;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Repository;
import org.springframework.util.CollectionUtils;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
@Repository("sampleEventDao")
public class SampleEventDaoImpl extends MetabaseDaoImpl<HttpApiSample> implements ISampleEventDao {

    private final MongoTemplate mongoTemplate;

    public SampleEventDaoImpl(MongoTemplate mongoTemplate) {
        this.mongoTemplate = mongoTemplate;
    }

    @Override
    public HttpApiSample getOneSampleEventByUri(String uri) {

        Map<String, String> queryMap = new HashMap<>();
        queryMap.put("uri", uri);

        return findQueryOne(queryMap);
    }

    @Override
    public HttpApiSample getLastSampleEventByUri(String uri) {
        Query query = new Query();
        query.addCriteria(Criteria.where("uri").is(uri));
        //排序
        org.springframework.data.domain.Sort dbSort = org.springframework.data.domain.Sort.by(org.springframework.data.domain.Sort.Direction.DESC, "timestamp");
        query.with(dbSort);
        return mongoTemplate.findOne(query, HttpApiSample.class, "httpSample");
    }

    @Override
    public long getCount(String uri) {

        MetabaseQuery query = new MetabaseQuery();
        query.where("type", Predicate.IS, "LABEL");
        if (DataUtil.isNotEmpty(uri)) {
            query.where("uri", Predicate.IS, uri);
        }
        return getCount(query);
    }

    @Override
    public List<HttpApiSample> getSampleEventList(String uri, Integer page, Integer limit) {
        MetabaseQuery query = new MetabaseQuery();

        query.setSkip((page - 1) * limit);
        query.limit(limit);

        query.where("type", Predicate.IS, "LABEL");
        if (DataUtil.isNotEmpty(uri)) {
            query.where("uri", Predicate.IS, uri);
        }

        //按照标签数量排序
        // query.sort(Sort.by("dataLabelCount", SortOrder.DESC));

        return getList(query);
    }

    @Override
    public Long getSnapshotSampleCount(String uri, List<Long> startTimestampList, List<String> reqDataLabels, List<String> rspDataLabels) {

        MetabaseQuery query = new MetabaseQuery();
        query.where("type", Predicate.IS, "LABEL");
        if (DataUtil.isNotEmpty(uri)) {
            query.where("uri", Predicate.IS, uri);
        }

        if (DataUtil.isNotEmpty(startTimestampList)) {
            query.where("startTimestamp", Predicate.IN, startTimestampList);
        }

        if (DataUtil.isNotEmpty(reqDataLabels)) {
            query.where("reqDataLabels", Predicate.ALL, reqDataLabels);
        }

        if (DataUtil.isNotEmpty(rspDataLabels)) {
            query.where("rspDataLabels", Predicate.ALL, rspDataLabels);
        }


        return getCount(query);
    }

    private MetabaseQuery fillingCommonSampleQuery(SampleSearchDto sampleSearchDto) {
        MetabaseQuery query = new MetabaseQuery();
        //query.where("type", Predicate.IS, "LABEL");
        if (sampleSearchDto != null) {

            if (DataUtil.isNotEmpty(sampleSearchDto.getUri())) {
                query.where("uri", Predicate.IS, sampleSearchDto.getUri());
            }

            if (DataUtil.isNotEmpty(sampleSearchDto.getReqDataLabels())) {
                query.where("reqDataLabels", Predicate.ALL, sampleSearchDto.getReqDataLabels());
            }

            if (DataUtil.isNotEmpty(sampleSearchDto.getRspDataLabels())) {
                query.where("rspDataLabels", Predicate.ALL, sampleSearchDto.getRspDataLabels());
            }
            if (DataUtil.isNotEmpty(sampleSearchDto.getSampleId())) {
                query.where("_id", Predicate.IS, sampleSearchDto.getSampleId());
            }
            if (!CollectionUtils.isEmpty(sampleSearchDto.getFields())) {
                query.fields(sampleSearchDto.getFields());
            }
            if (DataUtil.isNotEmpty(sampleSearchDto.getApiUrl())) {
                try {
                    String apiUrl = sampleSearchDto.getApiUrl();
                    //方法拆分
                    if (apiUrl.contains("#")) {
                        String method = apiUrl.split("#")[1];
                        query.where("req.method", Predicate.IS, method);
                    }
                    //参数拆分   post.password=xxx&get.appid=ccg&header.username=zhouchao
                    if (apiUrl.contains(";")) {
                        String s = apiUrl.split(";")[0];
                        String paramStr = apiUrl.substring(s.length() + 1);
                       // String[] paramArr = paramStr.split("&");
                        List<String> paramArr = UrlUtil.parseParamString(paramStr);
                        for (String param : paramArr) {
                            String key = param.split("=")[0];
                            String value = param.substring(key.length() + 1);
                            String location = key.split("\\.")[0];
                            String pm = key.split("\\.")[1];
                            if ("get".equals(location)) {
                                query.where("req.getArgs." + pm, Predicate.IS, value);
                            } else if ("post".equals(location)) {
                                query.where("req.postArgs." + pm, Predicate.IS, value);
                            } else if ("header".equals(location)) {
                                query.where("req.header." + pm, Predicate.IS, value);
                            }
                        }
                    }
                } catch (Exception e) {
                    log.error("fillingCommonSampleQuery api query error:", e);
                }

            }
        }
        return query;
    }


    @Override
    public Long getSnapshotSampleCount(SampleSearchDto sampleSearchDto) {
        MetabaseQuery query = fillingCommonSampleQuery(sampleSearchDto);
        return getCount(query);
    }

    @Override
    public List<HttpApiSample> getSnapshotSampleList(SampleSearchDto sampleSearchDto) {
        MetabaseQuery query = fillingCommonSampleQuery(sampleSearchDto);
        if (sampleSearchDto.getPage() != null && sampleSearchDto.getLimit() != null) {
            query.setSkip((sampleSearchDto.getPage() - 1) * sampleSearchDto.getLimit());
            query.limit(sampleSearchDto.getLimit());
        }
        //按照时间戳降序排序
        //query.sort(Sort.by("timestamp", SortOrder.DESC));
        List<HttpApiSample> list = getList(query);
        return list;
    }

    @Override
    public List<HttpApiSample> getSnapshotSampleList(String uri, String apiUrl, Integer page, Integer limit, List<Long> startTimestampList, List<String> reqDataLabels, List<String> rspDataLabels, List<String> fields) {
        SampleSearchDto sampleSearchDto = new SampleSearchDto();
        sampleSearchDto.setUri(uri);
        sampleSearchDto.setApiUrl(apiUrl);
        sampleSearchDto.setPage(page);
        sampleSearchDto.setLimit(limit);
        sampleSearchDto.setReqDataLabels(reqDataLabels);
        sampleSearchDto.setRspDataLabels(rspDataLabels);
        sampleSearchDto.setFields(fields);
        return getSnapshotSampleList(sampleSearchDto);
    }


    @Override
    public long countSampleListByComposite(String uri, String apiUrl, Integer compositeType) {
        MetabaseQuery query = new MetabaseQuery();
        //填充查询条件
        fillingSampleQuery(query, uri, apiUrl, compositeType, null);
        return getCount(query);
    }

    @Override
    public long countSampleListByComposite(SampleSearchDto sampleSearchDto) {
        MetabaseQuery query = new MetabaseQuery();
        //填充查询条件
        fillingSampleQuery(query, sampleSearchDto.getUri(), sampleSearchDto.getApiUrl(), sampleSearchDto.getCompositeType(), sampleSearchDto.getSampleId());
        return getCount(query);
    }

    @Override
    public HttpApiSample getSampleByApiUrl(String apiUrl) {
        MetabaseQuery query = new MetabaseQuery();

        query.where("apiUrl", Predicate.IS, apiUrl);

        return metabaseClientTemplate.findOne(query, HttpApiSample.class);
    }

    @Override
    public HttpApiSample getSampleByUri(String uri) {
        MetabaseQuery query = new MetabaseQuery();

        query.where("uri", Predicate.IS, uri);

        return metabaseClientTemplate.findOne(query, HttpApiSample.class);
    }

    @Override
    public HttpApiSample getSampleByHost(String host) {
        MetabaseQuery query = new MetabaseQuery();

        query.where("host", Predicate.IS, host);

        return metabaseClientTemplate.findOne(query, HttpApiSample.class);
    }

    @Override
    public List<HttpApiSample> getSamplesByApiUrl(String apiUrl) {
        MetabaseQuery query = new MetabaseQuery();

        query.where("apiUrl", Predicate.IS, apiUrl);

        return metabaseClientTemplate.find(query, HttpApiSample.class);
    }

    /**
     * @param apiUrl
     * @param page
     * @param limit
     * <AUTHOR>
     * @description: 资产定义接口样例查看
     * @date: 2021/9/23
     * @Return java.util.List<com.quanzhi.metabase.core.model.http.HttpApiSample>
     */
    @Override
    public List<HttpApiSample> getSampleListByComposite(String apiUrl, String uri, Integer page, Integer limit, Integer compositeType) {
        MetabaseQuery query = new MetabaseQuery();
        query.setSkip((page - 1) * limit);
        query.limit(limit);
        //填充查询条件
        fillingSampleQuery(query, uri, apiUrl, compositeType, null);
        //按照时间戳降序排序
        query.sort(Sort.by("timestamp", SortOrder.DESC));
        return getList(query);
    }

    @Override
    public List<HttpApiSample> getSampleListByComposite(SampleSearchDto sampleSearchDto) {
        MetabaseQuery query = new MetabaseQuery();
        if (DataUtil.isNotEmpty(sampleSearchDto.getPage()) && DataUtil.isNotEmpty(sampleSearchDto.getLimit())) {
            query.setSkip((sampleSearchDto.getPage() - 1) * sampleSearchDto.getLimit());
            query.limit(sampleSearchDto.getLimit());
        }
        //填充查询条件
        fillingSampleQuery(query, sampleSearchDto.getUri(), sampleSearchDto.getApiUrl(), sampleSearchDto.getCompositeType(), sampleSearchDto.getSampleId());
        //按照时间戳降序排序
       // query.sort(Sort.by("timestamp", SortOrder.DESC));
        return getList(query);
    }

    public void fillingSampleQuery(MetabaseQuery query, String uri, String apiUrl, Integer compositeType, String sampleId) {
        query.where("type", Predicate.IS, "LABEL");
        if (DataUtil.isNotEmpty(uri)) {
            query.where("uri", Predicate.IS, uri);
        }
        if (DataUtil.isNotEmpty(sampleId)) {
            query.where("_id", Predicate.IS, sampleId);
        }
        if (DataUtil.isNotEmpty(compositeType)) {
            //合并
            if (CompositeRule.CompositeTypeEnum.MERGE.getCompositeType().equals(compositeType)) {
                /*if (apiUrl.contains("$(param")) {
                    query.where("uri", Predicate.IS, "httpapi:" + apiUrl);
                } else {*/
                query.where("apiUrl", Predicate.IS, apiUrl);
                //}
            }
            //拆分
            if (CompositeRule.CompositeTypeEnum.SPLIT.getCompositeType().equals(compositeType) && !uri.contains(";") && !uri.contains("#")) {
                //带;和#说明已经拆出来了，不用在拼
                getApiSplitQuery(apiUrl, query);
            }
        }
    }


    /**
     * @param apiUrl
     * @param query
     * <AUTHOR>
     * @description: 拼接接口参数拆分查询条件
     * @date: 2021/9/23
     * @Return void
     */
    private void getApiSplitQuery(String apiUrl, MetabaseQuery query) {
        //参数拆分   post.password=xxx&get.appid=ccg&header.username=zhouchao
        //异常 http://www.shen.cn/main;post.password=&2ZPJdMB0X  这种参数里带&的很难区分，先不处理
        if (apiUrl.contains(";")) {
            String url = null;
            try {
                url = apiUrl.split(";")[0];
                String paramStr = apiUrl.substring(url.length() + 1);
               // String[] paramArr = paramStr.split("&");
                List<String> paramArr =UrlUtil.parseParamString(paramStr);
                for (String param : paramArr) {
                    String key = param.split("=")[0];
                    String value = param.substring(key.length() + 1);
                    String location = key.split("\\.")[0];
                    String pm = key.split("\\.")[1];
                    if ("get".equals(location)) {
                        query.where("req.getArgs." + pm, Predicate.IS, value);
                    } else if ("post".equals(location)) {
                        query.where("req.postArgs." + pm, Predicate.IS, value);
                    } else if ("header".equals(location)) {
                        query.where("req.header." + pm, Predicate.IS, value);
                    }
                }
            } catch (Exception e) {
                log.error("getApiSplitQuery error:", e);
            }
            query.where("apiUrl", Predicate.IS, url);
        } else if (apiUrl.contains("#")) {
            //方法拆分
            String url = apiUrl.split("#")[0];
            String method = apiUrl.split("#")[1];
            query.where("req.method", Predicate.IS, method);
            query.where("apiUrl", Predicate.IS, url);
        }

    }

    private MetabaseQuery fillSampleErrorQuery(SampleSearchDto sampleSearchDto) {
        MetabaseQuery query = new MetabaseQuery();
        if (DataUtil.isNotEmpty(sampleSearchDto.getType())) {
            query.where("type", Predicate.IS, sampleSearchDto.getType());
        }
        if (DataUtil.isNotEmpty(sampleSearchDto.getUri())) {
            query.where("uri", Predicate.IS, sampleSearchDto.getUri());
        }
        if (DataUtil.isNotEmpty(sampleSearchDto.getSampleId())) {
            query.where("sampleId", Predicate.IS, sampleSearchDto.getSampleId());
        }
        return query;
    }


    @Override
    public long countSampleListFromError(SampleSearchDto sampleSearchDto) {
        MetabaseQuery query = fillSampleErrorQuery(sampleSearchDto);
        Long count = metabaseClientTemplate.count(query, "sampleErrorEvent");
        return count;
    }


    @Override
    public List<SampleErrorEvent> getSampleListFromError(SampleSearchDto sampleSearchDto) {
        MetabaseQuery query = fillSampleErrorQuery(sampleSearchDto);
        if (DataUtil.isNotEmpty(sampleSearchDto.getPage()) && DataUtil.isNotEmpty(sampleSearchDto.getLimit())) {
            query.limit(sampleSearchDto.getLimit());
            query.skip((sampleSearchDto.getPage() - 1) * sampleSearchDto.getLimit());
        }
        List<SampleErrorEvent> sampleErrorEvents = metabaseClientTemplate.find(query, SampleErrorEvent.class);
        return sampleErrorEvents;
    }
}
