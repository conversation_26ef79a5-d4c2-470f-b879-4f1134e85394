package com.quanzhi.auditapiv2.common.dal.dto;

import com.quanzhi.metabase.core.model.http.CompositeRule;
import lombok.Data;

import java.util.List;
import java.util.Set;

/**
 * @Author: Linlm
 * @Description:
 * @Date: Created in 2020/5/15 下午2:27
 */
@Data
public class CompositeRuleDto {

    private String id;

    /**
     * 标识合并或拆分：1-合并，2-拆分
     */
    private Integer compositeType;

    /**
     * 资源类型：1-应用，2-接口
     */
    private Integer rscType;

    /**
     * 资源匹配规则:为空时表示撤销之前的操作，否则按给定的规则进行合并或拆分
     */
    private String rule;

    /**
     * 接口对应的host
     */
    private String host;

    /**
     * example类型:1-数字，2-字符串
     */
    @Deprecated
    private Integer exampleType;

    /**
     * 资源名称
     * 用于替换原始url
     */
    private String rscName;

    /**
     * 优先级，如: /1/2/3/4/5
     * 若接口/应用符合多个规则，返回优先级最高的规则定义的资源名称
     */
    private Integer priorityLevel;

    /**
     * 清单
     * 规则的一个可选匹配集合，定义清单后表示仅匹配该集合中的内容
     */
    @Deprecated
    private Set<String> rscSet;

    /**
     * 合并或拆分时间
     */
    private String executeTime;
    private Long createTime;
    private Long updateTime;

    /**
     * 是否撤销当前规则引起的合并/拆分操作
     */
    private Boolean isRepeal;

    /**
     * 含正则的rule
     */
    private String regexRule;

    /**
     * 正则表达式参数
     */
    private List<CompositeRule.RegexParam> regexParams;

    /**
     * 备份rule
     */
    @Deprecated
    private String backupRule;
    /**
     * 建议
     */
    private String suggestion;
    /**
     * 状态
     */
    private String state;

    /**
     * 应用指定路径合并信息
     */
    private CompositeRule.PathMergeInfo pathMergeInfo;

    private int version = 0;


}
