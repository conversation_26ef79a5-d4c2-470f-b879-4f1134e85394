package com.quanzhi.auditapiv2.common.dal.dao.impl;

import com.quanzhi.auditapiv2.common.dal.dao.OpenApiDao;
import com.quanzhi.auditapiv2.common.dal.dao.base.impl.BaseDaoImpl;
import com.quanzhi.auditapiv2.common.dal.entity.OpenApiConfig;
import com.quanzhi.auditapiv2.common.util.utils.DesensitizationUtil;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * @author: yangzixian
 * @date: 11/4/2023 19:37
 * @description:
 */
@Repository
public class OpenApiDaoImpl extends BaseDaoImpl<OpenApiConfig> implements OpenApiDao {

    private final MongoTemplate mongoTemplate;

    String tableName = "openapiKey";

    public OpenApiDaoImpl(MongoTemplate mongoTemplate) {
        this.mongoTemplate = mongoTemplate;
    }

    public OpenApiConfig findOneOpenApiKey(String callerId) {
        Query query = new Query(Criteria.where("callerId").is(callerId));
        return mongoTemplate.findOne(query, OpenApiConfig.class, tableName);
    }

    @Override
    public void editCallerID(String callerId, Long deadLine) {
        Query query = new Query(Criteria.where("callerId").is(callerId));
        Update update = new Update();
        update.set("deadline", deadLine);
        mongoTemplate.updateFirst(query, update, tableName);
    }

    public void saveOpenApiKey(OpenApiConfig openApiConfig) {
        mongoTemplate.save(openApiConfig, tableName);
    }

    public void delOpenApiKey(String callerId) {
        Query query = new Query(Criteria.where("callerId").is(callerId));
        mongoTemplate.remove(query, tableName);
    }

    @Override
    public List<OpenApiConfig> pageOpenApiKey(Integer page, Integer limit) {

        Query query = new Query();
        query.with(Sort.by(Sort.Direction.DESC, "createTime"));
        query.skip((page - 1) * limit);
        query.limit(limit);
        query.fields().include("callerId").include("deadline").include("createTime");
        return mongoTemplate.find(query, OpenApiConfig.class, tableName);
    }

    @Override
    public OpenApiConfig desensiOpenApiKey(String sensiAccesskey, String sensiSecretKey) {
        OpenApiConfig openApiConfig = new OpenApiConfig();
        String accessKey = DesensitizationUtil.sensitize(sensiAccesskey);
        String secretKey = DesensitizationUtil.sensitize(sensiSecretKey);
        openApiConfig.setAccessKey(accessKey);
        openApiConfig.setSecretKey(secretKey);
        return openApiConfig;
    }

}
