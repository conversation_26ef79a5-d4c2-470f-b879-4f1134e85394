package com.quanzhi.auditapiv2.common.dal.dao.impl;

import com.mongodb.BasicDBObject;
import com.quanzhi.auditapiv2.common.dal.dao.IIndexDefine;
import com.quanzhi.auditapiv2.common.dal.dao.IResourceChangedEventDao;
import com.quanzhi.auditapiv2.common.dal.dao.base.impl.BaseDaoImpl;
import com.quanzhi.auditapiv2.common.dal.entity.ResourceChangeEventModel;
import org.springframework.stereotype.Repository;

/**
 * <AUTHOR>
 * @date 2020/8/18 下午2:49
 */
@Repository
public class ResourceChangedEventDaoImpl extends BaseDaoImpl<ResourceChangeEventModel> implements IResourceChangedEventDao , IIndexDefine {

    @Override
    public void createIndex() {
        mongoTemplate.getCollection(getCollectionName()).createIndex(new BasicDBObject("timestamp", 1));

        mongoTemplate.getCollection(getCollectionName()).createIndex(new BasicDBObject("createTime", -1));
    }
}
