package com.quanzhi.auditapiv2.common.dal.entity;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 *
 * 《系统升级日志》
 *
 *
 * @Project:
 * @Module ID:
 * @Comments:
 * @JDK version used:      <JDK1.8>
 * <AUTHOR> [<EMAIL>]
 * @since 2020-04-08-上午9:56:10
 */
@Data
public class SysUpdateLog {

    @ApiModelProperty(value = "id", name = "id")
    private String id;

    /**
     * 升级状态
     * @see StatusEnum
     */
    @ApiModelProperty(value = "升级状态", name = "updateStatus")
    private Integer updateStatus;

    /**
     * 升级进度
     */
    @ApiModelProperty(value = "升级进度", name = "updateProgress")
    private Integer updateProgress;

    /**
     * 升级日志内容
     */
    @ApiModelProperty(value = "升级日志内容", name = "updateLog")
    private String updateLog;

    /**
     * 回滚状态
     * @see StatusEnum
     */
    @ApiModelProperty(value = "回滚状态", name = "rollbackStatus")
    private Integer rollbackStatus;

    /**
     * 回滚进度
     */
    @ApiModelProperty(value = "回滚进度", name = "rollbackProgress")
    private Integer rollbackProgress;

    /**
     * 回滚日志内容
     */
    @ApiModelProperty(value = "回滚日志内容", name = "rollbackLog")
    private String rollbackLog;

    /**
     * 当前版本号
     */
    @ApiModelProperty(value = "当前版本号", name = "version")
    private String version;

    /**
     * 旧版本号
     */
    @ApiModelProperty(value = "旧版本号", name = "oldVersion")
    private String oldVersion;

    /**
     * 创建者
     */
    @ApiModelProperty(value = "创建者", name = "createBy")
    private String createBy;

    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间", name = "createDate")
    private Long createDate;

    /**
     * 状态枚举类
     */
    public static enum StatusEnum {

        /**
         * 进行中
         */
        ONGOING(1, "进行中"),

        /**
         * 成功
         */
        SUCCESS(2, "成功"),

        /**
         * 失败
         */
        FAIL(3, "失败");

        private Integer status;

        private String value;

        StatusEnum(Integer status, String value) {
            this.status = status;
            this.value = value;
        }

        public Integer status() {
            return this.status;
        }

        public String value() {
            return this.value;
        }

        /**
         * 根据status获取枚举对象
         */
        public static StatusEnum getStatusEnum(Integer status){

            for (StatusEnum statusEnum : StatusEnum.values()) {
                if(statusEnum.status.equals(status)){
                    return statusEnum;
                }
            }
            return null;
        }
    }
}
