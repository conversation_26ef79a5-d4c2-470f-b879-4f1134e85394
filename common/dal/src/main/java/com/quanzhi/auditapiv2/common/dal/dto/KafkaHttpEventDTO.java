package com.quanzhi.auditapiv2.common.dal.dto;

import com.quanzhi.audit_core.common.model.HttpEvent;
import com.quanzhi.audit_core.common.model.Net;
import lombok.Data;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * @author: yangzixian
 * @date: 13/4/2023 17:43
 * @description:
 */
@Data
public class KafkaHttpEventDTO {

    private HttpEvent.HttpRequest req;

    private Net net;

    public String account;

    private HttpEvent.HttpResponse rsp;

    private String ip;

    private Long timestamp;

    private String apiName;

    private String apiUrl;

    private String apiLevel;

    private String host;

    private String appId;

    private String appUri;

    private String ua;

    private String referer;

    private Set<String> reqDataLabelIds;

    private Set<String> rspDataLabelIds;

    private String uaType;

    private String reqContentType;

    private String rspContentType;

    private Long rspContentLength;

    private List<String> classifications;

    private String appName;

    private List<String> featureLabels;

    // 敏感标签值
    private Map<String, Map<String, Set<String>>> labelValues;

}
