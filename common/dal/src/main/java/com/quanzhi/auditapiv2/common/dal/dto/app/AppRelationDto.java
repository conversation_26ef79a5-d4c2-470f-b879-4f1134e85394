package com.quanzhi.auditapiv2.common.dal.dto.app;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Data;

/**
 * @Auther: yangzixian
 * @Date: 2021/8/30 10:36
 * @Description:
 */
@Data
@Builder
@ApiModel("应用关系")
public class AppRelationDto {

    @ApiModelProperty("境外访问境外")
    private Long AbroadToAbroad;

    @ApiModelProperty("境外访问境内")
    private Long AbroadToTerritory;

    @ApiModelProperty("境外访问局域网")
    private Long AbroadToLocal;

    @ApiModelProperty("局域网访问局域网")
    private Long LocalToLocal;

    @ApiModelProperty("局域网访问境内")
    private Long LocalToTerritory;

    @ApiModelProperty("局域网访问境外")
    private Long LocalToAbroad;

    @ApiModelProperty("境内访问境内")
    private Long TerritoryToTerritory;

    @ApiModelProperty("境内访问境外")
    private Long TerritoryToAbroad;

    @ApiModelProperty("境内访问局域网")
    private Long TerritoryToLocal;

}
