package com.quanzhi.auditapiv2.common.dal.dao;

import com.quanzhi.auditapiv2.common.dal.dao.base.IMetabaseDao;
import com.quanzhi.auditapiv2.common.dal.dto.sample.SampleSearchDto;
import com.quanzhi.metabase.core.model.http.HttpApiSample;
import com.quanzhi.metabase.core.model.http.SampleErrorEvent;

import java.util.List;

public interface ISampleEventDao extends IMetabaseDao<HttpApiSample> {

    HttpApiSample getOneSampleEventByUri(String uri);
    HttpApiSample getLastSampleEventByUri(String uri);

    long getCount(String uri);

    List<HttpApiSample> getSampleEventList(String uri, Integer page, Integer limit);

    Long getSnapshotSampleCount(String uri, List<Long> startTimestampList, List<String> reqDataLabels, List<String> rspDataLabels);

    Long getSnapshotSampleCount(SampleSearchDto sampleSearchDto);

    List<HttpApiSample> getSnapshotSampleList(String uri, String apiUrl, Integer page, Integer limit, List<Long> startTimestampList, List<String> reqDataLabels, List<String> rspDataLabels, List<String> fields);

    List<HttpApiSample> getSnapshotSampleList(SampleSearchDto sampleSearchDto);

    List<HttpApiSample> getSampleListByComposite(String apiUrl, String uri, Integer page, Integer limit, Integer compositeType);
    List<HttpApiSample> getSampleListByComposite(SampleSearchDto sampleSearchDto);

    long countSampleListByComposite(String uri, String apiUrl, Integer compositeType);

    long countSampleListByComposite(SampleSearchDto sampleSearchDto);

    HttpApiSample getSampleByApiUrl(String apiUrl);
    HttpApiSample getSampleByUri(String uri);

    HttpApiSample getSampleByHost(String host);

    List<HttpApiSample> getSamplesByApiUrl(String apiUrl);

    long countSampleListFromError(SampleSearchDto sampleSearchDto);

    List<SampleErrorEvent> getSampleListFromError(SampleSearchDto sampleSearchDto);
}
