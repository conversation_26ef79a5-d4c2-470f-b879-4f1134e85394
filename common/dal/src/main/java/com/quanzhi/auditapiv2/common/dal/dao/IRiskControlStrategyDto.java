package com.quanzhi.auditapiv2.common.dal.dao;

import com.quanzhi.auditapiv2.common.dal.entity.riskEolink.StrategyDataDto;
import com.quanzhi.auditapiv2.common.dal.entity.riskEolink.StrategyDto;

import java.util.List;

/**
 * eolink新增管控策略API留存数据
 */
public interface IRiskControlStrategyDto {

    /**
     * 策略列表
     * @return
     */
    List<StrategyDataDto> getListStrategy(Integer page, Integer limit, Integer sort, String sortField, String name, String strategyType);


    long getCountStrategy(String name);
    /**
     * 新增策略
     * @param strategyDto
     * @return
     */
    void addStrategy(StrategyDto strategyDto);

    /**
     * 删除策略
     * @param uuid
     * @return
     */
    void deleteStrategy(String uuid);

    /**
     * 修改策略
     * @param strategyDto
     * @return
     */
    StrategyDataDto editStrategy(StrategyDto strategyDto);

    StrategyDto getStrategy(String blockId);
}
