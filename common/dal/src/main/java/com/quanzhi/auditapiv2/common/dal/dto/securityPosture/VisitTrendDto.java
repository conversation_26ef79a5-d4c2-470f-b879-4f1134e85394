package com.quanzhi.auditapiv2.common.dal.dto.securityPosture;

import com.quanzhi.auditapiv2.common.dal.entity.securityPosture.VisitTrend;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Data;

import java.util.List;

/**
 * @Auther: yangzixian
 * @Date: 2021/10/15 14:02
 * @Description:
 */
@Data
@Builder
@ApiModel("访问趋势")
public class VisitTrendDto {
    @ApiModelProperty("过去一分钟访问趋势 - 间隔5秒")
    private List<VisitTrend> visitTrendList;
}
