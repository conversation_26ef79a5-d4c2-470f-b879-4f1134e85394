package com.quanzhi.auditapiv2.common.dal.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * @author: zhousong
 * @data: 2018/8/13
 * 溯源任务
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ToolConfig {
    private String id;

    /**
     * name
     */
    private String name;

    /**
     * keyword
     */
    private String keyword;

    /**
     * 开始时间
     */
    private Long createTime;

    /**
     * savePath
     */
    private String savePath;

    /**
     * saveName
     */
    private String saveName;

    /**
     * resultType
     */
    private String resultType;

    /**
     * cmd
     */
    private String cmd;

    /**
     * scriptName
     */
    private String scriptName;

    /**
     * scriptPath
     */
    private String scriptPath;

    /**
     * desc
     */
    private String desc;

    /**
     * 结束时间
     */
    private String paramType;

    /**
     * 敏感标签
     */
    private List<String> requireParams;

}
