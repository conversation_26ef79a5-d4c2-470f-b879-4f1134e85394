package com.quanzhi.auditapiv2.common.dal.dao.impl.account;

import com.quanzhi.auditapiv2.common.dal.dao.account.AccountDateInfoDao;
import com.quanzhi.auditapiv2.common.dal.entity.AccountDateInfo;
import org.bson.Document;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Repository;

import java.util.*;

@Repository
public class AccountDateInfoDaoImpl implements AccountDateInfoDao {

    private final MongoTemplate mongoTemplate;

    private final String ACCOUNT_DATE_INFO_COLLECTION = "accountDateInfo";


    public AccountDateInfoDaoImpl(MongoTemplate mongoTemplate) {
        this.mongoTemplate = mongoTemplate;
    }

    @Override
    public Iterator<AccountDateInfo> iteratorByUpdateTime(long start, long end, int size, String date, String account) {
        Query query = new Query();
        if (start > 0 && end > 0) {
            query.addCriteria(Criteria.where("updateTime").gte(start).lte(end));
        }
        if (date != null) {
            query.addCriteria(Criteria.where("date").is(date));
        }
        if (account != null) {
            query.addCriteria(Criteria.where("account").is(account));
        }
        return mongoTemplate.getCollection(ACCOUNT_DATE_INFO_COLLECTION)
                .aggregate(Collections.singletonList(new Document("$match", query.getQueryObject())), AccountDateInfo.class)
                .batchSize(size).iterator();
    }
//    @Override
//    public void updateState(List<AccountDateInfo> accountDateInfos) {
//        BulkOperations accountOperations = mongoTemplate.bulkOps(BulkOperations.BulkMode.UNORDERED, ACCOUNT_DATE_INFO_COLLECTION);
//        List<Pair<Query, Update>> updateList = new ArrayList<>();
//        accountDateInfos.forEach(accountInfo -> {
//            Query query = Query.query(Criteria.where("_id").is(accountInfo.getId()));
//            Update update = Update.update("lastState", accountInfo.getLastState());
//            updateList.add(Pair.of(query, update));
//        });
//        // 批量更新
//        if (com.quanzhi.metabase.common.utils.DataUtil.isNotEmpty(updateList)) {
//            accountOperations.upsert(updateList);
//            accountOperations.execute();
//        }
//    }
}
