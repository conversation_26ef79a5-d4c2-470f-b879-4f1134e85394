package com.quanzhi.auditapiv2.common.dal.dao.impl;

import com.quanzhi.auditapiv2.common.dal.dao.IResourceFilterDao;
import com.quanzhi.auditapiv2.common.dal.dao.base.impl.MetabaseDaoImpl;
import com.quanzhi.metabase.core.model.filter.AssetFilterRule;
import com.quanzhi.metabase.core.model.filter.FilterHttpEventStat;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Repository;

@Repository
public class ResourceFilterDaoImpl extends MetabaseDaoImpl<AssetFilterRule> implements IResourceFilterDao {

    @Autowired
    private MongoTemplate mongoTemplate;

    @Override
    public FilterHttpEventStat getFilterStat(String ruleId) {
        Query query = Query.query(Criteria.where("ruleId").is(ruleId));
        return mongoTemplate.findOne(query, FilterHttpEventStat.class, "filterHttpEventStat");
    }
}
