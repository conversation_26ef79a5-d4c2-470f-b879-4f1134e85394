package com.quanzhi.auditapiv2.common.dal.dao.impl;

import com.quanzhi.audit_core.common.model.DataLabel;
import com.quanzhi.auditapiv2.common.dal.dao.IDataLabelNacosDao;
import com.quanzhi.auditapiv2.common.dal.dao.base.impl.NacosBaseDaoImpl;
import com.quanzhi.auditapiv2.common.dal.dto.DataLabelSearchDto;
import com.quanzhi.auditapiv2.common.util.utils.DataUtil;
import org.springframework.stereotype.Repository;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

@Repository("dataLabelNacosDao")
public class DataLabelNacosDaoImpl extends NacosBaseDaoImpl<DataLabel> implements IDataLabelNacosDao {

    @Override
    public Boolean checkNameExist(String name,String excludeId) {

        List<DataLabel> dataLabelModelList = getAll();

        for(int i = 0; i < dataLabelModelList.size();i++) {

            DataLabel dataLabelModel = dataLabelModelList.get(i);

            if(!dataLabelModel.getId().equals(excludeId) && dataLabelModel.getName().equals(name)
                    && (DataUtil.isEmpty(dataLabelModel.getDelFlag()) || dataLabelModel.getDelFlag() == 0)) {
                return true;
            }
        }

        return false;
    }

    @Override
    public long getCount(DataLabelSearchDto searchDto) {

        List<DataLabel> dataLabelModelList = getAll();

        List<DataLabel> newList = new ArrayList<DataLabel>();

        for(int i = 0; i < dataLabelModelList.size();i++) {

            DataLabel dataLabelModel = dataLabelModelList.get(i);

            List<Boolean> addToSearchList = new ArrayList<Boolean>();

            if (DataUtil.isNotEmpty(searchDto.getIdList()) && searchDto.getIdList().contains(dataLabelModel.getId())) {
                addToSearchList.add(true);
            } else {
                addToSearchList.add(false);
            }

            if (DataUtil.isNotEmpty(searchDto.getType()) && searchDto.getType().equals(dataLabelModel.getType())) {
                addToSearchList.add(true);
            } else {
                addToSearchList.add(false);
            }

            if(!addToSearchList.contains(false)) {
                newList.add(dataLabelModel);
            }
        }

        return newList.size();
    }

    @Override
    public Optional<DataLabel> convert(String labelId) {
        List<DataLabel> dataLabels = getAll();
        Optional<DataLabel> dataLabelOp = dataLabels.stream().filter(dataLabel -> dataLabel.getId() != null
                && dataLabel.getId().equals(labelId)).findAny();
        return dataLabelOp;
    }
}
