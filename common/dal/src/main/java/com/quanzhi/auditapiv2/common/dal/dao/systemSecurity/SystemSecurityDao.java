package com.quanzhi.auditapiv2.common.dal.dao.systemSecurity;

import com.quanzhi.auditapiv2.common.dal.dto.systemSecurity.SystemSecurityDto;
import com.quanzhi.auditapiv2.common.dal.entity.systemSecurity.SystemSecurity;
import org.springframework.data.mongodb.core.query.Criteria;

import java.util.List;

/**
 * @author: ya<PERSON><PERSON><PERSON><PERSON>
 * @date: 2022/7/19 20:05
 * @description:
 */
public interface SystemSecurityDao {

    /**
     * 添加系统安全配置
     *
     * @param systemSecurity 系统安全
     * @return {@link SystemSecurity}
     */
    SystemSecurity addSystemSecurity(SystemSecurity systemSecurity);

    /**
     * 更新系统安全配置
     *
     * @param systemSecurity 系统安全
     * @return {@link Long}
     */
    Long updateSystemSecurity(SystemSecurityDto systemSecurityDto);

    /**
     * 删除系统安全配置
     *
     * @param id id
     * @return {@link Long}
     */
    Long deleteSystemSecurity(String id);

    /**
     * 根据id查询系统安全配置
     *
     * @param id id
     * @return {@link SystemSecurity}
     */
    SystemSecurity selectSystemSecurityById(String id);

    /**
     * 分页查询系统安全配置
     *
     * @param page  页面
     * @param limit 限制
     * @return {@link List}<{@link SystemSecurity}>
     */
    List<SystemSecurity> listSystemSecurity(Integer page, Integer limit);

    /**
     * 查询总数
     *
     * @return {@link Long}
     */
    Long total();

    /**
     * 条件查询系统配置规则
     *
     * @param criteria 标准
     * @return {@link List}<{@link SystemSecurity}>
     */
    List<SystemSecurity> listSystemSecurityByCriterial(Criteria criteria);

    List<SystemSecurity> listSystemSecurityAll();

}
