package com.quanzhi.auditapiv2.common.dal.dao.impl;

import com.quanzhi.auditapiv2.common.dal.dao.IGatewayEthConfigDao;
import com.quanzhi.auditapiv2.common.dal.dao.base.impl.BaseDaoImpl;
import com.quanzhi.auditapiv2.common.dal.entity.AgentStatus;
import com.quanzhi.auditapiv2.common.dal.entity.GatewayEthConfig;
import com.quanzhi.auditapiv2.common.dal.entity.ScanTaskResult;
import com.quanzhi.auditapiv2.common.util.utils.DataUtil;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @date 2023/9/6 14:47
 */
@Repository("GatewayEthConfigDao")
public class GatewayEthConfigDaoImpl extends BaseDaoImpl<GatewayEthConfig> implements IGatewayEthConfigDao {

    @Autowired
    MongoTemplate mongoTemplate;

    private static String collectionName = "gatewayEthConfig";


    @Override
    public List<GatewayEthConfig> selectEthConfigPage(String gatewayIp, Integer page, Integer limit) {
        //查询条件
        Criteria criteria = new Criteria();
        if (StringUtils.isNotBlank(gatewayIp)) {
            criteria = Criteria.where("gatewayIp").is(gatewayIp);
        }
        //分页
        Pageable pageable = PageRequest.of(page - 1, limit);
        return mongoTemplate.find(new Query(criteria).with(pageable), GatewayEthConfig.class, collectionName);

    }

    @Override
    public Long count(String gatewayIp) {
        Criteria criteria = new Criteria();
        if (StringUtils.isNotBlank(gatewayIp)) {
            criteria = Criteria.where("gatewayIp").is(gatewayIp);
        }
        return mongoTemplate.count(new Query(criteria),collectionName);
    }

    @Override
    public GatewayEthConfig findByGatewayIpAndEthName(String gatewayIp, String ethName) {
        Criteria criteria = Criteria.where("gatewayIp").is(gatewayIp).and("ethName").is(ethName);
        return mongoTemplate.findOne(Query.query(criteria), GatewayEthConfig.class, collectionName);
    }

    @Override
    public  List<GatewayEthConfig>  findByGatewayIp(String gatewayIp) {
        Criteria criteria = Criteria.where("gatewayIp").is(gatewayIp);
        return mongoTemplate.find(Query.query(criteria), GatewayEthConfig.class, collectionName);
    }
}
