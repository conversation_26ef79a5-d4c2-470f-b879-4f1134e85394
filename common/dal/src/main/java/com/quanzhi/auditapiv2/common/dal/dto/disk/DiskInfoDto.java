package com.quanzhi.auditapiv2.common.dal.dto.disk;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel("磁盘信息")
public class DiskInfoDto {
    @ApiModelProperty("磁盘描述")
    private String description;
    @ApiModelProperty("使用")
    private long usage;
    @ApiModelProperty("系统预留")
    private long reserve;
    @ApiModelProperty("总大小")
    private long total;
    @ApiModelProperty("剩余")
    private long remain;
    @ApiModelProperty("标签，日志盘为 audit/资产盘为 api")
    private String tag;
}
