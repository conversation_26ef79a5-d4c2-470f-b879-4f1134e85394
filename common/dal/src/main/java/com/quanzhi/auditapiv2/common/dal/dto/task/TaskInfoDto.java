package com.quanzhi.auditapiv2.common.dal.dto.task;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 描述: 任务运行信息DTO
 *
 * @author: danniel_yu
 * @date: 2020/11/23 14:43
 */
@Data
public class TaskInfoDto {
    private static final long serialVersionUID = -1573733410446289380L;

    /**
     * 任务ID
     */
    @ApiModelProperty(value = "任务ID", name = "taskId")
    private String taskId;

    /**
     * 任务名称
     */
    @ApiModelProperty(value = "任务名称", name = "taskName")
    private String taskName;

    /**
     * 任务开始时间
     */
    @ApiModelProperty(value = "任务开始时间", name = "startTime")
    private Long startTime;

    /**
     * 任务最近更新时间
     */
    @ApiModelProperty(value = "任务最近更新时间", name = "lastUpdateTime")
    private Long lastUpdateTime;

    /**
     * 任务结束时间
     */
    @ApiModelProperty(value = "任务结束时间", name = "endTime")
    private Long endTime;

    /**
     * 任务创建时间
     */
    @ApiModelProperty(value = "任务创建时间", name = "createTime")
    private Long createTime;

    /**
     * 任务状态
     */
    @ApiModelProperty(value = "任务状态", name = "taskState")
    private String taskStatus;


    /**
     * 运行中任务数量
     */
    @ApiModelProperty(value = "运行中任务数量", name = "runningCnt")
    private int runningCnt;

    /**
     * 运行成功次数
     */
    @ApiModelProperty(value = "运行成功次数", name = "successCnt")
    private int successCnt;

    /**
     * 运行失败次数
     */
    @ApiModelProperty(value = "运行失败次数", name = "failedCnt")
    private int failedCnt;

    /**
     * 任务cron表达式
     */
    @ApiModelProperty(value = "任务cron表达式", name = "taskCron")
    private String taskCron;

    /**
     * 任务描述
     */
    @ApiModelProperty(value = "任务描述", name = "taskDescription")
    private String taskDescription;

    /**
     * 数据库类型
     */
    @ApiModelProperty(value = "数据库类型", name = "dbType")
    private String dbType;

}
