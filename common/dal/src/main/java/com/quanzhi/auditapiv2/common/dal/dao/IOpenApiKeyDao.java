package com.quanzhi.auditapiv2.common.dal.dao;


import com.quanzhi.auditapiv2.common.dal.dao.base.IBaseDao;
import com.quanzhi.auditapiv2.common.dal.entity.OpenApiKey;

/**
 * 
 * 《开放接口密钥持久层接口》
 * 
 * 
 * @Project: 
 * @Module ID:
 * @Comments: 
 * @JDK version used:      <JDK1.8> 
 * <AUTHOR> [<EMAIL>]
 * @since 2021-08-17-上午9:09:10
 */
public interface IOpenApiKeyDao extends IBaseDao<OpenApiKey> {

	String CLUSTER_OPEN_API_KEY = "cluster-master-open-api-key";


	/**
	 * id查询开放接口密钥详情
	 * @Comments:  <对此方法的描述，可以引用系统设计中的描述>
	 * <AUTHOR> [<EMAIL>]
	 * @since 2021-08-17 9:09
	 * @param
	 * @return
	 */
	OpenApiKey selectOpenApiKeyById(String useKey);

	OpenApiKey selectOpenApiKeyByAccessKey(String accessKey);

	/**
	 * 新增开放接口密钥
	 * @Comments:  <对此方法的描述，可以引用系统设计中的描述>
	 * <AUTHOR> [<EMAIL>]
	 * @since 2021-08-17 9:09
	 * @param
	 * @return
	 */
	OpenApiKey insertOpenApiKey(OpenApiKey openApiKey);

	Long count();

	OpenApiKey findOne();

	OpenApiKey getOrCreateClusterOpenAPI(String userKey);

}
