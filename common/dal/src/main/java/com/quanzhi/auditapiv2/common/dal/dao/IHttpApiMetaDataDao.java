package com.quanzhi.auditapiv2.common.dal.dao;

import com.quanzhi.auditapiv2.common.dal.dao.base.IMetabaseDao;
import com.quanzhi.metabase.core.model.http.HttpApiMetaData;

import java.util.List;

/**
 * 
 * 《接口元数据持久层接口》
 * 
 * 
 * @Project: 
 * @Module ID:
 * @Comments: 
 * @JDK version used:      <JDK1.8> 
 * <AUTHOR> [<EMAIL>]
 * @since 2020-04-08-上午9:56:10
 */
public interface IHttpApiMetaDataDao extends IMetabaseDao<HttpApiMetaData> {

	/**
	 * 应用id查询元数据列表(分页)
	 * @Comments:  <对此方法的描述，可以引用系统设计中的描述>
	 * <AUTHOR> [<EMAIL>]
	 * @since 2020-04-08 9:56
	 * @param
	 * @return
	 */
	List<HttpApiMetaData> selectHttpApiMetaDataListByAppUri(String appUri, Integer page, Integer limit) throws Exception;

	/**
	 * 接口id查询元数据列表(分页)
	 * @Comments:  <对此方法的描述，可以引用系统设计中的描述>
	 * <AUTHOR> [<EMAIL>]
	 * @since 2020-04-08 9:56
	 * @param
	 * @return
	 */
	List<HttpApiMetaData> selectHttpApiMetaDataListByApiUri(String apiUri, Integer page, Integer limit) throws Exception;
}
