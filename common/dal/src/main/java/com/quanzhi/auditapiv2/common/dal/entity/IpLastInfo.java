package com.quanzhi.auditapiv2.common.dal.entity;

import com.quanzhi.audit_core.common.adaptive.AdaptiveCounting;
import com.quanzhi.metabase.core.model.Entity;
import com.quanzhi.metabase.core.model.http.HttpResourceConstant;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/10/21 下午5:38
 */
@Data
@Entity(HttpResourceConstant.IP_LAST_INFO)
public class IpLastInfo {
    private String ip;

    /**
     * 首次发现日期
     */
    private String firstDate;

    /**
     * 访问次数
     */
    private Long visitCnt;

    private Long loginCnt;

    private Long rspDataDistinctCnt;

    private List<String> accessDomainIds;

    /**
     * 返回数据标签
     */
    private List<String> rspDataLabelList;

    // 阻断状态
    private Boolean blockFlag;

    private List<String> appUriList;

    private List<String> apiUriList;

    private List<String> relatedAccountList;

    private List<String> uaTypes;

    /**
     * 关联账号的桶
     */
    private AdaptiveCounting relatedAccountBucket;

    /**
     * 下载文件的桶
     */
    private AdaptiveCounting downloadFileBucket;

    private AdaptiveCounting rspDataBucket;

    private String country;

    private String province;

    private String city;

    private DateCnt dateCnt;

    private Long updateTime;

    @Data
    public static class DateCnt {
        // 统计日期
        private String date;

        // 上次计算量
        private Long lastVisitCnt;
        private Long lastLoginCnt;
        private Long lastRspDataDistinctCnt;
    }
}
