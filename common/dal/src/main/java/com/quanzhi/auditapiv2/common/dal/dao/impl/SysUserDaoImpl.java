package com.quanzhi.auditapiv2.common.dal.dao.impl;

import com.alibaba.fastjson.JSON;
import com.mongodb.client.result.UpdateResult;
import com.quanzhi.auditapiv2.common.dal.dao.ISysUserDao;
import com.quanzhi.auditapiv2.common.dal.dao.base.impl.BaseDaoImpl;
import com.quanzhi.auditapiv2.common.dal.dto.SysUserSearchDto;
import com.quanzhi.auditapiv2.common.dal.entity.SysUserModel;
import com.quanzhi.auditapiv2.common.util.utils.DataUtil;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.FindAndModifyOptions;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.aggregation.Aggregation;
import org.springframework.data.mongodb.core.query.BasicUpdate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

/**
 * Created by shenjie on 2017/11/9.
 */
@Repository("sysUserDao")
public class SysUserDaoImpl extends BaseDaoImpl<SysUserModel> implements ISysUserDao {

    @Resource
    MongoTemplate mongoTemplate;

    private final String collectionName = "sysUser";

    @Override
    public List<SysUserModel> selectUserList() {
        return mongoTemplate.find(new Query(), SysUserModel.class, collectionName);
    }

    @Override
    public SysUserModel getUserInfoByName(String username) {
        Criteria where = Criteria.where("username").is(username);
        return mongoTemplate.findOne(new Query(where), SysUserModel.class, collectionName);
    }

    @Override
    public boolean updateUserErrorInfo(String username, SysUserModel sysUserModel) {
        Criteria where = Criteria.where("username").is(username);
        Update update = Update.update("updateTime", System.currentTimeMillis())
                .set("errorCount", sysUserModel.getErrorCount())
                .set("lockTime", sysUserModel.getLockTime());

        UpdateResult res = mongoTemplate.updateFirst(new Query(where), update, collectionName);
        if (DataUtil.isEmpty(res) ) {
            return false;
        }

        return true;
    }

    @Override
    public boolean updateUserPwd(String username, String password) {
        Criteria where = Criteria.where("username").is(username);
        Update update = Update.update("updateTime", System.currentTimeMillis())
                .set("password", password);

        UpdateResult res = mongoTemplate.updateFirst(new Query(where), update, collectionName);
        if (DataUtil.isEmpty(res) ) {
            return false;
        }

        return true;
    }

    @Override
    public void updateMailAddr(String username, String mailAddr) {
        Criteria where = Criteria.where("username").is(username);
        Update update = Update.update("mailAddr", mailAddr);
        mongoTemplate.updateFirst(new Query(where), update, collectionName);
    }

    @Override
    public Criteria getWhereBySearch(SysUserSearchDto searchDto) throws Exception {
        Criteria where = null;
        if (DataUtil.isNotEmpty(searchDto.getStartTime()) && DataUtil.isNotEmpty(searchDto.getEndTime())) {
            if (DataUtil.isNotEmpty(where)) {
                where.and("updateTime").gte(searchDto.getStartTime()).lte(searchDto.getEndTime());
            } else {
                where = Criteria.where("updateTime").gte(searchDto.getStartTime()).lte(searchDto.getEndTime());
            }
        } else {
            if (DataUtil.isNotEmpty(searchDto.getStartTime())) {
                if (DataUtil.isNotEmpty(where)) {
                    where.and("updateTime").gte(searchDto.getStartTime());
                } else {
                    where = Criteria.where("updateTime").gte(searchDto.getStartTime());
                }
            }
            if (DataUtil.isNotEmpty(searchDto.getEndTime())) {
                if (DataUtil.isNotEmpty(where)) {
                    where.and("updateTime").lte(searchDto.getEndTime());
                } else {
                    where = Criteria.where("updateTime").lte(searchDto.getEndTime());
                }
            }
        }

        if (DataUtil.isNotEmpty(searchDto.getKeyword())) {
            if (DataUtil.isNotEmpty(where)) {
                where.and("username").regex(searchDto.getKeyword());
            } else {
                where = Criteria.where("username").regex(searchDto.getKeyword());
            }
        }

        if (DataUtil.isNotEmpty(searchDto.getGroupId())) {
            if (DataUtil.isNotEmpty(where)) {
                where.and("groupId").is(searchDto.getGroupId());
            } else {
                where = Criteria.where("groupId").is(searchDto.getGroupId());
            }
        }

        return where;
    }

    @Override
    public Long getCount(SysUserSearchDto searchDto) throws Exception {
        Criteria where = getWhereBySearch(searchDto);
        Query query = DataUtil.isNotEmpty(where) ? new Query(where) : new Query();

        return mongoTemplate.count(query, collectionName);
    }

    @Override
    public List<SysUserModel> getList(SysUserSearchDto searchDto, Integer start, Integer limit) throws Exception {
        Criteria where = getWhereBySearch(searchDto);
        Aggregation aggregation = null;
        if (DataUtil.isEmpty(where)) {
            aggregation = Aggregation.newAggregation(
                    Aggregation.sort(Sort.Direction.DESC, "_id"),
                    Aggregation.skip(start),
                    Aggregation.limit(limit)
            );
        } else {
            aggregation = Aggregation.newAggregation(
                    Aggregation.match(where),
                    Aggregation.sort(Sort.Direction.DESC, "_id"),
                    Aggregation.skip(start),
                    Aggregation.limit(limit)
            );
        }

        return mongoTemplate.aggregate(aggregation, collectionName, SysUserModel.class).getMappedResults();
    }

    @Override
    public SysUserModel getDataById(String id) throws Exception {
        Criteria where = Criteria.where("_id").is(getFilterId(id));

        return mongoTemplate.findOne(new Query(where), SysUserModel.class, collectionName);
    }

    @Override
    public SysUserModel insertData(SysUserModel sysUserModel) throws Exception {
        mongoTemplate.insert(sysUserModel, collectionName);
        return sysUserModel;
    }

    @Override
    public SysUserModel updateDataById(SysUserModel sysUserModel, String id) throws Exception {
        Criteria where = Criteria.where("_id").is(getFilterId(id));
        Update update = new BasicUpdate(JSON.toJSONString(sysUserModel));
        FindAndModifyOptions options = new FindAndModifyOptions().returnNew(true);

        return mongoTemplate.findAndModify(new Query(where), update, options, SysUserModel.class, collectionName);
    }

    @Override
    public SysUserModel updateDataById(Map<String, Object> params, String id) throws Exception {
        Criteria where = Criteria.where("_id").is(getFilterId(id));
        Update update = new Update();
        for (Map.Entry<String, Object> entry : params.entrySet()) {
            update.set(entry.getKey(), entry.getValue());
        }
        FindAndModifyOptions options = new FindAndModifyOptions().returnNew(true);

        return mongoTemplate.findAndModify(new Query(where), update, options, SysUserModel.class, collectionName);
    }

    @Override
    public Boolean deleteByIdList(List<String> idList) throws Exception {
        Criteria where = Criteria.where("_id").in(getFilterIds(idList));
        mongoTemplate.remove(new Query(where), collectionName);

        return true;
    }

    @Override
    protected String getCollectionName() {
        return collectionName;
    }
}
