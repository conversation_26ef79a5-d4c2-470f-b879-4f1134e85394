package com.quanzhi.auditapiv2.common.dal.dto.customize.tjdx;

import com.quanzhi.auditapiv2.common.util.utils.DataUtil;
import lombok.Data;

import java.util.List;

/**
 * @Author: K, 小康
 * @Date: 2024/02/06/下午2:38
 * @Description:
 */
@Data
public class SendHttpApp {
    private Integer orgCode;
    private String batchId;
    private Long sendTimestamp;
    private List<HttAppData> dataList;

    @Data
    public static class HttAppData{
        private Integer syncType;
        private String businessId;
        private String name;
        private Integer masterType;
        private String masterIp;
        private String securityCapability = "3";
        private Integer securityLevel;
    }

}

