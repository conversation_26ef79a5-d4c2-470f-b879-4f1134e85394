package com.quanzhi.auditapiv2.common.dal.dao.impl;

import com.quanzhi.auditapiv2.common.dal.dao.IAppDateStatDao;
import com.quanzhi.auditapiv2.common.dal.entity.TrafficStatistics;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.aggregation.Aggregation;
import org.springframework.data.mongodb.core.aggregation.AggregationResults;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.stereotype.Repository;

/**
 * @Auther: yangzixian
 * @Date: 2021/8/30 19:11
 * @Description:
 */
@Repository
public class AppDateStatDaoImpl implements IAppDateStatDao {

    @Autowired
    private MongoTemplate mongoTemplate;

    private String collectionName = "httpAppDateStat";

    @Override
    public AggregationResults<TrafficStatistics> getTrafficStatisticsList(String startDate, String endDate) {
        Aggregation aggregation = Aggregation.newAggregation(
                Aggregation.match(Criteria.where("date").gte(startDate).lte(endDate)),
                Aggregation.group("date")
                        .first("date").as("date")
                        .sum("dailyAmount").as("count")
        );
        return mongoTemplate.aggregate(aggregation, collectionName, TrafficStatistics.class);
    }

}
