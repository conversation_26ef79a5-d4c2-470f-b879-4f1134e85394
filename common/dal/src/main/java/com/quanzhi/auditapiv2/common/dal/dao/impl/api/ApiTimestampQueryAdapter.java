package com.quanzhi.auditapiv2.common.dal.dao.impl.api;

import com.quanzhi.auditapiv2.common.dal.dao.impl.common.Module;
import com.quanzhi.auditapiv2.common.dal.dao.impl.common.ModuleMapper;
import com.quanzhi.auditapiv2.common.dal.dao.impl.common.QueryAdapter;
import com.quanzhi.auditapiv2.common.dal.dto.HttpApiSearchDto;
import com.quanzhi.metabase.core.model.query.Criteria;
import com.quanzhi.metabase.core.model.query.MetabaseQuery;
import com.quanzhi.metabase.core.model.query.Predicate;

/**
 * <AUTHOR>
 * @date 2022/7/25 11:58 上午
 */
@ModuleMapper(modules = {Module.API})
public class ApiTimestampQueryAdapter implements QueryAdapter {

    @Override
    public void query(Object query, MetabaseQuery metabaseQuery) {
        if (!(query instanceof HttpApiSearchDto)) {
            return;
        }
        HttpApiSearchDto httpApiSearchDto = (HttpApiSearchDto) query;
        if (httpApiSearchDto.getCreatTimestamp() != null && httpApiSearchDto.getCreatTimestamp() > 0) {
            metabaseQuery.where("createTime", Predicate.GTE, httpApiSearchDto.getCreatTimestamp());
        }
        if (httpApiSearchDto.getUpdateTimestamp() != null && httpApiSearchDto.getUpdateTimestamp() > 0) {
            metabaseQuery.where("updateTime", Predicate.GTE, httpApiSearchDto.getUpdateTimestamp());
        }
        if ((httpApiSearchDto.getStartUpdateTime() != null && httpApiSearchDto.getStartUpdateTime() > 0) &&
                (httpApiSearchDto.getEndUpdateTime() != null && httpApiSearchDto.getEndUpdateTime() > 0)
        ) {
            Criteria criteria = new Criteria();
            criteria.andOperator(Criteria.where("updateTime").gte(httpApiSearchDto.getStartUpdateTime()), Criteria.where("updateTime").lte(httpApiSearchDto.getEndUpdateTime()));
            metabaseQuery.getCriteria().add(criteria);
        } else if (httpApiSearchDto.getEndUpdateTime() != null && httpApiSearchDto.getEndUpdateTime() > 0) {
            metabaseQuery.where("updateTime", Predicate.LTE, httpApiSearchDto.getEndUpdateTime());
        }

    }

    @Override
    public void query(Object query, org.springframework.data.mongodb.core.query.Criteria criteria) {

    }
}
