package com.quanzhi.auditapiv2.common.dal.dao.impl;

import com.quanzhi.auditapiv2.common.dal.dao.AgentDao;
import com.quanzhi.auditapiv2.common.dal.dao.base.impl.BaseDaoImpl;
import com.quanzhi.auditapiv2.common.dal.entity.Agent;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public class AgentDaoImpl extends BaseDaoImpl<Agent> implements AgentDao {
    @Autowired
    private MongoTemplate mongoTemplate;

    private static String collectionName = "agent";

    /**
     * 获取所有Agent
     *
     * @return
     */
    @Override
    public List<Agent> listAllAgent() {
        return mongoTemplate.find(Query.query(new Criteria()), Agent.class, collectionName);
    }
}
