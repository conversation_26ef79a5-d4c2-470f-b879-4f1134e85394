package com.quanzhi.auditapiv2.common.dal.dto.common;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Data;

/**
 * <AUTHOR>
 * create at 2021/8/20  上午
 * @description: 适配前端框架的通用分组
 **/
@Data
@ApiModel
@Builder
public class CommonGroupDto {

    @ApiModelProperty(value = "分组的中文名", name = "name")
    private String name;

    @ApiModelProperty(value = "分组的英文名", name = "id")
    private String id;

    @ApiModelProperty(value = "其他id", name = "otherId")
    private String otherId;

    @ApiModelProperty(value = "组名", name = "group")
    private String group;

    @ApiModelProperty(value = "其他组", name = "otherGroup")
    private String otherGroup;

    @ApiModelProperty(value = "对应数量", name = "count")
    private Long count;

    @ApiModelProperty(value = "存储一些额外信息", name = "extraInfo")
    private Object extraInfo;

}