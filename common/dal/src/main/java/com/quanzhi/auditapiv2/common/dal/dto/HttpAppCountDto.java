package com.quanzhi.auditapiv2.common.dal.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 *
 * 《应用数量统计dto》
 *
 *
 * @Project:
 * @Module ID:
 * @Comments:
 * @JDK version used:      <JDK1.8>
 * <AUTHOR> [<EMAIL>]
 * @since 2020-04-08-上午9:56:10
 */
@Data
@ApiModel
public class HttpAppCountDto {

    /**
     * 总应用数量
     */
    @ApiModelProperty(value = "总应用数量", name = "totalCount")
    private Long totalCount = (long) 0;

    /**
     * 高敏感应用数量
     */
    @ApiModelProperty(value = "高敏感应用数量", name = "sensitiveCount")
    private Long sensitiveCount = (long) 0;

    /**
     * 互联网应用
     */
    @ApiModelProperty(value = "互联网应用", name = "internetCount")
    private Long internetCount = (long) 0;

    /**
     * 高敏感互联网应用
     */
    @ApiModelProperty(value = "高敏感互联网应用", name = "sensitiveInternetCount")
    private Long sensitiveInternetCount = (long) 0;
}
