package com.quanzhi.auditapiv2.common.dal.dto.query;

import com.quanzhi.auditapiv2.common.util.utils.DataUtil;
import com.quanzhi.auditapiv2.common.util.utils.PagingUtil;
import com.quanzhi.metabase.core.model.query.MetabaseQuery;
import com.quanzhi.metabase.core.model.query.Sort;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * @Author: Linlm
 * @Description:
 * @Date: Created in 2020/6/2 下午6:16
 */
@Data
public class BaseSearchDto implements Serializable {
    /**
     * 排序
     */
    private List<SortDto> sortDtos;

    /**
     * 当前页：从1开始
     */
    private Integer page;

    /**
     * 每页行数
     */
    private Integer limit;

    public void fillMetabaseQuery(MetabaseQuery metabaseQuery){
        metabaseQuery.skip(0);
        if (getLimit() != null) {
            metabaseQuery.limit(getLimit());
        }

        if (DataUtil.isNotEmpty(getLimit())){
            metabaseQuery.skip(PagingUtil.getSkipByPage(getPage(), getLimit()));
        }

        if (DataUtil.isNotEmpty(getSortDtos())){
            Sort[] sorts =  SortDto.toSortForMetabaseQuery(sortDtos);
            metabaseQuery.setSort(sorts);
        }
    }
}
