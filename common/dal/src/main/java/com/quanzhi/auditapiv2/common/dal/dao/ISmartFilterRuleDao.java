package com.quanzhi.auditapiv2.common.dal.dao;

import com.quanzhi.auditapiv2.common.dal.dao.base.IBaseDao;
import com.quanzhi.auditapiv2.common.dal.dto.filter.SmartFilterCriteriaDTO;
import com.quanzhi.metabase.core.model.filter.SmartFilterRule;

import java.util.List;

public interface ISmartFilterRuleDao extends IBaseDao<SmartFilterRule> {

    /**
     * 查询智能过滤规则列表(分页)
     * @Comments:  <对此方法的描述，可以引用系统设计中的描述>
     * <AUTHOR> [<EMAIL>]
     * @since 2022-01-07 9:56
     * @param
     * @return
     */
    List<SmartFilterRule> selectSmartFilterRuleList(Integer page, Integer limit,String field,Integer sort,String host) throws Exception;


    List<SmartFilterRule> selectSmartFilterRuleList(SmartFilterCriteriaDTO smartFilterCriteriaDTO) throws Exception;


    /**
     * 查询智能过滤规则数量
     * @Comments:  <对此方法的描述，可以引用系统设计中的描述>
     * <AUTHOR> [<EMAIL>]
     * @since 2022-01-07 9:56
     * @param
     * @return
     */
    Long totalCount() throws Exception;


    Long totalCount(SmartFilterCriteriaDTO smartFilterCriteriaDTO);


    /**
     * id查询智能过滤规则详情
     * @Comments:  <对此方法的描述，可以引用系统设计中的描述>
     * <AUTHOR> [<EMAIL>]
     * @since 2022-01-07 9:56
     * @param
     * @return
     */
    SmartFilterRule selectSmartFilterRuleById(String id) throws Exception;

    /**
     * 编辑智能过滤规则
     * @Comments:  <对此方法的描述，可以引用系统设计中的描述>
     * <AUTHOR> [<EMAIL>]
     * @since 2022-01-07 9:56
     * @param
     * @return
     */
    SmartFilterRule updateSmartFilterRule(SmartFilterRule smartFilterRule) throws Exception;

}
