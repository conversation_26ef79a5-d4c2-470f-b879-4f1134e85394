package com.quanzhi.auditapiv2.common.dal.dto.query;

import com.quanzhi.auditapiv2.common.dal.dto.ExporTitleFieldDto;
import lombok.Data;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

/**
 * @ClassName IpInfoCriteriaDto
 * @Description TODO
 * <AUTHOR>
 * @Date 2020/11/21 15:42
 **/
@Data
public class IpInfoCriteriaDto implements Serializable {
    // 模糊
    private String ip;

    // OR 或关系 ALL 且关系, 如：dataLabel:or/and
    private Map<String, String> fieldOperateMap;

    // 风险等级：精确
    private List<Integer> riskLevel;

    // 应用关系：且、或
    private List<String> appUriList;

    // 网段：且、或
    private List<String> accessDomainIds;

    // 终端类型：且、或
    private List<String> uaTypes;

    // 返回数据标签：且、或
    private List<String> rspDataLabelList;

    // 风险事件：且、或
    private List<String> riskNames;

    // 阻断状态：精确
    private Boolean blockFlag;

    // 地域：模糊
    private String area;
    private String country;
    private String province;
    private String city;

    // 首次发现时间范围查询: yyyyMMdd
    private String startTime;
    private String endTime;

    // 活跃时间范围查询: yyyyMMdd
    private String startDate;
    private String endDate;

    // 日期：精确
    private String date;

    // 威胁标签：且、或
    private List<String> threatLabels;

    /**
     * 导出字段
     */
    private List<ExporTitleFieldDto> list;

    private Integer page;

    private Integer limit;

    private String field;

    private Integer sort;

    /**
     * 选择导出的 IP
     * @since 3.1
     */
    private List<String> exportIps;

    private String nodeId;

    private Map<String,Object> dataPermissionMap;


//    private CriteriaEnumType type;
}
