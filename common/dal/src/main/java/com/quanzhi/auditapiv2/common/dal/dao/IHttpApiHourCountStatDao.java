package com.quanzhi.auditapiv2.common.dal.dao;

import com.quanzhi.auditapiv2.common.dal.entity.HttpApiIpHourCountStat;

import java.util.List;
import java.util.Map;

public interface IHttpApiHourCountStatDao {
    /**
     * 根据URL列表和日期查询每小时访问量
     * @param uriList URL列表
     * @param day 日期字符串 (格式: yyyy-MM-dd)
     * @return 映射关系，Key: URL, Value: 小时访问量数组
     */
    Map<String, Long[]> getHourStatByUriListAndDay(List<String> uriList, String day);

    List<HttpApiIpHourCountStat>  getHourReqRspStat(List<String> uriList, String day);
}
