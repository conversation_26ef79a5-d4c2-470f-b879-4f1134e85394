package com.quanzhi.auditapiv2.common.dal.dto.securityPosture;

import com.quanzhi.auditapiv2.common.dal.dto.risk.SystemRiskIndexDto;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Data;

/**
 * @Auther: yangzixian
 * @Date: 2021/10/14 11:28
 * @Description:
 */
@Data
@Builder
@ApiModel("概览信息")
public class StatisticsDto {

    private String type;

    @ApiModelProperty("系统风险指数")
    private SystemRiskIndexDto riskIndex;

    @ApiModelProperty("总应用")
    private Long totalApp;

    @ApiModelProperty("高敏感应用")
    private Long highLevelApp;

    @ApiModelProperty("总API")
    private Long totalApi;

    @ApiModelProperty("高敏感API")
    private Long highLevelApi;

    @ApiModelProperty("总弱点事件")
    private Long totalWeakness;

    @ApiModelProperty("高风险弱点事件")
    private Long highLevelWeakness;

    @ApiModelProperty("总风险事件")
    private Long totalRisk;

    @ApiModelProperty("高风险风险事件")
    private Long highLevelRisk;
}
