package com.quanzhi.auditapiv2.common.dal.dao.apiZS;

import com.quanzhi.metabase.core.model.http.HttpAppResource;

import java.util.List;

/**
 *浙商定开，Dao层接口
 * @Author: 胡政乡
 * @Date: 2023/11/2 11:31
 **/

public interface ZSIHttpAppDao {
    /**
     *根据查询条件condition，获得api访问量前20的应用
     * @param condition:
     * @return: java.util.List<com.quanzhi.metabase.core.model.http.HttpAppResource>
     * @Author: 胡政乡
     * @Date: 2023/11/2 11:31
     **/
    List<HttpAppResource> selectAppVisitsTop(String condition);

    /**
     *appUri批量查询应用详情
     * @param uriList:
     * @return: java.util.List<com.quanzhi.metabase.core.model.http.HttpAppResource>
     * @Author: 胡政乡
     * @Date: 2023/11/2 11:35
     **/

    List<HttpAppResource> selectAppUriList(List<String> uriList);

}
