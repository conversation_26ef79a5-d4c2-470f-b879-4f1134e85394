package com.quanzhi.auditapiv2.common.dal.dto;

import lombok.Data;

import java.io.Serializable;
import java.util.Map;

@Data
public class HttpAppCriteriaDTO implements Serializable {

    private String ids;

    private String target;

    //批量监控用（开/关）
    private Boolean flag;

    //是否全选
    private Boolean complete;

    //app列表查询条件
    private Map<String, Object> map;

    //api列表查询条件
    private HttpApiSearchDto httpApiSearchDtoStr;
}
