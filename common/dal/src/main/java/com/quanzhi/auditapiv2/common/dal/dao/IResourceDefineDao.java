package com.quanzhi.auditapiv2.common.dal.dao;

import com.quanzhi.auditapiv2.common.dal.dto.CompositeRuleSearchDto;
import com.quanzhi.auditapiv2.common.dal.dto.assetDefinition.KeywordSplitRuleDto;
import com.quanzhi.auditapiv2.common.util.dto.ListOutputDto;
import com.quanzhi.metabase.core.model.http.CompositeRule;
import com.quanzhi.metabase.core.model.http.HttpApiResource;
import com.quanzhi.metabase.core.model.http.HttpAppResource;
import com.quanzhi.metabase.core.model.http.KeywordSplitRule;
import com.quanzhi.metabase.core.model.http.api.APICompositeResource;
import com.quanzhi.metabase.core.model.http.app.APPCompositeResource;
import com.quanzhi.metabase.core.model.query.MetabaseQuery;
import com.quanzhi.metabase.core.model.query.ResourceUpdates;
import org.springframework.data.domain.Pageable;

import java.util.List;

/**
 * @Author: Linlm
 * @Description:
 * @Date: Created in 2020/5/15 下午2:24
 */
public interface IResourceDefineDao {

    CompositeRule saveCompositeRule(CompositeRule compositeRule);

    CompositeRule getMergedAppCompositeRule(String host);

    CompositeRule getSplitAPICompositeRuleLike(String ruleLike);


    ListOutputDto<CompositeRule> getCompositeRules(CompositeRuleSearchDto searchDto);

    boolean existCompositeRules(String rscName);

    boolean existCompositeByRule(String rule);

    CompositeRule findByRscName(String rscName);

    CompositeRule findByRule(String rucancelResourcesle);

    CompositeRule findByRuleId(String ruleId);

    CompositeRule getCompositeRuleById(String compositeRuleId);

    HttpApiResource saveByRule(CompositeRule compositeRule);

    void cancelResources(Integer compositeType, Integer rscType, MetabaseQuery metabaseQuery);

    HttpAppResource matchHttpAppResource(String host);

    void recoverApiSample(String apiUrl, String compositeRuleId);

    List<HttpApiResource> findApiList(MetabaseQuery query);

    List<HttpAppResource> findAppList(MetabaseQuery query);

    void recoverCancelApi(String id);

    void addExcludeResources(String compositeRuleId, String apiUrl, String host);

    void recoverCancelApp(String host);

    KeywordSplitRule saveKeywordSplitRule(KeywordSplitRule keywordSplitRule);

    KeywordSplitRule findKeywordSplitRule(String id);

    ListOutputDto<KeywordSplitRule> listKeywordSplitRule(KeywordSplitRuleDto keywordSplitRuleDto);

    <T> List<T> find(MetabaseQuery query, Class<T> entityClass);

    boolean existApp(String host);

    void deleteCompositeRule(String ruleId);

    boolean exist(MetabaseQuery query, Class<?> entityClass);

    void hardDelete(String id);

    void update(MetabaseQuery query, ResourceUpdates updates, Class aclass);

    ListOutputDto<APICompositeResource> listAPICompositeResource(String compositeRuleId, Pageable pageable);

    ListOutputDto<APPCompositeResource> listAPPCompositeResource(String compositeRuleId, String rule, Pageable pageable);

    HttpAppResource findAppByRule(String ruleId);
    HttpApiResource findApiByRule(String ruleId);


}
