package com.quanzhi.auditapiv2.common.dal.dto.securityPosture;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * @Auther: yangzixian
 * @Date: 2021/10/19 10:43
 * @Description:
 */
@Data
@Builder
@ApiModel("大屏应用关系Map")
public class AppRelationMapDto {
    @ApiModelProperty("用于筛选的映射map")
    private Map<String, String> itemMap;
    @ApiModelProperty("用于展示的标签map")
    private Map<String, String> labelMap;
    @ApiModelProperty("用于展示具体访问数值的map")
    private Map<String, Long> linkMap;
    @ApiModelProperty("源节点枚举")
    private List<String> sourceList;
    @ApiModelProperty("源节点颜色map")
    private Map<String,String> sourceColorMap;
    @ApiModelProperty("目标节点枚举")
    private List<String> targetList;
    @ApiModelProperty("目标节点颜色map")
    private Map<String,String> targetColorMap;
    @ApiModelProperty("总节点枚举")
    private List<String> itemList;
    @ApiModelProperty("颜色对应枚举")
    private Map<String, String> colorMap;
}
