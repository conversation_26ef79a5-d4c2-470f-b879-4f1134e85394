package com.quanzhi.auditapiv2.common.dal.dto;

import com.quanzhi.audit_core.common.model.ApiOfflineDiscoverParam;
import lombok.Data;

import java.util.List;

@Data
public class XxlJobTaskDto {

    /**
     * 任务主体  接口或数据表
     * @see EntityEnum
     */
    private String entity;

    /**
     * 任务类型 清理或重跑
     * @see TaskTypeEnum
     */
    private String taskType;

    /**
     * 清理类型 数据标签、接口类型、推荐策略
     * @see DataType
     */
    private String dataType;

    private List<String> dataItemNames;

    /**
     * 数据内容  数据标签、接口类型、推荐策略的ids
     */
    private List<String> dataItems;

    /**
     * 清理周期 开始时间
     */
    private Long startTime;

    /**
     *清理周期  结束时间
     */
    private Long endTime;

    /**
     * 数据重跑 范围配置 全量false，特定true
     */
    private Boolean specified;

    /**
     * 清理方式
     * @see CleanMethod
     */
    private String cleanMethod;

    /**
     * 离线重跑的维度
     */
    private List<ApiOfflineDiscoverParam.DiscoverType> offlineDiscoverTypes;


    public enum TaskTypeEnum {
        CLEAN,RERUN
    }

    public enum EntityEnum {
        API,DATATABLE
    }

    public enum DataType {
        DATA_LABEL, CLASSIFICATION, RECOMMEND, FEATURE_LABEL, APP_CLASSIFICATION, APP_FEATURE_LABEL, WEAKNESS,URL,API
    }

    public enum CleanMethod {
        LABEL, FILE, SAMPLE, ACCOUNT, ASSET
    }

    /**
     * 执行人
     */
    private String userId;
}
