package com.quanzhi.auditapiv2.common.dal.dto.weakness;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2021/8/30 2:23 下午
 */
@Data
@ApiModel("数据暴露分析")
public class DataExposeStatisticsDto {
    @ApiModelProperty("弱点名称")
    private String name;
    @ApiModelProperty("弱点类型")
    private String type;
    @ApiModelProperty("高风险")
    private long highRiskCount;
    @ApiModelProperty("中风险")
    private long middleRiskCount;
    @ApiModelProperty("低风险")
    private long lowRiskCount;
    @ApiModelProperty("弱点ID")
    private String id;
}
