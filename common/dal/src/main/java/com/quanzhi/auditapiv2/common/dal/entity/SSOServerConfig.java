
package com.quanzhi.auditapiv2.common.dal.entity;

import com.quanzhi.uias.base.sdk.util.entity.InteractParam;
import lombok.Data;

import java.io.Serializable;

/**
 *
 * 《SSO配置》
 *
 *
 * @Project:
 * @Module ID:
 * @Comments:
 * @JDK version used:      <JDK1.8>
 * <AUTHOR> [<EMAIL>]
 * @since 2022-10-12-上午9:56:10
 */
@Data
public class SSOServerConfig implements Serializable {

    private String id;

    /**
     * SSO服务名称
     */
    private String name;

    /**
     * SSO服务地址
     */
    private InteractParam value;
}