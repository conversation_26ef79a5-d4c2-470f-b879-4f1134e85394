package com.quanzhi.auditapiv2.common.dal.dao;

import com.mongodb.client.MongoCursor;
import com.quanzhi.audit_core.common.model.RiskInfoAgg;
import com.quanzhi.auditapiv2.common.dal.dto.RiskLevelMatchDto;
import com.quanzhi.auditapiv2.common.dal.dto.query.RiskInfoAggCriteriaDto;
import org.bson.Document;

import java.util.List;

/**
 * @ClassName IRiskInfoAggDao
 * @Description TODO
 * <AUTHOR>
 * @Date 2020/11/23 11:29
 **/
public interface IRiskInfoAggDao {

    List<RiskInfoAgg> getRiskInfoAggList(RiskInfoAggCriteriaDto riskInfoAggCriteriaDto);

    Long getRiskInfoAggCount(RiskInfoAggCriteriaDto riskInfoAggCriteriaDto);

    long getRiskInfoAggCountByState(List<Integer> stateList, Integer level);

    MongoCursor<Document> getRiskInfoAggCursor() throws Exception;

    /**
     * 计算系统风险指数风险维度
     * @return
     */
    Double getRiskDimension();

    /**
     * <AUTHOR>
     * @date 2023/3/3 10:48 AM
     * @Description 获取 API、IP、ACCOUNT 的风险信息
     * @param entity, entityType
     * @return com.quanzhi.auditapiv2.common.dal.dto.RiskLevelMatchDto.Risk
     * @Since
     */
    RiskLevelMatchDto.Risk getRiskInfo(String entity, String entityType);
}
