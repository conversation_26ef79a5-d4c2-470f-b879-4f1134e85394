package com.quanzhi.auditapiv2.common.dal.entity;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 *
 * 《流量分析接口统计》
 *
 *
 * @Project:
 * @Module ID:
 * @Comments:
 * @JDK version used:      <JDK1.8>
 * <AUTHOR> [<EMAIL>]
 * @since 2020-08-28-下午16:52:18
 */
@Data
public class FlowAnalysisApiStat {

    @ApiModelProperty(value = "id", name = "id")
    private String id;

    @ApiModelProperty(value = "_id", name = "_id")
    private String _id;

    /**
     * 接口Uri
     */
    @ApiModelProperty(value = "接口Uri", name = "apiUri")
    private String apiUri;

    /**
     * 接口Url
     */
    @ApiModelProperty(value = "接口Url", name = "apiUrl")
    private String apiUrl;

    /**
     * 应用Uri
     */
    @ApiModelProperty(value = "应用Uri", name = "appUri")
    private String appUri;

    /**
     * 访问量
     */
    @ApiModelProperty(value = "访问量", name = "visitCnt")
    private Integer visitCnt;

    /**
     * 访问终端
     */
    @ApiModelProperty(value = "访问终端", name = "uaClassifications")
    private List<String> uaClassifications;

    /**
     * 访问终端详情
     */
    @ApiModelProperty(value = "访问终端详情", name = "visitCntByUaClassifications")
    private List<FlowAnalysisAppStat.VisitCntByUaClassification> visitCntByUaClassifications;

    /**
     * 访问域
     */
    @ApiModelProperty(value = "访问域", name = "accessDomainIds")
    private List<String> accessDomainIds;

    /**
     * 访问域详情
     */
    @ApiModelProperty(value = "访问域详情", name = "visitCntByAccessDomainIds")
    private List<FlowAnalysisAppStat.VisitCntByAccessDomainId> visitCntByAccessDomainIds;

    /**
     * ﻿ua类型
     */
    @ApiModelProperty(value = "终端类型", name = "uaTypes")
    private List<String> uaTypes;

    /**
     * 敏感标签
     */
    @ApiModelProperty(value = "敏感标签", name = "dataLabelIds")
    private List<String> dataLabelIds;

    /**
     * 日期
     */
    @ApiModelProperty(value = "日期", name = "date")
    private String date;

    /**
     * 时间戳
     */
    @ApiModelProperty(value = "时间戳", name = "timestamp")
    private Long timestamp;

    /**
     * 更新时间
     */
    @ApiModelProperty(value = "更新时间", name = "updateTime")
    private Long updateTime;

    @Data
    public static class VisitCntByUaClassification {

        /**
         * 访问终端
         */
        @ApiModelProperty(value = "访问终端", name = "uaClassification")
        private String uaClassification;

        /**
         * 访问量
         */
        @ApiModelProperty(value = "访问量", name = "visitCnt")
        private Integer visitCnt;
    }

    @Data
    public static class VisitCntByAccessDomainId {

        /**
         * 访问终端
         */
        @ApiModelProperty(value = "访问域", name = "accessDomainId")
        private String accessDomainId;

        /**
         * 访问量
         */
        @ApiModelProperty(value = "访问量", name = "visitCnt")
        private Integer visitCnt;
    }
}
