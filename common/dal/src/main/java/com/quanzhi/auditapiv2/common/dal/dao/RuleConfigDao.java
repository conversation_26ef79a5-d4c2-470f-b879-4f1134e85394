package com.quanzhi.auditapiv2.common.dal.dao;

import com.quanzhi.re.core.defaults.RuleDefault;
import com.quanzhi.re.core.domain.entity.po.RuleConfigPO;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public class RuleConfigDao {

    private final MongoTemplate mongoTemplate;

    public RuleConfigDao(MongoTemplate mongoTemplate) {
        this.mongoTemplate = mongoTemplate;
    }

    public RuleConfigPO save(RuleConfigPO ruleConfigDTO) {
        return mongoTemplate.save(ruleConfigDTO, RuleDefault.TABLE_NAME);
    }

    public void logicDelete(String id) {
        Update update = new Update();
        update.set("delFlag",true);
        update.set("updateTime",System.currentTimeMillis());
        mongoTemplate.upsert(new Query().addCriteria(Criteria.where("_id").is(id)), update,RuleDefault.TABLE_NAME);
    }

    public void updateById(String id,Update update) {
        update.set("updateTime",System.currentTimeMillis());
        mongoTemplate.upsert(new Query().addCriteria(Criteria.where("_id").is(id)), update,RuleDefault.TABLE_NAME);
    }

    public List<RuleConfigPO> findByIds(List<String> ids) {
        return mongoTemplate.find(new Query().addCriteria(Criteria.where("_id").in(ids)),RuleConfigPO.class,RuleDefault.TABLE_NAME);
    }

    public RuleConfigPO findById(String id) {
        return mongoTemplate.findById(id,RuleConfigPO.class,RuleDefault.TABLE_NAME);
    }

    public List<RuleConfigPO> findByCategory(String category) {
        return mongoTemplate.find(new Query().addCriteria(Criteria.where("category").is(category)),RuleConfigPO.class,RuleDefault.TABLE_NAME);
    }
}
