package com.quanzhi.auditapiv2.common.dal.dao.impl;

import com.quanzhi.auditapiv2.common.dal.dao.ISysUserGroupDao;
import com.quanzhi.auditapiv2.common.dal.entity.SysUserGroupModel;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;

/**
 * Created by she<PERSON><PERSON> on 2017/11/9.
 */
@Repository("sysUserGroupDao")
public class SysUserGroupDaoImpl implements ISysUserGroupDao {

    @Resource
    MongoTemplate mongoTemplate;

    @Override
    public SysUserGroupModel selectUserGroupById(String id) throws Exception {
        Criteria where = Criteria.where("_id").is(id);
        return mongoTemplate.findOne(new Query(where), SysUserGroupModel.class, "sysUserGroup");
    }
}
