package com.quanzhi.auditapiv2.common.dal.dao.impl;

import com.quanzhi.audit_core.common.model.IpPositionConfig;
import com.quanzhi.auditapiv2.common.dal.dao.IIpPositionConfigNacosDao;
import com.quanzhi.auditapiv2.common.dal.dao.base.impl.NacosBaseDaoImpl;
import org.springframework.stereotype.Repository;

/**
 * 
 * 《纯真库持久层Nacos接口实现》
 * 
 * 
 * @Project: 
 * @Module ID:
 * @Comments: 
 * @JDK version used:      <JDK1.8> 
 * <AUTHOR> [<EMAIL>]
 * @since 2020-04-08-上午9:56:10
 */
@Repository
public class IpPositionConfigNacosDaoImpl extends NacosBaseDaoImpl<IpPositionConfig> implements IIpPositionConfigNacosDao {
}
