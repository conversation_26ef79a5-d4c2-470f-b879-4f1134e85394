package com.quanzhi.auditapiv2.common.dal.entity;

import lombok.Data;

/**
 * <AUTHOR>
 * create at 2022/7/29 3:23 下午
 * @description: 接口自动拆分任务
 **/
@Data
public class ApiSplitTask {

    private String id;
    private String taskId;
    private String operator;
    private Long createTime;
    private Long finishTime;
    /**
     * 产出文件路径
     */
    private String resultPath;

    private String taskState;

    private String failReason;


    public enum StateEnum{
        SUCCESS,FAIL,RUN
    }
}