package com.quanzhi.auditapiv2.common.dal.dto.customize.tjdx;

import lombok.Data;

import java.util.List;

/**
 * @Author: K, 小康
 * @Date: 2024/02/21/上午11:44
 * @Description:
 */
@Data
public class SendHttpApi {
    private Integer orgCode;
    private String batchId;
    private Long sendTimestamp;
    private String commitDeviceIp;
    private String commitDeviceNo;
    private List<HttApiData> dataList;
    @Data
    public static class HttApiData{
        private Integer syncType;
        private String assetId;
        private String businessId;
        private String businessName;
        private String apiName;
        private String interfaceProtocol;
        private Integer agreement;//string转int
        private String apiAddress;
        private String apiIp;
        private String apiPort;//int转string
        private String apiHostName;//非必填
        private Integer requestMethod;//新增
        private Double requestData;//非必填
        private String eopName;//非必填
        private Integer assetLevel;
        private String apiDataType;
        private Integer dataLevel=0;//新增
        private String applicationClass="99";//新增
        private String applicationName;//非必填
        private Integer useType=99;//新增
        private Integer apiCallStatus;//新增
        private String apiType;//新增
        private String apiSecurityType = "99";//新增
        private String securityCapability = "3";//新增
        private String apiUser = "99";//新增
        private Integer transmissionScenario=99;//新增
        private Integer dataEncryption;
        private Integer channelEncryption;
        private String authType;
        private String safetyRespDept;//非必填
        private String safetyRespPerson;//非必填
        private String safetyRespMail;//非必填
        private List<Partner> partners;//非必填
    }

    @Data
    public static class Partner{
        private String partnerName;
        private String partnerSystem;
        private String partnerSafety;
    }
}
