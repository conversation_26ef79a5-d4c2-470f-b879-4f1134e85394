package com.quanzhi.auditapiv2.common.dal.dao;

import com.quanzhi.auditapiv2.common.dal.entity.riskEolink.RiskApiStrategyDto;

import java.util.List;

public interface RiskApiStrategyDao {

    RiskApiStrategyDto save(RiskApiStrategyDto dto);

    RiskApiStrategyDto update(RiskApiStrategyDto dto);

    List<RiskApiStrategyDto> listRiskApiStrategy(String api, String app);

    RiskApiStrategyDto findById(String id);
    RiskApiStrategyDto findByOperationId(String operationId);

    void deleteById(String id);

    void deleteByStrategyId(String strategyId);
}
