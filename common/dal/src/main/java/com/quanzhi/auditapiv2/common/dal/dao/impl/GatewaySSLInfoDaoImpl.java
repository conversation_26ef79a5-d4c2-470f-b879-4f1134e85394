package com.quanzhi.auditapiv2.common.dal.dao.impl;

import com.quanzhi.auditapiv2.common.dal.dao.IGatewaySSLInfoDao;
import com.quanzhi.auditapiv2.common.dal.dao.base.impl.BaseDaoImpl;
import com.quanzhi.auditapiv2.common.dal.entity.GatewaySSLInfo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 网关加密数据相关
 */
@Repository("GatewaySSLInfoDao")
public class GatewaySSLInfoDaoImpl extends BaseDaoImpl<GatewaySSLInfo> implements IGatewaySSLInfoDao {
    
    @Autowired
    MongoTemplate mongoTemplate;

    private static String collectionName = "gatewaySSLInfo";

    @Override
    public List<GatewaySSLInfo> getList(Long startTime, Long endTime, String gatewayIp) {
        Criteria criteria = Criteria.where("ip").is(gatewayIp).andOperator(Criteria.where("time").gte(startTime).lte(endTime));
        return mongoTemplate.find(new Query(criteria), GatewaySSLInfo.class, collectionName);
    }


    @Override
    public Long getCount(Long start, Long end) {
        return null;
    }

    @Override
    public GatewaySSLInfo findById(String id) {
        return null;
    }
}
