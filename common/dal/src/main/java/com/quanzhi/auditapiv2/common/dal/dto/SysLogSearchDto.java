package com.quanzhi.auditapiv2.common.dal.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.data.annotation.Id;

import java.io.Serializable;

/**
 * 《修改密码DTO》
 *
 * <AUTHOR> [<EMAIL>]
 * @Project:
 * @Module ID:
 * @Comments:
 * @JDK version used:      <JDK1.8>
 * @since 2021-12-01-上午9:56:10
 */
@Data
@ApiModel
public class SysLogSearchDto implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 操作
     */
    @Id
    @ApiModelProperty(value = "操作", name = "operation")
    private String operation;

    /**
     * 操作用户
     */
    @ApiModelProperty(value = "操作用户", name = "username")
    private String username;

    /**
     * ip
     */
    @ApiModelProperty(value = "ip", name = "ip")
    private String ip;

    /**
     * 创建时间
     */
    @Id
    @ApiModelProperty(value = "创建时间", name = "gmtCreate")
    private Long gmtCreate;

    /**
     * 开始时间
     */
    @ApiModelProperty(value = "开始时间", name = "startDate")
    private Long startDate;

    /**
     * 结束时间
     */
    @ApiModelProperty(value = "结束时间", name = "endDate")
    private Long endDate;

    /**
     * 当前页
     */
    @ApiModelProperty(value = "当前页", name = "page")
    private Integer page;

    /**
     * 每页显示条数
     */
    @ApiModelProperty(value = "每页显示条数", name = "limit")
    private Integer limit;

    /**
     * 排序字段
     */
    @ApiModelProperty(value = "排序字段", name = "field")
    private String field;
    
    /**
     * 1正序，2倒序
     */
    @ApiModelProperty(value = "1正序，2倒序", name = "sort")
    private Integer sort;
}
