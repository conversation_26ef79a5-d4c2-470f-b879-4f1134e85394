package com.quanzhi.auditapiv2.common.dal.dto.data;

import com.quanzhi.auditapiv2.common.dal.entity.aggRisk.AggRiskInfo;
import com.quanzhi.auditapiv2.common.dal.entity.data.DataInfo;
import com.quanzhi.auditapiv2.common.dal.entity.data.DataMonthInfo;
import com.quanzhi.metabase.core.model.ResourceEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;
import java.util.Map;

/**
 * @Author: yangzx
 * @Date: 2024/10/9 11:12
 */
@Data
public class DataInfoDto {

    @ApiModelProperty("数据库ID")
    private String id;

    @ApiModelProperty("数据名称")
    private String dataName;

    @ApiModelProperty("数据ID")
    private String dataId;

    @ApiModelProperty("累计访问次数")
    private Long totalCount;

    @ApiModelProperty("请求数据量")
    private Long reqCount;

    @ApiModelProperty("响应数据量")
    private Long rspCount;

    @ApiModelProperty("数据风险等级")
    private Integer dataRiskLevel;

    @ApiModelProperty("数据风险等级名称")
    private String dataRiskLevelName;

    @ApiModelProperty("关联风险个数")
    private Integer riskNum;

    @ApiModelProperty(value = "节点信息", name = "nodes")
    private List<ResourceEntity.Node> nodes;

    @ApiModelProperty("关联风险")
    private List<String> relatedRisks;

    @ApiModelProperty("数据类型ID")
    private String dataType;

    @ApiModelProperty("数据类型名称")
    private String dataTypeName;

    @ApiModelProperty("数据级别")
    private Integer dataLevel;

    @ApiModelProperty("数据级别名称")
    private String dataLevelName;

    @ApiModelProperty("关联应用个数")
    private Integer appNum;

    @ApiModelProperty("关注状态")
    private Boolean markState;

    @ApiModelProperty("关联账号个数")
    private Integer accountNum;

    @ApiModelProperty("关联API个数")
    private Integer apiNum;

    @ApiModelProperty("关联IP个数")
    private Integer ipNum;

    @ApiModelProperty("首次发现时间")
    private Long firstTime;

    @ApiModelProperty("最近活跃时间")
    private Long lastTime;

//    public enum DataLevelEnum {
//
//        /**
//         * 非敏感
//         */
//        LOW(1, "一级"),
//
//        /**
//         * 低敏感
//         */
//        MEDIUM(2, "二级"),
//
//        /**
//         * 中敏感
//         */
//        HIGH(3, "中敏感"),
//
//        /**
//         * 高敏感
//         */
//        HIGH(4, "高敏感");
//
//        private Integer level;
//
//        private String name;
//
//        DataLevelEnum(Integer level, String name) {
//
//            this.level = level;
//            this.name = name;
//        }
//
//        public Integer getLevel() {
//            return this.level;
//        }
//
//        public String getName() {
//            return this.name;
//        }
//
//        public static DataInfoDto.DataLevelEnum getDataLevelEnum(Integer level) {
//
//            for (DataInfoDto.DataLevelEnum dataLevelEnum : DataInfoDto.DataLevelEnum.values()) {
//                if (dataLevelEnum.getLevel().equals(level)) {
//                    return dataLevelEnum;
//                }
//            }
//            return LOW;
//        }
//
//    }

    public enum DataRiskLevelEnum {

        NON(0, "无风险"),

        /**
         * 低风险
         */
        LOW(1, "低风险"),

        /**
         * 中风险
         */
        MEDIUM(2, "中风险"),

        /**
         * 高风险
         */
        HIGH(3, "高风险"),
        OTHER(4, "其他");

        private Integer level;

        private String name;

        DataRiskLevelEnum(Integer level, String name) {

            this.level = level;
            this.name = name;
        }

        public Integer getLevel() {
            return this.level;
        }

        public String getName() {
            return this.name;
        }

        public static DataInfoDto.DataRiskLevelEnum getDataRiskLevelEnum(Integer level) {

            for (DataInfoDto.DataRiskLevelEnum dataRiskLevelEnum : DataInfoDto.DataRiskLevelEnum.values()) {
                if (dataRiskLevelEnum.getLevel().equals(level)) {
                    return dataRiskLevelEnum;
                }
            }
            return NON;
        }

    }

    @Mapper
    public interface DataInfoDtoMapper {

        DataInfoDto.DataInfoDtoMapper INSTANCE = Mappers.getMapper(DataInfoDto.DataInfoDtoMapper.class);

        DataInfoDto convert(DataInfo dataInfo);

    }

}
