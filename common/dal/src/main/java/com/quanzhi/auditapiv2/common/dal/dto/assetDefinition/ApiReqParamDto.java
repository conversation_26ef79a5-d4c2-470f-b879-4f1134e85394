package com.quanzhi.auditapiv2.common.dal.dto.assetDefinition;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * create at 2021/12/31 3:02 下午
 * @description: 接口请求参数标签
 **/
@Data
@ApiModel
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class ApiReqParamDto {


    @ApiModelProperty("请求类型(get|post|header)")
    private String reqType;

    @ApiModelProperty("请求参数key")
    private String key;

}