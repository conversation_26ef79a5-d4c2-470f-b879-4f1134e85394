package com.quanzhi.auditapiv2.common.dal.dao;

import com.quanzhi.auditapiv2.common.dal.dao.base.IMetabaseDao;
import com.quanzhi.auditapiv2.common.dal.dto.ApiWeaknessDto;
import com.quanzhi.auditapiv2.common.dal.dto.RiskLevelMatchDto;
import com.quanzhi.auditapiv2.common.dal.dto.common.CommonGroupDto;
import com.quanzhi.auditapiv2.common.dal.entity.weakness.WeaknessDistribute;
import com.quanzhi.auditapiv2.common.util.dto.AggregationDto;
import com.quanzhi.auditapiv2.common.util.dto.GroupDto;
import com.quanzhi.metabase.core.model.http.report.HttpWeaknessDateTotalStat;
import com.quanzhi.metabase.core.model.http.weakness.ApiWeakness;
import com.quanzhi.metabase.core.model.http.weakness.State;
import com.quanzhi.metabase.core.model.query.AggregationResult;
import com.quanzhi.metabase.core.model.query.MetabaseQuery;
import com.quanzhi.metabase.core.model.query.ResourceUpdates;

import java.util.Arrays;
import java.util.List;
import java.util.Map;

/**
 * 《接口弱点持久层接口》
 *
 * <AUTHOR> [<EMAIL>]
 * @Project:
 * @Module ID:
 * @Comments:
 * @JDK version used:      <JDK1.8>
 * @since 2020-08-28-下午16:52:18
 */
public interface IApiWeaknessDao extends IMetabaseDao<ApiWeakness> {

    List<String> SHOW_STATES = Arrays.asList("NEW", "REPAIRING", "FIXED");

    /**
     * 根据updateTime查找之后新增的弱点信息
     *
     * @param updateTime
     * @return
     */
    List<ApiWeakness> selectNewestWeaknessByUpdateTime(String updateTime);

    /**
     * 检查弱点是否已被清理
     *
     * @param id
     * @return
     */
    Boolean checkApiWeaknessStatus(String id);

    /**
     * 获取弱点名称、分类的分组统计信息
     *
     * @return
     * @throws Exception
     */
    List<CommonGroupDto> selectWeaknessIdGroup() throws Exception;

    List<CommonGroupDto> groupWeakness(String field) throws Exception;

    Map<String, List<String>> getWeaknessIdsByWeaknessType();

    /**
     * 查询接口弱点列表(分页)
     *
     * @param
     * @param showFields
     * @return
     * @Comments: <对此方法的描述，可以引用系统设计中的描述>
     * <AUTHOR> [<EMAIL>]
     * @since 2020-08-28 16:52
     */
    List<ApiWeakness> selectApiWeaknessList(Map<String, Object> map, String field, Integer sort, Integer page, Integer limit, String category, List<String> showFields) throws Exception;

    List<ApiWeakness> selectApiWeaknessListNoSort(Map<String, Object> map, Integer page, Integer limit) throws Exception;

    List<ApiWeakness> selectApiWeaknessListWithMoreType(Map<String, Object> map, String field, Integer sort, Integer page, Integer limit, List<String> category, List<String> showFields) throws Exception;

    List<ApiWeaknessDto> getNewWeaknessList(Long startTime, Long endTime, Integer page, Integer limit, String field, Integer sort);

    List<ApiWeaknessDto> getFixedWeaknessList(Long startTime, Long endTime, Integer page, Integer limit, String field, Integer sort);

    List<ApiWeaknessDto> getPepairingList(Long startTime, Long endTime, Integer page, Integer limit, String field, Integer sort);

    /**
     * 查询接口弱点列表(分页)
     *
     * @param
     * @return
     * @Comments: <对此方法的描述，可以引用系统设计中的描述>
     * <AUTHOR> [<EMAIL>]
     * @since 2020-08-28 16:52
     */
    List<ApiWeakness> selectActiveApiWeaknessList(Integer page, Integer limit) throws Exception;

    Long getCount(Long startTime, Long endTime, Long startDate, Long endDate, State state);

    long countByLevelArray(List<Integer> weaknessLevel);

    /**
     * 查询接口弱点列表
     *
     * @param
     * @return
     * @Comments: <对此方法的描述，可以引用系统设计中的描述>
     * <AUTHOR> [<EMAIL>]
     * @since 2020-08-28 16:52
     */
    List<ApiWeakness> selectApiWeaknessList(Map<String, Object> map) throws Exception;

    List<CommonGroupDto> groupByMap(Map<String, Object> map);

    /**
     * 查询忽略接口弱点列表
     *
     * @param
     * @return
     * @Comments: <对此方法的描述，可以引用系统设计中的描述>
     * <AUTHOR> [<EMAIL>]
     * @since 2020-08-28 16:52
     */
    List<ApiWeakness> selectIgnoreApiWeaknessList(Map<String, Object> map) throws Exception;

    /**
     * 查询接口弱点数量
     *
     * @param
     * @return
     * @Comments: <对此方法的描述，可以引用系统设计中的描述>
     * <AUTHOR> [<EMAIL>]
     * @since 2020-08-28 16:52
     */
    Long totalCount(Map<String, Object> map, List<String> category) throws Exception;

    /**
     * 发现时间查询接口弱点数量
     *
     * @param
     * @return
     * @Comments: <对此方法的描述，可以引用系统设计中的描述>
     * <AUTHOR> [<EMAIL>]
     * @since 2020-08-28 16:52
     */
    Long totalCountByEarlyTimestamp(Long startDate, Long endDate) throws Exception;

    /**
     * id查询接口弱点
     *
     * @param
     * @return
     * @Comments: <对此方法的描述，可以引用系统设计中的描述>
     * <AUTHOR> [<EMAIL>]
     * @since 2020-08-28 16:52
     */
    ApiWeakness selectApiWeaknessById(String id) throws Exception;

    List<ApiWeakness> selectApiWeaknessByIds(List<String> ids, Integer sort, String field) throws Exception;

    ApiWeakness getApiWeaknessByOperationId(String operationId) throws Exception;

    /**
     * 查询去重后的域名
     *
     * @param
     * @return
     * @Comments: <对此方法的描述，可以引用系统设计中的描述>
     * <AUTHOR> [<EMAIL>]
     * @since 2020-08-28 16:52
     */
    List<AggregationResult> selectHostList(String host, Integer page, Integer limit) throws Exception;

    /**
     * host查询接口弱点数量
     *
     * @param
     * @return
     * @Comments: <对此方法的描述，可以引用系统设计中的描述>
     * <AUTHOR> [<EMAIL>]
     * @since 2020-08-28 16:52
     */
    Long totalCountByHost(String host) throws Exception;

    /**
     * 弱点规则id查询弱点数量
     *
     * @param
     * @return
     * @Comments: <对此方法的描述，可以引用系统设计中的描述>
     * <AUTHOR> [<EMAIL>]
     * @since 2020-08-28 16:52
     */
    Long totalCountByWeaknessId(String weaknessId) throws Exception;

    Long totalCountByWeaknessType(String type) throws Exception;

    List<CommonGroupDto> groupByLevelAndType(String type, List<String> appUris) throws Exception;

    List<CommonGroupDto> groupByLevelAndType(Map<String, Object> map) throws Exception;

    List<CommonGroupDto> groupByIds(List<String> ids, List<String> category) throws Exception;

    Long totalCountByUpdateTime(String updateTime) throws Exception;

    /**
     * 编辑接口弱点
     *
     * @param
     * @return
     * @Comments: <对此方法的描述，可以引用系统设计中的描述>
     * <AUTHOR> [<EMAIL>]
     * @since 2020-08-28 16:52
     */
    ApiWeakness updateApiWeakness(ApiWeakness apiWeakness) throws Exception;

    List<HttpWeaknessDateTotalStat> getNewWeakCount(Long startTime, Long endTime);

    List<HttpWeaknessDateTotalStat> getFixedWeakCount(Long startTime, Long endTime);

    List<HttpWeaknessDateTotalStat> getPepairingWeakCount(Long startTime, Long endTime);

    Map<Integer, Long> getNewLevelAmount(Long startTime, Long endTime, Long startDate, Long endDate, State state);

    /**
     * 新发现弱点口令类数量
     *
     * @param startTime
     * @param endTime
     * @param map
     * @return
     */
    Map<String, Long> getPasswordAmount(Long startTime, Long endTime, Long startDate, Long endDate, Map<String, List<String>> map, State state);

    /**
     * 认证类
     *
     * @param startTime
     * @param endTime
     * @param map
     * @return
     */
    Map<String, Long> getIdentityAmount(Long startTime, Long endTime, Long startDate, Long endDate, Map<String, List<String>> map, State state);

    /**
     * 权限类
     *
     * @param startTime
     * @param endTime
     * @param map
     * @return
     */
    Map<String, Long> getAuthorizationAmount(Long startTime, Long endTime, Long startDate, Long endDate, Map<String, List<String>> map, State state);

    /**
     * 数据类
     *
     * @param startTime
     * @param endTime
     * @param map
     * @return
     */
    Map<String, Long> getDataflowAmount(Long startTime, Long endTime, Long startDate, Long endDate, Map<String, List<String>> map, State state);

    List<Map<String, Long>> getTypeGroup(Map<String, Object> map, Map<String, List<String>> weaknessTypeIds);

    /**
     * 查询接口弱点分组
     *
     * @param
     * @return
     * @Comments: <对此方法的描述，可以引用系统设计中的描述>
     * <AUTHOR> [<EMAIL>]
     * @since 2020-08-28 16:52
     */
    List<AggregationResult> selectApiWeaknessGroup(Map<String, Object> map, Integer sort, String[] groupFields, String category) throws Exception;

    List<AggregationResult> selectApiWeaknessGroup(Map<String, Object> map, Integer sort, String[] groupFields, List<String> categorys) throws Exception;

    /**
     * 弱点类型环形图
     *
     * @param
     * @return
     * @Comments: <对此方法的描述，可以引用系统设计中的描述>
     * <AUTHOR> [<EMAIL>]
     * @since 2021-08-31 16:52
     */
    List<AggregationDto> weaknessTypeDonutChartByAppUri(String appUri, GroupDto groupDto) throws Exception;

    List<Map> groupExposeData();

    Double getWeaknessDimension();

//	Long count();

    List<ApiWeakness> selectBy(String appUri, String weaknessId, List<String> fields);

    boolean exist(String apiUri, String weaknessId);

    void update(String id, Map<String, Object> update);

    void update(MetabaseQuery query, ResourceUpdates resourceUpdates);

    /**
     * 获取大屏概览信息 - 弱点事件、高风险弱点事件
     *
     * @return
     */
    List<Long> getStatistics();

    List<ApiWeakness> select(long startCreateTime);

    /**
     * 获取弱点分布 - 大屏
     *
     * @return
     */
    List<WeaknessDistribute> getWeaknessDistribute();

    MetabaseQuery getMetabaseQuery(Map<String, Object> map) throws Exception;

    org.springframework.data.mongodb.core.query.Criteria getMongoCriteria(Map<String, Object> map) throws Exception;

    long getWeaknessCount(Boolean flag);

    List<WeaknessDistribute> getWeaknessDistributed(boolean flag);

    /**
     * @param uri
     * @return com.quanzhi.auditapiv2.common.dal.dto.RiskLevelMatchDto.Weakness
     * <AUTHOR>
     * @date 2023/3/3 10:42 AM
     * @Description 获取接口的弱点信息
     * @Since
     */
    RiskLevelMatchDto.Weakness getWeakness(String uri);

    void updateWeaknessAppName(String uri, String name);

    List<ApiWeakness> selectByAppUri(String appUri, List<String> fields);
}
