package com.quanzhi.auditapiv2.common.dal.dao.impl.common;

import com.alibaba.fastjson.JSONArray;
import com.quanzhi.auditapiv2.common.dal.dto.group.DataPermissionTypeEnum;
import com.quanzhi.metabase.core.model.http.HttpAppResource;
import com.quanzhi.metabase.core.model.query.MetabaseQuery;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.mongodb.MongoDatabaseFactory;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.gridfs.GridFsCriteria;
import org.springframework.stereotype.Component;

import java.lang.reflect.Field;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * create at 2024/11/14 11:31
 * @description: 处理数据权限相关查询
 **/
@Component
@ModuleMapper(modules = {Module.APP, Module.API, Module.WEAKNESS, Module.RISK, Module.AGGRISK, Module.THREAT, Module.ACCOUNT, Module.IP, Module.FILE})
public class DataPermissionAdapter implements QueryAdapter {

    private Module module;

    @Autowired
    private DataPermissionUtil dataPermissionUtil;


    @Override
    public void query(Object query, Criteria criteria) {
        Map<String, Object> dataPermissionMap = getDataPermissionMap(query);
        if (dataPermissionMap == null || dataPermissionMap.isEmpty()) {
            return;
        }
        boolean existAnd=false;
        if (criteria.getCriteriaObject().containsKey("$and")) {
            existAnd=true;
        }
        if(existAnd){
            for (Map.Entry<String, Object> entry : dataPermissionMap.entrySet()) {
                String key = entry.getKey();
                Object value = entry.getValue();
                DataPermissionTypeEnum dataPermissionTypeEnum = DataPermissionTypeEnum.valueOf(key);
                if (module == Module.RISK || module == Module.AGGRISK || module == Module.THREAT || module == Module.IP || module == Module.FILE || module == Module.ACCOUNT) {
                    //特殊处理 转成对应的应用
                     convertToAppCriteriaWithSpring(dataPermissionTypeEnum, value,criteria);
                } else {
                    String dbKey = dataPermissionUtil.convertDbKey(dataPermissionTypeEnum,module);
                    if (DataPermissionTypeEnum.APP_DEPARTMENT == dataPermissionTypeEnum) {
                        if(value instanceof Collection){
                            criteria =criteria.and(dbKey)
                                    .elemMatch(org.springframework.data.mongodb.core.query.Criteria.where("key")
                                            .is("部门")
                                            .and("value").in(toStringArray(value)));
                        }
                        if(value instanceof String){
                            criteria =criteria.and(dbKey)
                                    .elemMatch(org.springframework.data.mongodb.core.query.Criteria.where("key")
                                            .is("部门")
                                            .and("value").is(value));
                        }
                    } else {
                        if (value instanceof String) {
                            criteria.and(dbKey).is(value);
                        }
                        if (value instanceof Collection) {
                            criteria.and(dbKey).in(toStringArray(value));
                        }
                    }
                }
            }
            return;
        }
        List<Criteria> criteriaList = new ArrayList<>();
        for (Map.Entry<String, Object> entry : dataPermissionMap.entrySet()) {
            String key = entry.getKey();
            Object value = entry.getValue();
            DataPermissionTypeEnum dataPermissionTypeEnum = DataPermissionTypeEnum.valueOf(key);
            Criteria cr = new Criteria();
            if (module == Module.RISK || module == Module.AGGRISK || module == Module.THREAT || module == Module.IP || module == Module.FILE || module == Module.ACCOUNT) {
                //特殊处理 转成对应的应用
                cr = convertToAppCriteriaWithSpring(dataPermissionTypeEnum, value,cr);
            } else {
                String dbKey = dataPermissionUtil.convertDbKey(dataPermissionTypeEnum,module);
                if (DataPermissionTypeEnum.APP_DEPARTMENT == dataPermissionTypeEnum) {
                    if(value instanceof String){
                        cr = org.springframework.data.mongodb.core.query.Criteria.where(dbKey)
                                .elemMatch(org.springframework.data.mongodb.core.query.Criteria.where("key")
                                        .is("部门")
                                        .and("value").is(value));
                    }
                    if(value instanceof Collection){
                        cr = org.springframework.data.mongodb.core.query.Criteria.where(dbKey)
                                .elemMatch(org.springframework.data.mongodb.core.query.Criteria.where("key")
                                        .is("部门")
                                        .and("value").in(toStringArray(value)));
                    }

                } else {
                    if (value instanceof String) {
                        cr.and(dbKey).is(value);
                    }
                    if (value instanceof Collection) {
                        cr.and(dbKey).in(toStringArray(value));
                    }
                }
            }
            criteriaList.add(cr);
        }
        if(criteriaList.size()>0){
            criteria.andOperator(criteriaList);
        }
    }

    public String[] toStringArray(Object values) {
        if (values == null) {
            return new String[0];
        }
        if (values instanceof Collection<?>) {
            return ((Collection<?>) values).stream()
                    .map(Object::toString)
                    .toArray(String[]::new);
        } else {
            return new String[]{values.toString()};
        }
    }


    private Criteria convertToAppCriteriaWithSpring(DataPermissionTypeEnum dataPermissionTypeEnum, Object value,Criteria criteria) {
        List<String> appUriList = dataPermissionUtil.getAppUriList(dataPermissionTypeEnum, value);
        switch (module) {
            case RISK:
            case AGGRISK:
                criteria.and("appUri").in(appUriList);
                break;
            case THREAT:
            case IP:
            case FILE:
            case ACCOUNT:
                criteria.and("appUriList").in(appUriList);
                break;
        }
        return criteria;
    }

    private com.quanzhi.metabase.core.model.query.Criteria convertToAppCriteriaWithMetabase(DataPermissionTypeEnum dataPermissionTypeEnum, Object value) {
        List<String> appUriList = dataPermissionUtil.getAppUriList(dataPermissionTypeEnum, value);
        com.quanzhi.metabase.core.model.query.Criteria criteria = null;
        switch (module) {
            case RISK:
            case AGGRISK:
                criteria = com.quanzhi.metabase.core.model.query.Criteria.where("appUri").in(appUriList);
                break;
            case THREAT:
            case IP:
            case FILE:
            case ACCOUNT:
                criteria = com.quanzhi.metabase.core.model.query.Criteria.where("appUriList").in(appUriList);
                break;
        }
        return criteria;
    }

    @Override
    public void query(Object query, MetabaseQuery metabaseQuery) {
        Map<String, Object> dataPermissionMap = getDataPermissionMap(query);
        if (dataPermissionMap == null || dataPermissionMap.isEmpty()) {
            return;
        }
        com.quanzhi.metabase.core.model.query.Criteria criteria = new com.quanzhi.metabase.core.model.query.Criteria();
        for (Map.Entry<String, Object> entry : dataPermissionMap.entrySet()) {
            String key = entry.getKey();
            Object value = entry.getValue();
            DataPermissionTypeEnum dataPermissionTypeEnum = DataPermissionTypeEnum.valueOf(key);
            com.quanzhi.metabase.core.model.query.Criteria cr = null;
            if (module == Module.RISK || module == Module.AGGRISK || module == Module.THREAT || module == Module.IP || module == Module.FILE || module == Module.ACCOUNT) {
                //特殊处理 转成对应的应用
                cr = convertToAppCriteriaWithMetabase(dataPermissionTypeEnum, value);
            } else {
                String dbKey = dataPermissionUtil.convertDbKey(dataPermissionTypeEnum,module);
                if (DataPermissionTypeEnum.APP_DEPARTMENT == dataPermissionTypeEnum) {
                    List<com.quanzhi.metabase.core.model.query.Criteria> departmentCriteriaList=new ArrayList<>();
                    if(value instanceof Collection){
                        departmentCriteriaList = Arrays.asList(com.quanzhi.metabase.core.model.query.Criteria.where("key").is("部门"),
                                com.quanzhi.metabase.core.model.query.Criteria.where("value").in(value));
                    }else{
                        departmentCriteriaList = Arrays.asList(com.quanzhi.metabase.core.model.query.Criteria.where("key").is("部门"),
                                com.quanzhi.metabase.core.model.query.Criteria.where("value").is(value));
                    }

                    cr = com.quanzhi.metabase.core.model.query.Criteria.where(dbKey)
                            .elemMatch(departmentCriteriaList);
                } else {
                    if (value instanceof String) {
                        cr = com.quanzhi.metabase.core.model.query.Criteria.where(dbKey).is(value);
                    }
                    if (value instanceof Collection) {
                        cr = com.quanzhi.metabase.core.model.query.Criteria.where(dbKey).in(value);
                    }
                }
            }
            criteria.andOperator(cr);
        }
        metabaseQuery.getCriteria().add(criteria);
    }

    private Map<String, Object> getDataPermissionMap(Object query) {
        if (query instanceof Map) {
            Object o = ((Map<String, Object>) query).get("dataPermissionMap");
            return o == null ? null : (Map<String, Object>) o;
        } else {
            Class<?> aClass = query.getClass();
            try {
                Field field = aClass.getDeclaredField("dataPermissionMap");
                field.setAccessible(true);
                Object value = field.get(query);
                return value == null ? null : (Map<String, Object>) value;
            } catch (NoSuchFieldException | IllegalAccessException e) {
                //ignore
            }
        }
        return null;
    }


    @Override
    public void setModule(Module module) {
        this.module = module;
    }
}