package com.quanzhi.auditapiv2.common.dal.dto.query;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * @ClassName HttpAppCriteriaDto
 * @Description TODO
 * <AUTHOR>
 * @Date 2020/10/16 14:16
 **/
@Data
public class HttpAppCriteriaDto implements Serializable {

    @ApiModelProperty(value = "id", name = "id")
    private String id;

    private List<String> tags;

    private String uri;

    private List<String> appUriList;

    /**
     * 是否关注
     */
    @ApiModelProperty(value = "是否关注", name = "followed")
    private Boolean followed;

    /**
     * 是否开启关联
     */
    private Boolean isIpAssociatedEnable;

    private Integer page=1;

    private Integer limit=10;

    private String field;

    private Integer sort;
}
