package com.quanzhi.auditapiv2.common.dal.dto.filter;

import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * create at 2024/5/11 16:33
 * @description: 智能过滤规则查询dto
 **/
@Data
public class SmartFilterCriteriaDTO {

    private String id;
    private String host;
    private String resource;
    private List<String> ids;


    private Integer page;
    private Integer limit;
    private String field;
    private Integer sort;
}