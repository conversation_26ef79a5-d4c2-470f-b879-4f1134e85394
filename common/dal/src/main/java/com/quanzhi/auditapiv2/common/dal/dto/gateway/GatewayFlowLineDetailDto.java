package com.quanzhi.auditapiv2.common.dal.dto.gateway;

import com.quanzhi.auditapiv2.common.dal.dto.LineDataDto;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @date 2023/8/17 17:39
 */

@Data
public class GatewayFlowLineDetailDto {

    //网关接受收速率
    private List<LineDataDto> gatewayFlowRate;
    //gzip包接收速率
    private List<LineDataDto> gzipFlowRate;
    //产品实际接受速率
    private List<LineDataDto> handlerFlowRate;
}
