package com.quanzhi.auditapiv2.common.dal.entity.riskEolink;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class BlockingStrategyLogResult {

    @ApiModelProperty(value = "id", name = "id")
    private String id;

    @ApiModelProperty(value = "部署 IP", name = "deployIp")
    @JsonProperty("deploy_ip")
    private String deployIp;

    @ApiModelProperty(value = "上游id", name = "upstreamId")
    @JsonProperty("upstream_id")
    private String upstreamId;

    @ApiModelProperty(value = "上游描述", name = "upstreamDesc")
    @JsonProperty("upstream_desc")
    private String upstreamDesc;

    /**
     * 策略名称
     */
    @ApiModelProperty(value = "策略名称", name = "strategyName")
    private String strategyName;

    /**
     * 策略类型
     */
    @ApiModelProperty(value = "策略类型", name = "strategyType")
    private String strategyType;

    /**
     * 策略ID
     */
    @ApiModelProperty(value = "策略ID", name = "blockId")
    private String blockId;

    @ApiModelProperty(value = "成功数量", name = "successCount")
    private Integer successCount;

    @ApiModelProperty(value = "失败数量", name = "failCount")
    private Integer failCount;

    @ApiModelProperty(value = "总数量", name = "totalCount")
    private Integer totalCount;

    @ApiModelProperty(value = "ip", name = "ip")
    private String ip;

    @ApiModelProperty(value = "API", name = "api")
    private String api;

    @ApiModelProperty(value = "APP", name = "app")
    private String app;

    @ApiModelProperty(value = "风险", name = "riskId")
    private String riskId;

    @ApiModelProperty(value = "策略来源 ip/api/strategy", name = "type")
    private String createType;


    @ApiModelProperty(value = "修改时间", name = "updateTime")
    private Long updateTime;


    @ApiModelProperty(value = "创建时间", name = "createTime")
    private Long createTime;


}
