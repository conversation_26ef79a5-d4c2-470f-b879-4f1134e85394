package com.quanzhi.auditapiv2.common.dal.dao.base;

import com.alibaba.nacos.api.exception.NacosException;
import com.quanzhi.auditapiv2.common.dal.dto.query.NacosFilterDto;
import com.quanzhi.auditapiv2.common.dal.dto.query.QueryNacos;

import java.util.List;
import java.util.Map;

/**
 * @Author: Linlm
 * @Description:
 * @Date: Created in 2020/7/6 下午1:57
 */
public interface INacos<T> {
    /**
     * 获取 properties类型的nacos配置
     * @param property
     * @param dataId
     * @param group
     * @return
     * @throws NacosException
     */
    public String getPropertyValue(String property, String dataId, String group);

    public Map<String,String> getPropertyValues(List<String> properties, String dataId, String group);

    /**
     * properties类型的nacos配置发布
     * @param property
     * @param newValue
     * @param dataId
     * @param group
     * @throws NacosException
     */
    public void publishPropertyValue(String property, String newValue, String dataId, String group);

    public void publishPropertyValues(Map<String,Object> properties, String dataId, String group);

    /**
     * 根据具体Id获取配置信息
     * @param id
     * @param collectionName
     * @return
     */
    public T findOne(String id, String collectionName);

    public T findOne(QueryNacos queryNacos, String collectionName);

    public long getCount(String collectionName);

    /**
     * 分页查询
     * @param page
     * @param limit
     * @param collectionName
     * @return
     */
    public List<T> page(Integer page, Integer limit, String collectionName);

    public List<T> page(Integer page, Integer limit, String collectionName,boolean isSync);

    public long getCount(List<NacosFilterDto> nacosFilterDtos, String collectionName);

    /**
     * nacos中有过滤条件
     * @param page
     * @param limit
     * @param nacosFilterDtos
     * @param collectionName
     * @return
     */
    public List<T> page(Integer page, Integer limit, List<NacosFilterDto> nacosFilterDtos, String collectionName);

    /**
     *
     * @param page
     * @param limit
     * @param nacosFilterDtos
     * @param collectionName
     * @param isSync 是否走缓存，true的话实时
     * @return
     */
    public List<T> page(Integer page, Integer limit, List<NacosFilterDto> nacosFilterDtos, String collectionName,boolean isSync);


    public long count(QueryNacos queryNacos, String collectionName);

    public List<T> find(QueryNacos queryNacos, String collectionName);

    public List<T> getAll(String collectionName);

    /**
     * 按照id批量删除
     * @param ids
     * @param collectionName
     * @return
     */
    public Boolean delete(List<String> ids, String collectionName);

    /**
     * 根据model中的id去更新model中其他字段的信息
     * @param model
     * @param collectionName
     * @return
     */
    public Boolean update(T model, String collectionName);

    /**
     * 批量更新
     * @param models
     * @param collectionName
     * @return
     */
    public Boolean batchUpdate(List<T> models, String collectionName);

    /**
     * 保存配置 如果入参model中有id，就更新当条记录，没有就生成新的记录
     * @param model
     * @param collectionName
     * @return
     */
    public T save(T model, String collectionName);

    /**
     * 保存单个配置所有信息，一般用于覆盖上传这种
     * @param models
     * @param collectionName
     */
    public void saveAll(List<T> models, String collectionName);

    void reset(List<T> models, String collectionName);
    /**
     * 获取所有符合高级筛选条件的信息
     * @param nacosFilterList
     * @param collectionName
     * @return
     */
    List<T> select(List<NacosFilterDto> nacosFilterList, String collectionName);

    List<T> page(Integer page, Integer limit, List<T> contentList);

}
