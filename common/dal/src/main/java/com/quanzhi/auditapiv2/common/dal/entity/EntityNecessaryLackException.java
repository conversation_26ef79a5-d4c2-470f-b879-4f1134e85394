package com.quanzhi.auditapiv2.common.dal.entity;

import java.text.MessageFormat;

/**
 * <AUTHOR>
 * @date 2020/8/27 上午11:01
 */
public class EntityNecessaryLackException extends  RuntimeException {

    public EntityNecessaryLackException(Exception e) {
            super(e.getMessage());
        }

    public EntityNecessaryLackException(String msg) {
            super(msg);
        }

    public EntityNecessaryLackException(String placeholderMsg, Object... keys) {
            super(MessageFormat.format(placeholderMsg, keys));
        }

}
