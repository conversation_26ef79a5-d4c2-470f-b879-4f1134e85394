package com.quanzhi.auditapiv2.common.dal.entity.workbenchConfig;

import lombok.Data;

import java.util.List;

/**
 * @Auther: yangzixian
 * @Date: 2021/12/30 10:23
 * @Description:
 */
@Data
public class WorkbenchConfig {

    /**
     * id
     */
    private String id;
    /**
     * API等级范围
     */
    private List<String> apiLevel;
    /**
     * 弱点等级范围
     */
    private List<Integer> weaknessLevel;
    /**
     * 风险等级范围
     */
    private List<Integer> riskLevel;
    /**
     * 是否展示API
     */
    private boolean showApi;
    /**
     * 是否展示弱点
     */
    private boolean showWeakness;
    /**
     * 是否展示风险
     */
    private boolean showRisk;
    /**
     * 更新时间
     */
    private long updateTime;

}
