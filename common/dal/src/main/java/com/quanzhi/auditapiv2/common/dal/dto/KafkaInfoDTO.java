package com.quanzhi.auditapiv2.common.dal.dto;

import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@NoArgsConstructor
public class KafkaInfoDTO implements Serializable {

    private String broker;

    private String topicName;

    private String consumerName;

    private Long timeStamp;

    private Long logEndOffset;

    private Double produceSpeed;

    private Double consumeSpeed;

    private Long lag;

}
