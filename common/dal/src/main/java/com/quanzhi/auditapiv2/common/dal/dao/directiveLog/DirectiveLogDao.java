package com.quanzhi.auditapiv2.common.dal.dao.directiveLog;

import com.quanzhi.auditapiv2.common.dal.entity.directive.DirectiveLog;
import com.quanzhi.auditapiv2.common.dal.entity.directive.DirectiveLogSearchDto;
import com.quanzhi.auditapiv2.common.util.utils.ConstantUtil;
import com.quanzhi.auditapiv2.common.util.utils.DataUtil;
import com.quanzhi.metabase.core.model.query.SortOrder;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * @Author: yangzx
 * @Date: 2024/7/9 17:35
 */
@Repository
public class DirectiveLogDao {

    private final MongoTemplate mongoTemplate;

    private String collectionName = "directiveLog";

    public DirectiveLogDao(MongoTemplate mongoTemplate) {
        this.mongoTemplate = mongoTemplate;
    }

    public List<DirectiveLog> list(DirectiveLogSearchDto directiveLogSearchDto) {
        Query query = new Query(buildCriteria(directiveLogSearchDto));
        if (DataUtil.isNotEmpty(directiveLogSearchDto.getPage()) && DataUtil.isNotEmpty(directiveLogSearchDto.getLimit())) {
            query.skip((directiveLogSearchDto.getPage() - 1) * directiveLogSearchDto.getLimit()).limit(directiveLogSearchDto.getLimit());
        }
        //排序
        Sort dbSort = Sort.by(Sort.Direction.DESC, "firstTime", "_id");
        return mongoTemplate.find(query.with(dbSort), DirectiveLog.class, collectionName);
    }

    public Long count(DirectiveLogSearchDto directiveLogSearchDto) {
        return mongoTemplate.count(new Query().addCriteria(buildCriteria(directiveLogSearchDto)), collectionName);
    }

    public Criteria buildCriteria(DirectiveLogSearchDto directiveLogSearchDto) {
        Criteria criteria = new Criteria();
        criteria.and("delFlag").is(false);
        if (DataUtil.isNotEmpty(directiveLogSearchDto)) {
            if (DataUtil.isNotEmpty(directiveLogSearchDto.getId())) {
                criteria.and("_id").is(directiveLogSearchDto.getId());
            }
            if (DataUtil.isNotEmpty(directiveLogSearchDto.getNodeId())) {
                criteria.and("result." + directiveLogSearchDto.getNodeId()).exists(true);
            }
            if (DataUtil.isNotEmpty(directiveLogSearchDto.getRemark())) {
                criteria.and("remark").regex(com.quanzhi.auditapiv2.common.util.utils.DataUtil.regexStrEscape(directiveLogSearchDto.getRemark()));
            }
            if (DataUtil.isNotEmpty(directiveLogSearchDto.getUrl())) {
                criteria.and("url").regex(com.quanzhi.auditapiv2.common.util.utils.DataUtil.regexStrEscape(directiveLogSearchDto.getUrl()));
            }
            if (DataUtil.isNotEmpty(directiveLogSearchDto.getState())) {
                criteria.and("state").is(directiveLogSearchDto.getState());
            }
            if (DataUtil.isNotEmpty(directiveLogSearchDto.getDirectiveValue())) {
                criteria.and("directiveValue").regex(com.quanzhi.auditapiv2.common.util.utils.DataUtil.regexStrEscape(directiveLogSearchDto.getDirectiveValue()));
            }
            if (DataUtil.isNotEmpty(directiveLogSearchDto.getFirstTimeStart()) && DataUtil.isNotEmpty(directiveLogSearchDto.getFirstTimeEnd())) {
                criteria.and("firstTime").gte(directiveLogSearchDto.getFirstTimeStart()).lte(directiveLogSearchDto.getFirstTimeEnd());
            }
            if (DataUtil.isNotEmpty(directiveLogSearchDto.getLastTimeStart()) && DataUtil.isNotEmpty(directiveLogSearchDto.getLastTimeEnd())) {
                criteria.and("lastTime").gte(directiveLogSearchDto.getLastTimeStart()).lte(directiveLogSearchDto.getLastTimeEnd());
            }
        }
        return criteria;
    }

}
