package com.quanzhi.auditapiv2.common.dal.dto;

import com.quanzhi.auditapiv2.common.dal.dto.common.CommonGroupDto;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * @author: yang<PERSON>xian
 * @date: 2022/5/5 15:52
 * @description:
 */
@Data
public class CommonDistributedDto {

    @ApiModelProperty("分布信息")
    private List<CommonGroupDto> commonDistributed;

    @ApiModelProperty("总个数")
    private Long totalCount;

}
