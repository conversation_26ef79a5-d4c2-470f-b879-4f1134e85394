package com.quanzhi.auditapiv2.common.dal.entity.riskEolink;

import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * 管控策略
 */
@Data
public class InterdictionTacticsDetailDto {
    /**
     * 策略uuid
     */
    private String uuid;
    /**
     * 策略名称
     */
    private String name;
    /**
     * 策略描述
     */
    private String desc;

    /**
     * 集群状态列表
     */
    private List<clusterStatuses> clusterStatuses;

    /**
     * 更新时间
     */
    private Date updateTime;

    @Data
    class clusterStatuses{

        /**
         * 集群名
         */
        private String name;

        /**
         * 状态
         */
        private String status;
    }

}
