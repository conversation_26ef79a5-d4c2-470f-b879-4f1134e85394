package com.quanzhi.auditapiv2.common.dal.dto;

import com.quanzhi.audit_core.common.model.DLPPolicy;
import com.quanzhi.audit_core.common.model.DataLabel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR>
 * @class DLPDataLabelDTO
 * @created 2023/3/29 15:15
 * @desc
 **/
@EqualsAndHashCode(callSuper = true)
@Data
public class DLPDataLabelDTO extends DataLabel {


    public boolean valid() {
        if (getName() == null) {
            return false;
        }
        if (getFirstClass() == null) {
            return false;
        }
        if (getLevel() == null) {
            return false;
        }
        if (CollectionUtils.isEmpty(getLocations())) {
            return false;
        }
        DLPPolicy dlpPolicy = getDlpPolicy();
        if (dlpPolicy == null) {
            return false;
        }
        DLPPolicy.Rule detect = dlpPolicy.getDetect();
        if (detect == null) {
            return false;
        }
        if (detect.getDict() == null && detect.getRegex() == null && detect.getBuiltInAlgo() == null && detect.getCustomAlgo() == null) {
            return false;
        }
        if (detect.getDict() != null) {
            if (CollectionUtils.isEmpty(detect.getDict().getValues())) {
                return false;
            }
        }
        if (detect.getRegex() != null) {
            if (CollectionUtils.isEmpty(detect.getRegex().getPatterns())) {
                return false;
            }
        }
        if (detect.getBuiltInAlgo() != null) {
            if (StringUtils.isEmpty(detect.getBuiltInAlgo().getAlgoId())) {
                return false;
            }
        }
        if (detect.getCustomAlgo() != null) {
            if (StringUtils.isEmpty(detect.getCustomAlgo().getValue())) {
                return false;
            }
        }
        return true;
    }
}
