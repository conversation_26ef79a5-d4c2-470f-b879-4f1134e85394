package com.quanzhi.auditapiv2.common.dal.dao.impl;

import com.mongodb.bulk.BulkWriteResult;
import com.quanzhi.audit_core.common.model.DLPPolicy;
import com.quanzhi.auditapiv2.common.dal.dao.DLPPolicyDao;
import org.springframework.data.mongodb.core.BulkOperations;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 * @class DLPPolicyDaoImpl
 * @created 2023/2/24 17:20
 * @desc
 **/
@SuppressWarnings("AlibabaClassNamingShouldBeCamel")
@Repository
public class DefaultDLPPolicyDaoImpl implements DLPPolicyDao {

    public static final String COLLECTION = "dlpPolicy";

    private final MongoTemplate mongoTemplate;

    public DefaultDLPPolicyDaoImpl(MongoTemplate mongoTemplate) {
        this.mongoTemplate = mongoTemplate;
    }

    @Override
    public void save(String label, DLPPolicy policy) {
        if (policy == null) {
            return;
        }
        Query query = Query.query(Criteria.where("_id").is(label));
        Update update = new Update()
                .set("detect", policy.getDetect())
                .set("validate", policy.getValidate())
                .set("filter", policy.getFilter())
                .set("context", policy.getContext())
                .set("mask", policy.getMask());
        mongoTemplate.upsert(query, update, COLLECTION);
    }

    @Override
    public DLPPolicy findOne(String labelId) {
        return mongoTemplate.findOne(Query.query(Criteria.where("_id").is(labelId)), DLPPolicy.class, COLLECTION);
    }

    @Override
    public void remove(String label) {
        mongoTemplate.remove(Query.query(Criteria.where("_id").is(label)), COLLECTION);
    }

    @Override
    public List<DLPPolicy> findAll() {
        return mongoTemplate.findAll(DLPPolicy.class, COLLECTION);
    }

    @Override
    public void bulkUpdate(List<DLPPolicy> policies) {
        BulkOperations ops = mongoTemplate.bulkOps(BulkOperations.BulkMode.UNORDERED, DLPPolicy.class, COLLECTION);
        for (DLPPolicy policy : policies) {
            Update update = Update.update("detect", policy.getDetect())
                    .set("validate", policy.getValidate())
                    .set("filter", policy.getFilter())
                    .set("context", policy.getContext())
                    .set("mask", policy.getMask());
            ;
            ops.updateOne(Query.query(Criteria.where("_id").is(policy.getId())), update);
        }
        BulkWriteResult execute = ops.execute();
        // TODO 祁灵 2023/5/16 15:20: 重试
    }
}
