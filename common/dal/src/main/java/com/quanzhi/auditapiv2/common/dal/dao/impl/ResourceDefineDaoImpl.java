package com.quanzhi.auditapiv2.common.dal.dao.impl;

import com.quanzhi.auditapiv2.common.dal.dao.IResourceDefineDao;
import com.quanzhi.auditapiv2.common.dal.dto.CompositeRuleSearchDto;
import com.quanzhi.auditapiv2.common.dal.dto.assetDefinition.KeywordSplitRuleDto;
import com.quanzhi.auditapiv2.common.util.dto.ListOutputDto;
import com.quanzhi.auditapiv2.common.util.utils.DataUtil;
import com.quanzhi.metabase.client.MetabaseClientTemplate;
import com.quanzhi.metabase.core.model.http.*;
import com.quanzhi.metabase.core.model.http.api.APICompositeResource;
import com.quanzhi.metabase.core.model.http.app.APPCompositeResource;
import com.quanzhi.metabase.core.model.query.*;
import org.docx4j.wml.P;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Repository;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * @Author: Linlm
 * @Description:
 * @Date: Created in 2020/5/15 下午2:25
 */
@Repository("resourceDefineDao")
public class ResourceDefineDaoImpl implements IResourceDefineDao {

    private final MetabaseClientTemplate metabaseClientTemplate;

    public ResourceDefineDaoImpl(MetabaseClientTemplate metabaseClientTemplate) {
        this.metabaseClientTemplate = metabaseClientTemplate;
    }

    @Override
    public CompositeRule saveCompositeRule(CompositeRule compositeRule) {
        compositeRule = metabaseClientTemplate.save(compositeRule, CompositeRule.class);
        metabaseClientTemplate.save(compositeRule);
        return compositeRule;
    }

    @Override
    public CompositeRule getMergedAppCompositeRule(String host) {
        return metabaseClientTemplate.findOne(new MetabaseQuery().where("hosts", Predicate.IS, host)
                .where("rscType", Predicate.IS, CompositeRule.RscTypeEnum.APP.getRscType())
                .where("state", Predicate.IS, CompositeRule.StateEnum.CONFIRMED.name())
                .where("isRepeal",
                        Predicate.IS, false), CompositeRule.class);
    }

    @Override
    public CompositeRule getSplitAPICompositeRuleLike(String ruleLike) {
        return metabaseClientTemplate.findOne(new MetabaseQuery().where("rule", Predicate.REGEX, "^" + Pattern.quote(ruleLike))
                .where("rscType", Predicate.IS, CompositeRule.RscTypeEnum.API.getRscType())
                .where("compositeType", Predicate.IS, CompositeRule.CompositeTypeEnum.SPLIT.getCompositeType())
                .where("state", Predicate.IS, CompositeRule.StateEnum.CONFIRMED.name())
                .where("isRepeal",
                        Predicate.IS, false), CompositeRule.class);
    }

    @Override
    public ListOutputDto<CompositeRule> getCompositeRules(CompositeRuleSearchDto searchDto) {
        MetabaseQuery metabaseQuery = new MetabaseQuery();
        if (DataUtil.isNotEmpty(searchDto.getRule())) {
            metabaseQuery.where("rule", Predicate.REGEX, Pattern.quote(searchDto.getRule().trim()));
        }
        if(DataUtil.isNotEmpty(searchDto.getAccuracyRule())){
            metabaseQuery.where("rule", Predicate.IS, searchDto.getAccuracyRule());
        }
        if (DataUtil.isNotEmpty(searchDto.getCompositeType())) {
            metabaseQuery.where("compositeType", Predicate.IS, searchDto.getCompositeType());
        }
        if (DataUtil.isNotEmpty(searchDto.getRscType())) {
            metabaseQuery.where("rscType", Predicate.IS, searchDto.getRscType());
        }
        if (DataUtil.isNotEmpty(searchDto.getState())) {
            metabaseQuery.where("state", Predicate.IS, searchDto.getState());
        }
        metabaseQuery.where("isRepeal", Predicate.IS, false)
                .where("delFlag", Predicate.IS, false);
        // 先查出count，然后再拼接分页条件
        long count = metabaseClientTemplate.count(metabaseQuery, CompositeRule.class);
        if (searchDto.getLimit() != null) {
            metabaseQuery.limit(searchDto.getLimit());
        } else {
            metabaseQuery.limit(100);
        }
        if (searchDto.getPage() != null) {
            metabaseQuery.skip(metabaseQuery.getLimit() * searchDto.getPage());
        }

        metabaseQuery.sort(Sort.by("updateTime", SortOrder.DESC));


        List<CompositeRule> compositeRules = metabaseClientTemplate.find(metabaseQuery, CompositeRule.class);
        ListOutputDto<CompositeRule> listOutputDto = new ListOutputDto<>();
        listOutputDto.setTotalCount(count);
        if (compositeRules == null) {
            listOutputDto.setRows(Collections.emptyList());
        } else {
            listOutputDto.setRows(compositeRules);
        }
        return listOutputDto;
    }

    @Override
    public boolean existCompositeRules(String rscName) {
        return metabaseClientTemplate.existBy(new MetabaseQuery().where("rscName", Predicate.IS, rscName)
                        .where("isRepeal", Predicate.IS, false)
                        .where("delFlag", Predicate.IS, false)
                , CompositeRule.class);
    }

    @Override
    public boolean existCompositeByRule(String rule) {
        return metabaseClientTemplate.existBy(new MetabaseQuery().where("rule", Predicate.IS, rule)
                        .where("isRepeal", Predicate.IS, false)
                        .where("delFlag", Predicate.IS, false)
                        .where("state", Predicate.IS, CompositeRule.StateEnum.CONFIRMED.name())
                , CompositeRule.class);
    }

    @Override
    public CompositeRule findByRscName(String rscName) {
        return metabaseClientTemplate.findOne(new MetabaseQuery().where("rscName", Predicate.IS, rscName)
                , CompositeRule.class);
    }

    @Override
    public CompositeRule findByRule(String rule) {
        return metabaseClientTemplate.findOne(new MetabaseQuery().where("rule", Predicate.IS, rule)
                , CompositeRule.class);
    }

    @Override
    public CompositeRule findByRuleId(String ruleId) {
        return metabaseClientTemplate.findOne(ruleId, CompositeRule.class);
    }

    @Override
    public CompositeRule getCompositeRuleById(String compositeRuleId) {
        return metabaseClientTemplate.findOne(compositeRuleId, CompositeRule.class);
    }

    @Override
    public HttpApiResource saveByRule(CompositeRule compositeRule) {
        return metabaseClientTemplate.saveByRule(compositeRule.getId(), HttpApiResource.class);
    }

    @Override
    public void cancelResources(Integer compositeType, Integer rscType, MetabaseQuery query) {
        ResourceUpdates resourceUpdates = new ResourceUpdates();
        resourceUpdates.set("restfulFlag", HttpApiFlagComposite.RestfulFlag.RESTFUL_CANCELED);
        resourceUpdates.set("delFlag", false);
        //接口的合并
        if (CompositeRule.RscTypeEnum.API.getRscType().equals(rscType)) {
            metabaseClientTemplate.update(query, resourceUpdates, false, HttpApiResource.class);
        }
        //应用的合并
        if (CompositeRule.RscTypeEnum.APP.getRscType().equals(rscType)) {
            metabaseClientTemplate.update(query, resourceUpdates, false, HttpAppResource.class);
        }
    }

    @Override
    public HttpAppResource matchHttpAppResource(String host) {
        HttpAppResource httpAppResource = metabaseClientTemplate.match(host, null, HttpAppResource.class);
        return httpAppResource;
    }


    /**
     * @param apiUrl
     * <AUTHOR>
     * @description: 接口样例的恢复
     * @date: 2021/7/27
     * @Return void
     */
    @Override
    public void recoverApiSample(String apiUrl, String compositeRuleId) {
        //根据规则ID找到合出来的restful接口
        HttpApiResource restfulApi = metabaseClientTemplate.findOne(new MetabaseQuery().where("compositeRuleId", Predicate.IS, compositeRuleId), HttpApiResource.class);
        if (DataUtil.isEmpty(restfulApi)) {
            return;
        }
        List<HttpApiResource.Sample> samples = restfulApi.getSamples();
        if (DataUtil.isEmpty(samples)) {
            return;
        }
        List<String> delSampleIds = new ArrayList<>();
        for (HttpApiResource.Sample sample : samples) {
            String sampleId = sample.getSampleId();
            HttpApiSample httpApiSample = metabaseClientTemplate.findOne(sampleId, HttpApiSample.class);
            if (httpApiSample != null && apiUrl.equals(httpApiSample.getApiName())) {
                delSampleIds.add(sampleId);
                metabaseClientTemplate.delete(sampleId, HttpApiSample.class);
            }
        }
        //删除对应的样例
        List<HttpApiResource.Sample> newSamples = restfulApi.getSamples().stream().filter(e -> !delSampleIds.contains(e.getSampleId())).collect(Collectors.toList());
        metabaseClientTemplate.update(restfulApi.getId(), new ResourceUpdates().set("samples", newSamples), HttpApiResource.class);
    }


    @Override
    public List<HttpApiResource> findApiList(MetabaseQuery query) {
        return metabaseClientTemplate.find(query, HttpApiResource.class);
    }

    @Override
    public List<HttpAppResource> findAppList(MetabaseQuery query) {
        return metabaseClientTemplate.find(query, HttpAppResource.class);
    }

    @Override
    public <T> List<T> find(MetabaseQuery query, Class<T> entityClass) {
        return metabaseClientTemplate.find(query, entityClass);
    }


    @Override
    public void recoverCancelApi(String id) {
        metabaseClientTemplate.update(id, new ResourceUpdates().set("restfulFlag", HttpApiFlagComposite.RestfulFlag.NONE), HttpApiResource.class);
    }

    @Override
    public void addExcludeResources(String compositeRuleId, String apiUrl, String host) {
        CompositeRule compositeRule = metabaseClientTemplate.findOne(compositeRuleId, CompositeRule.class);
        if (DataUtil.isEmpty(compositeRule)) {
            return;
        }
        List<String> excludeResources = compositeRule.getExcludeResources();
        if (DataUtil.isNotEmpty(apiUrl) && CompositeRule.RscTypeEnum.API.getRscType().equals(compositeRule.getRscType())) {
            if (DataUtil.isNotEmpty(excludeResources) && !excludeResources.contains(apiUrl)) {
                excludeResources.add(apiUrl);
            } else {
                excludeResources = new ArrayList<>();
                excludeResources.add(apiUrl);
            }
        }

        if (DataUtil.isNotEmpty(host) && CompositeRule.RscTypeEnum.APP.getRscType().equals(compositeRule.getRscType())) {
            if (DataUtil.isNotEmpty(excludeResources) && !excludeResources.contains(host)) {
                excludeResources.add(host);
            } else {
                excludeResources = new ArrayList<>();
                excludeResources.add(host);
            }
        }
        metabaseClientTemplate.update(compositeRuleId, ResourceUpdates.create().set("excludeResources", excludeResources), CompositeRule.class);
    }

    /**
     * 恢复取消合并的应用
     *
     * @param host
     */
    @Override
    public void recoverCancelApp(String host) {
        MetabaseQuery query = new MetabaseQuery();
        query.where("restfulFlag", Predicate.IS, HttpApiFlagComposite.RestfulFlag.RESTFUL_CANCELED);
        query.where("host", Predicate.IS, host);
        metabaseClientTemplate.update(query, ResourceUpdates.create().set("restfulFlag", HttpApiFlagComposite.RestfulFlag.NONE), false, HttpAppResource.class);
    }


    @Override
    public KeywordSplitRule saveKeywordSplitRule(KeywordSplitRule keywordSplitRule) {
        if (Boolean.TRUE.equals(keywordSplitRule.getDelFlag())) {
            metabaseClientTemplate.update(keywordSplitRule.getId(), ResourceUpdates.create().set("delFlag", true), KeywordSplitRule.class);
            return metabaseClientTemplate.findOne(keywordSplitRule.getId(), KeywordSplitRule.class);
        } else {
            return metabaseClientTemplate.save(keywordSplitRule);
        }
    }

    @Override
    public KeywordSplitRule findKeywordSplitRule(String id) {
        return metabaseClientTemplate.findOne(id, KeywordSplitRule.class);
    }


    @Override
    public ListOutputDto<KeywordSplitRule> listKeywordSplitRule(KeywordSplitRuleDto keywordSplitRuleDto) {
        ListOutputDto listOutputDto = new ListOutputDto();

        MetabaseQuery metabaseQuery = new MetabaseQuery().alive();
        if (DataUtil.isNotEmpty(keywordSplitRuleDto.getKeyword())) {
            metabaseQuery.where("keyword", Predicate.REGEX, keywordSplitRuleDto.getKeyword());
        }
        long totalCount = metabaseClientTemplate.count(metabaseQuery, KeywordSplitRule.class);
        listOutputDto.setTotalCount(totalCount);

        metabaseQuery.limit(keywordSplitRuleDto.getLimit());
        metabaseQuery.skip((keywordSplitRuleDto.getPage() - 1) * keywordSplitRuleDto.getLimit());
        metabaseQuery.sort(Sort.by("executeTime", SortOrder.DESC), Sort.by("updateTime", SortOrder.DESC));
        List<KeywordSplitRule> keywordSplitRules = metabaseClientTemplate.find(metabaseQuery, KeywordSplitRule.class);
        listOutputDto.setRows(keywordSplitRules);
        return listOutputDto;
    }

    @Override
    public boolean existApp(String host) {
        return metabaseClientTemplate.existBy(new MetabaseQuery().alive().where("host", Predicate.IS, host), HttpAppResource.class);
    }


    @Override
    public void deleteCompositeRule(String id) {
        ResourceUpdates resourceUpdates = new ResourceUpdates();
        resourceUpdates.set("delFlag", true);
        resourceUpdates.set("userDelFlag", true);
        metabaseClientTemplate.update(id, resourceUpdates, CompositeRule.class);
    }

    @Override
    public boolean exist(MetabaseQuery query, Class<?> clz) {
        return metabaseClientTemplate.existBy(query, clz);
    }

    @Override
    public void hardDelete(String id) {
        metabaseClientTemplate.delete(id, CompositeRule.class);
    }

    @Override
    public void update(MetabaseQuery query, ResourceUpdates updates, Class entityClass) {
        metabaseClientTemplate.update(query, updates, false, entityClass);
    }

    @Override
    public ListOutputDto<APICompositeResource> listAPICompositeResource(String compositeRuleId, Pageable pageable) {
        List<APICompositeResource> resources
                = metabaseClientTemplate.find(new MetabaseQuery().where("rules", Predicate.IS, compositeRuleId).limit(pageable.getPageSize())
                .skip((int) pageable.getOffset()), APICompositeResource.class);
        long count = metabaseClientTemplate.count(new MetabaseQuery().where("rules", Predicate.IS, compositeRuleId), APICompositeResource.class);
        return ListOutputDto.page(resources, count);
    }

    @Override
    public ListOutputDto<APPCompositeResource> listAPPCompositeResource(String compositeRuleId, String rule, Pageable pageable) {
        List<APPCompositeResource> resources
                = metabaseClientTemplate.find(new MetabaseQuery().where("rules", Predicate.IS, compositeRuleId).where("data.host", Predicate.REGEX, rule).limit(pageable.getPageNumber())
                .skip((int) pageable.getOffset()), APPCompositeResource.class);
        long count = metabaseClientTemplate.count(new MetabaseQuery().where("rules", Predicate.IS, compositeRuleId).where("data.host", Predicate.REGEX, rule), APPCompositeResource.class);
        return ListOutputDto.page(resources, count);
    }

    @Override
    public HttpAppResource findAppByRule(String ruleId) {
        return metabaseClientTemplate.findOne(new MetabaseQuery().where("compositeRules", Predicate.IS, ruleId), HttpAppResource.class);
    }

    @Override
    public HttpApiResource findApiByRule(String ruleId) {
        return metabaseClientTemplate.findOne(new MetabaseQuery().where("compositeRules", Predicate.IS, ruleId), HttpApiResource.class);
    }


}
