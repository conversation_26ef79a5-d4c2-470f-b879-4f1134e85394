package com.quanzhi.auditapiv2.common.dal.dto.gateway;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.Map;

/**
 * <AUTHOR>
 * @description 网关管理上报status信息转换类
 * @date 2023/3/21 20:53
 */
@Data
public class GatewayAgentManagerStatusDto {
    @JsonProperty("error_code")
    private int errorCode;
    @JsonProperty("error_msg")
    private String errorMsg;
    @JsonProperty("status")
    private Map<String, GatewayAgentDetailStatus> status;

    @Data
    public static class GatewayAgentDetailStatus {
        @JsonProperty("timestamp")
        private long timestamp;
        @JsonProperty("cpu_percent_use_self")
        private double cpuPercentUseSelf;
        @JsonProperty("cpu_percent_total")
        private double cpuPercentTotal;
        @JsonProperty("mem_bytes_use_self")
        private long memBytesUseSelf;
        @JsonProperty("mem_bytes_use_total")
        private long memBytesUseTotal;
        @JsonProperty("disk_bytes_use_total")
        private long diskBytesUseTotal;
        @JsonProperty("upload_rate")
        private long uploadRate;
        @JsonProperty("capture_eth")
        private Map<String, EthStatus> captureEth;
    }

    @Data
    public static class EthStatus {
        @JsonProperty("rx_rate")
        private long rxRate;
        @JsonProperty("tx_rate")
        private long txRate;
    }

    @Override
    public String toString() {
        return "AgentManagerStatusDto{" +
                "errorCode=" + errorCode +
                ", errorMsg='" + errorMsg + '\'' +
                ", status=" + status +
                '}';
    }
}