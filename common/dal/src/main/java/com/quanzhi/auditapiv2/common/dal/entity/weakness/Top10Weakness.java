package com.quanzhi.auditapiv2.common.dal.entity.weakness;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * @author: yang<PERSON>xian
 * @date: 2023.05.24 17:30
 * @description:
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class Top10Weakness {

    private String id;

    private String category;

    private String type;

    private String categoryDesc;

    private LevelEntity levelCntMap = new LevelEntity();

    private List<List<String>> weaknessNames;

    private Long totalCount;
    private String totalBlockStr;

    @Data
    public static class LevelEntity {

        private Long highCnt;
        private Long midCnt;
        private Long lowCnt;

    }

}
