package com.quanzhi.auditapiv2.common.dal.dto.docx;

import lombok.Data;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 *
 * 《明文密码透出》
 *
 *
 * @Project:
 * @Module ID:
 * @Comments:
 * @JDK version used:      <JDK1.8>
 * <AUTHOR> [<EMAIL>]
 * @since 2020-08-28-下午16:52:18
 */
@Data
public class Plainpasswordrsp extends ApiWeaknessExportWord {

    @Mapper
    public interface PlainpasswordrspMapper {

        Plainpasswordrsp.PlainpasswordrspMapper INSTANCE = Mappers.getMapper(Plainpasswordrsp.PlainpasswordrspMapper.class);
        Plainpasswordrsp convert(ApiWeaknessExportWord apiWeaknessExportWord);
    }
}
