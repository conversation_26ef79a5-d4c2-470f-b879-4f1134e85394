package com.quanzhi.auditapiv2.common.dal.dto.llm;

import com.quanzhi.metabase.core.model.filter.SmartFilterRule;
import com.quanzhi.metabase.core.model.http.weakness.State;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * create at 2025/6/27 15:18
 * @description:
 **/
@Data
public class AiInfoResult {

    private TypeEnum type;
    private ApiAiInfo apiAiInfo;
    private ApiWeaknessAiInfo apiWeaknessAiInfo;
    private AggRiskInfoAiInfo aggRiskInfoAiInfo;
    private SmartFilterRuleAiInfo smartFilterRuleAiInfo;

    @Data
    public static final class ApiAiInfo{
        private String uri;
        private String tagBussinessReason;
        private List<String> bussinessTags;
    }


    @Data
    public static final class ApiWeaknessAiInfo{
        private String id;
        private String weaknessOptReason;
        private State state;
    }

    @Data
    public static final class AggRiskInfoAiInfo{
        private String id;
        private String riskOptReason;
        private Integer state;
    }

    @Data
    public static final class SmartFilterRuleAiInfo{
       private SmartFilterRule smartFilterRule;
    }


    public enum TypeEnum{
        API,APP,WEAKNESS,AGG_RISK_INFO,SMART_FILTER_RULE
    }



}