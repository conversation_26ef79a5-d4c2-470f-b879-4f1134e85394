package com.quanzhi.auditapiv2.common.dal.dao.impl;

import com.quanzhi.auditapiv2.common.dal.dao.IApiWeaknessDao;
import com.quanzhi.auditapiv2.common.dal.dao.IHttpApiDao;
import com.quanzhi.auditapiv2.common.dal.dao.IRiskInfoAggDao;
import com.quanzhi.auditapiv2.common.dal.dao.ISysRiskIndexDao;
import com.quanzhi.auditapiv2.common.dal.entity.RiskIndex;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.stereotype.Repository;

/**
 * @Auther: yangzixian
 * @Date: 2021/8/30 16:35
 * @Description:
 */
@Repository
public class SysRiskIndexDaoImpl implements ISysRiskIndexDao {

    @Autowired
    private MongoTemplate mongoTemplate;

    @Autowired
    private IHttpApiDao httpApiDao;

    @Autowired
    private IApiWeaknessDao apiWeaknessDao;

    @Autowired
    private IRiskInfoAggDao riskInfoAggDao;

    private String collectionName = "riskIndex";

    @Override
    public void saveRiskIndex(RiskIndex riskIndex) {
        Update update = new Update();
        update.set("date", riskIndex.getDate());
        update.set("systemRiskLevel", riskIndex.getSystemRiskLevel());
        update.set("systemRiskIndex", riskIndex.getSystemRiskIndex());
        update.set("systemRiskIndexTrend", riskIndex.getSystemRiskIndexTrend());
        mongoTemplate.upsert(new Query().addCriteria(Criteria.where("date").is(riskIndex.getDate())), update, collectionName);
    }

    @Override
    public int getRiskIndex() {
        //计算资产维度
        Double assetDimension = httpApiDao.getAssetDimension();
        //计算弱点维度
        Double weaknessDimension = apiWeaknessDao.getWeaknessDimension();
        //计算风险维度
        Double riskDimension = riskInfoAggDao.getRiskDimension();
        //计算风险指数
        Double riskIndexDouble = (assetDimension + weaknessDimension + riskDimension) * 100 / 3;
        return riskIndexDouble.intValue();
    }

    @Override
    public RiskIndex getRiskIndexByDate(String date) {
        return mongoTemplate.findOne(new Query().addCriteria(Criteria.where("date").is(date)), RiskIndex.class, collectionName);
    }

}
