package com.quanzhi.auditapiv2.common.dal.dto;

import com.quanzhi.audit_core.common.model.NetworkSegment;
import com.quanzhi.auditapiv2.common.dal.entity.DstIpResolve;
import com.quanzhi.auditapiv2.common.dal.entity.DstIpResolve.ResolveTypeEnum;
import com.quanzhi.auditapiv2.common.util.utils.DataUtil;
import com.quanzhi.auditapiv2.common.util.utils.ServiceException;
import com.quanzhi.awdb_core.util.IpUtil;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

@NoArgsConstructor
@Data
public class DstIpResolveDto {
    @ApiModelProperty(value = "id", name = "id")
    private String id;

    /**
     * 优先级
     */
    @ApiModelProperty(value = "优先级", name = "priority")
    private Integer priority = 1;

    /**
     * 字段名
     */
    @ApiModelProperty(value = "字段名", name = "filedName")
    private String filedName;

    /**
     * 解析方式
     *
     * @see ResolveTypeEnum
     */
    @ApiModelProperty(value = "解析方式", name = "resolveType")
    private Integer resolveType;

    /**
     * 内部IP段
     */
    @ApiModelProperty(value = "内部IP段", name = "innerIpSegs")
    private List<IpSegmentDto> innerIpSegs;

    /**
     * 内部IP段
     */
    @ApiModelProperty(value = "IP过滤段", name = "innerIpSegs")
    private List<String> filterIps;

    /**
     * 方向
     */
    @ApiModelProperty(value = "方向", name = "direction")
    private Integer direction = DstIpResolve.DirectionEnum.REVERSE.value();

    @Data
    public static class IpSegmentDto {
        /**
         * 开始IP
         */
        @ApiModelProperty(value = "开始IP", name = "startIp")
        private String startIp;

        /**
         * 结束IP
         */
        @ApiModelProperty(value = "结束IP", name = "endIp")
        private String endIp;

        @ApiModelProperty("ip类型")
        private Integer ipType;
    }


    public static List<DstIpResolve> dtos2model(List<DstIpResolveDto> dstIpResolveDtos) {
        List<DstIpResolve> dstIpResolves = new ArrayList<>();
        if (CollectionUtils.isEmpty(dstIpResolveDtos)) {
            return dstIpResolves;
        }

        for (DstIpResolveDto i : dstIpResolveDtos) {
            DstIpResolve dstIpResolve = i.dto2model();
            dstIpResolves.add(dstIpResolve);
        }

        return dstIpResolves;
    }

    public DstIpResolve dto2model() {
        DstIpResolve dstIpResolve = new DstIpResolve();
        dstIpResolve.setId(this.getId());
        dstIpResolve.setPriority(this.priority);
        dstIpResolve.setResolveType(this.resolveType);
        dstIpResolve.setFiledName(this.filedName);
        dstIpResolve.setDirection(this.direction);
        dstIpResolve.setFilterIps(this.filterIps);

        List<DstIpResolve.IpSegment> innerIpSegs = new ArrayList<>();

        if (DataUtil.isNotEmpty(this.innerIpSegs)) {
            for (IpSegmentDto i : this.innerIpSegs) {
                DstIpResolve.IpSegment innerIpSeg = new DstIpResolve.IpSegment();
                innerIpSeg.setStartIp(i.getStartIp());
                innerIpSeg.setEndIp(i.getEndIp());
                innerIpSeg.setIpType(i.getIpType());

                innerIpSegs.add(innerIpSeg);
            }
        }

        // 兼容审计的 filterIps 字段
        List<DstIpResolveDto.IpSegmentDto> innerIpSegsCovert = filterIds2IpSegs(filterIps);
        if (DataUtil.isNotEmpty(innerIpSegsCovert)) {
            for (IpSegmentDto i : innerIpSegsCovert) {
                DstIpResolve.IpSegment innerIpSeg = new DstIpResolve.IpSegment();
                innerIpSeg.setStartIp(i.getStartIp());
                innerIpSeg.setEndIp(i.getEndIp());
                innerIpSeg.setIpType(i.getIpType());

                innerIpSegs.add(innerIpSeg);
            }
        }

        dstIpResolve.setInnerIpSegs(innerIpSegs);

        return dstIpResolve;
    }

    public static void isValid(List<DstIpResolveDto> dstIpResolveDtos) {
        if (DataUtil.isEmpty(dstIpResolveDtos)) {
            return;
        }

        Set<Integer> prioritySet = new HashSet<>();
        for (DstIpResolveDto i : dstIpResolveDtos) {
            DstIpResolveDto.isValid(i);

            boolean added = prioritySet.add(i.getPriority());
            if (!added) {
                throw new ServiceException("优先级不可重复");
            }
        }
    }

    public static void isValid(DstIpResolveDto dstIpResolveDto) {
        if (dstIpResolveDto.getPriority() < 1 || dstIpResolveDto.getPriority() > 100)
            throw new ServiceException("优先级不可以小于1或者超过100");
        if (DataUtil.isEmpty(dstIpResolveDto.getResolveType())) {
            throw new ServiceException("解析位置不能为空");
        }

        if (!DstIpResolve.ResolveTypeEnum.exist(dstIpResolveDto.getResolveType())) {
            throw new ServiceException("请填写合法的解析位置");
        }

        if (dstIpResolveDto.direction != null && dstIpResolveDto.direction != 0 && dstIpResolveDto.direction != 1) {
            throw new ServiceException("请填写合法的取值顺序");
        }

        if (dstIpResolveDto.getFiledName() == null || dstIpResolveDto.getFiledName().isEmpty()) {
            throw new ServiceException("字段名称不可为空");
        }

        if (dstIpResolveDto.getFiledName().length() > 50) {
            throw new ServiceException("字段路径字符数不可以超过50个字符");
        }

        // 兼容审计的 filterIps 字段
        if (DataUtil.isNotEmpty(dstIpResolveDto.getFilterIps())) {
            for (String i : dstIpResolveDto.getFilterIps()) {
                if (i.contains("/")) {
                    if (!IpUtil.isIPV6_IPV4(i.split("/")[0])) {
                        throw new ServiceException("配置的IP不合法");
                    }
                } else if (i.contains("-")) {
                    if (!IpUtil.isIPV6_IPV4(i.split("-")[0])) {
                        throw new ServiceException("配置的IP不合法");
                    }

                    if (!IpUtil.isIPV6_IPV4(i.split("-")[1])) {
                        throw new ServiceException("配置的IP不合法");
                    }
                } else {
                    if (!i.contains("*")) {
                        if (!IpUtil.isIPV6_IPV4(i)) {
                            throw new ServiceException("配置的IP不合法");
                        }
                    }
                }
            }
        }
    }

    public static List<DstIpResolveDto> models2dots(List<DstIpResolve> dstIpResolves) {

        if (DataUtil.isEmpty(dstIpResolves)) {
            return null;
        }

        List<DstIpResolveDto> dstIpResolveDtos = new ArrayList<>();
        for (DstIpResolve i : dstIpResolves) {
            DstIpResolveDto dstIpResolveDto = new DstIpResolveDto(i);
            dstIpResolveDtos.add(dstIpResolveDto);
        }

        return dstIpResolveDtos;
    }

    public DstIpResolveDto(DstIpResolve dstIpResolve) {
        if (DataUtil.isEmpty(dstIpResolve)) {
            return;
        }

        this.id = dstIpResolve.getId();
        this.filedName = dstIpResolve.getFiledName();
        this.priority = dstIpResolve.getPriority();
        this.resolveType = dstIpResolve.getResolveType();
        this.direction = dstIpResolve.getDirection();
        this.innerIpSegs = new ArrayList<>();
        this.filterIps = dstIpResolve.getFilterIps();
        if (DataUtil.isEmpty(filterIps)) {
            this.filterIps = new ArrayList<>();
        }

        List<DstIpResolve.IpSegment> innerIpSegs_ = dstIpResolve.getInnerIpSegs();
        if (DataUtil.isNotEmpty(innerIpSegs_)) {
            for (DstIpResolve.IpSegment i : innerIpSegs_) {
                IpSegmentDto innerIpSeg = new IpSegmentDto();
                innerIpSeg.setStartIp(i.getStartIp());
                innerIpSeg.setEndIp(i.getEndIp());
                innerIpSeg.setIpType(i.getIpType());

                this.innerIpSegs.add(innerIpSeg);
            }
        }

        // 兼容审计 filterIps 字段
        List<DstIpResolveDto.IpSegmentDto> innerIpSegsCovert = filterIds2IpSegs(dstIpResolve.getFilterIps());
        if (DataUtil.isNotEmpty(innerIpSegsCovert)) {
            for (IpSegmentDto i : innerIpSegsCovert) {
                IpSegmentDto innerIpSeg = new IpSegmentDto();
                innerIpSeg.setStartIp(i.getStartIp());
                innerIpSeg.setEndIp(i.getEndIp());
                innerIpSeg.setIpType(i.getIpType());

                this.innerIpSegs.add(innerIpSeg);
            }
        }
    }

    private List<DstIpResolveDto.IpSegmentDto> filterIds2IpSegs(List<String> filterIps) {
        if (DataUtil.isNotEmpty(filterIps)) {
            List<DstIpResolveDto.IpSegmentDto> innerIpSegs = new ArrayList<>();

            for (String i : filterIps) {
                IpSegmentDto innerIpSeg = new IpSegmentDto();

                if (i.contains("/")) {
                    innerIpSeg.setStartIp(i.trim());
                    innerIpSeg.setIpType(NetworkSegment.IpTypeEnum.IP_MASK.value());
                } else if (i.contains("-")) {
                    innerIpSeg.setStartIp(i.split("-")[0].trim());
                    innerIpSeg.setEndIp(i.split("-")[1].trim());
                    innerIpSeg.setIpType(NetworkSegment.IpTypeEnum.IP_SEG.value());
                } else {
                    innerIpSeg.setStartIp(i.trim());
                    innerIpSeg.setIpType(NetworkSegment.IpTypeEnum.IP.value());
                }

                innerIpSegs.add(innerIpSeg);
            }

            return innerIpSegs;
        }

        return new ArrayList<>();
    }
}
