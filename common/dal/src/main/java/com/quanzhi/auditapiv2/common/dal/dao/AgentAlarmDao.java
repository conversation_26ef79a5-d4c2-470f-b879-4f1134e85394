package com.quanzhi.auditapiv2.common.dal.dao;


import com.quanzhi.auditapiv2.common.dal.dao.base.IBaseDao;
import com.quanzhi.auditapiv2.common.dal.entity.AgentAlarm;
import org.springframework.data.mongodb.core.query.Criteria;

import java.util.List;


public interface AgentAlarmDao extends IBaseDao<AgentAlarm> {

    Long countByIpAndTime(String gatewayIp, String agentIp, Long startTime, Long endTime);

    List<AgentAlarm> pageByIpAndTime(String gatewayIp, String agentIp, Long startTime, Long endTime, Integer page, Integer limit);

    Long countByCriteria(Criteria criteria);
}
