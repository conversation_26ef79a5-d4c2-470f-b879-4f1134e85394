package com.quanzhi.auditapiv2.common.dal.dto;

import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @description: ip查询条件
 **/
@Data
public class IpSearchDetailDto {
    /**
     * 网段
     */
    private List<String> accessDomainIds;

    /**
     * 风险等级
     */
    private List<Integer> riskLevel;

    private String nodeId;

    private String appList;

    private String city;

    private Boolean blockFlag;

    private String accessDomainIdsOperator;

    private String uaTypesOperator;

    private String riskNamesOperator;

    private String rspDataLabelListOperator;

    private String firstDateType;

    private String lastDateType;

    private List<String> uaTypes;

    private List<String> rspDataLabelList;

    private List<String> riskNames;

    private List<Long> firstDate;

    private List<Long> lastDate;

    private FieldOperateMap fieldOperateMap;

    private String ip;

    private Map<String,Object> dataPermissionMap;

    @Data
    public static class FieldOperateMap {
        private String accessDomainIds;
        private String uaTypes;
        private String riskNames;
        private String rspDataLabelList;
    }

    public enum FieldEnum {

        accessDomainIds("accessDomainIds"),
        appUriList("appUriList"),
        blockFlag("blockFlag"),
        rspDataLabelList("rspDataLabelList"),
        uaTypes("uaTypes"),
        riskNames("riskInfo.riskNames"),
        firstDate("firstDate"),
        lastDate("lastDate"),
        city("city"),
        country("country"),
        province("province"),
        and("and"),
        or("or"),
        ip("ip"),
        nid("nodes.nid"),
        riskLevel("riskLevel");

        String name;

        FieldEnum(String name) {

            this.name = name;
        }

        public String getName() {
            return name;
        }

    }


}