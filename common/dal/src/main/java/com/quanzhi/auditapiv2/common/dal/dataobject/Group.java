package com.quanzhi.auditapiv2.common.dal.dataobject;

import lombok.Data;

/**
 * <AUTHOR>
 * create at 2024/11/13 11:32
 * @description: 数据组
 **/
@Data
public class Group {

    private String id;

    private String name;

    private String description;
    /**
     * 0 禁用
     * 1 启用
     */
    private Integer status;
    /**
     * 类型
     * 1 内置
     * 2 自定义
     */
    private Integer type;


    private Long createTime;

    private Long updateTime;

    private Boolean delFlag;
}