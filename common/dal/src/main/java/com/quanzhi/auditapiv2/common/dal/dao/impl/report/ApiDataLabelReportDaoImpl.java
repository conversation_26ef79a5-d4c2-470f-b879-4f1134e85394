package com.quanzhi.auditapiv2.common.dal.dao.impl.report;

import com.quanzhi.audit_core.common.utils.DataUtil;
import com.quanzhi.audit_core.common.utils.StringUtils;
import com.quanzhi.auditapiv2.common.dal.base.Sort;
import com.quanzhi.auditapiv2.common.dal.dao.report.ApiDataLabelCriteria;
import com.quanzhi.auditapiv2.common.dal.dao.report.ApiDataLabelReportDao;
import com.quanzhi.auditapiv2.common.dal.dao.report.DataLabelStatCriteria;
import com.quanzhi.auditapiv2.common.util.dto.ListOutputDto;
import com.quanzhi.metabase.client.MetabaseClientTemplate;
import com.quanzhi.metabase.core.model.http.HttpApiResource;
import com.quanzhi.metabase.core.model.http.report.ApiLabelDateStat;
import com.quanzhi.metabase.core.model.query.Criteria;
import com.quanzhi.metabase.core.model.query.MetabaseQuery;
import com.quanzhi.metabase.core.model.query.Predicate;
import com.quanzhi.metabase.core.model.query.SortOrder;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Repository;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

/**
 * @Author: HaoJun
 * @Date: 2020/9/14 3:08 下午
 */
@Repository
public class ApiDataLabelReportDaoImpl implements ApiDataLabelReportDao {

    private final MetabaseClientTemplate metabaseClientTemplate;

    public ApiDataLabelReportDaoImpl(MetabaseClientTemplate metabaseClientTemplate) {
        this.metabaseClientTemplate = metabaseClientTemplate;
    }

    @Override
    public ListOutputDto<HttpApiResource> listNewLabelApi(ApiDataLabelCriteria criteria) {
        checkParamValid(criteria);
        fixPage(criteria);
        MetabaseQuery metabaseQuery = convertBaseQuery(criteria);
        metabaseQuery.getCriteria().get(0)
                .andOperator(Criteria.where("dataLabels").in(criteria.getDataLabel()));
        metabaseQuery.getCriteria().get(0).andOperator(
                Criteria.where("labelConfigs.labelDetails").elemMatch(Arrays.asList(new Criteria()
                                .andOperator(Criteria
                                .where("discoverTimestamp")
                                .gte(criteria.getStartTimestamp()), Criteria
                                .where("discoverTimestamp")
                                .lte(criteria.getEndTimestamp()))
                        , Criteria.where("labelId").in(criteria.getDataLabel()))));
        // 先查询数量
        Long count = metabaseClientTemplate.count(metabaseQuery, HttpApiResource.class);
        // 如果总数不为空，再尝试拼接参数查询具体列
        if (count != null && count > 0) {
            bindPage(criteria, metabaseQuery);
            List<HttpApiResource> apiResources = metabaseClientTemplate.find(metabaseQuery, HttpApiResource.class);
            return ListOutputDto.page(apiResources, count);
        } else {
            return ListOutputDto.returnNull();
        }
    }

    @Override
    public ListOutputDto<HttpApiResource> listMissingLabelApi(ApiDataLabelCriteria criteria) {
        checkParamValid(criteria);
        fixPage(criteria);
        MetabaseQuery metabaseQuery = convertBaseQuery(criteria);

        metabaseQuery.getCriteria().get(0).andOperator(Criteria.where("labelConfigs.labelDetails")
                .elemMatch(Arrays.asList(Criteria
                                .where("lastActiveTimestamp")
                                .lt(criteria.getStartTimestamp())
                        , Criteria.where("labelId").is(criteria.getDataLabel()))));
        // 先查询数量
        Long count = metabaseClientTemplate.count(metabaseQuery, HttpApiResource.class);
        if (count != null && count > 0) {
            bindPage(criteria, metabaseQuery);
            List<HttpApiResource> apiResources = metabaseClientTemplate.find(metabaseQuery, HttpApiResource.class);
            // 从报表中移除该标签
            if (!org.springframework.util.CollectionUtils.isEmpty(apiResources)) {
                for (HttpApiResource apiResource : apiResources) {
                    if (apiResource.getReqDataLabels() != null
                            && apiResource.getReqDataLabels().contains(criteria.getDataLabel())) {
                        apiResource.getReqDataLabels().remove(criteria.getDataLabel());
                    }
                    if (apiResource.getRspDataLabels() != null
                            && apiResource.getRspDataLabels().contains(criteria.getDataLabel())) {
                        apiResource.getRspDataLabels().remove(criteria.getDataLabel());
                    }
                    if (apiResource.getDataLabels() != null
                            && apiResource.getDataLabels().contains(criteria.getDataLabel())) {
                        apiResource.getDataLabels().remove(criteria.getDataLabel());
                    }
                }
            }
            return ListOutputDto.page(apiResources, count);
        } else {
            return ListOutputDto.returnNull();
        }
    }

    @Override
    public List<ApiLabelDateStat> listStat(DataLabelStatCriteria criteriaDto) {
        MetabaseQuery metabaseQuery = new MetabaseQuery();
        Criteria criteria = new Criteria();
        criteria.andOperator(Criteria.where("date").gte(criteriaDto.getStartDate())
                , Criteria.where("date").lte(criteriaDto.getEndDate()), Criteria.where("labelId")
                        .in(criteriaDto.getLabels()));
        metabaseQuery.setCriteria(Collections.singletonList(criteria));
        return metabaseClientTemplate.find(metabaseQuery, ApiLabelDateStat.class);
    }

    /**
     * 因为产品端的page从1开始
     *
     * @param criteria
     */
    private void fixPage(ApiDataLabelCriteria criteria) {
        criteria.setPage(criteria.getPage() - 1);
    }

    private void checkParamValid(ApiDataLabelCriteria criteria) {
        if (criteria == null) {
            throw new IllegalArgumentException("criteria can't be null !");
        }
        if (criteria.getDataLabel() == null || criteria.getDataLabel().isEmpty()) {
            throw new IllegalArgumentException("missing dataLabel param.");
        }
        if (criteria.getStartTimestamp() <= 0 || criteria.getEndTimestamp() <= 0) {
            throw new IllegalArgumentException("missing start or end timestamp param.");
        }
    }

    /**
     * 基础查询条件拼接，MetabaseQuery中只有一个Criteria
     *
     * @param criteria
     * @return
     */
    private MetabaseQuery convertBaseQuery(ApiDataLabelCriteria criteria) {
        MetabaseQuery metabaseQuery = new MetabaseQuery();
        // ============== 单字段查询 start ==============
        if (CollectionUtils.isNotEmpty(criteria.getReqDataLabels())) {
            metabaseQuery.where("reqDataLabels", Predicate.ALL, criteria.getReqDataLabels());
        }
        if (CollectionUtils.isNotEmpty(criteria.getRspDataLabels())) {
            metabaseQuery.where("rspDataLabels", Predicate.ALL, criteria.getRspDataLabels());
        }
        if (CollectionUtils.isNotEmpty(criteria.getFeatureLabels())) {
            metabaseQuery.where("featureLabels", Predicate.ALL, criteria.getFeatureLabels());
        }
        if (CollectionUtils.isNotEmpty(criteria.getVisitDomains())) {
            metabaseQuery.where("visitDomains", Predicate.ALL, criteria.getVisitDomains());
        }
        if (CollectionUtils.isNotEmpty(criteria.getTerminals())) {
            metabaseQuery.where("terminals", Predicate.ALL, criteria.getTerminals());
        }
        if (CollectionUtils.isNotEmpty(criteria.getClassifications())) {
            metabaseQuery.where("classifications", Predicate.ALL, criteria.getClassifications());
        }
        if (StringUtils.isNotEmpty(criteria.getUri())) {
            metabaseQuery.where("uri", Predicate.LIKE, DataUtil.regexStrEscape(criteria.getUri()));
        }
        // ============== 单字段查询 end ==============
        // 避免 mongodb Criteria already contains 'xxx'
        Criteria andCriteria = new Criteria();
        if (metabaseQuery.getCriteria().size() > 0) {
            andCriteria.andOperator(metabaseQuery.getCriteria()
                    .toArray(new Criteria[metabaseQuery.getCriteria().size()]));
        }
        if (criteria.getStartDiscoverTimestamp() != null && criteria.getStartDiscoverTimestamp() > 0) {
            andCriteria.andOperator(Criteria.where("discoverTime")
                    .gte(criteria.getStartDiscoverTimestamp()));
        }
        if (criteria.getEndDiscoverTimestamp() != null && criteria.getEndDiscoverTimestamp() > 0) {
            andCriteria.andOperator(Criteria.where("discoverTime")
                    .lte(criteria.getEndDiscoverTimestamp()));
        }
        metabaseQuery.setCriteria(Arrays.asList(andCriteria));
        return metabaseQuery;
    }

    private void bindPage(ApiDataLabelCriteria criteria, MetabaseQuery metabaseQuery) {
        metabaseQuery.skip(criteria.getSize() * criteria.getPage()).limit(criteria.getSize());
        if (criteria.getSort() != null
                && criteria.getSort().getOrders() != null) {
            List<com.quanzhi.metabase.core.model.query.Sort> sorts = new ArrayList<>();
            for (Sort.Order order : criteria.getSort().getOrders()) {
                String property = order.getProperty();
                if ("totalVisits".equals(property)) {
                    property = "apiStat.totalVisits";
                }
                com.quanzhi.metabase.core.model.query.Sort sort
                        = com.quanzhi.metabase.core.model.query.Sort.by(property
                        , order.getDirection() == Sort.Direction.ASC
                                ? SortOrder.ASC : SortOrder.DESC);
                sorts.add(sort);
            }
            metabaseQuery.sort(sorts.toArray(new com.quanzhi.metabase.core.model.query.Sort[sorts.size()]));
        }
    }

}
