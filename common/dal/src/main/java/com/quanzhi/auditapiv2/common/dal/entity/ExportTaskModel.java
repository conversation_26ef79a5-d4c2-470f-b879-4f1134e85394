package com.quanzhi.auditapiv2.common.dal.entity;
import com.quanzhi.auditapiv2.common.util.entity.ExportTaskType;
import com.quanzhi.auditapiv2.common.util.utils.DateUtil;
import com.quanzhi.metabase.common.utils.DateUtils;
import lombok.Data;
import org.springframework.util.StringUtils;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2019-12-08
 * @time 12:03
 */
@Data
public class ExportTaskModel {
    /**
     * 任务ID
     */
    private String id;
    /**
     * 任务类型
     */
    private ExportTaskType taskType;

    /**
     * 任务名称
     */
    private String taskTypeName;
    /**
     * 任务状态
     */
    private ExportTaskStatus taskStatus = ExportTaskStatus.RUNNABLE;
    /**
     * 出错信息
     */
    private String errorMsg;
    /**
     * 结果文件
     */
    private List<String> resultFiles;
    /**
     * 任务创建时间
     */
    private Long createTime;
    /**
     * 任务创建用户
     */
    private String createdBy;
    /**
     * 任务开始时间
     */
    private Long startTime;
    /**
     * 任务结束时间
     */
    private Long stopTime;

    private String userId;
    /**
     * 序列化后的参数
     */
    private String serializedParams;

    private String resultFilePath;

    private String resultFileName;

    /**
     * 2.7迭代增加报告名称字段
     * 这个字段默认由 模块字段和 _ + YYYYMMDDHHMMSS
     * 可以由用户输入改变，用户输入后导出文件以用户输入为准
     */
    private String taskName;

    public String buildTaskName () {

        if( !StringUtils.isEmpty( this.taskName  ) ) {
            return this.taskName;
        }
        //taskTypeName是有 导出 开头的，但名称中不要
        return this.taskTypeName.substring(2) + "_" +  DateUtils.getDate(new Date( this.createTime ), DateUtil.DATE_PATTERN.YYYYMMDDHHMMSS);
    }
}
