package com.quanzhi.auditapiv2.common.dal.dao.impl.common;

import com.mongodb.client.result.DeleteResult;
import com.quanzhi.auditapiv2.common.dal.dao.common.QuickConfigDao;
import com.quanzhi.auditapiv2.common.dal.entity.common.QuickConfig;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Repository;

/**
 * <AUTHOR>
 * @date 2021/8/30 4:15 下午
 */
@Repository
public class QuickConfigDaoImpl implements QuickConfigDao {

    private final MongoTemplate mongoTemplate;

    public QuickConfigDaoImpl(MongoTemplate mongoTemplate) {
        this.mongoTemplate = mongoTemplate;
    }

    @Override
    public QuickConfig getOne(String key, String group) {
        return mongoTemplate.findOne(new Query(Criteria.where("key").is(key).and("group").is(group)),
                QuickConfig.class);
    }

    @Override
    public QuickConfig save(QuickConfig config) {
        if (config.getCreateTime() == null){
            config.setCreateTime(System.currentTimeMillis());
        }
        config.setUpdateTime(System.currentTimeMillis());
        return mongoTemplate.save(config);
    }

    @Override
    public boolean remove(String id) {
        QuickConfig quickConfig = mongoTemplate.findById(id, QuickConfig.class);
        DeleteResult deleteResult = mongoTemplate.remove(quickConfig);
        return deleteResult != null && deleteResult.wasAcknowledged();
    }
}
