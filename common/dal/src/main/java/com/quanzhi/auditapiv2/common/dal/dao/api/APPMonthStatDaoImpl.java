package com.quanzhi.auditapiv2.common.dal.dao.api;

import com.quanzhi.auditapiv2.common.dal.dao.APPMonthStatDao;
import com.quanzhi.auditapiv2.common.dal.dao.base.impl.MetabaseDaoImpl;
import com.quanzhi.metabase.core.model.http.app.HttpAppMonthStat;
import com.quanzhi.metabase.core.model.query.MetabaseQuery;
import com.quanzhi.metabase.core.model.query.Predicate;
import org.springframework.stereotype.Repository;

import java.util.List;
@Repository
public class APPMonthStatDaoImpl extends MetabaseDaoImpl<HttpAppMonthStat> implements APPMonthStatDao {
    @Override
    public List<HttpAppMonthStat> getAPIMonthStat(String uri, List<String> months) {
        return metabaseClientTemplate.find(new MetabaseQuery().where("uri", Predicate.IS, uri).where("month", Predicate.IN, months), HttpAppMonthStat.class);
    }
}