package com.quanzhi.auditapiv2.common.dal.dto.weakness;

import com.quanzhi.metabase.core.model.http.weakness.State;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;
import java.util.Map;


/**
 * <AUTHOR>
 * @date 2021/9/1 9:43 上午
 */
@Data
@ApiModel("弱点处理")
public class WeaknessSuggestionDto {
    @ApiModelProperty("弱点主键ID")
    private String id;
    @ApiModelProperty("修改后的状态")
    private State state;
    @ApiModelProperty("处理建议")
    private String suggestion;
    @ApiModelProperty("批量操作")
    private List<BatchOperation> batchOperations;
    @ApiModelProperty("弱点搜索参数")
    private Map<String, Object> params;

    public WeaknessSuggestionDto(){

    }

    public WeaknessSuggestionDto(WeaknessSuggestionDto weaknessSuggestion){
        this.id = weaknessSuggestion.getId();
        this.state = weaknessSuggestion.getState();
        this.suggestion = weaknessSuggestion.getSuggestion();
        this.batchOperations = weaknessSuggestion.getBatchOperations();
        this.params = weaknessSuggestion.getParams();
    }
}
