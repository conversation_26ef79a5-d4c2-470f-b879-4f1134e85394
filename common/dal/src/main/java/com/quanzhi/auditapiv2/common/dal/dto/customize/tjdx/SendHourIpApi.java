package com.quanzhi.auditapiv2.common.dal.dto.customize.tjdx;

import lombok.Data;

import java.util.List;

@Data
public class SendHourIpApi {
    private Integer orgCode;
    private String batchId;
    private Long sendTimestamp;
    private String commitDeviceIp;
    private String commitDeviceNo;
    private List<HourIpApiData> dataList;
    @Data
    public static class HourIpApiData{
        private Integer syncType;
        private String areaApiId;
        private String statisticalTime;
        private Double reqDataCnt;
        private Double respDataCnt;
        private Integer visits;
        private String sourceIp;
    }

}
