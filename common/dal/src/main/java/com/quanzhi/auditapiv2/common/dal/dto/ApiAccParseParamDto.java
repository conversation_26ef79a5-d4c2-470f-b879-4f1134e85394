package com.quanzhi.auditapiv2.common.dal.dto;

import lombok.Data;

import java.util.List;

@Data
public class ApiAccParseParamDto {
    List<ApiAccParseCriteriaDto> apiAccParseCriteriaDtos; // 其他api（不包含账号信息）配置登录凭证（包含SSO的登录凭证）、简单账号提取
    List<ApiAccParseCriteriaDto> apiAccParseLoginCriteriaDtos; // 登录api（有账号信息，包含SSO登录）：relateOrder=3x
    ApiAccParseCriteriaDto apiAccParseRelateCriteriaDto; // sso登录的跳转接口（不包含SSO登录，仅仅是跳转）
    List<String> removedApiUriList; // 删除的接口
    int parse;  // 1-追加，其他-覆盖
    ApiAccParseCriteriaDto.ParseType parseType;
}
