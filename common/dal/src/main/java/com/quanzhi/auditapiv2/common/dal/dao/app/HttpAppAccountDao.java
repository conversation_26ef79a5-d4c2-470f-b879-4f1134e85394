package com.quanzhi.auditapiv2.common.dal.dao.app;

import com.quanzhi.auditapiv2.common.util.dto.ListOutputDto;
import com.quanzhi.metabase.core.model.http.app.HttpAppAccount;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.util.List;

public interface HttpAppAccountDao {
    HttpAppAccount findByUri(String uri);

    List<HttpAppAccount> findByUriScope(String uri);

    List<HttpAppAccount> lstAllGlobal();

    Page<HttpAppAccount> pageGroupScope(String host, Pageable pageable);
    HttpAppAccount getOne(String id);

    HttpAppAccount save(HttpAppAccount appAccount);

    void delete(HttpAppAccount appAccount);

    long countGlobal();

    long countByUriScope(String uri);

    long countByUriScopeGroup(String uri);
}
