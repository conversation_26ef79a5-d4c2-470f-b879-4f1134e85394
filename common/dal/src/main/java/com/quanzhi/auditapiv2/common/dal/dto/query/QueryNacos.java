package com.quanzhi.auditapiv2.common.dal.dto.query;

import com.quanzhi.metabase.core.model.query.Sort;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * @Author: Linlm
 * @Description:
 * @Date: Created in 2020/7/3 下午6:31
 */
@Data
public class QueryNacos {
    private List<NacosFilterDto> criteria = new ArrayList<>();

    private Integer skip;

    private Integer limit;

    private Sort[] sort;

    private List<String> fields;

    public QueryNacos where(String property, Predicate predicate, Object value) {
        criteria.add(new NacosFilterDto(property, predicate, value));
        return this;
    }

    public QueryNacos skip(Integer skip) {
        this.skip = skip;
        return this;
    }

    public QueryNacos limit(Integer limit) {
        this.limit = limit;
        return this;
    }

    public QueryNacos sort(Sort... sort) {
        this.sort = sort;
        return this;
    }

    public QueryNacos fields(List<String> fields) {
        this.fields = fields;
        return this;
    }
}
