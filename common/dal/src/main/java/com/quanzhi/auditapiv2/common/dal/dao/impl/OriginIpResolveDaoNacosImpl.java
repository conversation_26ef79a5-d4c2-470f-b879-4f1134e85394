package com.quanzhi.auditapiv2.common.dal.dao.impl;

import com.quanzhi.auditapiv2.common.dal.dao.IOriginIpResolveNacosDao;
import com.quanzhi.auditapiv2.common.dal.dao.base.impl.NacosBaseDaoImpl;
import com.quanzhi.auditapiv2.common.dal.entity.OriginIpResolve;
import org.springframework.stereotype.Repository;

/**
 *
 * 《来源IP配置持久层Nacos接口实现》
 *
 *
 * @Project:
 * @Module ID:
 * @Comments:
 * @JDK version used:      <JDK1.8> 
 * <AUTHOR> [<EMAIL>]
 * @since 2020-04-08-上午9:56:10
 */
@Repository
public class OriginIpResolveDaoNacosImpl extends NacosBaseDaoImpl<OriginIpResolve> implements IOriginIpResolveNacosDao {
}
