package com.quanzhi.auditapiv2.common.dal.dto.common;

import com.quanzhi.auditapiv2.common.util.utils.DataUtil;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Data;

/**
 *
 * 《导入ExcelDTO》
 *
 *
 * @Project:
 * @Module ID:
 * @Comments:
 * @JDK version used:      <JDK1.8>
 * <AUTHOR> [<EMAIL>]
 * @since 2021-09-11-上午9:56:10
 */
@Data
@ApiModel
@Builder
public class ImportExcelDto {

    /**
     * 文件名称
     */
    @ApiModelProperty(value = "文件名称", name = "fileName")
    private String fileName;

    /**
     * 导入状态（1-进行中，2-成功，3-失败）
     * @see StatusEnum
     */
    @ApiModelProperty(value = "导入状态（1-进行中，2-成功，3-失败）", name = "status")
    private Integer status;

    /**
     * 导入状态名称
     */
    @ApiModelProperty(value = "导入状态名称", name = "statusName")
    private String statusName;

    /**
     * 导入进度
     */
    @ApiModelProperty(value = "导入进度", name = "progress")
    private Integer progress;

    /**
     * 导入成功数量
     */
    @ApiModelProperty(value = "导入成功数量", name = "successCount")
    private Long successCount;

    /**
     * 导入失败数量
     */
    @ApiModelProperty(value = "导入失败数量", name = "failCount")
    private Long failCount;

    public enum StatusEnum {

        /**
         * 进行中
         */
        WAITING(1, "进行中"),

        /**
         * 成功
         */
        SUCCESS(2, "成功"),

        /**
         * 失败
         */
        FAIL(3, "失败");

        Integer status;

        String name;

        StatusEnum(Integer status, String name){

            this.status = status;
            this.name = name;
        }

        public Integer getStatus(){
            return status;
        }

        public String getName(){
            return name;
        }

        public static StatusEnum getStatusEnum(Integer status) {
            
            if(DataUtil.isNotEmpty(status)) {

                for (StatusEnum statusEnum : StatusEnum.values()) {
                    if(statusEnum.getStatus().intValue() == status.intValue()) {
                        return statusEnum;
                    }
                }
            }
            
            return null;
        }
    }
}