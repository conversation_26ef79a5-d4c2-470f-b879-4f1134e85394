package com.quanzhi.auditapiv2.common.dal.mongoCollectionIndex.document;

import org.springframework.data.mongodb.core.index.CompoundIndex;
import org.springframework.data.mongodb.core.index.CompoundIndexes;
import org.springframework.data.mongodb.core.index.Indexed;
import org.springframework.data.mongodb.core.mapping.Document;

import java.util.Date;

@Document(collection = "ipMonthInfo")
@CompoundIndexes({
        @CompoundIndex(name = "ip_month", def = "{ip: 1, month: 1}",background = true),
})
public class IPMonthDocument {
    @Indexed(background = true)
    private Long updateTime;

    @Indexed(expireAfter = "360d")
    private Date createDate;
}
