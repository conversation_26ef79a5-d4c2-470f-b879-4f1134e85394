package com.quanzhi.auditapiv2.common.dal.dto;

import com.quanzhi.metabase.core.model.ResourceEntity;
import com.quanzhi.metabase.core.model.http.HttpAppResource;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

/**
 *
 * 《应用dto》
 *
 *
 * @Project:
 * @Module ID:
 * @Comments:
 * @JDK version used:      <JDK1.8>
 * <AUTHOR> [<EMAIL>]
 * @since 2020-04-08-上午9:56:10
 */
@Data
public class HttpAppDto {

    @ApiModelProperty(value = "id", name = "id")
    private String id;

    /**
     * 应用uri
     */
    @ApiModelProperty(value = "uri", name = "uri")
    private String uri;

    /**
     * 应用名称
     */
    @ApiModelProperty(value = "应用名称", name = "name")
    private String name;

    /**
     * 应用图标
     */
    @ApiModelProperty(value = "应用图标", name = "icon")
    private String icon;

    /**
     * 应用分类列表
     */
    @ApiModelProperty(value = "应用分类", name = "appClassifications")
    private List<String> appClassifications = new ArrayList<String>();

    /**
     * 应用分类内容列表
     */
    @ApiModelProperty(value = "应用分类内容列表", name = "appClassificationValue")
    private List<String> appClassificationValue = new ArrayList<String>();

    /**
     * 是否忽略
     */
    @ApiModelProperty(value = "是否忽略", name = "delFlag")
    private Boolean delFlag;

    /**
     * 应用标识列表
     */
    @ApiModelProperty(value = "应用标识列表", name = "sign")
    private List<String> featureLabels = new ArrayList<String>();

    /**
     * 应用标识内容列表
     */
    @ApiModelProperty(value = "应用标识内容列表", name = "sign")
    private List<String> featureLabelsValue = new ArrayList<String>();

    /**
     * host域
     */
    @ApiModelProperty(value = "host域", name = "host")
    private String host;

    /**
     * host域列表
     */
    @ApiModelProperty(value = "host域列表", name = "hosts")
    private List<String> hosts = new ArrayList<String>();

    /**
     * 等级
     */
    @ApiModelProperty(value = "等级", name = "level")
    private String level;

    /**
     * 访问域id列表
     */
    @ApiModelProperty(value = "访问域id列表", name = "visitDomains")
    private List<String> visitDomains = new ArrayList<String>();

    /**
     * 访问域内容列表
     */
    @ApiModelProperty(value = "访问域内容列表", name = "visitDomainsValue")
    private List<String> visitDomainsValue = new ArrayList<String>();

    /**
     * 部署域id列表
     */
    @ApiModelProperty(value = "部署域id列表", name = "deployDomains")
    private List<String> deployDomains = new ArrayList<String>();

    /**
     * 部署域内容列表
     */
    @ApiModelProperty(value = "部署域内容列表", name = "deployDomainsValue")
    private List<String> deployDomainsValue = new ArrayList<String>();

    /**
     * 返回数据标签id列表
     */
    @ApiModelProperty(value = "返回数据标签id列表", name = "rspDataLabels")
    private List<String> rspDataLabels = new ArrayList<String>();

    /**
     * 返回数据标签内容列表
     */
    @ApiModelProperty(value = "返回数据标签内容列表", name = "rspDataLabelsValue")
    private List<String> rspDataLabelsValue = new ArrayList<String>();

    /**
     * 请求数据标签id列表
     */
    @ApiModelProperty(value = "请求数据标签id列表", name = "reqDataLabels")
    private List<String> reqDataLabels = new ArrayList<String>();

    /**
     * 请求数据标签内容列表
     */
    @ApiModelProperty(value = "请求数据标签内容列表", name = "reqDataLabelsValue")
    private List<String> reqDataLabelsValue = new ArrayList<String>();

    /**
     * 首次发现时间
     */
    @ApiModelProperty(value = "首次发现时间", name = "discoverTime")
    private Long discoverTime;

    /**
     * 活跃时间
     */
    @ApiModelProperty(value = "活跃时间", name = "activeTime")
    private Long activeTime;


    /**
     * 复活时间
     */
    @ApiModelProperty(value = "复活时间", name = "reviveTime")
    private Long reviveTime;


    /**
     * 是否关注
     */
    @ApiModelProperty(value = "是否关注", name = "followed")
    private Boolean followed;

    /**
     * 接口弱点数量
     */
    @ApiModelProperty(value = "接口弱点数量", name = "apiWeaknessCount")
    private Long apiWeaknessCount;

    /**
     * 所属部门
     */
    private List<HttpAppResource.Department> departments;

    /**
     * 弱点接口数量
     */
    @Deprecated
    private Long apiCount;

    /**
     * 统计信息
     */
    @ApiModelProperty(value = "统计信息", name = "appStat")
    private HttpAppResource.AppStat appStat = new HttpAppResource.AppStat();

    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间", name = "createTime")
    private Long createTime;

    /**
     * 更新时间
     */
    @ApiModelProperty(value = "更新时间", name = "updateTime")
    private Long updateTime;

    /**
     * 是否敏感应用
     */
    @ApiModelProperty(value = "是否敏感应用", name = "sensitiveAppFlag")
    private Boolean sensitiveAppFlag;

    /**
     * 是否开启全流量
     */
    @ApiModelProperty(value = "是否开启全流量", name = "fullFlowEnable")
    private Boolean fullFlowEnable;

    @ApiModelProperty(value = "工单状态", name = "orderFlag")
    private Integer orderFlag;

    @ApiModelProperty(value = "工单状态名称", name = "orderFlagName")
    private String orderFlagName;

    @ApiModelProperty(value = "部署IP集", name = "deployIps")
    private Set<String> deployIps=new HashSet<>();

    @ApiModelProperty(value = "终端集", name = "terminals")
    private Set<String> terminals=new HashSet<>();

    @ApiModelProperty(value = "流量来源", name = "flowSources")
    private List<String> flowSources=new ArrayList<>();

    @ApiModelProperty(value = "生命状态", name = "appLifeFlag")
    private List<Short> appLifeFlag;

    /**
     * 应用状态名称
     */
    private List<String> appLifeFlagName;
    /**
     * 多节点信息
     */
    private List<ResourceEntity.Node> nodes;

    private List<String> provinces;

    private Short restfulFlag;
    private Short whenFlag;


    @Mapper
    public interface HttpAppDtoMapper {

        HttpAppDto.HttpAppDtoMapper INSTANCE = Mappers.getMapper(HttpAppDto.HttpAppDtoMapper.class);

        HttpAppDto convert(HttpAppResource httpAppResource);

    }
}
