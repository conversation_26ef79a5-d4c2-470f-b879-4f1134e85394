package com.quanzhi.auditapiv2.common.dal.entity;

import com.quanzhi.auditapiv2.common.dal.entity.account.AbstractTarget;
import com.quanzhi.metabase.core.model.http.HttpResourceConstant;
import lombok.*;
import lombok.experimental.SuperBuilder;
import org.springframework.data.mongodb.core.mapping.Document;

import java.util.Set;

/**
 * <AUTHOR>
 * @date 2020/10/21 下午5:38
 */
@EqualsAndHashCode(callSuper = true)
@Data
@SuperBuilder
@Document(HttpResourceConstant.IP_INFO)
@AllArgsConstructor
@NoArgsConstructor
public class IpInfo extends AbstractTarget {
    private String ip;
    private Set<String> relatedAccountList;
    private String city;
    private String country;
    private String province;
    private Long relatedAccountDistinctCnt;
    private Boolean blockFlag;
}
