package com.quanzhi.auditapiv2.common.dal.mongoCollectionIndex.document;

import com.quanzhi.auditapiv2.common.dal.mongoCollectionIndex.MongoCollectionConstant;
import org.springframework.data.mongodb.core.index.CompoundIndex;
import org.springframework.data.mongodb.core.index.CompoundIndexes;
import org.springframework.data.mongodb.core.index.Indexed;
import org.springframework.data.mongodb.core.mapping.Document;

import java.util.Set;

/**
 * <AUTHOR>
 * @date 2022/12/18 上午11:27
 */
@Document(collection = MongoCollectionConstant.HTTP_API)
@CompoundIndexes({
        @CompoundIndex(name = "delFlagAndUri", def = "{delFlag:1,uri:1}", background = true),
})
public class HttpApiDocument {

}
