package com.quanzhi.auditapiv2.common.dal.dao.sso;

import cn.hutool.core.bean.BeanUtil;
import com.quanzhi.auditapiv2.common.dal.dto.sso.LdapConfigDto;
import com.quanzhi.auditapiv2.common.dal.dto.sso.SsoConfigDto;
import com.quanzhi.auditapiv2.common.util.utils.DataUtil;
import com.quanzhi.login.entity.CasConfig;
import com.quanzhi.login.entity.DingConfig;
import com.quanzhi.login.entity.LdapConfig;
import com.quanzhi.login.entity.Oauth2Config;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Repository;

/**
 * @Author: K, 小康
 * @Date: 2024/08/01/下午2:42
 * @Description:
 */
@Repository
public class SsoDaoImpl implements SsoDao {
    private final MongoTemplate mongoTemplate;

    public SsoDaoImpl(MongoTemplate mongoTemplate) {
        this.mongoTemplate = mongoTemplate;
    }

    @Override
    public void saveLdapConfig(LdapConfig ldapConfig) {
        SsoConfigDto<LdapConfig> ssoConfigDto = new SsoConfigDto<>();
        ssoConfigDto.setConfig(ldapConfig);
        ssoConfigDto.setConfigType("ldap");
        mongoTemplate.remove(new Query(Criteria.where("configType").is("ldap")),"ssoConfig");
        mongoTemplate.save(ssoConfigDto,"ssoConfig");
    }

    @Override
    public LdapConfig getLdapConfig() {
        SsoConfigDto<LdapConfig> ssoConfigDto = mongoTemplate.findOne(new Query(Criteria.where("configType").is("ldap")), SsoConfigDto.class, "ssoConfig");
        if (DataUtil.isNotEmpty(ssoConfigDto)){
            return ssoConfigDto.getConfig();
        }else {
            return new LdapConfig();
        }
    }

    @Override
    public void saveOauth2Config(Oauth2Config oauth2Config) {
        SsoConfigDto<Oauth2Config> ssoConfigDto = new SsoConfigDto<>();
        ssoConfigDto.setConfig(oauth2Config);
        ssoConfigDto.setConfigType("oauth2");
        mongoTemplate.remove(new Query(Criteria.where("configType").is("oauth2")),"ssoConfig");
        mongoTemplate.save(ssoConfigDto,"ssoConfig");
    }

    @Override
    public Oauth2Config getOauth2Config() {
        SsoConfigDto<Oauth2Config> ssoConfigDto = mongoTemplate.findOne(new Query(Criteria.where("configType").is("oauth2")), SsoConfigDto.class, "ssoConfig");
        if (DataUtil.isNotEmpty(ssoConfigDto)){
            return ssoConfigDto.getConfig();
        }else {
            return new Oauth2Config();
        }
    }

    @Override
    public void saveCasConfig(CasConfig casConfig) {
        SsoConfigDto<CasConfig> ssoConfigDto = new SsoConfigDto<>();
        ssoConfigDto.setConfig(casConfig);
        ssoConfigDto.setConfigType("cas");
        mongoTemplate.remove(new Query(Criteria.where("configType").is("cas")),"ssoConfig");
        mongoTemplate.save(ssoConfigDto,"ssoConfig");
    }

    @Override
    public CasConfig getCasConfig() {
        SsoConfigDto<CasConfig> ssoConfigDto = mongoTemplate.findOne(new Query(Criteria.where("configType").is("cas")), SsoConfigDto.class, "ssoConfig");
        if (DataUtil.isNotEmpty(ssoConfigDto)){
            return ssoConfigDto.getConfig();
        }else {
            return new CasConfig();
        }
    }

    @Override
    public void saveDingConfig(DingConfig dingConfig) {
        SsoConfigDto<DingConfig> ssoConfigDto = new SsoConfigDto<>();
        ssoConfigDto.setConfig(dingConfig);
        ssoConfigDto.setConfigType("ding");
        mongoTemplate.remove(new Query(Criteria.where("configType").is("ding")),"ssoConfig");
        mongoTemplate.save(ssoConfigDto,"ssoConfig");
    }

    @Override
    public DingConfig getDingConfig() {
        SsoConfigDto<DingConfig> ssoConfigDto = mongoTemplate.findOne(new Query(Criteria.where("configType").is("ding")), SsoConfigDto.class, "ssoConfig");
        if (DataUtil.isNotEmpty(ssoConfigDto)){
            return ssoConfigDto.getConfig();
        }else {
            return new DingConfig();
        }
    }


}
