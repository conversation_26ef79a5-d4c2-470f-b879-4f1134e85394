package com.quanzhi.auditapiv2.common.dal.entity;

import com.quanzhi.metabase.core.model.event.EventType;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/8/18 下午2:08
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ResourceChangeEventModel {

    private String id;

    /**
     * 资源类型
     */
    private String resourceType;

    /**
     * 名称
     */
    private String name;

    /**
     * 接口id
     */
    private String apiId;

    /**
     * 应用id
     */
    private String appId;

    /**
     * 接口
     */
    private String uri;

    /**
     * 应用
     */
    private String appUri;

    /**
     * 事件类型
     */
    private EventType eventType;

    /**
     * 事件时间
     */
    private Long timestamp;

    /**
     * 创建时间
     */
    private Long createTime;

    /**
     * 接口类型
     */
    private List<String> classifications;

    /**
     * 订阅名称
     */
    private String subscribeName;

    /**
     * 订阅id
     */
    private String subscribeRuleId;

    /**
     * 样例id
     */
    private String sampleId;

    /**
     * 是否推荐接口
     */
    private boolean recommend;

    private List<String> lowClassifications;

    private List<String> reqDataLabels;

    private List<String> rspDataLabels;

    private List<String> lowReqDataLabels;

    private List<String> lowRspDataLabels;
    /**
     * 部署域id列表
     */
    private List<String> deployDomains;
    /**
     * 访问域id
     */
    private List<String> visitDomains;

    private List<String> featureLabels;

    private List<String> reqContentTypes;

    private List<String> rspContentTypes;

    private List<String> newClassifications;

    private List<String> newLowClassifications;

    private List<String> newReqDataLabels;

    private List<String> newRspDataLabels;

    private List<String> newLowReqDataLabels;

    private List<String> newLowRspDataLabels;
    /**
     * 部署域id列表
     */
    private List<String> newDeployDomains;
    /**
     * 访问域id
     */
    private List<String> newVisitDomains;

    private List<String> newFeatureLabels;

    private List<String> newReqContentTypes;

    private List<String> newRspContentTypes;

    /**
     * 订阅特征name集合
     */
    private List<String> subscribeFeatureDetail;

}

