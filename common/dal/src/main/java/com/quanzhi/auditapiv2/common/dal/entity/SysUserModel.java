package com.quanzhi.auditapiv2.common.dal.entity;

import com.quanzhi.auditapiv2.common.dal.dataobject.SysUser;
import lombok.Data;

import java.util.List;

/**
 * Created by she<PERSON><PERSON> on 2017/11/9.
 */
@Data
public class SysUserModel {

    private String id;

    private String username;

    private String password;

    private String repassword;

    private String groupId;

    private String groupName;

    private Long pwdModifiedTime;

    private Long gmtModified;

    /**
     * 是否为内置用户
     * 1：内置用户
     * 0：自定义用户
     */
    private Integer type;

    /**
     * 状态(0：禁用   1：正常)
     */
    private Integer status;

    private List<String> visitIps;

    private Long updateTime;

    private String updateTimeFormat;

    private Long lockTime;

    private String lockTimeFormat;

    private Integer errorCount;

    /**
     * 邮箱地址
     */
    private String mailAddr;

    /**
     * 是否初始密码
     * null/false - 初始密码，未修改，true - 已修改初始密码
     */
    private Boolean updatedPassword;

    public static SysUserModel createSSOUserModel(String username){
        SysUserModel sysUser = new SysUserModel();
        sysUser.setId(username);
        sysUser.setUsername(username);
        sysUser.setType(SysUser.TypeEnum.WEBADMIN.value());
        sysUser.setStatus(SysUser.StatusEnum.RESTORE.value());
        sysUser.setUpdatedPassword(false);
        sysUser.setPassword("****** secure ****** ");
        return sysUser;
    }

}
