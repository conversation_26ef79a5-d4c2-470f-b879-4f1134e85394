package com.quanzhi.auditapiv2.common.dal.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ScanRule {

    private String id;

    private String code;

    /**
     * 名称
     */
    private String name;

    /**
     * 插件代码
     */
    private String pluginCode;

    /**
     * 模块
     */
    private String module;

    /**
     * 所属类别
     * @see ScanRuleFamily
     */
    private String family;

    /**
     * 描述
     */
    private String description;

    /**
     * 简短描述
     */
    private String shortDescription;

    /**
     * 是否启用
     */
    private Boolean enable;

    /**
     * 插件
     */
//    private Plugin plugin;

    /**
     * 接口白名单
     */
    private List<String> apiWhiteList;

    /**
     * 应用白名单
     */
    private List<String> hostWhiteList;

    /**
     * 参数配置
     */
    private String params;

    /**
     * 创建时间
     */
    private Long createTime;

    /**
     * 创建人
     */
    private String creator;

    /**
     * 规则接口数量
     */
    private Long count;

    public enum ScanRuleFamily {

        /**
         * 口令类
         */
        PASSWORD("password"),

        /**
         * 认证类
         */
        IDENTITY("identity"),

        /**
         * 权限类
         */
        AUTHORIZATION("authorization"),

        /**
         * 数据限制类
         */
        DATAFLOW("dataflow");

        private String val;

        ScanRuleFamily(String val) {this.val = val;}

        public String val() {return this.val;}
    }

    public enum Degree {

        HIGH("3"),

        MIDDLE("2"),

        LOW("1");

        private String val;

        Degree(String val) {this.val = val;}

        public String val() {return this.val;}

    }

    /**
     * 风险等级
     */
    private String riskLevel;

    /**
     * 脆弱被利用可能性
     */
    private String utilized;

    /**
     * 被利用后的影响程度
     */
    private String utilizedInfluence;

    /**
     * 脆弱定义
     */
    private String definition;

    /**
     * 被利用方式
     */
    private String utilizedWay;

    /**
     * 风险解决方案
     */
    private String solution;

    /**
     * 被利用后的影响
     */
    private String utilizedInfluenceDesc;


}
