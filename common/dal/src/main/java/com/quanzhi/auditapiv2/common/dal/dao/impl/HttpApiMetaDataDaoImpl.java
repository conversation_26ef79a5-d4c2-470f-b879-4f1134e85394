package com.quanzhi.auditapiv2.common.dal.dao.impl;

import com.quanzhi.auditapiv2.common.dal.dao.IHttpApiMetaDataDao;
import com.quanzhi.auditapiv2.common.dal.dao.base.impl.MetabaseDaoImpl;
import com.quanzhi.metabase.core.model.http.HttpApiMetaData;
import com.quanzhi.metabase.core.model.query.MetabaseQuery;
import com.quanzhi.metabase.core.model.query.Predicate;
import com.quanzhi.metabase.core.model.query.Sort;
import com.quanzhi.metabase.core.model.query.SortOrder;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 *
 * 《接口元数据持久层接口》
 *
 *
 * @Project:
 * @Module ID:
 * @Comments:
 * @JDK version used:      <JDK1.8> 
 * <AUTHOR> [<EMAIL>]
 * @since 2020-04-08-上午9:56:10
 */
@Repository
public class HttpApiMetaDataDaoImpl extends MetabaseDaoImpl<HttpApiMetaData> implements IHttpApiMetaDataDao {

    /**
     * 应用id查询元数据列表(分页)
     * @Comments:  <对此方法的描述，可以引用系统设计中的描述>
     * <AUTHOR> [<EMAIL>]
     * @since 2020-04-08 9:56
     * @param
     * @return
     */
    @Override
    public List<HttpApiMetaData> selectHttpApiMetaDataListByAppUri(String appUri, Integer page, Integer limit) throws Exception {

        MetabaseQuery metabaseQuery = new MetabaseQuery();

        //查询条件
        metabaseQuery.where("appUri", Predicate.IS, appUri);

        //分页
        metabaseQuery.skip((page - 1) * limit);
        metabaseQuery.limit(limit);
    	//排序
        metabaseQuery.sort(Sort.by("startTimestamp", SortOrder.DESC));

        return metabaseClientTemplate.find(metabaseQuery, HttpApiMetaData.class);
    }
    
    /**
     * 接口id查询元数据列表(分页)
     * @Comments:  <对此方法的描述，可以引用系统设计中的描述>
     * <AUTHOR> [<EMAIL>]
     * @since 2020-04-08 9:56
     * @param
     * @return
     */
    @Override
    public List<HttpApiMetaData> selectHttpApiMetaDataListByApiUri(String apiUri, Integer page, Integer limit) throws Exception {

        MetabaseQuery metabaseQuery = new MetabaseQuery();

        //查询条件
        metabaseQuery.where("uri", Predicate.IS, apiUri);

        //分页
        metabaseQuery.skip((page - 1) * limit);
        metabaseQuery.limit(limit);
        //排序
        metabaseQuery.sort(Sort.by("startTimestamp", SortOrder.DESC));

        return metabaseClientTemplate.find(metabaseQuery, HttpApiMetaData.class);
    }
}
