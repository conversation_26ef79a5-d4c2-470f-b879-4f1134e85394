package com.quanzhi.auditapiv2.common.dal.mongoCollectionIndex.document;

import com.quanzhi.auditapiv2.common.dal.mongoCollectionIndex.MongoCollectionConstant;
import com.quanzhi.metabase.core.model.ResourceEntity;
import com.quanzhi.metabase.core.model.dto.RiskLevelMatchDto;
import org.springframework.data.mongodb.core.index.CompoundIndex;
import org.springframework.data.mongodb.core.index.CompoundIndexes;
import org.springframework.data.mongodb.core.index.Indexed;
import org.springframework.data.mongodb.core.mapping.Document;

import java.util.Date;
import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * @date 2022/12/18 上午11:27
 */
@Document(collection = MongoCollectionConstant.DATA_INFO)
@CompoundIndexes({
        @CompoundIndex(name = "markState_totalCount_id", def = "{markState:1,totalCount:1,_id:1}", background = true),
        @CompoundIndex(name = "markState_reqCount_id", def = "{markState:1,reqCount:1,_id:1}", background = true),
        @CompoundIndex(name = "markState_rspCount_id", def = "{markState:1,rspCount:1,_id:1}", background = true),
        @CompoundIndex(name = "markState_riskNum_id", def = "{markState:1,riskNum:1,_id:1}", background = true),
        @CompoundIndex(name = "markState_appNum_id", def = "{markState:1,appNum:1,_id:1}", background = true),
        @CompoundIndex(name = "markState_accountNum_id", def = "{markState:1,accountNum:1,_id:1}", background = true),
        @CompoundIndex(name = "markState_ipNum_id", def = "{markState:1,ipNum:1,_id:1}", background = true),
})
public class DataDocument {

    @Indexed(background = true)
    private String dataId;

    @Indexed(background = true)
    private String dataName;

    @Indexed(background = true)
    private Date createDate;

    @Indexed(background = true)
    private Boolean markState = false;

    @Indexed(background = true)
    private String dataType;

    @Indexed(background = true)
    private List<ResourceEntity.Node> nodes;

    @Indexed(background = true)
    private String dataTypeName;

    @Indexed(background = true)
    private Integer dataLevel;

    @Indexed(background = true)
    private String dataLevelName;

    @Indexed(background = true)
    private Integer dataRiskLevel;

    @Indexed(background = true)
    private String dataRiskLevelName;

    @Indexed(background = true)
    private RiskLevelMatchDto.Risk riskInfo;

    @Indexed(background = true)
    private Long apiNum;

    @Indexed(background = true)
    private Long appNum;

    @Indexed(background = true)
    private Long ipNum;

    @Indexed(background = true)
    private Long accountNum;

    @Indexed(background = true)
    private Set<String> relatedRisks;

    @Indexed(background = true)
    private Long riskNum;

    @Indexed(background = true)
    private Long reqCount;

    @Indexed(background = true)
    private Long rspCount;

    @Indexed(background = true)
    private Long totalCount;

    @Indexed(background = true)
    private Long firstTime;

    @Indexed(background = true)
    private Long lastTime;

}
