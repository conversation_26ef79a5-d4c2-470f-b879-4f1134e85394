package com.quanzhi.auditapiv2.common.dal.dao.impl;

import com.quanzhi.audit_core.common.model.FtpFeatureLabel;
import com.quanzhi.auditapiv2.common.dal.dao.IFtpFeatureLabelDao;
import com.quanzhi.auditapiv2.common.dal.dao.base.impl.NacosBaseDaoImpl;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public class FtpFeatureLabelDaoImpl extends NacosBaseDaoImpl<FtpFeatureLabel> implements IFtpFeatureLabelDao {

    @Override
    public Boolean checkNameExist(String name,String excludeId) {

        List<FtpFeatureLabel> list = getAll();

        for(int i = 0; i < list.size();i++) {

            FtpFeatureLabel ftpFeatureLabel = list.get(i);

            if(!ftpFeatureLabel.getId().equals(excludeId) && ftpFeatureLabel.getName().equals(name)) {
                return true;
            }
        }

        return false;
    }


}
