package com.quanzhi.auditapiv2.common.dal.dao.impl;

import com.quanzhi.auditapiv2.common.dal.dao.IScanTaskResultDao;
import com.quanzhi.auditapiv2.common.dal.dao.base.impl.BaseDaoImpl;
import com.quanzhi.auditapiv2.common.dal.entity.ScanTaskResult;
import com.quanzhi.auditapiv2.common.util.utils.DataUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.stereotype.Repository;

import java.util.ArrayList;
import java.util.List;

/**
 * 《报告任务结果持久层接口实现》
 *
 * <AUTHOR> [<EMAIL>]
 * @Project:
 * @Module ID:
 * @Comments:
 * @JDK version used:      <JDK1.8>
 * @since 2019-07-23-上午9:09:10
 */
@Repository
public class ScanTaskResultDaoImpl extends BaseDaoImpl<ScanTaskResult> implements IScanTaskResultDao {

    @Autowired
    MongoTemplate mongoTemplate;

    String collectionName = "scanTaskResult";

    @Override
    public List<ScanTaskResult> selectScanTaskResultList(String taskCode, Integer state, Integer page, Integer limit) {

        //查询条件
        Criteria criteria = Criteria.where("taskCode").is(taskCode);
        if (DataUtil.isNotEmpty(state)) {
            criteria = criteria.and("state").is(state);
        }
        //分页
        Pageable pageable = PageRequest.of(page - 1, limit);
        //排序
        Sort sort = Sort.by(Sort.Direction.DESC, "_id");
        return mongoTemplate.find(new Query(criteria).with(pageable).with(sort), ScanTaskResult.class, collectionName);
    }

    @Override
    public List<ScanTaskResult> selectScanTaskResultList(String taskCode, String name, Integer state, Integer page, Integer limit) {
        //查询条件
        Criteria criteria = new Criteria();
        if (DataUtil.isNotEmpty(taskCode)) {
            if (taskCode.contains(",")) {
                String[] items = taskCode.split(",");
                criteria.and("taskCode").in(items);
            } else {
                criteria = criteria.and("taskCode").is(taskCode);
            }
        }
        if (DataUtil.isNotEmpty(state)) {
            criteria = criteria.and("state").is(state);
        }
        if (DataUtil.isNotEmpty(name)) {
            name=DataUtil.regexStrEscape(name);
            criteria = criteria.and("name").regex(name);
        }
        //分页
        Pageable pageable = PageRequest.of(page - 1, limit);
        //排序
        Sort sort = Sort.by(Sort.Direction.DESC, "_id");
        return mongoTemplate.find(new Query(criteria).with(pageable).with(sort), ScanTaskResult.class, collectionName);
    }

    @Override
    public Long totalCount(String taskCode, String name, Integer state) {

        Criteria criteria = new Criteria();
        if (DataUtil.isNotEmpty(taskCode)) {
            if (taskCode.contains(",")) {
                String[] items = taskCode.split(",");
                criteria.and("taskCode").in(items);
            } else {
                criteria = criteria.and("taskCode").is(taskCode);
            }
        }
        if (DataUtil.isNotEmpty(state)) {
            criteria = criteria.and("state").is(state);
        }
        if (DataUtil.isNotEmpty(name)) {
            name=DataUtil.regexStrEscape(name);
            criteria = criteria.and("name").regex(name);
        }
        Long total = mongoTemplate.count(Query.query(criteria), collectionName);

        return total;
    }

    @Override
    public Long totalCount(String taskCode, Integer state) {

        Criteria criteria = Criteria.where("taskCode").is(taskCode);
        if (DataUtil.isNotEmpty(state)) {
            criteria = criteria.and("state").is(state);
        }

        Long total = mongoTemplate.count(Query.query(criteria), collectionName);

        return total;
    }


    @Override
    public ScanTaskResult selectScanTaskResultById(String id) {

        //查询条件
        Criteria criteria = Criteria.where("_id").is(id);

        return mongoTemplate.findOne(new Query(criteria), ScanTaskResult.class, collectionName);
    }

    @Override
    public ScanTaskResult insertScanTaskResult(ScanTaskResult result) {
        ScanTaskResult insert = mongoTemplate.insert(result, collectionName);
        return insert;
    }

    @Override
    public boolean exists(Query query) {
        return mongoTemplate.exists(query, collectionName);
    }

    @Override
    public void deleteByTaskId(String taskId) {
        Criteria criteria = Criteria.where("taskId").is(taskId);
        mongoTemplate.remove(new Query(criteria), collectionName);
    }

    @Override
    public List<ScanTaskResult> findAndRemoveByTaskId(String taskId) {
        Criteria criteria = Criteria.where("taskId").is(taskId);
        return mongoTemplate.findAllAndRemove(new Query(criteria), ScanTaskResult.class, collectionName);
    }

    @Override
    public List<ScanTaskResult> findNotStoppedByTaskId(String taskId) {
        Criteria criteria = Criteria.where("taskId").is(taskId).and("state").in(new ArrayList<Integer>() {{
            add(ScanTaskResult.JobStateEnum.RUNNING.getValue());
        }});
        Query query = new Query(criteria);
        return mongoTemplate.find(query, ScanTaskResult.class, collectionName);
    }

    @Override
    public List<ScanTaskResult> findNotStoppedByTaskCode(String taskCode) {
        Criteria criteria = Criteria.where("taskCode").is(taskCode).and("state").in(new ArrayList<Integer>() {{
            add(ScanTaskResult.JobStateEnum.RUNNING.getValue());
        }});
        Query query = new Query(criteria);
        return mongoTemplate.find(query, ScanTaskResult.class, collectionName);
    }

    @Override
    public ScanTaskResult getEarliestScanTaskResultId(String taskCode) {
        Criteria criteria = Criteria.where("taskCode").is(taskCode).and("state").is(ScanTaskResult.JobStateEnum.FINISH.getValue());
        Query query = new Query(criteria);
        Sort sort = Sort.by(Sort.Direction.DESC, "_id");
        return mongoTemplate.findOne(query.with(sort), ScanTaskResult.class, collectionName);
    }

    @Override
    public void updateResultByStatus(String resultId, int status) {
        Update update = Update.update("state", status);
        Criteria criteria = Criteria.where("_id").is(resultId);
        mongoTemplate.updateFirst(new Query(criteria), update, collectionName);
    }

    @Override
    public void updateResultByStatus(String resultId, Update update) {
        Criteria criteria = Criteria.where("_id").is(resultId);
        mongoTemplate.updateFirst(new Query(criteria), update, collectionName);
    }

    @Override
    public void update(Query query, Update update) {
        mongoTemplate.updateMulti(query,update,collectionName);
    }
}