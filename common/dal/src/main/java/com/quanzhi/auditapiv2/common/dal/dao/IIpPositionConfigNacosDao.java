package com.quanzhi.auditapiv2.common.dal.dao;

import com.quanzhi.audit_core.common.model.IpPositionConfig;
import com.quanzhi.auditapiv2.common.dal.dao.base.INacosBaseDao;
import org.springframework.stereotype.Repository;

/**
 * 
 * 《纯真库持久层Nacos接口》
 * 
 * 
 * @Project: 
 * @Module ID:
 * @Comments: 
 * @JDK version used:      <JDK1.8> 
 * <AUTHOR> [<EMAIL>]
 * @since 2020-04-08-上午9:56:10
 */
@Repository
public interface IIpPositionConfigNacosDao extends INacosBaseDao<IpPositionConfig> {
}
