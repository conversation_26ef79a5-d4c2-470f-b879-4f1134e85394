package com.quanzhi.auditapiv2.common.dal.dto.filter;

import com.quanzhi.metabase.core.model.filter.AssetFilterRule;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * create at 2023/3/29 11:25 上午
 * @description: 启用过滤规则
 **/
@Data
public class EnableFilterDto {

    /**
     * 过滤规则id
     */
    private List<String> ids;
    /**
     * 过滤方式
     */
    @Deprecated
    private FilterType filterType;

    /**
     * 名单类型
     */
    private AssetFilterRule.ConfigTypeEnum configType;


    /**
     * 历史数据任务id
     */
    private String taskId;

    /**
     * 对应的规则
     */
    private List<Object> rules;

    private String filterHost;


    public enum FilterType {
        //规则、插件、智能
        RULE, PLUGIN, SMART
    }
}