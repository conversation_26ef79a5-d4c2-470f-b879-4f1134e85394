package com.quanzhi.auditapiv2.common.dal.entity;

import lombok.Data;

/**
 * 系统版本信息
 */
@Data
public class VersionConfigModel {

    /**
     * 产品id
     */
    private String productId;

    /**
     * 产品名称
     */
    private String productName;

    /**
     * 产品序列
     */
    private String productSerial;

    /**
     * 上一版本版本号
     */
    private String oldVersion;

    /**
     * 上一版本升级说明
     */
    private String oldDesc;

    /**
     * 当前系统版本号
     */
    private String version;

    /**
     * 当前系统版本说明
     */
    private String desc;

    /**
     * 发布或更新系统时间
     */
    private long time;
}
