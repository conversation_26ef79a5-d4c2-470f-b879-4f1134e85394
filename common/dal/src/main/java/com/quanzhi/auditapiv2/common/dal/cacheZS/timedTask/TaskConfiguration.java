package com.quanzhi.auditapiv2.common.dal.cacheZS.timedTask;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.*;
import org.springframework.core.type.AnnotatedTypeMetadata;

@Configuration
public class TaskConfiguration {

    @Value("${qz.env.custom:}")
    private String custom;
    private String ZSYH="zsyh";
    //控制指定环境加载指定bean
    @Bean
    public LabelAppSegmentTask labelAppSegmentTask() {
        if (custom.equals(ZSYH)){
            return new LabelAppSegmentTask();
        }
        return null;
    }
}
