package com.quanzhi.auditapiv2.common.dal.entity;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.Instant;
import java.util.ArrayList;
import java.util.List;

/**
 * 网关上报的agent信息
 */
@Data
@NoArgsConstructor
public class AgentStatus {
    /**
     * 时间戳
     * 秒
     */
    private Long timestamp;
    /**
     * agent版本号
     */
    private String version;
    /**
     * agent抓包网卡IP
     */
    private String agentIp;
    /**
     * 网关IP
     */
    private String gatewayIp;
    /**
     * 监控进程状态
     * dead
     * active
     */
    private String monitorStats;
    /**
     * 守护进程状态
     * dead
     * active
     */
    private String daemonStats;
    /**
     * 抓包进程状态
     * dead
     * active
     */
    private String capStats;
    /**
     * 上传文件进程状态
     * dead
     * active
     */
    private String uploadStats;
    /**
     * CPU使用率
     */
    private Long cpuUsage;
    /**
     * 抓包目录大小
     * MB
     */
    private Long pcapCapcity;
    /**
     * 文件状态
     */
    private List<FileStats> fileStats;
    /**
     * 接收时间
     */
    private Long insertTime;

    @Data
    @NoArgsConstructor
    public static class FileStats {
        /**
         * 文件名
         */
        private String fileName;
        /**
         * 文件大小
         * B
         */
        private Long fileSize;
        /**
         * 文件状态
         * success
         * fail
         */
        private String fileStats;
        /**
         * 文件上传协议
         * FTP
         * FTPS
         */
        private String fileProtocol;
    }

    public AgentStatus(String data) {
        // JSON.parseObject(data).get
        JSONObject json = JSON.parseObject(data);
        if (json.containsKey("tm")) {
            this.timestamp = json.getLong("tm");
        }
        if (json.containsKey("version")) {
            this.version = json.getString("version");
        }
        if (json.containsKey("agent_ip")) {
            this.agentIp = json.getString("agent_ip");
        }
        if (json.containsKey("gw_ip")) {
            this.gatewayIp = json.getString("gw_ip");
        }
        if (json.containsKey("monitor_stats")) {
            this.monitorStats = json.getString("monitor_stats");
        }
        if (json.containsKey("daemon_stats")) {
            this.daemonStats = json.getString("daemon_stats");
        }
        if (json.containsKey("cap_stats")) {
            this.capStats = json.getString("cap_stats");
        }
        if (json.containsKey("upload_stats")) {
            this.uploadStats = json.getString("upload_stats");
        }
        if (json.containsKey("cpu_usage")) {
            this.cpuUsage = json.getLong("cpu_usage");
        }
        if (json.containsKey("pcap_capcity")) {
            this.pcapCapcity = json.getLong("pcap_capcity");
        }
        if (json.containsKey("file_stats")) {
            List<FileStats> list = new ArrayList<>();
            JSONArray jsonArray = json.getJSONArray("file_stats");
            for (int i = 0; i < jsonArray.size(); i++) {
                JSONObject jsonObject = jsonArray.getJSONObject(i);
                FileStats fileStats = new FileStats();
                if (jsonObject.containsKey("file_name")) {
                    fileStats.setFileName(jsonObject.getString("file_name"));
                }
                if (jsonObject.containsKey("file_size")) {
                    fileStats.setFileSize(jsonObject.getLong("file_size"));
                }
                if (jsonObject.containsKey("file_stats")) {
                    fileStats.setFileStats(jsonObject.getString("file_stats"));
                }
                if (jsonObject.containsKey("file_protocol")) {
                    fileStats.setFileProtocol(jsonObject.getString("file_protocol"));
                }
                list.add(fileStats);
            }
            this.fileStats = list;
        }
        this.insertTime = Instant.now().toEpochMilli();
    }

    public static void main(String[] args) {
        // 模拟数据
        String data = "{\n" +
                "  \"tm\": 1537349801000,\n" +
                "  \"version\": \"0.1\",\n" +
                "  \"ip\": \"*************\",\n" +
                "  \"monitor_stats\": \"active\",\n" +
                "  \"daemon_stats\": \"active\",\n" +
                "  \"cap_stats\": \"active\",\n" +
                "  \"upload_stats\": \"active\",\n" +
                "  \"cpu_usage\": 14,\n" +
                "  \"pcap_capcity\": 0,\n" +
                "  \"file_stats\": [\n" +
                "    {\n" +
                "      \"file_name\": \"文件1\",\n" +
                "      \"file_size\": 100,\n" +
                "      \"file_stats\": \"success\",\n" +
                "      \"file_protocol\": \"FTP\"\n" +
                "    },\n" +
                "    {\n" +
                "      \"file_name\": \"文件2\",\n" +
                "      \"file_size\": 1110,\n" +
                "      \"file_stats\": \"fail\",\n" +
                "      \"file_protocol\": \"FTPS\"\n" +
                "    }\n" +
                "  ]\n" +
                "}";


        String a = "{\n" +
                "  \"file_stats\": [\n" +
                "    {\n" +
                "      \"file_name\": \"file1\",\n" +
                "      \"file_stats\": \"fail\"\n" +
                "    },\n" +
                "    {\n" +
                "      \"file_name\": \"file2\",\n" +
                "      \"file_stats\": \"success\"\n" +
                "    }\n" +
                "  ],\n" +
                "  \"version\":\"1\"\n" +
                "}";
        JSONObject json = JSON.parseObject(a);
        Object o = json.get("file_stats");
        JSONArray list = JSONArray.parseArray(o.toString());
        // for (Object o1 : list) {
        //     JSONObject jsonObject = (JSONObject) o1;
        //     System.out.println(jsonObject.get("file_name"));
        // }
        for (int i = 0; i < list.size(); i++) {
            JSONObject jsonObject = list.getJSONObject(i);
            System.out.println(jsonObject.get("file_name"));
        }
        // list.forEach(s -> System.out.println(s.toString()));
        // System.out.println(o.toString());
    }


}
