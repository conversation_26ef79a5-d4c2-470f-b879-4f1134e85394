package com.quanzhi.auditapiv2.common.dal.dto.api;

import com.quanzhi.re.core.domain.entity.po.MatchRule;
import lombok.Data;

/**
 * <AUTHOR>
 * create at 2023/5/11 15:53
 * @description:
 **/
@Data
public class CommonApiFeature {

    private String id;
    private String name;
    private Boolean enable;
    /**
     * 规则
     */
    private MatchRule matchRule;
    private Integer type;
    private Integer code;
    private String group;
    private String groupName;

}
