package com.quanzhi.auditapiv2.common.dal.dao.impl;

import com.quanzhi.auditapiv2.common.dal.dao.IVisitTrendDao;
import com.quanzhi.auditapiv2.common.dal.entity.securityPosture.VisitTrend;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * @Auther: yangzixian
 * @Date: 2021/10/18 19:34
 * @Description:
 */
@Repository
public class VisitTrendDaoImpl implements IVisitTrendDao {

    @Autowired
    private MongoTemplate mongoTemplate;

    private String collectionName = "visitTrend";

    @Override
    public void saveVisitTrend(VisitTrend visitTrend) {
        mongoTemplate.save(visitTrend);
    }

    @Override
    public List<VisitTrend> getVisitTrend(long time) {
        return mongoTemplate.find(new Query().addCriteria(Criteria.where("time").lte(time)).limit(20), VisitTrend.class, collectionName);
    }

    @Override
    public void clearData() {
        //当前时间前两分钟
        long time = System.currentTimeMillis() - 1000 * 60 * 2;
        mongoTemplate.remove(new Query().addCriteria(Criteria.where("time").lte(time)), collectionName);
    }

    @Override
    public VisitTrend selectVisitTrendLatest() {
        return mongoTemplate.findOne(new Query().with(Sort.by(Sort.Order.desc("time"))).limit(1), VisitTrend.class, collectionName);
    }

}
