package com.quanzhi.auditapiv2.common.dal.dao;


import com.quanzhi.auditapiv2.common.dal.dao.base.IBaseDao;
import com.quanzhi.auditapiv2.common.dal.entity.ScheduleJobStatus;

/**
 * @author: zhousong
 * @data: 2018/3/4
 * 定时任务状态DAO
 */
public interface IScheduleJobStatusDao extends IBaseDao<ScheduleJobStatus> {

    /**
     * 保存
     * @param jobStatus
     */
    void upsert(ScheduleJobStatus jobStatus);

    /**
     * 根据任务名称查询任务状态
     * @param job
     */
    ScheduleJobStatus query(String job);

    ScheduleJobStatus query(String job, String date);
}
