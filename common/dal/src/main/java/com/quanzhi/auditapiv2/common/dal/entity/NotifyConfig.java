package com.quanzhi.auditapiv2.common.dal.entity;

import lombok.Data;

import java.util.List;

/**
 * 《通知配置》
 *
 * <AUTHOR> [<EMAIL>]
 * @Project:
 * @Module ID:
 * @Comments:
 * @JDK version used:      <JDK1.8>
 * @since 2020-04-08-上午9:56:10
 */
@Data
public class NotifyConfig {

    private String id;

    /**
     * 配置名称
     */
    private String name;

    /**
     * 唯一编码
     */
    private String code;

    /**
     * 通知类型
     */
    private NotifyTypeEnum type;

    /**
     * 通知来源
     */
    private String notifySource;

    /**
     * 通知方式
     */
    private List<NotifyMethodEnum> methods;

    /**
     * 通知名称
     */
    private String title;

    /**
     * 通知名称数值
     */
    private String value;

    /**
     * 是否显示填充数据
     */
    private Boolean isShow;

    /**
     * 通知名称数值单位
     */
    private String unit;

    /**
     * 是否发送通知
     */
    private Boolean isSend;

    /**
     * 接收邮箱分组
     */
    private String emailGroup;

    /**
     * webhook
     */
    private String webhookConfigId;

    public enum NotifyCodeEnum {

        /**
         * KAFKA积压超过负载
         */
        KAFKA_LAG_OVERLOAD("kafka积压超过100w"),

        /**
         * 数据清理
         * 系统积压超过
         */
        DATA_CLEAN_ACTION("数据清理"),

        /**
         * 系统盘空间不足
         */
        SYSTEM_DISK("系统盘空间不足20%"),

        /**
         * 数据盘空间不足
         */
        DATA_DISK("数据盘空间不足20%"),

        /**
         * 磁盘Inode使用率过高
         */
        INODE_DISK("数据盘Inode使用率超过80%"),

        /**
         * 每秒内I/O操作耗时占比过高
         */
        IO_DISK("每秒内I/O操作耗时占比超过80%"),

        /**
         * 可用内存小于
         */
        RAM("可用内存小于20%"),

        /**
         * CPU使用率大于
         */
        CPU("CPU使用率大于80%"),

        /**
         * 网口无流量进入
         */
        GATEWAY_FLOW_ZERO("网口无流量进入"),

        /**
         * 网关进入关无流
         */
        WAY_FLOW_ZERO("网关无流量接入"),

        /**
         * 网络流量超过性能负载
         */
        GATEWAY_FLOW_OVERLOAD("网络流量超过性能负载"),

        /**
         * 系统无http流量进入
         */
        SYSTEM_HTTP_FLOW_ZERO("系统无http流量进入"),

        /**
         * 系统http流量超过性能负载
         */
        SYSTEM_HTTP_FLOW_OVERLOAD("系统http流量超过性能负载"),

        /**
         * 集群服务器状态异常
         */
        NODE_STATUS_ERROR("集群服务器状态异常"),

        /**
         * 组件状态异常
         */
        COMPONENT_STATUS_ERROR("组件状态异常"),

        /**
         * 服务异常
         */
        SERVICE_STATUS_ERROR("服务异常"),

        /**
         * POD异常
         */
        POD_STATUS_ERROR("POD异常"),

        /**
         * 导出
         */
        EXPORT("导出完成/失败"),

        /**
         * 任务运行
         */
        TASK("任务运行完成/失败"),

        /**
         * 系统升级
         */
        SYSTEM_UPDATE("系统升级完成/失败"),

        /**
         * 系统授权
         */
        SYSTEM_LICENSE("系统授权完成/失败"),

        /**
         * 系统授权过期
         */
        SYSTEM_LICENSE_EXPIRE("系统授权过期"),

        /**
         * 网关授权过期
         */
        GATEWAY_LICENSE_EXPIRE("网关授权过期"),

        /**
         * 网关授权
         */
        GATEWAY_LICENSE("网关授权完成/失败"),

        DISK_ALERT("磁盘不足，请及时清理"),

        /**
         * 系统积压超过
         */
        SYSTEM_BACKLOG("系统积压超过XXXX条/恢复正常");

        String name;

        NotifyCodeEnum(String name) {
            this.name = name;
        }

        public String getName() {
            return name;
        }

        /**
         * 根据status获取枚举对象
         */
        public static NotifyCodeEnum getNotifyCodeEnum(String code) {

            for (NotifyCodeEnum notifyCodeEnum : NotifyCodeEnum.values()) {
                if (notifyCodeEnum.toString().equals(code)) {
                    return notifyCodeEnum;
                }
            }
            return null;
        }
    }

    public enum NotifyTypeEnum {

        /**
         * 监控
         */
        MONITOR("监控通知"),

        /**
         * 系统通知
         */
        SYSTEM("系统通知"),

        /**
         * 弱点通知
         */
        WEAKNESS("弱点通知"),

        /**
         * 异常通知
         */
        RISK("异常通知"),

        /**
         * 业务通知
         */
        SERVICE("业务通知");

        String name;

        NotifyTypeEnum(String name) {
            this.name = name;
        }

        public String getName() {
            return name;
        }
    }

    public enum NotifyMethodEnum {

        /**
         * 站内信
         */
        INTERNAL("站内信"),

        /**
         * 邮件
         */
        EMAIL("邮件"),

        /**
         * webhook
         */
        WEBHOOK("webhook");

        String name;

        NotifyMethodEnum(String name) {

            this.name = name;
        }

        public static NotifyMethodEnum getNotifyMethodEnum(String str) {

            for (NotifyMethodEnum notifyMethodEnum : NotifyMethodEnum.values()) {
                if (notifyMethodEnum.toString().equals(str)) {
                    return notifyMethodEnum;
                }
            }
            return null;
        }

        public String getName() {
            return name;
        }
    }
}
