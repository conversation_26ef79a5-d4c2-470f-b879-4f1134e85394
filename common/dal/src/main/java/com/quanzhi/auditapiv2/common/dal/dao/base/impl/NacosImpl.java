package com.quanzhi.auditapiv2.common.dal.dao.base.impl;


import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.nacos.api.annotation.NacosInjected;
import com.alibaba.nacos.api.config.ConfigService;
import com.alibaba.nacos.api.config.annotation.NacosValue;
import com.alibaba.nacos.api.config.listener.Listener;
import com.alibaba.nacos.api.exception.NacosException;
import com.quanzhi.audit_core.common.model.DataLabel;
import com.quanzhi.audit_core.extract.service.IExtractService;
import com.quanzhi.auditapiv2.common.dal.dao.base.INacos;
import com.quanzhi.auditapiv2.common.dal.dto.query.NacosFilterDto;
import com.quanzhi.auditapiv2.common.dal.dto.query.Predicate;
import com.quanzhi.auditapiv2.common.dal.dto.query.QueryNacos;
import com.quanzhi.auditapiv2.common.dal.entity.NacosBaseConfig;
import com.quanzhi.auditapiv2.common.dal.entity.RiskTemplate;
import com.quanzhi.auditapiv2.common.dal.enums.NacosConfigEnum;
import com.quanzhi.auditapiv2.common.dal.enums.NacosContentTypeEnum;
import com.quanzhi.auditapiv2.common.util.constant.DataConstant;
import com.quanzhi.auditapiv2.common.util.utils.*;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.lang.reflect.Field;
import java.util.*;
import java.util.concurrent.Executor;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

@Slf4j
@Component
public class NacosImpl<T> implements INacos<T> {
    private static final Logger logger = LoggerFactory.getLogger(NacosImpl.class);

    private static final long timeoutMs = 5000;
    private Map<String, List<T>> nacosConfigContentMap = new HashMap<>();

    @NacosInjected
    private ConfigService configService;

    @Autowired
    private IExtractService extractService;

    @NacosValue(value = "${nacos.save.sleep.time:2000}", autoRefreshed = true)
    private Integer nacosSaveSleepTime = 2000;

    private void initExtractLabel() {

        try {
            NacosBaseConfig nacosBaseConfig = NacosConfigEnum.DATA_LABEL.getNacosBaseConfig();
            String dataId = nacosBaseConfig.getDataId();
            String group = nacosBaseConfig.getGroup();
            Class clazz = nacosBaseConfig.getClazz();

            String content = configService.getConfig(dataId, group, timeoutMs);
            loadDataLabels(JSONArray.parseArray(content, clazz));

        } catch (Exception e) {
            log.error("initExtractLabel error", e);
        }
    }

    /**
     * 获取 properties类型的nacos配置
     *
     * @param property
     * @param dataId
     * @param group
     * @return
     */
    @Override
    public String getPropertyValue(String property, String dataId, String group) {
        try {
            String value = "";

            String content = configService.getConfig(dataId, group, 5000);

            String pattern = "(^|\\n|\\r)" + property + "=([^\\n\\r]*)(\\n|\\r|$)";

            Pattern r = Pattern.compile(pattern);

            Matcher m = r.matcher(content);

            if (m.find()) {
                value = m.group(2);
            }
            return value;
        } catch (Exception e) {
            logger.error("获取 properties 类型的nacos配置:【groupId:{}, dataId:{}，property:{}】失败 \n {}", group, dataId, property, e.getMessage());
            return "";
        }
    }

    @Override
    public Map<String, String> getPropertyValues(List<String> properties, String dataId, String group) {
        Map<String,String> result = new HashMap<>();
        try {
            String content = configService.getConfig(dataId, group, 5000L);
            for (String property: properties){
                String pattern = "(^|\\n|\\r)" + property + "=([^\\n\\r]*)(\\n|\\r|$)";
                Pattern r = Pattern.compile(pattern);
                Matcher m = r.matcher(content);
                if (m.find()) {
                    String value = m.group(2);
                    if(value != null && !value.isEmpty()){
                        result.put(property,value);
                    }
                }
            }
        }catch (Exception e){
            log.error("get property values:[groupId:{}, dataId:{}] error:",group,dataId,e);
            return result;
        }
        return result;
    }

    /**
     * properties类型的nacos配置发布
     *
     * @param property
     * @param newValue
     * @param dataId
     * @param group
     */
    @Override
    public void publishPropertyValue(String property, String newValue, String dataId, String group) {
        try {
            String newContent = null;
            String content = configService.getConfig(dataId, group, 5000);

            if (content.indexOf(property) != -1) {

                String pattern = "(^|\\n|\\r)" + property + "=([^\\n\\r]*)(\\n|\\r|$)";

                newContent = content.replaceFirst(pattern, "\n" + property + "=" + newValue + "\n");
            } else {
                newContent = content + "\n\r" + property + "=" + newValue + "\n\r";
            }
            configService.publishConfig(dataId, group, newContent);
        } catch (Exception e) {
            logger.error("发布 properties 类型的nacos配置:【groupId:{}, dataId:{}，property:{}，value:{}】失败 \n {}", group, dataId, property, newValue, e.getMessage());
        }
    }

    @Override
    public void publishPropertyValues(Map<String,Object> properties, String dataId, String group) {
        try {
            if(properties == null || properties.isEmpty()){
                return;
            }
            String content = configService.getConfig(dataId, group, 5000);
            String newContent = content;
            for (Map.Entry<String,Object> entry:properties.entrySet()){
                if(entry.getKey() == null || entry.getValue() == null){
                    continue;
                }
                if (newContent.contains(entry.getKey())) {
                    String pattern = "(^|\\n|\\r)" + entry.getKey() + "=([^\\n\\r]*)(\\n|\\r|$)";
                    newContent = newContent.replaceFirst(pattern, "\n" + entry.getKey() + "=" + entry.getValue() + "\n");
                } else {
                    newContent = newContent + "\n\r" + entry.getKey() + "=" + entry.getValue() + "\n\r";
                }
            }
            configService.publishConfig(dataId, group, newContent);
        } catch (Exception e) {
            logger.error("publish properties type config:[groupId:{}, dataId:{}] error", group, dataId, e);
        }
    }

    /**
     * 根据具体Id获取配置信息
     *
     * @param id
     * @param collectionName
     * @return
     */
    @Override
    public T findOne(String id, String collectionName) {
        QueryNacos queryNacos = new QueryNacos();
        queryNacos.where(DataConstant.PROPERTY_BASE_ID, Predicate.IS, id);

        queryNacos.setSkip(0);
        queryNacos.setLimit(1);

        List<T> contentList = this.find(queryNacos, collectionName);
        if (DataUtil.isNotEmpty(contentList)) {
            for (T modelInfo : contentList) {
                return modelInfo;
            }
        }

        return null;
    }

    @Override
    public T findOne(QueryNacos queryNacos, String collectionName) {
        if (DataUtil.isEmpty(queryNacos)) {
            queryNacos = new QueryNacos();
        }

        queryNacos.setSkip(0);
        queryNacos.setLimit(1);

        List<T> contentList = this.find(queryNacos, collectionName);
        if (DataUtil.isNotEmpty(contentList)) {
            for (T modelInfo : contentList) {
                return modelInfo;
            }
        }

        return null;
    }

    @Override
    public long getCount(String collectionName) {
        List<T> contentList = this.getAll(collectionName);
        if (DataUtil.isNotEmpty(contentList)) {
            return contentList.size();
        }

        return 0;
    }

    /**
     * 分页查询
     *
     * @param page
     * @param limit
     * @param collectionName
     * @return
     */
    @Override
    public List<T> page(Integer page, Integer limit, String collectionName) {
        List<T> contentList = this.getAll(collectionName);
        return this.page(page, limit, contentList);
    }

    @Override
    public List<T> page(Integer page, Integer limit, String collectionName, boolean isSync) {
        List<T> contentList = this.getAll(collectionName,isSync);
        return this.page(page, limit, contentList);
    }

    @Override
    public long getCount(List<NacosFilterDto> nacosFilterDtos, String collectionName) {
        // 按条件过滤
        List<T> filterList = this.filter(nacosFilterDtos, collectionName,true);

        return filterList.size();
    }

    /**
     * nacos中有过滤条件
     *
     * @param page
     * @param limit
     * @param nacosFilterDtos
     * @param collectionName
     * @return
     */
    @Override
    public List<T> page(Integer page, Integer limit, List<NacosFilterDto> nacosFilterDtos, String collectionName) {
        // 按条件过滤
        List<T> filterList = this.filter(nacosFilterDtos, collectionName,false);

        // 分页返回结果
        return this.page(page, limit, filterList);
    }

    @Override
    public List<T> page(Integer page, Integer limit, List<NacosFilterDto> nacosFilterDtos, String collectionName, boolean isSync) {
        // 按条件过滤
        List<T> filterList = this.filter(nacosFilterDtos, collectionName, isSync);

        // 分页返回结果
        return this.page(page, limit, filterList);
    }

    @Override
    public List<T> getAll(String collectionName) {
        try {
            List<T> allCollections = this.checkDataResourceExit(collectionName, true);

            if (DataUtil.isNotEmpty(allCollections)) {

                NacosBaseConfig nacosBaseConfig = NacosConfigEnum.getNacosConfigByCollection(collectionName);

                return JSONArray.parseArray(JSON.toJSONString(allCollections), nacosBaseConfig.getClazz());
            }

        } catch (Exception e) {
            logger.error(e.getMessage());
        }

        return new ArrayList<>();
    }

    public List<T> getAll(String collectionName, boolean isSync) {
        try {
            List<T> allCollections = this.checkDataResourceExit(collectionName, isSync);

            if (DataUtil.isNotEmpty(allCollections)) {

                NacosBaseConfig nacosBaseConfig = NacosConfigEnum.getNacosConfigByCollection(collectionName);

                return JSONArray.parseArray(JSON.toJSONString(allCollections), nacosBaseConfig.getClazz());
            }

        } catch (Exception e) {
            logger.error(e.getMessage());
        }

        return new ArrayList<>();
    }

    /**
     * 根据封装的条件，查询结果
     *
     * @param queryNacos
     * @param collectionName
     * @return
     */
    @Override
    public List<T> find(QueryNacos queryNacos, String collectionName) {
        List<NacosFilterDto> nacosFilterDtos = queryNacos.getCriteria();
        Integer skip = queryNacos.getSkip() == null ? 0 : queryNacos.getSkip();
        Integer limit = queryNacos.getLimit() == null ? Integer.MAX_VALUE : queryNacos.getLimit();
        int page = (skip / limit) + 1;

        return page(page, limit, nacosFilterDtos, collectionName);
    }

    /**
     * 根据分装的条件，查询结果总数
     *
     * @param queryNacos
     * @param collectionName
     * @return
     */
    @Override
    public long count(QueryNacos queryNacos, String collectionName) {
        List<NacosFilterDto> nacosFilterDtos = queryNacos.getCriteria();
        return this.getCount(nacosFilterDtos, collectionName);
    }

    /**
     * 按照id批量删除
     *
     * @param ids
     * @param collectionName
     * @return
     */
    @Override
    public Boolean delete(List<String> ids, String collectionName) {
        try {
            List<T> allList = this.getAll(collectionName);

            // 移除指定id的数据
            Iterator<T> iterator = allList.iterator();
            while (iterator.hasNext()) {
                T modelInfo = iterator.next();
                if (ids.contains(this.getId(modelInfo))) {
                    iterator.remove();
                }
            }

            // 更新数据，重新发布到nacos
            this.publishNacosConfig(collectionName, JSON.toJSONString(allList));
            updateCacheIfExist(allList, collectionName);

            return true;
        } catch (NacosException e) {
            return false;
        }
    }

    /**
     * 根据model中的id去更新model中其他字段的信息
     * 全字段覆盖
     *
     * @param model
     * @param collectionName
     * @return
     */
    @Override
    public Boolean update(T model, String collectionName) {
        try {
            List<T> allList = this.getAll(collectionName);

            this.updateProperties(model, allList);

            this.publishNacosConfig(collectionName, JSON.toJSONString(allList));
            updateCacheIfExist(allList, collectionName);

            return true;
        } catch (Exception e) {
            return false;
        }
    }

    /**
     * 批量更新
     *
     * @param models
     * @param collectionName
     * @return
     */
    @Override
    public Boolean batchUpdate(List<T> models, String collectionName) {
        try {
            List<T> allList = this.getAll(collectionName);

            for (T model : models) {
                this.updateProperties(model, allList);
            }

            this.publishNacosConfig(collectionName, JSON.toJSONString(allList));
            updateCacheIfExist(allList, collectionName);

            return true;
        } catch (Exception e) {
            return false;
        }
    }

    @Override
    public void reset(List<T> models, String collectionName) {
        try {
            if (collectionName.equals("riskTemplate")) {
                publishNacosConfig(collectionName, JSON.toJSONString(models.get(0)));
            }else {
                publishNacosConfig(collectionName, JSON.toJSONString(models));
            }
            updateCacheIfExist(models, collectionName);
        } catch (NacosException e) {
            throw new ServiceException("保存配置信息失败");
        }
    }

    /**
     * 获取高级筛选查询后的结果
     *
     * @param nacosFilterList
     * @param collectionName
     * @return
     */
    @Override
    public List<T> select(List<NacosFilterDto> nacosFilterList, String collectionName) {
        return this.getFilterSelect(nacosFilterList, collectionName);
    }

    private List<T> getFilterSelect(List<NacosFilterDto> nacosFilterList, String collectionName) {
        // 获取所有内容
        List<T> contentList = this.getAll(collectionName);
        if (DataUtil.isEmpty(contentList)) {
            return contentList;
        }
        List<T> filterContentArr = new ArrayList<>();
        if (DataUtil.isNotEmpty(nacosFilterList)) {
            for (T content : contentList) {
                boolean isMatch = this.andAllFilter(content, nacosFilterList);

                if (isMatch) {
                    filterContentArr.add(content);
                }
            }
        } else {
            filterContentArr = contentList;
        }

        return filterContentArr;

    }


    /**
     * 所有筛选条件都符合返回true
     *
     * @param content
     * @param nacosFilterList
     * @return
     */
    private boolean andAllFilter(T content, List<NacosFilterDto> nacosFilterList) {
        int num = 0;
        for (NacosFilterDto nacosFilterDto : nacosFilterList) {
            Object filtervalue = ReflectUtils.getValue(content, nacosFilterDto.getKey());
            if (DataUtil.isEmpty(filtervalue)) {
                return false;
            }

            switch (nacosFilterDto.getPredicate()) {
                case IN:
                    List<String> dtoArr = (List) nacosFilterDto.getValue();

                    if (filtervalue instanceof String || filtervalue instanceof Integer) {
                        if (dtoArr.contains(filtervalue)) {
                            num++;
                            continue;
                        }
                        return false;

                    } else if (filtervalue instanceof List) {
                        ArrayList<String> filterArr = (ArrayList) filtervalue;
                        for (String filterVal : dtoArr) {
                            if (filterArr.contains(filterVal)) {
                                num++;
                                break;
                            }
                        }
                        continue;
                    } else {
                        //TODO
                        continue;
                    }

                case IS:
                    if (!(filtervalue.toString()).equals(nacosFilterDto.getValue().toString())) {
                        return false;
                    }
                    num++;
                    continue;
                case REGEX:
                    if (!(filtervalue.toString()).contains(nacosFilterDto.getValue().toString())) {
                        return false;
                    }
                    num++;
                    continue;
                default:
                    continue;
            }
        }
        if (num == nacosFilterList.size()) {
            return true;
        }

        return false;
    }


    /**
     * 保存配置 如果入参model中有id，就更新当条记录，没有就生成新的记录
     *
     * @param model
     * @param collectionName
     * @return
     */
    @Override
    public T save(T model, String collectionName) {
        try {
            List<T> allList = this.getAll(collectionName);

            String id = this.getId(model);
            if (DataUtil.isNotEmpty(id)) {
                this.updateProperties(model, allList);
            } else {
                id = MD5Util.md5(DataUtil.getRandomStr(32));
                ReflectUtils.executeMethod(model, "setId", new Class[]{String.class}, new Object[]{id});

                allList.add(model);
            }

            this.publishNacosConfig(collectionName, JSON.toJSONString(allList));
            updateCacheIfExist(allList, collectionName);
            return model;
        } catch (Exception e) {
            logger.error("保存nacos配置失败", e);
            return null;
        }
    }

    /**
     * 保存单个配置所有信息，一般用于覆盖上传这种
     *
     * @param models
     * @param collectionName
     */
    @Override
    public void saveAll(List<T> models, String collectionName) {
        try {
            publishNacosConfig(collectionName, JSON.toJSONString(models));
            updateCacheIfExist(models, collectionName);
        } catch (Exception e) {
            throw new ServiceException("保存配置信息失败");
        }
    }

    /**
     * 如果是缓存过的配置，则更新缓存
     */
    private void updateCacheIfExist(List<T> configs, String collection) {
        if (nacosConfigContentMap.containsKey(collection)) {
            nacosConfigContentMap.put(collection, configs);
        }
    }

    /**
     * 增删改这三个操作对整个数组进行操作后再把整个数组序列化后push上去
     *
     * @param collectionName
     */
    private void publishNacosConfig(String collectionName, String content) throws NacosException {
        NacosBaseConfig nacosBaseConfig = NacosConfigEnum.getNacosConfigByCollection(collectionName);
        configService.publishConfig(nacosBaseConfig.getDataId(), nacosBaseConfig.getGroup(), content);
        //下面这段不能删，涉及nacos更新机制。删了会有很多问题
        try {
            Thread.sleep(nacosSaveSleepTime);
        } catch (Exception ex) {

        }
    }

    /**
     * 获取修改的数据源
     *
     * @param collectionName
     */
    private List<T> checkDataResourceExit(String collectionName, boolean isSync) {
        try {
            if (isSync) {
                if (collectionName.equals("riskTemplate")) {
                    return getSyncDataRiskTemplate(collectionName);
                }
                return getSyncData(collectionName);
            }
            if (DataUtil.isNotEmpty(nacosConfigContentMap.get(collectionName))) {
                return nacosConfigContentMap.get(collectionName);
            } else {
                if (collectionName.equals("riskTemplate")) {
                    return getSyncDataRiskTemplate(collectionName);
                }
                return getSyncData(collectionName);
            }
        } catch (Exception e) {
            logger.info("无此列表信息:" + collectionName);
        }

        return new ArrayList<>();
    }

    private List<T> getSyncData(String collectionName) throws NacosException {
        NacosBaseConfig nacosBaseConfig = NacosConfigEnum.getNacosConfigByCollection(collectionName);
        if (DataUtil.isNotEmpty(nacosBaseConfig)) {
            String content = configService.getConfig(nacosBaseConfig.getDataId(), nacosBaseConfig.getGroup(), timeoutMs);
            if (DataUtil.isNotEmpty(content)) {
                nacosConfigContentMap.put(collectionName, JSONArray.parseArray(content, nacosBaseConfig.getClazz()));
            } else {
                nacosConfigContentMap.put(collectionName, new ArrayList<>());
            }
            return nacosConfigContentMap.get(collectionName);
        }
        return new ArrayList<>();
    }

    private List<T> getSyncDataRiskTemplate(String collectionName) throws NacosException {
        NacosBaseConfig nacosBaseConfig = NacosConfigEnum.getNacosConfigByCollection(collectionName);
        if (DataUtil.isNotEmpty(nacosBaseConfig)) {
            String content = configService.getConfig(nacosBaseConfig.getDataId(), nacosBaseConfig.getGroup(), timeoutMs);
            if (DataUtil.isNotEmpty(content)) {
                List<RiskTemplate> riskTemplates = new ArrayList<>();
                riskTemplates.add(JSONObject.parseObject(content, RiskTemplate.class));
                nacosConfigContentMap.put(collectionName, (List<T>) riskTemplates);
            } else {
                nacosConfigContentMap.put(collectionName, new ArrayList<>());
            }
            return nacosConfigContentMap.get(collectionName);
        }
        return new ArrayList<>();
    }

    /**
     * 获取id的值
     *
     * @param modelInfo
     * @return
     */
    private String getId(T modelInfo) {
        return (String) ReflectUtils.getValue(modelInfo, DataConstant.PROPERTY_BASE_ID);
    }

    /**
     * 覆盖一条记录的信息
     *
     * @param model
     * @param allList
     */
    private void updateProperties(T model, List<T> allList) {

        String modelId = this.getId(model);

        boolean hasUpdate = false;

        for (int i = 0; i < allList.size(); i++) {

            T contentInfo = allList.get(i);

            String contentId = (String) ReflectUtils.getValue(contentInfo, "id");

            if (modelId.equals(contentId)) {
                hasUpdate = true;
                Class cls = model.getClass();
                Field[] fields = getDeclaredFields(cls);

                for (int j = 0; j < fields.length; j++) {
                    Field f = fields[j];
                    f.setAccessible(true);

                    try {
                        if (f.get(model) != null) {

                            String methodName = "set" + f.getName().replaceFirst(f.getName().substring(0, 1)
                                    , f.getName().substring(0, 1).toUpperCase());

                            ReflectUtils.executeMethod(contentInfo, methodName, new Class[]{f.get(model).getClass()}, new Object[]{f.get(model)});
                        }
                    } catch (Exception e) {
                        log.error("update content arr error:", e);
                    }
                }
                break;
            }
        }

        if (!hasUpdate) {
            allList.add(model);
        }
    }

    private static final Field[] getDeclaredFields(Class<?> clazz) {
        List<Field> fieldList = new ArrayList<>();
        while (clazz != null) {
            fieldList.addAll(new ArrayList<>(Arrays.asList(clazz.getDeclaredFields())));
            clazz = clazz.getSuperclass();
        }
        Field[] fields = new Field[fieldList.size()];
        fieldList.toArray(fields);
        return fields;
    }

    /**
     * 根据条件过滤文件的全部内容
     *
     * @param nacosFilterDtos
     * @param collectionName
     * @return
     */
    private List<T> filter(List<NacosFilterDto> nacosFilterDtos, String collectionName, boolean isSync) {
        // 获取所有内容
        List<T> contentList = this.getAll(collectionName, isSync);
        if (DataUtil.isEmpty(contentList)) {
            return contentList;
        }

        // 按条件过滤
        return this.getFilterArr(nacosFilterDtos, contentList);
    }

    /**
     * 根据条件过滤内容
     *
     * @param nacosFilterDtos
     * @param contentArr
     * @return
     */
    private List<T> getFilterArr(List<NacosFilterDto> nacosFilterDtos, List<T> contentArr) {
        List<T> filterContentArr = new ArrayList<>();
        if (DataUtil.isNotEmpty(nacosFilterDtos)) {
            for (T content : contentArr) {
                boolean isMatch = this.andFilter(content, nacosFilterDtos);

                if (isMatch) {
                    filterContentArr.add(content);
                }
            }
        } else {
            filterContentArr = contentArr;
        }

        return filterContentArr;
    }

    /**
     * 多条件 and 组合查询
     *
     * @param content
     * @param nacosFilterDtos
     * @return true - 结果满足匹配，false - 结果不满足条件
     */
    private boolean andFilter(T content, List<NacosFilterDto> nacosFilterDtos) {
        for (NacosFilterDto nacosFilterDto : nacosFilterDtos) {
            Object filtervalue = ReflectUtils.getValue(content, nacosFilterDto.getKey());
            if (DataUtil.isEmpty(filtervalue)) {
                return false;
            }

            switch (nacosFilterDto.getPredicate()) {
                case IN:
                    ArrayList<String> filterArr = (ArrayList) filtervalue;

                    List<String> dtoArr = (List) nacosFilterDto.getValue();

                    for (String filterVal : dtoArr) {
                        if (filterArr.contains(filterVal)) {
                            return true;
                        }
                    }
                    return false;

                case IS:
                    if (!(filtervalue.toString()).equals(nacosFilterDto.getValue().toString())) {
                        return false;
                    }
                    break;
                case REGEX:
                    if (!(filtervalue.toString()).contains(nacosFilterDto.getValue().toString())) {
                        return false;
                    }
                    break;
                default:
                    continue;
            }
        }

        return true;
    }

    /**
     * 多条件 or 组合查询
     *
     * @param content
     * @param nacosFilterDtos
     * @return true - 结果满足匹配，false - 结果不满足条件
     * @TODO 测试
     */
    private boolean orFilter(T content, List<NacosFilterDto> nacosFilterDtos) {
        for (NacosFilterDto nacosFilterDto : nacosFilterDtos) {
            Object filtervalue = ReflectUtils.getValue(content, nacosFilterDto.getKey());
            if (DataUtil.isEmpty(filtervalue)) {
                return false;
            }

            switch (nacosFilterDto.getPredicate()) {
                case IN:
                    ArrayList<String> filterArr = (ArrayList) filtervalue;

                    if (filterArr.contains(nacosFilterDto.getValue())) {
                        return true;
                    }
                    break;
                case IS:
                    if ((filtervalue.toString()).equals(nacosFilterDto.getValue())) {
                        return true;
                    }
                    break;
                case REGEX:
                    if ((filtervalue.toString()).contains(nacosFilterDto.getValue().toString())) {
                        return true;
                    }
                    break;
                default:
                    continue;
            }
        }

        return false;
    }

    /**
     * 分页返回结果
     *
     * @param page
     * @param limit
     * @param contentList
     * @return
     */
    @Override
    public List<T> page(Integer page, Integer limit, List<T> contentList) {
        if (DataUtil.isEmpty(contentList)) {
            return contentList;
        }

        ListPageUtil<T> listPageUtil = new ListPageUtil<>(contentList, page, limit);

        return listPageUtil.getPagedList();
    }

    /**
     * 初始化nacos配置，实时更新配置内容
     */
    public void initNacosConfig() {

        this.initExtractLabel();

        for (NacosConfigEnum nacosConfigEnum : NacosConfigEnum.values()) {
            String collection = nacosConfigEnum.getNacosCollection();
            NacosBaseConfig nacosBaseConfig = nacosConfigEnum.getNacosBaseConfig();

            String group = nacosBaseConfig.getGroup();
            String dataId = nacosBaseConfig.getDataId();
            Class clazz = nacosBaseConfig.getClazz();
            String type = nacosBaseConfig.getContentType();

            try {
                String content = configService.getConfigAndSignListener(dataId, group, timeoutMs,new NacosConfigContentListener(collection, clazz));

                if (type.equals(NacosContentTypeEnum.JSON.name())) {
                    if (!"auditapiv2.riskTemplate.json".equals(dataId)) {
                        nacosConfigContentMap.put(collection, JSONArray.parseArray(content, clazz));
                    }
                }
            } catch (NacosException e) {
                logger.error("获取nacos配置:[groupId:{}, dataId:{}]失败 \n {}", group, dataId, e.getErrMsg());
            }

        }
    }

    /**
     * 实现对nacos配置内容的监听
     * 实时更新nacos配置内容
     */
    private class NacosConfigContentListener implements Listener {
        private String collection;
        private Class clazz;

        NacosConfigContentListener(String collection, Class clazz) {
            this.collection = collection;
            this.clazz = clazz;
        }

        @Override
        public Executor getExecutor() {
            return null;
        }

        @Override
        public void receiveConfigInfo(String configInfo) {
            nacosConfigContentMap.put(collection, JSONArray.parseArray(configInfo, clazz));

            if (collection.equals(NacosConfigEnum.DATA_LABEL.getNacosCollection())) {
                loadDataLabels(JSONArray.parseArray(configInfo, clazz));
            }
        }
    }

    private void loadDataLabels(List<DataLabel> dataLabels) {
        if (dataLabels == null || dataLabels.isEmpty()) {
            return;
        }
        // 只过滤删除和识别状态，存储状态无关
        List<DataLabel> collect = dataLabels.stream()
                .filter(label -> {
                    if (label.getDelFlag() != null && label.getDelFlag() != 0) {
                        return false;
                    }
                    if (label.getEnabled() != null && !label.getEnabled()) {
                        return false;
                    }
                    return true;
                })
                .collect(Collectors.toList());
        if (collect.isEmpty()) {
            return;
        }
        log.info("load dataLabel {}",collect.size());
        extractService.loadDataLabels(collect);

    }

}
