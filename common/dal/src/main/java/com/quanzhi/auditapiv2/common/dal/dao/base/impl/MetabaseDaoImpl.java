package com.quanzhi.auditapiv2.common.dal.dao.base.impl;

import com.quanzhi.auditapiv2.common.dal.dao.base.IMetabaseDao;
import com.quanzhi.auditapiv2.common.util.dto.AggregationDto;
import com.quanzhi.auditapiv2.common.util.dto.GroupDto;
import com.quanzhi.auditapiv2.common.util.utils.ConstantUtil;
import com.quanzhi.auditapiv2.common.util.utils.DataUtil;
import com.quanzhi.auditapiv2.common.util.utils.GroupUtils;
import com.quanzhi.metabase.client.MetabaseClientTemplate;
import com.quanzhi.metabase.core.model.query.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.aggregation.Aggregation;
import org.springframework.data.mongodb.core.aggregation.AggregationOperation;
import org.springframework.data.mongodb.core.query.Criteria;

import java.lang.reflect.Field;
import java.lang.reflect.ParameterizedType;
import java.lang.reflect.Type;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;

import static org.springframework.data.mongodb.core.aggregation.Aggregation.newAggregationOptions;

@Slf4j
public class MetabaseDaoImpl<T> implements IMetabaseDao<T> {

    @Autowired
    protected MetabaseClientTemplate metabaseClientTemplate;

    @Autowired
    private MongoTemplate mongoTemplate;

    protected String collectionName;

    private Class<T> clazz;

    public MetabaseDaoImpl() {
        Type type = getClass();
        if (!(type instanceof ParameterizedType)) {
            type = getClass().getGenericSuperclass();
        }
        try {
            clazz = (Class<T>) ((ParameterizedType) type).getActualTypeArguments()[0];
        } catch (Exception e) {
            // ignore
        }

        determineCollectionName(this.clazz);
    }


    @Override
    public T findOne(String id) {

        T configInfo = metabaseClientTemplate.findOne(id, clazz);

        return configInfo;
    }

    @Override
    public T findQueryOne(Map<String, String> queryMap) {

        MetabaseQuery metabaseQuery = new MetabaseQuery();

        if (DataUtil.isNotEmpty(queryMap)) {

            for (String key : queryMap.keySet()) {
                String value = queryMap.get(key);
                metabaseQuery.where(key, Predicate.IS, value);
            }

        }
        T configInfo = metabaseClientTemplate.findOne(metabaseQuery, clazz);

        return configInfo;
    }

    @Override
    public boolean update(String id, T model) {

        ResourceUpdates updates = new ResourceUpdates();

        Class cls = model.getClass();
        List<Field> list = new ArrayList<Field>();
        list.addAll(Arrays.asList(cls.getDeclaredFields()));
        list.addAll(Arrays.asList(cls.getSuperclass().getDeclaredFields()));

        for (Field field : list) {

            field.setAccessible(true);

            try {
                //字段值不为空 放入map
                if (field.get(model) != null) {
                    updates.set(field.getName(), field.get(model));
                }
            } catch (IllegalAccessException e) {

                log.error("update model exception", e);
            }
        }

        metabaseClientTemplate.update(id, updates, clazz);
        return true;
    }

    @Override
    public List<T> getList(MetabaseQuery query) {
        return metabaseClientTemplate.find(query, clazz);
    }

    @Override
    public long getCount(MetabaseQuery query) {
        return metabaseClientTemplate.count(query, clazz);
    }

    @Override
    public T save(T model) {

        T entity = metabaseClientTemplate.save(model);
        return entity;
    }

    @Override
    public boolean exist(MetabaseQuery query) {
        return metabaseClientTemplate.existBy(query, clazz);
    }

    /**
     * 数据聚合
     *
     * @param query
     * @param metabaseGroupOperation
     * @param clz
     * @return
     */
    @Override
    public List<AggregationResult> aggregate(MetabaseQuery query, MetabaseGroupOperation metabaseGroupOperation, Class<?> clz) {
        return metabaseClientTemplate.aggregate(query, metabaseGroupOperation, clz);
    }

    /**
     * 根据实体类获取表名
     *
     * @param entityClass
     * @return
     */
    private void determineCollectionName(Class<?> entityClass) {
        if (DataUtil.isEmpty(entityClass)) {
            return;
        }
        // 如果collectionName字段不为空，那么不需要根据类名获取
        if (DataUtil.isNotEmpty(collectionName)) {
            return;
        }

        String collName = entityClass.getSimpleName();
        collName = collName.replaceFirst(collName.substring(0, 1)
                , collName.substring(0, 1).toLowerCase());
        this.collectionName = collName;

    }

    /**
     * 分组查询
     *
     * @param
     * @return
     * @Comments: <对此方法的描述，可以引用系统设计中的描述>
     * <AUTHOR> [<EMAIL>]
     * @since 2021-08-10 9:56
     */
    @Override
    public List<AggregationDto> group(Criteria criteria, GroupDto groupDto, Integer sort, Integer page, Integer limit, String collectionName) throws Exception {

        List<AggregationOperation> operations = new ArrayList<>();

        //查询条件
        if (DataUtil.isNotEmpty(criteria)) {
            operations.add(Aggregation.match(criteria));
        }
        //展开字段
        if (DataUtil.isNotEmpty(groupDto.getUnwind())) {
            operations.add(Aggregation.unwind(groupDto.getUnwind()));
        }
        //分组
        operations.add(GroupUtils.getGroupOperation(groupDto).as("resultAlias"));
        //排序
        if (DataUtil.isNotEmpty(sort)) {

            if (sort == ConstantUtil.Sort.ASC) {
                operations.add(Aggregation.sort(org.springframework.data.domain.Sort.Direction.ASC, "resultAlias").and(Sort.by(org.springframework.data.domain.Sort.Direction.ASC, "_id")));
            } else if (sort == ConstantUtil.Sort.DESC) {
                operations.add(Aggregation.sort(org.springframework.data.domain.Sort.Direction.DESC, "resultAlias").and(Sort.by(org.springframework.data.domain.Sort.Direction.ASC, "_id")));
            } else {
                operations.add(Aggregation.sort(org.springframework.data.domain.Sort.Direction.DESC, "resultAlias").and(Sort.by(org.springframework.data.domain.Sort.Direction.ASC, "_id")));
            }
        }
        //当前页
        if (DataUtil.isNotEmpty(page)) {

            operations.add(Aggregation.skip((page - 1)*limit));
        }
        //每页显示条数
        if (DataUtil.isNotEmpty(limit)) {

            operations.add(Aggregation.limit(limit));
        }

        Aggregation aggregation = Aggregation.newAggregation(operations).withOptions(newAggregationOptions().allowDiskUse(true).build());

        return mongoTemplate.aggregate(aggregation, collectionName, AggregationDto.class).getMappedResults();
    }
}
