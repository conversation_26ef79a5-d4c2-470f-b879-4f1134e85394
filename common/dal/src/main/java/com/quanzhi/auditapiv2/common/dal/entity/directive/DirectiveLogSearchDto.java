package com.quanzhi.auditapiv2.common.dal.entity.directive;

import com.quanzhi.auditapiv2.common.dal.dto.query.BaseSearchDto;
import lombok.Data;

import java.util.Map;

/**
 * @Author: yangzx
 * @Date: 2024/7/3 14:35
 */
@Data
public class DirectiveLogSearchDto extends BaseSearchDto {

    private String id;

    private String nodeId;

    private String remark;

    private String url;

    private String state;

    private String directiveValue;

    private boolean delFlag = false;

    private Long firstTimeStart;
    private Long firstTimeEnd;

    private Long lastTimeStart;
    private Long lastTimeEnd;

}
