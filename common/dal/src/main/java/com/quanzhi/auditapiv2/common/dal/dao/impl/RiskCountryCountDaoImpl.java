package com.quanzhi.auditapiv2.common.dal.dao.impl;

import com.quanzhi.auditapiv2.common.dal.dao.IRiskCountryCountDao;
import com.quanzhi.auditapiv2.common.dal.dao.base.impl.BaseDaoImpl;
import com.quanzhi.auditapiv2.common.dal.entity.RiskCountryCount;
import com.quanzhi.auditapiv2.common.util.utils.DataUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Set;

/**
 * 《风险国家分布统计持久层接口实现》
 *
 * <AUTHOR> [<EMAIL>]
 * @Project:
 * @Module ID:
 * @Comments:
 * @JDK version used:      <JDK1.8>
 * @since 2020-07-01-上午9:56:10
 */
@Repository
public class RiskCountryCountDaoImpl extends BaseDaoImpl<RiskCountryCount> implements IRiskCountryCountDao {

    @Autowired
    private MongoTemplate mongoTemplate;

    private String collectionName = "riskCountryCount";

    /**
     * 国家查询风险国家分布统计详情
     *
     * @param
     * @return
     * @Comments: <对此方法的描述，可以引用系统设计中的描述>
     * <AUTHOR> [<EMAIL>]
     * @since 2020-07-01 9:56
     */
    @Override
    public RiskCountryCount getRiskCountryCountByCountry(String country, String groupId) throws Exception {

        Criteria criteria = new Criteria();
        criteria.and("country").is(country);

        return mongoTemplate.findOne(new Query(criteria), RiskCountryCount.class, collectionName + "_" + groupId);
    }

    @Override
    public List<RiskCountryCount> getRiskCountryCount(String userGroupId) {
        return mongoTemplate.find(new Query(), RiskCountryCount.class, collectionName + "_" + userGroupId);
    }

    /**
     * 保存风险国家分布统计
     *
     * @param
     * @return
     * @Comments: <对此方法的描述，可以引用系统设计中的描述>
     * <AUTHOR> [<EMAIL>]
     * @since 2020-07-01 9:56
     */
    @Override
    public RiskCountryCount saveRiskCountryCount(RiskCountryCount riskCountryCount, String groupId) throws Exception {

        return mongoTemplate.save(riskCountryCount, collectionName + "_" + groupId);
    }

    @Override
    public void updateRiskCountryCount(RiskCountryCount riskCountryCount, String groupId) {
        Update update = new Update();
        update.set("count", riskCountryCount.getCount());
        update.set("isConfirm", riskCountryCount.getIsConfirm());
        mongoTemplate.upsert(new Query().addCriteria(Criteria.where("country").is(riskCountryCount.getCountry())), update, collectionName + "_" + groupId);
    }


    @Override
    public void saveRiskCountryCountByDataReveal(RiskCountryCount riskCountryCount) {
        String country = riskCountryCount.getCountry();
        RiskCountryCount dbOne = mongoTemplate.findOne(new Query().addCriteria(Criteria.where("country").is(country)), RiskCountryCount.class, collectionName + "_dataReveal");
        if (DataUtil.isNotEmpty(dbOne)) {
            Update update = new Update();
            update.set("count", riskCountryCount.getCount());
            mongoTemplate.upsert(new Query().addCriteria(Criteria.where("country").is(country)), update, collectionName + "_dataReveal");
        } else {
            mongoTemplate.insert(riskCountryCount, collectionName + "_dataReveal");
        }
    }

    @Override
    public List<RiskCountryCount> getRiskCountryCountByDataReveal() {
        return mongoTemplate.findAll(RiskCountryCount.class, collectionName + "_dataReveal");
    }

    @Override
    public void dealNoConfirmCountry(Set<String> nowCountrys) {
        Query query = new Query();
        query.addCriteria(Criteria.where("country").nin(nowCountrys));
        mongoTemplate.remove(query, collectionName + "_dataReveal");
    }

    @Override
    public void clear() {
        mongoTemplate.remove(new Query(), collectionName);
    }


}
