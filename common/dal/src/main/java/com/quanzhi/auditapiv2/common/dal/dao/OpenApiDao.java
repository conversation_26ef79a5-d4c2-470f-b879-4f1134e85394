package com.quanzhi.auditapiv2.common.dal.dao;

import com.quanzhi.auditapiv2.common.dal.dao.base.IBaseDao;
import com.quanzhi.auditapiv2.common.dal.entity.OpenApiConfig;

import java.util.List;

/**
 * @author: yang<PERSON>xian
 * @date: 11/4/2023 19:36
 * @description:
 */
public interface OpenApiDao extends IBaseDao<OpenApiConfig> {

    OpenApiConfig findOneOpenApiKey(String callerId);

    void editCallerID(String callerId,Long deadLine);

    void saveOpenApiKey(OpenApiConfig openApiConfig);

    void delOpenApiKey(String callerId);

    List<OpenApiConfig> pageOpenApiKey(Integer page, Integer limit);

    OpenApiConfig desensiOpenApiKey(String sensiAccesskey,String sensiSecretKey);

}
