package com.quanzhi.auditapiv2.common.dal.mongoCollectionIndex.document;

import com.quanzhi.metabase.core.model.http.HttpResourceConstant;
import org.springframework.data.mongodb.core.index.CompoundIndex;
import org.springframework.data.mongodb.core.index.CompoundIndexes;
import org.springframework.data.mongodb.core.index.Indexed;
import org.springframework.data.mongodb.core.mapping.Document;

import java.util.List;

@Document(HttpResourceConstant.IP_INFO)
@CompoundIndexes({
        @CompoundIndex(name = "riskNames", def = "{'riskInfo.riskNames': 1}", background = true),
        @CompoundIndex(name = "nodeId", def = "{'nodes.nid': 1}", background = true)
})
public class IpInfoDocument {
    @Indexed(background = true, unique = true, name = "ip_u")
    private String ip;
    @Indexed(background = true)
    private Integer riskLevel;

    @Indexed(background = true)
    private List<String> rspDataLabelList;

    @Indexed(background = true)
    private List<String> appUriList;

    @Indexed(background = true)
    private List<String> uaTypes;

    @Indexed(background = true)
    private Long visitCnt;

    @Indexed(background = true)
    private Long relatedAccountDistinctCnt;

    @Indexed(background = true)
    private Long rspDataLabelCnt;
    @Indexed(background = true)
    private String firstDate;

    @Indexed(background = true)
    private String lastDate;

    @Indexed(background = true)
    private Long updateTime;
}
