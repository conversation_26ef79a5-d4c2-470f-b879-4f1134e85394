package com.quanzhi.auditapiv2.common.dal.dto.aggRisk;

import com.quanzhi.auditapiv2.common.dal.entity.aggRisk.AggRiskOperationLog;
import com.quanzhi.auditapiv2.common.dal.entity.aggRisk.RiskV2OperationLog;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * @Author: yangzx
 * @Date: 2024/10/10 11:11
 */
@Data
public class RiskV2OperationLogDto {

    @ApiModelProperty("数据库ID")
    private String id;

    @ApiModelProperty("风险ID")
    private String riskId;

    @ApiModelProperty("操作人")
    private String operateName;

    @ApiModelProperty("旧状态")
    private Integer oldState;

    @ApiModelProperty("旧状态名称")
    private String oldStateName;

    @ApiModelProperty("新状态")
    private Integer newState;

    @ApiModelProperty("新状态名称")
    private String newStateName;

    @ApiModelProperty("操作时间")
    private Long operateTime;

    @Mapper
    public interface RiskV2OperationLogDtoMapper {

        RiskV2OperationLogDto.RiskV2OperationLogDtoMapper INSTANCE = Mappers.getMapper(RiskV2OperationLogDto.RiskV2OperationLogDtoMapper.class);

        RiskV2OperationLogDto convert(RiskV2OperationLog riskV2OperationLog);

    }

}
