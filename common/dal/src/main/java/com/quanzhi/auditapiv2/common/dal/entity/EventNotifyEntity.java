package com.quanzhi.auditapiv2.common.dal.entity;

import com.quanzhi.audit_core.common.model.HttpEvent;
import lombok.Data;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;
import org.mapstruct.factory.Mappers;

@Data
public class EventNotifyEntity extends HttpEvent {

    private String reqHeaderRaw;
    private String rspHeaderRaw;
    private String protocol;
    private String srcIp;
    private Integer srcPort;
    private String dstIp;
    private Integer dstPort;
    private Integer vlanId;

    @Mapper
    public interface EventNotifyEntityMapper {

        EventNotifyEntity.EventNotifyEntityMapper INSTANCE = Mappers.getMapper(EventNotifyEntity.EventNotifyEntityMapper.class);

        @Mappings({
                @Mapping(target = "srcIp", source = "net.srcIp"),
                @Mapping(target = "srcPort", source = "net.srcPort"),
                @Mapping(target = "dstIp", source = "net.dstIp"),
                @Mapping(target = "dstPort", source = "net.dstPort"),
                @Mapping(target = "vlanId", source = "net.vlanId")
        })
        EventNotifyEntity convert(HttpEvent httpEvent);

    }

}