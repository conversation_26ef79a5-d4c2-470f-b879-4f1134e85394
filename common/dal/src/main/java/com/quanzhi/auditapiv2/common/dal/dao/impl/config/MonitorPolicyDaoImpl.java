package com.quanzhi.auditapiv2.common.dal.dao.impl.config;

import com.quanzhi.audit_core.common.model.MonitoringPolicy;
import com.quanzhi.auditapiv2.common.dal.dao.base.impl.NacosBaseDaoImpl;
import com.quanzhi.auditapiv2.common.dal.dao.config.IMonitorPolicyDao;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * @ClassName MonitorPolicyDaoImpl
 * @Description
 * <AUTHOR>
 * @Date 2020/11/3 10:01
 **/
@Repository
public class MonitorPolicyDaoImpl extends NacosBaseDaoImpl<MonitoringPolicy> implements IMonitorPolicyDao {
    @Override
    public Boolean checkNameExist(String name, String excludeId) {
        List<MonitoringPolicy> monitoringPolicyList = getAll();

        for(int i = 0; i < monitoringPolicyList.size();i++) {

            MonitoringPolicy monitoringPolicy = monitoringPolicyList.get(i);

            if(!monitoringPolicy.getId().equals(excludeId) && monitoringPolicy.getName().equals(name)) {
                return true;
            }
        }
        return false;
    }
}
