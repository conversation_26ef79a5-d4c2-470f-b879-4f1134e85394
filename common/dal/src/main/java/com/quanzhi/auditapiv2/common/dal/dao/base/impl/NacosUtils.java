package com.quanzhi.auditapiv2.common.dal.dao.base.impl;


import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.nacos.api.annotation.NacosInjected;
import com.alibaba.nacos.api.config.ConfigService;
import com.alibaba.nacos.api.config.listener.Listener;
import com.alibaba.nacos.api.exception.NacosException;
import com.google.gson.Gson;
import com.quanzhi.audit_core.common.model.*;
import com.quanzhi.audit_core.extract.service.IExtractService;
import com.quanzhi.auditapiv2.common.dal.dto.query.NacosFilterDto;
import com.quanzhi.auditapiv2.common.dal.entity.BasicConfig;
import com.quanzhi.auditapiv2.common.dal.entity.NotifyConfig;
import com.quanzhi.auditapiv2.common.dal.entity.OriginIpResolve;
import com.quanzhi.auditapiv2.common.dal.entity.SysVersion;
import com.quanzhi.auditapiv2.common.util.entity.EmailConfig;
import com.quanzhi.auditapiv2.common.util.utils.DataUtil;
import com.quanzhi.auditapiv2.common.util.utils.MD5Util;
import com.quanzhi.auditapiv2.common.util.utils.ReflectUtils;
import com.quanzhi.auditapiv2.common.util.utils.ServiceException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.lang.reflect.Field;
import java.util.*;
import java.util.concurrent.Executor;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

@Slf4j
@Deprecated
@Component
public class NacosUtils<T> {

    private Map<String, NacosBaseConfig> nacosConfigMap = new HashMap<String, NacosBaseConfig>() {
        {
            put("dataLabel", new NacosBaseConfig("common.datalabel.json", "common", DataLabel.class, "JSON"));
            put("apiClassification", new NacosBaseConfig("discover.http.classification.json", "discover", ApiClassification.class, "JSON"));
            put("recommendPolicy", new NacosBaseConfig("discover.recommend.rules.json", "discover", RecommendPolicy.class, "JSON"));
            put("networkSegment", new NacosBaseConfig("common.networksegment.json", "common", NetworkSegment.class, "JSON"));
            put("featureLabel", new NacosBaseConfig("common.featurelabel.json", "common", FeatureLabel.class, "JSON"));
            put("emailFeatureLabel", new NacosBaseConfig("common.emailFeaturelabel.json", "common", EmailFeatureLabel.class, "JSON"));
            put("ftpFeatureLabel", new NacosBaseConfig("common.ftpFeaturelabel.json", "common", FtpFeatureLabel.class, "JSON"));
            put("gatewayEventBlackWhiteList", new NacosBaseConfig("common.gateway_blackwhitelist.json", "common", GatewayEventBlackWhiteList.class, "JSON"));
            put("handlerEventBlackWhiteList", new NacosBaseConfig("common.handler_blackwhitelist.json", "common", HandlerEventBlackWhiteList.class, "JSON"));
            put("eventFilterPlugin", new NacosBaseConfig("common.eventfilterplugin.json", "common", EventFilterPlugin.class, "JSON"));
            put("originIpResolve", new NacosBaseConfig("handler.originIpResolve.json", "handler", OriginIpResolve.class, "JSON"));
            put("emailConfig", new NacosBaseConfig("auditapiv2.emailConfig.json", "auditapiv2", EmailConfig.class, "JSON"));
            put("notifyConfig", new NacosBaseConfig("auditapiv2.notifyConfig.json", "auditapiv2", NotifyConfig.class, "JSON"));
            put("basicConfig", new NacosBaseConfig("auditapiv2.basicConfig.json", "auditapiv2", BasicConfig.class, "JSON"));
            put("ipPositionConfig", new NacosBaseConfig("common.ipposition.json", "common", IpPositionConfig.class, "JSON"));
            put("weaknessRule", new NacosBaseConfig("discover.weakness.rules.json", "discover", WeaknessRule.class, "JSON"));
            put("sysVersion", new NacosBaseConfig("auditapiv2.sysVersion.json", "auditapiv2", SysVersion.class, "JSON"));

            put("appClassification", new NacosBaseConfig("discover.http.appClassification.json", "discover", AppClassification.class, "JSON"));
            put("appFeatureLabel", new NacosBaseConfig("common.appFeaturelabel.json", "common", AppFeatureLabel.class, "JSON"));
        }
    };

    @Autowired
    private IExtractService extractService;

    private static Gson gson = new Gson();

    private Map<String, List<T>> nacosConfigContentMap = new HashMap<>();

    @NacosInjected
    private ConfigService configService;


    /**
     * 获取 properties类型的nacos配置
     *
     * @param property
     * @param dataId
     * @param group
     * @return
     * @throws NacosException
     */
    public String getPropertyValue(String property, String dataId, String group) throws NacosException {

        try {
            String value = "";

            String content = configService.getConfig(dataId, group, 5000);

            String pattern = "(^|\\n|\\r)" + property + "=([^\\n\\r]*)(\\n|\\r|$)";

            Pattern r = Pattern.compile(pattern);

            Matcher m = r.matcher(content);

            if (m.find()) {
                value = m.group(2);
            }
            return value;
        } catch (Exception e) {
            return "";
        }
    }

    /**
     * properties类型的nacos配置发布
     *
     * @param property
     * @param newValue
     * @param dataId
     * @param group
     * @throws NacosException
     */
    public void publishPropertyValue(String property, String newValue, String dataId, String group) throws NacosException {
        try {

            String newContent = null;
            String content = configService.getConfig(dataId, group, 5000);

            if (content.indexOf(property) != -1) {

                String pattern = "(^|\\n|\\r)" + property + "=([^\\n\\r]*)(\\n|\\r|$)";

                newContent = content.replaceFirst(pattern, "\n" + property + "=" + newValue + "\n");
            } else {
                newContent = content + "\n\r" + property + "=" + newValue + "\n\r";
            }
            configService.publishConfig(dataId, group, newContent);
        } catch (Exception e) {

        }
    }

    public void initNacosConfig() throws NacosException {
        for (Map.Entry<String, NacosBaseConfig> entry : nacosConfigMap.entrySet()) {
            try {
                NacosBaseConfig nacosBaseConfig = entry.getValue();

                String content = configService.getConfig(nacosBaseConfig.dataId, nacosBaseConfig.group, 5000);

                nacosConfigContentMap.put(entry.getKey(), JSONArray.parseArray(content, nacosBaseConfig.clazz));

                configService.addListener(nacosBaseConfig.dataId, nacosBaseConfig.group, new Listener() {
                    @Override
                    public void receiveConfigInfo(String configInfo) {
                        nacosConfigContentMap.put(entry.getKey(), JSONArray.parseArray(configInfo, nacosBaseConfig.clazz));
                    }

                    @Override
                    public Executor getExecutor() {
                        return null;
                    }
                });
            } catch (Exception e) {
                log.error("init nacos config {} error", entry.getKey(), e);
            }
        }
    }

    /**
     * 增删改这三个操作对整个数组进行操作后再把整个数组序列化后push上去
     */

    public void publishNacosConfig(String collectionName) throws NacosException {

        NacosBaseConfig nacosBaseConfig = nacosConfigMap.get(collectionName);

        String content = gson.toJson(nacosConfigContentMap.get(collectionName));

        configService.publishConfig(nacosBaseConfig.dataId, nacosBaseConfig.group, content);
    }

    /**
     * 根据具体Id获取配置信息
     *
     * @param id
     * @param collectionName
     * @return
     */
    public T findOne(String id, String collectionName) {

        try {

            ArrayList<T> contentArr = new ArrayList<T>(checkDataResourceExit(collectionName));
            for (int i = 0; i < contentArr.size(); i++) {

                T modelInfo = contentArr.get(i);
                String infoId = (String) ReflectUtils.getValue(modelInfo, "id");
                if (id.equals(infoId)) {

                    return (T) JSON.parseObject(JSON.toJSONString(modelInfo), nacosConfigMap.get(collectionName).clazz);

                }
            }
            throw new ServiceException("列表中无此id信息");

        } catch (Exception e) {
            throw new ServiceException("列表中无此id信息");
        }
    }

    /**
     * 分页查询
     *
     * @param page
     * @param limit
     * @param collectionName
     * @return
     */
    public List<T> page(Integer page, Integer limit, String collectionName) {
        try {

            ArrayList<T> contentArr = new ArrayList<T>(checkDataResourceExit(collectionName));
            Integer start = (page - 1) * limit;
            if (start > contentArr.size()) {
                throw new ServiceException("超出列表大小");
            } else {

                List<T> resultArr = contentArr.subList(start, (start + limit) > contentArr.size() ? contentArr.size() : (start + limit));

                return JSONArray.parseArray(JSON.toJSONString(resultArr), nacosConfigMap.get(collectionName).clazz);
            }

        } catch (Exception e) {
            throw new ServiceException("无此列表信息");
        }
    }

    /**
     * nacos中有过滤条件
     *
     * @param page
     * @param limit
     * @param nacosFilterDtos
     * @param collectionName
     * @return
     */
    public List<T> page(Integer page, Integer limit, List<NacosFilterDto> nacosFilterDtos, String collectionName) {
        try {

            ArrayList<T> contentArr = new ArrayList<T>(checkDataResourceExit(collectionName));

            List<T> filterContentArr = getFilterArr(nacosFilterDtos, contentArr);

            Integer start = (page - 1) * limit;
            if (start > filterContentArr.size()) {
                throw new ServiceException("超出列表大小");
            } else {

                List<T> resultArr = filterContentArr.subList(start, (start + limit) > filterContentArr.size() ? filterContentArr.size() : (start + limit));

                return JSONArray.parseArray(JSON.toJSONString(resultArr), nacosConfigMap.get(collectionName).clazz);
            }

        } catch (Exception e) {
            throw new ServiceException("无此列表信息");
        }
    }

    public long getCount(String collectionName) {
        try {

            ArrayList<T> contentArr = new ArrayList<T>(checkDataResourceExit(collectionName));
            return contentArr.size();

        } catch (Exception e) {
            throw new ServiceException("无此列表信息");
        }
    }

    public long getCount(List<NacosFilterDto> nacosFilterDtos, String collectionName) {
        try {

            ArrayList<T> contentArr = new ArrayList<T>(checkDataResourceExit(collectionName));

            List<T> filterContentArr = getFilterArr(nacosFilterDtos, contentArr);

            return filterContentArr.size();

        } catch (Exception e) {
            throw new ServiceException("无此列表信息");
        }
    }

    private List<T> getFilterArr(List<NacosFilterDto> nacosFilterDtos, List<T> contentArr) {

        List<T> filterContentArr = new ArrayList<>();
        if (DataUtil.isNotEmpty(nacosFilterDtos)) {

            for (T content : contentArr) {

                List<Boolean> addToFilterArr = new ArrayList<>();

                nacosFilterDtos.forEach(nacosFilterDto -> {

                    Object filtervalue = ReflectUtils.getValue(content, nacosFilterDto.getKey());

                    try {

                        String filterStr = null;

                        switch (nacosFilterDto.getPredicate()) {

                            case IN:
                                ArrayList<String> filterArr = (ArrayList) filtervalue;

                                if (filterArr.contains(nacosFilterDto.getValue())) {

                                    addToFilterArr.add(true);
                                } else {
                                    addToFilterArr.add(false);
                                }

                                break;
                            case IS:

                                filterStr = filtervalue.toString();

                                if (filterStr.equals(nacosFilterDto.getValue())) {
                                    addToFilterArr.add(true);
                                } else {
                                    addToFilterArr.add(false);
                                }
                                break;

                            case REGEX:

                                filterStr = filtervalue.toString();
                                if (filterStr.indexOf((String) nacosFilterDto.getValue()) != -1) {
                                    addToFilterArr.add(true);
                                } else {
                                    addToFilterArr.add(false);
                                }
                                break;

                            default:
                                addToFilterArr.add(true);

                        }
                    } catch (Exception e) {
                        addToFilterArr.add(true);
                    }

                });

                if (!addToFilterArr.contains(false)) {
                    filterContentArr.add(content);
                }
            }

        } else {
            filterContentArr = contentArr;
        }

        return filterContentArr;
    }

    public List<T> getAll(String collectionName) {
        try {

            ArrayList<T> contentArr = new ArrayList<T>(checkDataResourceExit(collectionName));

            return JSONArray.parseArray(JSON.toJSONString(contentArr), nacosConfigMap.get(collectionName).clazz);

        } catch (Exception e) {

            return new ArrayList<>();
        }
    }

    /**
     * 按照id批量删除
     *
     * @param ids
     * @param collectionName
     * @return
     */
    public Boolean delete(List<String> ids, String collectionName) {

        try {

            ArrayList<T> contentArr = new ArrayList<T>(checkDataResourceExit(collectionName));

            Iterator<T> iterator = contentArr.iterator();

            while (iterator.hasNext()) {

                T modelInfo = iterator.next();
                if (ids.contains((String) ReflectUtils.getValue(modelInfo, "id"))) {
                    iterator.remove();
                }
            }

            nacosConfigContentMap.put(collectionName, contentArr);
            publishNacosConfig(collectionName);

            return true;
        } catch (Exception e) {
            throw new ServiceException("无此列表信息");
        }
    }

    /**
     * 根据model中的id去更新model中其他字段的信息
     *
     * @param model
     * @param collectionName
     * @return
     */
    public Boolean update(T model, String collectionName) {

        try {

            ArrayList<T> contentArr = new ArrayList<T>(checkDataResourceExit(collectionName));

            String modelId = (String) ReflectUtils.getValue(model, "id");
            for (int i = 0; i < contentArr.size(); i++) {

                T contentInfo = contentArr.get(i);

                String contentId = (String) ReflectUtils.getValue(contentInfo, "id");

                if (modelId.equals(contentId)) {

                    Class cls = model.getClass();
                    Field[] fields = cls.getDeclaredFields();

                    for (int j = 0; j < fields.length; j++) {
                        Field f = fields[j];
                        f.setAccessible(true);

                        if (DataUtil.isNotEmpty(f.get(model))) {

                            String methodName = "set" + f.getName().replaceFirst(f.getName().substring(0, 1)
                                    , f.getName().substring(0, 1).toUpperCase());

                            ReflectUtils.executeMethod(contentInfo, methodName, new Class[]{f.get(model).getClass()}, new Object[]{f.get(model)});
                        }
                    }

                    break;
                }
            }

            nacosConfigContentMap.put(collectionName, contentArr);
            publishNacosConfig(collectionName);
            return true;

        } catch (Exception e) {
            throw new ServiceException("无此Id信息");
        }
    }

    /**
     * 批量更新
     *
     * @param models
     * @param collectionName
     * @return
     */
    public Boolean batchUpdate(List<T> models, String collectionName) {

        try {

            ArrayList<T> contentArr = new ArrayList<T>(checkDataResourceExit(collectionName));

            for (int i = 0; i < contentArr.size(); i++) {

                T contentInfo = contentArr.get(i);

                String contentId = (String) ReflectUtils.getValue(contentInfo, "id");

                for (int k = 0; k < models.size(); k++) {

                    T model = models.get(k);
                    String modelId = (String) ReflectUtils.getValue(model, "id");

                    if (modelId.equals(contentId)) {

                        Class cls = model.getClass();
                        Field[] fields = cls.getDeclaredFields();

                        for (int j = 0; j < fields.length; j++) {
                            Field f = fields[j];
                            f.setAccessible(true);

                            if (DataUtil.isNotEmpty(f.get(model))) {

                                String methodName = "set" + f.getName().replaceFirst(f.getName().substring(0, 1)
                                        , f.getName().substring(0, 1).toUpperCase());

                                ReflectUtils.executeMethod(contentInfo, methodName, new Class[]{f.get(model).getClass()}, new Object[]{f.get(model)});
                            }
                        }

                        break;
                    }
                }


            }

            nacosConfigContentMap.put(collectionName, contentArr);
            publishNacosConfig(collectionName);
            return true;

        } catch (Exception e) {
            throw new ServiceException("无此Id信息");
        }
    }

    /**
     * 保存配置 如果入参model中有id，就更新当条记录，没有就生成新的记录
     *
     * @param model
     * @param collectionName
     * @return
     */
    public T save(T model, String collectionName) {

        try {
            ArrayList<T> contentArr = new ArrayList<T>(checkDataResourceExit(collectionName));

            String modelId = (String) ReflectUtils.getValue(model, "id");

            if (DataUtil.isNotEmpty(modelId)) {

                for (int i = 0; i < contentArr.size(); i++) {

                    T contentInfo = contentArr.get(i);

                    String contentId = (String) ReflectUtils.getValue(contentInfo, "id");

                    if (modelId.equals(contentId)) {

                        contentArr.set(i, model);
                        break;
                    }
                }
            } else {
                String id = MD5Util.md5(DataUtil.getRandomStr(32));
                ReflectUtils.executeMethod(model, "setId", new Class[]{String.class}, new Object[]{id});

                contentArr.add(model);
            }

            nacosConfigContentMap.put(collectionName, contentArr);
            publishNacosConfig(collectionName);

            return model;

        } catch (Exception e) {

            throw new ServiceException("保存失败");
        }
    }

    /**
     * 保存单个配置所有信息，一般用于覆盖上传这种
     *
     * @param models
     * @param collectionName
     */
    public void saveAll(List<T> models, String collectionName) {
        try {

            nacosConfigContentMap.put(collectionName, models);
            publishNacosConfig(collectionName);

        } catch (Exception e) {
            throw new ServiceException("无此列表信息");
        }
    }

    /**
     * 获取修改的数据源
     */
    public List<T> checkDataResourceExit(String collectionName) {
        try {
            if (nacosConfigContentMap.isEmpty()) {
                initNacosConfig();
            }
            if (nacosConfigContentMap.containsKey(collectionName) && DataUtil.isNotEmpty(nacosConfigContentMap.get(collectionName))) {

                return nacosConfigContentMap.get(collectionName);
            } else {

                NacosBaseConfig nacosBaseConfig = nacosConfigMap.get(collectionName);

                String content = configService.getConfig(nacosBaseConfig.dataId, nacosBaseConfig.group, 5000);

                if (DataUtil.isNotEmpty(content)) {
                    nacosConfigContentMap.put(
                            collectionName,
                            JSONArray.parseArray(content, nacosBaseConfig.clazz)
                    );
                } else {
                    nacosConfigContentMap.put(
                            collectionName,
                            new ArrayList<>()
                    );
                }

                return nacosConfigContentMap.get(collectionName);
            }
        } catch (Exception e) {
            throw new ServiceException("无此列表信息");
        }
    }


    public class NacosBaseConfig<T> {

        private Class<T> clazz;

        /**
         * nacos的dataId
         */
        private String dataId;

        /**
         * nacos的group
         */
        private String group;

        /**
         * @see NacosContentTypeEnum
         */
        private String contentType;


        NacosBaseConfig(String dataId, String group, Class clazz, String contentType) {
            this.dataId = dataId;
            this.group = group;
            this.clazz = clazz;
            this.contentType = contentType;
        }

    }

    public enum NacosContentTypeEnum {

        TEXT, JSON, XML, YAML, HTML, Properties

    }


}
