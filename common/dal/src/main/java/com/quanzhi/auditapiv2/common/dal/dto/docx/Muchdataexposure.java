package com.quanzhi.auditapiv2.common.dal.dto.docx;

import lombok.Data;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 *
 * 《单次数据量过大》
 *
 *
 * @Project:
 * @Module ID:
 * @Comments:
 * @JDK version used:      <JDK1.8>
 * <AUTHOR> [<EMAIL>]
 * @since 2020-08-28-下午16:52:18
 */
@Data
public class Muchdataexposure extends ApiWeaknessExportWord {

    @Mapper
    public interface MuchdataexposureMapper {

        Muchdataexposure.MuchdataexposureMapper INSTANCE = Mappers.getMapper(Muchdataexposure.MuchdataexposureMapper.class);
        Muchdataexposure convert(ApiWeaknessExportWord apiWeaknessExportWord);
    }
}
