package com.quanzhi.auditapiv2.common.dal.dao.impl;

import com.mongodb.client.result.DeleteResult;
import com.quanzhi.audit_core.common.model.ApiAccountInfo;
import com.quanzhi.audit_core.common.utils.DataUtil;
import com.quanzhi.auditapiv2.common.dal.dao.IApiAccountInfoDao;
import com.quanzhi.auditapiv2.common.dal.dto.query.ApiAccountInfoCriteriaDto;
import com.quanzhi.auditapiv2.common.dal.mongoCollectionIndex.document.AccountInfoDocument;
import com.quanzhi.auditapiv2.common.dal.mongoCollectionIndex.document.AccountMonthDocument;
import com.quanzhi.auditapiv2.common.dal.mongoCollectionIndex.document.IPMonthDocument;
import com.quanzhi.auditapiv2.common.dal.mongoCollectionIndex.document.IpInfoDocument;
import com.quanzhi.auditapiv2.common.util.utils.ConstantUtil;
import com.quanzhi.metabase.common.utils.DateUtil;
import com.quanzhi.metabase.core.model.http.HttpResourceConstant;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.index.IndexDefinition;
import org.springframework.data.mongodb.core.index.IndexOperations;
import org.springframework.data.mongodb.core.index.IndexResolver;
import org.springframework.data.mongodb.core.index.MongoPersistentEntityIndexResolver;
import org.springframework.data.mongodb.core.mapping.MongoMappingContext;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * @ClassName ApiAccountInfoDaoImpl
 * @Description TODO
 * <AUTHOR>
 * @Date 2020/11/22 23:37
 **/
@Repository
@Slf4j
public class ApiAccountInfoDaoImpl implements IApiAccountInfoDao {

    @Autowired
    private MongoTemplate mongoTemplate;
    @Autowired
    private MongoMappingContext mongoMappingContext;

    @Override
    public List<ApiAccountInfo> getApiAccountInfoList(ApiAccountInfoCriteriaDto apiAccountInfoCriteriaDto) {
        Query query=new Query();

        //查询条件
        if(DataUtil.isNotEmpty(apiAccountInfoCriteriaDto.getAccount())){
            query.addCriteria(Criteria.where("account").regex(apiAccountInfoCriteriaDto.getAccount()));
        }
        if(DataUtil.isNotEmpty(apiAccountInfoCriteriaDto.getThreatLabels())){
            query.addCriteria(Criteria.where("threatLabels").in(apiAccountInfoCriteriaDto.getThreatLabels()));
        }
        if(DataUtil.isNotEmpty(apiAccountInfoCriteriaDto.getStartTime()) && DataUtil.isNotEmpty(apiAccountInfoCriteriaDto.getEndTime())){
            query.addCriteria(new Criteria().andOperator(
                    Criteria.where("firstDate").gte(apiAccountInfoCriteriaDto.getStartTime()),
                    Criteria.where("firstDate").lte(apiAccountInfoCriteriaDto.getEndTime())));
        }
        if(DataUtil.isNotEmpty(apiAccountInfoCriteriaDto.getStartDate()) && DataUtil.isNotEmpty(apiAccountInfoCriteriaDto.getEndDate())){
            query.addCriteria(new Criteria().andOperator(
                    Criteria.where("lastDate").gte(apiAccountInfoCriteriaDto.getStartDate()),
                    Criteria.where("lastDate").lte(apiAccountInfoCriteriaDto.getEndDate())));
        }
        //排序
        if(DataUtil.isNotEmpty(apiAccountInfoCriteriaDto.getField()) && DataUtil.isNotEmpty(apiAccountInfoCriteriaDto.getSort())){
            if(apiAccountInfoCriteriaDto.getSort() == ConstantUtil.Sort.ASC){
                query.with(Sort.by(Sort.Order.asc(apiAccountInfoCriteriaDto.getField())));
            } else if(apiAccountInfoCriteriaDto.getSort() == ConstantUtil.Sort.DESC){
                query.with(Sort.by(Sort.Order.desc(apiAccountInfoCriteriaDto.getField())));
            } else{
                query.with(Sort.by(Sort.Order.desc(apiAccountInfoCriteriaDto.getField())));
            }
        }
        //分页
        query.limit(apiAccountInfoCriteriaDto.getLimit());
        query.skip((apiAccountInfoCriteriaDto.getPage()-1)*apiAccountInfoCriteriaDto.getLimit());
        return mongoTemplate.find(query, ApiAccountInfo.class);
    }

    @Override
    public ApiAccountInfo getApiAccountInfo(String account) {
        return mongoTemplate.findOne(new Query().addCriteria(Criteria.where("account").is(account)),ApiAccountInfo.class);
    }

    @Override
    public Long getApiAccountInfoCount(ApiAccountInfoCriteriaDto apiAccountInfoCriteriaDto) {
        Query query=new Query();

        //查询条件
        if(DataUtil.isNotEmpty(apiAccountInfoCriteriaDto.getAccount())){
            query.addCriteria(Criteria.where("account").regex(apiAccountInfoCriteriaDto.getAccount()));
        }
        if(DataUtil.isNotEmpty(apiAccountInfoCriteriaDto.getThreatLabels())){
            query.addCriteria(Criteria.where("threatLabels").in(apiAccountInfoCriteriaDto.getThreatLabels()));
        }
        if(DataUtil.isNotEmpty(apiAccountInfoCriteriaDto.getStartTime()) && DataUtil.isNotEmpty(apiAccountInfoCriteriaDto.getEndTime())){
            query.addCriteria(new Criteria().andOperator(
                    Criteria.where("firstDate").gte(apiAccountInfoCriteriaDto.getStartTime()),
                    Criteria.where("firstDate").lte(apiAccountInfoCriteriaDto.getEndTime())));
        }
        if(DataUtil.isNotEmpty(apiAccountInfoCriteriaDto.getStartDate()) && DataUtil.isNotEmpty(apiAccountInfoCriteriaDto.getEndDate())){
            query.addCriteria(new Criteria().andOperator(
                    Criteria.where("lastDate").gte(apiAccountInfoCriteriaDto.getStartDate()),
                    Criteria.where("lastDate").lte(apiAccountInfoCriteriaDto.getEndDate())));
        }
        return mongoTemplate.count(query,ApiAccountInfo.class);
    }

    @Override
    public void remove(long start, long end) {
        String formatDate = DateUtil.format(start, DateUtil.DATE_PATTERN.YYYYMMDD);
        String endDate = DateUtil.format(end, DateUtil.DATE_PATTERN.YYYYMMDD);
        String today = DateUtil.currentDate();
        if (today.equals(endDate)) {
            long count = mongoTemplate.count(new Query(), HttpResourceConstant.ACCOUNT_INFO);
            mongoTemplate.dropCollection(HttpResourceConstant.ACCOUNT_INFO);
            mongoTemplate.dropCollection( HttpResourceConstant.ACCOUNT_DATE_INFO);
            mongoTemplate.dropCollection( "accountMonthInfo");
            log.info("account delete all: {}", count);
            // 重新创建索引
            createIndex(AccountInfoDocument.class);
            createIndex(AccountMonthDocument.class);
        }else {
            Query query = new Query();
            query.addCriteria(Criteria.where("firstDate").gte(formatDate).lte(endDate));
            DeleteResult remove = mongoTemplate.remove(query, HttpResourceConstant.ACCOUNT_INFO);
            log.info("accountInfo delete size: {}", remove.getDeletedCount());


            Query query2 = new Query();
            query2.addCriteria(Criteria.where("timestamp").gte(start).lte(end));
            DeleteResult remove2 = mongoTemplate.remove(query2, HttpResourceConstant.ACCOUNT_DATE_INFO);
            log.info("accountDateInfo delete size: {}", remove2.getDeletedCount());

            Query query3 = new Query();
            query3.addCriteria(Criteria.where("updateTime").gte(start).lt(end));
            DeleteResult remove3 = mongoTemplate.remove(query3, HttpResourceConstant.DEPART);
            log.info("depart delete size: {}", remove3.getDeletedCount());

            Query query4 = new Query();
            query4.addCriteria(Criteria.where("updateTime").gte(start).lte(end));
            DeleteResult remove4 = mongoTemplate.remove(query4, "accountMonthInfo");
            log.info("accountDateInfo delete size: {}", remove4.getDeletedCount());
        }
    }

    private void createIndex(Class<?> clz) {
        IndexOperations indexOps = mongoTemplate.indexOps(clz);
        IndexResolver resolver = new MongoPersistentEntityIndexResolver(mongoMappingContext);
        for (IndexDefinition next : resolver.resolveIndexFor(clz)) {
            try {
                indexOps.ensureIndex(next);
            } catch (Exception e) {
                log.error("ensure {} index {} error", clz.getName(), next.getIndexKeys(), e);
            }
        }
    }
}
