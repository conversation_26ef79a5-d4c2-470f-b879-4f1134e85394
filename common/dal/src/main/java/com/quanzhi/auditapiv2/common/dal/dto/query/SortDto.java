package com.quanzhi.auditapiv2.common.dal.dto.query;

import com.quanzhi.auditapiv2.common.util.utils.DataUtil;
import com.quanzhi.metabase.core.model.query.Sort;
import com.quanzhi.metabase.core.model.query.SortOrder;
import lombok.Data;

import java.util.List;

/**
 * @author: zhousong
 * @data: 2018/11/1
 * 排序DTO
 */
@Data
public class SortDto {

    /**
     * 需要排序的字段名称
     */
    private String field;

    /**
     * 排序: 1 - 升序，2 - 降序
     */
    private Integer sort;

    public static Sort[] toSortForMetabaseQuery(List<SortDto> sortDtos){
        Sort[] sorts = null;
        if (DataUtil.isNotEmpty(sortDtos)){
            sorts = new Sort[sortDtos.size()];
            for (int n=0; n<sortDtos.size(); n++) {
                SortDto sortDto = sortDtos.get(0);
                if (sortDto.sort.intValue() == SortInt.ASC) {
                    sorts[n] = Sort.by(sortDto.getField(), SortOrder.ASC);
                } else {
                    sorts[n] = Sort.by(sortDto.getField(), SortOrder.DESC);
                }
            }
        }

        return sorts;
    }

    public static class SortInt {
        /**
         * 升序
         */
        public static final int ASC = 1;

        /**
         * 降序
         */
        public static final int DESC = 2;
    }
}
