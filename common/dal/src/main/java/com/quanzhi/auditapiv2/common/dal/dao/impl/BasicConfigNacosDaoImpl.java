package com.quanzhi.auditapiv2.common.dal.dao.impl;

import com.alibaba.fastjson.JSONObject;
import com.quanzhi.auditapiv2.common.dal.dao.IBasicConfigNacosDao;
import com.quanzhi.auditapiv2.common.dal.dao.base.impl.NacosBaseDaoImpl;
import com.quanzhi.auditapiv2.common.dal.dto.query.Predicate;
import com.quanzhi.auditapiv2.common.dal.dto.query.QueryNacos;
import com.quanzhi.auditapiv2.common.dal.entity.BasicConfig;
import com.quanzhi.auditapiv2.common.dal.entity.VersionConfigModel;
import com.quanzhi.auditapiv2.common.util.constant.DataConstant;
import com.quanzhi.auditapiv2.common.util.utils.DataUtil;
import com.quanzhi.auditapiv2.common.util.utils.MD5Util;
import org.springframework.stereotype.Repository;

/**
 * 
 * 《基础配置持久层Nacos接口实现》
 * 
 * 
 * @Project: 
 * @Module ID:
 * @Comments: 
 * @JDK version used:      <JDK1.8> 
 * <AUTHOR> [<EMAIL>]
 * @since 2020-04-08-上午9:56:10
 */
@Repository
public class BasicConfigNacosDaoImpl extends NacosBaseDaoImpl<BasicConfig> implements IBasicConfigNacosDao {
    private final static String NAME = "versionConfig";

    @Override
    public VersionConfigModel getVersionConfig() {
        return this.getVersionByName(NAME);
    }

    @Override
    public void saveVersionConfig(VersionConfigModel versionConfigModel) throws Exception {
        if (DataUtil.isEmpty(versionConfigModel)){
            return;
        }

        BasicConfig basicConfig = this.getBasicConfigByName(NAME);
        if (DataUtil.isEmpty(basicConfig)){
            String id =  MD5Util.md5(DataUtil.getRandomStr(32) + System.currentTimeMillis());

            basicConfig = new BasicConfig();
            basicConfig.setUpdateTime(System.currentTimeMillis());
            basicConfig.setId(id);
            basicConfig.setType(BasicConfig.TypeEnum.COMMON.value());
            basicConfig.setName(NAME);
        } else {
            basicConfig.setUpdateTime(System.currentTimeMillis());
        }

        basicConfig.setValueObj(versionConfigModel);

        super.save(basicConfig);
    }

    @Override
    public VersionConfigModel getVersionByName(String name) {
        QueryNacos queryNacos = new QueryNacos();
        queryNacos.where(DataConstant.COMMON_FIELD_NAME, Predicate.IS, name);

        BasicConfig sysConfig = super.findOne(queryNacos);
        if (DataUtil.isEmpty(sysConfig)) {
            return null;
        }

        Object valueObj = sysConfig.getValueObj();

        VersionConfigModel versionConfigModel = null;
        if (DataUtil.isNotEmpty(valueObj)){
            versionConfigModel = JSONObject.parseObject(JSONObject.toJSONString(valueObj), VersionConfigModel.class);
        }

        return versionConfigModel;
    }

    /**
     * 根据名称获取配置信息
     * @param name
     * @return
     */
    private BasicConfig getBasicConfigByName(String name){
        QueryNacos queryNacos = new QueryNacos();
        queryNacos.where(DataConstant.COMMON_FIELD_NAME, Predicate.IS, name);

        return super.findOne(queryNacos);
    }
}
