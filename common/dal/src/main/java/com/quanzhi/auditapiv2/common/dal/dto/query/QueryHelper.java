package com.quanzhi.auditapiv2.common.dal.dto.query;

import com.quanzhi.auditapiv2.common.util.utils.DataUtil;
import com.quanzhi.auditapiv2.common.util.utils.ServiceException;
import com.quanzhi.metabase.core.model.query.MetabaseQuery;
import com.quanzhi.metabase.core.model.query.ResourceUpdates;
import com.quanzhi.metabase.core.model.query.Sort;
import org.bson.types.ObjectId;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * 查询相关的帮助方法
 *
 * <AUTHOR>
 * @date 2020-02-26
 * @time 15:25
 */
public class QueryHelper {
    /**
     * 将给定的MetabaseQuery查询对象转换为MongoDB的查询语句
     *
     * @param metabaseQuery
     * @return
     */
    public static Query toQuery(MetabaseQuery metabaseQuery) throws ServiceException {
        Criteria criteria = getCriteria(metabaseQuery);
        Query query = Query.query(criteria);
        if (DataUtil.isNotEmpty(metabaseQuery.getSort())) {
            org.springframework.data.domain.Sort.Order[] mongoOrders = new org.springframework.data.domain.Sort.Order[metabaseQuery.getSort().length];
            for (int i = 0; i < metabaseQuery.getSort().length; i++) {
                Sort sort = metabaseQuery.getSort()[i];
                switch (sort.getOrder()) {
                    case NONE:
                    case ASC:
                        mongoOrders[i] = new org.springframework.data.domain.Sort.Order(org.springframework.data.domain.Sort.Direction.ASC,
                                sort.getProperty());
                        break;
                    case DESC:
                        mongoOrders[i] = new org.springframework.data.domain.Sort.Order(org.springframework.data.domain.Sort.Direction.DESC,
                                sort.getProperty());
                        break;
                    default:
                        break;
                }
            }
            query.with(org.springframework.data.domain.Sort.by(mongoOrders));
        }
        if (metabaseQuery.getSkip() != null && metabaseQuery.getSkip() > 0) {
            query.skip(metabaseQuery.getSkip());
        }
        if (metabaseQuery.getLimit() != null && metabaseQuery.getLimit() > 0) {
            query.limit(metabaseQuery.getLimit());
        }
        return query;
    }

    public static Criteria getCriteria(MetabaseQuery metabaseQuery) {
        Criteria criteria = new Criteria();
        if (metabaseQuery == null){
            return criteria;
        }
        for (com.quanzhi.metabase.core.model.query.Criteria criteriaExpr : metabaseQuery.getCriteria()) {
            criteria = getCriteria(criteria, criteriaExpr);
        }
        return criteria;
    }

    private static Criteria getCriteria(Criteria criteria, com.quanzhi.metabase.core.model.query.Criteria criteriaExpr) {
        if (criteriaExpr.getPredicate() != null) {
            String property = criteriaExpr.getProperty();
            if (criteria == null) {
                criteria = Criteria.where(criteriaExpr.getProperty());
            } else {
                criteria = criteria.and(property);
            }
            switch (criteriaExpr.getPredicate()) {
                case IS:
                    criteria.is(criteriaExpr.getValue());
                    break;
                case GT:
                    replaceId(criteriaExpr);
                    criteria.gt(criteriaExpr.getValue());
                    break;
                case GTE:
                    replaceId(criteriaExpr);
                    criteria.gte(criteriaExpr.getValue());
                    break;
                case LT:
                    replaceId(criteriaExpr);
                    criteria.lt(criteriaExpr.getValue());
                    break;
                case LTE:
                    replaceId(criteriaExpr);
                    criteria.lte(criteriaExpr.getValue());
                    break;
                case BETWEEN:
                    List<Object> betweenValues = (ArrayList) criteriaExpr.getValue();
                    Object start = betweenValues.get(0);
                    Object end = betweenValues.get(1);
                    criteria.gt(start).lte(end);
                    break;
                case IN:
                    if (criteriaExpr.getValue() instanceof Collection) {
                        criteria.in((Collection) criteriaExpr.getValue());
                    } else {
                        criteria.in(criteriaExpr.getValue());
                    }
                    break;
                case NE:
                    criteria.ne(criteriaExpr.getValue());
                    break;
                case LIKE:
                case REGEX:
                    criteria.regex(criteriaExpr.getValue().toString());
                    break;
                case EXISTS:
                    criteria.exists(Boolean.valueOf(criteriaExpr.toString()));
                    break;
                case ALL:
                    if (criteriaExpr.getValue() instanceof Collection) {
                        criteria.all((Collection) criteriaExpr.getValue());
                    } else {
                        criteria.all(criteriaExpr.getValue());
                    }
                    break;
                case NIN:
                    if (criteriaExpr.getValue() instanceof Collection) {
                        criteria.nin((Collection) criteriaExpr.getValue());
                    } else {
                        criteria.nin(criteriaExpr.getValue());
                    }
                    break;
                case ELEM_MATCH:
                    if (!CollectionUtils.isEmpty(criteriaExpr.getCriteriaChain())) {
                        Criteria newCriteria = null;
                        for (com.quanzhi.metabase.core.model.query.Criteria expr
                                : criteriaExpr.getCriteriaChain()) {
                            newCriteria = getCriteria(newCriteria, expr);
                        }
                        criteria = criteria.where(criteriaExpr.getProperty()).elemMatch(newCriteria);
                    }
                    break;
                default:
                    throw new IllegalArgumentException("not supported predicate: " + criteriaExpr.getPredicate());
            }
        } else if (criteriaExpr.getOperator() != null
                && !CollectionUtils.isEmpty(criteriaExpr.getCriteriaChain())) {
            List<Criteria> criteriaList = new ArrayList<>();
            for (com.quanzhi.metabase.core.model.query.Criteria c : criteriaExpr.getCriteriaChain()) {
                criteriaList.add(getCriteria(new Criteria(), c));
            }
            Criteria complexCriteria = new Criteria();
            switch (criteriaExpr.getOperator()) {
                case AND:
                    complexCriteria.andOperator(criteriaList.toArray(new Criteria[criteriaList.size()]));
                    break;
                case OR:
                    complexCriteria.orOperator(criteriaList.toArray(new Criteria[criteriaList.size()]));
                    break;
                case NOR:
                    complexCriteria.norOperator(criteriaList.toArray(new Criteria[criteriaList.size()]));
                    break;
            }
            if (criteria == null) {
                criteria = complexCriteria;
            } else {
                criteria = new Criteria().andOperator(criteria, complexCriteria);
            }
        }
        return criteria;
    }




    /**
     * 将string替换为ObjectId
     * 通常在通过_id比大小的时候进行替换
     *
     * @param criteriaExpr
     */
    private static void replaceId(com.quanzhi.metabase.core.model.query.Criteria criteriaExpr) {
        if ("_id".equals(criteriaExpr.getProperty())) {
            criteriaExpr.setValue(new ObjectId(criteriaExpr.getValue().toString()));
        }
    }

    /**
     * 将资源更新字段转换为MongoDB的Update对象
     *
     * @param map
     * @return
     */
    public static Update toUpdate(Map<String, Object> map) {
        return toUpdate(map, true);
    }

    /**
     * 将资源更新查询对象转换为MongoDB的Update对象
     *
     * @param resourceUpdates
     * @return
     */
    public static Update toUpdate(ResourceUpdates resourceUpdates) {
        return toUpdate(resourceUpdates.getSetMap(), false);
    }

    /**
     * 将资源更新字段转换为MongoDB的Update对象
     */
    public static Update toUpdate(Map<String, Object> map, boolean ignoreNull) {
        Update update = new Update();
        for (Map.Entry<String, Object> entry : map.entrySet()) {
            if (entry.getValue() == null && ignoreNull) {
                continue;
            }
            update.set(entry.getKey(), entry.getValue());
        }
        return update;
    }
}
