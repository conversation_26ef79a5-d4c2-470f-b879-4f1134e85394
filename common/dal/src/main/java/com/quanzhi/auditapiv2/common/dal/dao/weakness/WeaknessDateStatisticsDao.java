package com.quanzhi.auditapiv2.common.dal.dao.weakness;

import com.quanzhi.auditapiv2.common.dal.entity.weakness.WeaknessDateStatistics;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/8/31 4:56 下午
 */
public interface WeaknessDateStatisticsDao {

    WeaknessDateStatistics getOne(String date);

    void save(WeaknessDateStatistics weaknessDateStatistics);

    List<WeaknessDateStatistics> list(String startDate, String endDate);

    WeaknessDateStatistics countByWeakness(Date date);

    long count();

}
