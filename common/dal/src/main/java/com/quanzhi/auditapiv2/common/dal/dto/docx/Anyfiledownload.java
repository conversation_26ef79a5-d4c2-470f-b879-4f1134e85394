package com.quanzhi.auditapiv2.common.dal.dto.docx;

import lombok.Data;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 *
 * 《指定文件下载接口》
 *
 *
 * @Project:
 * @Module ID:
 * @Comments:
 * @JDK version used:      <JDK1.8>
 * <AUTHOR> [<EMAIL>]
 * @since 2020-08-28-下午16:52:18
 */
@Data
public class Anyfiledownload extends ApiWeaknessExportWord {

    @Mapper
    public interface AnyfiledownloadMapper {

        Anyfiledownload.AnyfiledownloadMapper INSTANCE = Mappers.getMapper(Anyfiledownload.AnyfiledownloadMapper.class);
        Anyfiledownload convert(ApiWeaknessExportWord apiWeaknessExportWord);
    }
}
