package com.quanzhi.auditapiv2.common.dal.dao.impl;

import com.quanzhi.auditapiv2.common.dal.dao.IGatewaySSLPemDao;
import com.quanzhi.auditapiv2.common.dal.dao.base.impl.BaseDaoImpl;
import com.quanzhi.auditapiv2.common.dal.entity.GatewaySSLPem;
import com.quanzhi.auditapiv2.common.util.utils.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 * @description 加密证书相关dao
 * @date 2023/8/25 10:45
 */
@Repository("GatewaySSLPemDao")
public class GatewaySSLPemDaoImpl extends BaseDaoImpl<GatewaySSLPem> implements IGatewaySSLPemDao {

    @Autowired
    MongoTemplate mongoTemplate;

    private static String collectionName = "gatewaySSLPem";

    @Override
    public GatewaySSLPem findById(String id) {
        return mongoTemplate.findById(id, GatewaySSLPem.class, collectionName);
    }

    @Override
    public List<GatewaySSLPem> selectSSLPemPage(String name, Integer page, Integer limit) throws Exception {
        Sort sort = Sort.by(Sort.Direction.DESC, "createTime");
        Criteria criteria = new Criteria();
        if (!StringUtils.isNullOrEmpty(name)) {
            criteria.and("name").regex(name);
        }
        return mongoTemplate.find(new Query(criteria).skip((page - 1) * limit).limit(limit).with(sort), GatewaySSLPem.class, collectionName);

    }

    @Override
    public Long count(String name) {
        Criteria criteria = new Criteria();
        if (!StringUtils.isNullOrEmpty(name)) {
            criteria.and("name").regex(name);
        }
        return mongoTemplate.count(new Query(criteria), collectionName);
    }

    @Override
    public void deleteById(String id) {
        Criteria criteria = Criteria.where("_id").is(id);
        mongoTemplate.remove(new Query(criteria), collectionName);
    }

    @Override
    public List<GatewaySSLPem> selectSSLPemByIsAll(Boolean isAll) {
        Criteria criteria = Criteria.where("isAll").is(isAll);
        return mongoTemplate.find(new Query(criteria), GatewaySSLPem.class,collectionName);
    }

}
