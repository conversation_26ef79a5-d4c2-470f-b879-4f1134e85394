package com.quanzhi.auditapiv2.common.dal.dto.securityPosture;

import com.quanzhi.metabase.core.model.http.HttpAppResource;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Data;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * @Auther: yangzixian
 * @Date: 2021/10/15 10:19
 * @Description:
 */
@Data
@Builder
@ApiModel("关注App")
public class RiskFlowDto {
    @ApiModelProperty("关注区")
    private List<region> regions;
    @ApiModelProperty("风险分布")
    private List<risk> riskDistribute;

    @Data
    public static final class risk {
        @ApiModelProperty("ip")
        private String ip;
        @ApiModelProperty("区域")
        private String area;
        @ApiModelProperty("风险等级")
        private Integer level;
        @ApiModelProperty("攻击时间")
        private Long attackTime;
        @ApiModelProperty("攻击应用")
        private String attackApp;
        @ApiModelProperty("攻击类型")
        private String attackType;
    }

    @Data
    public static final class region {
        @ApiModelProperty("区名称")
        private String regionName;
        @ApiModelProperty("区内关注应用")
        private List<httpApp> apps;

        @Data
        public static final class httpApp {
            @ApiModelProperty("是否被攻击")
            private boolean isActive = false;
            @ApiModelProperty("应用域名")
            private String host;
            @ApiModelProperty("应用名称")
            private String name;
            @Mapper
            public interface httpAppMapper {
                httpApp.httpAppMapper INSTANCE = Mappers.getMapper(httpApp.httpAppMapper.class);
                httpApp convert(HttpAppResource httpAppResource);
            }
        }
    }
}
