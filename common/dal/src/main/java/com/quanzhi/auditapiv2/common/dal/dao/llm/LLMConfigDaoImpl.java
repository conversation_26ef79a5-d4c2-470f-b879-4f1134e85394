package com.quanzhi.auditapiv2.common.dal.dao.llm;

import com.quanzhi.auditapiv2.common.dal.entity.llm.LLMConfig;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 * create at 2025/4/21 14:26
 * @description:
 **/
@Repository
public class LLMConfigDaoImpl implements ILLMConfigDao {

    private final MongoTemplate mongoTemplate;

    public LLMConfigDaoImpl(MongoTemplate mongoTemplate) {
        this.mongoTemplate = mongoTemplate;
    }


    @Override
    public LLMConfig save(LLMConfig llmConfig) {
        return mongoTemplate.save(llmConfig, "LLMConfig");
    }


    public Long count(Query query) {
        long count = mongoTemplate.count(query, "LLMConfig");
        return count;
    }

    public List<LLMConfig> list(Query query) {
        List<LLMConfig> llmConfigs = mongoTemplate.find(query, LLMConfig.class, "LLMConfig");
        return llmConfigs;
    }


}