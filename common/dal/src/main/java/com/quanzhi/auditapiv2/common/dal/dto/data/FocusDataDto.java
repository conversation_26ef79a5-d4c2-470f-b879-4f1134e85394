package com.quanzhi.auditapiv2.common.dal.dto.data;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * @Author: yangzx
 * @Date: 2024/10/9 11:12
 */
@Data
public class FocusDataDto {

    @ApiModelProperty("id")
    private String id;

    @ApiModelProperty("name")
    private String name;

    @ApiModelProperty("关注数据")
    private List<String> focusData;

}
