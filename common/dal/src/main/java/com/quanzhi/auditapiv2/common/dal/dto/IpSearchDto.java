package com.quanzhi.auditapiv2.common.dal.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @description: ip查询条件
 **/
@Data
@ApiModel
public class IpSearchDto {
    /**
     * 网段
     */
    @ApiModelProperty(value = "网段", name = "accessDomainIds")
    private List<String> accessDomainIds;

    /**
     * 风险等级
     */
    @ApiModelProperty(value = "风险等级", name = "riskLevel")
    private List<Integer> riskLevel;


    /**
     * 分组字段枚举
     */
    public enum GroupFieldEnum {

        accessDomainIds("accessDomainIds"),

        riskLevel("riskLevel");

        String name;

        GroupFieldEnum(String name) {

            this.name = name;
        }

        public String getName() {
            return name;
        }

    }


}