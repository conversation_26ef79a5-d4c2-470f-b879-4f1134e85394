package com.quanzhi.auditapiv2.common.dal.dto.docx;

import lombok.Data;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 *
 * 《登录认证不合理》
 *
 *
 * @Project:
 * @Module ID:
 * @Comments:
 * @JDK version used:      <JDK1.8>
 * <AUTHOR> [<EMAIL>]
 * @since 2020-08-28-下午16:52:18
 */
@Data
public class Unreasonableauth extends ApiWeaknessExportWord {

    @Mapper
    public interface UnreasonableauthMapper {

        Unreasonableauth.UnreasonableauthMapper INSTANCE = Mappers.getMapper(Unreasonableauth.UnreasonableauthMapper.class);
        Unreasonableauth convert(ApiWeaknessExportWord apiWeaknessExportWord);
    }
}
