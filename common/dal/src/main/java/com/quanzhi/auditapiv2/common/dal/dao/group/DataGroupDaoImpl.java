package com.quanzhi.auditapiv2.common.dal.dao.group;

import com.quanzhi.auditapiv2.common.dal.dataobject.DataGroup;
import com.quanzhi.auditapiv2.common.dal.dto.group.DataGroupDto;
import com.quanzhi.auditapiv2.common.dal.dto.group.DataGroupSearchDto;
import com.quanzhi.auditapiv2.common.util.dto.ListOutputDto;
import com.quanzhi.auditapiv2.common.util.utils.ConstantUtil;
import com.quanzhi.auditapiv2.common.util.utils.DataUtil;
import com.quanzhi.metabase.core.model.query.SortOrder;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Repository;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * create at 2024/11/13 14:36
 * @description:
 **/
@Repository
public class DataGroupDaoImpl implements IDataGroupDao {

    private final MongoTemplate mongoTemplate;

    public DataGroupDaoImpl(MongoTemplate mongoTemplate) {
        this.mongoTemplate = mongoTemplate;
    }

    @Override
    public List<DataGroup> find(Query query) {
        List<DataGroup> list = mongoTemplate.find(query, DataGroup.class);
        return list;
    }

    @Override
    public long count(Query query) {
        return mongoTemplate.count(query, DataGroup.class);
    }

    @Override
    public DataGroup save(DataGroup dataGroup) {
        if(dataGroup.getCreateTime()==null){
            dataGroup.setCreateTime(System.currentTimeMillis());
        }
        dataGroup.setUpdateTime(System.currentTimeMillis());
        return mongoTemplate.save(dataGroup);
    }


    @Override
    public List<DataGroup> find(DataGroupSearchDto dataGroupSearchDto) {
        Query query=fillQuery(dataGroupSearchDto);
        return find(query);
    }
    @Override
    public ListOutputDto<DataGroup> page(DataGroupSearchDto dataGroupSearchDto) {
        ListOutputDto outputDto=new ListOutputDto();
        Query query=fillQuery(dataGroupSearchDto);
        long count=count(query);
        outputDto.setTotalCount(count);
        if(count==0){
            outputDto.setRows(new ArrayList());
        }else{
         if(DataUtil.isNotEmpty(dataGroupSearchDto.getPage())&&DataUtil.isNotEmpty(dataGroupSearchDto.getLimit())){
             query.skip((dataGroupSearchDto.getPage()-1)*dataGroupSearchDto.getLimit());
             query.limit(dataGroupSearchDto.getLimit());
         }
         if(DataUtil.isNotEmpty(dataGroupSearchDto.getField())){
             if(DataUtil.isNotEmpty(dataGroupSearchDto.getSort())){
                 if(dataGroupSearchDto.getSort()== ConstantUtil.Sort.ASC){
                     query.with(Sort.by(Sort.Order.asc(dataGroupSearchDto.getField())));
                 }else{
                     query.with(Sort.by(Sort.Order.desc(dataGroupSearchDto.getField())));
                 }
             }
         }
         List<DataGroup> list=find(query);
         outputDto.setRows(list);
        }
        return outputDto;
    }

    private Query fillQuery(DataGroupSearchDto dataGroupSearchDto) {
        Query query=new Query();
        Criteria criteria=Criteria.where("delFlag").is(false);
        if(dataGroupSearchDto!=null){
            if(DataUtil.isNotEmpty(dataGroupSearchDto.getId())){
                criteria.and("id").is(dataGroupSearchDto.getId());
            }
            if(DataUtil.isNotEmpty(dataGroupSearchDto.getIds())){
                criteria.and("id").in(dataGroupSearchDto.getIds());
            }
            if(DataUtil.isNotEmpty(dataGroupSearchDto.getName())){
                criteria.and("name").regex(DataUtil.regexStrEscape(dataGroupSearchDto.getName()));
            }
            if(DataUtil.isNotEmpty(dataGroupSearchDto.getStatus())){
                criteria.and("status").is(dataGroupSearchDto.getStatus());
            }

        }
        query.addCriteria(criteria);
        return query;
    }


}