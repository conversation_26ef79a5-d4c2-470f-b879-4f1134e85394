package com.quanzhi.auditapiv2.common.dal.dao.impl;

import com.quanzhi.auditapiv2.common.dal.dao.IApiLifeCycleDao;
import com.quanzhi.auditapiv2.common.dal.dao.base.impl.BaseDaoImpl;
import com.quanzhi.auditapiv2.common.dal.entity.api.ApiLifeCycle;
import org.springframework.stereotype.Repository;

/**
 * <AUTHOR>
 * create at 2021/8/30 8:42 下午
 * @description:
 **/
@Repository
public class ApiLifeCycleDaoImpl extends BaseDaoImpl<ApiLifeCycle> implements IApiLifeCycleDao {








}