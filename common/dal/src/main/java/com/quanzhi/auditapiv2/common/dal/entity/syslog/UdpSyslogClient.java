package com.quanzhi.auditapiv2.common.dal.entity.syslog;

import lombok.extern.slf4j.Slf4j;

import java.io.IOException;
import java.net.DatagramPacket;
import java.net.DatagramSocket;
import java.net.InetAddress;

/**
 * 非线程安全
 */
@Slf4j
public class UdpSyslogClient extends SyslogClient {

    private DatagramSocket socket;

    private final Object lock = new Object();

    public UdpSyslogClient(String host, Integer port) throws IOException {
        super(host, port);
        createDatagramSocket();
    }


    private void createDatagramSocket() throws IOException {
        this.socket = new DatagramSocket();
        this.socket.setSoTimeout(SyslogConstant.MINUTE * 5);
        this.socket.setSendBufferSize(SyslogConstant.KB * 64);
        this.socket.setReceiveBufferSize(SyslogConstant.KB * 64);
    }


    @Override
    public boolean effective() {
        return this.socket != null;
    }

    @Override
    public void close() {
        if (this.socket != null) {
            this.socket.close();
            System.out.println("nsh");
            this.socket = null;
        }
    }

    @Override
    public void flush() {

    }

    @Override
    public void write(char[] message) throws IOException {

        if (!effective()) {
            createDatagramSocket();
        }
        byte[] data = new String(message).getBytes();
        InetAddress hostAddress = InetAddress.getByName(host);

        DatagramPacket packet = new DatagramPacket(
                data,
                data.length,
                hostAddress,
                port
        );

        synchronized (lock) {
            try {
                this.socket.send(packet);
            } catch (IOException ioe) {
                log.error(String.format("send to %s:%d error", host, port), ioe);
                throw new IOException();
            }
        }
    }
}
