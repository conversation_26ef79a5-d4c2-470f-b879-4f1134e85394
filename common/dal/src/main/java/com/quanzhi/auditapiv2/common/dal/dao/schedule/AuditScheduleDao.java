package com.quanzhi.auditapiv2.common.dal.dao.schedule;

import com.quanzhi.audit.mix.schdule.domain.entity.Schedule;
import com.quanzhi.audit.mix.schdule.domain.entity.TriggerStatus;
import com.quanzhi.auditapiv2.common.dal.entity.schedule.RunInfo;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Map;

/**
 * @author: yangzixian
 * @date: 2023.08.21 10:52
 * @description:
 */
@Repository
public class AuditScheduleDao {

    private final MongoTemplate mongoTemplate;

    private final String COLLECTION_NAME = "schedule";

    private final String COLLECTION_NAME_RUN = "scheduleRunInfo";

    public AuditScheduleDao(MongoTemplate mongoTemplate) {
        this.mongoTemplate = mongoTemplate;
    }

    public List<Schedule> listSchedules(Query query) {
        return mongoTemplate.find(query, Schedule.class, COLLECTION_NAME);
    }

    public Long countSchedules(Query query) {
        return mongoTemplate.count(query, COLLECTION_NAME);
    }

    public Schedule getScheduleByExecutor(String executor) {
        return mongoTemplate.findOne(new Query().addCriteria(Criteria.where("executor").is(executor)), Schedule.class, COLLECTION_NAME);
    }

    public void updateScheduleCron(String executor, String cron) {
        mongoTemplate.updateFirst(new Query().addCriteria(Criteria.where("executor").is(executor)), new Update().set("cron", cron), COLLECTION_NAME);
    }

    public void updateScheduleState(String executor, TriggerStatus triggerStatus) {
        mongoTemplate.updateFirst(new Query().addCriteria(Criteria.where("executor").is(executor)), new Update().set("triggerStatus", triggerStatus.name()), COLLECTION_NAME);
    }

    public Schedule save(Schedule schedule) {
        return mongoTemplate.save(schedule, COLLECTION_NAME);
    }

    public List<Schedule> getAllSchedules() {
        return mongoTemplate.find(new Query(), Schedule.class, COLLECTION_NAME);
    }

    public void saveRunInfo(List<RunInfo> runInfos) {
        for (RunInfo runInfo : runInfos) {
            Update update = new Update();
            update.set("runInfo", runInfo.getRunInfo());
            mongoTemplate.upsert(new Query().addCriteria(Criteria.where("executor").is(runInfo.getExecutor())), update, COLLECTION_NAME_RUN);
        }
    }

    public RunInfo getRunInfo(String executor) {
        RunInfo runInfo = mongoTemplate.findOne(new Query().addCriteria(Criteria.where("executor").is(executor)), RunInfo.class, COLLECTION_NAME_RUN);
        return runInfo;
    }

    public void updateRunInfo(String executor, Map<String, Long> runInfo) {
        mongoTemplate.updateFirst(new Query().addCriteria(Criteria.where("executor").is(executor)), new Update().set("runInfo", runInfo), COLLECTION_NAME_RUN);
    }

}
