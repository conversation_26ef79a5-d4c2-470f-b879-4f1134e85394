package com.quanzhi.auditapiv2.common.dal.dao.convert;

import com.alibaba.fastjson.JSONObject;
import com.quanzhi.audit_core.common.config.annotation.DynamicValue;
import com.quanzhi.audit_core.common.model.AssetLifeStateConfig;
import com.quanzhi.auditapiv2.common.util.utils.DataUtil;
import com.quanzhi.auditapiv2.common.util.utils.DateFormat;
import com.quanzhi.auditapiv2.common.util.utils.DateUtil;
import com.quanzhi.metabase.core.model.http.constant.AssetLifeStateEnum;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.Getter;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * create at 2023/4/20 19:46
 * @description: 对资产生命状态配置做些转换
 **/
@Component
public class AssetLifeStateConfigConvert {

    @DynamicValue(dataId = "common.asset.life.state.config.json", groupId = "common", typeClz = AssetLifeStateConfig.class)
    @Getter
    private List<AssetLifeStateConfig> assetLifeStateConfigs;

    @Data
    @AllArgsConstructor
    public static class LifeDateTag {
        private AssetLifeStateConfig assetLifeStateConfig;
        //新增-临界点 大于该值为新增
        private Long firstDatePoint;
        //活跃-临界点 大于该值为活跃
        private Long activeDatePoint;
        //失活-临界点 小于该值为失活
        private Long inactiveDatePoint;
        //复活-临界点  复活时间大于该值仍为复活
        private Long reviveDatePoint;
    }

    @Data
    public static class CriteriaTag {
        private Criteria springCriteria;
        private com.quanzhi.metabase.core.model.query.Criteria metabaseCriteria;
    }


    private LifeDateTag createDateTag(AssetLifeStateConfig config) {
        Integer firstTimeDay = config.getNewFirstTimeDay();
        Integer activeTimeDay = config.getActiveActiveTimeDay();
        Integer inactiveActiveTimeDay = config.getInactiveActiveTimeDay();
        //复活的持续天数
        Integer reviveHoldDay = config.getReviveHoldDay();

        Date currenDate = new Date();
        Date firstDate = DateUtil.addDate(currenDate, 5, -(firstTimeDay - 1));
        long firstDatePoint = DateUtil.getDateZeroTimestamp(firstDate);


        Date activeDate = DateUtil.addDate(currenDate, 5, -(activeTimeDay - 1));
        long activeDatePoint = DateUtil.getDateZeroTimestamp(activeDate);

        Date inactiveDate = DateUtil.addDate(currenDate, 5, -(inactiveActiveTimeDay));
        long inactiveDatePoint = DateUtil.getDateLastTimestamp(inactiveDate);

        Date reviveDate = DateUtil.addDate(currenDate, 5, -(reviveHoldDay));
        long reviveDatePoint = reviveDate.getTime();

        return new LifeDateTag(config, firstDatePoint, activeDatePoint, inactiveDatePoint, reviveDatePoint);

    }


    private AssetLifeStateConfig getAssetLifeConfig(AssetLifeStateConfig.AssetTypeEnum assetTypeEnum) {
        if (DataUtil.isEmpty(assetLifeStateConfigs)) {
            return null;
        }
        return assetLifeStateConfigs.stream().filter(e -> e.getAssetType() == assetTypeEnum).findFirst().get();
    }


    /**
     * @param assetTypeEnum
     * @param assetLifeStateEnum
     * <AUTHOR>
     * @description: 返回对应状态的条件
     * @date: 2023/4/21
     * @Return org.springframework.data.mongodb.core.query.Criteria
     */
    public CriteriaTag getCriteria(AssetLifeStateConfig.AssetTypeEnum assetTypeEnum, AssetLifeStateEnum assetLifeStateEnum) {
        CriteriaTag criteriaTag = new CriteriaTag();
        AssetLifeStateConfig assetLifeConfig = getAssetLifeConfig(assetTypeEnum);
        if (assetLifeConfig == null) {
            return criteriaTag;
        }
        LifeDateTag dateTag = createDateTag(assetLifeConfig);
        switch (assetTypeEnum) {
            case API:
            case APP:
                buildAppAndApiCriteria(dateTag, assetLifeStateEnum, criteriaTag);
                break;
            case ACCOUNT:
                buildAccountCriteria(dateTag, assetLifeStateEnum, criteriaTag);
            default:
        }
        return criteriaTag;
    }

    public Criteria getMongoCriteria(AssetLifeStateConfig.AssetTypeEnum assetTypeEnum, AssetLifeStateEnum assetLifeStateEnum) {
        Criteria criteria = new Criteria();
        AssetLifeStateConfig assetLifeConfig = getAssetLifeConfig(assetTypeEnum);
        if (assetLifeConfig == null) {
            return criteria;
        }
        LifeDateTag dateTag = createDateTag(assetLifeConfig);
        switch (assetTypeEnum) {
            case API:
            case APP:
                criteria = buildAppAndApiMongoCriteria(dateTag, assetLifeStateEnum);
                break;
            case ACCOUNT:
                criteria = buildAppAndApiMongoCriteria(dateTag, assetLifeStateEnum);
            default:
        }
        return criteria;
    }

    /**
     * 可能会命中多个状态，由调用端按需取
     */
    public List<Short> getAssetLifeState(JSONObject jsonObject) {
        String assetType = jsonObject.getString("assetType");
        LifeDateTag lifeDateTag;
        AssetLifeStateConfig assetLifeConfig = null;
        switch (assetType) {
            case "API":
                assetLifeConfig = getAssetLifeConfig(AssetLifeStateConfig.AssetTypeEnum.API);
                break;
            case "APP":
                assetLifeConfig = getAssetLifeConfig(AssetLifeStateConfig.AssetTypeEnum.APP);
                break;
            case "ACCOUNT":
                assetLifeConfig = getAssetLifeConfig(AssetLifeStateConfig.AssetTypeEnum.ACCOUNT);
                break;
        }
        if (assetLifeConfig == null) {
            return new ArrayList<>();
        }
        lifeDateTag = createDateTag(assetLifeConfig);
        return checkAppOrApiLifeState(jsonObject, lifeDateTag);
    }

    private List<Short> checkAppOrApiLifeState(JSONObject jsonObject, LifeDateTag dateTag) {
        List<Short> lifeStates = new ArrayList<>();
        Long discoverTime = jsonObject.getLong("discoverTime");
        if (discoverTime != null && discoverTime >= dateTag.getFirstDatePoint() &&
                Boolean.TRUE.equals(dateTag.getAssetLifeStateConfig().getNewOpenFlag())) {
            lifeStates.add(AssetLifeStateEnum.NEW.getNum());
        }
        Long reviveTime = jsonObject.getLong("reviveTime");
        if (reviveTime != null && reviveTime >= dateTag.getReviveDatePoint() &&
                Boolean.TRUE.equals(dateTag.getAssetLifeStateConfig().getReviveOpenFlag())) {
            lifeStates.add(AssetLifeStateEnum.REVIVE.getNum());
        }
        Long activeTime = jsonObject.getLong("activeTime");
        if (activeTime != null && activeTime >= dateTag.getActiveDatePoint() &&
                Boolean.TRUE.equals(dateTag.getAssetLifeStateConfig().getActiveOpenFlag())) {
            lifeStates.add(AssetLifeStateEnum.ACTIVE.getNum());
        }
        if (activeTime != null && activeTime <= dateTag.getInactiveDatePoint() &&
                Boolean.TRUE.equals(dateTag.getAssetLifeStateConfig().getInactiveOpenFlag())) {
            lifeStates.add(AssetLifeStateEnum.INACTIVE.getNum());
        }
        return lifeStates;
    }

    private void buildAppAndApiCriteria(AssetLifeStateConfigConvert.LifeDateTag dateTag, AssetLifeStateEnum assetLifeStateEnum, CriteriaTag criteriaTag) {
        Criteria springCriteria = new Criteria();
        com.quanzhi.metabase.core.model.query.Criteria metabaseCriteria = new com.quanzhi.metabase.core.model.query.Criteria();
        AssetLifeStateConfig assetLifeStateConfig = dateTag.getAssetLifeStateConfig();
        switch (assetLifeStateEnum) {
            case NEW:
                if (Boolean.TRUE.equals(assetLifeStateConfig.getNewOpenFlag())) {
                    springCriteria.and("discoverTime").gte(dateTag.getFirstDatePoint());
                    metabaseCriteria.andOperator(com.quanzhi.metabase.core.model.query.Criteria.where("discoverTime").gte(dateTag.getFirstDatePoint()));
                } else {
                    springCriteria.and("discoverTime").is(*********);
                    metabaseCriteria.andOperator(com.quanzhi.metabase.core.model.query.Criteria.where("discoverTime").is(*********));
                }
                break;
            case ACTIVE:
                if (Boolean.TRUE.equals(assetLifeStateConfig.getActiveOpenFlag())) {
                    springCriteria.and("activeTime").gte(dateTag.getActiveDatePoint());
                    metabaseCriteria.andOperator(com.quanzhi.metabase.core.model.query.Criteria.where("activeTime").gte(dateTag.getActiveDatePoint()));
                } else {
                    springCriteria.and("activeTime").is(*********);
                    metabaseCriteria.andOperator(com.quanzhi.metabase.core.model.query.Criteria.where("activeTime").is(*********));
                }
                break;
            case INACTIVE:
                if (Boolean.TRUE.equals(assetLifeStateConfig.getInactiveOpenFlag())) {
                    springCriteria.and("activeTime").lte(dateTag.getInactiveDatePoint());
                    metabaseCriteria.andOperator(com.quanzhi.metabase.core.model.query.Criteria.where("activeTime").lte(dateTag.getInactiveDatePoint()));
                } else {
                    springCriteria.and("activeTime").is(*********);
                    metabaseCriteria.andOperator(com.quanzhi.metabase.core.model.query.Criteria.where("activeTime").is(*********));
                }
                break;
            case REVIVE:
                if (Boolean.TRUE.equals(assetLifeStateConfig.getReviveOpenFlag())) {
                    springCriteria.and("reviveTime").gte(dateTag.getReviveDatePoint());
                    metabaseCriteria.andOperator(com.quanzhi.metabase.core.model.query.Criteria.where("reviveTime").gte(dateTag.getReviveDatePoint()));
                } else {
                    springCriteria.and("reviveTime").is(*********);
                    metabaseCriteria.andOperator(com.quanzhi.metabase.core.model.query.Criteria.where("reviveTime").is(*********));
                }
                break;
        }
        criteriaTag.setSpringCriteria(springCriteria);
        criteriaTag.setMetabaseCriteria(metabaseCriteria);
    }

    private Criteria buildAppAndApiMongoCriteria(AssetLifeStateConfigConvert.LifeDateTag dateTag, AssetLifeStateEnum assetLifeStateEnum) {
        Criteria springCriteria = new Criteria();
        com.quanzhi.metabase.core.model.query.Criteria metabaseCriteria = new com.quanzhi.metabase.core.model.query.Criteria();
        AssetLifeStateConfig assetLifeStateConfig = dateTag.getAssetLifeStateConfig();
        switch (assetLifeStateEnum) {
            case NEW:
                if (Boolean.TRUE.equals(assetLifeStateConfig.getNewOpenFlag())) {
                    springCriteria.and("discoverTime").gte(dateTag.getFirstDatePoint());
                    metabaseCriteria.andOperator(com.quanzhi.metabase.core.model.query.Criteria.where("discoverTime").gte(dateTag.getFirstDatePoint()));
                } else {
                    springCriteria.and("discoverTime").is(*********);
                    metabaseCriteria.andOperator(com.quanzhi.metabase.core.model.query.Criteria.where("discoverTime").is(*********));
                }
                break;
            case ACTIVE:
                if (Boolean.TRUE.equals(assetLifeStateConfig.getActiveOpenFlag())) {
                    springCriteria.and("activeTime").gte(dateTag.getActiveDatePoint());
                    metabaseCriteria.andOperator(com.quanzhi.metabase.core.model.query.Criteria.where("activeTime").gte(dateTag.getActiveDatePoint()));
                } else {
                    springCriteria.and("activeTime").is(*********);
                    metabaseCriteria.andOperator(com.quanzhi.metabase.core.model.query.Criteria.where("activeTime").is(*********));
                }
                break;
            case INACTIVE:
                if (Boolean.TRUE.equals(assetLifeStateConfig.getInactiveOpenFlag())) {
                    springCriteria.and("activeTime").lte(dateTag.getInactiveDatePoint());
                    metabaseCriteria.andOperator(com.quanzhi.metabase.core.model.query.Criteria.where("activeTime").lte(dateTag.getInactiveDatePoint()));
                } else {
                    springCriteria.and("activeTime").is(*********);
                    metabaseCriteria.andOperator(com.quanzhi.metabase.core.model.query.Criteria.where("activeTime").is(*********));
                }
                break;
            case REVIVE:
                if (Boolean.TRUE.equals(assetLifeStateConfig.getReviveOpenFlag())) {
                    springCriteria.and("reviveTime").gte(dateTag.getReviveDatePoint());
                    metabaseCriteria.andOperator(com.quanzhi.metabase.core.model.query.Criteria.where("reviveTime").gte(dateTag.getReviveDatePoint()));
                } else {
                    springCriteria.and("reviveTime").is(*********);
                    metabaseCriteria.andOperator(com.quanzhi.metabase.core.model.query.Criteria.where("reviveTime").is(*********));
                }
                break;
        }
        return springCriteria;
    }

    private void buildAccountCriteria(LifeDateTag dateTag, AssetLifeStateEnum assetLifeStateEnum, CriteriaTag criteriaTag) {
        Criteria springCriteria = new Criteria();
        com.quanzhi.metabase.core.model.query.Criteria metabaseCriteria = new com.quanzhi.metabase.core.model.query.Criteria();

        long firstDatePoint = dateTag.getFirstDatePoint();
        long activeDatePoint = dateTag.getActiveDatePoint();
        long inactiveDatePoint = dateTag.getInactiveDatePoint();
        AssetLifeStateConfig assetLifeStateConfig = dateTag.getAssetLifeStateConfig();

        String firstDate = cn.hutool.core.date.DateUtil.format(new Date(firstDatePoint), DateFormat.FORMAT_YMD);
        String activeDate = cn.hutool.core.date.DateUtil.format(new Date(activeDatePoint), DateFormat.FORMAT_YMD);
        String inactiveDate = cn.hutool.core.date.DateUtil.format(new Date(inactiveDatePoint), DateFormat.FORMAT_YMD);

        switch (assetLifeStateEnum) {
            case NEW:
                if (Boolean.TRUE.equals(assetLifeStateConfig.getNewOpenFlag())) {
                    springCriteria.and("firstDate").gte(firstDate);
                    metabaseCriteria.andOperator(com.quanzhi.metabase.core.model.query.Criteria.where("firstDate").gte(firstDate));
                } else {
                    springCriteria.and("firstDate").is("19700101");
                    metabaseCriteria.andOperator(com.quanzhi.metabase.core.model.query.Criteria.where("firstDate").is("19700101"));
                }
                break;
            case ACTIVE:
                if (Boolean.TRUE.equals(assetLifeStateConfig.getActiveOpenFlag())) {
                    springCriteria.and("lastDate").gte(activeDate);
                    metabaseCriteria.andOperator(com.quanzhi.metabase.core.model.query.Criteria.where("lastDate").gte(activeDate));
                } else {
                    springCriteria.and("lastDate").is("19700101");
                    metabaseCriteria.andOperator(com.quanzhi.metabase.core.model.query.Criteria.where("lastDate").is("19700101"));
                }
                break;
            case INACTIVE:
                if (Boolean.TRUE.equals(assetLifeStateConfig.getInactiveOpenFlag())) {
                    springCriteria.and("lastDate").lte(inactiveDate);
                    metabaseCriteria.andOperator(com.quanzhi.metabase.core.model.query.Criteria.where("lastDate").lte(inactiveDate));
                } else {
                    springCriteria.and("lastDate").is("19700101");
                    metabaseCriteria.andOperator(com.quanzhi.metabase.core.model.query.Criteria.where("lastDate").is("19700101"));
                }
                break;
            case REVIVE:
                if (Boolean.TRUE.equals(assetLifeStateConfig.getReviveOpenFlag())) {
                    springCriteria.and("reviveTime").gte(dateTag.getReviveDatePoint());
                    metabaseCriteria.andOperator(com.quanzhi.metabase.core.model.query.Criteria.where("reviveTime").gte(dateTag.getReviveDatePoint()));
                } else {
                    springCriteria.and("reviveTime").is("19700101");
                    metabaseCriteria.andOperator(com.quanzhi.metabase.core.model.query.Criteria.where("reviveTime").is("19700101"));
                }
                break;
        }
        criteriaTag.setSpringCriteria(springCriteria);
        criteriaTag.setMetabaseCriteria(metabaseCriteria);
    }


    public static void main(String[] args) {
        Date date = DateUtil.addDate(new Date(), 5, -(0));
        System.out.println(DateUtil.getDateZeroTimestamp(date));

        Date date2 = DateUtil.addDate(new Date(), 5, -(1));
        System.out.println(DateUtil.getDateLastTimestamp(date2));

    }


}