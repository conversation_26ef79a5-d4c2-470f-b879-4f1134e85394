package com.quanzhi.auditapiv2.common.dal.dao;

import com.quanzhi.auditapiv2.common.dal.entity.riskEolink.RiskIpStrategyDto;

public interface RiskIpStrategyDao {

    RiskIpStrategyDto save(RiskIpStrategyDto dto);

    RiskIpStrategyDto findByIp(String ip);
    RiskIpStrategyDto findByAccount(String account);

    RiskIpStrategyDto findByUuid(String uuid);

    RiskIpStrategyDto update(RiskIpStrategyDto dto);

    void deleteByIp(String ip);

    void deleteByStrategyId(String strategyId);
}
