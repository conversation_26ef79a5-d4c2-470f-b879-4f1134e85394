package com.quanzhi.auditapiv2.common.dal.entity.systemSecurity;

import lombok.Data;

import java.util.List;

/**
 * @author: yangzixian
 * @date: 2022/7/18 17:57
 * @description:
 */
@Data
public class SystemSecurity {

    private String id;

    private Integer listType;

    private List<String> listValue;

    private Long createTime;

    private Long updateTime;

    public enum ListTypeEnum {

        /**
         * 黑名单
         */
        BLACK(1),

        /**
         * 白名单
         */
        WHITE(2);

        Integer type;

        ListTypeEnum(Integer type) {

            this.type = type;
        }

        public Integer getType() {
            return type;
        }
    }

}
