package com.quanzhi.auditapiv2.common.dal.dto;

import com.quanzhi.audit_core.common.model.NetworkSegment;
import com.quanzhi.auditapiv2.common.dal.entity.OriginIpResolve;
import com.quanzhi.auditapiv2.common.dal.entity.OriginIpResolve.ResolveTypeEnum;
import com.quanzhi.auditapiv2.common.util.utils.DataUtil;
import com.quanzhi.auditapiv2.common.util.utils.ServiceException;
import com.quanzhi.awdb_core.util.IpUtil;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

/**
 *
 * 《来源IP配置dto》
 *
 *
 * @Project:
 * @Module ID:
 * @Comments:
 * @JDK version used:      <JDK1.8>
 * <AUTHOR> [<EMAIL>]
 * @since 2020-04-08-上午9:56:10
 */
@Data
@NoArgsConstructor
public class OriginIpResolveDto {

    @ApiModelProperty(value = "id", name = "id")
    private String id;

    /**
     * 优先级
     */
    @ApiModelProperty(value = "优先级", name = "priority")
    private Integer priority;

    /**
     * 字段名
     */
    @ApiModelProperty(value = "字段名", name = "filedName")
    private String filedName;

    /**
     * 解析方式
     * @see ResolveTypeEnum
     */
    @ApiModelProperty(value = "解析方式", name = "resolveType")
    private Integer resolveType;

    /**
     * 内部IP段
     */
    @ApiModelProperty(value = "内部IP段", name = "innerIpSegs")
    private List<IpSegmentDto> innerIpSegs;

    /**
     * 内部IP段
     */
    @ApiModelProperty(value = "IP过滤段", name = "innerIpSegs")
    private List<String> filterIps;

    /**
     * 方向
     */
    @ApiModelProperty(value = "方向", name = "direction")
    private Integer direction = OriginIpResolve.DirectionEnum.REVERSE.value();

    @Data
    public static class IpSegmentDto {

        /**
         * 开始IP
         */
        @ApiModelProperty(value = "开始IP", name = "startIp")
        private String startIp;

        /**
         * 结束IP
         */
        @ApiModelProperty(value = "结束IP", name = "endIp")
        private String endIp;

        @ApiModelProperty("ip类型")
        private Integer ipType;
    }

    public static List<OriginIpResolve> dtos2model(List<OriginIpResolveDto> originIpResolveDtos) {
        List<OriginIpResolve> originIpResolves = new ArrayList<>();
        if (DataUtil.isEmpty(originIpResolveDtos)) {
            return originIpResolves;
        }

        originIpResolveDtos.forEach(i -> {
            OriginIpResolve originIpResolve = i.dto2model();
            originIpResolves.add(originIpResolve);
        });

        return originIpResolves;
    }

    public OriginIpResolve dto2model() {
        OriginIpResolve originIpResolve = new OriginIpResolve();
        originIpResolve.setId(this.getId());
        originIpResolve.setPriority(this.priority);
        originIpResolve.setResolveType(this.resolveType);
        originIpResolve.setFiledName(this.filedName);
        originIpResolve.setDirection(this.direction);

//        List<OriginIpResolve.IpSegment> innerIpSegs = new ArrayList<>();
//
//        if (DataUtil.isNotEmpty(this.innerIpSegs)) {
//            this.innerIpSegs.stream().forEach(i -> {
//                OriginIpResolve.IpSegment innerIpSeg = new OriginIpResolve.IpSegment();
//                innerIpSeg.setStartIp(i.getStartIp());
//                innerIpSeg.setEndIp(i.getEndIp());
//                innerIpSeg.setIpType(i.getIpType());
//
//                innerIpSegs.add(innerIpSeg);
//            });
//        }

        // 兼容审计的 filterIps 字段
//        List<IpSegmentDto> innerIpSegsCovert = filterIds2IpSegs(filterIps);
//        if (DataUtil.isNotEmpty(innerIpSegsCovert)){
//            innerIpSegsCovert.stream().forEach(i -> {
//                OriginIpResolve.IpSegment innerIpSeg = new OriginIpResolve.IpSegment();
//                innerIpSeg.setStartIp(i.getStartIp());
//                innerIpSeg.setEndIp(i.getEndIp());
//                innerIpSeg.setIpType(i.getIpType());
//
//                innerIpSegs.add(innerIpSeg);
//            });
//            originIpResolve.setFilterIps(new ArrayList<>());
//        }
//
//        originIpResolve.setInnerIpSegs(innerIpSegs);
        originIpResolve.setFilterIps(filterIps);

        return originIpResolve;
    }

    public static void isValid(List<OriginIpResolveDto> originIpResolveDtos) {
        if (DataUtil.isEmpty(originIpResolveDtos)) {
            return;
        }

        Set<Integer> prioritySet = new HashSet<>();
        for (OriginIpResolveDto i : originIpResolveDtos) {
            OriginIpResolveDto.isValid(i);

            boolean added = prioritySet.add(i.getPriority());
            if (!added) {
                throw new ServiceException("优先级不可重复");
            }
        }
    }

    public static void isValid(OriginIpResolveDto originIpResolveDto) {
        if (originIpResolveDto.getPriority()<1||originIpResolveDto.getPriority()>100)
            throw new ServiceException("优先级不可以小于1或者超过100");
        if (DataUtil.isEmpty(originIpResolveDto.getResolveType())) {
            throw new ServiceException("解析位置不能为空");
        }

        if( !OriginIpResolve.ResolveTypeEnum.exist(originIpResolveDto.getResolveType()) ) {
            throw new ServiceException("请填写合法的解析位置");
        }

        if(originIpResolveDto.direction != null && originIpResolveDto.direction != 0 && originIpResolveDto.direction != 1 ) {
            throw new ServiceException("请填写合法的取值顺序");
        }

        if (originIpResolveDto.getFiledName() == null || originIpResolveDto.getFiledName().isEmpty()) {
            throw new ServiceException("字段名称不可为空");
        }

        if (originIpResolveDto.getFiledName().length()>50) {
            throw new ServiceException("字段路径字符数不可以超过50个字符");
        }

        // 兼容审计的 filterIps 字段
        if (DataUtil.isNotEmpty(originIpResolveDto.getFilterIps())) {
            originIpResolveDto.getFilterIps().forEach(i -> {
                if(i.contains("/")) {
                    if (!IpUtil.isIPV6_IPV4(i.split("/")[0])) {
                        throw new ServiceException("配置的IP不合法");
                    }
                } else if(i.contains("-")){
                    if (!IpUtil.isIPV6_IPV4(i.split("-")[0])) {
                        throw new ServiceException("配置的IP不合法");
                    }

                    if (!IpUtil.isIPV6_IPV4(i.split("-")[1])) {
                        throw new ServiceException("配置的IP不合法");
                    }
                } else {
                    if(!i.contains("*")) {
                        if (!IpUtil.isIPV6_IPV4(i)) {
                            throw new ServiceException("配置的IP不合法");
                        }
                    }
                }
            });
        }
    }

    public static List<OriginIpResolveDto> models2dots(List<OriginIpResolve> originIpResolves) {

        if (DataUtil.isEmpty(originIpResolves)) {
            return null;
        }

        List<OriginIpResolveDto> originIpResolveDtos = new ArrayList<>();
        for (OriginIpResolve i : originIpResolves) {
            OriginIpResolveDto originIpResolveDto = new OriginIpResolveDto(i);
            originIpResolveDtos.add(originIpResolveDto);
        }

        return originIpResolveDtos;
    }

    public OriginIpResolveDto(OriginIpResolve originIpResolve) {
        if (DataUtil.isEmpty(originIpResolve)) {
            return;
        }

        this.id = originIpResolve.getId();
        this.filedName = originIpResolve.getFiledName();
        this.priority = originIpResolve.getPriority();
        this.resolveType = originIpResolve.getResolveType();
        this.direction = originIpResolve.getDirection();
        this.innerIpSegs = new ArrayList<>();
        this.filterIps = originIpResolve.getFilterIps();
        if (DataUtil.isEmpty(filterIps)){
            this.filterIps = new ArrayList<>();
        }

        List<OriginIpResolve.IpSegment> innerIpSegs_ = originIpResolve.getInnerIpSegs();
        if (DataUtil.isNotEmpty(innerIpSegs_)){
            for (OriginIpResolve.IpSegment i : innerIpSegs_) {
                IpSegmentDto innerIpSeg = new IpSegmentDto();
                innerIpSeg.setStartIp(i.getStartIp());
                innerIpSeg.setEndIp(i.getEndIp());
                innerIpSeg.setIpType(i.getIpType());

                this.innerIpSegs.add(innerIpSeg);
            }
        }

        // 兼容审计 filterIps 字段
        List<IpSegmentDto> innerIpSegsCovert = filterIds2IpSegs(originIpResolve.getFilterIps());
        if (DataUtil.isNotEmpty(innerIpSegsCovert)){
            for (IpSegmentDto i : innerIpSegsCovert) {
                IpSegmentDto innerIpSeg = new IpSegmentDto();
                innerIpSeg.setStartIp(i.getStartIp());
                innerIpSeg.setEndIp(i.getEndIp());
                innerIpSeg.setIpType(i.getIpType());

                this.innerIpSegs.add(innerIpSeg);
            }
        }
    }

    private List<IpSegmentDto> filterIds2IpSegs(List<String> filterIps) {
        if (DataUtil.isNotEmpty(filterIps)) {
            List<IpSegmentDto> innerIpSegs = new ArrayList<>();

            for (String i : filterIps) {
                IpSegmentDto innerIpSeg = new IpSegmentDto();

                if (i.contains("/")) {
                    innerIpSeg.setStartIp(i.trim());
                    innerIpSeg.setIpType(NetworkSegment.IpTypeEnum.IP_MASK.value());
                } else if (i.contains("-")) {
                    innerIpSeg.setStartIp(i.split("-")[0].trim());
                    innerIpSeg.setEndIp(i.split("-")[1].trim());
                    innerIpSeg.setIpType(NetworkSegment.IpTypeEnum.IP_SEG.value());
                } else {
                    innerIpSeg.setStartIp(i.trim());
                    innerIpSeg.setIpType(NetworkSegment.IpTypeEnum.IP.value());
                }

                innerIpSegs.add(innerIpSeg);
            }

            return innerIpSegs;
        }

        return new ArrayList<>();
    }
}
