package com.quanzhi.auditapiv2.common.dal.dto.group;

import com.quanzhi.auditapiv2.common.dal.dataobject.DataGroup;
import lombok.Data;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;
import java.util.List;
import com.quanzhi.audit.mix.permission.domain.Condition;

/**
 * <AUTHOR>
 * create at 2024/11/13 14:27
 * @description: 数据组DTO
 **/
@Data
public class DataGroupDto {

    private String id;

    private String name;

    private String description;
    /**
     * 0 禁用
     * 1 启用
     */
    private Integer status;
    /**
     * 1 内置
     * 2 自定义
     */
    private Integer type;

    private Long createTime;

    private Long updateTime;

    private Boolean delFlag;

    private List<Condition> conditions;

    @org.mapstruct.Mapper
    public interface DataGroupMapper {
        DataGroupDto.DataGroupMapper INSTANCE = Mappers.getMapper(DataGroupDto.DataGroupMapper.class);

        DataGroupDto convert(DataGroup dataGroup);

        DataGroup convert(DataGroupDto dataGroupDto);
    }
}