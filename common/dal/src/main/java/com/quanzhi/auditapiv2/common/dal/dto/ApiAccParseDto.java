package com.quanzhi.auditapiv2.common.dal.dto;

import com.quanzhi.metabase.core.model.http.ApiAccParseRelation;
import com.quanzhi.metabase.core.model.http.HttpApiResource;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.io.Serializable;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @ClassName ApiAccParseDto
 * @Description TODO
 * <AUTHOR>
 * @Date 2020/10/20 14:21
 **/
@Data
public class ApiAccParseDto implements Serializable {

    @ApiModelProperty(value = "id", name = "id")
    private String id;

    private List<ApiAccParseRelation> apiAccParseRelations;

    // 要解析的api
    private String uri;

    private String apiUrl;

    /**
     * 账号配置
     */
    private List<HttpApiResource.AccParseConfigs> userParseConfigs;

    /**
     * session配置
     */
    private List<HttpApiResource.AccParseConfigs> sessionParseConfigs;

    /**
     * 密码配置
     */
    private List<HttpApiResource.AccParseConfigs> passwordParseConfigs;

    /**
     * 登录凭证配置
     */
    private List<HttpApiResource.AccParseConfigs> loginSuccessConfigs;


    // 未勾选的暂存配置
    /**
     * 账号配置
     */
    private List<HttpApiResource.AccParseConfigs> unableUserParseConfigs;
    /**
     * session配置
     */
    private List<HttpApiResource.AccParseConfigs> unableSessionParseConfigs;

    /**
     * 密码配置
     */
    private List<HttpApiResource.AccParseConfigs> unablePasswordParseConfigs;
    /**
     * 登录凭证配置
     */
    private List<HttpApiResource.AccParseConfigs> unableLoginSuccessConfigs;

    private boolean isSso;

    private boolean accParseEnable;

    private boolean staffInfoParseEnable;

    // 作用于应用下所有未配置的api：false-不作用，true-作用于应用
    private Boolean enableAppConfig;

    /**
     * 组织架构部门
     */
    private List<HttpApiResource.AccParseConfigs> staffDepartParseConfigs;

    private List<HttpApiResource.AccParseConfigs> staffNameParseConfigs;

    private List<HttpApiResource.AccParseConfigs> staffChineseParseConfigs;

    private List<HttpApiResource.AccParseConfigs> staffIdCardParseConfigs;

    private List<HttpApiResource.AccParseConfigs> staffBankCardParseConfigs;

    private List<HttpApiResource.AccParseConfigs> staffEmailParseConfigs;

    private List<HttpApiResource.AccParseConfigs> staffIdParseConfigs;

    private List<HttpApiResource.AccParseConfigs> staffMobileParseConfigs;
    private List<HttpApiResource.AccParseConfigs> staffNickNameParseConfigs;
    private List<HttpApiResource.AccParseConfigs> staffRoleParseConfigs;

    private List<String> tags;

    // 接口是否使用了应用的配置
    private Map<String, List<HttpApiResource.AccParseConfigs>> appConfigsMap = new HashMap<>();

    @Mapper
    public interface ApiAccParseDtoMapper {

        ApiAccParseDtoMapper INSTANCE = Mappers.getMapper(ApiAccParseDtoMapper.class);

        ApiAccParseDto convert(HttpApiResource httpApiResource);

        List<ApiAccParseDto> convert(List<HttpApiResource> httpApiResources);

        ApiAccParseDto convert(ApiAccParseDto apiAccParseDto);
    }
}
