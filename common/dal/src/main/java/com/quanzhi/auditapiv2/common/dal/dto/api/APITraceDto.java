package com.quanzhi.auditapiv2.common.dal.dto.api;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@ApiModel("追踪信息")
public class APITraceDto {
    @ApiModelProperty("主键ID")
    private String id;
    @ApiModelProperty("追踪任务URL数据")
    private String url;
    @ApiModelProperty("状态 RUNNING SUCCESS FAILURE")
    private String state;
    @ApiModelProperty("状态描述")
    private String stateDesc;
    @ApiModelProperty("开始时间")
    private long startTimestamp;
    @ApiModelProperty("结束时间")
    private Long endTimestamp;
    @ApiModelProperty("中间结果集")
    private List<TraceResult> results;
    @ApiModelProperty("最终结果描述")
    private String result;
    @ApiModelProperty("错误信息")
    private String errorMsg;

    public void setEndTimestamp(long endTimestamp) {
        if (endTimestamp == 0){
            this.endTimestamp = null;
        }else{
            this.endTimestamp = endTimestamp;
        }
    }

    @Data
    @ApiModel("追踪结果（中间结果，有各种方式）")
    @AllArgsConstructor
    @NoArgsConstructor
    public static final class TraceResult{
        @ApiModelProperty("结果描述")
        private String result;
        @ApiModelProperty("是否成功")
        private boolean success;
    }

}
