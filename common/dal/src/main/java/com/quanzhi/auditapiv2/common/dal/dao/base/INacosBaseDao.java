package com.quanzhi.auditapiv2.common.dal.dao.base;

import com.quanzhi.auditapiv2.common.dal.dto.query.NacosFilterDto;
import com.quanzhi.auditapiv2.common.dal.dto.query.QueryNacos;

import java.util.List;

public interface INacosBaseDao<T> {
    void initCollectionName(String collectionName);

    /**
     * 获取索引
     *
     * @return
     */
    T findOne(String id);

    /**
     * 获取索引
     *
     * @return
     */
    T getById(String id);

    public T findOne(QueryNacos queryNacos);

    /**
     * 根据字段更新数据
     */
    boolean update(T model);

    List<T> getList(Integer page, Integer limit);

    List<T> getList(Integer page,Integer limit, List<NacosFilterDto> nacosFilterDtos);

    /**
     * 获取总数
     */
    long getCount();

    long getCount(List<NacosFilterDto> nacosFilterDtos);

    /**
     * 保存配置
     * @param model
     * @return
     */
    T save(T model);

    /**
     * 批量删除
     * @param ids
     * @return
     */
    boolean delete(List<String> ids);

    List<T> getAll();

    boolean batchUpdate(List<T> models);

    void replaceAll(List<T> models);

    void reset(List<T> models);

    List<T> getAllSelect(List<NacosFilterDto> nacosFilterList);

    List<T> page(Integer page,Integer limit,List<T> content);
}
