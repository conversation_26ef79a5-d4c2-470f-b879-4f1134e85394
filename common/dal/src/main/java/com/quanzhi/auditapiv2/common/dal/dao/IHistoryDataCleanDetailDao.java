package com.quanzhi.auditapiv2.common.dal.dao;

import com.quanzhi.audit_core.common.model.HistoryDataCleanTaskDetail;
import com.quanzhi.auditapiv2.common.dal.dao.base.IBaseDao;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/7/21 上午10:09
 */
public interface IHistoryDataCleanDetailDao extends IBaseDao<HistoryDataCleanTaskDetail>{

    Long getDistintCount(String dataType,String taskId);

    List<HistoryDataCleanTaskDetail> getDistintList(Integer page,Integer limit,String dataType,String taskId);

}
