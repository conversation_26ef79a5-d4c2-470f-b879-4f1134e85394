package com.quanzhi.auditapiv2.common.dal.dao.impl;

import com.quanzhi.audit_core.common.model.DataLabelFirstClass;
import com.quanzhi.auditapiv2.common.dal.dao.IDataLabelFirstClassNacosDao;
import com.quanzhi.auditapiv2.common.dal.dao.base.impl.NacosBaseDaoImpl;
import org.springframework.stereotype.Repository;

/**
 * 
 * 《数据标签一级分类持久层Nacos接口实现》
 * 
 * 
 * @Project: 
 * @Module ID:
 * @Comments: 
 * @JDK version used:      <JDK1.8> 
 * <AUTHOR> [<EMAIL>]
 * @since 2022-07-25-上午9:56:10
 */
@Repository
public class DataLabelFirstClassNacosDaoImpl extends NacosBaseDaoImpl<DataLabelFirstClass> implements IDataLabelFirstClassNacosDao {
}
