package com.quanzhi.auditapiv2.common.dal.dao.impl;

import com.quanzhi.auditapiv2.common.dal.dao.IHttpApiSnapshotDao;
import com.quanzhi.auditapiv2.common.dal.dao.base.impl.MetabaseDaoImpl;
import com.quanzhi.metabase.core.model.http.HttpApiSnapshot;
import com.quanzhi.metabase.core.model.query.MetabaseQuery;
import com.quanzhi.metabase.core.model.query.Predicate;
import com.quanzhi.metabase.core.model.query.Sort;
import com.quanzhi.metabase.core.model.query.SortOrder;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 
 * 《接口快照持久层接口实现》
 * 
 * 
 * @Project: 
 * @Module ID:
 * @Comments: 
 * @JDK version used:      <JDK1.8> 
 * <AUTHOR> [<EMAIL>]
 * @since 2020-04-08-上午9:56:10
 */
@Repository
public class HttpApiSnapshotDaoImpl extends MetabaseDaoImpl<HttpApiSnapshot> implements IHttpApiSnapshotDao {

    /**
     * 查询接口快照列表(分页)
     * @Comments:  <对此方法的描述，可以引用系统设计中的描述>
     * <AUTHOR> [<EMAIL>]
     * @since 2020-04-08 9:56
     * @param
     * @return
     */
    @Override
    public List<HttpApiSnapshot> selectHttpApiSnapshotList(String appId, Long startDate, Long endDate, Integer page, Integer limit) throws Exception {

        MetabaseQuery metabaseQuery = new MetabaseQuery();

        //查询条件
        metabaseQuery.where("delFlag", Predicate.IS, false);
        metabaseQuery.where("appId", Predicate.IS, appId);
        metabaseQuery.where("startTimestamp", Predicate.GTE, startDate);
        metabaseQuery.where("endTimestamp", Predicate.LTE, endDate);

        //分页
        metabaseQuery.skip((page - 1) * limit);
        metabaseQuery.limit(limit);

        return metabaseClientTemplate.find(metabaseQuery, HttpApiSnapshot.class);
    }

    /**
     * 获取最近周期的快照列表
     * @param limit
     * @param uri
     * @return
     */
    @Override
    public List<HttpApiSnapshot> getNearHttpApiSnapshotList(String uri,Integer limit) {

        MetabaseQuery metabaseQuery = new MetabaseQuery();

        //查询条件
        metabaseQuery.where("delFlag", Predicate.IS, false);
        metabaseQuery.where("uri", Predicate.IS, uri);

        //排序
        metabaseQuery.sort(Sort.by("startTimestamp", SortOrder.DESC));

        //分页
        metabaseQuery.skip(0);
        metabaseQuery.limit(limit);

        return metabaseClientTemplate.find(metabaseQuery, HttpApiSnapshot.class);
    }

    /**
     * 查询接口快照数量
     * @Comments:  <对此方法的描述，可以引用系统设计中的描述>
     * <AUTHOR> [<EMAIL>]
     * @since 2020-04-08 9:56
     * @param
     * @return
     */
    @Override
    public Long totalCount(String appId, Long startDate, Long endDate) {

        MetabaseQuery metabaseQuery = new MetabaseQuery();

        //查询条件
        metabaseQuery.where("delFlag", Predicate.IS, false);
        metabaseQuery.where("appId", Predicate.IS, appId);
        metabaseQuery.where("startTimestamp", Predicate.GTE, startDate);
        metabaseQuery.where("endTimestamp", Predicate.LTE, endDate);

        return metabaseClientTemplate.count(metabaseQuery, HttpApiSnapshot.class);
    }

    /**
     * id查询接口快照详情
     * @Comments:  <对此方法的描述，可以引用系统设计中的描述>
     * <AUTHOR> [<EMAIL>]
     * @since 2020-04-08 9:56
     * @param
     * @return
     */
    @Override
    public HttpApiSnapshot selectHttpApiSnapshotById(String id) throws Exception {

        return metabaseClientTemplate.findOne(id, HttpApiSnapshot.class);
    }
}
