package com.quanzhi.auditapiv2.common.dal.dao;

import com.quanzhi.auditapiv2.common.dal.entity.search.KeywordMapping;
import com.quanzhi.auditapiv2.common.dal.entity.search.SearchConfigIndex;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/2/20 3:29 下午
 */
public interface SearchConfigIndexDao {
    void save(SearchConfigIndex searchConfigIndex);
    SearchConfigIndex findByType(String type);
    List<SearchConfigIndex> list(String q);

    List<SearchConfigIndex> list(String q, List<KeywordMapping.Type> types);

    List<SearchConfigIndex> list(List<String> keywords, List<KeywordMapping.Type> types);

    long count();

    void clear();
}
