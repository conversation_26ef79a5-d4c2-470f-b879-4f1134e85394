package com.quanzhi.auditapiv2.common.dal.mongoCollectionIndex.document;

import com.quanzhi.auditapiv2.common.dal.mongoCollectionIndex.MongoCollectionConstant;
import org.springframework.data.mongodb.core.index.CompoundIndex;
import org.springframework.data.mongodb.core.index.CompoundIndexes;
import org.springframework.data.mongodb.core.index.IndexDirection;
import org.springframework.data.mongodb.core.index.Indexed;
import org.springframework.data.mongodb.core.mapping.Document;

@Document(collection = MongoCollectionConstant.FILE_INFO)
@CompoundIndexes({
        @CompoundIndex(name = "fileType_downloadCnt_asc", def = "{fileType:1,downloadCnt:1}",background = true),
        @CompoundIndex(name = "fileType_downloadCnt_desc", def = "{fileType:1,downloadCnt:-1}",background = true),
        @CompoundIndex(name = "fileType_dataDistinctCnt_asc", def = "{fileType:1,dataDistinctCnt:1}",background = true),
        @CompoundIndex(name = "fileType_dataDistinctCnt_desc", def = "{fileType:1,dataDistinctCnt:-1}",background = true),
        @CompoundIndex(name = "fileType_updateTime_asc", def = "{fileType:1,updateTime:1}",background = true),
        @CompoundIndex(name = "fileType_updateTime_desc", def = "{fileType:1,updateTime:-1}",background = true),
        @CompoundIndex(name = "fileType_uploadCnt_asc", def = "{fileType:1,uploadCnt:1}",background = true),
        @CompoundIndex(name = "fileType_uploadCnt_desc", def = "{fileType:1,uploadCnt:-1}",background = true),
        @CompoundIndex(name = "fileNameList_downloadCnt_asc", def = "{fileNameList:1,downloadCnt:1}",background = true),
        @CompoundIndex(name = "fileNameList_downloadCnt_desc", def = "{fileNameList:1,downloadCnt:-1}",background = true),
        @CompoundIndex(name = "fileNameList_dataDistinctCnt_asc", def = "{fileNameList:1,dataDistinctCnt:1}",background = true),
        @CompoundIndex(name = "fileNameList_dataDistinctCnt_desc", def = "{fileNameList:1,dataDistinctCnt:-1}",background = true),
        @CompoundIndex(name = "fileNameList_updateTime_asc", def = "{fileNameList:1,updateTime:1}",background = true),
        @CompoundIndex(name = "fileNameList_updateTime_desc", def = "{fileNameList:1,updateTime:-1}",background = true),
        @CompoundIndex(name = "fileNameList_uploadCnt_asc", def = "{fileNameList:1,uploadCnt:1}",background = true),
        @CompoundIndex(name = "fileNameList_uploadCnt_desc", def = "{fileNameList:1,uploadCnt:-1}",background = true),
        @CompoundIndex(name = "fileLevel_downloadCnt_asc", def = "{fileLevel:1,downloadCnt:1}",background = true),
        @CompoundIndex(name = "fileLevel_downloadCnt_desc", def = "{fileLevel:1,downloadCnt:-1}",background = true),
        @CompoundIndex(name = "fileLevel_dataDistinctCnt_asc", def = "{fileLevel:1,dataDistinctCnt:1}",background = true),
        @CompoundIndex(name = "fileLevel_dataDistinctCnt_desc", def = "{fileLevel:1,dataDistinctCnt:-1}",background = true),
        @CompoundIndex(name = "fileLevel_updateTime_asc", def = "{fileLevel:1,updateTime:1}",background = true),
        @CompoundIndex(name = "fileLevel_updateTime_desc", def = "{fileLevel:1,updateTime:-1}",background = true),
        @CompoundIndex(name = "fileLevel_uploadCnt_asc", def = "{fileLevel:1,uploadCnt:1}",background = true),
        @CompoundIndex(name = "fileLevel_uploadCnt_desc", def = "{fileLevel:1,uploadCnt:-1}",background = true)
})
public class FileInfoDocument {

    @Indexed(name = "fileLen",direction = IndexDirection.DESCENDING,background = true)
    private Long fileLen;

    @Indexed(name = "uploadCnt",direction = IndexDirection.DESCENDING,background = true)
    private Long uploadCnt;

    @Indexed(name = "downloadCnt",direction = IndexDirection.DESCENDING,background = true)
    private Long downloadCnt;

    @Indexed(name = "dataDistinctCnt",direction = IndexDirection.DESCENDING,background = true)
    private Long dataDistinctCnt;

    @Indexed(name = "updateTime",direction = IndexDirection.DESCENDING,background = true)
    private Long updateTime;

    @Indexed(name = "timestamp",direction = IndexDirection.DESCENDING,background = true)
    private Long timestamp;
}
