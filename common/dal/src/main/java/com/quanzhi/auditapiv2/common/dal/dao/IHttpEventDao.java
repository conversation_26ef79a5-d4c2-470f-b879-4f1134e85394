package com.quanzhi.auditapiv2.common.dal.dao;

import com.quanzhi.audit_core.common.model.FilterHttpEvent;
import com.quanzhi.audit_core.common.model.HttpEvent;
import com.quanzhi.auditapiv2.common.dal.dao.base.IMetabaseDao;
import org.springframework.data.mongodb.core.query.Query;

import java.util.List;

/**
 * 
 * 《过滤事件持久层接口》
 * 
 * 
 * @Project: 
 * @Module ID:
 * @Comments: 
 * @JDK version used:      <JDK1.8> 
 * <AUTHOR> [<EMAIL>]
 * @since 2020-04-08-上午9:56:10
 */
public interface IHttpEventDao extends IMetabaseDao<HttpEvent> {

	/**
	 * 查询过滤事件列表(分页)
	 * @Comments:  <对此方法的描述，可以引用系统设计中的描述>
	 * <AUTHOR> [<EMAIL>]
	 * @since 2020-04-08 9:56
	 * @param
	 * @return
	 */
	List<HttpEvent> selectHttpEventList(String eventFilterPluginId, Integer page, Integer limit) throws Exception;

	/**
	 * 查询过滤事件数量
	 * @Comments:  <对此方法的描述，可以引用系统设计中的描述>
	 * <AUTHOR> [<EMAIL>]
	 * @since 2020-04-08 9:56
	 * @param
	 * @return
	 */
	Long totalCount(String eventFilterPluginId) throws Exception;

	/**
	 * id查询过滤事件详情
	 * @Comments:  <对此方法的描述，可以引用系统设计中的描述>
	 * <AUTHOR> [<EMAIL>]
	 * @since 2020-04-08 9:56
	 * @param
	 * @return
	 */
	HttpEvent selectHttpEventById(String id, String eventFilterPluginId) throws Exception;

	Long count(Query query, String collection);

	<T> List<T> query(Query query, Class<T> aClass,String collection);

	FilterHttpEvent getFilterHttpEventById(String httpEventId);

	FilterHttpEvent findByUri(String uri);
}
