package com.quanzhi.auditapiv2.common.dal.dto.docx;

import com.quanzhi.auditapiv2.common.dal.entity.weakness.Top10Weakness;
import lombok.Data;

/**
 * 《接口弱点dto》
 *
 * <AUTHOR> [<EMAIL>]
 * @Project:
 * @Module ID:
 * @Comments:
 * @JDK version used:      <JDK1.8>
 * @since 2020-08-28-下午16:52:18
 */
@Data
public class OverView {

    /**
     * 导出时间
     */
    private String date = "";

    private String companyName = "";

    private Long highWeaknessNum = 0L;
    private String highWeaknessBlock = "";
    private Long midWeaknessNum = 0L;
    private String midWeaknessBlock = "";
    private Long lowWeaknessNum = 0L;
    private String lowWeaknessBlock = "";

    private Top10Weakness top1;
    private Top10Weakness top2;
    private Top10Weakness top3;
    private Top10Weakness top4;
    private Top10Weakness top5;
    private Top10Weakness top6;
    private Top10Weakness top7;
    private Top10Weakness top8;
    private Top10Weakness top9;
    private Top10Weakness top10;
    private Top10Weakness top11;

}
