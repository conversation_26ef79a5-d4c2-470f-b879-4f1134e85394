package com.quanzhi.auditapiv2.common.dal.mongoCollectionIndex.document;

import com.quanzhi.auditapiv2.common.dal.mongoCollectionIndex.MongoCollectionConstant;
import org.springframework.data.mongodb.core.index.CompoundIndex;
import org.springframework.data.mongodb.core.index.CompoundIndexes;
import org.springframework.data.mongodb.core.index.Indexed;
import org.springframework.data.mongodb.core.mapping.Document;

/**
 * <AUTHOR>
 * @date 2020/11/16 上午11:27
 */

@Document(collection = MongoCollectionConstant.ACCOUNT_DATE_INFO)
@CompoundIndexes({
        @CompoundIndex(name = "date_account", def = "{date:-1,account:-1}",background = true),
        // 账号概览
        @CompoundIndex(name = "date_rspDataDistinctCnt_account", def = "{date:-1,rspDataDistinctCnt:-1,account:-1}",background = true),
        // 文件概览
        @CompoundIndex(name = "date_downloadFileDistinctCnt", def = "{date:-1,downloadFileDistinctCnt:-1}",background = true)
})
public class AccountDateInfoDocument {
    @Indexed(background = true)
    private String account;

    @Indexed(background = true)
    private String date;

    @Indexed(background = true)
    private Long updateTime;

    @Indexed(background = true)
    private Long visitCnt;
}
