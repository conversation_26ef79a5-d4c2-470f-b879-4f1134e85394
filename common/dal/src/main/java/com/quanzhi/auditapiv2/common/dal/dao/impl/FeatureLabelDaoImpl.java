package com.quanzhi.auditapiv2.common.dal.dao.impl;

import com.quanzhi.audit_core.common.model.FeatureLabel;
import com.quanzhi.auditapiv2.common.dal.dao.IFeatureLabelDao;
import com.quanzhi.auditapiv2.common.dal.dao.base.impl.NacosBaseDaoImpl;
import com.quanzhi.auditapiv2.common.util.utils.DataUtil;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository("featureLabelDao")
public class FeatureLabelDaoImpl extends NacosBaseDaoImpl<FeatureLabel> implements IFeatureLabelDao {

    @Override
    public Boolean checkNameExist(String name,String excludeId) {

        List<FeatureLabel> list = getAll();

        for(int i = 0; i < list.size();i++) {

            FeatureLabel featureLabel = list.get(i);

            if(!featureLabel.getId().equals(excludeId) && featureLabel.getName().equals(name) && (DataUtil.isEmpty(featureLabel.getDelFlag()) || !featureLabel.getDelFlag())) {
                return true;
            }
        }

        return false;
    }


}
