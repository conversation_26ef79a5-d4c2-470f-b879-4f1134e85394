package com.quanzhi.auditapiv2.common.dal.dto;

import com.quanzhi.audit_core.common.model.AccountInfo;
import com.quanzhi.audit_core.common.model.IpInfo;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * @author: yangzixian
 * @date: 7/4/2023 10:25
 * @description:
 */
@Data
public class AttackInfoDto {

    @ApiModelProperty(value = "是否未知", name = "unKnow")
    private Boolean unKnow = false;

    @ApiModelProperty(value = "访问次数", name = "visitCnt")
    private long visitCnt;

    @ApiModelProperty(value = "地域", name = "location")
    private List<String> location;

    @ApiModelProperty(value = "账号总数", name = "accountNum")
    private long accountNum;

    @ApiModelProperty(value = "ip总数", name = "accountNum")
    private long ipNum;
    @ApiModelProperty(value = "部署域", name = "networkDomains")
    private List<String> networkDomains;

    @ApiModelProperty(value = "单次返回最大去重数据量", name = "rspMaxDistinctCnt")
    private String rspMaxDistinctCnt;

    @ApiModelProperty(value = "返回数据标签", name = "rspDataLabelList")
    private List<String> rspDataLabelList;

    @ApiModelProperty(value = "风险事件", name = "riskEvents")
    private List<String> riskEvents;

    @ApiModelProperty(value = "终端类型", name = "uaTypes")
    private List<String> uaTypes;

    @ApiModelProperty(value = "阻断状态", name = "blockFlag")
    private Boolean blockFlag;

    @ApiModelProperty(value = "风险等级", name = "riskLevel")
    private Integer riskLevel;

    @ApiModelProperty(value = "风险等级名称", name = "riskLevelName")
    private String riskLevelName;

    @ApiModelProperty(value = "国家", name = "country")
    private String country;

    @ApiModelProperty(value = "省份", name = "province")
    private String province;

    @ApiModelProperty(value = "城市", name = "city")
    private String city;

    @ApiModelProperty(value = "威胁标签", name = "threatLabels")
    private List<String> threatLabels;

    private String staffDepart;
    private String staffName;
    private String staffId;
    private String staffEmail;
    private String staffNickName;
    private String staffBankCard;
    private String staffMobile;
    private String staffRole;
    private String staffChinese;
    private Boolean staffFlag;

    @Mapper
    public interface AttackInfoDtoMapper {

        AttackInfoDto.AttackInfoDtoMapper INSTANCE = Mappers.getMapper(AttackInfoDto.AttackInfoDtoMapper.class);

        @Mapping(target = "networkDomains", source = "accessDomainIds")
        @Mapping(target = "rspMaxDistinctCnt", source = "maxRspDataDistinctCnt")
        @Mapping(target = "accountNum", source = "relatedAccountDistinctCnt")
        AttackInfoDto convert(IpInfo ipInfo);

        @Mapping(target = "rspMaxDistinctCnt", source = "maxRspDataDistinctCnt")
        AttackInfoDto convert(AccountInfo accountInfo);

    }

}
