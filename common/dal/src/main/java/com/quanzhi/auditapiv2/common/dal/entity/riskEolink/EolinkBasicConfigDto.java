package com.quanzhi.auditapiv2.common.dal.entity.riskEolink;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

@Data
public class EolinkBasicConfigDto {
    private String id;
    /**
     * 产品编号
     */
    private String productId;

    /**
     * 产品名称
     */
    private String productName;

    /**
     * 版本号
     */
    private String version;

    /**
     * 版本类型
     */
    @JsonProperty("version_type")
    private String versionType;

    /**
     * 设备唯一编号
     */
    private String uniqueCode;

    /**
     * 产品序列
     */
    @JsonProperty("product_serial")
    private String productSerial;

    /**
     * 开始时间(时间戳)
     */
    @JsonProperty("start_time")
    private String startTime;

    /**
     * 结束时间(时间戳)
     */
    @JsonProperty("end_time")
    private String endTime;

    /**
     * 签发时间(时间戳)
     */
    private String issued;

    /**
     * 当前授权文件状态  0:未授权  1:已授权  2:已过期
     */
    private Integer state = 0;

    /**
     * 产品组件模块
     */
    private String modules;

    private String company;

    private String updateTime;

    private String createTime;

    //是否删除 1删除 0未删除
    private Integer isDel = 0;

}
