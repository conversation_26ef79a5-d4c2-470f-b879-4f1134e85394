package com.quanzhi.auditapiv2.common.dal.entity.riskEolink;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;
import java.util.Map;

@Data
public class StrategyConfig {
    /**
     * allow | [string] | 允许访问 | 默认值
     * refuse | [string] | 拒绝访问 | -
     */
    @ApiModelProperty(value = "访问规则，当strategy_type为visit值，该配置必填", name = "visitRule")
    @JsonProperty("visit_rule")
    private String visitRule;

    @ApiModelProperty(value = "是否继续匹配其他策略", name = "continues")
    @JsonProperty("continue")
    private Boolean continues;
    @ApiModelProperty(value = "生效范围，当strategy_type为visit值，该配置必填", name = "influenceSphere")
    @JsonProperty("influence_sphere")
    private Map<String, List<String>> influenceSphere;
    @ApiModelProperty(value = "[]string 维度，当strategy_type为limiting值，该配置必填", name = "metrics")
    private List<String> metrics;
    @ApiModelProperty(value = "限流次数配置，当strategy_type为limiting值，该配置有效", name = "query")
    private Traffic query;
    @ApiModelProperty(value = "限报文总量配置，当strategy_type为limiting值，该配置有效", name = "traffic")
    private Traffic traffic;
    @ApiModelProperty(value = "拦截后返回响应，当strategy_type为limiting值，该配置必填", name = "response")
    private Response response;

    @Data
    public static class Response{
        /**
         * HTTP响应状态码
         */
        @ApiModelProperty(value = "HTTP响应状态码", name = "statusCode")
        @JsonProperty("status_code")
        private int statusCode;
        /**
         * 响应Content-Type
         */
        @ApiModelProperty(value = "响应Content-Type", name = "contentType")
        @JsonProperty("content_type")
        private String contentType;
        /**
         * 响应内容格式
         */
        @ApiModelProperty(value = "响应内容格式", name = "charset")
        private String charset;
        /**
         * 响应头部参数
         */
        @ApiModelProperty(value = "响应头部参数", name = "header")
        private List<Header> header;
        /**
         * 响应体内容
         */
        @ApiModelProperty(value = "响应体内容", name = "body")
        private String body;
    }
    @Data
    class Header{
        private String key;
        private String value;
    }
    @Data
    public static class Traffic{
        /**
         * 每秒限报文总量
         */
        @ApiModelProperty(value = "每秒限报文总量", name = "second")
        private int second = 0;
        /**
         * 每分钟限报文总量
         */
        @ApiModelProperty(value = "每分钟限报文总量", name = "minute")
        private int minute = 0;
        /**
         * 每小时限报文总量
         */
        @ApiModelProperty(value = "每小时限报文总量", name = "hour")
        private int hour = 0;
    }
}
