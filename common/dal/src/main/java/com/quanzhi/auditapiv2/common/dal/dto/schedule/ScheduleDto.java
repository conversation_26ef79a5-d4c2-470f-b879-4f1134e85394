package com.quanzhi.auditapiv2.common.dal.dto.schedule;

import com.quanzhi.audit.mix.schdule.domain.entity.BlockStrategy;
import com.quanzhi.audit.mix.schdule.domain.entity.Schedule;
import com.quanzhi.audit.mix.schdule.domain.entity.TriggerStatus;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * @author: yangzixian
 * @date: 2023.08.21 10:38
 * @description:
 */
@Data
public class ScheduleDto {

    @ApiModelProperty(value = "id", name = "id")
    private String id;

    @ApiModelProperty(value = "任务名称", name = "name")
    private String name;

    @ApiModelProperty(value = "运行中任务数量", name = "runTaskNum")
    private Long runTaskNum;

    @ApiModelProperty(value = "运行成功次数", name = "runSuccessNum")
    private Long runSuccessNum;

    @ApiModelProperty(value = "运行失败次数", name = "runFailNum")
    private Long runFailNum;

    @ApiModelProperty(value = "任务状态(OPEN / CLOSE)", name = "triggerStatus")
    private TriggerStatus triggerStatus;

    @ApiModelProperty(value = "任务运行周期", name = "cron")
    private String cron;

    @ApiModelProperty(value = "创建时间", name = "createTime")
    private Long createTime;

    @ApiModelProperty(value = "任务描述", name = "description")
    private String description;

    @ApiModelProperty(value = "任务id", name = "executor")
    private String executor;

    @ApiModelProperty(value = "任务所属组件", name = "app")
    private String app;

    @ApiModelProperty(value = "更新时间", name = "updateTime")
    private Long updateTime;

    @ApiModelProperty(value = "是否永远关闭", name = "alwaysClose")
    private Boolean alwaysClose;

    @Mapper
    public interface ScheduleDtoMapper {

        ScheduleDto.ScheduleDtoMapper INSTANCE = Mappers.getMapper(ScheduleDto.ScheduleDtoMapper.class);

        ScheduleDto convert(Schedule schedule);

    }

}
