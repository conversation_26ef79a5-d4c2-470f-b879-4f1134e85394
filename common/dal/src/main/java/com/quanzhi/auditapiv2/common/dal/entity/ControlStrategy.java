package com.quanzhi.auditapiv2.common.dal.entity;

import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * create at 2022/12/16 11:25 上午
 * @description: 管控策略
 **/
@Data
public class ControlStrategy extends BaseEntity {

    /**
     * 管控策略名称
     */
    private String controlName;
    /**
     * 管控类型
     */
    private ControlType controlType;
    /**
     * 管控内容
     */
    private List<String> controlContents;
    /**
     * 策略描述
     */
    private String strategyDesc;
    /**
     * 管控策略
     */
    private List<StrategyPolicy> strategyPolicies;
    /**
     * 管控策略表达式
     */
    private String strategyMathExpre;
    /**
     * 是否开启
     */
    private Boolean enable;


    @Data
    public static class StrategyPolicy {
        private String policyIndex;
        private String policyType;
        private List<Object> policyValue;

    }

    public enum ControlType {
        IP
    }


    public enum PolicyType {
        SOURCE_IP("SOURCE_IP", "SOURCE_IP", "源IP"),
        SOURCE_IP_HOST("SOURCE_IP_HOST","SOURCE_IP","源IP和域名"),
        RISK_LEVEL("RISK_LEVEL", "RISK", "风险等级"),
        RISK_POLICY("RISK_POLICY", "RISK", "风险策略");

        private String id;
        private String parent;
        private String name;

        PolicyType(String id, String parent, String name) {
            this.id = id;
            this.parent = parent;
            this.name = name;
        }

        public String getId() {
            return id;
        }

        public String getParent() {
            return parent;
        }

        public String getName() {
            return name;
        }
    }

}