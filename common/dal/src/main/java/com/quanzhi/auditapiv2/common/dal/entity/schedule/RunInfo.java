package com.quanzhi.auditapiv2.common.dal.entity.schedule;

import lombok.Data;

import java.util.Map;

/**
 * @author: yangzixian
 * @date: 2023.10.23 17:40
 * @description:
 */
@Data
public class RunInfo {

    private String executor;

    private Map<String, Long> runInfo;

    public RunInfo(String executor, Map<String, Long> runInfo) {
        this.executor = executor;
        this.runInfo = runInfo;
    }

}
