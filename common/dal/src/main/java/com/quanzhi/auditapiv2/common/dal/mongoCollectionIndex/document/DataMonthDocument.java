package com.quanzhi.auditapiv2.common.dal.mongoCollectionIndex.document;

import org.springframework.data.mongodb.core.index.CompoundIndex;
import org.springframework.data.mongodb.core.index.CompoundIndexes;
import org.springframework.data.mongodb.core.index.Indexed;
import org.springframework.data.mongodb.core.mapping.Document;

import java.util.Date;

@Document(collection = "dataLabelMonthInfo")
@CompoundIndexes({
        @CompoundIndex(name = "dataId_month", def = "{dataId: 1, month: 1}", background = true),
})
public class DataMonthDocument {
    @Indexed(background = true)
    private Long updateTime;

    @Indexed(expireAfter = "360d")
    private Date createDate;
}
