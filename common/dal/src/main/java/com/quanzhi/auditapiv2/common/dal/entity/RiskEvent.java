package com.quanzhi.auditapiv2.common.dal.entity;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * @ClassName RiskEvent
 * @Description TODO：异常事件
 * <AUTHOR>
 * @Date 2020/12/24 11:53
 **/
@Data
public class RiskEvent implements Serializable {

    /**
     * 异常时间
     */
    private String riskTime;

    /**
     * 异常规则
     */
    private String name;

    /**
     * 异常主体
     */
    private List<Object> entity;

    /**
     * 异常等级
     */
    private String level;

    /**
     * 异常状态
     */
    private String state;

    /**
     * 最近发现时间
     */
    private String lastTime;

}
