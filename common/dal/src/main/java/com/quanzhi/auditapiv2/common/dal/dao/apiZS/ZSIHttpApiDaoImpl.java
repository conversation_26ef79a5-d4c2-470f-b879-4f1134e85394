package com.quanzhi.auditapiv2.common.dal.dao.apiZS;

import com.quanzhi.auditapiv2.common.dal.entity.apiZS.CountAppDomainEntity;
import com.quanzhi.auditapiv2.common.dal.entity.apiZS.CountDataLabelAppEntity;
import lombok.extern.slf4j.Slf4j;
import org.bson.Document;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.aggregation.*;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.stereotype.Repository;

import java.util.*;

import static org.springframework.data.mongodb.core.aggregation.Aggregation.*;

@Slf4j
@Repository()
public class ZSIHttpApiDaoImpl implements ZSIHttpApiDao {

    @Autowired
    private MongoTemplate mongoTemplate;

    private String collectionName = "httpApi";

    @Override
    public List<String> selectAppTop20(List<String> listLabel) {
        if(listLabel==null){
            log.info("传入参数listLabel为空");
            return null;
        }
        List<String> strings = new ArrayList<>();
        MatchOperation matchOperation = match(Criteria.where("rspDataLabels").in(listLabel).and("delFlag").is(false));

        GroupOperation groupOperation = group("appUri")
                .addToSet("uri").as("apiUriSet");

        ProjectionOperation projectionOperation = Aggregation.project()
                .and("_id").as("appUri")
                .and("apiUriSet").size().as("totalApi")
                .andExclude("_id");
        SortOperation sort = Aggregation.sort(org.springframework.data.domain.Sort.Direction.DESC, "totalApi");

        Aggregation aggregation = Aggregation.newAggregation(
                matchOperation,
                groupOperation,
                projectionOperation,
                sort,
                limit(20)
        );
        List<Document> results = mongoTemplate.aggregate(aggregation, collectionName, Document.class).getMappedResults();
        for (Document doc : results) {
            String appUri = doc.getString("appUri");
            strings.add(appUri);
        }
        return strings;
    }

    @Override
    public List<String> selectNetworkTop10(List<String> listLabel) {
        if(listLabel==null){
            log.info("传入参数listLabel为空");
            return null;
        }
        List<String> stringNetwork = new ArrayList<>();
        MatchOperation matchOperation = match(Criteria.where("rspDataLabels").in(listLabel).and("delFlag").is(false));

        UnwindOperation unwindOperation = unwind("$visitDomains");

        GroupOperation groupOperation = group("visitDomains")
                .addToSet("uri").as("apiUriSet");

        ProjectionOperation projectionOperation = project()
                .and("_id").as("visitDomain")
                .and("apiUriSet").size().as("totalApi");

        SortOperation sort = Aggregation.sort(org.springframework.data.domain.Sort.Direction.DESC, "totalApi");

        Aggregation aggregation = newAggregation(
                matchOperation,
                unwindOperation,
                groupOperation,
                projectionOperation,
                sort,
                limit(10)
        );

        List<Document> results = mongoTemplate.aggregate(aggregation, collectionName, Document.class).getMappedResults();

        for (Document doc : results) {
            String visitDomain = doc.getString("visitDomain");
            stringNetwork.add(visitDomain);
        }
        return stringNetwork;
    }


    @Override
    public Map<String, Integer> selectApiCountTop10() {
        Map<String, Integer> stringLabel = new HashMap<>();
        UnwindOperation unwind = Aggregation.unwind("$rspDataLabels");
        MatchOperation delFlag = match(Criteria.where("delFlag").is(false));
        GroupOperation groupOperation = group("$rspDataLabels").addToSet("$uri").as("apiUriSet");
        ProjectionOperation projectionOperation = Aggregation.project()
                .and("_id").as("rspDataLabel")
                .and("apiUriSet").size().as("totalApi")
                .andExclude("_id");
        SortOperation sort = Aggregation.sort(org.springframework.data.domain.Sort.Direction.DESC, "totalApi");
        LimitOperation limit = limit(10);
        Aggregation aggregation = Aggregation.newAggregation(unwind,delFlag,groupOperation, projectionOperation, sort, limit).withOptions(AggregationOptions.builder()
                .allowDiskUse(true)
                .build());;
        List<Document> results = mongoTemplate.aggregate(aggregation, collectionName, Document.class).getMappedResults();

        for (Document doc : results) {
            stringLabel.put(doc.getString("rspDataLabel"), doc.getInteger("totalApi"));;
        }
        return stringLabel;
    }

    @Override
    public List<CountDataLabelAppEntity> selectApiAndAppCount(List<String> labelTop, List<String> appList) {
        if(labelTop==null){
            log.info("查询条件labelTop为空！");
            return null;
        }
        if(appList==null){
            log.info("查询条件appList为空！");
            return null;
        }
        MatchOperation match1 = match(new org.springframework.data.mongodb.core.query.Criteria("appUri").in(appList).and("delFlag").is(false));
        UnwindOperation unwind = Aggregation.unwind("rspDataLabels");
        GroupOperation group = group("rspDataLabels", "appUri").addToSet("uri").as("apiUriSet");
        ProjectionOperation projection = Aggregation.project()
                .and("_id.appUri").as("appUri")
                .and("_id.rspDataLabels").as("rspDataLabel")
                .and("apiUriSet").size().as("totalApi")
                .andExclude("_id");
        MatchOperation match2 = match(new org.springframework.data.mongodb.core.query.Criteria("rspDataLabel").in(labelTop).and("appUri").in(appList));
//        SortOperation sort = Aggregation.sort(org.springframework.data.domain.Sort.Direction.DESC, "totalApi");

        List<AggregationOperation> operations = Arrays.asList(match1, unwind, group, projection, match2);
        Aggregation aggregationApiAndApp = Aggregation.newAggregation(operations).withOptions(AggregationOptions.builder()
                .allowDiskUse(true)
                .build());

        AggregationResults<CountDataLabelAppEntity> aggregateApiAndApp = mongoTemplate.aggregate(aggregationApiAndApp, collectionName, CountDataLabelAppEntity.class);
        List<CountDataLabelAppEntity> CountDataLabelApps = aggregateApiAndApp.getMappedResults();
        return CountDataLabelApps;
    }


    @Override
    public List<CountAppDomainEntity> selectAppAndDomainCount(List<String> labelTop,List<String> appList, List<String> visitDomains) {

        if(labelTop==null){
            log.info("查询条件labelTop为空！");
            return null;
        }
        if(appList==null){
            log.info("查询条件appList为空！");
            return null;
        }
        if(visitDomains==null){
            log.info("查询条件visitDomains为空！");
            return null;
        }
        //定义匹配条件
        Criteria matchCriteria = Criteria.where("appUri").in(appList)
                .and("rspDataLabels").in(labelTop).and("delFlag").is(false);
        // 构建聚合管道
        Aggregation aggregation = Aggregation.newAggregation(
                match(matchCriteria),
                Aggregation.unwind("visitDomains"),
                group("visitDomains", "appUri")
                        .addToSet("uri").as("apiUriSet"),
                Aggregation.project()
                        .and("_id.appUri").as("appUri")
                        .and("_id.visitDomains").as("visitDomains")
                        .and(ArrayOperators.Size.lengthOfArray("$apiUriSet")).as("totalApi"),
                match(Criteria.where("visitDomains").in(visitDomains)
                        .and("appUri").in(appList))
//                Aggregation.sort(Sort.Direction.DESC, "totalApi")
        );

        // 执行聚合操作
        AggregationResults<CountAppDomainEntity> results = mongoTemplate.aggregate(aggregation, collectionName, CountAppDomainEntity.class);
        List<CountAppDomainEntity> resultDocuments = results.getMappedResults();
        return resultDocuments;
    }




}
