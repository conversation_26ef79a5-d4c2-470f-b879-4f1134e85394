package com.quanzhi.auditapiv2.common.dal.dao.impl;

import com.quanzhi.auditapiv2.common.dal.dao.IScheduleJobStatusDao;
import com.quanzhi.auditapiv2.common.dal.dao.base.impl.BaseDaoImpl;
import com.quanzhi.auditapiv2.common.dal.entity.ScheduleJobStatus;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.stereotype.Repository;

/**
 * @author: zhousong
 * @data: 2018/3/4
 */
@Repository("scheduleStatusDao")
public class ScheduleJobStatusDaoImpl extends BaseDaoImpl<ScheduleJobStatus> implements IScheduleJobStatusDao {

    @Autowired
    private MongoTemplate mongoTemplate;

    @Override
    public void upsert(ScheduleJobStatus jobStatus) {
        Criteria where = Criteria.where("job").is(jobStatus.getJob()).and("date").is(jobStatus.getDate());
        Update update = Update.update("lastEventTime", jobStatus.getLastEventTime());
        mongoTemplate.upsert(new Query(where), update, ScheduleJobStatus.class);
    }

    @Override
    public ScheduleJobStatus query(String job) {
        Criteria criteria = Criteria.where("job").is(job);
        return mongoTemplate.findOne(new Query(criteria), ScheduleJobStatus.class);
    }

    @Override
    public ScheduleJobStatus query(String job, String date) {
        Criteria criteria = Criteria.where("job").is(job).and("date").is(date);
        return mongoTemplate.findOne(new Query(criteria), ScheduleJobStatus.class);
    }
}
