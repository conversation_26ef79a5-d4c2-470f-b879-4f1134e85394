package com.quanzhi.auditapiv2.common.dal.entity;

import com.quanzhi.auditapiv2.common.dal.enums.NacosContentTypeEnum;
import lombok.AllArgsConstructor;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2020/12/29 上午10:36
 *
 * 定义nacos中单个值改动的配置
 */
@AllArgsConstructor
@Data
public class NacosSingleKeyConfig {

    /**
     * nacos的dataId
     */
    private String dataId;

    /**
     * nacos的group
     */
    private String group;

    /**
     * @see NacosContentTypeEnum
     */
    private String contentType;

    /**
     * 在存储的数据是个jsonArray的时候，需要一个key先定位到具体的对象
     */
    private String matchKey;

    /**
     * matchKey定位的value
     */
    private String matchKeyValue;

    /**
     * 所需要改变的valueKey
     */
    private String matchValue;
}
