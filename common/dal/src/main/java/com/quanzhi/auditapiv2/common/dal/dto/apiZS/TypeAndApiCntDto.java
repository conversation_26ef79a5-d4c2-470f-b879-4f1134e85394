package com.quanzhi.auditapiv2.common.dal.dto.apiZS;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@ApiModel("api标签类型和访问量")
public class TypeAndApiCntDto {
    @ApiModelProperty("标签类型")
    private String type;
    @ApiModelProperty("访问量")
    private Integer labelApiCnt;
}
