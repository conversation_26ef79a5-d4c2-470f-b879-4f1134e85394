package com.quanzhi.auditapiv2.common.dal.entity;

import com.quanzhi.metabase.core.model.Entity;
import com.quanzhi.metabase.core.model.http.HttpResourceConstant;
import lombok.Data;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2020/10/21 下午5:57
 */
@Data
@Entity(HttpResourceConstant.ACCOUNT_DATE_INFO)
@Document("accountDateInfo")
public class AccountDateInfo implements Serializable {

    private String date;

    private List<String> appUriList;

    private List<String> apiUriList;

    @Id
    private String id;

    private String account;

    /**
     * 部门
     */
    private String staffDepart;

    /**
     * 姓名
     */
    private String staffName;

    /**
     * 员工Id
     */
    private String staffId;

    /**
     * 邮箱
     */
    private String staffEmail;

    /**
     * 昵称
     */
    private String staffNickName;

    /**
     * 银行卡号
     */
    private String staffBankCard;

    /**
     * 手机号码
     */
    private String staffMobile;

    /**
     * 姓名
     */
    private String staffChinese;

    /**
     * 身份证号
     */
    private String staffIdCard;

    /**
     * 角色
     */
    private String staffRole;

    private Long visitCnt;

    /**
     * 单次最大返回数据量
     */
    private Long maxRspDataDistinctCnt;

    /**
     * 单次最大返回数据量时间
     */
    private String maxRspDataDistinctCntDate;
    /**
     * 返回数据累计
     */
    private Long rspDataDistinctCnt;

    /**
     * 返回数据标签
     */
    private List<String> rspDataLabelList;

    /**
     * 首次发现时间
     */
    private String firstDate;

    /**
     * 最近使用时间
     */
    private String lastDate;

    /**
     * 登录次数
     */
    private Long loginCnt;

    /**
     * 账号下载文件去重数
     */
    private Long downloadFileDistinctCnt;

    private List<String> eventDefineIdList;

    /**
     * 按终端类型分组的终端列表
     */
    private Map<String, List<String>> uaByUaType;

    //ua 去重列表
    private List<String> uaTypes;

    /**
     * 关联IP清单，限制在100以内
     */
    private List<String> relatedIpList;

    private Long updateTime;
}
