package com.quanzhi.auditapiv2.common.dal.entity;

import lombok.Data;

/**
 * <AUTHOR>
 * @description  加密流量监控信息
 * @date 2023/8/10 15:05
 */
@Data
public class GatewaySSLInfo {

    private String id;

    private Long time;

    /**
     * 网关ip
     */
    private String ip;

    /**
     * 加密流量
     */
    private Long sslParserBits;

    /**
     * 加密流量速率
     */
    private Long sslParserBitsRate;

    /**
     * ssl事件
     */
    private Long sslCnt;

    /**
     * http事件
     */
    private Long httpCnt;

    /**
     * ftp事件
     */
    private Long ftpCnt;

    /**
     * 事件总数
     */
    private Long totalCnt;

    /*-------------累加-------------------*/

    /**
     * ssl事件累加
     */
    private Long sslCntIncrease;

    /**
     * http事件
     */
    private Long httpCntIncrease;

    /**
     * ftp事件
     */
    private Long ftpCntIncrease;

    /**
     * 事件总数
     */
    private Long totalCntIncrease;

}
