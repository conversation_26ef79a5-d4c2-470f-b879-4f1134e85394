package com.quanzhi.auditapiv2.common.dal.dto;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.List;

/**
 *
 * 《导出标题dto》
 *
 *
 * @Project:
 * @Module ID:
 * @Comments:
 * @JDK version used:      <JDK1.8>
 * <AUTHOR> [<EMAIL>]
 * @since 2020-08-28-下午16:52:18
 */
@Data
@EqualsAndHashCode
public class ExporTitleFieldDto implements Serializable {

    /**
     * 标题
     */
    private String title;
    
    /**
     * 字段名
     */
    private String field;

    /**
     * 自定义字段集合
     */
    private List<String> customFieldList;
}
