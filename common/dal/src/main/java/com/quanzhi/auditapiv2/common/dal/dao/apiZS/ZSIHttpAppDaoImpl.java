package com.quanzhi.auditapiv2.common.dal.dao.apiZS;


import com.quanzhi.metabase.core.model.http.HttpAppResource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Repository;

import java.util.List;

@Slf4j
@Repository()
public class ZSIHttpAppDaoImpl implements ZSIHttpAppDao {

    @Autowired
    private MongoTemplate mongoTemplate;

    private String collectionName = "httpApp";

    /**
     *根据查询条件condition，获得api访问量前20的应用
     * @param condition:
     * @return: java.util.List<com.quanzhi.metabase.core.model.http.HttpAppResource>
     * @Author: 胡政乡
     * @Date: 2023/11/2 11:31
     **/
    @Override
    public List<HttpAppResource> selectAppVisitsTop(String condition){

        Query query = Query.query(org.springframework.data.mongodb.core.query.Criteria.where("uri").regex(condition, "i"))
                .with(org.springframework.data.domain.Sort.by(org.springframework.data.domain.Sort.Direction.DESC,"appStat.totalVisits"))
                .limit(20);
        List<HttpAppResource> httpAppResources = mongoTemplate.find(query, HttpAppResource.class, collectionName);
        return httpAppResources;
    }

    /**
     *appUri批量查询应用详情
     * @param uriList:
     * @return: java.util.List<com.quanzhi.metabase.core.model.http.HttpAppResource>
     * @Author: 胡政乡
     * @Date: 2023/11/2 11:35
     **/
    @Override
    public List<HttpAppResource> selectAppUriList(List<String> uriList) {
        Query query = new Query();
        query.addCriteria(Criteria.where("uri").in(uriList));
        List<HttpAppResource> httpAppResources = mongoTemplate.find(query, HttpAppResource.class, collectionName);
        return httpAppResources;
    }

}
