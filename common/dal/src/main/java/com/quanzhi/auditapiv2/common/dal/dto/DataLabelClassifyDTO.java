package com.quanzhi.auditapiv2.common.dal.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @class DataLabelClassifyDTO
 * @created 2024/7/9 17:40
 * @desc
 **/
@ApiModel
@Data
public class DataLabelClassifyDTO {

    /**
     * 模板 ID
     */
    @ApiModelProperty("模板ID")
    private String id;
    /**
     * 分类
     */
    @ApiModelProperty("分类")
    private List<String> classifies;
}
