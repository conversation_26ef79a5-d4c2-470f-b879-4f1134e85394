package com.quanzhi.auditapiv2.common.dal.dto;

import com.quanzhi.auditapiv2.common.dal.entity.NotifyConfig;
import lombok.Data;

import java.util.List;

/**
 *
 * 《普罗米修斯推送数据格式》
 *
 *
 * @Project:
 * @Module ID:
 * @Comments:
 * @JDK version used:      <JDK1.8>
 * <AUTHOR> [<EMAIL>]
 * @since 2020-04-08-上午9:56:10
 */
@Data
public class PrometheusDto {

    private Integer dashboardId;

    private List<EvalMatche> evalMatches;
    
    private Integer orgId;
    
    private Integer panelId;
    
    private Integer ruleId;

    private String ruleName;

    private String ruleUrl;

    /*
     * 状态（alerting-告警，ok-恢复）
     * @see com.quanzhi.auditapiv2.common.dal.dto.PrometheusDto.StateEnum
     */
    private String state;

    private Tags tags;

    private String title;

    private Object ext;

    @Data
    public static class EvalMatche {

        /*
         * 服务器地址
         */
        private String ip;

        /*
         * 数据百分比
         */
        private Double value;

        /*
         * 告警信息
         */
        private String metric;

        /*
         * 服务器信息
         */
        private Server tags;

        /*
         * 磁盘/内存使用信息
         */
        private AdditiveMetric additiveMetric;
    }

    @Data
    public static class Tags {

        /*
         * 阈值
         */
        private String threshold;

        /*
         * 持续时间
         */
        private String duration;

        /*
         * 消息类型
         */
        private NotifyConfig.NotifyCodeEnum alertType;

        /*
         * 组件名
         */
        private String componentName;

        /*
         * 告警等级
         */
        private String level;
        
        
    }

    @Data
    public static class Server {

        private String __name__;

        private String attr;

        private String exported_job;

        private String index;

        private String device;

        private String fstype;

        /*
         * 服务地址
         */
        private String instance;

        private String job;

        private String mountpoint;
    }

    @Data
    public static class AdditiveMetric {

        /*
         * 总空间大小，单位字节
         */
        private Long totalSizeBytes;

        /*
         * 已使用空间大小，单位字节
         */
        private Long usedSizeBytes;

        /*
         * 可用空间大小，单位字节
         */
        private Long availableSizeBytes;

        /*
         * 总内存大小，单位字节
         */
        private Long totalMemoryBytes;
        /*
         * 总内存大小，单位字节
         */
        private Long usedMemoryBytes;
        /*
         * 总内存大小，单位字节
         */
        private Long availableMemoryBytes;

        /*
         * 起始时间戳，精确到毫秒
         */
        private Long beginTime;

        /*
         * 结束时间戳，精确到毫秒
         */
        private Long endTime;
    }

    public enum StateEnum {

        /**
         * 告警
         */
        ALERTING("alerting"),

        /**
         * 恢复
         */
        OK("ok");

        String name;

        StateEnum(String name){

            this.name = name;
        }

        public String getName(){
            return name;
        }
    }
}
