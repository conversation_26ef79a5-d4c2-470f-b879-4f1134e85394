package com.quanzhi.auditapiv2.common.dal.dao.impl;

import com.quanzhi.auditapiv2.common.dal.dao.ICustomFieldsDao;
import com.quanzhi.auditapiv2.common.dal.dao.base.impl.BaseDaoImpl;
import com.quanzhi.auditapiv2.common.dal.entity.CustomFields;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * @author: yang<PERSON>xian
 * @date: 28/3/2023 16:17
 * @description:
 */
@Repository
public class CustomFieldsDaoImpl extends BaseDaoImpl<CustomFields> implements ICustomFieldsDao {

    private final MongoTemplate mongoTemplate;

    private String collectionName = "customFields";

    public CustomFieldsDaoImpl(MongoTemplate mongoTemplate) {
        this.mongoTemplate = mongoTemplate;
    }

    @Override
    public List<CustomFields> findCustomFieldsByType(String type) {
        return mongoTemplate.find(new Query().addCriteria(Criteria.where("type").is(type)), CustomFields.class, collectionName);
    }
}
