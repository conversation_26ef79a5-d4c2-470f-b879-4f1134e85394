package com.quanzhi.auditapiv2.common.dal.dto;

import com.quanzhi.audit_core.common.model.PluginPolicy;
import com.quanzhi.audit_core.common.model.WeaknessRule;
import com.quanzhi.auditapiv2.common.dal.enums.WeaknessType;
import com.quanzhi.re.core.domain.entity.po.MatchRule;
import lombok.Data;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 *
 * 《弱点规则dto》
 *
 *
 * @Project:
 * @Module ID:
 * @Comments:
 * @JDK version used:      <JDK1.8>
 * <AUTHOR> [<EMAIL>]
 * @since 2020-08-28-下午16:52:18
 */
@Data
public class WeaknessRuleDto {

    private String id;

    private String subType;

    private WeaknessType weaknessType;

    /**
     * 等级
     */
    private Integer level;

    /**
     * 开关
     */
    private Boolean enable;

    /**
     * 规则
     */
    private MatchRule matchRule;

    /**
     * 规则编码
     */
    private String code;

    /**
     * 规则名称
     */
    private String name;

    /**
     * 规则类别
     * @see com.quanzhi.audit_core.common.model.WeaknessRule.TypeEnum
     */
    private String type;

    /**
     * 插件策略
     */
    private List<PluginPolicy> pluginPolicies;

    /**
     * 规则描述
     */
    private String description;

    /**
     * 弱点定义
     */
    private String definition;

    /**
     * 被利用方式
     */
    private String utilizedWay;

    /**
     * 被利用后的影响
     */
    private String utilizedInfluenceDesc;

    /**
     * 处理建议
     */
    private String solution;

    /**
     * 应用黑名单
     */
    private List<String> appBlackList;

    /**
     * 接口黑名单
     */
    private List<String> apiBlackList;

    /**
     * 创建人
     */
    private String creator;

    /**
     * 创建时间
     */
    private Long createTime;

    /**
     * 接口弱点数量
     */
    private Long apiWeaknessCount;

    @Mapper
    public interface WeaknessRuleDtoMapper {

        WeaknessRuleDtoMapper INSTANCE = Mappers.getMapper(WeaknessRuleDtoMapper.class);

        WeaknessRuleDto convert(WeaknessRule WeaknessRule);

    }
}
