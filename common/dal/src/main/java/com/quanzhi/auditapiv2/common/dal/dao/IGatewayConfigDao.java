package com.quanzhi.auditapiv2.common.dal.dao;

import com.quanzhi.auditapiv2.common.dal.dao.base.IBaseDao;
import com.quanzhi.auditapiv2.common.dal.entity.GatewayConfig;

import java.util.List;

/**
 *
 * 《网关配置持久层接口》
 *
 *
 * @Project:
 * @Module ID:
 * @Comments:
 * @JDK version used:      <JDK1.8> 
 * <AUTHOR> [<EMAIL>]
 * @since 2020-04-08-上午9:56:10
 */
public interface IGatewayConfigDao extends IBaseDao<GatewayConfig> {
    
    GatewayConfig getByIp(String gatewayIp);

    List<GatewayConfig> selectGatewayConfigPage(Integer page, Integer limit);

    Long count();

    void deleteById(String id);

}
