package com.quanzhi.auditapiv2.common.dal.entity;

import com.quanzhi.metabase.core.model.BaseEntity;
import lombok.Data;

import java.util.List;

/**
 *
 * 《通知》
 *
 *
 * @Project:
 * @Module ID:
 * @Comments:
 * @JDK version used:      <JDK1.8> 
 * <AUTHOR> [<EMAIL>]
 * @since 2020-04-08-上午9:56:10
 */
@Data
public class Notify extends BaseEntity {

    /**
     * 唯一编码
     */
    private String code;
    
    /**
     * 标题
     */
    private String title;
    
    /**
     * 内容
     */
    private String content;

    /**
     * 服务器地址
     */
    private String ip;

    /**
     * 通知对象
     */
    private List<String> notifyObject;
    
    /**
     * 通知类型
     */
    private NotifyConfig.NotifyTypeEnum type;

    /**
     * 通知来源
     */
    private String notifySource;

    /**
     * 发送方式
     */
    private List<NotifyConfig.NotifyMethodEnum> methods;

    /**
     * 通知名称数值
     */
    private String value;

    /**
     * 是否显示填充数据
     */
    private Boolean isShow;

    /**
     * 通知名称数值单位
     */
    private String unit;

    /**
     * 是否发送通知
     */
    private Boolean isSend;
    
    /**
     * 已读状态
     */
    private ReadStateEnum readState;
    
    /**
     * 已读时间
     */
    private Long readTime;
    
    /**
     * 通知发送时间
     */
    private Long notifyTime;
    
    /**
     * 触发时间
     */
    private Long triggerTime;
    
    /**
     * 额外信息
     */
    private String extraData;

    private String serviceType;
    /**
     * 间隔时间
     */
    private Long intervalSeconds;

    public enum ReadStateEnum {

        /**
         * 未读
         */
        UNREAD("未读"),

        /**
         * 已读
         */
        READ("已读");

        String name;

        ReadStateEnum(String name){
            this.name = name;
        }

        public String getName(){
            return name;
        }
    }
}
