package com.quanzhi.auditapiv2.common.dal.dao;

import com.quanzhi.auditapiv2.common.dal.dao.base.IBaseDao;
import com.quanzhi.auditapiv2.common.dal.dto.SysUserSearchDto;
import com.quanzhi.auditapiv2.common.dal.entity.SysUserModel;
import org.springframework.data.mongodb.core.query.Criteria;

import java.util.List;
import java.util.Map;

/**
 * Created by she<PERSON><PERSON> on 2017/11/9.
 */
public interface ISysUserDao extends IBaseDao<SysUserModel> {
    /**
     * 获取用户列表
     * @return
     */
    List<SysUserModel> selectUserList();

    /**
     * 根据用户名获取用户信息
     * @param username
     * @return
     */
    SysUserModel getUserInfoByName(String username);

    /**
     * 更新用户错误信息
     * @param username
     * @param sysUserModel
     * @return
     */
    boolean updateUserErrorInfo(String username, SysUserModel sysUserModel);

    /**
     * 更新用户账号和密码
     * @param username
     * @param password
     * @return
     * @throws Exception
     */
    boolean updateUserPwd(String username, String password);

    /**
     * 更新用户的邮件地址
     * @param username
     * @param mailAddr
     */
    void updateMailAddr(String username, String mailAddr);

    /**
     * 根据筛选条件获取where
     * @param searchDto
     * @return
     * @throws Exception
     */
    Criteria getWhereBySearch(SysUserSearchDto searchDto) throws Exception;

    /**
     * 获取记录数
     * @param searchDto
     * @return
     * @throws Exception
     */
    Long getCount(SysUserSearchDto searchDto) throws Exception;

    /**
     * 获取记录列表
     * @param searchDto
     * @param start
     * @param limit
     * @return
     * @throws Exception
     */
    List<SysUserModel> getList(SysUserSearchDto searchDto, Integer start, Integer limit) throws Exception;

    /**
     * 根据Id获取用户信息
     * @param id
     * @return
     * @throws Exception
     */
    SysUserModel getDataById(String id) throws Exception;

    /**
     * 插入用户信息
     * @param sysUserModel
     * @return
     * @throws Exception
     */
    SysUserModel insertData(SysUserModel sysUserModel) throws Exception;

    /**
     * 更新用户信息
     * @param sysUserModel
     * @param id
     * @return
     * @throws Exception
     */
    SysUserModel updateDataById(SysUserModel sysUserModel, String id) throws Exception;

    /**
     * 更新用户信息
     * @param params
     * @param id
     * @return
     * @throws Exception
     */
    SysUserModel updateDataById(Map<String, Object> params, String id) throws Exception;

    /**
     * 批量删除
     * @param idList
     * @return
     * @throws Exception
     */
    Boolean deleteByIdList(List<String> idList) throws Exception;
}
