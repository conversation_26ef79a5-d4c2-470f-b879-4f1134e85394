package com.quanzhi.auditapiv2.common.dal.dao;

import com.quanzhi.auditapiv2.common.dal.dao.base.IMetabaseDao;
import com.quanzhi.metabase.core.model.http.HttpAppSnapshot;

import java.util.List;

/**
 * 
 * 《应用快照持久层接口》
 * 
 * 
 * @Project: 
 * @Module ID:
 * @Comments: 
 * @JDK version used:      <JDK1.8> 
 * <AUTHOR> [<EMAIL>]
 * @since 2020-04-08-上午9:56:10
 */
public interface IHttpAppSnapshotDao extends IMetabaseDao<HttpAppSnapshot> {

	/**
	 * 查询应用快照列表(分页)
	 * @Comments:  <对此方法的描述，可以引用系统设计中的描述>
	 * <AUTHOR> [<EMAIL>]
	 * @since 2020-04-08 9:56
	 * @param
	 * @return
	 */
	List<HttpAppSnapshot> selectHttpAppSnapshotList(String appId, Integer page, Integer limit) throws Exception;

	/**
	 * 查询应用快照数量
	 * @Comments:  <对此方法的描述，可以引用系统设计中的描述>
	 * <AUTHOR> [<EMAIL>]
	 * @since 2020-04-08 9:56
	 * @param
	 * @return
	 */
	Long totalCount(String appId) throws Exception;

	/**
	 * 应用id查询应用快照详情
	 * @Comments:  <对此方法的描述，可以引用系统设计中的描述>
	 * <AUTHOR> [<EMAIL>]
	 * @since 2020-04-08 9:56
	 * @param
	 * @return
	 */
	HttpAppSnapshot selectHttpAppSnapshotByAppId(String appId, Long startDate, Long endDate) throws Exception;
}
