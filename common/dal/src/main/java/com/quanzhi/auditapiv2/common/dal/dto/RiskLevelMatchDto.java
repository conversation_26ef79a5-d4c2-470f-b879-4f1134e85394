package com.quanzhi.auditapiv2.common.dal.dto;

import com.quanzhi.auditapiv2.common.util.utils.DataUtil;
import lombok.Data;

import java.util.HashSet;
import java.util.Map;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Description
 * @Version
 * @createTime 2023/3/2 9:52 AM
 */
@Data
public class RiskLevelMatchDto {

    @Data
    public static class Weakness{
        // 弱点名称
        private Set<String> weaknessNames = new HashSet<>();

        // 弱点等级
        private Set<Integer> weaknessLevels;

        // 弱点等级数量分组
        private Map<Integer, Long> levelNumMap;

        // 弱点等级1的数量
        private Long weaknessLevelLow = 0L;

        // 弱点等级2的数量
        private Long weaknessLevelMedium = 0L;

        // 弱点等级3的数量
        private Long weaknessLevelHigh = 0L;

        public void setLevelNum(){
            if (DataUtil.isNotEmpty(weaknessLevels)){
                this.levelNumMap = weaknessLevels.stream().collect(Collectors.groupingBy(Function.identity(), Collectors.counting()));

                weaknessLevelLow = levelNumMap == null ? 0L : (levelNumMap.get(1) == null ? 0L : levelNumMap.get(1));
                weaknessLevelMedium = levelNumMap == null ? 0L : (levelNumMap.get(2) == null ? 0L : levelNumMap.get(2));
                weaknessLevelHigh = levelNumMap == null ? 0L : (levelNumMap.get(3) == null ? 0L : levelNumMap.get(3));
            }
        }
    }

    @Data
    public static class Risk{
        // 风险名称
        private Set<String> riskNames = new HashSet<>();

        // 风险等级
        private Set<Integer> riskLevels;

        // 风险等级数量分组
        private Map<Integer, Long> levelNumMap;

        // 风险等级1的数量
        private Long riskLevelLow = 0L;

        // 风险等级2的数量
        private Long riskLevelMedium = 0L;

        // 风险等级3的数量
        private Long riskLevelHigh = 0L;

        // 根据配置算出的 风险等级名称
        private String levelName;

        // 触发风险时间
        private Long riskFirstTime;

        // 最近风险时间
        private Long riskLastTime;

        // 风险状态
        private Integer state;

        // 风险数量：按风险状态分组的组内数量
        private Long riskCnt;

        // 风险总量
        private Long totalRiskCnt;

        // 已确认风险数量
        private Long confirmRiskCount;

        public void setLevelNum(){
            if (DataUtil.isNotEmpty(riskLevels)){
                this.levelNumMap = riskLevels.stream().collect(Collectors.groupingBy(Function.identity(), Collectors.counting()));

                riskLevelLow = levelNumMap == null ? 0L : (levelNumMap.get(1) == null ? 0L : levelNumMap.get(1));
                riskLevelMedium = levelNumMap == null ? 0L : (levelNumMap.get(2) == null ? 0L : levelNumMap.get(2));
                riskLevelHigh = levelNumMap == null ? 0L : (levelNumMap.get(3) == null ? 0L : levelNumMap.get(3));
            }
        }
    }
}
