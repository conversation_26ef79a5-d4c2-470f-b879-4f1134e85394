package com.quanzhi.auditapiv2.common.dal.dto;

import com.quanzhi.metabase.core.model.ResourceEntity;
import com.quanzhi.metabase.core.model.http.HttpApiResource;
import com.quanzhi.metabase.core.model.http.HttpApiSample;
import com.quanzhi.metabase.core.model.http.weakness.ApiWeakness;
import com.quanzhi.metabase.core.model.http.weakness.Sample;
import com.quanzhi.metabase.core.model.http.weakness.State;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;
import org.mapstruct.factory.Mappers;

import java.util.List;
import java.util.Set;

/**
 * 《接口弱点dto》
 *
 * <AUTHOR> [<EMAIL>]
 * @Project:
 * @Module ID:
 * @Comments:
 * @JDK version used:      <JDK1.8>
 * @since 2020-08-28-下午16:52:18
 */
@Data
@ApiModel("弱点")
public class ApiWeaknessDto extends ResourceEntity {

    /**
     * 域名
     */
    @ApiModelProperty("域名")
    private String host;

    @ApiModelProperty("弱点影响面")
    private String affectedSurface = "--";

    @ApiModelProperty("是否为升级弱点")
    private Boolean updateWeak = false;

    /**
     * 应用uri
     */
    @ApiModelProperty("应用uri")
    private String appUri;

    /**
     * 最早的事件发现时间戳
     */
    @ApiModelProperty("最早的事件发现时间戳")
    private Long earlyTimestamp;

    /**
     * 上次事件发现时间戳
     */
    @ApiModelProperty("上次事件发现时间戳")
    private Long lastTimestamp;

    /**
     * 检测修复时间
     */
    @ApiModelProperty("检测修复时间")
    private Long fixedTimestamp;

    /**
     * 弱点id标识
     */
    @ApiModelProperty("弱点id标识")
    private String weaknessId;

    @ApiModelProperty("操作id标识")
    private String operationId;

    /**
     * 弱点名称
     */
    @ApiModelProperty("弱点名称")
    private String name;

    /**
     * 等级
     */
    @ApiModelProperty("等级")
    private Integer level;

    /**
     * 等级名称
     */
    @ApiModelProperty("等级名称")
    private String levelName;

    /**
     * 样例信息
     */
    @ApiModelProperty("样例信息")
    private List<Sample> samples;

    /**
     * 状态
     */
    @ApiModelProperty("状态")
    private State state;

    /**
     * 状态名称
     */
    @ApiModelProperty("状态名称")
    private String stateName;

    /**
     * 忽略前状态
     */
    @ApiModelProperty("忽略前状态")
    private State oldState;

    /**
     * 上次操作者
     */
    @ApiModelProperty("上次操作者")
    private String lastOperator;

    /**
     * 接口信息
     */
    @ApiModelProperty("接口信息")
    private List<HttpApiResource> apiList;

    /**
     * 接口Dto信息
     */
    //    private HttpApiDto httpApiDto = new HttpApiDto();
    @ApiModelProperty("接口Dto信息")
    private HttpApiDto httpApiDto;

    /**
     * 弱点类别
     */
    @ApiModelProperty("弱点类别")
    private String category;

    @ApiModelProperty("弱点类别")
    private String type;

    @ApiModelProperty("弱点类别名称")
    private String typeName;

    /**
     * 处理意见
     */
    @ApiModelProperty("处理意见")
    private String suggestion;

    /**
     * 处理意见
     */
    @ApiModelProperty("解决方式")
    private String solution;

    /**
     * 描述
     */
    @ApiModelProperty("弱点描述")
    private String description;

    @ApiModelProperty(value = "工单状态", name = "orderFlag")
    private Integer orderFlag;

    @ApiModelProperty(value = "工单状态名称", name = "orderFlagName")
    private String orderFlagName;
    
    @ApiModelProperty("最新一条样例详情")
    private HttpApiSample httpApiSampleDetail;

    private Set<String> province;

    private ApiWeakness.AiInfo aiInfo;

    private boolean hostFlag;


//    private String apiUrl;
//
//    @Override
//    public void setUri(String uri) {
//        super.setUri(uri);
//        if (uri != null && uri.startsWith("httpapi:")) {
//            setApiUrl(MetabaseURI.getName(uri));
//        }else{
//            setApiUrl(uri);
//        }
//    }
//
//    public void setApiUrl(String apiUrl) {
//        if (apiUrl == null || apiUrl.length() == 0) {
//            return;
//        }
//        this.apiUrl = apiUrl;
//    }

    @Mapper
    public interface ApiWeaknessDtoMapper {

        ApiWeaknessDtoMapper INSTANCE = Mappers.getMapper(ApiWeaknessDtoMapper.class);

        @Mappings({
                @Mapping(source = "api", target = "httpApiDto"),
                @Mapping(source = "type", target = "category")
        })
        ApiWeaknessDto convert(ApiWeakness apiWeakness);

        @Mappings({
                @Mapping(target = "api", source = "httpApiDto"),
                @Mapping(target = "type", source = "category")
        })
        ApiWeakness convert(ApiWeaknessDto apiWeakness);

    }
}
