package com.quanzhi.auditapiv2.common.dal.dao;

import com.quanzhi.auditapiv2.common.dal.entity.ip.IpLabel;

import java.util.List;

/**
 * @Auther: yangzixian
 * @Date: 2021/9/1 11:37
 * @Description:
 */
public interface IIpLabelDao {

    /**
     * 保存ip标签
     *
     * @param ipLabel
     * @return
     */
    String saveIpLabel(IpLabel ipLabel);

    /**
     * 删除ip标签
     *
     * @param id
     * @return
     */
    String deleteIpLabel(String id);

    /**
     * 更新ip标签
     *
     * @param ipLabel
     * @return
     */
    String updateIpLabel(IpLabel ipLabel);

    /**
     * 查询所有ip标签
     *
     * @return
     */
    List<IpLabel> selectIpLabelList();

    /**
     * 查询IP标签，包括被删除的
     * @return
     */
    List<IpLabel> selectIpLabelWithDel();

    /**
     * 查询单个ip标签
     *
     * @param id
     * @return
     */
    IpLabel selectIpLabelById(String id);

}
