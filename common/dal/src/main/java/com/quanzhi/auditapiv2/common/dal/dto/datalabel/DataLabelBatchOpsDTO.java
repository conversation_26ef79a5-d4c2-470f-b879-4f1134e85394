package com.quanzhi.auditapiv2.common.dal.dto.datalabel;

import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * <AUTHOR>
 * @class DataLabelBatchOpsDTO
 * @created 2023/7/18 17:53
 * @desc
 **/
@ApiModel
@Data
@EqualsAndHashCode(callSuper = true)
public class DataLabelBatchOpsDTO extends DataLabelQuery {

    private List<String> ids;

    private String opt;

    private Boolean isCheckAll;
}
