package com.quanzhi.auditapiv2.common.dal.dao.node;

import com.quanzhi.auditapiv2.common.dal.entity.node.ClusterNode;
import com.quanzhi.auditapiv2.common.dal.entity.node.ClusterType;
import com.quanzhi.auditapiv2.common.util.dto.ListOutputDto;
import org.springframework.data.domain.Pageable;

import java.util.List;
import java.util.Map;


public interface ClusterNodeDao {
    ClusterNode save(ClusterNode node);

    void renew(ClusterNode node);

    ListOutputDto<ClusterNode> page(Pageable pageable);

    List<ClusterNode> findAll();

    ClusterNode findOne(String id);

    void remove(ClusterNode role);

    ClusterNode findByUrl(String url);

    ClusterNode findByNID(String nid);

    ClusterNode getMasterNode();

    boolean exist();

    String getNodeId();

    ClusterType getClusterType();

    Map<String, ClusterNode> getNid2NodeMap();

}
