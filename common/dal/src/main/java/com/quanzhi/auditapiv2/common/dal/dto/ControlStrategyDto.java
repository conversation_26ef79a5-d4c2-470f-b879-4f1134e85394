package com.quanzhi.auditapiv2.common.dal.dto;

import com.quanzhi.auditapiv2.common.dal.entity.ControlStrategy;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * <AUTHOR>
 * create at 2022/12/16 4:25 下午
 * @description: 管控策略dto
 **/
@Data
public class ControlStrategyDto {

    private String id;

    @ApiModelProperty(value = "管控策略名称", name = "controlName")
    private String controlName;

    @ApiModelProperty(value = "管控内容", name = "controlContents")
    private List<String> controlContents;

    @ApiModelProperty(value = "策略描述", name = "strategyDesc")
    private String strategyDesc;

    @ApiModelProperty(value = "管控策略", name = "strategyPolicies")
    private List<ControlStrategy.StrategyPolicy> strategyPolicies;

    @ApiModelProperty(value = "管控策略表达式", name = "strategyMathExpre")
    private String strategyMathExpre;

    @ApiModelProperty(value = "是否开启", name = "enable")
    private Boolean enable;

    @ApiModelProperty(value = "更新时间", name = "updateTime")
    private Long updateTime;


    private Long updateTimeStart;
    private Long updateTimeEnd;
    private Integer page;
    private Integer limit;


    @Mapper
    public interface ControlStrategyDtoMapper {
        ControlStrategyDtoMapper INSTANCE = Mappers.getMapper(ControlStrategyDtoMapper.class);

        ControlStrategy convert(ControlStrategyDto controlStrategyDto);

        ControlStrategyDto convert(ControlStrategy controlStrategy);

    }

}