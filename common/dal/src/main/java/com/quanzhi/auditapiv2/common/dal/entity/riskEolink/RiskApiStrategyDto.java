package com.quanzhi.auditapiv2.common.dal.entity.riskEolink;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
public class RiskApiStrategyDto {

    @ApiModelProperty(value = "id", name = "id")
    private String id;
    @ApiModelProperty(value = "ip", name = "ip")
    private String ip;
    @ApiModelProperty(value = "ips", name = "ips")
    private List<String> ips;
    @ApiModelProperty(value = "账号", name = "account")
    private String account;
    @ApiModelProperty(value = "api", name = "api")
    private String api;

    @ApiModelProperty(value = "风险ID", name = "operationId")
    private String operationId;
    @ApiModelProperty(value = "风险数据库唯一ID", name = "riskId")
    private String riskId;
    @ApiModelProperty(value = "策略id", name = "strategyUuid")
    private String strategyUuid;
    @ApiModelProperty(value = "策略id", name = "strategyId")
    private String strategyId;
    @ApiModelProperty(value = "管控状态 0未管控 1已管控", name = "status")
    private Integer status = 0;

    @ApiModelProperty(value = "管控类型", name = "strategyType")
    private String strategyType;
    @ApiModelProperty(value = "阻断时间/限流次数", name = "count")
    private Integer count;
    @ApiModelProperty(value = "是否生效与次IP在API的其他风险", name = "isAll")
    private Boolean isAll;
    private String updateTime;
    private String createTime;
}
