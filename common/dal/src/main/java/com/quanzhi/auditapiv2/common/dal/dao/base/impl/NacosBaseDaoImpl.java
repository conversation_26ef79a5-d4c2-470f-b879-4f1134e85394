package com.quanzhi.auditapiv2.common.dal.dao.base.impl;

import com.quanzhi.auditapiv2.common.dal.dao.base.INacos;
import com.quanzhi.auditapiv2.common.dal.dao.base.INacosBaseDao;
import com.quanzhi.auditapiv2.common.dal.dto.query.NacosFilterDto;
import com.quanzhi.auditapiv2.common.dal.dto.query.QueryNacos;
import com.quanzhi.auditapiv2.common.util.utils.DataUtil;
import com.quanzhi.auditapiv2.common.util.utils.ReflectUtils;
import org.springframework.beans.factory.annotation.Autowired;

import java.lang.reflect.ParameterizedType;
import java.lang.reflect.Type;
import java.util.Collections;
import java.util.Comparator;
import java.util.List;

public class NacosBaseDaoImpl<T> implements INacosBaseDao<T> {

    @Autowired
    protected INacos<T> nacosImpl;

    protected String collectionName;

    private Class<T> clazz;

    public NacosBaseDaoImpl() {
        this.initCollectionName(null);
    }

    /**
     * 根据类类型获取表名
     */
    @Override
    public void initCollectionName(String collectionName) {
        if (DataUtil.isEmpty(collectionName)) {
            Type type = getClass();
            if (!(type instanceof ParameterizedType)) {
                type = getClass().getGenericSuperclass();
            }
            try {
                clazz = (Class<T>) ((ParameterizedType) type).getActualTypeArguments()[0];
            } catch (Exception e) {
                // ignore
            }

            determineCollectionName(this.clazz);
        } else {
            this.collectionName = collectionName;
        }
    }

    @Override
    public T findOne(String id) {

        T configInfo = nacosImpl.findOne(id, collectionName);

        return configInfo;
    }

    @Override
    public T getById(String id) {

        T configInfo = nacosImpl.findOne(id, collectionName);

        return configInfo;
    }

    @Override
    public T findOne(QueryNacos queryNacos) {
        return nacosImpl.findOne(queryNacos, collectionName);
    }

    @Override
    public boolean update(T model) {
        return nacosImpl.update(model, collectionName);
    }

    @Override
    public List<T> getList(Integer page, Integer limit) {
        List<T> list = nacosImpl.page(page, limit, collectionName, true);

        Collections.sort(list, new Comparator<T>() {
            @Override
            public int compare(T a, T b) {
                try {
                    Long updateTime_a = (Long) ReflectUtils.getValue(a, "updateTime");
                    Long updateTime_b = (Long) ReflectUtils.getValue(b, "updateTime");
//                    if (DataUtil.isEmpty(updateTime_a)) {
//                        if (DataUtil.isEmpty(updateTime_b)) {
//                            return 0;
//                        }
//                        return 1;
//                    }
                    if (DataUtil.isEmpty(updateTime_b)) {
                        return -1;
                    }
                    return updateTime_b.compareTo(updateTime_a);
                } catch (Exception e) {
                    return 0;
                }

            }
        });

        return list;
    }

    @Override
    public List<T> getList(Integer page, Integer limit, List<NacosFilterDto> nacosFilterDtos) {
        List<T> list = nacosImpl.select(nacosFilterDtos, collectionName);

        Collections.sort(list, new Comparator<T>() {
            @Override
            public int compare(T a, T b) {

                try {
                    Long updateTime_a = (Long) ReflectUtils.getValue(a, "updateTime");
                    Long updateTime_b = (Long) ReflectUtils.getValue(b, "updateTime");

                    if (DataUtil.isEmpty(updateTime_a)) {
                        return 1;
                    }

                    if (DataUtil.isEmpty(updateTime_b)) {
                        return -1;
                    }

                    return updateTime_a - updateTime_b > 0 ? -1 : 1;
                } catch (Exception e) {
                    return 1;
                }

            }
        });

        list = nacosImpl.page(page, limit, list);
        return list;
    }

    @Override
    public long getCount() {
        return nacosImpl.getCount(collectionName);
    }

    @Override
    public long getCount(List<NacosFilterDto> nacosFilterDtos) {
        return nacosImpl.getCount(nacosFilterDtos, collectionName);
    }

    @Override
    public T save(T model) {
        return nacosImpl.save(model, collectionName);
    }

    @Override
    public boolean delete(List<String> ids) {
        return nacosImpl.delete(ids, collectionName);
    }

    @Override
    public List<T> getAll() {
        return nacosImpl.getAll(collectionName);
    }

    @Override
    public boolean batchUpdate(List<T> models) {
        return nacosImpl.batchUpdate(models, collectionName);
    }

    @Override
    public void replaceAll(List<T> models) {
        nacosImpl.saveAll(models, collectionName);
    }

    @Override
    public void reset(List<T> models) {
        nacosImpl.reset(models, collectionName);
    }

    @Override
    public List<T> page(Integer page, Integer limit, List<T> content) {
        return nacosImpl.page(page, limit, content);
    }

    @Override
    public List<T> getAllSelect(List<NacosFilterDto> nacosFilterList) {
        return nacosImpl.select(nacosFilterList, collectionName);
    }


    /**
     * 根据实体类获取表名
     *
     * @param entityClass
     * @return
     */
    private void determineCollectionName(Class<?> entityClass) {
        if (DataUtil.isEmpty(entityClass)) {
            return;
        }
        // 如果collectionName字段不为空，那么不需要根据类名获取
        if (DataUtil.isNotEmpty(collectionName)) {
            return;
        }

        String collName = entityClass.getSimpleName();
        collName = collName.replaceFirst(collName.substring(0, 1)
                , collName.substring(0, 1).toLowerCase());
        this.collectionName = collName;

    }
}
