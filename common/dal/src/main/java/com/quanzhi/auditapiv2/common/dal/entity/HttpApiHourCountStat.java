package com.quanzhi.auditapiv2.common.dal.entity;

import lombok.Data;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

import java.util.Map;

@Data
@Document(collection = "httpApiHourCountStat") // MongoDB集合名称
public class HttpApiHourCountStat {

    @Id
    private String id; // 对应MongoDB的_id字段

    private String uri; // API地址

    private String day; // 日期，例如20241217

    private Map<String, Map<String, Long>> dateStats; // 内嵌文档：key为小时，value为统计数据
}
