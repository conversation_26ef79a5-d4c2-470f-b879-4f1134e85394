package com.quanzhi.auditapiv2.common.dal.dao.impl;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.parser.Feature;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.quanzhi.auditapiv2.common.dal.dao.IScanToolDao;
import com.quanzhi.auditapiv2.common.dal.dao.base.impl.BaseDaoImpl;
import com.quanzhi.auditapiv2.common.dal.dto.ResultFilterRuleDto;
import com.quanzhi.auditapiv2.common.dal.entity.ScanTask;
import com.quanzhi.auditapiv2.common.dal.entity.ScanTool;
import com.quanzhi.auditapiv2.common.util.utils.CommonUtils;
import com.quanzhi.auditapiv2.common.util.utils.DataUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.stereotype.Repository;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Repository
public class ScanToolDaoImpl extends BaseDaoImpl<ScanTool> implements IScanToolDao {

    @Autowired
    MongoTemplate mongoTemplate;

    String collectionName = "scanTool";

    @Override
    public List<ScanTool> selectScanToolList() {

        //查询条件
        Criteria criteria = new Criteria();

        return mongoTemplate.find(new Query(criteria), ScanTool.class, collectionName);
    }

    @Override
    public ScanTool findByCode(String code) {
        Criteria criteria = Criteria.where("code").is(code);
        return mongoTemplate.findOne(new Query(criteria), ScanTool.class, collectionName);
    }

    @Override
    public ScanTool getScanToolByFileHash(String fileHash) {
        Criteria criteria = Criteria.where("fileHash").is(fileHash);
        return mongoTemplate.findOne(new Query(criteria), ScanTool.class, collectionName);
    }

    @Override
    public ScanTool selectScanToolById(String id) {

        //查询条件
        Criteria criteria = Criteria.where("_id").is(id);

        return mongoTemplate.findOne(new Query(criteria), ScanTool.class, collectionName);
    }

    @Override
    public ScanTool insert(ScanTool scanTool){
        return mongoTemplate.insert(scanTool,collectionName);
    }

    @Override
    public ScanTool findOneByQuery(Query query){
        return mongoTemplate.findOne(query, ScanTool.class,collectionName);
    }

    @Override
    public void updateScanToolByToolCode(Query query, Update update){
        mongoTemplate.updateMulti(query,update, ScanTool.class,collectionName);
    }

    @Override
    public void addToolFilters(String ToolCode,String scanRule, List<ResultFilterRuleDto> filters) {
        Query query = new Query();
        query.addCriteria(Criteria.where("code").is(ToolCode));
        Map scanTool = mongoTemplate.findOne(query, Map.class, "scanTool");
        String paramsJson = (String) scanTool.get("params");
        Map config = JSONObject.parseObject(paramsJson, Feature.OrderedField);

        if(DataUtil.isEmpty( config )) {
            config = new HashMap();
        }
        if (config.get("filters") == null) {
            config.put("filters", new ArrayList<>());
        }
        List configFilters = (List) config.get("filters");

        String currentResultSet = ToolCode + "_" + scanRule;

        List newConfigFilters = (List) configFilters.stream().filter(filterObject -> {

            Map<String, Object> filterMap = (Map) filterObject;
            return !filterMap.get("resultSet").equals(currentResultSet);

        }).collect(Collectors.toList());

        for (ResultFilterRuleDto filterRuleDto : filters) {
            Map<String, Object> filterMap = new HashMap<String, Object>();
            filterMap.put("resultSet", filterRuleDto.getResultSet());
            filterMap.put("field", filterRuleDto.getField());
            filterMap.put("op", filterRuleDto.getOp());
            filterMap.put("value", filterRuleDto.getValue());
            String id = CommonUtils.MD5(JSONObject.toJSONString(filterMap));
            filterMap.put("_id", id);
            newConfigFilters.add(filterMap);
        }
        config.put("filters",newConfigFilters);

        Update update = new Update();
        update.set("params", JSONObject.toJSONString(config, SerializerFeature.MapSortField));
        mongoTemplate.updateFirst(query, update, "scanTool");
    }
}