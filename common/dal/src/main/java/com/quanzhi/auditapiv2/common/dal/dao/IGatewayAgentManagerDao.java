package com.quanzhi.auditapiv2.common.dal.dao;

import com.quanzhi.auditapiv2.common.dal.dao.base.IBaseDao;
import com.quanzhi.auditapiv2.common.dal.entity.GatewayAgentManagerStatus;

import java.util.List;

/**
 * <AUTHOR>
 * @description   探针管理
 * @date 2023/4/5 16:05
 */
public interface IGatewayAgentManagerDao extends IBaseDao<GatewayAgentManagerStatus> {

    List<GatewayAgentManagerStatus> findByName(String name);

    List<GatewayAgentManagerStatus> findByAgentIp(String agentIp);

    List<GatewayAgentManagerStatus> findByGatewayIp(String gatewayIp);

    List<GatewayAgentManagerStatus> findByAgentIp(List<String> agentIp);

    List<GatewayAgentManagerStatus> findByFullName(String name);

    GatewayAgentManagerStatus findByAgentIpAndPort(String gatewayIp,String agentIp,Integer port);

    Long getAgentInfoByNameCount(String name);

    void deleteById(String id);

    void deleteByAgentIp(String ip);

    void deleteByGatewayIp(String ip);

    List<GatewayAgentManagerStatus> getPageByGatewayIpAndName(String gatewayIp, String agentName, Integer page, Integer limit);

    Long getPageByGatewayIpAndNameCount(String gatewayIp, String agentName);

    List<GatewayAgentManagerStatus> getPageByGatewayIp(String gatewayIp, String agentName);


    List<String> getAllGatewayIp();


}
