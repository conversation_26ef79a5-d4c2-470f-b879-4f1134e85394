package com.quanzhi.auditapiv2.common.dal.dataobject;

import lombok.Data;
import org.bson.types.ObjectId;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

import java.io.Serializable;
import java.util.List;

/**
 * @Description:
 * @Author: danniel.yu
 * @Date: 2020-05-11 15:23
 */
@Data
@Document
public class SysMenu implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 菜单id
     */
    @Id
    private String id;

    /**
     * 父级id，一级菜单为0
     */
    private String parentId;

    /**
     * 父级菜单名称
     */
    private String parentName;

    /**
     * 菜单名称
     */
    private String name;

    /**
     * 菜单url
     */
    private String url;

    /**
     * 授权标识(多个用逗号分隔，如：user:list,user:create)
     */
    private String perms;

    /**
     * 类型(0：目录   1：菜单   2：按钮)
     */
    private Integer type;

    /**
     * 菜单图标
     */
    private String icon;

    /**
     * 排序
     */
    private Integer orderNum;

    /**
     * 创建时间
     */
    private Long gmtCreate;

    /**
     * 修改时间
     */
    private Long gmtModified;

    /**
     * ztree属性
     */
    private Boolean open;

    /**
     * 主版本菜单 master
     * 定开自己配
     */
    private String customCode;

    private List<?> list;

    private List<SysMenu> children;
}
