package com.quanzhi.auditapiv2.common.dal.entity;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * @ClassName PolicyConfigInfo
 * @Description TODO
 * <AUTHOR>
 * @Date 2021/2/4 11:48
 **/
@Data
public class PolicyConfigInfo implements Serializable {

    private String id;

    private String policy;

    private String name;

    private List<PolicyConfig> operator;

    private List<PolicyConfigInfo> subPolicy;

    private String dataId;

    private String groupId;

    private List<PolicyConfig> values;

    @Data
    public static class PolicyConfig implements Serializable{

        private String id;

        private String name;
    }
}