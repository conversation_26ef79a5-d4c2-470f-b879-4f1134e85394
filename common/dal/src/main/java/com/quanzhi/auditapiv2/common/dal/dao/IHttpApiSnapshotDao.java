package com.quanzhi.auditapiv2.common.dal.dao;

import com.quanzhi.auditapiv2.common.dal.dao.base.IMetabaseDao;
import com.quanzhi.metabase.core.model.http.HttpApiSnapshot;

import java.util.List;

/**
 * 
 * 《接口快照持久层接口》
 * 
 * 
 * @Project: 
 * @Module ID:
 * @Comments: 
 * @JDK version used:      <JDK1.8> 
 * <AUTHOR> [<EMAIL>]
 * @since 2020-04-08-上午9:56:10
 */
public interface IHttpApiSnapshotDao extends IMetabaseDao<HttpApiSnapshot> {

	/**
	 * 查询接口快照列表(分页)
	 * @Comments:  <对此方法的描述，可以引用系统设计中的描述>
	 * <AUTHOR> [<EMAIL>]
	 * @since 2020-04-08 9:56
	 * @param
	 * @return
	 */
	List<HttpApiSnapshot> selectHttpApiSnapshotList(String appId, Long startDate, Long endDate, Integer page, Integer limit) throws Exception;

	/**
	 * 查询接口快照数量
	 * @Comments:  <对此方法的描述，可以引用系统设计中的描述>
	 * <AUTHOR> [<EMAIL>]
	 * @since 2020-04-08 9:56
	 * @param
	 * @return
	 */
	Long totalCount(String appId, Long startDate, Long endDate);

	/**
	 * id查询接口快照详情
	 * @Comments:  <对此方法的描述，可以引用系统设计中的描述>
	 * <AUTHOR> [<EMAIL>]
	 * @since 2020-04-08 9:56
	 * @param
	 * @return
	 */
	HttpApiSnapshot selectHttpApiSnapshotById(String id) throws Exception;

	/**
	 * 获取最近周期的快照列表
	 * @param limit
	 * @param uri
	 * @return
	 */
	List<HttpApiSnapshot> getNearHttpApiSnapshotList(String uri,Integer limit);
}
