package com.quanzhi.auditapiv2.common.dal.dto.docx;

import lombok.Data;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 *
 * 《目录遍历接口》
 *
 *
 * @Project:
 * @Module ID:
 * @Comments:
 * @JDK version used:      <JDK1.8>
 * <AUTHOR> [<EMAIL>]
 * @since 2020-08-28-下午16:52:18
 */
@Data
public class Directorytraverse extends ApiWeaknessExportWord {

    @Mapper
    public interface DirectorytraverseMapper {

        Directorytraverse.DirectorytraverseMapper INSTANCE = Mappers.getMapper(Directorytraverse.DirectorytraverseMapper.class);
        Directorytraverse convert(ApiWeaknessExportWord apiWeaknessExportWord);
    }
}
