package com.quanzhi.auditapiv2.common.dal.dao;

import com.alibaba.fastjson.JSONObject;
import com.mongodb.client.MongoCursor;
import com.quanzhi.auditapiv2.common.dal.dao.base.IMetabaseDao;
import com.quanzhi.auditapiv2.common.dal.dto.app.AppAccountDto;
import com.quanzhi.auditapiv2.common.dal.dto.app.AppRelationDto;
import com.quanzhi.auditapiv2.common.dal.dto.query.HttpAppCriteriaDto;
import com.quanzhi.auditapiv2.common.util.dto.AggregationDto;
import com.quanzhi.auditapiv2.common.util.dto.GroupDto;
import com.quanzhi.metabase.core.model.http.HttpAppDateStat;
import com.quanzhi.metabase.core.model.http.HttpAppResource;
import com.quanzhi.metabase.core.model.query.AggregationResult;
import com.quanzhi.metabase.core.model.query.MetabaseGroupOperation;
import com.quanzhi.metabase.core.model.query.MetabaseQuery;
import com.quanzhi.metabase.core.model.query.ResourceUpdates;
import org.bson.Document;
import org.springframework.data.mongodb.core.query.Query;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 《应用持久层接口》
 *
 * <AUTHOR> [<EMAIL>]
 * @Project:
 * @Module ID:
 * @Comments:
 * @JDK version used:      <JDK1.8>
 * @since 2020-04-08-上午9:56:10
 */
public interface IHttpAppDao extends IMetabaseDao<HttpAppResource> {

    /**
     * 查询应用列表(分页)
     *
     * @param
     * @return
     * @Comments: <对此方法的描述，可以引用系统设计中的描述>
     * <AUTHOR> [<EMAIL>]
     * @since 2020-04-08 9:56
     */
    List<HttpAppResource> selectHttpAppList(Map<String, Object> map, String field, Integer sort, Integer page, Integer limit) throws Exception;

    List<HttpAppResource> selectHttpAppListByFeatureLabel(String featureLabel) throws Exception;

    List<HttpAppResource> selectHttpAppListByDataLabel(String dataLabel) throws Exception;

    void updateHttpAppByFeatureLabel(String label) throws Exception;

    void updateHttpAppByDataLabel(String label) throws Exception;

    /**
     * 查询应用数量
     *
     * @param
     * @return
     * @Comments: <对此方法的描述，可以引用系统设计中的描述>
     * <AUTHOR> [<EMAIL>]
     * @since 2020-04-08 9:56
     */
    Long totalCount(Map<String, Object> map) throws Exception;

    org.springframework.data.mongodb.core.query.Criteria getCriteria(Map<String, Object> map);

    /**
     * 查询应用分组
     *
     * @param
     * @return
     * @Comments: <对此方法的描述，可以引用系统设计中的描述>
     * <AUTHOR> [<EMAIL>]
     * @since 2020-04-08 9:56
     */
    List<AggregationResult> selectHttpAppGroup(Map<String, Object> map, Integer sort, String[] groupFields) throws Exception;

    Long getCount(Long startTime, Long endTime);

    /**
     * id查询应用详情
     *
     * @param
     * @return
     * @Comments: <对此方法的描述，可以引用系统设计中的描述>
     * <AUTHOR> [<EMAIL>]
     * @since 2020-04-08 9:56
     */
    HttpAppResource selectHttpAppById(String id) throws Exception;

    /**
     * appUri查询应用详情
     *
     * @param
     * @return
     * @Comments: <对此方法的描述，可以引用系统设计中的描述>
     * <AUTHOR> [<EMAIL>]
     * @since 2020-04-08 9:56
     */
    HttpAppResource selectHttpAppByAppUri(String appUri) throws Exception;

    HttpAppResource selectHttpAppByUri(String appUri) throws Exception;

    /**
     * hosts查询应用详情
     *
     * @param
     * @return
     * @Comments: <对此方法的描述，可以引用系统设计中的描述>
     * <AUTHOR> [<EMAIL>]
     * @since 2020-04-08 9:56
     */
    HttpAppResource selectHttpAppByHosts(String host) throws Exception;

    HttpAppResource selectHttpAppByHost(String host) throws Exception;


    /**
     * 应用名称查询详情
     *
     * @param
     * @return
     * @Comments: <对此方法的描述，可以引用系统设计中的描述>
     * <AUTHOR> [<EMAIL>]
     * @since 2020-04-08 9:56
     */
    HttpAppResource selectHttpAppByName(String name) throws Exception;

    /**
     * parentAppId查询应用详情
     *
     * @param
     * @return
     * @Comments: <对此方法的描述，可以引用系统设计中的描述>
     * <AUTHOR> [<EMAIL>]
     * @since 2020-04-08 9:56
     */
    List<HttpAppResource> selectHttpAppByParentAppId(String parentAppId) throws Exception;

    /**
     * 编辑应用
     *
     * @param
     * @return
     * @Comments: <对此方法的描述，可以引用系统设计中的描述>
     * <AUTHOR> [<EMAIL>]
     * @since 2020-04-08 9:56
     */
    HttpAppResource updateHttpApp(HttpAppResource httpAppResource) throws Exception;

    void update(String id, Map<String, Object> updates);


    List<AggregationResult> getHttpApiVisits(MetabaseQuery query, MetabaseGroupOperation metabaseGroupOperation, Class<HttpAppDateStat> httpAppDateStatClass);

    List<HttpAppResource> getNewAppList(Integer page, Integer limit, String field, Integer sort, Long startTime, Long endTime);

    /**
     * 根据应用uri和是否已启用解析查询推荐和已启用数量
     *
     * @param uri
     * @param isParse
     * @return
     */
    Long countByUriAndIsParse(String uri, List<String> tags, int isParse) throws Exception;

    List<HttpAppResource> getAccParseAppList(HttpAppCriteriaDto httpAppCriteriaDto);

    Long countByHttpAppCriDto(HttpAppCriteriaDto httpAppCriteriaDto);

    void batchUpdateByIds(List<String> uris, int type);

    void batchUpdateByUris(List<String> uris, int type, String target);

    void saveAppAccParse(HttpAppResource httpAppResource, int parse) throws Exception;

    void saveHttpApp(List<HttpAppResource> httpAppResourceList);

    HttpAppResource getHttpAppResourceByUri(String uri);

    /**
     * 获取应用关系
     *
     * @return
     */
    AppRelationDto getAppRelation(List<String> appUris);

    /**
     * 敏感应用Top10
     *
     * @param
     * @return
     * @Comments: <对此方法的描述，可以引用系统设计中的描述>
     * <AUTHOR> [<EMAIL>]
     * @since 2020-04-08 9:56
     */
    List<HttpAppResource> sensitiveAppTop10(Map<String, Object> map, String field) throws Exception;

    /**
     * 应用分组查询(不走matabase)
     *
     * @param
     * @return
     * @Comments: <对此方法的描述，可以引用系统设计中的描述>
     * <AUTHOR> [<EMAIL>]
     * @since 2021-09-02 9:56
     */
    List<AggregationDto> group(Map<String, Object> map, GroupDto groupDto, Integer sort, Integer page, Integer limit) throws Exception;

    /**
     * 时间戳转日期分组(不走matabase)
     *
     * @param
     * @return
     * @Comments: <对此方法的描述，可以引用系统设计中的描述>
     * <AUTHOR> [<EMAIL>]
     * @since 2021-09-02 9:56
     */
    List<AggregationDto> group() throws Exception;

    /**
     * 获取大屏概览信息 - 总应用、高敏感应用
     *
     * @return
     */
    List<Long> getStatistics();

    /**
     * 获取网段下所有关注的APP
     *
     * @param network
     * @return
     */
    List<String> selectAllAppByNetwork(String network);

    /**
     * 获取一个host范围内的所有app信息
     *
     * @param host
     * @return
     */
    List<HttpAppResource> selectAppByHost(List<String> host);

    /**
     * @param visitDomain
     * @param deployDomain
     * @return
     */
    long getAppRelationWithVisitAndDeploy(String visitDomain, String deployDomain);

    void updateAppOrderFlag(MetabaseQuery metabaseQuery, ResourceUpdates resourceUpdates);

    /**
     * 查询全部应用uri
     *
     * @param
     * @return
     * @Comments: <对此方法的描述，可以引用系统设计中的描述>
     * <AUTHOR> [<EMAIL>]
     * @since 2022-03-09 9:56
     */
    List<HttpAppResource> selectHttpAppUriList() throws Exception;

    /**
     * @param appUri
     * @return com.quanzhi.auditapiv2.common.dal.dto.app.AppAccountDto
     * <AUTHOR>
     * @date 2023/2/28 6:21 PM
     * @Description 获取应用下的账号
     * @Since
     */
    AppAccountDto aggrAccountByApp(String appUri);

    MongoCursor<Document> getHttpAppList(Map<String, Object> map, int batchSize);

    List<HttpAppResource> getHttpApisByUris(Set<String> appUriSet);

    List<Short> getAssetLifeFlag(JSONObject jsonObject);

    String findAppIcon(String uri);

    Long count(Query query, String collection);

    List<String> getCustomDepartments();
}
