package com.quanzhi.auditapiv2.common.dal.entity;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * @ClassName RiskDetail
 * @Description TODO
 * <AUTHOR>
 * @Date 2020/12/24 21:55
 **/
@Data
public class RiskDetail implements Serializable {

    /**
     * 事件时间
     */
    private String timestamp;

    /**
     * API
     */
    private String apiUrl;

    /**
     * API类型
     */
    private List<String> classifications;

    /**
     * 应用
     */
    private String host;

    /**
     * IP
     */
    private String ip;

    /**
     * 账号
     */
    private String account;

    /**
     * 登录结果
     * LOGIN_SUCCESS
     * LOGIN_FAIL
     */
    private String loginResult;

    /**
     * 请求方法
     */
    private String method;

    /**
     * 请求类型
     */
    private String reqContentType;

    /**
     * referer
     */
    private String referer;

    /**
     * user-agent
     */
    private String ua;

    /**
     * 返回类型
     */
    private String rspContentType;

    /**
     * 返回长度
     */
    private Long rspContentLength;

    /**
     * 返回状态码
     */
    private String status;
}
