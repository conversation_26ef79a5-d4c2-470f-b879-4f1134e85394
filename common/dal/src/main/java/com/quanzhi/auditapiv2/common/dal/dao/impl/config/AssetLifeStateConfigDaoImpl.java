package com.quanzhi.auditapiv2.common.dal.dao.impl.config;


import com.quanzhi.audit_core.common.model.AssetLifeStateConfig;
import com.quanzhi.auditapiv2.common.dal.dao.base.impl.NacosBaseDaoImpl;
import com.quanzhi.auditapiv2.common.dal.dao.config.IAssetLifeStateConfigDao;
import org.springframework.stereotype.Repository;

/**
 * <AUTHOR>
 * create at 2023/2/21 7:51 下午
 * @description:
 **/
@Repository
public class AssetLifeStateConfigDaoImpl extends NacosBaseDaoImpl<AssetLifeStateConfig> implements IAssetLifeStateConfigDao {

}