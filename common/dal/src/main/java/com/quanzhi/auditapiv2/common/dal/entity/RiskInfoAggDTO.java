package com.quanzhi.auditapiv2.common.dal.entity;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * @ClassName RiskInfoAggDTO
 * @Description TODO
 * <AUTHOR>
 * @Date 2020/12/24 11:41
 **/
@Data
public class RiskInfoAggDTO implements Serializable {

    /**
     * 异常事件
     */
    private RiskEvent riskEvent;

    /**
     * 异常详情
     */
    private List<RiskDetail> riskDetail;

}
