package com.quanzhi.auditapiv2.common.dal.entity.securityPosture;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Data;

/**
 * @Auther: yangzixian
 * @Date: 2021/10/15 14:02
 * @Description:
 */
@Data
@Builder
@ApiModel("访问趋势")
public class VisitTrend {
    @ApiModelProperty("时间")
    private Long time;
    @ApiModelProperty("总访问次数")
    private Long totalCount;
    @ApiModelProperty("新增访问次数")
    private Long newCount;
}
