package com.quanzhi.auditapiv2.common.dal.dataobject.query;

import com.quanzhi.auditapiv2.common.dal.common.entity.QueryBase;
import lombok.Data;
import org.bson.types.ObjectId;

import java.util.List;

/**
 * @Description:
 * @Author: danniel.yu
 * @Date: 2020-05-11 15:24
 */
@Data
public class QuerySysUser extends QueryBase  {
    /**
     * 用户id
     */
    private String id;

    private List<String> ids;
    
    /**
     * 用户名
     */
    private String username;

    /**
     * 角色id
     */
    private String roleId;

    /**
     * 状态(0：禁用   1：正常)
     */
    private Integer status;

    /**
     * 环境标识，与配置文件中保持一致：1 - 默认使用api产品，2 - 使用出境工具的网关配置
     */
    private Integer envFlag;



}
