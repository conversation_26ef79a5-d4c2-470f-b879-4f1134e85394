package com.quanzhi.auditapiv2.common.dal.dao.impl.systemSecurity;

import com.quanzhi.auditapiv2.common.dal.dao.systemSecurity.SystemSecurityDao;
import com.quanzhi.auditapiv2.common.dal.dto.systemSecurity.SystemSecurityDto;
import com.quanzhi.auditapiv2.common.dal.entity.systemSecurity.SystemSecurity;
import org.bson.types.ObjectId;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * @author: yangzixian
 * @date: 2022/7/19 20:06
 * @description:
 */
@Repository
public class SystemSecurityDaoImpl implements SystemSecurityDao {

    private final MongoTemplate mongoTemplate;

    private final String collectionName = "systemSecurity";

    public SystemSecurityDaoImpl(MongoTemplate mongoTemplate) {
        this.mongoTemplate = mongoTemplate;
    }

    @Override
    public SystemSecurity addSystemSecurity(SystemSecurity systemSecurity) {
        return mongoTemplate.save(systemSecurity);
    }

    @Override
    public Long updateSystemSecurity(SystemSecurityDto systemSecurityDto) {
        Update update = new Update();
        update.set("listType", systemSecurityDto.getListType());
        update.set("listValue", systemSecurityDto.getListValue());
        update.set("updateTime", System.currentTimeMillis());
        return mongoTemplate.upsert(new Query().addCriteria(Criteria.where("_id").is(new ObjectId(systemSecurityDto.getId()))),
                update, collectionName).getModifiedCount();
    }

    @Override
    public Long deleteSystemSecurity(String id) {
        return mongoTemplate.remove(new Query().addCriteria(Criteria.where("_id").is(new ObjectId(id))), SystemSecurity.class, collectionName).getDeletedCount();
    }

    @Override
    public SystemSecurity selectSystemSecurityById(String id) {
        return mongoTemplate.findOne(new Query().addCriteria(Criteria.where("_id").is(new ObjectId(id))), SystemSecurity.class, collectionName);
    }

    @Override
    public List<SystemSecurity> listSystemSecurity(Integer page, Integer limit) {
        return mongoTemplate.find(new Query().limit(limit).skip((page - 1) * limit), SystemSecurity.class, collectionName);
    }

    @Override
    public Long total() {
        return mongoTemplate.count(new Query(), collectionName);
    }

    @Override
    public List<SystemSecurity> listSystemSecurityByCriterial(Criteria criteria) {
        return mongoTemplate.find(new Query().addCriteria(criteria), SystemSecurity.class, collectionName);
    }

    @Override
    public List<SystemSecurity> listSystemSecurityAll() {
        return mongoTemplate.find(new Query(), SystemSecurity.class, collectionName);
    }
}
