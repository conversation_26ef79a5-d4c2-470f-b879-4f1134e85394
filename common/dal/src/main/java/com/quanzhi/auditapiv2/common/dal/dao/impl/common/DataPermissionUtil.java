package com.quanzhi.auditapiv2.common.dal.dao.impl.common;

import com.quanzhi.audit.mix.permission.domain.Condition;
import com.quanzhi.audit.mix.permission.domain.enums.OwnerEnum;
import com.quanzhi.audit.mix.permission.domain.enums.ResourceEnum;
import com.quanzhi.audit.mix.permission.service.PermissionSupport;
import com.quanzhi.auditapiv2.common.dal.dataobject.DataGroup;
import com.quanzhi.auditapiv2.common.dal.dto.group.DataPermissionTypeEnum;
import com.quanzhi.auditapiv2.common.util.utils.DataUtil;
import com.quanzhi.metabase.core.model.http.HttpAppResource;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * create at 2025/1/9 18:45
 * @description:
 **/
@Component
public class DataPermissionUtil {

    @Autowired
    private MongoTemplate mongoTemplate;

    @Autowired
    private PermissionSupport permissionSupport;

    private static final String VIRTUAL_APP = "virtualApp";


    /**
     * 获取当前各数据分组的权限条件
     * @return
     */
    public Map<String,List<String>> getCurrentDataGroupConditions(){
        Map<String,List<String>> result=new HashMap<>();
        List<DataGroup> dataGroups = mongoTemplate.find(Query.query(Criteria.where("delFlag").is(false)), DataGroup.class, "dataGroup");
        for(DataGroup dataGroup:dataGroups){
            List<Condition> conditions = dataGroup.getConditions();
            if(DataUtil.isEmpty(conditions)){
                result.put(dataGroup.getId(),new ArrayList<>());
            }else{
                List<String> appUriList=new ArrayList<>();
                for(Condition condition:conditions){
                    String key = condition.getKey();
                    List<String> values= (List<String>) condition.getValue();
                    DataPermissionTypeEnum dataPermissionTypeEnum = DataPermissionTypeEnum.valueOf(key);
                    List<String> apps = getAppUriList(dataPermissionTypeEnum, values);
                    appUriList.addAll(apps);
                }
                result.put(dataGroup.getId(),appUriList);
            }
        }
        return result;
    }




    public List<String> convertAppUriQuery(String userId) {
        List<String> result=new ArrayList<>();
        List<Condition> conditions = permissionSupport.getConditions(userId, OwnerEnum.DEPARTMENT, ResourceEnum.ASSET);
        for (Condition condition:conditions){
            String key = condition.getKey();
            DataPermissionTypeEnum dataPermissionTypeEnum = DataPermissionTypeEnum.valueOf(key);
            List<String> values= (List<String>) condition.getValue();
            List<String> appUriList = getAppUriList(dataPermissionTypeEnum, values);
            result.addAll(appUriList);
        }
        return result;
    }
    public List<String> getAppUriList(DataPermissionTypeEnum dataPermissionTypeEnum, Object values) {
        String[] value=null;
        if(values instanceof List){
            List<String> list = (List<String>) values;
            value= list.stream().toArray(String[]::new);
        }else{
            value=new String[]{values.toString()};
        }
        List<String> list = new ArrayList<>();
        Query query = new Query(Criteria.where("delFlag").is(false));
        List<HttpAppResource> appResources=new ArrayList<>();
        switch (dataPermissionTypeEnum) {
            case NODE_ID:
                query.addCriteria(Criteria.where("nodes.nid").in(value));
                appResources = mongoTemplate.find(query, HttpAppResource.class, "httpApp");
                if(appResources.isEmpty()){
                    list.add(VIRTUAL_APP);
                    return list;
                }
                break;
            case APP_DEPARTMENT:
                Criteria cr = org.springframework.data.mongodb.core.query.Criteria.where("departments.properties")
                        .elemMatch(org.springframework.data.mongodb.core.query.Criteria.where("key")
                                .is("部门")
                                .and("value").in(value));
                query.addCriteria(cr);
                appResources = mongoTemplate.find(query, HttpAppResource.class, "httpApp");
                if(appResources.isEmpty()){
                    list.add(VIRTUAL_APP);
                    return list;
                }
                break;
            case DEPLOY_DOMAIN:
                query.addCriteria(Criteria.where("deployDomains").in(value));
                appResources = mongoTemplate.find(query, HttpAppResource.class, "httpApp");
                if(appResources.isEmpty()){
                    list.add(VIRTUAL_APP);
                    return list;
                }
                break;
            case APP_SPECIFY:
                list.addAll(Arrays.asList(value));
                return list;
        }
        list = appResources.stream().map(e -> e.getUri()).collect(Collectors.toList());
        return list;
    }

    public String convertDbKey(DataPermissionTypeEnum dataPermissionTypeEnum, Module module) {
            switch (module) {
                case APP:
                    return AppCriteriaKey(dataPermissionTypeEnum);
                case API:
                    return ApiCriteriaKey(dataPermissionTypeEnum);
                case WEAKNESS:
                    return WeaknessCriteriaKey(dataPermissionTypeEnum);
                case RISK:
                case AGGRISK:
                    return RiskCriteriaKey(dataPermissionTypeEnum);
                case THREAT:
                    return ThreatCriteriaKey(dataPermissionTypeEnum);

            }
            return dataPermissionTypeEnum.name();
        }
    private String ThreatCriteriaKey(DataPermissionTypeEnum dataPermissionTypeEnum) {
        switch (dataPermissionTypeEnum) {
            case NODE_ID:
                return "nodes.nid";
            case APP_FEATURELABEL:
                return "featureLabels";
            case DEPLOY_DOMAIN:
                return "deployDomains";
            case APP_SPECIFY:
                return "appUriList";
            default:
                return dataPermissionTypeEnum.name();
        }
    }

    private String RiskCriteriaKey(DataPermissionTypeEnum dataPermissionTypeEnum) {
        switch (dataPermissionTypeEnum) {
            case NODE_ID:
                return "nodes.nid";
            case APP_FEATURELABEL:
                return "featureLabels";
            case DEPLOY_DOMAIN:
                return "deployDomains";
            case APP_SPECIFY:
                return "appUri";
            case APP_DEPARTMENT:
                return "departments.properties";
            default:
                return dataPermissionTypeEnum.name();
        }
    }

    private String WeaknessCriteriaKey(DataPermissionTypeEnum dataPermissionTypeEnum) {
        switch (dataPermissionTypeEnum) {
            case NODE_ID:
                return "nodes.nid";
            case APP_FEATURELABEL:
                return "featureLabels";
            case DEPLOY_DOMAIN:
                return "api.deployDomains";
            case APP_SPECIFY:
                return "appUri";
            case APP_DEPARTMENT:
                return "api.departments.properties";
            default:
                return dataPermissionTypeEnum.name();
        }
    }

    private String ApiCriteriaKey(DataPermissionTypeEnum dataPermissionTypeEnum) {
        switch (dataPermissionTypeEnum) {
            case NODE_ID:
                return "nodes.nid";
            case APP_FEATURELABEL:
                return "featureLabels";
            case DEPLOY_DOMAIN:
                return "deployDomains";
            case APP_SPECIFY:
                return "appUri";
            case APP_DEPARTMENT:
                return "departments.properties";
            default:
                return dataPermissionTypeEnum.name();
        }
    }

    private String AppCriteriaKey(DataPermissionTypeEnum dataPermissionTypeEnum) {
        switch (dataPermissionTypeEnum) {
            case NODE_ID:
                return "nodes.nid";
            case APP_FEATURELABEL:
                return "featureLabels";
            case DEPLOY_DOMAIN:
                return "deployDomains";
            case APP_SPECIFY:
                return "uri";
            case APP_DEPARTMENT:
                return "departments.properties";
            default:
                return dataPermissionTypeEnum.name();
        }

    }
}