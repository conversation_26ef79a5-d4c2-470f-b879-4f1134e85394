package com.quanzhi.auditapiv2.common.dal.dto.weakness;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Data;

import java.util.List;

/**
 * @Auther: yangzixian
 * @Date: 2021/8/30 11:01
 * @Description:
 */
@Data
@Builder
@ApiModel("弱点分布")
public class WeaknessDistributedDto {

    private String id;

    private String type;

    private String viewType;

    @ApiModelProperty("弱点分布信息 - 外圈")
    private List<weaknessInfo> weaknessDistributed;

    @ApiModelProperty("弱点分布信息 - 内圈")
    private List<innerEntity> weaknessInnerDistributed;

    @ApiModelProperty("总弱点个数")
    private Long totalCount;

    @Data
    @Builder
    public static final class innerEntity implements Comparable<WeaknessDistributedDto.innerEntity> {
        @ApiModelProperty("id")
        private String id;
        @ApiModelProperty("名称")
        private String name;
        @ApiModelProperty("数量")
        private long count;

        @Override
        public int compareTo(innerEntity o) {
            return Long.compare(o.count, count);
        }

    }

    @Data
    @Builder
    public static class weaknessInfo implements Comparable<WeaknessDistributedDto.weaknessInfo> {
        @ApiModelProperty("弱点ID")
        private String id;
        @ApiModelProperty("弱点名称")
        private String name;
        @ApiModelProperty("弱点类型")
        private String type;
        @ApiModelProperty("弱点类型名称")
        private String typeName;
        @ApiModelProperty("弱点访问域")
        private String visitDomain;
        @ApiModelProperty("弱点数量")
        private Long count;

        @Override
        public int compareTo(weaknessInfo o) {
            return Long.compare(o.count, count);
        }

    }

}
