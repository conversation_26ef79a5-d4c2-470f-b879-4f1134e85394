package com.quanzhi.auditapiv2.common.dal.entity.riskEolink;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
public class AddStrategyDto {

    @ApiModelProperty(value = "id", name = "id")
    private String id;

    @ApiModelProperty(value = "uuid", name = "uuid")
    private String uuid;

    /**
     * 风险ID
     */
    @ApiModelProperty(value = "风险ID", name = "riskId")
    private String riskId;

    @ApiModelProperty(value = "风险ID对应页面上的ID", name = "operationId")
    private String operationId;

    /**
     * 威胁IP
     */
    @ApiModelProperty(value = "威胁IP", name = "ip")
    private String ip;

    @ApiModelProperty(value = "多个威胁IP", name = "ips")
    private List<String> ips;

    /**
     * 威胁账号
     */
    @ApiModelProperty(value = "威胁账号", name = "account")
    private List<String> account;

    @ApiModelProperty(value = "威胁账号关联ip", name = "accountRelatedIps")
    private List<String> accountRelatedIps;

    private String api;

    /**
     * 获取IP信息的配置
     */
    @ApiModelProperty(value = "获取IP信息的配置", name = "actionConfig")
    private String actionConfig;

    @ApiModelProperty(value = "策略名称", name = "name")
    private String name;

    @ApiModelProperty(value = "策略类型", name = "strategyType")
    private String strategyType;

    /**
     * 阻断时间/限流次数
     */
    @ApiModelProperty(value = "阻断时间/限流次数", name = "count")
    private Integer count;

    @ApiModelProperty(value = "是否生效与次IP在API的其他风险", name = "isAll")
    private Boolean isAll;


    @ApiModelProperty(value = "策略描述", name = "desc")
    private String desc;

    @ApiModelProperty(value = "策略指标和指标规则废弃", name = "strategyIndicators")
    private StrategyRuleDto strategyIndicators;

    @ApiModelProperty(value = "策略指标和指标规则", name = "filters")
    private StrategyRuleDto filters;

    @ApiModelProperty(value = "作用纬度", name = "dimension")
    private Dimension dimension;

    @ApiModelProperty(value = "管控状态 1开启 0关闭", name = "status")
    private Integer status;

    @ApiModelProperty(value = "策略来源 ip/api/strategy", name = "type")
    private String createType;

    private String updateTime;

    private String createTime;

    @ApiModelProperty(value = "删除表达 1删除 0未删除", name = "status")
    private Integer delFlag;

    @Data
    public static class Dimension {

        @ApiModelProperty(value = "作用维度类型 应用app API api", name = "type")
        private String type;

        @ApiModelProperty(value = "作用维度值", name = "value")
        private String value;

    }

}
