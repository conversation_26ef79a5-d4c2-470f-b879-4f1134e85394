package com.quanzhi.auditapiv2.common.dal.dao.impl.app;

import com.quanzhi.auditapiv2.common.dal.dao.app.HttpAppAccountDao;
import com.quanzhi.metabase.client.MetabaseClientTemplate;
import com.quanzhi.metabase.core.model.http.app.HttpAppAccount;
import com.quanzhi.metabase.core.model.query.MetabaseQuery;
import com.quanzhi.metabase.core.model.query.Predicate;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Repository;
import org.springframework.util.ObjectUtils;

import java.util.Collections;
import java.util.List;
import java.util.regex.Pattern;

@Repository
public class HttpAppAccountDaoImpl implements HttpAppAccountDao {

    private final MetabaseClientTemplate metabaseClientTemplate;

    public HttpAppAccountDaoImpl(MetabaseClientTemplate metabaseClientTemplate) {
        this.metabaseClientTemplate = metabaseClientTemplate;
    }

    @Override
    public HttpAppAccount findByUri(String uri) {
        return metabaseClientTemplate.findByUri(uri, HttpAppAccount.class);
    }

    @Override
    public List<HttpAppAccount> findByUriScope(String uri) {
        return metabaseClientTemplate.find(new MetabaseQuery().where("uris", Predicate.IS, uri), HttpAppAccount.class);
    }

    @Override
    public List<HttpAppAccount> lstAllGlobal() {
        return metabaseClientTemplate.find(new MetabaseQuery().where("all", Predicate.IS, true), HttpAppAccount.class);
    }

    @Override
    public Page<HttpAppAccount> pageGroupScope(String host, Pageable pageable) {
        MetabaseQuery metabaseQuery = new MetabaseQuery();
        if (!ObjectUtils.isEmpty(host)) {
            metabaseQuery.where("uris", Predicate.REGEX, "^httpapp:.*" + Pattern.quote(host));
        }
        metabaseQuery.where("groupType", Predicate.IS, HttpAppAccount.GroupType.GROUP.name());
        long count = metabaseClientTemplate.count(metabaseQuery, HttpAppAccount.class);
        List<HttpAppAccount> list = null;
        if (count > 0) {
            list = metabaseClientTemplate.find(metabaseQuery.limit(pageable.getPageSize()).skip(pageable.getPageSize() * pageable.getPageNumber()), HttpAppAccount.class);
        }
        if (list == null) {
            list = Collections.emptyList();
        }
        return new PageImpl<>(list, pageable, count);
    }

    @Override
    public HttpAppAccount getOne(String id) {
        return metabaseClientTemplate.findOne(id, HttpAppAccount.class);
    }

    @Override
    public HttpAppAccount save(HttpAppAccount appAccount) {
        return metabaseClientTemplate.save(appAccount);
    }

    @Override
    public void delete(HttpAppAccount appAccount) {
        metabaseClientTemplate.delete(appAccount.getId(), HttpAppAccount.class);
    }

    @Override
    public long countGlobal() {
        return metabaseClientTemplate.count(new MetabaseQuery().where("all", Predicate.IS, true), HttpAppAccount.class);
    }

    @Override
    public long countByUriScope(String uri) {
        return metabaseClientTemplate.count(new MetabaseQuery().where("uris", Predicate.IS, uri), HttpAppAccount.class);
    }

    @Override
    public long countByUriScopeGroup(String uri) {
        return metabaseClientTemplate.count(new MetabaseQuery().where("uris", Predicate.IS, uri).where("groupType", Predicate.IS, HttpAppAccount.GroupType.GROUP.name()), HttpAppAccount.class);
    }
}
