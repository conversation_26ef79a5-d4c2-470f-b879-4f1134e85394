package com.quanzhi.auditapiv2.common.dal.dao.impl;

import com.quanzhi.auditapiv2.common.dal.dao.IStrategyInfoDto;
import com.quanzhi.auditapiv2.common.dal.entity.riskEolink.AddStrategyDto;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.stereotype.Repository;

import java.util.ArrayList;
import java.util.List;

@Repository
public class StrategyInfoDtoImpl implements IStrategyInfoDto {
    @Autowired
    private MongoTemplate mongoTemplate;

    private String collectionName = "strategyInfo";

    @Override
    public AddStrategyDto save(AddStrategyDto dto) {
        String time = String.valueOf(System.currentTimeMillis());
        dto.setCreateTime(time);
        dto.setUpdateTime(time);
        return mongoTemplate.insert(dto, collectionName);
    }

    @Override
    public AddStrategyDto update(AddStrategyDto dto) {

        Criteria criteria = Criteria.where("_id").is(dto.getId());
        Query query = new Query(criteria);
        Update update = new Update();
        String time = String.valueOf(System.currentTimeMillis());

        update.set("updateTime", time);
        update.set("name", dto.getName());
        update.set("count", dto.getCount());
        update.set("uuid", dto.getUuid());
        update.set("isAll", dto.getIsAll());
        update.set("desc", dto.getDesc());
        update.set("strategyIndicators", dto.getStrategyIndicators());
        update.set("dimension", dto.getDimension());
        update.set("status", dto.getStatus());
        update.set("riskId", dto.getRiskId());
        update.set("api", dto.getApi());

        mongoTemplate.updateFirst(query, update, collectionName);

        return dto;
    }

    @Override
    public void updateStatus(String id, Integer status) {

        Criteria criteria = Criteria.where("_id").is(id);
        Query query = new Query(criteria);
        Update update = new Update();
        String time = String.valueOf(System.currentTimeMillis());

        update.set("status", status);
        update.set("updateTime", time);

        mongoTemplate.updateFirst(query, update, collectionName);
    }

    @Override
    public List<AddStrategyDto> listRiskStrategy(String api, String app) {
        return null;
    }

    @Override
    public AddStrategyDto findById(String id) {
        Criteria criteria = Criteria.where("_id").is(id);
        return mongoTemplate.findOne(new Query(criteria), AddStrategyDto.class, collectionName);
    }

    @Override
    public AddStrategyDto findByUuid(String uuid) {
        Criteria criteria = Criteria.where("uuid").is(uuid);
        return mongoTemplate.findOne(new Query(criteria), AddStrategyDto.class, collectionName);
    }

    @Override
    public List<AddStrategyDto> listRiskStrategyByPage(Integer page, Integer limit, Integer sorts, String sortField, String name, String strategyType) {
        Criteria criteria = Criteria.where("delFlag").is(0);

        // 构建查询条件
        List<Criteria> subCriteria = new ArrayList<>();
        if (name != null && !name.equals("")) {
            subCriteria.add(Criteria.where("name").regex(name));
        }
        if (strategyType != null && !strategyType.equals("")) {
            subCriteria.add(Criteria.where("strategyType").regex(strategyType));
        }
        if (!subCriteria.isEmpty()) {
            criteria.orOperator(subCriteria.toArray(new Criteria[0]));
        }

        // 构建排序条件
        Sort sort = Sort.by(Sort.Direction.DESC, sortField != null ? sortField : "createTime");

        // 执行查询并返回结果
        return mongoTemplate.find(new Query(criteria).with(sort).limit(limit).skip((page - 1) * limit), AddStrategyDto.class, collectionName);
    }


    @Override
    public long selectCount(String name, String strategyType) {
        Criteria criteria = Criteria.where("delFlag").is(0);

        // 构建查询条件
        List<Criteria> subCriteria = new ArrayList<>();
        if (name != null && !name.equals("")) {
            subCriteria.add(Criteria.where("name").regex(name));
        }
        if (strategyType != null && !strategyType.equals("")) {
            subCriteria.add(Criteria.where("strategyType").regex(strategyType));
        }

        if (!subCriteria.isEmpty()) {
            criteria.andOperator(subCriteria.toArray(new Criteria[0]));
        }

        return mongoTemplate.count(new Query(criteria), collectionName);
    }

    @Override
    public boolean deleteById(String id) {

        Criteria criteria = Criteria.where("_id").is(id);
        Query query = new Query(criteria);
        mongoTemplate.remove(query, collectionName);
        return true;
    }

    @Override
    public void deleteUpdateDelFlag(String id) {

        Criteria criteria = Criteria.where("_id").is(id);
        Query query = new Query(criteria);
        Update update = new Update();

        update.set("delFlag", 1);

        mongoTemplate.updateFirst(query, update, collectionName);
    }

    @Override
    public List<AddStrategyDto> selectStrategyByRisk(String riskName, Integer riskLevel) {
        List<String> riskNameList = new ArrayList<>();
        List<String> riskLevelList = new ArrayList<>();
        riskNameList.add(riskName);
        riskLevelList.add(riskLevel.toString());
        Criteria criteria = new Criteria();

        // 构建查询条件
        List<Criteria> subCriteria = new ArrayList<>();
        if (riskName != null && !riskName.equals("")) {
            subCriteria.add(Criteria.where("strategyIndicators.rules.type").is("riskName").and("strategyIndicators.rules.content").in(riskNameList));
        }
        if (riskLevel != null) {
            subCriteria.add(Criteria.where("strategyIndicators.rules.type").is("riskLevel").and("strategyIndicators.rules.content").in(riskLevelList));
        }
        if (!subCriteria.isEmpty()) {
            criteria.orOperator(subCriteria.toArray(new Criteria[0]));
        }

        return mongoTemplate.find(new Query(criteria), AddStrategyDto.class, collectionName);
    }

    @Override
    public AddStrategyDto selectStrategyByAccount(String account) {
        return mongoTemplate.findOne(new Query().addCriteria(Criteria.where("account").in(account)), AddStrategyDto.class, collectionName);
    }

}
