package com.quanzhi.auditapiv2.common.dal.entity;

import com.quanzhi.auditapiv2.common.util.utils.DataUtil;
import lombok.Data;

import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/2/20 上午10:28
 */
@Data
public class ReportConfig {

    private String _id;

    private String name;

    /**
     * 报告执行进度百分比数值
     */
    private Integer percentage;

    private ReportStatus status = ReportStatus.RUNNING;

    private List<ReportExportType> exportTypes = Arrays.asList(ReportExportType.HTML, ReportExportType.EXCEL, ReportExportType.WORD, ReportExportType.PDF);

    /**
     * 数据源查询
     */
    private List<ReportDto> reportSearchDtoList;

    /**
     * 报告大小
     */
    private Long reportSize;

    private Long createTime;

    private Long endTime;

    private Long duration;

    /**
     * 报告生成的形式
     */
    public enum ReportExportType {

        HTML,EXCEL,WORD,PDF
    }


    public enum ReportStatus{

        /**
         * 运行中
         */
        RUNNING(1),

        /**
         * 已成功
         */
        SUCCESS(2),

        /**
         * 失败
         */
        FAIL(3);

        private int status;

        ReportStatus(int status) {this.status = status;}

        public int value() {return this.status;}
    }

    public void check(ReportConfig reportTask) throws Exception {

        if(DataUtil.isEmpty( reportTask.getName() )) {
            throw  new Exception("任务名称是必须的！");
        }

        if(DataUtil.isEmpty( reportTask.getReportSearchDtoList() )) {
            throw  new Exception("必须选择资产或者弱点！");
        }
    }

    public boolean containsHtmlExportType() {

        return this.exportTypes.contains( ReportExportType.HTML );
    }

    public boolean containsExcelExportType() {

        return this.exportTypes.contains( ReportExportType.EXCEL );
    }

    public boolean containsWordExportType() {

        return this.exportTypes.contains( ReportExportType.WORD );
    }

    public boolean containsPdfExportType() {

        return this.exportTypes.contains( ReportExportType.PDF );
    }
}
