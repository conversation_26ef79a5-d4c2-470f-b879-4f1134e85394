package com.quanzhi.auditapiv2.common.dal.dao.base;


import com.quanzhi.auditapiv2.common.util.dto.AggregationDto;
import com.quanzhi.auditapiv2.common.util.dto.GroupDto;
import com.quanzhi.metabase.core.model.query.AggregationResult;
import com.quanzhi.metabase.core.model.query.MetabaseGroupOperation;
import com.quanzhi.metabase.core.model.query.MetabaseQuery;
import org.springframework.data.mongodb.core.query.Criteria;

import java.util.List;
import java.util.Map;

public interface IMetabaseDao <T> {

    /**
     * 获取索引
     *
     * @return
     */
    T findOne(String id);

    T findQueryOne(Map<String,String> queryMap);

    /**
     * 根据字段更新数据
     */
    boolean update(String id,T model);

    List<T> getList(MetabaseQuery query);

    /**
     * 获取总数
     */
    long getCount(MetabaseQuery query);

    /**
     * 保存配置
     * @param model
     * @return
     */
    T save(T model);

    boolean exist(MetabaseQuery query);

    /**
     * 数据聚合
     * @param query
     * @param metabaseGroupOperation
     * @param clz
     * @return
     */
    public List<AggregationResult> aggregate(MetabaseQuery query, MetabaseGroupOperation metabaseGroupOperation, Class<?> clz);

    /**
     * 分组查询
     * @Comments:  <对此方法的描述，可以引用系统设计中的描述>
     * <AUTHOR> [<EMAIL>]
     * @since 2021-08-10 9:56
     * @param
     * @return
     */
    List<AggregationDto> group(Criteria criteria, GroupDto groupDto, Integer sort, Integer page, Integer limit, String collectionName) throws Exception;
}
