package com.quanzhi.auditapiv2.common.dal.dao;

import com.quanzhi.auditapiv2.common.dal.dao.base.IMetabaseDao;
import com.quanzhi.metabase.core.model.http.HttpApiSample;

import java.util.List;

/**
 * 
 * 《接口样例持久层接口》
 * 
 * 
 * @Project: 
 * @Module ID:
 * @Comments: 
 * @JDK version used:      <JDK1.8> 
 * <AUTHOR> [<EMAIL>]
 * @since 2020-04-08-上午9:56:10
 */
public interface IHttpApiSampleDao extends IMetabaseDao<HttpApiSample> {

	/**
	 * 接口快照id查询接口样例列表(分页)
	 * @Comments:  <对此方法的描述，可以引用系统设计中的描述>
	 * <AUTHOR> [<EMAIL>]
	 * @since 2020-04-08 9:56
	 * @param
	 * @return
	 */
	List<HttpApiSample> selectHttpApiSampleListByHttpApiSnapshotId(String httpApiSnapshotId, Integer page, Integer limit) throws Exception;

	/**
	 * 接口快照id查询接口样例数量
	 * @Comments:  <对此方法的描述，可以引用系统设计中的描述>
	 * <AUTHOR> [<EMAIL>]
	 * @since 2020-04-08 9:56
	 * @param
	 * @return
	 */
	Long totalCount(String httpApiSnapshotId) throws Exception;
}
