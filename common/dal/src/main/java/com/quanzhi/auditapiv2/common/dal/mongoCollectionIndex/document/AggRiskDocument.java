package com.quanzhi.auditapiv2.common.dal.mongoCollectionIndex.document;

import com.quanzhi.auditapiv2.common.dal.mongoCollectionIndex.MongoCollectionConstant;
import org.springframework.data.mongodb.core.index.CompoundIndex;
import org.springframework.data.mongodb.core.index.CompoundIndexes;
import org.springframework.data.mongodb.core.index.Indexed;
import org.springframework.data.mongodb.core.mapping.Document;

import java.util.HashSet;
import java.util.Set;

/**
 * <AUTHOR>
 * @date 2022/12/18 上午11:27
 */
@Document(collection = MongoCollectionConstant.AGG_RISK)
@CompoundIndexes({
        @CompoundIndex(name = "riskMark_level_id", def = "{riskMark:1,level:1,_id:1}", background = true),
        @CompoundIndex(name = "riskMark_firstTime_id", def = "{riskMark:1,firstTime:1,_id:1}", background = true),
        @CompoundIndex(name = "riskMark_lastTime_id", def = "{riskMark:1,lastTime:1,_id:1}", background = true),
})
public class AggRiskDocument {

    @Indexed(background = true)
    private Set<String> apiUris;

    @Indexed(background = true)
    private Set<String> apiUrls;

    @Indexed(background = true)
    private Set<String> appNames;

    @Indexed(background = true)
    private Set<String> appUris;

    @Indexed(background = true)
    private Set<String> hosts;

    @Indexed(background = true)
    private Set<String> ips;

    @Indexed(background = true)
    private Set<String> entities;

    @Indexed(background = true)
    private Set<String> entityTypes;

    @Indexed(background = true)
    private Integer level;

    @Indexed(background = true)
    private String name;

    @Indexed(background = true)
    private String desc;

    @Indexed(background = true)
    private Integer state;

    @Indexed(background = true)
    private Set<String> deployDomains = new HashSet<>();

    @Indexed(background = true)
    private String type;

    @Indexed(background = true)
    private Boolean riskMark;

    @Indexed(background = true)
    private String policyId;

    @Indexed(background = true)
    private Long firstTime;

    @Indexed(background = true)
    private Long lastTime;

}
