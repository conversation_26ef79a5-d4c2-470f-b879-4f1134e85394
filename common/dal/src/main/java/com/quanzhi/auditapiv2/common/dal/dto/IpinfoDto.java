package com.quanzhi.auditapiv2.common.dal.dto;

import com.quanzhi.audit_core.common.model.IpInfo;
import lombok.Data;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.io.Serializable;
import java.util.List;

/**
 * @ClassName IpinfoDto
 * @Description TODO
 * <AUTHOR>
 * @Date 2020/11/21 15:40
 **/
@Data
public class IpinfoDto implements Serializable {

    private String ip;

    private String country;

    private String province;

    private String city;

    /**
     * 访问次数
     */
    private Long visitCnt;

    /**
     * 关联异常数量
     */
    private Long abnCnt;

    /**
     * 首次发现日期
     */
    private String firstDate;

    /**
     * 当天
     */
    private String date;

    /**
     * 最后发现日期
     */
    private String lastDate;

    /**
     * 威胁标签
     */
    private List<String> threatLabels;

    /**
     * 威胁确认时间
     */
    private Long threatConfirmTime;

    /**
     * 异常确认次数
     */
    private Integer confirmExceptionCnt;

    /**
     * 地域，格式：country.province.city 例：中国.浙江.杭州
     */
    private String area;

    @Mapper
    public interface IpInfoDtoMapper {

        IpInfoDtoMapper INSTANCE = Mappers.getMapper(IpInfoDtoMapper.class);

        IpinfoDto convert(IpInfo ipInfo);

        List<IpinfoDto> convert(List<IpInfo> ipInfos);

    }
}
