package com.quanzhi.auditapiv2.common.dal.dao;

import com.quanzhi.auditapiv2.common.dal.dao.base.IBaseDao;
import com.quanzhi.auditapiv2.common.dal.entity.Notify;

import java.util.List;
import java.util.Map;

/**
 *
 * 《通知持久层接口》
 *
 *
 * @Project:
 * @Module ID:
 * @Comments:
 * @JDK version used:      <JDK1.8> 
 * <AUTHOR> [<EMAIL>]
 * @since 2020-04-08-上午9:56:10
 */
public interface INotifyDao extends IBaseDao<Notify> {

	/**
	 * 查询通知记录列表(分页)
	 * @Comments:  <对此方法的描述，可以引用系统设计中的描述>
	 * <AUTHOR> [<EMAIL>]
	 * @since 2020-04-08 9:56
	 * @param
	 * @return
	 */
	List<Notify> selectNotifyList(Map<String, Object> map, String field, Integer sort, Integer page, Integer limit) throws Exception;

	/**
	 * 查询通知记录数量
	 * @Comments:  <对此方法的描述，可以引用系统设计中的描述>
	 * <AUTHOR> [<EMAIL>]
	 * @since 2020-04-08 9:56
	 * @param
	 * @return
	 */
	Long totalCount(Map<String, Object> map) throws Exception;

	/**
	 * 查询通知记录列表
	 * @Comments:  <对此方法的描述，可以引用系统设计中的描述>
	 * <AUTHOR> [<EMAIL>]
	 * @since 2020-04-08 9:56
	 * @param
	 * @return
	 */
	List<Notify> selectNotifyList(Map<String, Object> map) throws Exception;

	/**
	 * 去重查询通知记录
	 * @Comments:  <对此方法的描述，可以引用系统设计中的描述>
	 * <AUTHOR> [<EMAIL>]
	 * @since 2020-04-08 9:56
	 * @param
	 * @return
	 */
	List<String> selectDistinctNotifyList(Map<String, Object> map) throws Exception;

	/**
	 * id查询通知记录详情
	 * @Comments:  <对此方法的描述，可以引用系统设计中的描述>
	 * <AUTHOR> [<EMAIL>]
	 * @since 2020-04-08 9:56
	 * @param
	 * @return
	 */
	Notify selectNotifyById(String id) throws Exception;

	/**
	 * 新增通知记录
	 * @Comments:  <对此方法的描述，可以引用系统设计中的描述>
	 * <AUTHOR> [<EMAIL>]
	 * @since 2020-04-08 9:56
	 * @param
	 * @return
	 */
	Notify insertNotify(Notify notify) throws Exception;

	/**
	 * 编辑通知记录
	 * @Comments:  <对此方法的描述，可以引用系统设计中的描述>
	 * <AUTHOR> [<EMAIL>]
	 * @since 2020-04-08 9:56
	 * @param
	 * @return
	 */
	Notify updateNotify(Notify notify) throws Exception;

	/**
	 * 一键已读
	 * @Comments:  <对此方法的描述，可以引用系统设计中的描述>
	 * <AUTHOR> [<EMAIL>]
	 * @since 2020-04-08 9:56
	 * @param
	 * @return
	 */
	void readAll() throws Exception;
}
