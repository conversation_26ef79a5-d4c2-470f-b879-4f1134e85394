package com.quanzhi.auditapiv2.common.dal.dao;

import com.quanzhi.auditapiv2.common.dal.entity.TrafficStatistics;
import org.springframework.data.mongodb.core.aggregation.AggregationResults;

/**
 * @Auther: yangzixian
 * @Date: 2021/8/30 19:10
 * @Description:
 */
public interface IAppDateStatDao {

    /**
     * 获取时间范围内的流量数据，按日期分组
     * @param startDate
     * @param endDate
     * @return
     */
    AggregationResults<TrafficStatistics> getTrafficStatisticsList(String startDate, String endDate);

}
