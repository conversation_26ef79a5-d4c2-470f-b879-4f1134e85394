package com.quanzhi.auditapiv2.common.dal.dao;

import com.quanzhi.audit_core.common.model.DataLabel;
import com.quanzhi.auditapiv2.common.dal.dao.base.INacosBaseDao;
import com.quanzhi.auditapiv2.common.dal.dto.DataLabelSearchDto;

import java.util.Optional;

public interface IDataLabelNacosDao extends INacosBaseDao<DataLabel> {

    Boolean checkNameExist(String name,String excludeId);

    long getCount(DataLabelSearchDto searchDto);

    Optional<DataLabel> convert(String labelId);
}
