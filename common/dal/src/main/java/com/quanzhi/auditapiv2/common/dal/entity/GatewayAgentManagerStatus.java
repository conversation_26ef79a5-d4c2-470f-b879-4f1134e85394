package com.quanzhi.auditapiv2.common.dal.entity;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.ArrayList;
import java.util.List;


/**
 * 网关上报的agent信息
 */
@Data
@NoArgsConstructor
public class GatewayAgentManagerStatus {

    /**
     * id
     */
    private String id;

    /**
     * 网关别名
     */
    private String name;

    /**
     * agent 服务端ip
     */
    private String gatewayIp;

    /**
     * agent采集端ip
     */
    private String agentIp;

    /**
     * agent采集端端口
     */
    private Integer agentPort;

    /**
     * 账号
     */
    private String account;

    /**
     * 密码
     */
    private String password;

    /**
     * 启用禁用
     */
    private short enable;

    /**
     * 状态 0异常 1正常 2未开启 3安装失败
     */
    private Integer status;

    private String errorMsg;

    /**
     * 网口列表
     */
    private List<String> eths;

    // agent监听的端口
    private List<String> checkedPorts = new ArrayList<>();

    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间", name = "createTime")
    private Long createTime;

    /**
     * 更新时间
     */
    @ApiModelProperty(value = "更新时间", name = "updateTime")
    private Long updateTime;

}
