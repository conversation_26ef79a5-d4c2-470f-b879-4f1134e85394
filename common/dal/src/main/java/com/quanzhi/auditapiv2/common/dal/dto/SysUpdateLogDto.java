package com.quanzhi.auditapiv2.common.dal.dto;

import com.quanzhi.auditapiv2.common.dal.entity.SysUpdateLog;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 *
 * 《系统升级日志dto》
 *
 *
 * @Project:
 * @Module ID:
 * @Comments:
 * @JDK version used:      <JDK1.8>
 * <AUTHOR> [<EMAIL>]
 * @since 2020-04-08-上午9:56:10
 */
@Data
public class SysUpdateLogDto {

    @ApiModelProperty(value = "id", name = "id")
    private String id;

    /**
     * 升级状态
     * @see com.quanzhi.auditapiv2.common.dal.entity.SysUpdateLog.StatusEnum
     */
    @ApiModelProperty(value = "升级状态", name = "updateStatus")
    private Integer updateStatus;

    /**
     * 升级状态名称
     */
    @ApiModelProperty(value = "升级状态名称", name = "updateStatusName")
    private String updateStatusName;

    /**
     * 升级进度
     */
    @ApiModelProperty(value = "升级进度", name = "updateProgress")
    private Integer updateProgress;

    /**
     * 升级日志内容
     */
    @ApiModelProperty(value = "升级日志内容", name = "updateLog")
    private String updateLog;

    /**
     * 回滚状态
     * @see com.quanzhi.auditapiv2.common.dal.entity.SysUpdateLog.StatusEnum
     */
    @ApiModelProperty(value = "回滚状态", name = "rollbackStatus")
    private Integer rollbackStatus;

    /**
     * 回滚状态名称
     */
    @ApiModelProperty(value = "回滚状态名称", name = "rollbackStatusName")
    private String rollbackStatusName;

    /**
     * 回滚进度
     */
    @ApiModelProperty(value = "回滚进度", name = "rollbackProgress")
    private Integer rollbackProgress;

    /**
     * 回滚日志内容
     */
    @ApiModelProperty(value = "回滚日志内容", name = "rollbackLog")
    private String rollbackLog;

    /**
     * 当前版本号
     */
    @ApiModelProperty(value = "当前版本号", name = "version")
    private String version;

    /**
     * 旧版本号
     */
    @ApiModelProperty(value = "旧版本号", name = "oldVersion")
    private String oldVersion;

    /**
     * 创建者
     */
    @ApiModelProperty(value = "创建者", name = "createBy")
    private String createBy;

    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间", name = "createDate")
    private Long createDate;

    /**
     * model转dto
     */
    @Mapper
    public interface SysUpdateLogMapper {

        SysUpdateLogMapper INSTANCE = Mappers.getMapper(SysUpdateLogMapper.class);

        SysUpdateLogDto convert(SysUpdateLog sysUpdateLog);

    }
}
