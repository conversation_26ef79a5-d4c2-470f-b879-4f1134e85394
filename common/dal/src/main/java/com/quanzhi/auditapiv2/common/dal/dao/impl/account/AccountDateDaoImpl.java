package com.quanzhi.auditapiv2.common.dal.dao.impl.account;

import com.quanzhi.auditapiv2.common.dal.dao.account.AccountDateDao;
import com.quanzhi.auditapiv2.common.dal.entity.AccountDateInfo;
import com.quanzhi.auditapiv2.common.util.utils.StringUtils;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.aggregation.Aggregation;
import org.springframework.data.mongodb.core.aggregation.AggregationResults;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public class AccountDateDaoImpl implements AccountDateDao {

    private final MongoTemplate mongoTemplate;

    public AccountDateDaoImpl(MongoTemplate mongoTemplate) {
        this.mongoTemplate = mongoTemplate;
    }

    @Override
    public List<AccountDateInfo> getAccountVisitCntByDate(String account, String start, String end) {
        Criteria criteria = Criteria.where("date").gte(start).lte(end);
        if (!org.springframework.util.StringUtils.isEmpty(account)) {
            criteria.and("account").is(account);
        }
        Aggregation aggregation = Aggregation.newAggregation(Aggregation.match(criteria), Aggregation.group("date").sum("visitCnt").as("visitCnt"), Aggregation.project("date", "visitCnt"));
        AggregationResults<AccountDateInfo> accountDateInfos = mongoTemplate.aggregate(aggregation, "accountDateInfo", AccountDateInfo.class);
        return accountDateInfos.getMappedResults();
    }
}
