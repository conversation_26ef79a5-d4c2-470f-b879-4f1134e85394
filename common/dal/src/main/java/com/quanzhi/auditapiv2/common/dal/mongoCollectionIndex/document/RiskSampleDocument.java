package com.quanzhi.auditapiv2.common.dal.mongoCollectionIndex.document;

import com.quanzhi.auditapiv2.common.dal.mongoCollectionIndex.MongoCollectionConstant;
import org.springframework.data.mongodb.core.index.CompoundIndex;
import org.springframework.data.mongodb.core.index.CompoundIndexes;
import org.springframework.data.mongodb.core.index.Indexed;
import org.springframework.data.mongodb.core.mapping.Document;

/**
 * <AUTHOR>
 * @date 2022/12/18 上午11:27
 */
@Document(collection = MongoCollectionConstant.RISK_SAMPLE)
@CompoundIndexes({
        @CompoundIndex(name = "time_id", def = "{'httpEvent.timestamp':1, '_id':1}", background = true),
        @CompoundIndex(name = "event_ip", def = "{'httpEvent.ip':1}", background = true),
        @CompoundIndex(name = "ip_uri_login", def = "{'httpEvent.ip':1, 'httpEvent.apiUrl':1, 'httpEvent.loginInfo.loginResult':1}", background = true),
})
public class RiskSampleDocument {

    @Indexed(background = true)
    private Long createTime;

    @Indexed(background = true)
    private Boolean isLive;

//    @Indexed(background = true)
//    private String riskId;

    @Indexed(background = true)
    private String aggRiskId;

}
