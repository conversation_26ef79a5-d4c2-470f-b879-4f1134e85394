package com.quanzhi.auditapiv2.common.dal.dto.api;

import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * create at 2023/5/11 15:32
 * @description: 级联API标签
 **/
@Data
public class CascadeApiFeatureDto {

    /**
     * 组id
     */
    private String id;
    /**
     * 组名
     */
    private String name;

    private List<FeatureInfo> children;


    @Data
    public static class FeatureInfo {
        private String id;
        private String name;
        private Boolean enabled;
        /**
         * 是否内置
         */
        private Integer type;
        /**
         * 区分接口分类和接口标签
         */
        private Integer code;
    }


    public static CascadeApiFeatureDto.FeatureInfo convert(CommonApiFeature e) {
        CascadeApiFeatureDto.FeatureInfo featureInfo = new CascadeApiFeatureDto.FeatureInfo();
        featureInfo.setId(e.getId());
        featureInfo.setName(e.getName());
        featureInfo.setEnabled(e.getEnable());
        featureInfo.setType(e.getType());
        featureInfo.setCode(e.getCode());
        return featureInfo;
    }

}