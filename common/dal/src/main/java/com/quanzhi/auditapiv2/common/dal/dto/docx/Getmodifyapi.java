package com.quanzhi.auditapiv2.common.dal.dto.docx;

import lombok.Data;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 *
 * 《GET方式执行敏感操作》
 *
 *
 * @Project:
 * @Module ID:
 * @Comments:
 * @JDK version used:      <JDK1.8>
 * <AUTHOR> [<EMAIL>]
 * @since 2020-08-28-下午16:52:18
 */
@Data
public class Getmodifyapi extends ApiWeaknessExportWord {

    @Mapper
    public interface GetmodifyapiMapper {

        Getmodifyapi.GetmodifyapiMapper INSTANCE = Mappers.getMapper(Getmodifyapi.GetmodifyapiMapper.class);
        Getmodifyapi convert(ApiWeaknessExportWord apiWeaknessExportWord);
    }
}
