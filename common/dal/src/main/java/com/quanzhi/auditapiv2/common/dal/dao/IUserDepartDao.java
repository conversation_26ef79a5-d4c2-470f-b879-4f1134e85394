package com.quanzhi.auditapiv2.common.dal.dao;

import com.quanzhi.auditapiv2.common.dal.dao.base.IMetabaseDao;
import com.quanzhi.auditapiv2.common.dal.dto.UserDepartCriteriaDto;
import com.quanzhi.metabase.core.model.http.UserDepart;

import java.util.List;

/**
 * @ClassName IUserDepartDao
 * @Description TODO
 * <AUTHOR>
 * @Date 2020/10/16 17:10
 **/
public interface IUserDepartDao extends IMetabaseDao<UserDepart> {

    int saveUserDepart(UserDepart userDepart, String account);

    List<UserDepart> getUserDepartList(UserDepartCriteriaDto userDepartCriteriaDto);

    UserDepart getUserDepartById(String staffId);

    Long getCountByCriteria(UserDepartCriteriaDto userDepartCriteriaDto);

    void updateById(UserDepart userDepart);

    // 查询字段内容是账号的部门信息，只返回一条
    UserDepart getUserDepart(String account, List<String> fields);
}
