package com.quanzhi.auditapiv2.common.dal.dto;

import com.quanzhi.audit_core.common.model.ApiAccountInfo;
import lombok.Data;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * @ClassName ApiAccountInfoDto
 * @Description TODO
 * <AUTHOR>
 * @Date 2020/11/22 23:30
 **/
@Data
public class ApiAccountInfoDto {

    private String apiUri;

    /**
     * 账号
     */
    private String account;

    /**
     * 登录次数
     */
    private String loginCnt;

    /**
     * 首次发现日期
     */
    private String firstDate;

    /**
     * 最后发现日期
     */
    private String lastDate;

    /**
     * 账号标签
     */
    private List<String> threatLabels;

    @Mapper
    public interface ApiAccountInfoDtoMapper {

        ApiAccountInfoDtoMapper INSTANCE = Mappers.getMapper(ApiAccountInfoDtoMapper.class);

        ApiAccountInfoDto convert(ApiAccountInfo apiAccountInfo);

        List<ApiAccountInfoDto> convert(List<ApiAccountInfo> apiAccountInfos);

    }
}
