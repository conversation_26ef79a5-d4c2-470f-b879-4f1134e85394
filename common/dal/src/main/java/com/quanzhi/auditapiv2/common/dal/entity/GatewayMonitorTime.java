package com.quanzhi.auditapiv2.common.dal.entity;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * @ClassName GatewayMonitorTime
 * @Description TODO
 * <AUTHOR>
 * @Date 2021/2/23 10:59
 **/
@Data
@NoArgsConstructor
@AllArgsConstructor
public class GatewayMonitorTime implements Serializable {

    private String startTime;

    private String endTime;
}
