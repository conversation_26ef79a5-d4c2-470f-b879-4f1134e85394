package com.quanzhi.auditapiv2.common.dal.dto.datalabel;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * @Author: ycl
 * @Date: 2021/7/13 19:23
 * 标签查询条件dto
 */
@ApiModel
@Data
public class DataLabelQueryDto implements Serializable {
    /**
     * 标签名称
     */
    String name;

    /**
     * 识别位置
     */
    List<String> locations;

    /**
     * 识别状态
     */
    Boolean status;

    /**
     * 所属类别
     */
    String firstClass;

    /**
     * 所属级别
     */
    String labelLevel;

    /**
     * 分组标识
     */
    @ApiModelProperty(allowableValues = "classify level monitor store")
    String groupKey;
}
