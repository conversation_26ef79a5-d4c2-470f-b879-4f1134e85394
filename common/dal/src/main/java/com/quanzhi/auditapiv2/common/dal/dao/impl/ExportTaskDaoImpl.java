package com.quanzhi.auditapiv2.common.dal.dao.impl;

import com.alibaba.nacos.api.config.annotation.NacosValue;
import com.quanzhi.auditapiv2.common.dal.dao.IExportTaskDao;
import com.quanzhi.auditapiv2.common.dal.dao.base.impl.BaseDaoImpl;
import com.quanzhi.auditapiv2.common.dal.entity.ExportTaskModel;
import com.quanzhi.auditapiv2.common.dal.entity.ExportTaskStatus;
import com.quanzhi.auditapiv2.common.util.entity.ExportTaskType;
import com.quanzhi.auditapiv2.common.util.utils.DataUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.stereotype.Repository;

import javax.servlet.http.HttpServletRequest;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2019-12-08
 * @time 12:05
 */
@Repository
public class ExportTaskDaoImpl extends BaseDaoImpl<ExportTaskModel> implements IExportTaskDao {
    public static final String EXPORT_TASK_COLLECTION_NAME = "exportTask";
    private MongoTemplate mongoTemplate;

    @NacosValue(value = "${isAssetAuthorization:false}", autoRefreshed = true)
    private boolean isAssetAuthorization = false;

    @Autowired
    private HttpServletRequest request;

    @Autowired
    public ExportTaskDaoImpl(MongoTemplate mongoTemplate) {
        this.mongoTemplate = mongoTemplate;
    }

    @Override
    public ExportTaskModel findFirstTask() {
        Criteria criteria = Criteria.where("taskStatus").is(ExportTaskStatus.RUNNABLE);
        Sort sortByCreateTime = Sort.by(Sort.Direction.ASC, "createTime");
        Query query = Query.query(criteria).with(sortByCreateTime);
        ExportTaskModel exportTaskModel = mongoTemplate.findOne(query, ExportTaskModel.class, EXPORT_TASK_COLLECTION_NAME);
        if (DataUtil.isNotEmpty(exportTaskModel)) {
            return exportTaskModel;
        } else {
            //对于一直处于运行中的任务尝试重新运行
            criteria = Criteria.where("taskStatus").is(ExportTaskStatus.RUNNING);
            criteria.and("createTime").lt(System.currentTimeMillis() - 30 * 60 * 1000);
            sortByCreateTime = Sort.by(Sort.Direction.ASC, "createTime");
            query = Query.query(criteria).with(sortByCreateTime);
            return mongoTemplate.findOne(query, ExportTaskModel.class, EXPORT_TASK_COLLECTION_NAME);
        }
    }

    @Override
    public ExportTaskModel getTask(String taskId) {
        return mongoTemplate.findById(taskId, ExportTaskModel.class, EXPORT_TASK_COLLECTION_NAME);
    }

    @Override
    public Boolean taskNameExist (String taskId,String taskName) {
        return mongoTemplate.exists( Query.query( new Criteria().where("_id").ne(taskId).and("taskName").is(taskName) ), EXPORT_TASK_COLLECTION_NAME);
    }

    @Override
    public void updateTaskName (String taskId,String taskName) {

        mongoTemplate.updateFirst( Query.query( new Criteria().where("_id").is(taskId)),Update.update("taskName",taskName),EXPORT_TASK_COLLECTION_NAME);
    }

    @Override
    public List<ExportTaskModel> getTaskByTypeStatus(ExportTaskType taskType, ExportTaskStatus taskStatus) {
        Criteria criteria = new Criteria();
        if ( taskType != null ) {
            criteria = Criteria.where("taskType").is(taskType);
        }
        if ( taskStatus != null ) {
            criteria.and("taskStatus").is(taskStatus);
        }
        return mongoTemplate.find(Query.query(criteria), ExportTaskModel.class, EXPORT_TASK_COLLECTION_NAME);
    }

    @Override
    public int insertTask(ExportTaskModel task) {
        if ( task.getTaskStatus() == null ) {
            task.setTaskStatus(ExportTaskStatus.RUNNABLE);
        }
        task.setCreateTime(System.currentTimeMillis());
        mongoTemplate.insert(task,EXPORT_TASK_COLLECTION_NAME);
        return 1;
    }

    @Override
    public void removeTask(String taskId) {
        Criteria criteria =  Criteria.where("_id").is(taskId);
        mongoTemplate.remove(new Query(criteria),EXPORT_TASK_COLLECTION_NAME);

    }

    @Override
    public List<ExportTaskModel> pageTask(ExportTaskType taskType, ExportTaskStatus taskStatus, int page, int pageSize,String sort) {
        Criteria criteria = new Criteria();
        if ( taskType != null ) {
            criteria.and("taskType").is(taskType);
        }
        if ( taskStatus != null ) {
            criteria.and("taskStatus").is(taskStatus);
        }

        if(isAssetAuthorization) {

            String userId = (String) request.getSession().getAttribute("userId");
            String username = (String) request.getSession().getAttribute("username");
            if(DataUtil.isNotEmpty(userId) && DataUtil.isNotEmpty(username) && !"webadmin".equals(username)) {

                criteria.and("userId").is(userId);
            }
        }
        
        Pageable pageable = PageRequest.of(page - 1, pageSize);
        //默认倒序
        Sort sortByCreateTime = Sort.by(Sort.Direction.DESC, "createTime");
        if(DataUtil.isNotEmpty(sort) && sort.equals("asc")){
            sortByCreateTime = Sort.by(Sort.Direction.ASC, "createTime");
        }
        Query query = Query.query(criteria).with(pageable).with(sortByCreateTime);
        return mongoTemplate.find(query, ExportTaskModel.class, EXPORT_TASK_COLLECTION_NAME);
    }

    @Override
    public Integer pageTaskCount(ExportTaskType taskType, ExportTaskStatus taskStatus) {
        Criteria criteria = new Criteria();
        if ( taskType != null ) {
            criteria.and("taskType").is(taskType);
        }
        if ( taskStatus != null ) {
            criteria.and("taskStatus").is(taskStatus);
        }

        if(isAssetAuthorization) {
            
            String userId = (String) request.getSession().getAttribute("userId");
            String username = (String) request.getSession().getAttribute("username");
            if(DataUtil.isNotEmpty(userId) && DataUtil.isNotEmpty(username) && !"webadmin".equals(username)) {

                criteria.and("userId").is(userId);
            }
        }
        
        Query query = Query.query(criteria);
        return mongoTemplate.find(query, ExportTaskModel.class, EXPORT_TASK_COLLECTION_NAME).size();
    }

    @Override
    public void setStatusAndMsg(String taskId, ExportTaskStatus status, String errMsg) {
        Query query = Query.query(Criteria.where("_id").is(taskId));
        Update update = new Update();
        update.set("taskStatus", status).set("errorMsg", errMsg);
        mongoTemplate.updateFirst(query, update, EXPORT_TASK_COLLECTION_NAME);
    }

    @Override
    public void setStartTask(String taskId) {
        Query query = Query.query(Criteria.where("_id").is(taskId));
        Update update = new Update();
        update.set("taskStatus", ExportTaskStatus.RUNNING).set("errorMsg", null).set("startTime",System.currentTimeMillis());
        mongoTemplate.updateFirst(query, update, EXPORT_TASK_COLLECTION_NAME);
    }

    @Override
    public void setResultFiles(String taskId, List<String> resultFiles) {
        Query query = Query.query(Criteria.where("_id").is(taskId));
        Update update = new Update();
        update.set("resultFiles", resultFiles);
        mongoTemplate.updateFirst(query, update, EXPORT_TASK_COLLECTION_NAME);
    }

    @Override
    public void setTerminatedTask(String taskId, ExportTaskStatus status, String errMsg, List<String> resultFiles,ExportTaskModel taskModel) {
        Query query = Query.query(Criteria.where("_id").is(taskId));
        Update update = new Update();
        update.set("taskStatus", status)
                .set("errorMsg", errMsg)
                .set("resultFiles", resultFiles)
                .set("stopTime", System.currentTimeMillis())
                .set("resultFilePath",taskModel.getResultFilePath())
                .set("resultFileName",taskModel.getResultFileName());
        mongoTemplate.updateFirst(query, update, ExportTaskModel.class, EXPORT_TASK_COLLECTION_NAME);
    }

    @Override
    public void setTerminatedTask(String taskId, ExportTaskStatus status, String errMsg, List<String> resultFiles) {
        Query query = Query.query(Criteria.where("_id").is(taskId));
        Update update = new Update();
        update.set("taskStatus", status)
                .set("errorMsg", errMsg)
                .set("resultFiles", resultFiles)
                .set("stopTime", System.currentTimeMillis());
        mongoTemplate.updateFirst(query, update, EXPORT_TASK_COLLECTION_NAME);
    }

    @Override
    public void updateExportTypeName(String taskId,String taskTypeName) {
        Query query = Query.query(Criteria.where("_id").is(taskId));
        Update update = new Update();
        update.set("taskTypeName", taskTypeName);
        mongoTemplate.updateFirst(query, update, EXPORT_TASK_COLLECTION_NAME);
    }

    @Override
    public Long getTaskTotalCount(){
        return mongoTemplate.count(new Query(),EXPORT_TASK_COLLECTION_NAME);
    }
}
