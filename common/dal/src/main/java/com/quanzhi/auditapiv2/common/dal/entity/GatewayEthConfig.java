package com.quanzhi.auditapiv2.common.dal.entity;

import lombok.Data;

/**
 * <AUTHOR>
 * @description 网关网口配置
 * @date 2023/8/29 20:38
 */
@Data
public class GatewayEthConfig {

    private String id;

    private Long time;

    /**
     * 网口别名
     */
    private String name;

    /**
     * 网口原始名
     */
    private String ethName;

    /**
     * 网关ip
     */
    private String gatewayIp;

    /**
     * 网口绑定ip
     */
    private String bindIp;

    /**
     * 绑定状态
     */
    private Integer bindStatus;

    /**
     * 创建时间
     */
    private Long createTime;

    /**
     * 修改时间
     */
    private Long updateTime;
}
