package com.quanzhi.auditapiv2.common.dal.dao.impl;

import com.quanzhi.audit_core.common.model.HistoryDataCleanTaskDetail;
import com.quanzhi.auditapiv2.common.dal.dao.IHistoryDataCleanDetailDao;
import com.quanzhi.auditapiv2.common.dal.dao.base.impl.BaseDaoImpl;
import org.springframework.data.mongodb.core.aggregation.Aggregation;
import org.springframework.data.mongodb.core.aggregation.AggregationResults;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2020/7/21 上午10:10
 */
@Repository
public class HistoryDataCleanTaskDetailDaoImpl extends BaseDaoImpl<HistoryDataCleanTaskDetail> implements IHistoryDataCleanDetailDao{


    private String collectionName = "historyDataCleanTaskDetail";

    @Override
    public  Long getDistintCount(String dataType,String taskId) {

        Criteria criteria = Criteria.where("taskId").is(taskId);
        criteria.and("type").is(dataType);


        String group = dataType.equals("HTTPAPI") ? "uri" : "appUri";
        Aggregation aggregation = Aggregation.newAggregation(
                Aggregation.match(criteria),
                 Aggregation.group( group  )
                , Aggregation.count().as("count"));
        AggregationResults<Map> results = mongoTemplate.aggregate(aggregation, collectionName, Map.class);

        if (results == null || results.getMappedResults() == null || results.getMappedResults().size() == 0) {
            return 0l;
        } else {
            return Long.valueOf(results.getMappedResults().get(0).get("count").toString());
        }
    }

    @Override
    public List<HistoryDataCleanTaskDetail> getDistintList(Integer page,Integer limit,String dataType,String taskId) {

        Criteria criteria = Criteria.where("taskId").is(taskId);
        criteria.and("type").is(dataType);

        Aggregation aggregation = null;
        String group = dataType.equals("HTTPAPI") ? "uri" : "appUri";
        aggregation = Aggregation.newAggregation(
                Aggregation.match(criteria),
                Aggregation.group(group)
                .first("uri").as("uri")
                .first("taskId").as("taskId")
                .first("appUri").as("appUri")
                        .first("host").as("host")
                .first("timestamp").as("timestamp")
                        .first("sampleEventId").as("sampleEventId")
                        .first("_id").as("id")
                .first("apiUrl").as("apiUrl"),
                Aggregation.skip((page - 1)*limit),
                Aggregation.limit(limit));

        return mongoTemplate.aggregate(aggregation, collectionName, HistoryDataCleanTaskDetail.class).getMappedResults();
    }
}
