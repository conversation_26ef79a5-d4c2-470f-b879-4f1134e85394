package com.quanzhi.auditapiv2.common.dal.dto.app;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Data;

/**
 * @Author: HaoJun
 * @Date: 2021/6/28 4:37 下午
 */
@Data
@Builder
@ApiModel("应用简单统计")
public class AppStatistics {
    @ApiModelProperty("总数")
    private long totalCount;
    @ApiModelProperty("高敏感数量")
    private long highLevelCount;
}
