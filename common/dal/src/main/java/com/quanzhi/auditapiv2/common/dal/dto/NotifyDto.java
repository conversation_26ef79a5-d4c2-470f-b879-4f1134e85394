package com.quanzhi.auditapiv2.common.dal.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 *
 * 《通知dto》
 *
 *
 * @Project:
 * @Module ID:
 * @Comments:
 * @JDK version used:      <JDK1.8> 
 * <AUTHOR> [<EMAIL>]
 * @since 2020-04-08-上午9:56:10
 */
@Data
@ApiModel
public class NotifyDto {

    @ApiModelProperty(value = "id", name = "id", required = true)
    private String id;

    /**
     * 唯一编码
     */
    @ApiModelProperty(value = "唯一编码", name = "code", required = true)
    private String code;
    
    /**
     * 标题
     */
    @ApiModelProperty(value = "标题", name = "title", required = true)
    private String title;
    
    /**
     * 内容
     */
    @ApiModelProperty(value = "内容", name = "content", required = true)
    private String content;
    
    /**
     * 触发时间
     */
    @ApiModelProperty(value = "触发时间", name = "triggerTime", required = true)
    private Long triggerTime;

    @ApiModelProperty(value = "间隔时间", name = "intervalSeconds", required = false)
    private Long intervalSeconds;
}
