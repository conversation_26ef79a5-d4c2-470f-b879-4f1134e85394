package com.quanzhi.auditapiv2.common.dal.dto.datalabel;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@ApiModel
@Data
public class DataLabelQuery {

    private Integer page;
    private Integer limit;
    @ApiModelProperty(name = "sort", value = "排序键", allowableValues = "level")
    private String sort;
    @ApiModelProperty(name = "order", value = "正序倒序", allowableValues = "-1 1")
    private Integer order;

    /**
     * 模糊
     */
    @ApiModelProperty(name = "name", value = "标签名称")
    private String name;

    @ApiModelProperty(name = "classify", value = "标签分类")
    private List<String> classify;
    @ApiModelProperty(name = "level", value = "敏感等级")
    private List<Integer> level;
    @ApiModelProperty(name = "monitor", value = "监控开关")
    private Boolean monitor;
    @ApiModelProperty(name = "store", value = "存储开关")
    private Boolean store;
    @ApiModelProperty(name = "position", value = "提取位置")
    private List<String> position;

    public boolean checkValid() {
        if (page == null || page < 1) {
            return false;
        }
        if (limit == null || limit > 100) {
            return false;
        }
        if (sort != null) {
            if (!sort.equals("level")) {
                return false;
            }
        }
        if (order != null) {
            if (order != -1 && order != 1) {
                return false;
            }
        }

        return true;
    }

}