package com.quanzhi.auditapiv2.common.dal.cacheZS.timedTask;


import com.quanzhi.audit.mix.schdule.application.annotation.LockedScheduler;
import com.quanzhi.auditapiv2.common.dal.cacheZS.CacheZS;
import com.quanzhi.auditapiv2.common.dal.dao.apiZS.ZSIHttpApiDao;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.Scheduled;

import javax.annotation.PostConstruct;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@EnableScheduling//开启定时任务
public class LabelAppSegmentTask {

    @Autowired
    CacheZS cacheZS;
    @Autowired
    ZSIHttpApiDao zsiHttpApiDao;

    private String dataLabel="dataLabelTop";

    private String app="appTop20";
    private String network="networkTop10";


    @LockedScheduler(cron = "0 0 */1 * * ?", executor = "labelAppSegmentJob", description = "数据流动地图缓存任务")//每隔一个小时执行一次
    @PostConstruct
    public void getLabelAppSegmentTask(){
        //获得top10标签
        Map<String, Integer> rspDataLabels = zsiHttpApiDao.selectApiCountTop10();
        //将标签列表放入缓存
        cacheZS.put(dataLabel,rspDataLabels);

        List<String> keyList = new ArrayList<>(rspDataLabels.keySet());
        //在标签数据基础上获得前20app
        List<String> listAppTop20 = zsiHttpApiDao.selectAppTop20(keyList);

        //放入缓存
        cacheZS.put(app,listAppTop20);
        //在标签数据基础上获得前10网段
        List<String> networkTop10 = zsiHttpApiDao.selectNetworkTop10(keyList);
        cacheZS.put(network,networkTop10);

    }
}
