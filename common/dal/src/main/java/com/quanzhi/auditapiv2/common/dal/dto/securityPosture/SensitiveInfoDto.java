package com.quanzhi.auditapiv2.common.dal.dto.securityPosture;

import com.quanzhi.auditapiv2.common.dal.entity.CountEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Data;

import java.util.List;

/**
 * @Auther: yangzixian
 * @Date: 2021/10/14 10:05
 * @Description: 敏感信息暴露Dto
 */
@Data
@Builder
@ApiModel("敏感信息暴露")
public class SensitiveInfoDto {

    private String type;

    @ApiModelProperty("标签列表 - 外圈")
    private List<CountEntity> dataLabelInfoList;

    @ApiModelProperty("标签列表 - 内圈")
    private List<innerEntity> dataLabelInfoInnerList;

    @Data
    @Builder
    public static final class innerEntity implements Comparable<SensitiveInfoDto.innerEntity> {
        @ApiModelProperty("id")
        private String id;
        @ApiModelProperty("名称")
        private String name;
        @ApiModelProperty("数量")
        private long count;

        @Override
        public int compareTo(innerEntity o) {
            return Long.compare(o.count, count);
        }

    }

    @ApiModelProperty("总数")
    private Long totalCount;
}
