package com.quanzhi.auditapiv2.common.dal.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 《开放接口密钥》
 *
 * <AUTHOR> [<EMAIL>]
 * @Project:
 * @Module ID:
 * @Comments:
 * @JDK version used:      <JDK1.8>
 * @since 2021-08-17-上午9:09:10
 */
@Data
public class OpenApiKeySearchDto {

    private String id;

    private String useKey;

    private Long page;

    private Long limit;

}
