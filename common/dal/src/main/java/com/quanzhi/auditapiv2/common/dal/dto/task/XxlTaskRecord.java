package com.quanzhi.auditapiv2.common.dal.dto.task;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 描述: 任务运行日志记录DTO
 *
 * @author: danniel_yu
 * @date: 2020/11/25 13:42
 */
@Data
public class XxlTaskRecord {
    /**
     * 任务ID
     */
    @ApiModelProperty(value = "任务ID", name = "logId")
    private String logId;

    /**
     * 任务开始时间
     */
    @ApiModelProperty(value = "任务开始时间", name = "startTime")
    private Long startTime;

    /**
     * 任务状态
     */
    @ApiModelProperty(value = "任务状态", name = "status")
    private String status;


}
