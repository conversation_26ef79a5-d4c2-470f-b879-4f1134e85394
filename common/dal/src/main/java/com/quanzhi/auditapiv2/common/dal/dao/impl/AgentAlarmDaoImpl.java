package com.quanzhi.auditapiv2.common.dal.dao.impl;

import com.quanzhi.auditapiv2.common.dal.dao.AgentAlarmDao;
import com.quanzhi.auditapiv2.common.dal.dao.base.impl.BaseDaoImpl;
import com.quanzhi.auditapiv2.common.dal.entity.AgentAlarm;
import com.quanzhi.auditapiv2.common.util.utils.DataUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public class AgentAlarmDaoImpl extends BaseDaoImpl<AgentAlarm> implements AgentAlarmDao {
    @Autowired
    MongoTemplate mongoTemplate;
    private static String collectionName = "agentAlarm";

    @Override
    public Long countByIpAndTime(String gatewayIp, String agentIp, Long startTime, Long endTime) {
        Criteria criteria = new Criteria();
        if (DataUtil.isNotEmpty(gatewayIp)) {
            criteria = Criteria.where("gatewayIp").is(gatewayIp);
        }
        if (DataUtil.isNotEmpty(agentIp)) {
            if (DataUtil.isEmpty(criteria)) {
                criteria = Criteria.where("agentIp").is(agentIp);
            }
            criteria = criteria.and("agentIp").is(agentIp);
        }
        if (DataUtil.isNotEmpty(startTime) && DataUtil.isNotEmpty(endTime)) {
            if (DataUtil.isEmpty(criteria)) {
                criteria = Criteria.where("timestamp").gte(startTime).lte(endTime);
            }
            criteria = criteria.and("timestamp").gte(startTime).lte(endTime);
        } else {
            if (DataUtil.isNotEmpty(startTime)) {
                if (DataUtil.isEmpty(criteria)) {
                    criteria = Criteria.where("timestamp").gte(startTime);
                }
                criteria = criteria.and("timestamp").gte(startTime);
            }
            if (DataUtil.isNotEmpty(endTime)) {
                if (DataUtil.isEmpty(criteria)) {
                    criteria = Criteria.where("timestamp").lte(endTime);
                }
                criteria = criteria.and("timestamp").lte(endTime);
            }
        }
        return mongoTemplate.count(Query.query(criteria), collectionName);
    }

    @Override
    public List<AgentAlarm> pageByIpAndTime(String gatewayIp, String agentIp, Long startTime, Long endTime, Integer page, Integer limit) {
        Criteria criteria = new Criteria();
        if (DataUtil.isNotEmpty(gatewayIp)) {
            criteria = Criteria.where("gatewayIp").is(gatewayIp);
        }
        if (DataUtil.isNotEmpty(agentIp)) {
            if (DataUtil.isEmpty(criteria)) {
                criteria = Criteria.where("agentIp").is(agentIp);
            }
            criteria = criteria.and("agentIp").is(agentIp);
        }
        if (DataUtil.isNotEmpty(startTime) && DataUtil.isNotEmpty(endTime)) {
            if (DataUtil.isEmpty(criteria)) {
                criteria = Criteria.where("timestamp").gte(startTime).lte(endTime);
            }
            criteria = criteria.and("timestamp").gte(startTime).lte(endTime);
        } else {
            if (DataUtil.isNotEmpty(startTime)) {
                if (DataUtil.isEmpty(criteria)) {
                    criteria = Criteria.where("timestamp").gte(startTime);
                }
                criteria = criteria.and("timestamp").gte(startTime);
            }
            if (DataUtil.isNotEmpty(endTime)) {
                if (DataUtil.isEmpty(criteria)) {
                    criteria = Criteria.where("timestamp").lte(endTime);
                }
                criteria = criteria.and("timestamp").lte(endTime);
            }
        }
        Sort sort =Sort.by(Sort.Direction.DESC, "timestamp");
        Query query=new Query();
        query.addCriteria(criteria);
        query.with(sort);
        query.skip(page);
        query.limit(limit);
        return mongoTemplate.find(query, AgentAlarm.class, collectionName);
    }

    @Override
    public Long countByCriteria(Criteria criteria) {
        return mongoTemplate.count(Query.query(criteria), collectionName);
    }
}
