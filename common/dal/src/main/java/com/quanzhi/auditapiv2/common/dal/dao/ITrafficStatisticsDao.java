package com.quanzhi.auditapiv2.common.dal.dao;

import com.quanzhi.auditapiv2.common.dal.entity.TrafficStatistics;

import java.util.List;

/**
 * @author: yangzixian
 * @date: 21/3/2023 18:02
 * @description:
 */

public interface ITrafficStatisticsDao {

    List<TrafficStatistics> getTrafficStatisticsList(String startDate, String endDate);

    TrafficStatistics getTrafficStatisticByDate(String date);

}
