package com.quanzhi.auditapiv2.common.dal.dto.filter;

import com.quanzhi.audit_core.common.model.FilterHttpEvent;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * create at 2023/5/22 18:09
 * @description:
 **/
@Data
@NoArgsConstructor
public class FilterHttpEventDto {

    private String id;
    private String ruleId;
    private String ruleName;
    private FilterHttpEvent.FilterType filterType;
    private String uri;
    private String httpEvent;
    private Long createTime;
    private Date createDate;
    private Map<String, Object> sourceMap = new HashMap<>();

    public FilterHttpEventDto(FilterHttpEvent filterHttpEvent) {
        this.id=filterHttpEvent.getId();
        this.ruleId=filterHttpEvent.getRuleId();
        this.ruleName=filterHttpEvent.getRuleName();
        this.filterType=filterHttpEvent.getFilterType();
        this.uri=filterHttpEvent.getUri();
        this.httpEvent=filterHttpEvent.getHttpEvent();
        this.createTime=filterHttpEvent.getCreateTime();
        this.createDate=filterHttpEvent.getCreateDate();
    }
}