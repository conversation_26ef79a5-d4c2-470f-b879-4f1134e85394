package com.quanzhi.auditapiv2.common.dal.dao;

import com.mongodb.client.MongoCursor;
import com.quanzhi.auditapiv2.common.dal.dao.base.IMetabaseDao;
import com.quanzhi.auditapiv2.common.dal.dto.ApiAccParseCriteriaDto;
import com.quanzhi.auditapiv2.common.dal.dto.HttpApiSearchDto;
import com.quanzhi.auditapiv2.common.dal.dto.api.ApiStatistics;
import com.quanzhi.auditapiv2.common.dal.entity.CountEntity;
import com.quanzhi.auditapiv2.common.dal.entity.riskEolink.SyncGatewayDto;
import com.quanzhi.auditapiv2.common.util.dto.AggregationDto;
import com.quanzhi.auditapiv2.common.util.dto.GroupDto;
import com.quanzhi.metabase.core.model.http.HttpApiResource;
import com.quanzhi.metabase.core.model.http.report.HttpApiDateTotalStat;
import com.quanzhi.metabase.core.model.query.AggregationResult;
import com.quanzhi.metabase.core.model.query.MetabaseGroupOperation;
import com.quanzhi.metabase.core.model.query.MetabaseQuery;
import com.quanzhi.metabase.core.model.query.ResourceUpdates;
import org.bson.Document;
import org.springframework.data.mongodb.core.aggregation.AggregationResults;
import org.springframework.data.mongodb.core.query.Query;

import java.util.List;
import java.util.Map;
import java.util.Set;

public interface IHttpApiDao extends IMetabaseDao<HttpApiResource> {

    String updateApiState(String id, String apiState, String remark);

    List<HttpApiResource> selectHttpApiListByFeatureLabel(String featureLabel) throws Exception;

    List<HttpApiResource> selectHttpApiListByDataLabel(String dataLabel) throws Exception;

    void updateHttpApiByFeatureLabel(String label) throws Exception;

    void updateHttpApiByDataLabel(String label) throws Exception;

    long getSearchCount(HttpApiSearchDto httpApiSearchDto);

    org.springframework.data.mongodb.core.query.Criteria getCriteria(HttpApiSearchDto httpApiSearchDto);

    long countUseDataApi();

    Long getCount(Long startTime, Long endTime, String type);

    List<HttpApiResource> getHttpApis(HttpApiSearchDto httpApiSearchDto);

    List<HttpApiResource> getHttpApisByUris(String appUri, Set<String> apiUriSet, Set<String> relateIdSet);

    List<HttpApiResource> getHttpApis(Integer page, Integer limit, String field, Integer sort, HttpApiSearchDto httpApiSearchDto);

    List<HttpApiResource> getHttpApisFields(Integer page, Integer limit, String field, Integer sort, HttpApiSearchDto httpApiSearchDto, List<String> fields);

    /**
     * 获取标签，接口分类，特征标签的统计count，用或逻辑
     *
     * @param classifications
     * @param featureLabels
     * @param labels
     * @return
     */
    long getApiInfoCount(
            List<String> classifications,
            List<String> featureLabels,
            List<String> labels
    );

    List<AggregationResult> getHttpApiVisits(MetabaseQuery query, MetabaseGroupOperation metabaseGroupOperation, Class<?> clz);

    List<HttpApiResource> selectHttpApiByAppUri(String appUri, List<String> fields) throws Exception;

    List<HttpApiResource> selectHttpApiByAppUri(String appUri, List<String> fields, int limit) throws Exception;


    List<HttpApiResource> selectHttpApiByHost(String host, List<String> fields) throws Exception;

    HttpApiResource selectHttpApiByUri(String uri) throws Exception;

    HttpApiResource selectHttpApiByUri(String uri, List<String> fields) throws Exception;


    HttpApiResource selectHttpApiByApiUrl(String apiUrl);

    List<HttpApiResource> getHttpNewApis(Integer page, Integer limit, String field, Integer sort, Long startTime, Long endTime);

    List<HttpApiResource> getHttpActiveApis(Integer page, Integer limit, String field, Integer sort, Long startTime, Long endTime);

    List<HttpApiDateTotalStat> getActiveApiCount(Long startTime, Long endTime);

    List<HttpApiDateTotalStat> getActiveSensitiveApiCount(Long startTime, Long endTime);

    void saveAccParseConfig(ApiAccParseCriteriaDto apiAccParseCriteriaDto, int type) throws Exception;

    void createApiParse(ApiAccParseCriteriaDto apiAccParseCriteriaDto, HttpApiResource httpApiResource, int parseType) throws Exception;

    String saveHttpApiList(List<HttpApiResource> httpApiResourceList);

    void batchUpdateByIds(List<String> uris, int type);

    void batchUpdateByIds(List<String> uris, int type, String target);

    void initRiskNum();

    List<HttpApiResource> getAccParseApiList(ApiAccParseCriteriaDto apiAccParseCriteriaDto);

    List<HttpApiResource> getAccParseAPIList(String appUri);

    Long getAccParseApiCount(ApiAccParseCriteriaDto apiAccParseCriteriaDto);

    Long getAppParseCount(String appUri, List<String> tags, int parse);

    void deleteConfig(String uri) throws Exception;

    void update(String id, Map<String, Object> updates);

    void update(MetabaseQuery query, ResourceUpdates resourceUpdates);

    List<HttpApiResource> selectHttpApiByRegexApiUrl(String apiUrl);

    /**
     * 根据弱点名称分组聚合查询
     *
     * @param flag
     * @return
     */
//    AggregationResults<WeaknessDistribute> getWeaknessDistributed(boolean flag);

    /**
     * 获取概览统计信息
     *
     * @return
     */
    ApiStatistics getApiStatistics(long startTime, long endTime, List<String> appUris);

    /**
     * 统计api数量（全量或互联网访问api）
     *
     * @param flag
     * @return
     */
    Long countApi(String type);

    Double getAssetDimension();

    List<AggregationDto> group(HttpApiSearchDto httpApiSearchDto, GroupDto groupDto, Integer sort, Integer page, Integer limit) throws Exception;

    /**
     * 获取数据标签访问量top10
     *
     * @param type
     * @param source
     * @return
     */
    AggregationResults<CountEntity> getDataLabelTop10(String type, String source, List<String> appUris);


    AggregationResults<CountEntity> getDataLabelAggWithProvince(Long startTime, String type, String source, List<String> appUris);


    /**
     * 获取API等级分布信息
     *
     * @return
     */
    AggregationResults<CountEntity> getApiLevel();

    /**
     * 获取敏感信息暴露环形图
     *
     * @return
     */
    List<CountEntity> getSensitiveInfo();

    /**
     * 获取大屏概览信息 - 总API、高敏感API
     *
     * @return
     */
    List<Long> getStatistics();

    Long count(Query query, String collectionName);

    <T> List<T> find(Query query, Class<T> entityClass, String collectionName);

    void updateByApiUri(String apiUri, Integer riskNum, List<Integer> riskLevels);

    /**
     * 应用结构分组查询
     *
     * @param
     * @return
     * @Comments: <对此方法的描述，可以引用系统设计中的描述>
     * <AUTHOR> [<EMAIL>]
     * @since 2022-111-17 9:56
     */
    List<AggregationDto> urlPathsGroup(HttpApiSearchDto httpApiSearchDto, Integer sort, Integer page, Integer limit, Integer node) throws Exception;

    List<AggregationDto> compositeUrlPathsGroup(HttpApiSearchDto httpApiSearchDto, Integer sort, Integer page, Integer limit, Integer node) throws Exception;

    MongoCursor<Document> getHttpApiList(HttpApiSearchDto httpApiSearchDtoStr, int batchSize);

    List<HttpApiResource> getHttpApiList(Set<String> apiUriSet, String relateId);

    List<HttpApiResource> getAccParseApiOne(ApiAccParseCriteriaDto apiAccParseCriteriaDto);

    /**
     * 找到所有配置账号的 API
     *
     * @param appUri
     * @return
     */
    List<HttpApiResource> getAccParseAPIs(String appUri);


    MetabaseQuery convertQuery(HttpApiSearchDto apiSearchDto);

    List<HttpApiResource> getApiResourceByProvince(List<String> province);


    List<HttpApiResource> findAll();

    void updateProvince(String id, List<String> province);

    List<String> getAppUriByProvince(List<String> province);

    List<HttpApiResource> getApiResourceByIds(List<String> ids);

    List<SyncGatewayDto> getApiResourceByIdsGateway(List<String> ids);

    void updateByGatewayId(String id);

    List<HttpApiResource> findByUri(String uri);

    HttpApiResource findOneByUri(String uri);

}
