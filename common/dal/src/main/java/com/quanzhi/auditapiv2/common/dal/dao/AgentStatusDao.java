package com.quanzhi.auditapiv2.common.dal.dao;


import com.quanzhi.auditapiv2.common.dal.dao.base.IBaseDao;
import com.quanzhi.auditapiv2.common.dal.entity.AgentStatus;

import java.util.List;


public interface AgentStatusDao extends IBaseDao<AgentStatus> {

    /**
     * 获取时间范围内的Agent IP
     *
     * @param startTime
     * @param endTime
     * @return
     */
    List<AgentStatus> listIpByTime(long startTime, long endTime);

    List<AgentStatus> listByPage(String gatewayIp, String agentIp, Long startTime, Long endTime, Integer page, Integer limit);

    Long countAgentInfo(String gatewayIp, String agentIp, Long startTime, Long endTime);

    List<AgentStatus> listLatest(long skip, long limit);

    AgentStatus getLatestOne(String gatewayIp, String agentIp);
}
