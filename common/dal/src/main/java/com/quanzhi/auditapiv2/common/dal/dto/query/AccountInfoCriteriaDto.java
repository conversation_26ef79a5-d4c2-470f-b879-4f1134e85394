package com.quanzhi.auditapiv2.common.dal.dto.query;

import com.quanzhi.auditapiv2.common.dal.dto.ExporTitleFieldDto;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

/**
 * @ClassName AccountInfoCriteriaDto
 * @Description TODO
 * <AUTHOR>
 * @Date 2020/11/17 9:58
 **/
@Data
public class AccountInfoCriteriaDto implements Serializable {

    // OR 或关系 ALL 且关系, 如：dataLabel:or/and
    private Map<String, String> fieldOperateMap;

    @ApiModelProperty(value = "账号")
    private String account;

    @ApiModelProperty(value = "部门")
    private String staffDepart;

    @ApiModelProperty(value = "数据标签")
    private List<String> dataLabel;

    /**
     * 数据标签搜索：ALL：且  IN：或
     */
    private String dataLabelOperate;

    @ApiModelProperty(value = "当前页数")
    private Integer page;

    @ApiModelProperty("每页条数")
    private Integer limit;

    @ApiModelProperty("排序字段")
    private String field;

    @ApiModelProperty("排序规则")
    private Integer sort;

    private CriteriaEnumType type;

    /**
     * 最近使用时间范围查询
     */
    private String startDate;

    private String endDate;

    /**
     * 首次发现时间范围查询
     */
    private String startTime;

    private String endTime;

    /**
     * 更新时间范围查询
     */
    private Long startUpdateTime;
    private Long endUpdateTime;

    /**
     * 导出字段
     */
    private List<ExporTitleFieldDto> list;

    /**
     * 邮箱
     */
    private String staffEmail;

    /**
     *  英文账号
     */
    private String staffName;

    //姓名
    private String staffChinese;

    /**
     * 角色
     */
    private String staffRole;

    /**
     * 账号生命状态
     */
    private Integer accountLifeFlag;

    @ApiModelProperty(value = "风险事件")
    private List<String> riskNames;

    /**
     * 风险事件搜索：ALL：且  IN：或
     */
    private String riskNamesOperate;

    // 风险等级
    private List<Integer> riskLevel;

    // 应用关系
    private String appUri;

    private List<String> ids;

    private String nodeId;

    private Map<String,Object> dataPermissionMap;

}
