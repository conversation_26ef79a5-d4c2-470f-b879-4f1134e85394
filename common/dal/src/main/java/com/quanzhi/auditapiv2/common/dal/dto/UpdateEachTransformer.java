package com.quanzhi.auditapiv2.common.dal.dto;

import com.mongodb.BasicDBObject;
import com.mongodb.DBObject;
import net.sf.json.JSONArray;
import net.sf.json.JSONObject;
import org.bson.Transformer;

/**
 * @author: zhousong
 * @data: 2019/10/31
 */
public class UpdateEachTransformer implements Transformer {

    @Override
    public Object transform(Object objectToTransform) {
        JSONObject jsonObject = JSONObject.fromObject(objectToTransform);
        JSONArray jsonArray = (JSONArray) jsonObject.get("value");
        Object[] objects = new Object[jsonArray.size()];
        jsonArray.toArray(objects);
        DBObject dbObject = new BasicDBObject();
        dbObject.put("$each", objects);
        return dbObject;
    }
}
