package com.quanzhi.auditapiv2.common.dal.dao.impl;

import com.quanzhi.audit_core.common.model.ResourceLevelStrategy;
import com.quanzhi.auditapiv2.common.dal.dao.IResourceLevelDao;
import com.quanzhi.auditapiv2.common.dal.dao.base.impl.NacosBaseDaoImpl;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * @ClassName ResourceDaoImpl
 * @Description TODO
 * <AUTHOR>
 * @Date 2021/2/2 11:00
 **/
@Repository
public class ResourceLevelDaoImpl extends NacosBaseDaoImpl<ResourceLevelStrategy> implements IResourceLevelDao {
    @Override
    public boolean checkNameExist(String name, String id) {
        List<ResourceLevelStrategy> resourceLevelStrategies = getAll();
        for(ResourceLevelStrategy resourceLevelStrategy:resourceLevelStrategies){
            if(!resourceLevelStrategy.getId().equals(id) && resourceLevelStrategy.getName().equals(name)){
                return true;
            }
        }
        return false;
    }
}
