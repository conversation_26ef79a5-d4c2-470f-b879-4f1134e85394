package com.quanzhi.auditapiv2.common.dal.dto;

import io.swagger.annotations.ApiModel;
import lombok.Builder;
import lombok.Data;

import java.util.List;

/**
 * @Auther: yangzixian
 * @Date: 2021/8/23 16:05
 * @Description:
 */
@Data
@Builder
@ApiModel("级联标签")
public class CascadeLabelDto {

    private List<LabelInfo> cascadeLabels;

    public CascadeLabelDto(List<LabelInfo> cascadeLabels) {
        this.cascadeLabels = cascadeLabels;
    }

    @Data
    public static class LabelInfo {
        private String id;
        private String templateId;
        private Boolean enabled;
        private String label;
        private String value;
        private List<LabelInfo> children;
    }

}
