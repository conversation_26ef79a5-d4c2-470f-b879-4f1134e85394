package com.quanzhi.auditapiv2.common.dal.dto;

import com.quanzhi.audit_core.common.model.DataLabel;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.beans.BeanUtils;

import java.util.List;

/**
 * @author: zhousong
 * @data: 2018/11/2
 * 数据标签dto
 */
@Data
@NoArgsConstructor
public class DataLabelDto {

    /**
     * ID
     */
    private String id;

    /**
     * 标签名称
     */
    private String name;

    private Integer type;

    /**
     * 提取方法
     */
    private List<DataLabel.ExtractType> extractType;

    /**
     * 能否根据该标签提取内容
     */
    private Boolean canExtract;

    private Long updateTime;

    private String updateTimeFormat;

    private String operateName;

    private List<DataLabel.DataLabelCondition> conditionList;

    private Boolean enabled;

    private String pluginId;

    public DataLabelDto(DataLabel dataLabel) {
        BeanUtils.copyProperties(dataLabel, this);
    }

    public DataLabel dto2model() {
        DataLabel dataLabel = new DataLabel();
        BeanUtils.copyProperties(this, dataLabel);
        return dataLabel;
    }
}
