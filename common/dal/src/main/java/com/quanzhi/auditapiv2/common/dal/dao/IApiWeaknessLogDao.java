package com.quanzhi.auditapiv2.common.dal.dao;

import com.quanzhi.auditapiv2.common.dal.dao.base.IMetabaseDao;
import com.quanzhi.auditapiv2.common.dal.entity.ApiWeaknessLog;

import java.util.List;

/**
 * 
 * 《接口弱点日志持久层接口》
 * 
 * 
 * @Project: 
 * @Module ID:
 * @Comments: 
 * @JDK version used:      <JDK1.8> 
 * <AUTHOR> [<EMAIL>]
 * @since 2020-08-28-下午16:52:18
 */
public interface IApiWeaknessLogDao extends IMetabaseDao<ApiWeaknessLog> {

	/**
	 * 查询接口弱点日志列表(分页)
	 * @Comments:  <对此方法的描述，可以引用系统设计中的描述>
	 * <AUTHOR> [<EMAIL>]
	 * @since 2020-08-28 16:52
	 * @param
	 * @return
	 */
	List<ApiWeaknessLog> selectApiWeaknessLogList(String apiWeaknessId) throws Exception;

	/**
	 * 新增接口弱点日志
	 * @Comments:  <对此方法的描述，可以引用系统设计中的描述>
	 * <AUTHOR> [<EMAIL>]
	 * @since 2020-08-28 16:52
	 * @param
	 * @return
	 */
	ApiWeaknessLog insertApiWeaknessLog(ApiWeaknessLog apiWeaknessLog) throws Exception;

	/**
	 * 删除接口弱点日志
	 * @Comments:  <对此方法的描述，可以引用系统设计中的描述>
	 * <AUTHOR> [<EMAIL>]
	 * @since 2020-08-28 16:52
	 * @param
	 * @return
	 */
	void delApiWeaknessLog(String id) throws Exception;
}
