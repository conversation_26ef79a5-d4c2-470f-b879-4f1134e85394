package com.quanzhi.auditapiv2.common.dal.entity;

/**
 * <AUTHOR>
 * @date 2019-12-08
 * @time 13:24
 */
public enum ExportTaskStatus {
    /**
     * 待执行
     */
    RUNNABLE("待执行", 0),
    /**
     * 正在运行中
     */
    RUNNING("运行中", 100),
    /**
     * 任务已手动取消
     */
    CANCELED("已取消", 200),
    /**
     * 任务已成功终止
     */
    TERMINATED("已终止", 300),
    /**
     * 任务出错
     */
    ERROR("错误", 400);

    String statusName;
    int code;
    ExportTaskStatus(String statusName, int code) {
        this.code = code;
        this.statusName = statusName;
    }
}
