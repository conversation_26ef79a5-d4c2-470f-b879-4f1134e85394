package com.quanzhi.auditapiv2.common.dal.dao.impl;

import com.quanzhi.auditapiv2.common.dal.dao.IBigScreenConfigDao;
import com.quanzhi.auditapiv2.common.dal.entity.securityPosture.BigScreenConfig;
import org.bson.types.ObjectId;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.stereotype.Repository;

/**
 * @Auther: yangzixian
 * @Date: 2021/10/21 16:10
 * @Description:
 */
@Repository
public class BigScreenConfigDaoImpl implements IBigScreenConfigDao {

    @Autowired
    private MongoTemplate mongoTemplate;

    private String collectionName = "bigScreenConfig";

    @Override
    public void saveBigScreenConfig(BigScreenConfig bigScreenConfig) {
        Update update = new Update();
        update.set("bigScreenName", bigScreenConfig.getBigScreenName());
        update.set("networkOne", bigScreenConfig.getNetworkOne());
        update.set("networkTwo", bigScreenConfig.getNetworkTwo());
        update.set("updateTime", bigScreenConfig.getUpdateTime());
        mongoTemplate.upsert(new Query().addCriteria(Criteria.where("_id").is(new ObjectId(bigScreenConfig.getId()))), update, collectionName);
    }

    @Override
    public BigScreenConfig selectBigScreenConfig() {
        return mongoTemplate.findOne(new Query(), BigScreenConfig.class, collectionName);
    }

}
