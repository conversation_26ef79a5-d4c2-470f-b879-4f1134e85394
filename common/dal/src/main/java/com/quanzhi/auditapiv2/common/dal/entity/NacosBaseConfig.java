package com.quanzhi.auditapiv2.common.dal.entity;

import com.quanzhi.auditapiv2.common.dal.enums.NacosContentTypeEnum;
import lombok.AllArgsConstructor;
import lombok.Data;

/**
 * @Author: Linlm
 * @Description:
 * @Date: Created in 2020/7/6 上午10:21
 */
@AllArgsConstructor
@Data
public class NacosBaseConfig<T> {
    /**
     * nacos的dataId
     */
    private String dataId;

    /**
     * nacos的group
     */
    private String group;

    private Class<T> clazz;

    /**
     * @see NacosContentTypeEnum
     */
    private String contentType;
}
