package com.quanzhi.auditapiv2.common.dal.dto;

import com.quanzhi.metabase.core.model.http.HttpApiResource;
import lombok.Data;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.io.Serializable;
import java.util.List;

/**
 * @ClassName OrganizationDto
 * @Description TODO
 * <AUTHOR>
 * @Date 2020/11/12 10:29
 **/
@Data
public class OrganizationDto implements Serializable {


    private String uri;

    private Boolean staffInfoParseEnable;

    private List<String> tags;

        /**
     * 组织架构部门
     */
    private List<HttpApiResource.AccParseConfigs> staffDepartParseConfigs;

    private List<HttpApiResource.AccParseConfigs> staffNameParseConfigs;

    private List<HttpApiResource.AccParseConfigs> staffChineseParseConfigs;

    private List<HttpApiResource.AccParseConfigs> staffIdCardParseConfigs;

    private List<HttpApiResource.AccParseConfigs> staffBankCardParseConfigs;

    private List<HttpApiResource.AccParseConfigs> staffEmailParseConfigs;

    private List<HttpApiResource.AccParseConfigs> staffIdParseConfigs;

    private List<HttpApiResource.AccParseConfigs> staffMobileParseConfigs;

    private List<HttpApiResource.AccParseConfigs> staffRoleParseConfigs;

    /**
     * 昵称
     */
    private List<HttpApiResource.AccParseConfigs> staffNickNameParseConfigs;

    @Mapper
    public interface OrganizationExportDtoMapper {

        OrganizationExportDtoMapper INSTANCE = Mappers.getMapper(OrganizationExportDtoMapper.class);

        OrganizationDto convert(HttpApiResource httpApiResource);

        List<OrganizationDto> converts(List<HttpApiResource> httpApiResources);

    }

}
