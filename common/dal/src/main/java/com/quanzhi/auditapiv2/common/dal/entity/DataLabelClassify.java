package com.quanzhi.auditapiv2.common.dal.entity;

import com.quanzhi.audit_core.common.model.DataLabel;
import lombok.Data;
import org.springframework.util.CollectionUtils;

import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2022/4/20 11:11 上午
 */
@Data
public class DataLabelClassify {

    private String id;

    /**
     * 分类分级名称
     */
    private String name;

    /**
     * 开关
     */
    private boolean enabled;

    private String desc;

    private Integer labelNum;

    private boolean deletable;

    /**
     * 数据
     */
    private List<Classify> data;

    /**
     * 分类的层级数量
     */
    private Integer depth;

    @Data
    public static class Classify {

        private String classify;

        /**
         * @since 3.3.0-beta
         */
        private String classifyId;

        /**
         * 分级
         * 不能用数组的index，有时会缺失
         * @desc 不知道什么意思，同一层级会存在重复
         */
        private int level;

        /**
         * 是否可删除
         * @since 3.2
         */
        private boolean deletable;

        private List<ClassifyCell> data;

        private List<Classify> children;

    }

    @Data
    public static class ClassifyCell {

        private int labelLevel;

        private List<String> labelIds;

        //页面展示用
        private List<String> labelNames;
    }

    //nacos配置中并没有按分类分级level进行排序，这边主动排下序
    public static List<DataLabelClassify> getSortedDataLabelClassify (
            List<DataLabelClassify> dataLabelClassifies,
                                                                      Map<String, DataLabel> dataLabelMap) {


        Map<String,String> classifyDescMap = new HashMap<>();
        classifyDescMap.put("个人信息分类分级","此模版分类分级标准依据国标委发布的信息安全技术个人信息安全规范。");
        classifyDescMap.put("金融分类分级","此模版分类分级标准依据于中国人民银行发布的金融数据安全-数据安全分级指南。");
        classifyDescMap.put("电信分类分级","此模版分类分级依据为工信部发布的基础电信企业数据分类分级方法。");


        for(DataLabelClassify dataLabelClassify : dataLabelClassifies) {

            HashSet labelIds = new HashSet();

            sortClassifies( dataLabelClassify.getData(),dataLabelMap, labelIds );

            dataLabelClassify.setLabelNum( labelIds.size() );
            dataLabelClassify.setDesc( classifyDescMap.get(dataLabelClassify.getId()  ) );
        }

        return dataLabelClassifies;
    }

    private static void sortClassifies(List<Classify> classifies ,Map<String, DataLabel> dataLabelMap,HashSet labelIds) {

        if( !CollectionUtils.isEmpty( classifies ) ) {

            classifies.sort( (i,j) -> {
                int level_1 = i.getLevel();
                int level_2 = j.getLevel();

                return level_1 - level_2 >= 0 ? 1 : -1;
            });

            for(Classify classify : classifies ) {

                if( !CollectionUtils.isEmpty( classify.getChildren() ) ) {
                    sortClassifies( classify.getChildren(),dataLabelMap,labelIds );
                }

                if( !CollectionUtils.isEmpty(classify.getData()) ) {
                     classify.getData().stream().forEach(i -> {

                         if(!CollectionUtils.isEmpty(i.getLabelIds())) {

                             labelIds.addAll( i.getLabelIds() );

                             i.setLabelNames( i.getLabelIds().stream().map(j -> {
                                 if (dataLabelMap.containsKey( j )) {
                                     return dataLabelMap.get( j ).getName();
                                 } else {
                                    return j;
                                 }
                             }).collect(Collectors.toList()) );
                         }
                     });
                }
            }
        }
    }
}
