package com.quanzhi.auditapiv2.common.dal.dao;

import com.quanzhi.auditapiv2.common.dal.dao.base.IBaseDao;
import com.quanzhi.auditapiv2.common.dal.entity.GatewaySSLPem;
import com.quanzhi.auditapiv2.common.dal.entity.SysUpdateLog;

import java.util.List;

/**
 * <AUTHOR>
 * @description  ssl证书相关
 * @date 2023/8/24 18:47
 */
public interface IGatewaySSLPemDao extends IBaseDao<GatewaySSLPem> {

    GatewaySSLPem findById(String id);

    List<GatewaySSLPem> selectSSLPemPage(String name, Integer page, Integer limit) throws Exception;

    Long count(String name);

    void deleteById(String id);

    List<GatewaySSLPem> selectSSLPemByIsAll(Boolean isAll);
}
