package com.quanzhi.auditapiv2.common.dal.dao;

import com.quanzhi.audit_core.common.model.DLPPolicy;

import java.util.List;

/**
 * <AUTHOR>
 * @interface DLPPolicyDao
 * @created 2023/2/24 17:19
 * @desc
 **/
@SuppressWarnings("AlibabaClassNamingShouldBeCamel")
public interface DLPPolicyDao {

    /**
     * 保存 更新
     *
     * @param label  标签ID
     * @param policy policy
     */
    void save(String label, DLPPolicy policy);

    /**
     * 查询
     *
     * @param labelId 标签
     * @return
     */
    DLPPolicy findOne(String labelId);

    /**
     * 删除
     *
     * @param label 标签ID
     */
    void remove(String label);

    List<DLPPolicy> findAll();

    void bulkUpdate(List<DLPPolicy> policies);
}
