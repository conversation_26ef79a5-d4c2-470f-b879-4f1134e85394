package com.quanzhi.auditapiv2.common.dal.dto;

import com.alibaba.fastjson.JSON;
import com.quanzhi.audit_core.common.model.ScopeMatchInfo;
import com.quanzhi.auditapiv2.common.dal.entity.FileSensitive;
import lombok.Data;

/**
 * <AUTHOR>
 * @Description 文件敏感等级配置
 * @Version
 * @createTime 2023/2/20 4:58 PM
 */
@Data
public class FileSensitiveDto {
    // 数据id
    private String id;

    // 等级名称
    private String levelName;

    // 等级描述
    private String levelDesc;


    // 匹配规则
    private ScopeMatchInfo scopeMatchInfo;

    // 启用状态: true-启用，false-关闭
    private Boolean enableFlag;



    public FileSensitive covertFileSensitive(){
        FileSensitive fileSensitive = JSON.parseObject(JSON.toJSONString(this), FileSensitive.class);

        return fileSensitive;
    }
}
