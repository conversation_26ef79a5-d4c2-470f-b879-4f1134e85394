package com.quanzhi.auditapiv2.common.dal.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * @author: yangzixian
 * @date: 10/4/2023 18:19
 * @description:
 */
@Data
public class DealApiDto {

    @ApiModelProperty(value = "同步配置ID列表", name = "configIds")
    private List<String> configIds;

    @ApiModelProperty(value = "推送字段列表", name = "fields")
    private List<String> fields;

    @ApiModelProperty(value = "推送字段是否全选，此处为true则推送字段列表不使用", name = "isAllFields")
    private Boolean isAllFields;

    @ApiModelProperty(value = "检索条件，批量推送根据检索条件获取推送数据列表，单个处理条件使用资产id检索单个资产", name = "httpApiSearchDto")
    private HttpApiSearchDto httpApiSearchDto;

}
