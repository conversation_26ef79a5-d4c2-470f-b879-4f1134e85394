package com.quanzhi.auditapiv2.common.dal.dto.gateway;

import lombok.Data;

/**
 * <AUTHOR>
 * @description
 * @date 2023/9/13 14:29
 */
@Data
public class GatewayEthConfigDto {


    private String id;

    private Long time;

    /**
     * 网口别名
     */
    private String name;

    /**
     * 网口原始名
     */
    private String ethName;

    /**
     * 网关ip
     */
    private String gatewayIp;

    /**
     * 网口绑定ip
     */
    private String bindIp;

    /**
     * 绑定状态
     */
    private Integer bindStatus;

    /**
     * 7天流量峰值
     */
    private String maxFlowFor7d;

    /**
     * 7天平均流量
     */
    private String avgFlowFor7d;

    /**
     * 创建时间
     */
    private Long createTime;

    /**
     * 修改时间
     */
    private Long updateTime;
}
