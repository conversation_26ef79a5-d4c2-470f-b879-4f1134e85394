package com.quanzhi.auditapiv2.common.dal.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * @author: yang<PERSON>xian
 * @date: 10/4/2023 19:46
 * @description:
 */
@Data
public class ApiWeaknessGroupDto {

    @ApiModelProperty(value = "检索条件", name = "params")
    private String params;

    @ApiModelProperty(value = "1正序，2倒序", name = "sort")
    private Integer sort;

    @ApiModelProperty(value = "分组字段，逗号分隔", name = "groupFields")
    private String groupFields;

    @ApiModelProperty(value = "类型", name = "categorys")
    private List<String> categorys;

}
