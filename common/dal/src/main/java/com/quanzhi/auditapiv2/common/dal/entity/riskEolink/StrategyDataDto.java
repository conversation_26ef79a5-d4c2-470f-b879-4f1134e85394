package com.quanzhi.auditapiv2.common.dal.entity.riskEolink;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;
import java.util.Map;

@Data
public class StrategyDataDto {
    private String id;
    /**
     * 策略类型
     */
    @ApiModelProperty(value = "策略类型", name = "strategyType")
    @JsonProperty("strategy_type")
    private String strategyType;
    /**
     * 策略UUID，不填则自动生成
     */
    @ApiModelProperty(value = "策略UUID，不填则自动生成", name = "uuid")
    private String uuid;
    /**
     * 策略名称
     */
    @ApiModelProperty(value = "策略名称", name = "name")
    private String name;
    /**
     * 策略描述
     */
    @ApiModelProperty(value = "策略描述", name = "desc")
    private String desc;
    /**
     * 筛选条件列表
     */
    @ApiModelProperty(value = "筛选条件列表", name = "filters")
    private Map<String, String> filters;

    private StrategyConfig config;

    /**
     * 集群状态列表
     */
    @ApiModelProperty(value = "集群状态列表", name = "clusterStatuses")
    @JsonProperty("cluster_statuses")
    private List<ClusterStatuses> clusterStatuses;

    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间", name = "createTime")
    @JsonProperty("create_time")
    private String createTime;

    /**
     * 修改时间
     */
    @ApiModelProperty(value = "修改时间", name = "updateTime")
    @JsonProperty("update_time")
    private String updateTime;

    @Data
    public static class ClusterStatuses {
        /**
         * 集群ID
         */
        @ApiModelProperty(value = "集群ID", name = "id")
        private String id;
        /**
         * 集群名
         */
        @ApiModelProperty(value = "集群名", name = "name")
        private String name;
        /**
         * 集群标题
         */
        @ApiModelProperty(value = "集群标题", name = "title")
        private String title;
        /**
         * 状态
         */
        @ApiModelProperty(value = "状态 offline 未上线 online 已发布 upgrade 待更新", name = "status")
        private String status;
    }
}
