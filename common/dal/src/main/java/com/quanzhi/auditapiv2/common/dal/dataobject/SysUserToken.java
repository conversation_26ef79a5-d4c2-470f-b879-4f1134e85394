package com.quanzhi.auditapiv2.common.dal.dataobject;

import lombok.Data;
import org.springframework.data.mongodb.core.mapping.Document;

import java.io.Serializable;

/**
 * @Description:
 * @Author: danniel.yu
 * @Date: 2020-05-11 16:11
 */
@Data
@Document
public class SysUserToken implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 用户id
     */
    private String userId;

    /**
     * token
     */
    private String token;

    /**
     * 过期时间
     */
    private Long gmtExpire;

    /**
     * 更新时间
     */
    private Long gmtModified;
}
