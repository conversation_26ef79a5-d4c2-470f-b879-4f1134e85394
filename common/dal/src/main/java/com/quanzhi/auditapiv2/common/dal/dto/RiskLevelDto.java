package com.quanzhi.auditapiv2.common.dal.dto;

import com.alibaba.fastjson.JSON;
import com.quanzhi.audit_core.common.model.RiskLevel;
import com.quanzhi.audit_core.common.model.ScopeMatchInfo;
import lombok.Data;

/**
 * <AUTHOR>
 * @Description 风险等级配置
 * @Version
 * @createTime 2023/2/20 4:58 PM
 */
@Data
public class RiskLevelDto {
    // 数据id
    private String id;

    // 等级名称
    private String levelName;

    // 等级描述
    private String levelDesc;

    // 等级
    private Integer level = 0;

    // 匹配规则
    private ScopeMatchInfo scopeMatchInfo;

    // 启用状态: true-启用，false-关闭
    private Boolean enableFlag;

    // 风险等级类型：API、IP、ACCOUNT
    // @See com.quanzhi.auditapiv2.core.model.SubjectType
    private String levelType;


    public RiskLevel covertRiskLevel(){
        RiskLevel riskLevel = JSON.parseObject(JSON.toJSONString(this), RiskLevel.class);

        return riskLevel;
    }
}
