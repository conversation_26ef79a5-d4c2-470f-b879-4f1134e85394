package com.quanzhi.auditapiv2.common.dal.dao;

import com.quanzhi.auditapiv2.common.dal.dao.base.IBaseDao;
import com.quanzhi.auditapiv2.common.dal.entity.GatewaySSLInfo;

import java.util.List;

public interface IGatewaySSLInfoDao extends IBaseDao<GatewaySSLInfo> {

    List<GatewaySSLInfo> getList(Long startTime, Long endTime, String gatewayIp);

    Long getCount(Long start, Long end);

    GatewaySSLInfo findById(String id);
}
