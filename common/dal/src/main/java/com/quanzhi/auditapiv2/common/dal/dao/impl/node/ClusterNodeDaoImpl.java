package com.quanzhi.auditapiv2.common.dal.dao.impl.node;

import com.quanzhi.auditapiv2.common.dal.dao.common.QuickConfigDao;
import com.quanzhi.auditapiv2.common.dal.dao.node.ClusterNodeDao;
import com.quanzhi.auditapiv2.common.dal.entity.common.QuickConfig;
import com.quanzhi.auditapiv2.common.dal.entity.node.ClusterNode;
import com.quanzhi.auditapiv2.common.dal.entity.node.ClusterType;
import com.quanzhi.auditapiv2.common.dal.entity.node.event.ClusterNodeAddEvent;
import com.quanzhi.auditapiv2.common.dal.entity.node.event.ClusterNodeRemoveEvent;
import com.quanzhi.auditapiv2.common.util.dto.ListOutputDto;
import lombok.RequiredArgsConstructor;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.stereotype.Repository;
import org.springframework.util.ObjectUtils;

import java.util.List;
import java.util.Map;
import java.util.UUID;
import java.util.stream.Collectors;

@Repository
@RequiredArgsConstructor
public class ClusterNodeDaoImpl implements ClusterNodeDao {
    private final MongoTemplate mongoTemplate;
    private final ApplicationEventPublisher eventPublisher;
    private final QuickConfigDao quickConfigDao;
    private final UniqCode uniqCode;
    private static final String GROUP = "cluster";
    private static final String DATA_ID = "nodeId";

    @Override
    public ClusterNode save(ClusterNode role) {
        // 防止前端空内容
        if (ObjectUtils.isEmpty(role.getId())) {
            role.setId(null);
        }
        boolean newAdd = role.getId() == null;
        if (role.getCreateTimestamp() <= 0) {
            role.setCreateTimestamp(System.currentTimeMillis());
        }
        role.setUpdateTimestamp(System.currentTimeMillis());
        ClusterNode node = mongoTemplate.save(role);
        if (newAdd) {
            eventPublisher.publishEvent(new ClusterNodeAddEvent(role));
        }
        return node;
    }

    @Override
    public void renew(ClusterNode node) {
        mongoTemplate.updateFirst(Query.query(Criteria.where("_id").is(node.getId())),
                Update.update("lastAliveTimestamp", node.getLastAliveTimestamp()).set("state",
                        node.getState().name()).set("updateTimestamp", System.currentTimeMillis()).set("errorMsg", node.getErrorMsg()), ClusterNode.class);
    }

    @Override
    public ListOutputDto<ClusterNode> page(Pageable pageable) {
        return ListOutputDto.page(mongoTemplate.find(new Query().limit(pageable.getPageSize()).skip(pageable.getOffset()), ClusterNode.class), mongoTemplate.count(new Query(), ClusterNode.class));
    }

    @Override
    public List<ClusterNode> findAll() {
        return mongoTemplate.findAll(ClusterNode.class);
    }

    @Override
    public ClusterNode findOne(String id) {
        try {
            return mongoTemplate.findById(id, ClusterNode.class);
        } catch (Exception e) {
            return null;
        }
    }

    @Override
    public void remove(ClusterNode role) {
        mongoTemplate.remove(role);
        eventPublisher.publishEvent(new ClusterNodeRemoveEvent(role));
    }

    @Override
    public ClusterNode findByUrl(String url) {
        return mongoTemplate.findOne(Query.query(Criteria.where("url").is(url)), ClusterNode.class);
    }

    @Override
    public ClusterNode findByNID(String nid) {
        return mongoTemplate.findOne(Query.query(Criteria.where("nid").is(nid)), ClusterNode.class);
    }

    @Override
    public ClusterNode getMasterNode() {
        return mongoTemplate.findOne(Query.query(Criteria.where("type").is(ClusterType.MASTER.name())), ClusterNode.class);
    }

    @Override
    public boolean exist() {
        return mongoTemplate.exists(new Query(), ClusterNode.class);
    }

    @Override
    public String getNodeId() {
        QuickConfig quickConfig = quickConfigDao.getOne(DATA_ID, GROUP);
        if (quickConfig == null) {
            quickConfig = new QuickConfig();
            quickConfig.setGroup(GROUP);
            quickConfig.setKey(DATA_ID);
            quickConfig.setVersion(1);
            quickConfig.setCreateTime(System.currentTimeMillis());
            quickConfig.setUpdateTime(System.currentTimeMillis());
            quickConfig.setValue(uniqCode.getUniqCode());
            quickConfigDao.save(quickConfig);
        }
        return quickConfig.getValue();
    }

    @Override
    public ClusterType getClusterType() {
        if (!exist()) {
            return ClusterType.STANDALONE;
        }
        // 如果该单机节点中存储着主节点，证明是子节点
        if (getMasterNode() != null) {
            return ClusterType.SLAVE;
        }
        return ClusterType.MASTER;
    }

    @Override
    public Map<String, ClusterNode> getNid2NodeMap() {
        //获取节点id和名称的映射
        List<ClusterNode> nodes = findAll();
        Map<String, ClusterNode> id2Node = nodes.stream().collect(Collectors.toMap(ClusterNode::getNid, obj -> obj));
        return id2Node;
    }


}
