package com.quanzhi.auditapiv2.common.dal.dto.query;

import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * 封装nacos配置文件内容过滤条件
 * 暂时只支持 and 查询
 */
@Data
@NoArgsConstructor
public class NacosFilterDto implements Serializable {

    /**
     * 过滤的key
     */
    private String key;

    /**
     * 匹配模式
     * @see Predicate
     */
    private Predicate predicate;

    /**
     * 过滤匹配的值
     */
    private Object value;

    private Operator operator;

    private String sortField;

    private Integer sortType;


    private List<NacosFilterDto> nacosFilterChain;

    public NacosFilterDto(String key, Predicate predicate, Object value) {
        this.key = key;
        this.predicate = predicate;
        this.value = value;
    }

    public static final NacosFilterDto where(String property) {
        NacosFilterDto criteria = new NacosFilterDto();
        criteria.setKey(property);
        return criteria;
    }

    public NacosFilterDto is(Object value) {
        if (key == null) {
            throw new IllegalArgumentException("set property first.");
        }
        setValue(value);
        return this;
    }

    public NacosFilterDto orOperator(NacosFilterDto... criteria) {
        addOperator(Operator.OR, criteria);
        return this;
    }

    public NacosFilterDto andOperator(NacosFilterDto... criteria) {
        addOperator(Operator.AND, criteria);
        return this;
    }

    public void addOperator(Operator op, NacosFilterDto... criteria) {
        if (operator != null) {
            throw new IllegalArgumentException("exist " + operator + " operator.");
        }
        this.operator = op;
        if (this.nacosFilterChain == null) {
            this.nacosFilterChain = new ArrayList<>();
        }
        this.nacosFilterChain.addAll(Arrays.asList(criteria));
    }
}
