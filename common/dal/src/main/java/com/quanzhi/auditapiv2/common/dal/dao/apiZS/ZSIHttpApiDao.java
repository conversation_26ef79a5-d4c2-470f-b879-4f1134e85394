package com.quanzhi.auditapiv2.common.dal.dao.apiZS;

import com.quanzhi.auditapiv2.common.dal.entity.apiZS.CountAppDomainEntity;
import com.quanzhi.auditapiv2.common.dal.entity.apiZS.CountDataLabelAppEntity;


import java.util.List;
import java.util.Map;

/**
 *浙商定开，Dao层接口
 * @Author: 胡政乡
 * @Date: 2023/11/2 11:31
 **/
public interface ZSIHttpApiDao {



    /**
     *获得在标签数据的基础上的应用访问量前20
     * @param listLabel:
     * @return: java.util.List<java.lang.String>
     * @Author: 胡政乡
     * @Date: 2023/11/6 10:06
     **/

    List<String> selectAppTop20(List<String> listLabel);

    /**
     *获得在标签数据的基础上的网段访问量前10
     * @param listLabel:
     * @return: java.util.List<java.lang.String>
     * @Author: 胡政乡
     * @Date: 2023/11/6 11:16
     **/

    List<String> selectNetworkTop10(List<String> listLabel);


    /**
     *获得访问量前10的标签
     * @return:
     * @Author: 胡政乡
     * @Date: 2023/11/1 11:51
     **/
    Map<String, Integer> selectApiCountTop10();

    /**
     *获得标签与应用聚合的api数量
     * @param labelTop:
     * @param appList:
     * @return: java.util.List<com.quanzhi.auditapiv2.common.dal.entity.apiZS.CountDataLabelAppEntity>
     * @Author: 胡政乡
     * @Date: 2023/11/1 11:48
     **/
    List<CountDataLabelAppEntity> selectApiAndAppCount(List<String> labelTop, List<String> appList );

    /**
     *获得标签、应用、网段聚合的结果
     * @param labelTop:
     * @param appList:
     * @param visitDomains:
     * @return: java.util.List<com.quanzhi.auditapiv2.common.dal.entity.apiZS.CountAppDomainEntity>
     * @Author: 胡政乡
     * @Date: 2023/11/2 15:59
     **/

    List<CountAppDomainEntity> selectAppAndDomainCount(List<String> labelTop, List<String> appList, List<String> visitDomains );
}
