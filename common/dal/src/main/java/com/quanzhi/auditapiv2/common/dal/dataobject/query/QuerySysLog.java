package com.quanzhi.auditapiv2.common.dal.dataobject.query;

import com.quanzhi.auditapiv2.common.dal.common.entity.QueryBase;
import lombok.Data;
import org.bson.types.ObjectId;

/**
 * @Description:
 * @Author: danniel.yu
 * @Date: 2020-05-11 15:24
 */
@Data
public class QuerySysLog  extends QueryBase {
    /**
     * 日志id
     */
    private ObjectId id;

    /**
     * 操作用户id
     */
    private ObjectId userId;

    /**
     * 操作用户
     */
    private String username;

    /**
     * 操作
     */
    private String operation;

    /**
     * 方法
     */
    private String method;


}
