package com.quanzhi.auditapiv2.common.dal.entity;

import com.quanzhi.audit_core.common.adaptive.AdaptiveCounting;
import com.quanzhi.metabase.core.model.Entity;
import com.quanzhi.metabase.core.model.http.HttpResourceConstant;
import lombok.Data;
import org.springframework.data.mongodb.core.mapping.Document;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2020/10/21 下午5:38
 */
@Data
@Entity(HttpResourceConstant.IP_DATE_INFO)
@Document(HttpResourceConstant.IP_DATE_INFO)
public class IpDateInfo implements Serializable {

    /**
     * 当天
     */
    private String date;

    private Long loginCnt;

    private List<String> relatedAccountList;

    private List<String> appUriList;

    private List<String> apiUriList;

    private List<String> eventDefineIdList;

    private AdaptiveCounting rspDataBucket;

    private Integer reqBodyLengthCnt;

    private Integer rspBodyLengthCnt;

    private String id;

    private String ip;

    private String country;

    private String province;

    private String city;

    @Deprecated
    private String accessDomainId;

    private List<String> accessDomainIds;

    @Deprecated
    private Map<String,String> domain;

    /**
     * 按风险标签分组的风险列表
     */
    private Map<String,List<String>> riskByRiskTag;

    /**
     * 访问次数
     */
    private Long visitCnt;

    //ua 去重列表
    private List<String> uaTypes;

    /**
     * 关联异常数量
     */
    @Deprecated
    private Long abnCnt;

    /**
     * 首次发现日期
     */
    private String firstDate;

    /**
     * 最后发现日期
     */
    private String lastDate;

    /**
     * 成功登录账号去重数量
     */
    private Long loginSuccessAccountDistinctCnt;

    /**
     * 成功登录账号清单，限制在1000以内
     */
    private List<String> loginSuccessAccountList;

    /**
     * 1天的按标签分组的敏感数据去重统计量
     */
    @Deprecated
    private Map<String,Long> rspDataDistinctCnt1dByLabel;

    /**
     * 7天的按标签分组的敏感数据去重统计量
     */
    @Deprecated
    private Map<String,Long> rspDataDistinctCnt7dByLabel;

    /**
     * 30天的按标签分组的敏感数据去重统计量
     */
    @Deprecated
    private Map<String,Long> rspDataDistinctCnt30dByLabel;

    /**
     * 全部的按标签分组的敏感数据去重统计量
     */
    @Deprecated
    private Map<String,Long> rspDataDistinctCntByLabel;

    /**
     * 1天的按标签分组的数据量去重桶
     */
    @Deprecated
    private Map<String,Object> dataBucket1dByLabel;

    /**
     * 7天的按标签分组的数据量去重桶
     */
    @Deprecated
    private Map<String,Object> dataBucket7dByLabel;

    /**
     * 30天的按标签分组的数据量去重桶
     */
    @Deprecated
    private Map<String,Object> dataBucket30dByLabel;

    /**
     * 全部的按标签分组的数据量去重桶
     */
    @Deprecated
    private Map<String,Object> dataBucketByLabel;

    /**
     * 单次最大返回数据量
     */
    private Long maxRspDataDistinctCnt;

    /**
     * 单次最大返回数据量时间
     */
    private String maxRspDataDistinctCntDate;

    /**
     * 关联账号去重数
     */
    private Long relatedAccountDistinctCnt;

    /**
     * 关联账号的桶
     */
    private AdaptiveCounting relatedAccountBucket;

    /**
     * 下载文件的桶
     */
    private AdaptiveCounting downloadFileBucket;

    /**
     * IP下载文件去重数
     */
    private Long downloadFileDistinctCnt;


    /**
     * 上传文件sha256的去重桶
     */
    private AdaptiveCounting uploadFileBucket;

    /**
     * 返回数据标签
     */
    private List<String> rspDataLabelList;

    /**
     * 返回数据去重量
     */
    private Long rspDataDistinctCnt;

    /**
     * 按终端类型分组的终端列表
     */
    private Map<String,List<String>> uaByUaType;


    /**
     * 按小时统计的访问量
     */
    private List<Long> hourlyVisitCnt;


    private List<Long> loginCntHrly;

}
