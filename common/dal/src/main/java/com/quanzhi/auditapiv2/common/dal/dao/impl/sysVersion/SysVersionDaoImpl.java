package com.quanzhi.auditapiv2.common.dal.dao.impl.sysVersion;

import com.quanzhi.auditapiv2.common.dal.dao.sysVersion.SysVersionDao;
import com.quanzhi.auditapiv2.common.dal.entity.SysVersion;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * @author: yang<PERSON>xian
 * @date: 2022/8/30 16:33
 * @description:
 */
@Repository
public class SysVersionDaoImpl implements SysVersionDao {

    private final MongoTemplate mongoTemplate;

    private final String collectionName = "sysVersion";

    public SysVersionDaoImpl(MongoTemplate mongoTemplate) {
        this.mongoTemplate = mongoTemplate;
    }

    @Override
    public SysVersion getSysVersion(String id) {
        return mongoTemplate.findOne(new Query().addCriteria(Criteria.where("_id").is(id)), SysVersion.class, collectionName);
    }

    @Override
    public SysVersion saveSysVersion(SysVersion sysVersion) {
        Update update = new Update();
        update.set("version", sysVersion.getVersion());
        mongoTemplate.upsert(new Query().addCriteria(Criteria.where("_id").is(sysVersion.getId())), update, collectionName);
        return sysVersion;
    }

    @Override
    public List<SysVersion> getAll() {
        return mongoTemplate.find(new Query(), SysVersion.class, collectionName);
    }

    @Override
    public void init() {
        if (!mongoTemplate.collectionExists(collectionName)) {
            mongoTemplate.createCollection(collectionName);
        }
    }
}
