package com.quanzhi.auditapiv2.common.dal.dao.impl;

import com.quanzhi.audit_core.common.constant.Configs;
import com.quanzhi.auditapiv2.common.dal.dao.IRetainPolicyDao;
import com.quanzhi.auditapiv2.common.dal.dao.base.INacos;
import com.quanzhi.auditapiv2.common.util.utils.DataUtil;
import org.apache.poi.ss.formula.functions.T;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.util.HashMap;
import java.util.Map;

@Repository
public class RetainPolicyDaoImpl implements IRetainPolicyDao{
    private Logger logger = LoggerFactory.getLogger(RetainPolicyDaoImpl.class);

    @Autowired
    protected INacos<T> nacosImpl;

    @Override
    public Map<String,String> getRetainCount() {
        Map<String,String> countMap = new HashMap<>();
        countMap.put("discover_sample_count","0");
        countMap.put("metabase_snapshot_count","0");

        try {
            countMap.put("discover_sample_count",nacosImpl.getPropertyValue(Configs.Discover.SAMPLE_LEAST_COUNT,Configs.Discover.DISCOVER_DATA,"discover"));
            countMap.put("metabase_snapshot_count",nacosImpl.getPropertyValue(Configs.Metabase.SNAPSHOT_COUNT,Configs.Metabase.METABASE_DATA,"metabase"));
        } catch (Exception e) {
            logger.error("nacos err is:",e);
        }
        return countMap;
    }

    @Override
    public void save(String discover_sample_count,String metabase_snapshot_count){

        if(DataUtil.isNotEmpty( discover_sample_count )) {
            try {
                nacosImpl.publishPropertyValue(Configs.Discover.SAMPLE_LEAST_COUNT,discover_sample_count,Configs.Discover.DISCOVER_DATA,"discover");
            } catch (Exception e) {
                logger.error("nacos err is:",e);
            }
        }

        if(DataUtil.isNotEmpty( metabase_snapshot_count )) {
            try {
                nacosImpl.publishPropertyValue(Configs.Metabase.SNAPSHOT_COUNT,metabase_snapshot_count,Configs.Metabase.METABASE_DATA,"metabase");
            } catch (Exception e) {
                logger.error("nacos err is:",e);
            }

        }
    }
}
