package com.quanzhi.auditapiv2.common.dal.entity;

import com.quanzhi.auditapiv2.common.util.utils.DataUtil;
import com.quanzhi.auditapiv2.common.util.utils.DateUtil;
import lombok.Data;

import java.util.Date;

/**
 * Created by she<PERSON><PERSON> on 2017/11/9.
 */
@Data
public class AuditLogModel {

    private String id;

    private String username;

    private String log;

    private Long logTime;

    private String logTimeFormat;

    private String ip;

    private String userAgent;

    public String getLogTimeFormat() {
        if (DataUtil.isEmpty(logTime)) {
            return "0000-00-00 00:00:00";
        } else {
            return DateUtil.format(new Date(logTime), DateUtil.DATE_PATTERN.YYYY_MM_DD_HH_MM_SS);
        }
    }
}
