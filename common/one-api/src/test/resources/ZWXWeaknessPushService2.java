package com.quanzhi.auditapiv2.api.wxb;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.alibaba.nacos.api.config.annotation.NacosValue;
import com.quanzhi.auditapiv2.api.repository.PushRepository;
import com.quanzhi.auditapiv2.api.service.AbstractWeaknessPushService;
import com.quanzhi.auditapiv2.common.util.utils.DateUtils;
import com.quanzhi.metabase.core.model.http.weakness.ApiWeakness;
import okhttp3.*;
import org.apache.commons.collections4.CollectionUtils;

import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @date 2021/10/27 10:01 上午
 */
public class ZWXWeaknessPushService2 extends AbstractWeaknessPushService {

    private OkHttpClient httpClient = (new OkHttpClient.Builder())
            .connectTimeout(30L, TimeUnit.SECONDS)
            .readTimeout(30L, TimeUnit.SECONDS)
            .build();

    @NacosValue("${push.zwx.baseUrl:null}")
    private String baseUrl;
    @NacosValue("${push.zwx.key:null}")
    private String key;
    @NacosValue("${push.zwx.from:null}")
    private String from;

    private static final String HALT = "vopc_2018%receive_api%#!=";

    public ZWXWeaknessPushService2(PushRepository pushRepository) {
        super(pushRepository);
    }

    @Override
    public void push(List<ApiWeakness> weaknesses) {
        List<ZWXWeakness> zwxWeaknesses = new ArrayList<>();
        for (ApiWeakness weakness : weaknesses) {
            ZWXWeakness zwxWeakness = createZwxWeakness(weakness);
            if (zwxWeakness != null) {
                zwxWeaknesses.add(zwxWeakness);
            }
        }
        if (CollectionUtils.isEmpty(zwxWeaknesses)) {
            return;
        }
        ZWXRequest<ZWXWeakness> request = new ZWXRequest<>();
        request.setData_values(zwxWeaknesses);
        long timestamp = System.currentTimeMillis();
        try {
            request.setToken(MD5(from + timestamp + HALT + key));
        } catch (NoSuchAlgorithmException e) {
            throw new ZWXServiceException(e);
        } catch (UnsupportedEncodingException e) {
            throw new ZWXServiceException(e);
        }
        request.setTimestamp(timestamp);
        request.setTotal(zwxWeaknesses.size());
        Request r = (new Request.Builder())
                .put(RequestBody.create(MediaType.parse("application/json; charset=utf-8")
                        , JSON.toJSONString(request, new SerializerFeature[]{SerializerFeature.IgnoreNonFieldGetter, SerializerFeature.WriteNullListAsEmpty})))
                .url(this.baseUrl + "/receive/eventlog/api").build();
        execute(r);
    }

    private ZWXWeakness createZwxWeakness(ApiWeakness weakness) {
        ZWXWeakness zwxWeakness = new ZWXWeakness();
        zwxWeakness.setCompanyCode("91330110MA28T76R41");
        switch (weakness.getWeaknessId()) {
            case "dboperatorapi":
                zwxWeakness.setIncidentType("SAV001");
                break;
            case "shellapi":
                zwxWeakness.setIncidentType("SAV011");
                break;
            case "password":
            case "horizontalaccess":
                zwxWeakness.setIncidentType("SAV007");
                break;
            case "anyfiledownload":
                zwxWeakness.setIncidentType("SAV009");
                break;
            case "weakpassword":
                zwxWeakness.setIncidentType("CAV001");
                break;
        }
        if (zwxWeakness.getIncidentType() == null) {
            return null;
        }
        zwxWeakness.setVulName(weakness.getName());
        zwxWeakness.setVulLevel(4);
        zwxWeakness.setVulCode(weakness.getId());
        switch (weakness.getLevel()) {
            case 1:
                zwxWeakness.setVulLevel(4);
                break;
            case 2:
                zwxWeakness.setVulLevel(3);
                break;
            case 3:
                zwxWeakness.setVulLevel(2);
                break;
        }
        zwxWeakness.setFoundTime(DateUtils.format(new Date(weakness.getCreateTime()), "YYYY-MM-dd hh:mm:ss"));
        zwxWeakness.setIsIntelligence(0);
        if (weakness.getApi() != null
                && weakness.getApi().getApiUrl() != null
                && weakness.getApi().getApiUrl().startsWith("https")) {
            zwxWeakness.setProtocol("https");
        } else {
            zwxWeakness.setProtocol("http");
        }
        zwxWeakness.setStartTime(DateUtils.format(new Date(weakness.getEarlyTimestamp()), "YYYY-MM-dd hh:mm:ss"));
        zwxWeakness.setEndTime(zwxWeakness.getStartTime());
        return zwxWeakness;
    }

    private void execute(Request request) {
        try {
            Response resp = httpClient.newCall(request).execute();
            ZWXResponse response = parseResp(resp);
            if (!response.checkSuccess()) {
                throw new ZWXServiceException(response.getResult_code(), response.getMessage());
            }
        } catch (IOException e) {
            throw new ZWXServiceException(e);
        }
    }

    private ZWXResponse parseResp(Response resp) {
        if (resp.isSuccessful()) {
            try {
                return (ZWXResponse)JSON.parseObject(resp.body().string(), ZWXResponse.class);
            } catch (IOException var4) {
                throw new ZWXServiceException(var4);
            }
        } else {
            throw new ZWXServiceException(resp.toString());
        }
    }

    public static String MD5(String data) throws NoSuchAlgorithmException, UnsupportedEncodingException {
        MessageDigest md = MessageDigest.getInstance("MD5");
        byte[] array = md.digest(data.getBytes("UTF-8"));
        StringBuilder sb = new StringBuilder();
        for (byte item : array) {
            sb.append(Integer.toHexString((item & 0xFF) | 0x100).substring(1, 3));
        }
        return sb.toString().toUpperCase();
    }

}



