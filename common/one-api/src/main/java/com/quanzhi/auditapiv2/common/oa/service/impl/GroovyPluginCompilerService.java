//package com.quanzhi.auditapiv2.common.oa.service.impl;
//
//import com.quanzhi.auditapiv2.common.oa.domain.ContentType;
//import com.quanzhi.auditapiv2.common.oa.domain.Language;
//import com.quanzhi.auditapiv2.common.oa.domain.Plugin;
//import com.quanzhi.auditapiv2.common.oa.exception.PluginCompilerException;
//import com.quanzhi.auditapiv2.common.oa.exception.PluginResolveException;
//import com.quanzhi.auditapiv2.common.oa.service.PluginBeanResolver;
//import com.quanzhi.auditapiv2.common.oa.service.PluginCompilerService;
//import groovy.lang.GroovyClassLoader;
//import org.springframework.stereotype.Service;
//
//import java.io.File;
//import java.io.IOException;
//
///**
// * <AUTHOR>
// * @date 2021/10/28 11:04 上午
// */
////@Service
//public class GroovyPluginCompilerService implements PluginCompilerService {
//
//    private final GroovyClassLoader groovyClassLoader = new GroovyClassLoader();
//
//    private final PluginBeanResolver pluginBeanResolver;
//
//    public GroovyPluginCompilerService(PluginBeanResolver pluginBeanResolver) {
//        this.pluginBeanResolver = pluginBeanResolver;
//    }
//
//    @Override
//    public <T> T compile(Plugin plugin) throws PluginCompilerException {
//        Class<?> clz;
//        if (plugin.getContentType() == ContentType.CODE) {
//            clz = groovyClassLoader.parseClass(plugin.getContent());
//        } else if (plugin.getContentType() == ContentType.PATH) {
//            try {
//                clz = groovyClassLoader.parseClass(new File(plugin.getContent()));
//            } catch (IOException e) {
//                throw new PluginCompilerException(e);
//            }
//        } else {
//            throw new PluginCompilerException("error plugin contentType: " + plugin.getContentType());
//        }
//        try {
//            return (T) pluginBeanResolver.resolve(clz);
//        } catch (PluginResolveException e) {
//            throw new PluginCompilerException(e);
//        }
//    }
//
//    @Override
//    public boolean checkCompile(Plugin plugin) {
//        return plugin.getLanguage() == Language.GROOVY;
//    }
//}
