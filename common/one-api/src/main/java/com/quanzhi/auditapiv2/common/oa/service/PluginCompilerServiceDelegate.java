//package com.quanzhi.auditapiv2.common.oa.service;
//
//import com.quanzhi.auditapiv2.common.oa.domain.Plugin;
//import com.quanzhi.auditapiv2.common.oa.exception.PluginCompilerException;
//import org.springframework.context.annotation.Primary;
//import org.springframework.stereotype.Service;
//
//import java.util.List;
//
///**
// * <AUTHOR>
// * @date 2021/10/27 4:35 下午
// */
////@Service
////@Primary
//public class PluginCompilerServiceDelegate implements PluginCompilerService {
//
//    private final List<PluginCompilerService> pluginCompilerServices;
//
//    public PluginCompilerServiceDelegate(List<PluginCompilerService> pluginCompilerServices) {
//        this.pluginCompilerServices = pluginCompilerServices;
//    }
//
//    @Override
//    public <T> T compile(Plugin plugin) throws PluginCompilerException {
//        if (!checkCompile(plugin)) {
//            return null;
//        }
//        for (PluginCompilerService pluginCompilerService : pluginCompilerServices) {
//            if (pluginCompilerService.checkCompile(plugin)) {
//                T obj = pluginCompilerService.compile(plugin);
//                if (obj != null) {
//                    return obj;
//                }
//            }
//        }
//        return null;
//    }
//
//    @Override
//    public boolean checkCompile(Plugin plugin) {
//        // 可以在这里加一些签名校验的逻辑
//        return true;
//    }
//}
