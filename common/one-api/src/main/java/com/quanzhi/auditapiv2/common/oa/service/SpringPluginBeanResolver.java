//package com.quanzhi.auditapiv2.common.oa.service;
//
//import com.quanzhi.auditapiv2.common.oa.exception.PluginCompilerException;
//import com.quanzhi.auditapiv2.common.oa.exception.PluginResolveException;
//import org.springframework.beans.BeansException;
//import org.springframework.beans.factory.BeanFactory;
//import org.springframework.beans.factory.BeanFactoryAware;
//import org.springframework.stereotype.Service;
//
//import java.lang.reflect.Constructor;
//import java.lang.reflect.InvocationTargetException;
//
///**
// * <AUTHOR>
// * @date 2021/10/28 11:18 上午
// */
////@Service
//public class SpringPluginBeanResolver implements PluginBeanResolver, BeanFactoryAware {
//
//    private BeanFactory beanFactory;
//
//    @Override
//    public Object resolve(Class<?> clz) throws PluginResolveException {
//        try {
//            return newInstance(clz);
//        } catch (InstantiationException e) {
//            throw new PluginResolveException(e);
//        } catch (IllegalAccessException e) {
//            throw new PluginResolveException(e);
//        } catch (PluginCompilerException e) {
//            throw new PluginResolveException(e);
//        } catch (InvocationTargetException e) {
//            throw new PluginResolveException(e);
//        }
//    }
//
//    /**
//     * coding 需要支持数组、集合参数；支持Autowired字段、方法注入
//     * @param clz
//     * @return
//     * @throws InstantiationException
//     * @throws IllegalAccessException
//     * @throws PluginCompilerException
//     * @throws InvocationTargetException
//     */
//    private Object newInstance(Class<?> clz) throws InstantiationException, IllegalAccessException, PluginCompilerException, InvocationTargetException {
//        Constructor<?> constructors[] = clz.getConstructors();
//        if (constructors == null) {
//            return clz.newInstance();
//        }
//        if (constructors.length > 1) {
//            throw new PluginCompilerException("found one more constructors, instance fail.");
//        }
//        Constructor constructor = constructors[0];
//        Class[] parameterTypes = constructor.getParameterTypes();
//        Object[] objects = new Object[parameterTypes.length];
//        for (int i = 0; i < parameterTypes.length; i++) {
//            Class<?> parameterType = parameterTypes[i];
//            objects[i] = beanFactory.getBean(parameterType);
//        }
//        Object object = constructor.newInstance(objects);
//        return object;
//    }
//
//    @Override
//    public void setBeanFactory(BeanFactory beanFactory) throws BeansException {
//        this.beanFactory = beanFactory;
//    }
//}
