//package com.quanzhi.auditapiv2.common.oa.service.impl;
//
//import com.quanzhi.auditapiv2.common.oa.domain.ContentType;
//import com.quanzhi.auditapiv2.common.oa.domain.Language;
//import com.quanzhi.auditapiv2.common.oa.domain.Plugin;
//import com.quanzhi.auditapiv2.common.oa.exception.PluginResolveException;
//import com.quanzhi.auditapiv2.common.oa.service.PluginBeanResolver;
//import com.quanzhi.auditapiv2.common.oa.service.PluginCompilerService;
//import com.quanzhi.auditapiv2.common.oa.exception.PluginCompilerException;
//import org.codehaus.commons.compiler.CompileException;
//import org.codehaus.commons.compiler.ICompiler;
//import org.codehaus.commons.compiler.util.ResourceFinderClassLoader;
//import org.codehaus.commons.compiler.util.resource.*;
//import org.codehaus.janino.CompilerFactory;
//import org.springframework.stereotype.Service;
//
//import java.io.File;
//import java.io.IOException;
//import java.lang.reflect.InvocationTargetException;
//import java.util.HashMap;
//import java.util.Map;
//
///**
// * <AUTHOR>
// * @date 2021/10/27 3:06 下午
// */
////@Service
//public class JavaPluginCompilerService implements PluginCompilerService {
//
//    private final PluginBeanResolver pluginBeanResolver;
//
//    public JavaPluginCompilerService(PluginBeanResolver pluginBeanResolver) {
//        this.pluginBeanResolver = pluginBeanResolver;
//    }
//
//    @Override
//    public <T> T compile(Plugin plugin) throws PluginCompilerException {
//        if (!checkCompile(plugin)) {
//            return null;
//        }
//        CompilerFactory compilerFactory = new CompilerFactory();
//        ICompiler compiler = compilerFactory.newCompiler();
//        Map<String, byte[]> classes = new HashMap<>();
//        compiler.setClassFileCreator(new MapResourceCreator(classes));
//        try {
//            if (plugin.getContentType() == ContentType.CODE) {
//                compiler.compile(new Resource[]{
//                        new StringResource(
//                                plugin.getName(),
//                                plugin.getContent()
//                        )
//                });
//            } else if (plugin.getContentType() == ContentType.PATH) {
//                compiler.compile(new Resource[]{
//                        new FileResource(new File(plugin.getContent())
//                        )
//                });
//            }
//            // Set up a class loader that uses the generated classes.
//            ClassLoader cl = new ResourceFinderClassLoader(
//                    new MapResourceFinder(classes),    // resourceFinder
//                    ClassLoader.getSystemClassLoader() // parent
//            );
//            Class<?> currentClz = cl.loadClass(plugin.getName());
//            return (T) pluginBeanResolver.resolve(currentClz);
//        } catch (CompileException e) {
//            throw new PluginCompilerException(e);
//        } catch (IOException e) {
//            throw new PluginCompilerException(e);
//        } catch (PluginResolveException e) {
//            throw new PluginCompilerException(e);
//        } catch (ClassNotFoundException e) {
//            throw new PluginCompilerException(e);
//        }
//    }
//
//    @Override
//    public boolean checkCompile(Plugin plugin) {
//        return plugin.getLanguage() == Language.JAVA;
//    }
//}
