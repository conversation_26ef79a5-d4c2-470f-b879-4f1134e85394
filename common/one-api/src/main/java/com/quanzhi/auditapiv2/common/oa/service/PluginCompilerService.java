//package com.quanzhi.auditapiv2.common.oa.service;
//
//import com.quanzhi.auditapiv2.common.oa.domain.Plugin;
//import com.quanzhi.auditapiv2.common.oa.exception.PluginCompilerException;
//
///**
// * <AUTHOR>
// * @date 2021/10/27 3:04 下午
// */
//public interface PluginCompilerService {
//
//    <T> T compile(Plugin plugin) throws PluginCompilerException;
//
//    boolean checkCompile(Plugin plugin);
//
//}
