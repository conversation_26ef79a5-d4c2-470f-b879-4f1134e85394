//package com.quanzhi.auditapiv2.common.oa.repository.mongo;
//
//import com.quanzhi.auditapiv2.common.oa.domain.Plugin;
//import com.quanzhi.auditapiv2.common.oa.repository.PluginRepository;
//import org.springframework.data.mongodb.core.MongoTemplate;
//import org.springframework.data.mongodb.core.query.Criteria;
//import org.springframework.data.mongodb.core.query.Query;
//import org.springframework.stereotype.Repository;
//
//import java.util.List;
//
///**
// * <AUTHOR>
// * @date 2021/10/27 2:50 下午
// */
////@Repository
//public class PluginRepositoryMongoImpl implements PluginRepository {
//
//    private final MongoTemplate mongoTemplate;
//
//    public PluginRepositoryMongoImpl(MongoTemplate mongoTemplate) {
//        this.mongoTemplate = mongoTemplate;
//    }
//
//    @Override
//    public List<Plugin> getAll(String type) {
//        return mongoTemplate.find(new Query().addCriteria(Criteria.where("type").is(type)), Plugin.class, "commonPlugin");
//    }
//}
