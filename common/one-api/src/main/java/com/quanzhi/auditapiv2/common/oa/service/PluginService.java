/*
package com.quanzhi.auditapiv2.common.oa.service;

import com.quanzhi.auditapiv2.common.oa.domain.Plugin;
import com.quanzhi.auditapiv2.common.oa.exception.PluginCompilerException;
import com.quanzhi.auditapiv2.common.oa.repository.PluginRepository;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

*/
/**
 * <AUTHOR>
 * @date 2021/10/27 2:54 下午
 *//*

//@Service
@Slf4j
public class PluginService {

    private final PluginRepository pluginRepository;

    private final PluginCompilerService pluginCompilerService;

    private final Map<String, Plugin> pluginMap = new ConcurrentHashMap<>();

    public PluginService(PluginRepository pluginRepository,
                         PluginCompilerService pluginCompilerService) {
        this.pluginRepository = pluginRepository;
        this.pluginCompilerService = pluginCompilerService;
    }

    public List<Plugin> findAll(String type) {
        List<Plugin> plugins = pluginRepository.getAll(type);
        if (plugins == null) {
            return Collections.emptyList();
        }
        return plugins.stream().map(plugin -> {
                    String key = plugin.getId() + "#" + plugin.getVersion();
                    Plugin cache = pluginMap.get(key);
                    if (cache == null) {
                        try {
                            plugin.setPlugin(pluginCompilerService.compile(plugin));
                        } catch (PluginCompilerException e) {
                            log.error("compile plugin error, pluginId:[{}]", plugin.getId(), e);
                            return null;
                        }
                        pluginMap.put(key, plugin);
                        return plugin;
                    } else {
                        return cache;
                    }
                }).filter(plugin -> plugin != null)
                .collect(Collectors.toList());
    }
}
*/
