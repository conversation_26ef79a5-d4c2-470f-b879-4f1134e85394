package com.quanzhi.auditapiv2.api.service;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.nacos.api.exception.NacosException;
import com.quanzhi.audit_core.common.model.DataLabel;
import com.quanzhi.auditapiv2.common.util.dto.ListOutputDto;
import com.quanzhi.auditapiv2.common.util.utils.DataUtil;
import com.quanzhi.auditapiv2.core.service.manager.web.IDataLabelService;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @Auther: yangzixian
 * @Date: 2021/11/26 15:58
 * @Description:
 */
@Service
public class DataLabelOpenService {

    private final IDataLabelService dataLabelService;

    public DataLabelOpenService(IDataLabelService dataLabelService) {
        this.dataLabelService = dataLabelService;
    }

    /**
     * 分页获取标签信息
     * @param page
     * @param limit
     * @param accuracy
     * @return
     */
    public ListOutputDto<DataLabel> getListByPage(Integer page, Integer limit, String accuracy) {
        return dataLabelService.getListByPage(page, limit, accuracy);
    }

    public ListOutputDto<DataLabel> list() {
        List<DataLabel> all = dataLabelService.getAll();
        if (CollectionUtils.isEmpty(all)) {
            return new ListOutputDto<>();
        }
        List<DataLabel> collect = all.stream().map(dataLabel -> {
            DataLabel label = new DataLabel();
            label.setId(dataLabel.getId());
            label.setName(dataLabel.getName());
            label.setLocations(dataLabel.getLocations());
            return label;
        }).collect(Collectors.toList());

        return  new ListOutputDto<>(collect.size(), collect);
    }

    public List<String> listId(){
        List<DataLabel> all = dataLabelService.getAll();
        if (CollectionUtils.isEmpty(all)) {
            return new ArrayList<>();
        }
        return all.stream().map(DataLabel::getId).collect(Collectors.toList());
    }

    public Object transfer(Object result, String type, Map<String, String> fieldName) throws Exception {
        Map<String, String> dataLabelMap = dataLabelService.getDataLabeMap();
        if(result instanceof HashMap){
            if(((HashMap) result).containsKey("rows")){
                for(Object obj:(List)((HashMap) result).get("rows")){
                    setField(obj,type,fieldName,dataLabelMap);
                }
            }
        }else if(result instanceof JSONObject){
            if(((JSONObject) result).containsKey("rows")){
                for(Object obj:(List)((JSONObject) result).get("rows")){
                    setField(obj,type,fieldName,dataLabelMap);
                }
            } else{
                setField(result,type,fieldName,dataLabelMap);
            }
        }
        return result;
    }

    private void setField(Object obj,String type,Map<String, String> fieldName, Map<String, String> dataLabelMap) throws Exception{
        for (String srcFieldName : fieldName.keySet()) {
            Object object = null;
            if(DataUtil.isNotEmpty(type)){
                object = getDataLabels(obj, srcFieldName,type,dataLabelMap);
            }else{
                object = getDataLabels(obj,srcFieldName,"map",dataLabelMap);
            }
            if(DataUtil.isNotEmpty(object)){
                //把旧的字段删除
                ((JSONObject)obj).remove(srcFieldName);
                ((JSONObject) obj).put(fieldName.get(srcFieldName),object);
            }
        }
    }


    private Object getDataLabels(Object result, String srcFieldName,String type, Map<String, String> dataLabelMap) throws NacosException {
        if(result instanceof JSONObject){
            if(DataUtil.isNotEmpty(((JSONObject) result).get(srcFieldName))){
                List list=new ArrayList();
                if(((JSONObject) result).get(srcFieldName) instanceof List){

                    if(type.equals("string")){
                        for(Object obj:(List)((JSONObject) result).get(srcFieldName)){
                            String labelName = dataLabelMap.get(obj);
                            if(DataUtil.isNotEmpty(labelName)){
                                list.add(labelName);
                            }
                        }
                    } else if(type.equals("map")){
                        for(Object obj:(List)((JSONObject) result).get(srcFieldName)){
                            JSONObject jsonObject=new JSONObject();
                            jsonObject.put("dataLabelId",obj);
                            String labelName = dataLabelMap.get(obj);
                            if(DataUtil.isNotEmpty(labelName)){
                                jsonObject.put("dataLabelName",labelName);
                                list.add(jsonObject);
                            }

                        }
                    }

                }
                return list;
            }
        }
        return null;
    }
}
