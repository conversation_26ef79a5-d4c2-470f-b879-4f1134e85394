package com.quanzhi.auditapiv2.api.service;

import com.alibaba.fastjson.JSONObject;
import com.quanzhi.audit_core.common.model.DataLabel;
import com.quanzhi.auditapiv2.api.dao.FileInfoDao;
import com.quanzhi.auditapiv2.common.dal.dao.IDataLabelNacosDao;
import com.quanzhi.auditapiv2.common.util.dto.ListOutputDto;
import com.quanzhi.auditapiv2.common.util.utils.DataUtil;
import com.quanzhi.auditapiv2.openapi.sdk.dto.Pageable;
import com.quanzhi.auditapiv2.openapi.sdk.dto.openapi.FileInfoDTOV3;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;

@Service
public class FileOpenService {

    private final FileInfoDao fileInfoDao;

    private final IDataLabelNacosDao dataLabelNacosDao;

    public FileOpenService(FileInfoDao fileInfoDao, IDataLabelNacosDao dataLabelNacosDao) {
        this.fileInfoDao = fileInfoDao;
        this.dataLabelNacosDao = dataLabelNacosDao;
    }

    public ListOutputDto<FileInfoDTOV3> getFileInfoList(Pageable pageable){
        ListOutputDto<FileInfoDTOV3> listOutputDto = new ListOutputDto<>();
        Query query = new Query();
        listOutputDto.setTotalCount(fileInfoDao.getFileCount(query));
        // 再加分页 排序
        query.with(Sort.by(pageable.getSort() == null || pageable.getSort().intValue() == 2 ? Sort.Direction.DESC : Sort.Direction.ASC,pageable.getField() == null ? "downloadCnt" : pageable.getField()));
        query.skip((pageable.getPage() - 1) * pageable.getSize());
        query.limit(pageable.getSize());
        List<Map> fileList = fileInfoDao.getFileList(query);
        List<FileInfoDTOV3> fileInfoDTOV3List = new ArrayList<>();
        for(Map map:fileList){
            FileInfoDTOV3 fileInfoDTOV3 = new FileInfoDTOV3();
            fileInfoDTOV3.setFileName(map.get("fileName") != null ? map.get("fileName").toString() : "");
            fileInfoDTOV3.setFileType(map.get("fileType").toString());
            fileInfoDTOV3.setFileLen(Long.parseLong(map.get("fileLen").toString()));
            fileInfoDTOV3.setUploadCnt(Long.parseLong(map.get("uploadCnt").toString()));
            fileInfoDTOV3.setFileLevel(map.get("fileLevel") != null ? map.get("fileLevel").toString() : "");
            fileInfoDTOV3.setDownloadCnt(Long.parseLong(map.get("downloadCnt").toString()));
            fileInfoDTOV3.setDataDistinctCnt(Long.parseLong(map.get("dataDistinctCnt").toString()));
            fileInfoDTOV3.setDataDistinctCntByLabel(getDataDistinctCntByLabel(map.get("dataDistinctCntByLabel")));
            fileInfoDTOV3.setTimestamp(Long.parseLong(map.get("timestamp").toString()));
            fileInfoDTOV3.setUpdateTime(Long.parseLong(map.get("updateTime").toString()));
            fileInfoDTOV3List.add(fileInfoDTOV3);
        }
        listOutputDto.setRows(fileInfoDTOV3List);
        return listOutputDto;
    }

    private List<JSONObject> getDataDistinctCntByLabel(Object map){
        List<DataLabel> dataLabels = dataLabelNacosDao.getAll();
        if (dataLabels == null) {
            dataLabels = new ArrayList<>();
        }
        List<JSONObject> result = new ArrayList<>();
        if(DataUtil.isNotEmpty(map)){
            for(Map.Entry entry : ((Map<Object,Object>)map).entrySet()){
                JSONObject jsonObject = new JSONObject();
                Object key = entry.getKey();
                String dataLabel = getDataLabel(dataLabels, key.toString());
                jsonObject.put("dataLabelName",dataLabel);
                jsonObject.put("dataDistinctCnt",Long.parseLong(entry.getValue().toString()));
                result.add(jsonObject);
            }
        }
        return result;
    }

    private String getDataLabel(List<DataLabel> dataLabels, String label) {
        Optional<DataLabel> dataLabelOp = dataLabels.stream().filter(dataLabel -> dataLabel.getId() != null
                && dataLabel.getId().equals(label)).findAny();
        return dataLabelOp.isPresent() ? dataLabelOp.get().getName() : null;
    }
}
