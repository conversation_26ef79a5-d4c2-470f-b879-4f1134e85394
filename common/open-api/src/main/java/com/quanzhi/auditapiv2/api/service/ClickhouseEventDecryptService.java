package com.quanzhi.auditapiv2.api.service;

import com.alibaba.fastjson.JSONObject;
import com.quanzhi.audit.crypt.sdk.QzCrypt;
import com.quanzhi.audit.crypt.sdk.enums.EncryModelEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * <AUTHOR>
 * @date 2021/2/24 上午11:02
 */
@Service
@Slf4j
public class ClickhouseEventDecryptService{
    private static final String cryptoServer = "nacos-server:8848";
    private static final String encryEnum = "CLIENT";
    private boolean hasInit = false;
    private void initQzCrypt() {
        if(!hasInit) {
            try {
                QzCrypt.init(cryptoServer);
                hasInit = true;
            } catch (Exception e) {
                log.error("initQzCrypt error",e);
            }
        }
    }

    /**
     * 批量解密
     * @param sourceList
     * @return
     */
    public List<String> decrypt(List<String> sourceList) {

        initQzCrypt();

        if (QzCrypt.isNeedCrypt()) {
            List<String> content = QzCrypt.decrypt(EncryModelEnum.valueOf(encryEnum),sourceList);
            return content;
        } else {
            return sourceList;
        }
    }

    public void decryptList(List<JSONObject> list){
        initQzCrypt();
        list.stream().forEach(row->{
            if(row.getJSONObject("evidenceInfo") != null && row.getJSONObject("evidenceInfo").getJSONArray("extractValues") != null) {

            List<Object> extractValues = row.getJSONObject("evidenceInfo").getJSONArray("extractValues").toJavaList(Object.class);

                if( extractValues != null ) {

                    extractValues.forEach(extractValue ->{

                        if(((JSONObject)extractValue).getJSONObject("labelValues") != null) {

                            Set<String> totalValues = new HashSet<>();
                            ((JSONObject)extractValue).getJSONObject("labelValues").keySet().stream().forEach(key ->{
                                List<String> values = ((JSONObject) extractValue).getJSONObject("labelValues").getJSONArray(key).toJavaList(String.class);
                                List<String> decryptValues = decrypt(values);
                                Set<String> decryptValuesSet = new HashSet<>(decryptValues);
                                ((JSONObject) extractValue).getJSONObject("labelValues").put(key,decryptValuesSet);
                                totalValues.addAll(decryptValuesSet);
                            });
                            ((JSONObject) extractValue).put("values",totalValues);
                        }
                    });
                }
            }
        });
    }

    public Object decryptEvent(Object result){
        List<JSONObject> list = JSONObject.parseArray(((Map<String,Object>)result).get("rows").toString(),JSONObject.class);
        decryptList(list);
        return list;
    }
}
