package com.quanzhi.auditapiv2.api.service;

import com.quanzhi.auditapiv2.api.converter.ApiweaknessLogConverter;
import com.quanzhi.auditapiv2.common.dal.entity.ApiWeaknessLog;
import com.quanzhi.auditapiv2.core.service.manager.web.IApiWeaknessLogService;
import com.quanzhi.auditapiv2.openapi.sdk.dto.weakness.ApiWeaknessLogDTO;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * @Auther: yangzixian
 * @Date: 2021/11/26 16:03
 * @Description:
 */
@Service
public class WeaknessLogOpenService {

    private final IApiWeaknessLogService apiWeaknessLogService;

    public WeaknessLogOpenService(IApiWeaknessLogService apiWeaknessLogService) {
        this.apiWeaknessLogService = apiWeaknessLogService;
    }

    /**
     * 获取弱点操作日志
     *
     * @param apiWeaknessId
     * @return
     * @throws Exception
     */
    public List<ApiWeaknessLogDTO> getApiWeaknessLogList(String apiWeaknessId) throws Exception {
        List<ApiWeaknessLog> apiWeaknessLogList = apiWeaknessLogService.getApiWeaknessLogList(apiWeaknessId);
        List<ApiWeaknessLogDTO> apiWeaknessLogDTOS = new ArrayList<>();
        for (ApiWeaknessLog apiWeaknessLog : apiWeaknessLogList) {
            apiWeaknessLogDTOS.add(ApiweaknessLogConverter.INSTANCE.convert(apiWeaknessLog));
        }
        return apiWeaknessLogDTOS;
    }

}
