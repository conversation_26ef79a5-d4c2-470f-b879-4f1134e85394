package com.quanzhi.auditapiv2.api.converter;

import com.quanzhi.auditapiv2.core.risk.dto.RiskInfoDto;
import com.quanzhi.auditapiv2.core.risk.dto.RiskV2Dto;
import com.quanzhi.auditapiv2.openapi.sdk.dto.openapi.RiskDTOV3;
import com.quanzhi.auditapiv2.openapi.sdk.dto.risk.RiskDTO;
import com.quanzhi.auditapiv2.openapi.sdk.dto.risk.RiskOpenDto;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;
import org.mapstruct.factory.Mappers;

@Mapper
public interface RiskConverter {
    RiskConverter INSTANCE = Mappers.getMapper(RiskConverter.class);

    @Mappings({
            @Mapping(target = "sourceRiskId", source = "id")
    })
    RiskDTO convert(RiskInfoDto riskInfoDto);

    RiskOpenDto convertOpen(RiskInfoDto riskInfoDto);

    @Mappings({
            @Mapping(target = "name", source = "policySnapshot.name"),
            @Mapping(target = "type", source = "policySnapshot.group"),
            @Mapping(target = "rspLabelList", source = "rspLabelListValue")
    })
    RiskDTOV3 convertOpenV3(RiskInfoDto riskInfoDto);

    @Mappings({
            @Mapping(target = "riskDesc", source = "desc")
    })
    RiskDTOV3 convertOpenV4(RiskV2Dto riskV2Dto);

}
