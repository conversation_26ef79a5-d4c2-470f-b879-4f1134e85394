package com.quanzhi.auditapiv2.api.service;

import com.quanzhi.audit_core.common.model.*;
import com.quanzhi.auditapiv2.common.util.utils.DataUtil;
import com.quanzhi.auditapiv2.core.model.UaTypeDto;
import com.quanzhi.auditapiv2.core.service.manager.web.*;
import com.quanzhi.auditapiv2.openapi.sdk.dto.openapi.query.DataLabelCreateDTOV3;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/9/8 11:26 上午
 */
@Service
public class LabelConfigOpenService {

    private final IDataLabelService dataLabelService;

    private final IUaTypeService uaTypeService;

    private final INetworkSegmentService networkSegmentService;

    private final IFeatureLabelService featureLabelService;

    private final IApiClassifierService apiClassifierService;

    private final IAppFeatureLabelService appFeatureLabelService;

    public LabelConfigOpenService(IDataLabelService dataLabelService,
                                  IUaTypeService uaTypeService,
                                  INetworkSegmentService networkSegmentService, IFeatureLabelService featureLabelService, IApiClassifierService apiClassifierService, IAppFeatureLabelService appFeatureLabelService) {
        this.dataLabelService = dataLabelService;
        this.uaTypeService = uaTypeService;
        this.networkSegmentService = networkSegmentService;
        this.featureLabelService = featureLabelService;
        this.apiClassifierService = apiClassifierService;
        this.appFeatureLabelService = appFeatureLabelService;
    }

    public List<DataLabel> listDataLabels() {
        return dataLabelService.getAll();
    }


    public List<UaTypeDto> listUaTypes() {
        return uaTypeService.getAllUaTypeDto();
    }

    public List<NetworkSegment> listNetworkSegment(){
        return networkSegmentService.getAll();
    }

    public List<FeatureLabel> listApiFeatureLabels(){
        return featureLabelService.getAll();
    }
    public List<ApiClassification> listApiClassifiers(){
        return apiClassifierService.getAll();
    }
    public List<AppFeatureLabel> listAppFeatureLabels(){
        return appFeatureLabelService.getAll();
    }
    public void createDataLabel(DataLabelCreateDTOV3 dataLabelCreateDTOV3){
        DataLabel dataLabel = new DataLabel();
        dataLabel.setDelFlag(0);
        dataLabel.setType(DataLabel.TypeEnum.CUSTOM.val());
        // 名称
        dataLabel.setName(dataLabelCreateDTOV3.getName().trim());
        dataLabel.setFirstClass("unclassified");
        dataLabel.setLevel(dataLabelCreateDTOV3.getLevel());
        List<String> locations = new ArrayList<>();
        locations.add("POST");
        locations.add("GET");
        locations.add("BODY");
        dataLabel.setLocations(locations);
        dataLabel.setEnabled(dataLabelCreateDTOV3.getDiscoverEnable() == null ? true : dataLabelCreateDTOV3.getDiscoverEnable());
        dataLabel.setCanExtract(dataLabelCreateDTOV3.getStoreEnable() == null ? false : dataLabelCreateDTOV3.getStoreEnable());
        DLPPolicy dlpPolicy = new DLPPolicy();
        DLPPolicy.Rule rule = new DLPPolicy.Rule();
        // 正则
        DLPPolicy.Rule.Regex regex = new DLPPolicy.Rule.Regex();
        List<String> regexFlags = new ArrayList<>();
        regexFlags.add("caseless");
        regexFlags.add("find");
        regex.setFlags(regexFlags);
        regex.setRange("all");
        List<String> patterns = new ArrayList<>();
        patterns.add(dataLabelCreateDTOV3.getRegex());
        regex.setPatterns(patterns);
        if(DataUtil.isNotEmpty(dataLabelCreateDTOV3.getRegex())){
            rule.setRegex(regex);
        }
        // 词组
        DLPPolicy.Rule.Dict dict = new DLPPolicy.Rule.Dict();
        dict.setRange("all");
        dict.setValues(dataLabelCreateDTOV3.getDictValues());
        List<String> dictFlags = new ArrayList<>();
        dictFlags.add("caseless");
        dictFlags.add("contains");
        dict.setFlags(dictFlags);
        if(DataUtil.isNotEmpty(dataLabelCreateDTOV3.getDictValues())){
            rule.setDict(dict);
        }
        dlpPolicy.setDetect(rule);
        dataLabel.setDlpPolicy(dlpPolicy);
        dataLabel.setCreateTime(System.currentTimeMillis());
        dataLabel.setUpdateTime(System.currentTimeMillis());
        dataLabelService.saveData(dataLabel);
    }
}
