package com.quanzhi.auditapiv2.api.domain.risk;

import com.quanzhi.auditapiv2.core.risk.dto.RiskInfoDto;
import com.quanzhi.auditapiv2.openapi.sdk.dto.risk.Entity;
import com.quanzhi.metabase.core.model.http.HttpApiSample;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * @author: yangzixian
 * @date: 2023.10.11 11:00
 * @description:
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class RiskSampleDto {

    private String primary;

    private Long eventTime;

    private String ip;

    private String srcIp;

    private String dstIp;

    private String account;

    private String apiUrl;

    private List<String> apiTypes;

    private String apiLevel;

    private String appUri;

    private String host;

    private String reqContentType;

    private String method;

    private String terminal;

    private String ua;

    private String referer;

    private String rspContentType;

    private Long rspLength;

    private String rspStatusCode;

    private List<String> reqDataLabelIds;

    private List<String> rspDataLabelIds;

    private HttpApiSample.HttpRequest req;

    private HttpApiSample.HttpResponse rsp;

}
