package com.quanzhi.auditapiv2.api.dto;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class MonitorLogDTO implements Serializable {

    private String primary;

    private Long eventTime;

    private String srcIp;

    private String dstIp;

    private String account;

    private String apiUrl;

    private List<String> apiTypes;

    private String apiLevel;

    private String appUri;

    private String host;

    private String reqContentType;

    private String method;

    private String terminal;

    private String ua;

    private String referer;

    private String rspContentType;

    private Long rspLength;

    private String rspStatusCode;

    private List<String> reqDataLabelIds;

    private List<String> rspDataLabelIds;
}
