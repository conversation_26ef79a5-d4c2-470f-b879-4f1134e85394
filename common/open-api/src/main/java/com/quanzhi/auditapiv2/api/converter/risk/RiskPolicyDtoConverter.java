package com.quanzhi.auditapiv2.api.converter.risk;

import com.quanzhi.auditapiv2.api.domain.risk.RiskPolicyDto;
import com.quanzhi.auditapiv2.api.domain.risk.RiskPolicyOpenSearchDto;
import com.quanzhi.auditapiv2.core.risk.entity.RiskPolicy;
import com.quanzhi.auditapiv2.core.risk.entity.RiskPolicySearchDto;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

/**
 * @author: yangzixian
 * @date: 2023.10.11 09:57
 * @description:
 */
@Mapper
public interface RiskPolicyDtoConverter {

    RiskPolicyDtoConverter INSTANCE = Mappers.getMapper(RiskPolicyDtoConverter.class);

    @Mapping(source = "eventRule", target = "entityRule")
    @Mapping(source = "eventRuleHash", target = "entityRuleHash")
    @Mapping(source = "id", target = "_id")
    RiskPolicyDto convert(RiskPolicy riskPolicy);

}
