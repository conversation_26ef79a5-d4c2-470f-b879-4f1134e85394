package com.quanzhi.auditapiv2.api.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.nacos.api.config.annotation.NacosValue;
import com.quanzhi.audit.analysis.enums.JobType;
import com.quanzhi.audit.analysis.facade.TaskManagerFacade;
import com.quanzhi.auditapiv2.common.util.dto.ListOutputDto;
import com.quanzhi.auditapiv2.common.util.utils.DataUtil;
import com.quanzhi.auditapiv2.common.util.utils.DateUtil;
import com.quanzhi.auditapiv2.openapi.sdk.dto.Pageable;
import com.quanzhi.auditapiv2.openapi.sdk.dto.openapi.LogSearchDTOV3;
import com.quanzhi.operate.localMethodOperate.localMethodBuildIn.ILocalMethodTransform;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;

@Service
@Slf4j
public class LogSearchOpenService {

    private final TaskManagerFacade taskManagerFacade;

    private final ILocalMethodTransform localMethodTransform;

    public LogSearchOpenService(TaskManagerFacade taskManagerFacade, ILocalMethodTransform localMethodTransform) {
        this.taskManagerFacade = taskManagerFacade;
        this.localMethodTransform = localMethodTransform;
    }

    @NacosValue(value = "${clickhouse.query.ago.days:0}",autoRefreshed = true)
    private Long agoDays;

    public ListOutputDto<LogSearchDTOV3> getLogList(Pageable pageable){
        try {
            ListOutputDto<LogSearchDTOV3> listOutputDto = new ListOutputDto<>();
            LocalDate currentDate = LocalDate.now(ZoneId.of("Asia/Shanghai"));
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
            String formatCurrentDate = currentDate.format(formatter);
            LocalDate oneWeekAgo = currentDate.minusDays(agoDays);
            String formatOneWeekAgo = oneWeekAgo.format(formatter);
            long startTimestamp = DateUtil.getDateStartSeconds(formatOneWeekAgo);
            long endTimestamp = DateUtil.getDateEndSeconds(formatCurrentDate);
            String sort = pageable.getSort() == null || pageable.getSort().intValue() == 2 ? "DESC" : "ASC";
            int skip = (pageable.getPage() -1) * pageable.getSize();
            String countSql = "SELECT count(*) as totalCount,any(date) from future_http_defined_event WHERE timestamp >= " + startTimestamp + " AND timestamp <= " + endTimestamp;
            String querySql = "SELECT timestamp,net_srcIp_v4,net_srcIp_v6,net_srcPort,dstIpPosition_city,net_dstIp_v4,net_dstIp_v6,net_dstPort,originIpPosition_city,account,ip_v4,ip_v6,host,apiUrl,classifications,featureLabels,apiLevel,appName,appLevel,reqContentType,reqMethod,reqDataLabelIds,rspDataLabelIds,uaType,ua,referer,rspContentType,rspContentLength,rspStatus,appFeatureLabels FROM future_http_defined_event WHERE timestamp >= " + startTimestamp + " AND timestamp <= " + endTimestamp + " ORDER BY timestamp "  + sort + " LIMIT " + skip + "," + pageable.getSize();
            Long totalCount = getTotalCount(taskManagerFacade.query(countSql, JobType.AGGTASK));
            listOutputDto.setTotalCount(totalCount);
            if(totalCount.longValue() == 0){
                listOutputDto.setRows(new ArrayList<>());
                return listOutputDto;
            }
            List<Map> result = getResult(taskManagerFacade.query(querySql, JobType.QUERYTASK));
            List<LogSearchDTOV3> logSearchDTOV3List = new ArrayList<>();
            for (Map map : result) {
                LogSearchDTOV3 logSearchDTOV3 = new LogSearchDTOV3();
                logSearchDTOV3.setTimestamp(Long.parseLong(map.get("timestamp").toString()));
                JSONObject net = JSONObject.parseObject(JSON.toJSONString(map.get("net")),JSONObject.class);
                logSearchDTOV3.setSrcIp(net.get("srcIp").toString());
                logSearchDTOV3.setSrcPort(Integer.parseInt(net.get("srcPort").toString()));
                logSearchDTOV3.setDstIp(net.get("dstIp").toString());
                logSearchDTOV3.setDstPort(Integer.parseInt(net.get("dstPort").toString()));
                JSONObject originIpPosition = JSONObject.parseObject(JSON.toJSONString(map.get("originIpPosition")),JSONObject.class);
                logSearchDTOV3.setOriginIpPosition(originIpPosition.get("city").toString());
                JSONObject dstIpPosition = JSONObject.parseObject(JSON.toJSONString(map.get("dstIpPosition")),JSONObject.class);
                logSearchDTOV3.setDstIpPosition(dstIpPosition.get("city").toString());
                logSearchDTOV3.setProtocol("TCP");
                logSearchDTOV3.setAccount(map.get("account").toString());
                logSearchDTOV3.setApiUrl(map.get("apiUrl").toString());
                Object apiFeatureLabels = map.get("apiFeatureLabels");
                if(DataUtil.isNotEmpty(apiFeatureLabels)){
                    logSearchDTOV3.setApiFeatureLabels(Arrays.asList(localMethodTransform.transformApiFeatureLabelId2name(apiFeatureLabels).split(",")));
                } else{
                    logSearchDTOV3.setApiFeatureLabels(new ArrayList<>());
                }
                logSearchDTOV3.setApiLevel(map.get("apiLevel").toString());
                logSearchDTOV3.setHost(map.get("host").toString());
                logSearchDTOV3.setAppLevel(map.get("appLevel").toString());
                logSearchDTOV3.setAppName(map.get("appName").toString());
                logSearchDTOV3.setReqContentType(map.get("reqContentType").toString());
                logSearchDTOV3.setReqMethod(map.get("reqMethod").toString());
                logSearchDTOV3.setUaType(map.get("uaType").toString());
                logSearchDTOV3.setUa(map.get("ua").toString());
                logSearchDTOV3.setReferer(map.get("referer").toString());
                logSearchDTOV3.setRspContentType(map.get("rspContentType").toString());
                logSearchDTOV3.setRspStatus(map.get("rspStatus").toString());
                if(DataUtil.isNotEmpty(map.get("reqDataLabelIds"))){
                    logSearchDTOV3.setReqDataLabels(Arrays.asList(localMethodTransform.transformDataLabelId2name(map.get("reqDataLabelIds")).split(",")));
                } else{
                    logSearchDTOV3.setReqDataLabels(new ArrayList<>());
                }
                if(DataUtil.isNotEmpty(map.get("rspDataLabelIds"))){
                    logSearchDTOV3.setRspDataLabels(Arrays.asList(localMethodTransform.transformDataLabelId2name(map.get("rspDataLabelIds")).split(",")));
                } else{
                    logSearchDTOV3.setRspDataLabels(new ArrayList<>());
                }
                if(DataUtil.isNotEmpty(map.get("appFeatureLabels"))){
                    logSearchDTOV3.setAppFeatureLabels(Arrays.asList(localMethodTransform.transformAppFeatureLabelId2name(map.get("appFeatureLabels")).split(",")));
                } else{
                    logSearchDTOV3.setAppFeatureLabels(new ArrayList<>());
                }
                logSearchDTOV3List.add(logSearchDTOV3);
            }
            listOutputDto.setRows(logSearchDTOV3List);
            return listOutputDto;
        }catch (Exception e){
            log.error("clickhouse event query error",e);
        }
        return null;
    }

    private List<Map> getResult(String rsp){
        JSONObject rspObj = JSON.parseObject(rsp);
        List<Map> maps = JSONObject.parseArray(rspObj.getString("data"), Map.class);
        return maps;
    }

    private Long getTotalCount(String rsp){
        List<Map> result = getResult(rsp);
        for (Map map : result) {
            return Long.parseLong(map.get("totalCount").toString().trim());
        }
        return 0L;
    }
}
