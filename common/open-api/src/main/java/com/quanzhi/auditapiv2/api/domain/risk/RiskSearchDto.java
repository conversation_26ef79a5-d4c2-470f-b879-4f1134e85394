package com.quanzhi.auditapiv2.api.domain.risk;

import com.quanzhi.auditapiv2.core.risk.dto.common.CommonSearchDto;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * @author: yangzixian
 * @date: 2023.10.11 10:37
 * @description:
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class RiskSearchDto extends CommonSearchDto {

    @ApiModelProperty(value = "最近活跃时间", name = "timestamp")
    private List<Long> timestamp;

    @ApiModelProperty(value = "风险ID", name = "riskId")
    private String riskId;

    @ApiModelProperty(value = "id", name = "id")
    private String id;

}
