package com.quanzhi.auditapiv2.api.service;

import com.quanzhi.audit_core.common.model.ApiClassification;
import com.quanzhi.audit_core.common.model.DataLabel;
import com.quanzhi.audit_core.common.model.FeatureLabel;
import com.quanzhi.audit_core.common.model.WeaknessRule;
import com.quanzhi.auditapiv2.api.converter.HttpApiConverter;
import com.quanzhi.auditapiv2.api.converter.WeaknessConverter;
import com.quanzhi.auditapiv2.common.dal.dao.IApiClassifierDao;
import com.quanzhi.auditapiv2.common.dal.dao.IDataLabelNacosDao;
import com.quanzhi.auditapiv2.common.dal.dao.IFeatureLabelDao;
import com.quanzhi.auditapiv2.common.dal.dto.ApiWeaknessDto;
import com.quanzhi.auditapiv2.common.dal.dto.weakness.WeaknessSuggestionDto;
import com.quanzhi.auditapiv2.common.dal.enums.WeaknessLevelEnum;
import com.quanzhi.auditapiv2.common.util.dto.ListOutputDto;
import com.quanzhi.auditapiv2.common.util.utils.DataUtil;
import com.quanzhi.auditapiv2.common.util.utils.MD5Util;
import com.quanzhi.auditapiv2.core.service.NacosDataServiceBuilder;
import com.quanzhi.auditapiv2.core.service.manager.dto.HttpApiDto;
import com.quanzhi.auditapiv2.core.service.manager.dto.SampleEventDto;
import com.quanzhi.auditapiv2.core.service.manager.dto.weakness.WeaknessStatistics;
import com.quanzhi.auditapiv2.core.service.manager.web.IApiWeaknessService;
import com.quanzhi.auditapiv2.core.service.manager.web.IDataLabelService;
import com.quanzhi.auditapiv2.core.service.manager.web.IHttpApiService;
import com.quanzhi.auditapiv2.core.service.manager.web.IWeaknessRuleService;
import com.quanzhi.auditapiv2.core.service.manager.web.api.ApiTransformService;
import com.quanzhi.auditapiv2.core.service.manager.web.weakness.WeaknessOperationService;
import com.quanzhi.auditapiv2.openapi.sdk.dto.HttpApiDTO;
import com.quanzhi.auditapiv2.openapi.sdk.dto.Pageable;
import com.quanzhi.auditapiv2.openapi.sdk.dto.TimestampPageable;
import com.quanzhi.auditapiv2.openapi.sdk.dto.openapi.WeaknessDTOV3;
import com.quanzhi.auditapiv2.openapi.sdk.dto.weakness.WeaknessDTO;
import com.quanzhi.auditapiv2.openapi.sdk.dto.weakness.WeaknessEventDTO;
import com.quanzhi.metabase.core.model.http.HttpApiResource;
import com.quanzhi.metabase.core.model.http.HttpApiSample;
import com.quanzhi.metabase.core.model.http.HttpAppResource;
import com.quanzhi.metabase.core.model.http.Pair;
import com.quanzhi.metabase.core.model.http.api.BriefApi;
import com.quanzhi.metabase.core.model.http.weakness.ApiWeakness;
import com.quanzhi.metabase.core.model.http.weakness.Proof;
import com.quanzhi.metabase.core.model.http.weakness.Sample;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @Author: HaoJun
 * @Date: 2021/8/18 3:33 下午
 */
@Service
@Slf4j
public class WeaknessOpenService {

    private final IApiWeaknessService apiWeaknessService;

    private final IWeaknessRuleService weaknessRuleService;

    private final IHttpApiService apiService;

    private final ApiTransformService apiTransformService;

    private final WeaknessOperationService weaknessOperationService;

    private final IDataLabelService dataLabelService;

    private final IDataLabelNacosDao dataLabelNacosDao;

    private final IFeatureLabelDao featureLabelDao;

    private final IApiClassifierDao apiClassifierDao;

    @Resource
    private NacosDataServiceBuilder nacosDadaServiceBuilder;

    public WeaknessOpenService(IApiWeaknessService apiWeaknessService,
                               IWeaknessRuleService weaknessRuleService,
                               ApiTransformService apiTransformService,
                               IHttpApiService apiService,
                               WeaknessOperationService weaknessOperationService,
                               IDataLabelService dataLabelService, IDataLabelNacosDao dataLabelNacosDao, IFeatureLabelDao featureLabelDao, IApiClassifierDao apiClassifierDao) {
        this.apiWeaknessService = apiWeaknessService;
        this.weaknessRuleService = weaknessRuleService;
        this.apiTransformService = apiTransformService;
        this.apiService = apiService;
        this.weaknessOperationService = weaknessOperationService;
        this.dataLabelService = dataLabelService;
        this.dataLabelNacosDao = dataLabelNacosDao;
        this.featureLabelDao = featureLabelDao;
        this.apiClassifierDao = apiClassifierDao;
    }

    public Boolean checkWeaknessStatus(String id) {
        return apiWeaknessService.checkApiWeaknessStatus(id);
    }

    public void deal(String username, WeaknessSuggestionDto weaknessAddDto) throws Exception {
        weaknessOperationService.deal(username, weaknessAddDto);
    }

    /**
     * 根据id获取弱点详细信息
     *
     * @param id
     * @return
     * @throws Exception
     */
    public WeaknessDTO getApiWeaknessById(String id) throws Exception {
        WeaknessDTO weaknessDTO = WeaknessConverter.INSTANCE.convert(apiWeaknessService.getApiWeaknessById(id));
        this.fillApiWeaknessDto(weaknessDTO);
        return weaknessDTO;
    }

    /**
     * 编辑弱点信息
     *
     * @param apiWeakness
     * @return
     */
    public void editApiWeakness(ApiWeakness apiWeakness) throws Exception {
        apiWeaknessService.editApiWeakness(apiWeakness, true);
    }

    /**
     * 批量忽略
     *
     * @param ids
     */
    public void filter(String ids) {
        try {
            apiWeaknessService.filter(ids, "批量忽略");
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 根据ObjectId查找该id之后新增的弱点信息
     *
     * @param updateTime
     * @return
     */
    public List<WeaknessDTO> selectNewestWeaknessByUpdateTime(String updateTime) throws Exception {
        List<ApiWeaknessDto> apiWeaknessDtos = apiWeaknessService.selectNewestWeaknessByUpdateTime(updateTime);
        List<WeaknessDTO> data = new ArrayList<>(apiWeaknessDtos.size());
        apiWeaknessDtos.forEach(apiWeaknessDto -> {
            data.add(WeaknessConverter.INSTANCE.convert(apiWeaknessDto));
        });
//        ProduceData<List<WeaknessDTO>> produceData = new ProduceData<>();
//        if (DataUtil.isNotEmpty(data)) {
//            produceData.setData(data);
//            produceData.setIndex(String.valueOf(apiWeaknessDtos.get(0).getUpdateTime()));
//            if (apiWeaknessService.totalCountByUpdateTime(String.valueOf(apiWeaknessDtos.get(0).getUpdateTime())) > 0) {
//                produceData.setHasNext(true);
//            }
//        }
        return data;
    }

    public ListOutputDto<WeaknessDTO> getWeaknessList(Pageable pageable) throws Exception {
        ListOutputDto<ApiWeakness> weaknessDtoListOutputDto = getApiWeaknessList(pageable);
        if (weaknessDtoListOutputDto == null) {
            return new ListOutputDto<>();
        }
        ListOutputDto<WeaknessDTO> weaknessDTOListOutputDto
                = weaknessDtoListOutputDto.map(WeaknessConverter.INSTANCE::convert);
        for (WeaknessDTO weaknessDTO : weaknessDTOListOutputDto.getRows()) {
            WeaknessRule weaknessRule = weaknessRuleService.getWeaknessRuleById(weaknessDTO.getWeaknessId());
            if (weaknessRule != null) {
                weaknessDTO.setDescription(weaknessRule.getDescription());
                weaknessDTO.setSolution(weaknessRule.getSolution());
            }
            weaknessDTO.setStateName(weaknessDTO.getState().value());
            weaknessDTO.setLevelName(WeaknessLevelEnum.getName(weaknessDTO.getLevel()));
            HttpApiResource apiResource = apiService.getHttpApiByUri(weaknessDTO.getApiUri());
            if (DataUtil.isNotEmpty(apiResource)) {
                HttpApiDto httpApiDto = apiTransformService.transform(apiResource);
                weaknessDTO.setApi(HttpApiConverter.INSTANCE.convert(httpApiDto));
            } else {
                weaknessDTO.setApi(null);
            }

            this.fillApiWeaknessDto(weaknessDTO);
        }
        return weaknessDTOListOutputDto;

    }

    public ListOutputDto<WeaknessDTOV3> getWeaknessListV3(Pageable pageable) throws Exception {
        ListOutputDto<ApiWeakness> weaknessDtoListOutputDto = getApiWeaknessList(pageable);
        if (weaknessDtoListOutputDto == null) {
            return new ListOutputDto<>();
        }
        ListOutputDto<WeaknessDTOV3> weaknessDTOV3ListOutputDto = new ListOutputDto<>();
        List<WeaknessDTOV3> weaknessDTOV3List = new ArrayList<>();
        Map<String, DataLabel> dataLabelMap = nacosDadaServiceBuilder.build().getDataLabelMap();
        Map<String, FeatureLabel> featureLabelMap = nacosDadaServiceBuilder.build().getFeatureLabelMap();
        Map<String, String> apiClassificationMap = nacosDadaServiceBuilder.build().getApiClassifierMap();
        for (ApiWeakness apiWeakness : weaknessDtoListOutputDto.getRows()) {
            WeaknessDTOV3 weaknessDTOV3 = new WeaknessDTOV3();
            if (DataUtil.isNotEmpty(apiWeakness.getId())) {
                weaknessDTOV3.setId(apiWeakness.getId());
            }
            if (DataUtil.isNotEmpty(apiWeakness.getOperationId())) {
                weaknessDTOV3.setOperationId(apiWeakness.getOperationId());
            }
            if (DataUtil.isNotEmpty(apiWeakness.getApi().getApiUrl())) {
                weaknessDTOV3.setApiUrl(apiWeakness.getApi().getApiUrl());
            }
            if (DataUtil.isNotEmpty(apiWeakness.getLevel())) {
                weaknessDTOV3.setLevelName(WeaknessLevelEnum.getName(apiWeakness.getLevel()));
            }
            if (DataUtil.isNotEmpty(apiWeakness.getHost())) {
                weaknessDTOV3.setHost(apiWeakness.getHost());
            }
            if (DataUtil.isNotEmpty(apiWeakness.getName())) {
                weaknessDTOV3.setName(apiWeakness.getName());
            }
            WeaknessRule weaknessRule = weaknessRuleService.getWeaknessRuleById(apiWeakness.getWeaknessId());
            if (weaknessRule != null) {
                weaknessDTOV3.setTypeName(weaknessRule.getTypeName());
            }
            if (DataUtil.isNotEmpty(apiWeakness.getState())) {
                weaknessDTOV3.setStateName(apiWeakness.getState().value());
            }
            if (DataUtil.isNotEmpty(apiWeakness.getSuggestion())) {
                weaknessDTOV3.setSuggestion(apiWeakness.getSuggestion());
            } else {
                weaknessDTOV3.setSuggestion("");
            }
            if (DataUtil.isNotEmpty(apiWeakness.getApi().getAppName())) {
                weaknessDTOV3.setAppName(apiWeakness.getApi().getAppName());
            }
            if (!CollectionUtils.isEmpty(apiWeakness.getApi().getReqDataLabels())) {
                List<String> collect = apiWeakness.getApi().getReqDataLabels().stream()
                        .map(label -> dataLabelMap.get(label) == null ? null : dataLabelMap.get(label).getName())
                        .filter(Objects::nonNull)
                        .collect(Collectors.toList());
                weaknessDTOV3.setReqDataLabels(collect);
            } else {
                weaknessDTOV3.setReqDataLabels(new ArrayList<>());
            }

            if (!CollectionUtils.isEmpty(apiWeakness.getApi().getRspDataLabels())) {
                List<String> stringList = apiWeakness.getApi().getRspDataLabels().stream()
                        .map(label -> dataLabelMap.get(label) == null ? null : dataLabelMap.get(label).getName())
                        .filter(Objects::nonNull)
                        .collect(Collectors.toList());
                weaknessDTOV3.setRspDataLabels(stringList);
            } else {
                weaknessDTOV3.setRspDataLabels(new ArrayList<>());
            }
            List<String> featureLabelsValue = new ArrayList<>();
            if (!CollectionUtils.isEmpty(apiWeakness.getApi().getFeatureLabels())) {
                featureLabelsValue = apiWeakness.getApi().getFeatureLabels().stream()
                        .map(label -> featureLabelMap.get(label) == null ? null : featureLabelMap.get(label).getName())
                        .filter(Objects::nonNull)
                        .collect(Collectors.toList());
            }
            List<String> classificationsValue = new ArrayList<>();
            if (!CollectionUtils.isEmpty(apiWeakness.getApi().getClassifications())) {
                classificationsValue = apiWeakness.getApi().getClassifications().stream()
                        .map(label -> apiClassificationMap.get(label) == null ? null : apiClassificationMap.get(label))
                        .filter(Objects::nonNull)
                        .collect(Collectors.toList());
            }
            // API 标签
            List<String> apiFeatureLabels = new ArrayList<>();
            apiFeatureLabels.addAll(classificationsValue);
            apiFeatureLabels.addAll(featureLabelsValue);
            weaknessDTOV3.setApiFeatureLabelNames(apiFeatureLabels);
            if (DataUtil.isNotEmpty(apiWeakness.getApi().getMethods())) {
                weaknessDTOV3.setMethods(apiWeakness.getApi().getMethods());
            }
            if (DataUtil.isNotEmpty(apiWeakness.getApi().getMaxRspLabelValueCount())) {
                weaknessDTOV3.setMaxRspLabelValueCount(apiWeakness.getApi().getMaxRspLabelValueCount());
            }
            if (DataUtil.isNotEmpty(apiWeakness.getApi().getVisitDomains())) {
                weaknessDTOV3.setVisitDomains(apiWeakness.getApi().getVisitDomains());
            }
            if (DataUtil.isNotEmpty(apiWeakness.getApi().getDeployDomains())) {
                weaknessDTOV3.setDeployDomains(apiWeakness.getApi().getDeployDomains());
            }
            if (DataUtil.isNotEmpty(apiWeakness.getApi().getTerminals())) {
                weaknessDTOV3.setTerminals(apiWeakness.getApi().getTerminals());
            }
            if (DataUtil.isNotEmpty(apiWeakness.getApi().getRspContentTypes())) {
                weaknessDTOV3.setRspContentTypes(apiWeakness.getApi().getRspContentTypes());
            }
            fillProperties(apiWeakness.getApi(),weaknessDTOV3);
            if (DataUtil.isNotEmpty(apiWeakness.getEarlyTimestamp())) {
                weaknessDTOV3.setEarlyTimestamp(apiWeakness.getEarlyTimestamp());
            }
            if (DataUtil.isNotEmpty(apiWeakness.getLastTimestamp())) {
                weaknessDTOV3.setLastTimestamp(apiWeakness.getLastTimestamp());
            }
            weaknessDTOV3List.add(weaknessDTOV3);
        }
        weaknessDTOV3ListOutputDto.setTotalCount(weaknessDtoListOutputDto.getTotalCount());
        weaknessDTOV3ListOutputDto.setRows(weaknessDTOV3List);
        return weaknessDTOV3ListOutputDto;
    }

    private void fillProperties(BriefApi source, WeaknessDTOV3 target) {
        if (source == null || source.getDepartments() == null || source.getDepartments().isEmpty()){
            target.setCustomFields(new ArrayList<>());
            return;
        }
        List<Map<String, String>> result = new ArrayList<>();
        for (HttpAppResource.Department department : source.getDepartments()) {
            List<Pair<String>> props = department.getProperties();
            if (props != null) {
                for (Pair<String> pair : props) {
                    if (pair != null && pair.getKey() != null && pair.getValue() != null) {
                        Map<String, String> propMap = new HashMap<>();
                        propMap.put("key", pair.getKey());
                        propMap.put("value", pair.getValue());
                        result.add(propMap);
                    }
                }
            }
        }
        target.setCustomFields(result);
    }

    private String getDataLabel(List<DataLabel> dataLabels, String label) {
        Optional<DataLabel> dataLabelOp = dataLabels.stream().filter(dataLabel -> dataLabel.getId() != null
                && dataLabel.getId().equals(label)).findAny();
        return dataLabelOp.isPresent() ? dataLabelOp.get().getName() : null;
    }

    private String getFeatureLabel(List<FeatureLabel> featureLabels, String label) {
        Optional<FeatureLabel> featureLabelOp = featureLabels.stream()
                .filter(featureLabel -> featureLabel.getId() != null
                        && featureLabel.getId().equals(label)).findAny();
        return featureLabelOp.isPresent() ? featureLabelOp.get().getName() : null;
    }

    private String getClassification(List<ApiClassification> apiClassifications, String label) {
        Optional<ApiClassification> classificationOp = apiClassifications.stream().filter(classification -> classification.getId() != null
                && classification.getId().equals(label)).findAny();
        return classificationOp.isPresent() ? classificationOp.get().getName() : null;
    }

    private ListOutputDto<ApiWeakness> getApiWeaknessList(Pageable pageable) throws Exception {
        Map<String, Object> objectMap = ((TimestampPageable) pageable).generateTimeMap();
        List<String> stateList = new ArrayList<>();
        stateList.add("NEW");
        stateList.add("REPAIRING");
        stateList.add("FIXED");
        stateList.add("REOPEN");
        objectMap.put("state", stateList);
        ListOutputDto<ApiWeakness> weaknessDtoListOutputDto = apiWeaknessService.getSimpleApiWeaknessList(
                pageable instanceof TimestampPageable ? objectMap : Collections.emptyMap(),
                pageable.getField() == null ? "level" : pageable.getField(),
                pageable.getSort() == null ? 2 : pageable.getSort(),
                pageable.getPage(),
                pageable.getSize());
        return weaknessDtoListOutputDto;
    }

    /**
     * @param weaknessDTO
     * @return void
     * <AUTHOR>
     * @date 2022/11/3 4:05 PM
     * @Description 补充运营中心所需数据
     * @Since
     */
    private void fillApiWeaknessDto(WeaknessDTO weaknessDTO) {
        if (weaknessDTO == null) {
            return;
        }

        HttpApiDTO httpApiDTO = weaknessDTO.getApi();
        if (DataUtil.isNotEmpty(httpApiDTO)) {
            weaknessDTO.setApiUrl(httpApiDTO.getApiUrl());
            weaknessDTO.setApiTypes(httpApiDTO.getClassifications() == null ? new HashSet<>() : new HashSet<>(httpApiDTO.getClassifications()));
            weaknessDTO.setApiTypeIds(httpApiDTO.getClassificationIds() == null ? new HashSet<>() : new HashSet<>(httpApiDTO.getClassificationIds()));
            weaknessDTO.setMaxOnceRspDataDistinctCnt((httpApiDTO.getApiStat() == null || httpApiDTO.getApiStat().getMaxReqLabelValueCount() == null) ? 0 : httpApiDTO.getApiStat().getMaxReqLabelValueCount());
            weaknessDTO.setReqDataLabels(httpApiDTO.getReqDataLabelIds() == null ? new HashSet<>() : new HashSet<>(httpApiDTO.getReqDataLabelIds()));
            weaknessDTO.setReqDataLabelsValue(httpApiDTO.getReqDataLabels() == null ? new HashSet<>() : new HashSet<>(httpApiDTO.getReqDataLabels()));
            weaknessDTO.setRspDataLabels(httpApiDTO.getRspDataLabelIds() == null ? new HashSet<>() : new HashSet<>(httpApiDTO.getRspDataLabelIds()));
            weaknessDTO.setRspDataLabelsValue(httpApiDTO.getRspDataLabels() == null ? new HashSet<>() : new HashSet<>(httpApiDTO.getRspDataLabels()));
            weaknessDTO.setVisitNetSegments(httpApiDTO.getVisitDomains() == null ? new HashSet<>() : new HashSet<>(httpApiDTO.getVisitDomains()));
            weaknessDTO.setApiFeatureLabels(httpApiDTO.getFeatureLabelIds() == null ? new HashSet<>() : new HashSet<>(httpApiDTO.getFeatureLabelIds()));
            weaknessDTO.setApiFeatureLabelsValue(httpApiDTO.getFeatureLabels() == null ? new HashSet<>() : new HashSet<>(httpApiDTO.getFeatureLabels()));
            weaknessDTO.setUaTypes(httpApiDTO.getTerminals() == null ? new HashSet<>() : new HashSet<>(httpApiDTO.getTerminals()));
        }
        weaknessDTO.setWeaknessType(weaknessDTO.getType());
        weaknessDTO.setStatus(weaknessDTO.getStateName());
        weaknessDTO.setFindTimeMs(weaknessDTO.getEarlyTimestamp());
        weaknessDTO.setLastTimeMs(weaknessDTO.getLastTimestamp());
        weaknessDTO.setWeaknessLevel(weaknessDTO.getLevelName());
        weaknessDTO.setOperationId("WEAKNESS" + weaknessDTO.getFindTimeMs() + MD5Util.md5(weaknessDTO.getId()));
        if (DataUtil.isNotEmpty(weaknessDTO.getSamples())) {
            weaknessDTO.setSampleId(weaknessDTO.getSamples().get(weaknessDTO.getSamples().size() - 1).getSampleId());
        }

        // 弱点特征
        if (DataUtil.isNotEmpty(weaknessDTO.getSamples())) {
            // 需要特别处理这个字段，直接返回的不准确
            Sample sample = weaknessDTO.getSamples().get(0);
            List<Proof> proofs = sample.getProof();
            weaknessDTO.setFeature("--");

            if (DataUtil.isNotEmpty(proofs)) {
                List<String> features = new ArrayList<>();
                for (Proof proof : proofs) {
                    String feature = "";
                    if (DataUtil.isNotEmpty(proof.getValue())) {
                        feature = (DataUtil.isNotEmpty(proof.getName()) ? proof.getName() + ": " : "") + proof.getValue();
                    } else {
                        feature = proof.getName();
                    }
                    features.add(feature);
                }

                weaknessDTO.setFeature(String.join("; ", features));
            }
        }

        weaknessDTO.setDepartment("--");
        weaknessDTO.setAppName("--");

        // 详情需要的信息
        weaknessDTO.setWeaknessName(weaknessDTO.getName());
        weaknessDTO.setMaxOnceRspDataDistictCnt(weaknessDTO.getMaxOnceRspDataDistinctCnt() == null ? 0L : new Long(weaknessDTO.getMaxOnceRspDataDistinctCnt()));
//        weaknessDTO.setReq();
//        weaknessDTO.setRsp();
    }

    public List<SampleEventDto> listSamples(boolean desensitize, String id) {
        List<SampleEventDto> sampleEventDtos = apiWeaknessService.listSamples(desensitize, id,null);
        if (sampleEventDtos == null) {
            sampleEventDtos = Collections.emptyList();
        }
        sampleEventDtos.stream().forEach(sampleEventDto -> {
//            sampleEventDto.setSamples(null);
            sampleEventDto.setUpLabels(dataLabelService.convert(sampleEventDto.getUpLabels()).stream().map(d -> d.getName()).collect(Collectors.toSet()));
            sampleEventDto.setDownLabels(dataLabelService.convert(sampleEventDto.getDownLabels()).stream().map(d -> d.getName()).collect(Collectors.toSet()));
        });
        return sampleEventDtos;
    }

    public SampleEventDto listLastSamples(boolean desensitize, String id) {
        SampleEventDto sampleEventDto = apiWeaknessService.listLastSamples(desensitize, id);
        if (sampleEventDto == null) {
            sampleEventDto = new SampleEventDto(new HttpApiSample());
        }
        sampleEventDto.setUpLabels(dataLabelService.convert(sampleEventDto.getUpLabels()).stream().map(d -> d.getName()).collect(Collectors.toSet()));
        sampleEventDto.setDownLabels(dataLabelService.convert(sampleEventDto.getDownLabels()).stream().map(d -> d.getName()).collect(Collectors.toSet()));
        return sampleEventDto;
    }

    public WeaknessStatistics simpleStatistics() {
        return apiWeaknessService.simpleStatistics();
    }

    public ListOutputDto<WeaknessEventDTO> getWeaknessEventList(Map<String, Object> mapParam, String field, Integer sort, Integer page, Integer limit, Boolean flag) {
        ListOutputDto<WeaknessEventDTO> result = new ListOutputDto<>();
        List<WeaknessEventDTO> rows = new ArrayList<>();
        try {
            ListOutputDto<ApiWeaknessDto> apiWeaknessList
                    = apiWeaknessService.getApiWeaknessList(mapParam, field, sort, page, limit, flag);
            if (apiWeaknessList == null) {
                return result;
            }
            List<ApiWeaknessDto> apiWeaknessDtoList = apiWeaknessList.getRows();
            for (ApiWeaknessDto apiWeaknessDto : apiWeaknessDtoList) {
                WeaknessEventDTO weaknessEventDTO = new WeaknessEventDTO();
                weaknessEventDTO.setReqDataLabelsValue(apiWeaknessDto.getHttpApiDto().getReqDataLabelsValue() == null ?
                        new HashSet<>() : new HashSet<>(apiWeaknessDto.getHttpApiDto().getReqDataLabelsValue()));

                weaknessEventDTO.setApiFeatureLabelsValue(apiWeaknessDto.getHttpApiDto().getFeatureLabelsValue() == null ?
                        new HashSet<>() : new HashSet<>(apiWeaknessDto.getHttpApiDto().getFeatureLabelsValue()));

                weaknessEventDTO.setDescription(apiWeaknessDto.getDescription() == null ? "" : apiWeaknessDto.getDescription());

                weaknessEventDTO.setReqDataLabels(apiWeaknessDto.getHttpApiDto().getReqDataLabels() == null ?
                        new HashSet<>() : new HashSet<>(apiWeaknessDto.getHttpApiDto().getReqDataLabels()));

                weaknessEventDTO.setApiUri(apiWeaknessDto.getUri() == null ? "" : apiWeaknessDto.getUri());

                weaknessEventDTO.setApiUrl(apiWeaknessDto.getHttpApiDto().getApiUrl() == null ?
                        "" : apiWeaknessDto.getHttpApiDto().getApiUrl());

                weaknessEventDTO.setSolution(apiWeaknessDto.getSolution() == null ? "" : apiWeaknessDto.getSolution());

                converterSampleIdListAndFeature(weaknessEventDTO, apiWeaknessDto);

                weaknessEventDTO.setHost(apiWeaknessDto.getHost() == null ? "" : apiWeaknessDto.getHost());

                if (apiWeaknessDto.getHttpApiDto().getApiStat() != null) {
                    weaknessEventDTO.setMaxOnceRspDataDistinctCnt(apiWeaknessDto.getHttpApiDto().getApiStat().getMaxReqLabelValueCount() == null
                            ? 0 : apiWeaknessDto.getHttpApiDto().getApiStat().getMaxReqLabelValueCount());
                }

                weaknessEventDTO.setOperationId(apiWeaknessDto.getOperationId() == null ? "" : apiWeaknessDto.getOperationId());

                weaknessEventDTO.setApiTypeIds(apiWeaknessDto.getHttpApiDto().getClassifications() == null ?
                        new HashSet<>() : new HashSet<>(apiWeaknessDto.getHttpApiDto().getClassifications()));

                weaknessEventDTO.setUaTypes(apiWeaknessDto.getHttpApiDto().getTerminals() == null ?
                        new HashSet<>() : new HashSet<>(apiWeaknessDto.getHttpApiDto().getTerminals()));

                weaknessEventDTO.setId(apiWeaknessDto.getId() == null ? "" : apiWeaknessDto.getId());

                weaknessEventDTO.setVisitNetSegments(apiWeaknessDto.getHttpApiDto().getVisitDomainsValue() == null ?
                        new HashSet<>() : new HashSet<>(apiWeaknessDto.getHttpApiDto().getVisitDomainsValue()));

                weaknessEventDTO.setWeaknessId(apiWeaknessDto.getWeaknessId() == null ? "" : apiWeaknessDto.getWeaknessId());

                weaknessEventDTO.setWeaknessType(apiWeaknessDto.getType() == null ? "" : apiWeaknessDto.getType());

                weaknessEventDTO.setRspDataLabels(apiWeaknessDto.getHttpApiDto().getRspDataLabels() == null ?
                        new HashSet<>() : new HashSet<>(apiWeaknessDto.getHttpApiDto().getRspDataLabels()));

                weaknessEventDTO.setAppName(apiWeaknessDto.getHttpApiDto().getAppName() == null ?
                        "" : apiWeaknessDto.getHttpApiDto().getAppName());

                weaknessEventDTO.setWeaknessLevel(apiWeaknessDto.getLevelName() == null ?
                        "" : apiWeaknessDto.getLevelName());

                weaknessEventDTO.setUpdateTime(apiWeaknessDto.getUpdateTime() == null ?
                        0l : apiWeaknessDto.getUpdateTime());

                weaknessEventDTO.setApiTypes(apiWeaknessDto.getHttpApiDto().getClassificationValue() == null ?
                        new HashSet<>() : new HashSet<>(apiWeaknessDto.getHttpApiDto().getClassificationValue()));

                weaknessEventDTO.setAppUri(apiWeaknessDto.getAppUri() == null ?
                        "" : apiWeaknessDto.getAppUri());

                weaknessEventDTO.setApiFeatureLabels(apiWeaknessDto.getHttpApiDto().getFeatureLabels() == null ?
                        new HashSet<>() : new HashSet<>(apiWeaknessDto.getHttpApiDto().getFeatureLabels()));

                weaknessEventDTO.setCreateTime(apiWeaknessDto.getCreateTime() == null ?
                        0l : apiWeaknessDto.getCreateTime());

                weaknessEventDTO.setName(apiWeaknessDto.getName() == null ?
                        "" : apiWeaknessDto.getName());

                weaknessEventDTO.setRspDataLabelsValue(apiWeaknessDto.getHttpApiDto().getRspDataLabelsValue() == null ?
                        new HashSet<>() : new HashSet<>(apiWeaknessDto.getHttpApiDto().getRspDataLabelsValue()));

                weaknessEventDTO.setFindTimeMs(apiWeaknessDto.getEarlyTimestamp() == null ?
                        0l : apiWeaknessDto.getEarlyTimestamp());

                weaknessEventDTO.setLastTimeMs(apiWeaknessDto.getLastTimestamp() == null ?
                        0l : apiWeaknessDto.getLastTimestamp());

                weaknessEventDTO.setStatus(apiWeaknessDto.getStateName() == null ?
                        "" : apiWeaknessDto.getStateName());


                rows.add(weaknessEventDTO);
            }
            result.setRows(rows);
            result.setTotalCount(apiWeaknessList.getTotalCount());
            return result;
        } catch (Exception e) {
            log.error("getWeaknessEventList error", e);
            return result;
        }
    }

    private void converterSampleIdListAndFeature(WeaknessEventDTO weaknessEventDTO, ApiWeaknessDto apiWeaknessDto) {
        try {
            List<Sample> samples = apiWeaknessDto.getSamples();
            Set<String> samplesSet = new HashSet<>();
            if (samples != null && !samples.isEmpty()) {
                for (Sample sample : samples) {
                    String sampleId = sample.getSampleId();
                    samplesSet.add(sampleId);
                }
                weaknessEventDTO.setSampleIdList(samplesSet);
                if (samples.get(0).getProof() != null && !samples.get(0).getProof().isEmpty()) {
                    List<Proof> proof = samples.get(0).getProof();
                    Proof proof1 = proof.get(0);
                    String name = proof1.getName();
                    String value = proof1.getValue();
                    String feature = name + ":" + value;
                    weaknessEventDTO.setFeature(feature);
                }
            }
        } catch (Exception e) {
            log.error("converterSampleIdListAndFeature error", e);
            weaknessEventDTO.setSampleIdList(new HashSet<>());
            weaknessEventDTO.setFeature("");
        }
    }
}
