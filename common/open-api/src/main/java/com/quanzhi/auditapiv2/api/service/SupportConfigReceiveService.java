package com.quanzhi.auditapiv2.api.service;

import com.quanzhi.auditapiv2.common.util.utils.ProcessUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * create at 2024/12/9 14:50
 * @description:  对接平台-接收配置参数
 **/
@Component
@Slf4j
public class SupportConfigReceiveService {

    private static final String SCRIPT_PATH = "/home/<USER>";

    public void updateSupportConfig() {
        ProcessUtil.executeProcessCmd("bash",SCRIPT_PATH);
        log.info("updateSupportConfig finish");
    }
}