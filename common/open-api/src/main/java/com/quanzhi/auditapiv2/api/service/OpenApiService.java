package com.quanzhi.auditapiv2.api.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.nacos.api.annotation.NacosInjected;
import com.alibaba.nacos.api.config.ConfigService;
import com.quanzhi.audit.mix.log.utils.ResourceHelper;
import com.quanzhi.audit_core.common.config.annotation.DynamicValue;
import com.quanzhi.auditapiv2.api.converter.OpenApiConfigConverter;
import com.quanzhi.auditapiv2.api.domain.OpenApiConfig;
import com.quanzhi.auditapiv2.common.util.utils.DataUtil;
import com.quanzhi.auditapiv2.core.service.node.common.TunnelConfig;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
@Slf4j
public class OpenApiService {

    @DynamicValue(dataId = "auditapiv2.openApiConfig.json", groupId = "auditapiv2", typeClz = OpenApiConfig.class, convertClz = OpenApiConfigConverter.class)
    private Map<String, OpenApiConfig> openApiConfigMap;

    private static final String GROUP = "auditapiv2";

    private static final String DATA_ID = "auditapiv2.openApiConfig.json";

    private static final String PATH = "open-api/auditapiv2.openApiConfig.json";

    private static final Long TIMEOUT = 5000L;

    @NacosInjected
    private ConfigService configService;

    public JSON replaceReqArgs(JSONObject reqBody, String reqUrl){
        OpenApiConfig openApiConfig = null;
        if(DataUtil.isNotEmpty(openApiConfigMap) && openApiConfigMap.containsKey(reqUrl)){
            openApiConfig = JSONObject.parseObject(JSON.toJSONString(openApiConfigMap.get(reqUrl)),OpenApiConfig.class);
        }
        // 有可能那个注解在重启之后会拿不到配置，这时主动去nacos拿一下,重新设置一下
        if(DataUtil.isEmpty(openApiConfig)) {
            List<OpenApiConfig> openApiConfigList = getOpenApiConfigList();
            Map<String, OpenApiConfig> collect = openApiConfigList.stream().collect(Collectors.toMap(OpenApiConfig::getApiUrl, i -> i, (i, j) -> i));
            this.openApiConfigMap = collect;
            OpenApiConfig apiConfig = collect.get(reqUrl);
            if(apiConfig != null){
                openApiConfig = JSONObject.parseObject(JSON.toJSONString(apiConfig),OpenApiConfig.class);
            }else{
                // 从本地文件再拿一下，补丁包没有merge-config,配置不会合并
                String content = ResourceHelper.parse(PATH);
                if(content != null && !content.isEmpty()){
                    List<OpenApiConfig> openApiConfigs = JSONObject.parseArray(content,OpenApiConfig.class);
                    Map<String, OpenApiConfig> collectConfigMap = openApiConfigs.stream().collect(Collectors.toMap(OpenApiConfig::getApiUrl, i -> i, (i, j) -> i));
                    openApiConfig = collectConfigMap.get(reqUrl);
                }
            }
        }
        JSON req = OpenApiConfig.transform(openApiConfig.getBackendRequestConfig(), reqBody);
        return req;
    }

    private List<OpenApiConfig> getOpenApiConfigList(){
        try {
            String config = configService.getConfig(DATA_ID, GROUP, TIMEOUT);
            return JSONObject.parseArray(config,OpenApiConfig.class);
        }catch (Exception e){
            log.error("get sendDataConfig error",e);
            return new ArrayList<>();
        }
    }
}
