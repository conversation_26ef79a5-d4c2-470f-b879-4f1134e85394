package com.quanzhi.auditapiv2.api.wxb;

import lombok.Data;

/**
 * <AUTHOR>
 * @date 2021/10/27 9:32 上午
 * 浙江网信虚拟运营中心上传弱点格式
 */
@Data
public class ZWXWeakness {
    // 隐患类别编码
    private String incidentType;
    // 隐患名称
    private String vulName;
    // 隐患编号
    private String vulCode;
    // 隐患等级（1-危急 2-高危 3-中危 4-低危）
    private int vulLevel;
    // 厂商统一社会信用代码
    private String companyCode;
    // 发现时间（YYYY-MM-dd hh:mm:ss）
    private String foundTime;
    // 是否危险情报告警
    private int isIntelligence;
    // 使用协议
    private String protocol;
    // 开始时间
    private String startTime;
    // 结束时间
    private String endTime;
}
