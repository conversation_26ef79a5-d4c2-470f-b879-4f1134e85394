package com.quanzhi.auditapiv2.api.converter.risk;

import com.quanzhi.auditapiv2.api.domain.risk.RiskDto;
import com.quanzhi.auditapiv2.api.domain.risk.RiskSampleDto;
import com.quanzhi.auditapiv2.core.risk.dto.RiskInfoDto;
import com.quanzhi.auditapiv2.core.service.manager.dto.SampleEventDto;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

/**
 * @author: yangzixian
 * @date: 2023.10.11 09:57
 * @description:
 */
@Mapper
public interface RiskSampleDtoConverter {

    RiskSampleDtoConverter INSTANCE = Mappers.getMapper(RiskSampleDtoConverter.class);

    @Mapping(target = "eventTime", source = "timestamp")
    @Mapping(target = "srcIp", source = "net.srcIp")
    @Mapping(target = "dstIp", source = "net.dstIp")
    @Mapping(target = "apiTypes", source = "featureLabels")
    @Mapping(target = "terminal", source = "uaType")
    @Mapping(target = "rspLength", source = "rspContentLength")
    @Mapping(target = "rspStatusCode", source = "rsp.status")
    @Mapping(target = "primary", source = "id")
    RiskSampleDto convert(SampleEventDto sampleEventDto);

}
