package com.quanzhi.auditapiv2.api.domain.risk;

import com.quanzhi.auditapiv2.core.risk.dto.common.CommonSearchDto;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * @author: yangzixian
 * @date: 2023.10.11 09:51
 * @description:
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class RiskPolicyOpenSearchDto extends CommonSearchDto {

    @ApiModelProperty(value = "排序方式（1-正序，2-倒序）", name = "sortType")
    private Integer sortType;

    @ApiModelProperty(value = "是否开启", name = "needDeleted")
    private Boolean needDeleted;

    @ApiModelProperty(value = "最近修改时间开始", name = "startUpdateTime")
    private Long startUpdateTime;

    @ApiModelProperty(value = "最近修改时间结束", name = "endUpdateTime")
    private Long endUpdateTime;

}
