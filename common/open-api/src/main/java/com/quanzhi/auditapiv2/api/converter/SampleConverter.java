package com.quanzhi.auditapiv2.api.converter;

import com.quanzhi.auditapiv2.core.service.manager.dto.SampleEventBatchTestDto;
import com.quanzhi.auditapiv2.core.service.manager.dto.SampleEventDto;
import com.quanzhi.auditapiv2.openapi.sdk.dto.sample.SampleEventBatchTestDTO;
import com.quanzhi.auditapiv2.openapi.sdk.dto.sample.SampleEventDTO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * @Author: HaoJun
 * @Date: 2021/8/18 3:35 下午
 */
@Mapper
public interface SampleConverter {

    SampleConverter INSTANCE = Mappers.getMapper(SampleConverter.class);

    SampleEventDTO convert(SampleEventDto sampleEventDto);

    SampleEventBatchTestDTO convert(SampleEventBatchTestDto sampleEventBatchTestDto);

}
