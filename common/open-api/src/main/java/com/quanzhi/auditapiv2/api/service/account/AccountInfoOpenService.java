package com.quanzhi.auditapiv2.api.service.account;

import com.quanzhi.audit_core.common.model.NetworkDomain;
import com.quanzhi.auditapiv2.common.dal.dao.AccountInfoDao;
import com.quanzhi.auditapiv2.common.dal.dao.IHttpAppDao;
import com.quanzhi.auditapiv2.common.dal.dao.account.AccountDateDao;
import com.quanzhi.auditapiv2.common.dal.entity.AccountInfo;
import com.quanzhi.auditapiv2.common.dal.entity.AccountDateInfo;
import com.quanzhi.auditapiv2.common.util.utils.DateUtils;
import com.quanzhi.auditapiv2.core.service.manager.web.INetworkSegmentService;
import com.quanzhi.auditapiv2.openapi.sdk.dto.account.*;
import com.quanzhi.metabase.core.model.http.HttpAppResource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;

@Service
@Slf4j
public class AccountInfoOpenService {

    private final AccountDateDao accountDateDao;

    private final AccountInfoDao accountInfoDao;

    private final IHttpAppDao httpAppDao;

    private final INetworkSegmentService networkSegmentService;

    public AccountInfoOpenService(AccountDateDao accountDateDao, AccountInfoDao accountInfoDao, IHttpAppDao httpAppDao, INetworkSegmentService networkSegmentService) {
        this.accountDateDao = accountDateDao;
        this.accountInfoDao = accountInfoDao;
        this.httpAppDao = httpAppDao;
        this.networkSegmentService = networkSegmentService;
    }


    public List<DateVisitCntDTO> getVisitCntByAccount(DateCriteriaDTO criteria) {
        return accountDateDao.getAccountVisitCntByDate(criteria.getAccount(), criteria.getStartDate(), criteria.getEndDate()).stream().map(a -> DateVisitCntDTO.builder().visitCnt(a.getVisitCnt()).date(a.getId()).build()).collect(java.util.stream.Collectors.toList());
    }

    public KeyValueListDTO getAccountVisit(TimestampCriteriaDTO criteria) {
        List<AccountDateInfo> accountDateInfos = accountDateDao.getAccountVisitCntByDate(criteria.getAccounts(), DateUtils.format(new Date(criteria.getStartTimestamp()), DateUtils.DATE_PATTERN_SIMPLE), DateUtils.format(new Date(criteria.getEndTimestamp()), DateUtils.DATE_PATTERN_SIMPLE));
        if (accountDateInfos == null) {
            return null;
        }
        KeyValueListDTO keyValueListDTO = new KeyValueListDTO();
        keyValueListDTO.setKeyList(accountDateInfos.stream().map(AccountDateInfo::getDate).collect(Collectors.toList()));
        keyValueListDTO.setValueList(accountDateInfos.stream().map(AccountDateInfo::getVisitCnt).collect(Collectors.toList()));
        return keyValueListDTO;
    }

    public AccountInfoDTO getAccountInfo(String account) {
        AccountInfo accountInfo = accountInfoDao.getAccount(account);
        if (accountInfo != null) {
            AccountInfoDTO accountInfoDTO = new AccountInfoDTO();
            accountInfoDTO.setAccount(accountInfo.getAccount());
            if (!CollectionUtils.isEmpty(accountInfo.getAppUriList())) {
                accountInfoDTO.setAppInfos(accountInfo.getAppUriList().stream().map(appUri -> {
                    try {
                        HttpAppResource appResource = httpAppDao.selectHttpAppByUri(appUri);
                        if (appResource != null) {
                            return AppInfoDTO.builder().uri(appUri).name(appResource.getName()).host(appResource.getHost()).build();
                        }
                    } catch (Exception e) {
                        log.error("select app error, uri:[{}]", appUri, e);
                    }
                    return null;
                }).filter(Objects::nonNull).collect(java.util.stream.Collectors.toList()));
            } else {
                try {
                    HttpAppResource appResource = httpAppDao.selectHttpAppByUri(accountInfo.getAppUri());
                    if (appResource != null) {
                        accountInfoDTO.setAppInfos(Collections.singletonList(AppInfoDTO.builder().uri(appResource.getUri()).name(appResource.getName()).host(appResource.getHost()).build()));
                    }
                } catch (Exception e) {
                    log.error("select app error, uri:[{}]", accountInfo.getAppUri(), e);
                }
            }
            accountInfoDTO.setUaInfos(new ArrayList<>());
            if (!CollectionUtils.isEmpty(accountInfo.getUaByUaType())) {
                for (Map.Entry<String, Set<String>> entry : accountInfo.getUaByUaType().entrySet()) {
                    accountInfoDTO.getUaInfos().add(UaInfoDTO.builder().uaTypes(new ArrayList<>(entry.getValue())).name(entry.getKey()).build());
                }
            }
            if (!CollectionUtils.isEmpty(accountInfo.getRelatedIpList())) {
                accountInfoDTO.setIpInfos(new ArrayList<>());
                Map<String, List<String>> netMap = new HashMap<>();
                accountInfo.getRelatedIpList().forEach(ip -> {
                    List<NetworkDomain> networkDomains = networkSegmentService.getNetworkDomains(ip);
                    if (!CollectionUtils.isEmpty(networkDomains)) {
                        networkDomains.forEach(networkDomain -> {
                            if (netMap.containsKey(networkDomain.getId())) {
                                netMap.get(networkDomain.getId()).add(ip);
                            } else {
                                netMap.put(networkDomain.getId(), new ArrayList<>(Collections.singletonList(ip)));
                            }
                        });
                    }
                });
                for (Map.Entry<String, List<String>> entry : netMap.entrySet()) {
                    accountInfoDTO.getIpInfos().add(IPInfoDTO.builder().ips(entry.getValue()).accessDomainId(entry.getKey()).build());
                }
            }
            return accountInfoDTO;
        } else {
            throw new IllegalArgumentException("未查到账号信息");
        }
    }
}
