package com.quanzhi.auditapiv2.api.domain.risk;

import com.quanzhi.audit_core.common.risk.Policy;
import com.quanzhi.audit_core.common.risk.Rule;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Map;

/**
 * @author: yangzixian
 * @date: 2023.10.11 11:00
 * @description:
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class RiskPolicyDto {

    private String _id;

    private String name;

    private List<EntityRelation> entityConfigs;

    private Boolean enable;

    private Rule quotaRule;

    private String granularity;

    private Integer level;

    private Rule entityRule;

    private Policy.RiskTimeConfig riskTimeConfig;

    private String entityRuleHash;

    private Boolean delFlag;

    private Map<String, String> realTimeRiskConfig;

    @Data
    public static class EntityRelation {
        /**
         * 主体类型
         *
         * @see EntityEnum
         */
        private String type;

        /**
         * jsonPath
         */
        private String jsonPath;
        /**
         * 关系
         *
         * @see RelationEnum
         */
        private String relation;
    }

    public enum EntityEnum {
        /**
         * 应用
         */
        APP,
        /**
         * API
         */
        API,
        /**
         * 账号
         */
        ACCOUNT,
        /**
         * session
         */
        SESSION,
        /**
         * IP
         */
        IP,
    }

    public enum RelationEnum {
        /**
         * entity
         */
        ENTITY,
        /**
         * channel
         */
        CHANNEL,
        /**
         * RELATED
         * 关联信息
         */
        RELATED
    }

    @Data
    public static class RiskTimeConfig {
        /**
         * 时间类型
         */
        private Policy.RiskTimeTypeEnum type;
        /**
         * jsonPath
         */
        private String jsonPath;

        // private String function;
    }

    public enum RiskTimeTypeEnum {
        /**
         * 日期
         */
        DATE,
        /**
         * 小时
         */
        HOUR,
        /**
         * 分钟
         */
        MINUTE,
        /**
         * 无
         */
        NULL,
        /**
         * 时间戳
         */
        TIMESTAMP
    }

}
