package com.quanzhi.auditapiv2.api.domain;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import lombok.Data;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

@Data
public class OpenApiConfig implements Serializable {

    private String apiUrl;

    private String apiName;

    private BackendRequestConfig backendRequestConfig;

    private String postMode;

    @Data
    public static class BackendRequestConfig implements Serializable{

        private JSON backendParamTemplateJSONPath;

        private Map<String,BackendParamTransform> backendParamTransformMap;
    }

    @Data
    public static class BackendParamTransform implements Serializable{

        private List<String> jsonPath;

        private BackendParamValueType backendParamValueType;

        /**
         * 可能有多级转换
         * 比如某个value是个序列化json，需要传某个值
         */
        private BackendParamTransform backendParamTransform;
    }

    public enum BackendParamValueType {
        _JSONSTRING,
        _JSONARRAY
    }


    public static JSON transform (BackendRequestConfig backendRequestConfig, JSONObject reqBody) {

        JSON result = backendRequestConfig.getBackendParamTemplateJSONPath();

        if(backendRequestConfig.getBackendParamTemplateJSONPath() != null && backendRequestConfig.getBackendParamTransformMap() != null && reqBody != null) {

            Map<String,BackendParamTransform> backendParamTransformMap =  backendRequestConfig.getBackendParamTransformMap();

            for( Map.Entry<String,Object> entry : reqBody.entrySet() ) {

                String key = entry.getKey();

                if(backendParamTransformMap.containsKey( key )) {

                    BackendParamTransform backendParamTransform = backendParamTransformMap.get( key );

                    result = getFinalOriginalJson(backendRequestConfig.getBackendParamTemplateJSONPath(),backendParamTransform,entry.getValue());

                    backendRequestConfig.setBackendParamTemplateJSONPath( result );
                }
            }
        }

        return result;
    }

    public static JSON getFinalOriginalJson (JSON backendParamTemplate,BackendParamTransform backendParamTransform,Object jsonPathVal) {

        JSON result = backendParamTemplate;

        if(backendParamTransform.getBackendParamTransform() != null) {

            switch ( backendParamTransform.getBackendParamValueType() ) {

                case _JSONSTRING:

                    Object tmp = getJsonPathValue(backendParamTemplate,backendParamTransform.getJsonPath());

                    JSON _tmp = getFinalOriginalJson( JSON.parseObject(tmp.toString()), backendParamTransform.getBackendParamTransform(),jsonPathVal);

                    replaceJsonPathValue(backendParamTemplate,backendParamTransform.getJsonPath() , JSON.toJSONString(_tmp) );

                    break;

                case _JSONARRAY:

                    Object tmp_arr = getJsonPathValue(backendParamTemplate,backendParamTransform.getJsonPath());

                    JSON _tmp_ = getFinalOriginalJson( JSON.parseArray(tmp_arr.toString()), backendParamTransform.getBackendParamTransform(),jsonPathVal);

                    replaceJsonPathValue(backendParamTemplate,backendParamTransform.getJsonPath() , JSON.toJSONString(_tmp_) );

                    break;
            }

        } else {

            replaceJsonPathValue(backendParamTemplate,backendParamTransform.getJsonPath() ,jsonPathVal );
        }

        return result;
    }

    /**
     * 从对象中根据 jsonPath 提取到对应的值
     * @param path
     * @param originalVal
     * @return
     */
    public static Object getJsonPathValue(Object originalVal,List<String> path) {
        for (int j = 0; j < path.size(); j++) {

            originalVal = JSONObject.parseObject(JSON.toJSONString(originalVal));

            String[] childPaths = path.get(j).split("\\.");

            for (int i = 0; i < childPaths.length; i++) {

                String childPath = childPaths[i];

                if (childPath.equals("$")) {
                    continue;
                }

                if (childPath.contains("[") && childPath.contains("]")) {

                    int arrIndex = Integer.valueOf(childPath.split("\\[")[1].split("\\]")[0]);

                    childPath = childPath.split("\\[")[0];

                    originalVal = ((JSONObject) originalVal).getJSONArray(childPath).get(arrIndex);

                } else {

                    originalVal = ((JSONObject) originalVal).get(childPath);
                }

            }
        }
        return originalVal;
    }

    /**
     * 替换jsonPath对应的value
     * @param originalVal
     * @param path
     * @return
     */
    public static Object replaceJsonPathValue(Object originalVal,List<String> path,Object jsonPathVal) {
        Object result=null;
        for (int j = 0; j < path.size(); j++) {
            Object target = originalVal;
            String[] childPaths = path.get(j).split("\\.");

            for (int i = 0; i < childPaths.length; i++) {

                String childPath = childPaths[i];

                if (childPath.equals("$")) {
                    continue;
                }

                if (i == childPaths.length - 1) {
                    break;
                }

                if (childPath.contains("[") && childPath.contains("]")) {

                    int arrIndex = Integer.valueOf(childPath.split("\\[")[1].split("\\]")[0]);

                    childPath = childPath.split("\\[")[0];

                    target = ((JSONObject) target).getJSONArray(childPath).get(arrIndex);

                } else {

                    target = ((JSONObject) target).get(childPath);
                }

            }

            if (childPaths[childPaths.length - 1].indexOf("[") != -1 && childPaths[childPaths.length - 1].indexOf("]") != -1) {

                String lastPath = childPaths[childPaths.length - 1];

                int arrIndex = Integer.valueOf(lastPath.split("\\[")[1].split("\\]")[0]);

                String childPath = lastPath.split("\\[")[0];

                ((JSONObject) target).getJSONArray(childPath).set(arrIndex, jsonPathVal);

            } else {

                ((JSONObject) target).put(childPaths[childPaths.length - 1], jsonPathVal);
            }
            result= target;
        }
        return result;
    }
}
