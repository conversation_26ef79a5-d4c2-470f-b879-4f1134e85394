package com.quanzhi.auditapiv2.api.push;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.quanzhi.auditapiv2.api.domain.ProduceData;
import com.quanzhi.auditapiv2.api.domain.Subscriber;
import com.quanzhi.auditapiv2.api.domain.Subscription;
import com.quanzhi.auditapiv2.api.exception.PushServiceException;
import com.quanzhi.auditapiv2.api.repository.SubscriberRepository;
import com.quanzhi.auditapiv2.common.dal.dao.IOpenApiKeyDao;
import com.quanzhi.auditapiv2.common.dal.entity.OpenApiKey;
import com.quanzhi.auditapiv2.openapi.sdk.domain.AuthInfo;
import com.quanzhi.auditapiv2.openapi.sdk.domain.AuthPer;
import com.quanzhi.auditapiv2.openapi.sdk.dto.SubscriberDTO;
import com.quanzhi.auditapiv2.openapi.sdk.util.AuthUtils;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.security.NoSuchAlgorithmException;
import java.util.List;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2021/11/25 3:39 下午
 */
@Component
@Slf4j
public class PushService {

    private final SubscriberRepository subscriberRepository;

    private final List<Producible<?>> producibleList;

    private final IOpenApiKeyDao openApiKeyDaoImpl;

    private OkHttpClient httpClient = (new OkHttpClient.Builder())
            .connectTimeout(30L, TimeUnit.SECONDS)
            .readTimeout(30L, TimeUnit.SECONDS)
            .build();

    public PushService(SubscriberRepository subscriberRepository, List<Producible<?>> producibleList, IOpenApiKeyDao openApiKeyDaoImpl) {
        this.subscriberRepository = subscriberRepository;
        this.producibleList = producibleList;
        this.openApiKeyDaoImpl = openApiKeyDaoImpl;
    }

    public void add(SubscriberDTO subscriberDTO) {
        Subscriber subscriber = subscriberRepository.findOne(subscriberDTO.getAccessKey());
        if (subscriber == null) {
            subscriber = new Subscriber();
        }
        if (subscriber.getAccessKey() == null) {
            subscriber.setAccessKey(subscriberDTO.getAccessKey());
        }
        if (subscriber.getUrl() == null) {
            subscriber.setUrl(subscriberDTO.getUrl());
        }
        if (subscriber.getCreateTime() == null) {
            subscriber.setCreateTime(System.currentTimeMillis());
        }
        if (subscriber.getSecretKey() == null) {
            OpenApiKey openApiKey = openApiKeyDaoImpl.selectOpenApiKeyById(subscriber.getAccessKey());
            if (openApiKey == null) {
                throw new IllegalStateException("请先在分系统中配置运营中心的开放API");
            }
            subscriber.setSecretKey(openApiKey.getKey());
        }
        if (subscriberDTO.getSubscriptions() != null) {
            subscriber.setSubscriptions(subscriberDTO.getSubscriptions().stream().map(subscriptionDTO
                            -> new Subscription(subscriptionDTO.getTopic(), subscriptionDTO.getIndex()))
                    .collect(Collectors.toList()));
        }
        subscriberRepository.save(subscriber);
    }

    public void remove(SubscriberDTO subscriberDTO) {
        Subscriber subscriber = subscriberRepository.findOne(subscriberDTO.getAccessKey());
        if (subscriber == null) {
            return;
        }
        boolean success = subscriberRepository.remove(subscriber);
        if (!success) {
            throw new IllegalStateException("删除推送失败");
        }
    }

    public void push() {
        if (CollectionUtils.isEmpty(producibleList)) {
            return;
        }
        List<Subscriber> subscribers = subscriberRepository.findAll();
        if (CollectionUtils.isEmpty(subscribers)) {
            return;
        }
        for (Subscriber subscriber : subscribers) {
            // 推送订阅者感兴趣的模块
            if (subscriber.getSubscriptions() == null) {
                continue;
            }
            pushSubscriber(subscriber);
        }
    }

    private void pushSubscriber(Subscriber subscriber) {
        for (Subscription subscription : subscriber.getSubscriptions()) {
            if (StringUtils.isEmpty(subscription.getTopic())) {
                continue;
            }
            for (Producible<?> producible : producibleList) {
                try {
                    produce(subscriber, subscription, producible);
                } catch (Throwable e) {
                    log.error("produce error, topic:[{}] index:[{}]", subscription.getTopic(), subscription.getIndex(), e);
                }
            }
        }
    }

    private void produce(Subscriber subscriber, Subscription subscription, Producible<?> producible) throws UnsupportedEncodingException, NoSuchAlgorithmException {
        ProduceData<?> data = producible.produce(subscription.getTopic(), subscription.getIndex());
        while (data != null) {
            subscription.setIndex(data.getIndex());
            PushRequest<?> request = new PushRequest<>();
            AuthInfo authInfo = new AuthInfo();
            authInfo.setAccessKey(subscriber.getAccessKey());
            authInfo.setSignType(subscriber.getSignType());
            authInfo.setSecretKey(subscriber.getSecretKey());
            AuthPer authPer = AuthUtils.getAuthPerStatic(authInfo);
            request.setTopic(subscription.getTopic());
            request.setSign(authPer.getSignValue());
            request.setSignType(authPer.getSignType());
            request.setTimestamp(authPer.getTimestamp());
            Request r = (new Request.Builder())
                    .post(RequestBody.create(MediaType.parse("application/json; charset=utf-8")
                            , JSON.toJSONString(request, new SerializerFeature[]{SerializerFeature.IgnoreNonFieldGetter, SerializerFeature.WriteNullListAsEmpty})))
                    .url(subscriber.getUrl()).build();
            execute(r);
            subscriberRepository.save(subscriber);
            if (!data.isHasNext()) {
                break;
            }
        }
    }

    private void execute(Request request) {
        try {
            Response resp = httpClient.newCall(request).execute();
            PushResponse response = parseResp(resp);
            if (!response.checkSuccess()) {
                throw new PushServiceException(response.getMsg());
            }
        } catch (IOException e) {
            throw new PushServiceException(e);
        }
    }

    private PushResponse parseResp(Response resp) {
        if (resp.isSuccessful()) {
            try {
                return JSON.parseObject(resp.body().string(), PushResponse.class);
            } catch (IOException var4) {
                throw new PushServiceException(var4);
            }
        } else {
            throw new PushServiceException(resp.toString());
        }
    }
}
