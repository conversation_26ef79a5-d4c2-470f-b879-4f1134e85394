package com.quanzhi.auditapiv2.api.service;

import com.quanzhi.auditapiv2.common.dal.dto.query.IpInfoCriteriaDto;
import com.quanzhi.auditapiv2.common.util.dto.ListOutputDto;
import com.quanzhi.auditapiv2.common.util.utils.DataUtil;
import com.quanzhi.auditapiv2.core.service.manager.web.IIpInfoService;
import com.quanzhi.auditapiv2.openapi.sdk.dto.Pageable;
import com.quanzhi.auditapiv2.openapi.sdk.dto.openapi.IpInfoDTOV3;
import org.bson.Document;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

@Service
public class IpInfoOpenService {

    private final IIpInfoService ipInfoService;

    public IpInfoOpenService(IIpInfoService ipInfoService) {
        this.ipInfoService = ipInfoService;
    }

    public ListOutputDto<IpInfoDTOV3> getIpInfoOpenDTOV3(Pageable pageable){
        ListOutputDto<IpInfoDTOV3> listOutputDto = new ListOutputDto<>();
        List<IpInfoDTOV3> ipInfoDTOV3List = new ArrayList<>();
        IpInfoCriteriaDto ipInfoCriteriaDto = new IpInfoCriteriaDto();
        ipInfoCriteriaDto.setPage(pageable.getPage());
        ipInfoCriteriaDto.setLimit(pageable.getSize());
        ipInfoCriteriaDto.setField(pageable.getField() == null ? "visitCnt" : pageable.getField());
        ipInfoCriteriaDto.setSort(pageable.getSort() == null ? 2 : pageable.getSort());
        ListOutputDto<Document> ipInfoList = ipInfoService.getIpInfoList(ipInfoCriteriaDto);
        if(DataUtil.isNotEmpty(ipInfoList)){
            for (Document document : ipInfoList.getRows()){
                IpInfoDTOV3 ipInfoDTOV3 = new IpInfoDTOV3();
                ipInfoDTOV3.setIp(document.getString("ip"));
                ipInfoDTOV3.setAccessDomainIds(document.get("accessDomainIds") != null ? document.getList("accessDomainIds",String.class) : new ArrayList<>());
                ipInfoDTOV3.setRiskLevel(document.getString("riskLevel"));
                ipInfoDTOV3.setAppList(document.getString("appList") != null ? Arrays.asList(document.getString("appList").split(",")) : new ArrayList<>());
                ipInfoDTOV3.setArea(document.getString("area"));
                ipInfoDTOV3.setVisitCnt(document.getLong("visitCnt"));
                ipInfoDTOV3.setRiskNames(document.getString("riskNames") != null ? Arrays.asList(document.getString("riskNames").split(",")) : new ArrayList<>());
                ipInfoDTOV3.setRelatedAccountDistinctCnt(document.getLong("relatedAccountDistinctCnt"));
                ipInfoDTOV3.setRspDataLabelList(document.getString("labelList") != null ? Arrays.asList(document.getString("labelList").split(",")) : new ArrayList<>());
                ipInfoDTOV3.setUaTypes(document.getList("uaTypes",String.class));
                ipInfoDTOV3.setRspDataDistinctCnt(document.getLong("rspDataDistinctCnt"));
                ipInfoDTOV3.setMaxRspDataDistinctCnt(document.getLong("maxRspDataDistinctCnt"));
                ipInfoDTOV3.setMaxRspDataDistinctCntDate(document.getString("maxRspDataDistinctCntDate"));
                ipInfoDTOV3.setBlockFlag(document.getBoolean("blockFlag"));
                ipInfoDTOV3.setFirstDate(document.getString("firstDate"));
                ipInfoDTOV3.setLastDate(document.getString("lastDate"));
                ipInfoDTOV3List.add(ipInfoDTOV3);
            }
        }
        listOutputDto.setRows(ipInfoDTOV3List);
        listOutputDto.setTotalCount(ipInfoList.getTotalCount());
        return listOutputDto;
    }
}
