package com.quanzhi.auditapiv2.api.service;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR>
 * create at 2021/11/26 2:18 下午
 * @description:
 **/
@Service
@Slf4j
public class HttpSystemOpenService {

    /**
     * 监控组件
     */
    private static final List<String> MONITOR_COMPONENT_LIST = Arrays.asList("auditapiv2_alive", "discover_alive", "handler_alive", "metabase_alive", "kafka_alive", "mongo_alive", "nacos_alive", "redis_alive", "xxl_job_alive", "clickHouse_alive", "gw_process_id");
    /**
     * PROMETHEUS服务查询地址
     */
    private static final String PROMETHEUS_PREFIX_URL = "http://prometheus-server:9090/api/v1/query";


    /**
     * <AUTHOR>
     * @description: 获取本系统运行状态
     * 一个组件异常，就返回异常
     * 0:异常 1：正常
     * @date: 2021/11/26
     * @Return java.lang.String
     */
    public String getSystemStat() {
        // {"status":"success","data":{"resultType":"vector","result":[{"metric":{"__name__":"auditapiv2_alive","domain":"auditapiv2","exported_job":"auditapiv2","instance":"pushgateway-server:9091","job":"pushGateway"},"value":[**********,"1"]}]}}
        /*for (String componentName : MONITOR_COMPONENT_LIST) {
            boolean isHealthy = true;
            String url = PROMETHEUS_PREFIX_URL + "?query=" + componentName + "&time=" + (System.currentTimeMillis() / 1000);
            String result = null;
            try {
                result = HttpUtil.get(url);
            } catch (Exception exception) {
                log.error("prometheus 服务接口不通:", exception);
                return "0";
            }
            try {
                JSONArray jsonArray = JSONUtil.parseObj(result).getJSONObject("data").getJSONArray("result");
                if (DataUtil.isEmpty(jsonArray) && jsonArray.size() == 0) {
                    isHealthy = false;
                }
                Object value = jsonArray.getJSONObject(0).getJSONArray("value").getObj(1);
                if ("0".equals(String.valueOf(value))) {
                    isHealthy = false;
                }
            } catch (Exception exception) {
                log.error("解析组件状态结果异常:{}", exception);
                isHealthy = false;
            }
            if (!isHealthy) {
                log.info("组件:{}异常,{}", componentName, result);
                return "0";
            }
        }*/
        //高版本无PROMETHEUS，直接返回正常
        return "1";
    }
}