package com.quanzhi.auditapiv2.api.service;

import com.quanzhi.audit_core.common.model.ApiClassification;
import com.quanzhi.audit_core.common.model.DataLabel;
import com.quanzhi.audit_core.common.model.FeatureLabel;
import com.quanzhi.auditapiv2.api.converter.HttpApiConverter;
import com.quanzhi.auditapiv2.common.dal.dao.IHttpApiDao;
import com.quanzhi.auditapiv2.common.dal.dao.IHttpApiHourCountStatDao;
import com.quanzhi.auditapiv2.common.dal.dto.HttpApiSearchDto;
import com.quanzhi.auditapiv2.common.dal.dto.api.CommonApiFeature;
import com.quanzhi.auditapiv2.common.dal.dto.common.CommonGroupDto;
import com.quanzhi.auditapiv2.common.dal.entity.ApiCountStat;
import com.quanzhi.auditapiv2.common.dal.entity.HttpApiIpHourCountStat;
import com.quanzhi.auditapiv2.common.util.dto.ListOutputDto;
import com.quanzhi.auditapiv2.common.util.utils.DataUtil;
import com.quanzhi.auditapiv2.common.util.utils.StringUtils;
import com.quanzhi.auditapiv2.core.service.manager.dto.HttpApiDto;
import com.quanzhi.auditapiv2.core.service.manager.dto.SampleEventDto;
import com.quanzhi.auditapiv2.core.service.manager.web.*;
import com.quanzhi.auditapiv2.core.service.manager.web.api.ApiTransformService;
import com.quanzhi.auditapiv2.core.service.manager.web.api.IHttpApiOverviewService;
import com.quanzhi.auditapiv2.openapi.sdk.dto.*;
import com.quanzhi.auditapiv2.openapi.sdk.dto.api.ApiDataLabelTop;
import com.quanzhi.auditapiv2.openapi.sdk.dto.openapi.HttpApiDTOV3;
import com.quanzhi.metabase.core.model.http.HttpApiResource;
import com.quanzhi.metabase.core.model.http.HttpApiSample;
import com.quanzhi.metabase.core.model.query.AggregationResult;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @Author: HaoJun
 * @Date: 2021/8/18 12:06 下午
 */
@Service
public class HttpApiOpenService {

    private final IHttpApiService httpApiService;

    private final ApiTransformService apiTransformService;

    private final ISampleEventService sampleEventService;

    private final IDataLabelService dataLabelService;

    private final IFeatureLabelService featureLabelService;

    private final IHttpApiOverviewService httpApiOverviewService;

    private IHttpAppService httpAppServiceImpl;

    private IApiClassifierService apiClassifierService;

    private IHttpApiHourCountStatDao httpApiHourCountStatDao;


    public HttpApiOpenService(IHttpApiService httpApiService,
                              ApiTransformService apiTransformService,
                              ISampleEventService sampleEventService,
                              IDataLabelService dataLabelService,
                              IFeatureLabelService featureLabelService,
                              IHttpAppService httpAppServiceImpl,
                              IApiClassifierService apiClassifierService,
                              IHttpApiOverviewService httpApiOverviewService,
                              IHttpApiHourCountStatDao httpApiHourCountStatDao
    ) {
        this.httpApiService = httpApiService;
        this.apiTransformService = apiTransformService;
        this.sampleEventService = sampleEventService;
        this.dataLabelService = dataLabelService;
        this.featureLabelService = featureLabelService;
        this.httpApiOverviewService = httpApiOverviewService;
        this.httpAppServiceImpl = httpAppServiceImpl;
        this.apiClassifierService = apiClassifierService;
        this.httpApiHourCountStatDao = httpApiHourCountStatDao;
    }

    public ListOutputDto<HttpApiDTO> getHttpApis(Pageable pageable) throws Exception {
        ListOutputDto<HttpApiDto> httpApiDtoListOutputDto = getApiDtoList(pageable);
        return httpApiDtoListOutputDto.map(HttpApiConverter.INSTANCE::convert);
    }

    public ListOutputDto<HttpApiDTOV3> getHttpApisV3(Pageable pageable) throws Exception {
        ListOutputDto<HttpApiDto> httpApiDtoListOutputDto = getApiDtoList(pageable);
        return httpApiDtoListOutputDto.map(HttpApiConverter.INSTANCE::convertV3);
    }

    private ListOutputDto<HttpApiDto> getApiDtoList(Pageable pageable) throws Exception {
        HttpApiSearchDto apiSearchDto;
        if (pageable instanceof TimestampPageable) {
            apiSearchDto = new HttpApiSearchDto();
            if (((TimestampPageable) pageable).getCreateTimestamp() > 0) {
                apiSearchDto.setCreatTimestamp(((TimestampPageable) pageable).getCreateTimestamp());
            }
            if (((TimestampPageable) pageable).getUpdateTimestamp() > 0) {
                apiSearchDto.setUpdateTimestamp(((TimestampPageable) pageable).getUpdateTimestamp());
            }
            if (((TimestampPageable) pageable).getEndUpdateTime() > 0) {
                apiSearchDto.setEndUpdateTime(((TimestampPageable) pageable).getEndUpdateTime());
            }
            if(((TimestampPageable) pageable).getStartUpdateTime() > 0){
                apiSearchDto.setStartUpdateTime(((TimestampPageable) pageable).getStartUpdateTime());
            }
            if(DataUtil.isNotEmpty(((TimestampPageable) pageable).getFlowSource())){
                apiSearchDto.setFlowSource(((TimestampPageable) pageable).getFlowSource());
            }
            if(DataUtil.isNotEmpty(((TimestampPageable) pageable).getApiLifeFlag())){
                
                List<Short> list = new ArrayList<Short>();
                list.add(((TimestampPageable) pageable).getApiLifeFlag());
                apiSearchDto.setApiLifeFlag(list);
            }
            Boolean needDel = ((TimestampPageable) pageable).getNeedDel();
            if (needDel != null && Boolean.TRUE.equals(needDel)) {
                apiSearchDto.setDelFlag(null);
            } else {
                apiSearchDto.setDelFlag(false);
            }
        } else {
            apiSearchDto = new HttpApiSearchDto();
            apiSearchDto.setDelFlag(false);
        }
        ListOutputDto<HttpApiResource> httpApiResourceListOutputDto = httpApiService.getHttpApis(pageable.getPage(),
                pageable.getSize(),
                pageable.getField() == null ? "apiStat.totalVisits" : pageable.getField(),
                pageable.getSort() == null ? 2 : pageable.getSort(),
                apiSearchDto);
        if (httpApiResourceListOutputDto == null) {
            return new ListOutputDto<>();
        }
        ListOutputDto<HttpApiDto> httpApiDtoListOutputDto = httpApiResourceListOutputDto.map(apiTransformService::transform);
        return httpApiDtoListOutputDto;
    }

    public ListOutputDto<SampleEventDto> listSamples(UriPageableDTO uri) {
        if (StringUtils.isNullOrEmpty(uri.getUri())) {
            throw new IllegalArgumentException("missing uri parameter");
        }
        if (uri.getPage() <= 0) {
            throw new IllegalArgumentException("missing page parameter");
        }
        if (uri.getSize() <= 0) {
            uri.setSize(10);
        }
        ListOutputDto<HttpApiSample> apiSampleListOutputDto = sampleEventService.getHttpEventSampleList(uri.getPage(), uri.getSize(), uri.getUri());
        if (apiSampleListOutputDto == null) {
            return new ListOutputDto<>();
        }
        return apiSampleListOutputDto.map(source -> {
            SampleEventDto sampleEventDto = sampleEventService.httpApiSample2SampleEventDto(source);
            sampleEventDto.setUpLabels(dataLabelService.convert(sampleEventDto.getUpLabels()).stream().map(d -> d.getName()).collect(Collectors.toSet()));
            sampleEventDto.setDownLabels(dataLabelService.convert(sampleEventDto.getDownLabels()).stream().map(d -> d.getName()).collect(Collectors.toSet()));
            sampleEventDto.setSamples(null);
            return sampleEventDto;
        });
    }

    public List<DataLabel> listDataLabels() {
        return dataLabelService.getAll();
    }

    public List<FeatureLabel> listFeatureLabels() {
        return featureLabelService.getAll();
    }

    public ListOutputDto<VisitCntDTO> getHttpAppVisits(String uri, String startDate, String endDate) {
        List<AggregationResult> httpAppVisits = httpAppServiceImpl.getHttpAppVisits(uri, null, startDate, endDate);
        if (httpAppVisits == null) {
            return new ListOutputDto<>();
        }
        ListOutputDto<VisitCntDTO> result = new ListOutputDto<>();
        result.setRows(httpAppVisits.stream().map(appVisits -> new VisitCntDTO(Long.valueOf(String.valueOf(appVisits.getResultAlias())),appVisits.getId())).collect(Collectors.toList()));
        result.setTotalCount(httpAppVisits.size());
        return result;
    }

    public ListOutputDto<ApiFeatureLabelsDTO> getListFeatureLabels() {
        List<CommonApiFeature> apiFeatureAll = getApiFeatureAll();
        if (apiFeatureAll.isEmpty()){
            return new ListOutputDto<>();
        }
        ListOutputDto<ApiFeatureLabelsDTO> result = new ListOutputDto<>();
        result.setRows(apiFeatureAll.stream().map(featureLabel -> new ApiFeatureLabelsDTO(featureLabel.getId(),featureLabel.getName(),featureLabel.getEnable())).collect(Collectors.toList()));
        result.setTotalCount(apiFeatureAll.size());
        return result;
    }

    public ListOutputDto<ApiTypeDTO> getListApiTypes() {
        List<CommonApiFeature> apiFeatureAll = getApiFeatureAll();
        if (apiFeatureAll.isEmpty()){
            return new ListOutputDto<>();
        }
        ListOutputDto<ApiTypeDTO> result = new ListOutputDto<>();
        result.setRows(apiFeatureAll.stream().map(featureLabel -> new ApiTypeDTO(featureLabel.getId(),featureLabel.getName())).collect(Collectors.toList()));
        result.setTotalCount(apiFeatureAll.size());
        return result;
    }



    private List<CommonApiFeature> getApiFeatureAll(){
        List<FeatureLabel> featureLabels = featureLabelService.getAll();
        List<ApiClassification> classifications = apiClassifierService.getAll();

        List<CommonApiFeature> commonApiFeatures = new ArrayList<>();

        for (FeatureLabel featureLabel : featureLabels) {
            if (Boolean.TRUE.equals(featureLabel.getDelFlag())) {
                continue;
            }
            CommonApiFeature commonApiFeature = new CommonApiFeature();
            BeanUtils.copyProperties(featureLabel, commonApiFeature);
            if (commonApiFeature.getType() == null) {
                commonApiFeature.setType(FeatureLabel.TypeEnum.SYSTEM.val());
            }
            if (DataUtil.isEmpty(commonApiFeature.getGroup())) {
                commonApiFeature.setGroup("other");
                commonApiFeature.setGroupName("其他");
            }
            commonApiFeature.setCode(FeatureLabel.CodeEnum.FEATURELABEL.getCode());
            commonApiFeatures.add(commonApiFeature);
        }
        for (ApiClassification apiClassification : classifications) {
            if (Boolean.TRUE.equals(apiClassification.getDelFlag())) {
                continue;
            }
            CommonApiFeature commonApiFeature = new CommonApiFeature();
            BeanUtils.copyProperties(apiClassification, commonApiFeature);
            if (commonApiFeature.getType() == null) {
                commonApiFeature.setType(FeatureLabel.TypeEnum.SYSTEM.val());
            }
            if (DataUtil.isEmpty(commonApiFeature.getGroup())) {
                commonApiFeature.setGroup("other");
                commonApiFeature.setGroupName("其他");
            }
            commonApiFeature.setCode(FeatureLabel.CodeEnum.CLASSIFICATIONS.getCode());
            commonApiFeatures.add(commonApiFeature);
        }

        return commonApiFeatures;
    }


    public List<ApiDataLabelTop> dataLabelTop(Integer top) {
        HttpApiSearchDto apiSearchDto = new HttpApiSearchDto();
        apiSearchDto.setPage(1);
        apiSearchDto.setLimit(top);
        apiSearchDto.setGroupField(HttpApiSearchDto.GroupFieldEnum.RSP_DATA_LABEL.getName());
        List<CommonGroupDto> commonGroupDtos = httpApiOverviewService.apiGroup(apiSearchDto);
        List<ApiDataLabelTop> collect = commonGroupDtos.stream().map(e -> {
            ApiDataLabelTop apiDataLabelTop = new ApiDataLabelTop();
            apiDataLabelTop.setCount(e.getCount());
            apiDataLabelTop.setLabel(e.getId());
            apiDataLabelTop.setName(e.getName());
            return apiDataLabelTop;
        }).collect(Collectors.toList());
        return collect;
    }

    public List<CommonGroupDto> group(HttpApiSearchDto apiSearchDto) {
        List<CommonGroupDto> commonGroupDtos = httpApiOverviewService.apiGroup(apiSearchDto);
        return commonGroupDtos;
    }

    public Map<String, ApiCountStat> getHourStatByUriListAndDay(List<String> uriList, String day) {
        List<HttpApiIpHourCountStat> hourReqRspStat = httpApiHourCountStatDao.getHourReqRspStat(uriList, day);

        Map<String, ApiCountStat> result = new HashMap<>();

        for (HttpApiIpHourCountStat stat : hourReqRspStat) {
            ApiCountStat apiCountStat = result.computeIfAbsent(stat.getUri(), k -> {
                ApiCountStat newStat = new ApiCountStat();
                newStat.setHourlyStats(new HashMap<>());
                newStat.setReqHourlyStats(new HashMap<>());
                newStat.setRspHourlyStats(new HashMap<>());
                return newStat;
            });

            for (Map.Entry<String, ApiCountStat> entry : stat.getIpStats().entrySet()) {
                ApiCountStat ipStat = entry.getValue();

                // 合并hourlyStats
                if (ipStat.getHourlyStats() != null) {
                    ipStat.getHourlyStats().forEach((key, value) ->
                            apiCountStat.getHourlyStats().merge(key, value, Long::sum)
                    );
                }

                // 合并reqHourlyStats
                if (ipStat.getReqHourlyStats() != null) {
                    ipStat.getReqHourlyStats().forEach((key, value) ->
                            apiCountStat.getReqHourlyStats().merge(key, value, Long::sum)
                    );
                }

                // 合并rspHourlyStats
                if (ipStat.getRspHourlyStats() != null) {
                    ipStat.getRspHourlyStats().forEach((key, value) ->
                            apiCountStat.getRspHourlyStats().merge(key, value, Long::sum)
                    );
                }
            }
        }

        return result;

    }

    public List<HttpApiIpHourCountStat> getHourReqRspStat(List<String> uriList, String day) {
        return httpApiHourCountStatDao.getHourReqRspStat(uriList, day);
    }
}
