package com.quanzhi.auditapiv2.api.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.quanzhi.auditapiv2.api.dto.MonitorLogDTO;
import com.quanzhi.auditapiv2.common.util.utils.DataUtil;
import com.quanzhi.operate.atomDefinition.ActionConfigFrontTransform;
import com.quanzhi.operate.operateHandler.IOperateHandlerService;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service
public class MonitorLogOpenService {

    private final IOperateHandlerService operateHandlerService;

    private final OpenApiService openApiService;

    private final String MONITOR_LOG_URL = "/v2/query/monitorLogs";

    private final String MONITOR_LOG_COUNT = "/v2/count/monitorLogs";

    private final String MONITOR_LOG_SAMPLE = "/v2/get/monitorLog";

    private final String MONITOR_LOG_LABEL_VALUE = "/v2/query/dataLabelValue";

    private final MongoTemplate mongoTemplate;

    public MonitorLogOpenService(IOperateHandlerService operateHandlerService, OpenApiService openApiService, MongoTemplate mongoTemplate) {
        this.operateHandlerService = operateHandlerService;
        this.openApiService = openApiService;
        this.mongoTemplate = mongoTemplate;
    }

    public Map<String,Object> getListByPage(JSONObject reqBody) throws Exception {
        Map<String,Object> map = new HashMap<>();
        if(DataUtil.isNotEmpty(reqBody.get("sortType")) && "1".equals(reqBody.get("sortType").toString())){
            reqBody.put("sortType","ASC");
        }
        Map<String,Object> result = getActionFrontResult(reqBody,MONITOR_LOG_COUNT);
        long totalCount = Long.parseLong(result.get("totalCount").toString());
        map.put("totalCnt",totalCount);
        if(totalCount == 0) {
            map.put("rows",new ArrayList<>());
            return map;
        }
        Map<String,Object> ListResult = getActionFrontResult(reqBody,MONITOR_LOG_URL);
        List<MonitorLogDTO> monitorLogDTOList = new ArrayList<>();
        Object rows = ListResult.get("rows");
        JSONArray.parseArray(JSON.toJSONString(rows),Map.class).forEach(item -> {
            MonitorLogDTO monitorLogDTO = transform(item);
            monitorLogDTOList.add(monitorLogDTO);
        });
        map.put("rows",monitorLogDTOList);
        return map;
    }

    private MonitorLogDTO transform(Map item){
        MonitorLogDTO monitorLogDTO = new MonitorLogDTO();
        monitorLogDTO.setPrimary(item.get("primary").toString());
        monitorLogDTO.setEventTime(Long.parseLong(item.get("timestamp").toString()));
        JSONObject net = JSONObject.parseObject(JSON.toJSONString(item.get("net")),JSONObject.class);
        monitorLogDTO.setSrcIp(net.get("srcIp").toString());
        monitorLogDTO.setDstIp(net.get("dstIp").toString());
        monitorLogDTO.setAccount(item.get("account") == null ? "" : item.get("account").toString());
        monitorLogDTO.setApiUrl(item.get("apiUrl").toString());
        List<String> classifications = new ArrayList<>();
        if(item.get("classifications") != null) {
            classifications.addAll((List<String>) item.get("classifications"));
        }
        monitorLogDTO.setApiTypes(classifications);
        monitorLogDTO.setApiLevel(item.get("apiLevel").toString());
        monitorLogDTO.setAppUri(item.get("appUri").toString());
        monitorLogDTO.setHost(item.get("host").toString());
        monitorLogDTO.setReqContentType(item.get("reqContentType").toString());
        monitorLogDTO.setMethod(item.get("reqMethod").toString());
        monitorLogDTO.setTerminal(item.get("uaType").toString());
        monitorLogDTO.setUa(item.get("ua").toString());
        monitorLogDTO.setReferer(item.get("referer") == null ? "" : item.get("referer").toString());
        monitorLogDTO.setRspContentType(item.get("rspContentType").toString());
        monitorLogDTO.setRspLength(Long.parseLong(item.get("rspContentLength").toString()));
        monitorLogDTO.setRspStatusCode(item.get("rspStatus") == null ? "" : item.get("rspStatus").toString());
        List<String> reqDataLabelIds = new ArrayList<>();
        if (item.get("reqDataLabelIds") != null){
            reqDataLabelIds.addAll((List<String>) item.get("reqDataLabelIds"));
        }
        monitorLogDTO.setReqDataLabelIds(reqDataLabelIds);
        List<String> rspDataLabelIds = new ArrayList<>();
        if (item.get("rspDataLabelIds") != null){
            rspDataLabelIds.addAll((List<String>) item.get("rspDataLabelIds"));
        }
        monitorLogDTO.setRspDataLabelIds(rspDataLabelIds);
        return monitorLogDTO;
    }

    public Map<String,Object> getSample(JSONObject reqBody){
        try {
            Map<String,Object> result = getActionFrontResult(reqBody,MONITOR_LOG_SAMPLE);
            return result;
        } catch (Exception e) {
            return null;
        }
    }

    public Map<String,Object> getLabelValue(JSONObject reqBody){
        try {
            Map<String,Object> result = getActionFrontResult(reqBody,MONITOR_LOG_LABEL_VALUE);
            return result;
        } catch (Exception e) {
            return null;
        }
    }

    public Map<String,Object> getActionFrontResult(JSONObject reqBody,String url) throws Exception {
        JSON args = openApiService.replaceReqArgs(reqBody, url);
        ActionConfigFrontTransform actionConfigFrontTransformQuery = JSONObject.toJavaObject(args, ActionConfigFrontTransform.class);
        Map<String,Object> result = operateHandlerService.handleActions(actionConfigFrontTransformQuery, null);
        return result;
    }

    public Object queryDataMap() {
        return mongoTemplate.findAll(Map.class,"clickhouseConfigMap");
    }
}
