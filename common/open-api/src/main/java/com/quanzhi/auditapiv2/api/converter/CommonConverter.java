package com.quanzhi.auditapiv2.api.converter;

import com.quanzhi.auditapiv2.core.model.UaTypeDto;
import com.quanzhi.auditapiv2.openapi.sdk.dto.UaTypeDTO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * @Author: HaoJun
 * @Date: 2021/8/18 3:35 下午
 */
@Mapper
public interface CommonConverter {

    CommonConverter INSTANCE = Mappers.getMapper(CommonConverter.class);

    UaTypeDTO convert(UaTypeDto uaTypeDto);

}
