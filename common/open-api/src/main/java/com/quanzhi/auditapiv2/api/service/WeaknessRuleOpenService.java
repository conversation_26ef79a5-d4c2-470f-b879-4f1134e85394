package com.quanzhi.auditapiv2.api.service;

import com.quanzhi.audit_core.common.model.WeaknessRule;
import com.quanzhi.auditapiv2.api.converter.WeaknessConverter;
import com.quanzhi.auditapiv2.common.dal.dto.common.CascadeDto;
import com.quanzhi.auditapiv2.core.service.manager.web.IApiWeaknessService;
import com.quanzhi.auditapiv2.core.service.manager.web.IWeaknessRuleService;
import com.quanzhi.auditapiv2.openapi.sdk.dto.CascadeDTO;
import com.quanzhi.auditapiv2.openapi.sdk.dto.weakness.WeaknessRuleDTO;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * @Auther: yangzixian
 * @Date: 2021/11/26 11:35
 * @Description:
 */
@Service
public class WeaknessRuleOpenService {

    private final IWeaknessRuleService weaknessRuleService;
    private final IApiWeaknessService apiWeaknessService;

    public WeaknessRuleOpenService(IWeaknessRuleService weaknessRuleService, IApiWeaknessService apiWeaknessService) {
        this.weaknessRuleService = weaknessRuleService;
        this.apiWeaknessService = apiWeaknessService;
    }

    /**
     * 弱点规则级联列表
     * @return
     */
    public List<CascadeDTO> getCascades() {
        List<CascadeDTO> cascadeDTOS = new ArrayList<>();
        List<CascadeDto> cascades = weaknessRuleService.getCascades();
        for (CascadeDto cascade : cascades) {
            cascadeDTOS.add(WeaknessConverter.INSTANCE.convert(cascade));
        }
        return cascadeDTOS;
    }

    /**
     * 黑名单
     *
     * @param list
     */
    public void black(List<WeaknessRule> list) {
        try {
            weaknessRuleService.black(list);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 根据id获取弱点规则信息
     *
     * @param id
     * @return
     * @throws Exception
     */
    public WeaknessRuleDTO getWeaknessRuleById(String id) throws Exception {
        return WeaknessConverter.INSTANCE.convert(weaknessRuleService.getWeaknessRuleById(id));
    }

}
