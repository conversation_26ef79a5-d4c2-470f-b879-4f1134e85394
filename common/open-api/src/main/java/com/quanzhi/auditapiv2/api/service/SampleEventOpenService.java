package com.quanzhi.auditapiv2.api.service;

import com.quanzhi.auditapiv2.core.service.manager.dto.ExtractValueDto;
import com.quanzhi.auditapiv2.core.service.manager.web.ISampleEventService;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.TreeSet;

/**
 * @Auther: yangzixian
 * @Date: 2021/11/26 16:09
 * @Description:
 */
@Service
public class SampleEventOpenService {

    private final ISampleEventService sampleEventService;

    public SampleEventOpenService(ISampleEventService sampleEventService) {
        this.sampleEventService = sampleEventService;
    }

    public Map<String, Set<TreeSet<String>>> getSampleLabels(String uri) {
        return sampleEventService.getSampleLabels(uri);
    }

    public List<ExtractValueDto> extractContentByLabels(String content, List<String> labels, String accuracy, Boolean desensitize, String originValueDesensiKey) {
        return sampleEventService.extractContentByLabels(content, labels, accuracy, desensitize, originValueDesensiKey);
    }

}
