package com.quanzhi.auditapiv2.api.converter.risk;

import com.quanzhi.auditapiv2.api.domain.risk.RiskPolicyOpenSearchDto;
import com.quanzhi.auditapiv2.core.risk.entity.RiskPolicySearchDto;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

/**
 * @author: yangzixian
 * @date: 2023.10.11 09:57
 * @description:
 */
@Mapper
public interface RiskPolicySearchDtoConverter {

    RiskPolicySearchDtoConverter INSTANCE = Mappers.getMapper(RiskPolicySearchDtoConverter.class);

    @Mapping(source = "sortType", target = "sort")
    @Mapping(source = "needDeleted", target = "delFlag")
    RiskPolicySearchDto convert(RiskPolicyOpenSearchDto riskPolicyOpenSearchDto);

}
