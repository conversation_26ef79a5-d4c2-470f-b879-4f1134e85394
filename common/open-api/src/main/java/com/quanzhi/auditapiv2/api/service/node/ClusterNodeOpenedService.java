package com.quanzhi.auditapiv2.api.service.node;

import com.quanzhi.audit.mix.schdule.application.service.ScheduleApplicationService;
import com.quanzhi.audit.mix.schdule.domain.exception.ScheduleException;
import com.quanzhi.auditapiv2.common.dal.dao.node.ClusterNodeDao;
import com.quanzhi.auditapiv2.common.dal.entity.node.ClusterNode;
import com.quanzhi.auditapiv2.common.dal.entity.node.ClusterState;
import com.quanzhi.auditapiv2.common.dal.entity.node.ClusterType;
import com.quanzhi.auditapiv2.common.util.dto.ErrorCode;
import com.quanzhi.auditapiv2.common.util.utils.StringUtils;
import com.quanzhi.auditapiv2.core.service.node.cluster.ClusterNodeService;
import com.quanzhi.auditapiv2.core.service.node.cluster.OpenAPIQueryService;
import com.quanzhi.auditapiv2.core.service.node.cluster.dto.ClusterNodeDTO;
import com.quanzhi.auditapiv2.core.service.node.cluster.dto.ClusterNodeMapper;
import com.quanzhi.auditapiv2.core.service.node.cluster.dto.SyncDTO;
import com.quanzhi.auditapiv2.core.service.node.cluster.dto.TaskDTO;
import com.quanzhi.auditapiv2.core.service.node.cluster.exception.ClusterNodeException;
import com.quanzhi.auditapiv2.core.service.node.common.TunnelCollection;
import com.quanzhi.auditapiv2.core.service.node.common.TunnelMapRecord;
import com.quanzhi.auditapiv2.core.service.node.source.watermark.SourceReadProgressRepository;
import com.quanzhi.auditapiv2.core.service.node.task.ClusterTunnelTaskExecutorService;
import com.quanzhi.auditapiv2.openapi.sdk.domain.AuthInfo;
import com.quanzhi.auditapiv2.openapi.sdk.dto.node.TunnelData;
import com.quanzhi.auditapiv2.openapi.sdk.exception.OpenApiException;
import com.quanzhi.auditapiv2.openapi.sdk.request.node.PingRequest;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
@Slf4j
public class ClusterNodeOpenedService {
    private final ClusterNodeDao clusterNodeDao;
    private final ClusterTunnelTaskExecutorService clusterTunnelTaskExecutorService;
    private final OpenAPIQueryService openAPIQueryService;
    private final SourceReadProgressRepository sourceReadProgressRepository;
    private final ConsumeControlService consumeControlService;
    private final ScheduleApplicationService scheduleApplicationService;

    public ClusterNodeDTO connect(ClusterNodeDTO node, boolean force) throws ClusterNodeException {
        // 链接主节点
        if (node.getType() == ClusterType.MASTER) {
            // 检测是否已经有主节点
            ClusterNode existMaster = clusterNodeDao.getMasterNode();
            // 强制移除该主节点
            if (existMaster != null) {
                if (!existMaster.getNid().equals(node.getNid())) {
                    if (force) {
                        clusterNodeDao.remove(existMaster);
                    } else {
                        throw new ClusterNodeException(ErrorCode.CLUSTER_NODE_ADD_ERROR);
                    }
                } else {
                    // #fixbug 当主节点重新安装后，再添加相同的子节点，子节点不会再次同步老的数据
                    if (node.isInit()){
                        log.info("master node init");
                        clusterNodeDao.remove(existMaster);
                    }else {
                        node.setId(existMaster.getId());
                    }
                }
            }
        } else {
            // 子节点请求链接
            throw new UnsupportedOperationException("目前只支持主节点链接");
        }
        ClusterNode masterNode = ClusterNodeMapper.INSTANCE.convert(node);
        ClusterNodeDTO cur = new ClusterNodeDTO();
        cur.setIp(ClusterNodeService.getIp());
        cur.setNid(clusterNodeDao.getNodeId());
        // 尝试连接这个节点，如果连接成功了代表这个节点是可用的
        try {
            ping(masterNode);
        } catch (OpenApiException e) {
            throw new IllegalStateException("子节点无法连接到主节点，请检查主节点 URL 配置，错误信息：" + getReplaceMsg(e));
        }
        clusterNodeDao.save(masterNode);
        return cur;
    }

    private static String getReplaceMsg(OpenApiException e) {
        return e.getMessage().replace("com.quanzhi.auditapiv2.openapi.sdk.exception.OpenApiException:", "");
    }

    public void remove(ClusterNodeDTO node) {
        ClusterNode curNode = clusterNodeDao.findByNID(node.getNid());
        if (curNode != null) {
            clusterNodeDao.remove(curNode);
        }
    }

    public void readTunnel(TunnelData data) {
        if (consumeControlService.isStop()) {
            throw new IllegalStateException("no space on disk");
        }
        data.getRecords().forEach(record -> clusterTunnelTaskExecutorService.push(new TunnelMapRecord(data.getNid(), record.getValues(), new TunnelCollection(record.getCollection().getName()))));
    }

    public void ping(ClusterNode node) throws OpenApiException {
        AuthInfo authInfo = ClusterNodeService.createAuthInfo(node);
        openAPIQueryService.execute(authInfo, new PingRequest());
    }

    /**
     * 确保 MongoDB 正常即可
     */
    public void ping(String nid) throws ClusterNodeException {
        if (!StringUtils.isNullOrEmpty(nid)) {
            ClusterNode node = clusterNodeDao.findByNID(nid);
            if (node == null) {
                throw new ClusterNodeException(ErrorCode.CLUSTER_NODE_REMOVE_ERROR);
            }
            node.setLastReceivedTimestamp(System.currentTimeMillis());
            clusterNodeDao.save(node);
        }
    }

    public void sync(SyncDTO syncDTO) {
        ClusterType clusterType = clusterNodeDao.getClusterType();
        if (clusterType != ClusterType.SLAVE) {
            throw new IllegalStateException("节点类型异常");
        }
        if (consumeControlService.isStop()) {
            throw new IllegalStateException("no space on disk");
        }
        if (syncDTO.isAll()) {
            sourceReadProgressRepository.deleteAll();
        }
        if (StringUtils.isNullOrEmpty(syncDTO.getTaskId())) {
            clusterTunnelTaskExecutorService.executeSyncSlaveDataNow();
        } else {
            execute(syncDTO.getTaskId());
        }
    }
    public void execute(String id){
        try {
            scheduleApplicationService.execute("auditapiv2", "ClusterTunnelTask", id);
        } catch (ScheduleException e) {
            log.error("execute error", e);
            throw new IllegalStateException("任务正在运行中，请稍后再试");
        }
    }
}
