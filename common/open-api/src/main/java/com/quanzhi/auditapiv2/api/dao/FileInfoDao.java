package com.quanzhi.auditapiv2.api.dao;

import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Map;

@Repository
public class FileInfoDao {

    private final MongoTemplate mongoTemplate;

    private static final String COLLECTION_NAME = "fileInfo";

    public FileInfoDao(MongoTemplate mongoTemplate) {
        this.mongoTemplate = mongoTemplate;
    }

    public List<Map> getFileList(Query query){
        return mongoTemplate.find(query,Map.class,COLLECTION_NAME);
    }

    public Long getFileCount(Query query){
        return mongoTemplate.count(query,COLLECTION_NAME);
    }
}
