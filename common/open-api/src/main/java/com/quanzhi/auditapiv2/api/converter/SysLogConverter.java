package com.quanzhi.auditapiv2.api.converter;

import com.quanzhi.auditapiv2.common.dal.dataobject.SysLog;
import com.quanzhi.auditapiv2.openapi.sdk.dto.SysLogDTO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2021/8/18 12:04 下午
 */
@Mapper
public interface SysLogConverter {

    SysLogConverter INSTANCE = Mappers.getMapper(SysLogConverter.class);

    SysLogDTO convert(SysLog sysLog);
}
