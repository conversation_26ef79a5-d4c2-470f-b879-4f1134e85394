package com.quanzhi.auditapiv2.api.service;

import com.quanzhi.audit_core.common.model.AppFeatureLabel;
import com.quanzhi.auditapiv2.api.converter.HttpAppConverter;
import com.quanzhi.auditapiv2.common.dal.dao.IHttpAppDao;
import com.quanzhi.auditapiv2.common.dal.dao.app.HttpAppMetaDataDao;
import com.quanzhi.auditapiv2.common.dal.dto.NetworkSegmentDto;
import com.quanzhi.auditapiv2.common.dal.dto.common.CommonGroupDto;
import com.quanzhi.auditapiv2.core.service.manager.web.IAppFeatureLabelService;
import com.quanzhi.auditapiv2.core.service.manager.web.INetworkSegmentService;
import com.quanzhi.auditapiv2.openapi.sdk.dto.HttpAppDTO;
import com.quanzhi.auditapiv2.openapi.sdk.dto.Pageable;
import com.quanzhi.auditapiv2.common.dal.dto.HttpAppDto;
import com.quanzhi.auditapiv2.common.util.dto.ListOutputDto;
import com.quanzhi.auditapiv2.core.service.manager.web.IHttpAppService;
import com.quanzhi.auditapiv2.openapi.sdk.dto.TimestampPageable;
import com.quanzhi.auditapiv2.openapi.sdk.dto.openapi.HttpAppDTOV3;
import com.quanzhi.metabase.core.model.http.app.HttpAppMetaData;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import com.quanzhi.auditapiv2.openapi.sdk.dto.app.AppNetSegmentDto;
import com.quanzhi.auditapiv2.openapi.sdk.dto.app.AppVisitDto;
import com.quanzhi.metabase.core.model.http.HttpAppResource;
import lombok.extern.slf4j.Slf4j;

import java.util.*;
import java.util.stream.Collectors;

/**
 * @Author: HaoJun
 * @Date: 2021/8/18 12:06 下午
 */
@Service
@Slf4j
public class HttpAppOpenService {

    private final IHttpAppService httpAppService;

    private IAppFeatureLabelService appFeatureLabelService;

    private final INetworkSegmentService networkSegmentService;

    private final IHttpAppDao httpAppDao;

    private final HttpAppMetaDataDao httpAppMetaDataDao;


    public HttpAppOpenService(IHttpAppService httpAppService, IAppFeatureLabelService appFeatureLabelService
            , INetworkSegmentService networkSegmentService
            , IHttpAppDao httpAppDao, HttpAppMetaDataDao httpAppMetaDataDao) {
        this.httpAppService = httpAppService;
        this.appFeatureLabelService = appFeatureLabelService;
        this.networkSegmentService = networkSegmentService;
        this.httpAppDao = httpAppDao;
        this.httpAppMetaDataDao = httpAppMetaDataDao;
    }

    public ListOutputDto<HttpAppDTO> getHttpApps(Pageable pageable) throws Exception {
        ListOutputDto<HttpAppDto> httpApiResourceListOutputDto = getAppDtoList(pageable);
        ListOutputDto<HttpAppDTO> listOutputDto = httpApiResourceListOutputDto.map(HttpAppConverter.INSTANCE::convert);
        return listOutputDto;
    }

    public ListOutputDto<HttpAppDTOV3> getHttpAppsV3(Pageable pageable) throws Exception {
        ListOutputDto<HttpAppDto> httpApiResourceListOutputDto = getAppDtoList(pageable);
        ListOutputDto<HttpAppDTOV3> listOutputDto = httpApiResourceListOutputDto.map(HttpAppConverter.INSTANCE::convertV3);
        return listOutputDto;
    }

    public Set<String> getDeployIpAndPort(String appUri){
        HttpAppMetaData httpAppMetaData = httpAppMetaDataDao.findByUri(appUri);
        if (httpAppMetaData != null && httpAppMetaData.getDeployIpPorts() != null){
            return  httpAppMetaData.getDeployIpPorts();
        }
        return new HashSet<>();

    }


    private ListOutputDto<HttpAppDto> getAppDtoList(Pageable pageable) throws Exception {
        Map<String, Object> map = new HashMap<>();
        if (pageable instanceof TimestampPageable) {
            map = ((TimestampPageable) pageable).generateTimeMap();
        }
        ListOutputDto<HttpAppDto> httpApiResourceListOutputDto = httpAppService.getHttpAppList(map,
                pageable.getField() == null ? "appStat.totalVisits" : pageable.getField(),
                pageable.getSort() == null ? 2 : pageable.getSort(),
                pageable.getPage(),
                pageable.getSize());
        return httpApiResourceListOutputDto;
    }

    public List<AppFeatureLabel> list() {
        return appFeatureLabelService.getAll();
    }

    public List<AppVisitDto> appVisitCntTop(Integer top) {
        try {
            ListOutputDto<HttpAppDto> httpAppList = httpAppService.getHttpAppList(new HashMap<>(), "appStat.totalVisits", 2, 1, top);
            List<HttpAppDto> rows = httpAppList.getRows();
            List<AppVisitDto> collect = rows.stream().map(e -> {
                AppVisitDto appVisitDto = new AppVisitDto();
                appVisitDto.setHost(e.getHost());
                appVisitDto.setAppName(e.getName());
                appVisitDto.setVisitCnt(e.getAppStat().getTotalVisits());
                return appVisitDto;
            }).collect(Collectors.toList());
            return collect;
        } catch (Exception e) {
            log.error("getHttpAppList error:", e);
            return new ArrayList<>();
        }
    }

    public Map<String, List<AppNetSegmentDto>> accessDeployNetSegment(String appUri) {
        Map<String, String> map = new HashMap<>();
        map.put("uri", appUri);
        HttpAppResource appOne = httpAppService.findQueryOne(map);
        try {
            Map<String, List<AppNetSegmentDto>> appNetMap = new HashMap<>();
            Map<String, List<NetworkSegmentDto>> networkSegmentListByAppId = networkSegmentService.getNetworkSegmentListByAppId(appOne.getId());
            networkSegmentListByAppId.forEach((k, v) -> {
                List<AppNetSegmentDto> collect = v.stream().map(e -> {
                    AppNetSegmentDto appNetSegmentDto = new AppNetSegmentDto();
                    appNetSegmentDto.setIps(Arrays.asList(e.getIps().split(",")));
                    String[] netSegArr = e.getId().split("-");
                    appNetSegmentDto.setIds(new AppNetSegmentDto.NetSegment(netSegArr[0], netSegArr.length == 2 ? netSegArr[1] : null));
                    return appNetSegmentDto;
                }).collect(Collectors.toList());

                if ("deployDomains".equals(k)) {
                    appNetMap.put("deployNetSegments", collect);
                }
                if ("visitDomains".equals(k)) {
                    appNetMap.put("visitNetSegments", collect);
                }
            });
            return appNetMap;
        } catch (Exception e) {
            log.error("getNetworkSegmentListByAppId error:", e);
            return new HashMap<>();
        }


    }

    /**
     * 应用生命状态分组
     */
    public  List<CommonGroupDto> appLifeGroup() {
        try {
            List<CommonGroupDto> appLifeFlag = httpAppService.getHttpAppGroup(new HashMap<>(), null, "appLifeFlag");
            return appLifeFlag;
        } catch (Exception e) {
           log.error("getHttpAppGroup error:",e);
           return new ArrayList<>();
        }
    }

    public List<CommonGroupDto> sensitiveAppTop10() {
        List<CommonGroupDto> list = new ArrayList<CommonGroupDto>();
        Map<String, Object> map = new HashMap<String, Object>();
        map.put("isSensitiveApp", true);
        map.put("delFlag", false);
        try {
            List<HttpAppResource> httpAppList = httpAppDao.sensitiveAppTop10(map, "appStat.sensitiveApiCount");
            for (HttpAppResource httpAppResource : httpAppList) {
                CommonGroupDto commonGroupDto = CommonGroupDto.builder()
                        .id(httpAppResource.getUri())
                        .count(httpAppResource.getAppStat().getSensitiveApiCount())
                        .name(httpAppResource.getName())
                        .extraInfo(new HashMap<String, String>() {
                            {
                                put("id", httpAppResource.getId());
                                put("host", httpAppResource.getHost());
                                put("name", httpAppResource.getName());
                            }
                        })
                        .build();
                list.add(commonGroupDto);
            }
        }catch (Exception e){
            log.error("获取敏感应用top10失败",e);
        }
        return list;
    }
}
