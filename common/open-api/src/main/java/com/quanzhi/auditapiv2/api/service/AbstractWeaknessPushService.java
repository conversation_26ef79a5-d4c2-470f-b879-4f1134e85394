package com.quanzhi.auditapiv2.api.service;

import com.quanzhi.auditapiv2.api.domain.PushRecord;
import com.quanzhi.auditapiv2.api.repository.PushRepository;
import com.quanzhi.metabase.core.model.http.weakness.ApiWeakness;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2021/10/27 9:51 上午
 */
public abstract class AbstractWeaknessPushService implements WeaknessPushService {

    private final PushRepository pushRepository;

    private static final String TYPE = "WEAKNESS";

    public AbstractWeaknessPushService(PushRepository pushRepository) {
        this.pushRepository = pushRepository;
    }

    @Override
    public void onFail(String code, List<ApiWeakness> weaknesses, Throwable error) {
        PushRecord pushRecord = new PushRecord();
        pushRecord.setCode(code);
        pushRecord.setType(TYPE);
        pushRecord.setSuccess(false);
        pushRecord.setMsg(error.getMessage());
        pushRecord.setContent(weaknesses.stream()
                .map(weakness -> weakness.getId())
                .collect(Collectors.joining()));
        pushRepository.save(pushRecord);
    }

    @Override
    public void onSuccess(String code, List<ApiWeakness> weaknesses) {
        PushRecord pushRecord = new PushRecord();
        pushRecord.setCode(code);
        pushRecord.setType(TYPE);
        pushRecord.setSuccess(true);
        pushRecord.setContent(weaknesses.stream()
                .map(weakness -> weakness.getId())
                .collect(Collectors.joining()));
        pushRepository.save(pushRecord);
    }
}
