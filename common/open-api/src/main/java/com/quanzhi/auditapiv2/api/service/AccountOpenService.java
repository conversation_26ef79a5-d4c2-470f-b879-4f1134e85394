package com.quanzhi.auditapiv2.api.service;

import com.quanzhi.auditapiv2.common.dal.dto.AccountInfoDto;
import com.quanzhi.auditapiv2.common.dal.dto.query.AccountInfoCriteriaDto;
import com.quanzhi.auditapiv2.common.util.dto.ListOutputDto;
import com.quanzhi.auditapiv2.common.util.utils.DataUtil;
import com.quanzhi.auditapiv2.core.service.manager.web.IAccountInfoService;
import com.quanzhi.auditapiv2.openapi.sdk.dto.Pageable;
import com.quanzhi.auditapiv2.openapi.sdk.dto.openapi.AccountInfoDTOV3;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashSet;
import java.util.List;
import java.util.stream.Collectors;

@Service
public class AccountOpenService {

    private final IAccountInfoService accountInfoService;

    public AccountOpenService(IAccountInfoService accountInfoService) {
        this.accountInfoService = accountInfoService;
    }

    public ListOutputDto<AccountInfoDTOV3> getAccountInfoList(Pageable pageable){
        ListOutputDto<AccountInfoDTOV3> listOutputDto = new ListOutputDto<>();
        List<AccountInfoDTOV3> accountInfoDTOV3List = new ArrayList<>();
        AccountInfoCriteriaDto accountInfoCriteriaDto = new AccountInfoCriteriaDto();
        accountInfoCriteriaDto.setPage(pageable.getPage());
        accountInfoCriteriaDto.setLimit(pageable.getSize());
        accountInfoCriteriaDto.setField(pageable.getField() == null ? "visitCnt" : pageable.getField());
        accountInfoCriteriaDto.setSort(pageable.getSort() == null ? 2 : pageable.getSort());
        ListOutputDto<AccountInfoDto> accountInfoListFields = accountInfoService.getAccountInfoListFields(accountInfoCriteriaDto, null);
        for(AccountInfoDto accountInfoDto : accountInfoListFields.getRows()){
            AccountInfoDTOV3 accountInfoDTOV3 = new AccountInfoDTOV3();
            accountInfoDTOV3.setAccount(accountInfoDto.getAccount());
            if(DataUtil.isNotEmpty(accountInfoDto.getApp())){
                accountInfoDTOV3.setAppUriList(Arrays.asList(accountInfoDto.getApp().split(",")).stream().map(i-> i.trim()).collect(Collectors.toList()));
            } else{
                accountInfoDTOV3.setAppUriList(new ArrayList<>());
            }
            accountInfoDTOV3.setStaffChinese(accountInfoDto.getStaffChinese() == null? "" : accountInfoDto.getStaffChinese());
            accountInfoDTOV3.setStaffNickName(accountInfoDto.getStaffNickName() == null? "" : accountInfoDto.getStaffNickName());
            accountInfoDTOV3.setStaffName(accountInfoDto.getStaffName() == null? "" : accountInfoDto.getStaffName());
            accountInfoDTOV3.setStaffId(accountInfoDTOV3.getStaffId() == null? "" : accountInfoDto.getStaffId());
            accountInfoDTOV3.setStaffDepart(accountInfoDto.getStaffDepart() == null? "" : accountInfoDto.getStaffDepart());
            accountInfoDTOV3.setStaffRole(accountInfoDto.getStaffRole() == null? "" : accountInfoDto.getStaffRole());
            if(DataUtil.isNotEmpty(accountInfoDto.getAccountLifeFlag())){
                accountInfoDTOV3.setAccountLifeFlagName(Arrays.asList(accountInfoDto.getAccountLifeFlag().split(",")).stream().filter(i-> i != null && !"null".equals(i) && !"".equals(i)).map(i->i.trim()).collect(Collectors.toList()));
            } else{
                accountInfoDTOV3.setAccountLifeFlagName(new ArrayList<>());
            }
            accountInfoDTOV3.setVisitCnt(accountInfoDto.getVisitCnt());
            accountInfoDTOV3.setRiskLevel(accountInfoDto.getRiskLevelName());
            if(DataUtil.isNotEmpty(accountInfoDto.getDataLabelName())){
                accountInfoDTOV3.setRspDataLabelList(Arrays.asList(accountInfoDto.getDataLabelName().split(",")).stream().filter(i-> i != null && !"null".equals(i) && !"".equals(i)).map(i->i.trim()).collect(Collectors.toList()));
            } else{
                accountInfoDTOV3.setRspDataLabelList(new ArrayList<>());
            }
            accountInfoDTOV3.setStaffMobile(accountInfoDto.getStaffMobile() == null? "" : accountInfoDto.getStaffMobile());
            accountInfoDTOV3.setStaffEmail(accountInfoDto.getStaffEmail() == null? "" : accountInfoDto.getStaffEmail());
            accountInfoDTOV3.setRelatedIpDistinctCnt(accountInfoDto.getRelatedIpDistinctCnt());
            if(DataUtil.isNotEmpty(accountInfoDto.getRiskInfo()) && DataUtil.isNotEmpty(accountInfoDto.getRiskInfo().getRiskNames())){
                accountInfoDTOV3.setRiskNames(accountInfoDto.getRiskInfo().getRiskNames());
            } else{
                accountInfoDTOV3.setRiskNames(new HashSet<>());
            }
//            accountInfoDTOV3.setMaxRspDataDistinctCnt(accountInfoDto.getMaxRspDataDistinctCnt());
//            accountInfoDTOV3.setMaxRspDataDistinctCntDate(accountInfoDto.getMaxRspDataDistinctCntDate());
            accountInfoDTOV3.setMaxRspDataDistinctCnt(0L);
            accountInfoDTOV3.setFirstDate(accountInfoDto.getFirstDate());
            accountInfoDTOV3.setLastDate(accountInfoDto.getLastDate());
            accountInfoDTOV3List.add(accountInfoDTOV3);
        }
        listOutputDto.setRows(accountInfoDTOV3List);
        listOutputDto.setTotalCount(accountInfoListFields.getTotalCount());
        return listOutputDto;
    }
}
