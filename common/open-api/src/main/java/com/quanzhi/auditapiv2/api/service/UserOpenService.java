package com.quanzhi.auditapiv2.api.service;

import com.quanzhi.auditapiv2.api.converter.SysRoleConverter;
import com.quanzhi.auditapiv2.common.dal.dataobject.SysRole;
import com.quanzhi.auditapiv2.common.dal.dataobject.SysUser;
import com.quanzhi.auditapiv2.common.dal.entity.ConstantInfo;
import com.quanzhi.auditapiv2.core.service.SysRoleService;
import com.quanzhi.auditapiv2.core.service.SysUserService;
import com.quanzhi.auditapiv2.openapi.sdk.dto.SysRoleDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.servlet.http.Cookie;
import javax.servlet.http.HttpSession;
import java.util.ArrayList;
import java.util.List;

@Slf4j
@Service
public class UserOpenService {
    private final SysUserService sysUserService;

    private final SysRoleService sysRoleService;

    public UserOpenService(SysUserService sysUserService, SysRoleService sysRoleService) {
        this.sysUserService = sysUserService;
        this.sysRoleService = sysRoleService;
    }

    /**
     * 获取当前系统角色信息
     * @return
     */
    public List<SysRoleDTO> getSysRole(){
        log.info("开发接口获取当前系统角色信息");
        List<SysRoleDTO> list = null;
        try {
            List<SysRole> data = sysRoleService.listAll();

            list = new ArrayList<>();
            for (SysRole sysRole : data) {

                SysRoleDTO sysRoleDTO = SysRoleConverter.INSTANCE.convert(sysRole);
                list.add(sysRoleDTO);
            }
        } catch (Exception e) {
            log.error("系统角色开放接口-角色全量查询", e);
        }

        return list;
    }


    /**
     * 开发接口根据用户名称获取用户信息
     * @param username
     * @return
     */
    public SysUser getUserDetail(String username) {
        log.info("开发接口根据用户名称获取用户信息");
        return sysUserService.getByUserName(username);
    }


    public SysUser getUserDetailByCookie(Cookie[] cookies) {
        log.info("开发接口根据用户名称获取用户信息");
        //TODO:根据cookie获取用户信息
        for (int i = 0; i < cookies.length; i++) {

        }
        String username = null;
        return sysUserService.getByUserName(username);
    }

    public SysUser getUserDetailByHttpSession(HttpSession httpSession) {
        log.info("开发接口根据用户名称获取用户信息");
        //TODO:根据httpSession获取用户信息

        String username = (String) httpSession.getAttribute(ConstantInfo.username);
        return sysUserService.getByUserName(username);
    }
    /**
     * 获取所有用户信息
     */
    public List<SysUser> getuserList() {
        return sysUserService.listAll();
    }
}
