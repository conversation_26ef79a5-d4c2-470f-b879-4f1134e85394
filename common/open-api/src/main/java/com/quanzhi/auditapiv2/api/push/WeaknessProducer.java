package com.quanzhi.auditapiv2.api.push;

import com.quanzhi.auditapiv2.api.domain.ProduceData;
import com.quanzhi.metabase.core.model.http.weakness.ApiWeakness;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/11/26 10:41 上午
 */
@Component
public class WeaknessProducer implements Producible<List<ApiWeakness>>{
    @Override
    public ProduceData<List<ApiWeakness>> produce(String topic, String index) {
        return null;
    }
}
