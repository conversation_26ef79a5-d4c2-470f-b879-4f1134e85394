package com.quanzhi.auditapiv2.api.domain.risk;

import com.quanzhi.auditapiv2.core.risk.dto.RiskInfoDto;
import com.quanzhi.auditapiv2.openapi.sdk.dto.risk.Entity;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * @author: yangzixian
 * @date: 2023.10.11 11:00
 * @description:
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class RiskDto {

    private String id;

    private Long findTimeMs;

    private String status;

    private String appUri;

    private List<String> appUriList;

    private String riskPolicyId;

    private String riskPolicyName;

    private String entityType;

    private String entityValue;

    private String level;

    private List<String> domainIds;

    private String riskId;

    private String depart;

    private String remark;

    private Long distinctSensiValueCnt;

    private Long updateTime;

    private Long createTime;

    private List<Entity> channels;

    private Long eventCnt;

    private Long dataDistinctCnt;

    private Long maxVisitCntPerMin;

    private List<RiskInfoDto.DataLabelDistinct> dataDistinctCntByDataLabel;

    /**
     * 涉及数据标签
     */
    private List<String> rspLabelList;

    private List<String> rspLabelListValue;

}
