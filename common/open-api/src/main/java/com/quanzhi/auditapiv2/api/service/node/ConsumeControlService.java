package com.quanzhi.auditapiv2.api.service.node;

import com.alibaba.fastjson.JSON;
import com.alibaba.nacos.api.annotation.NacosInjected;
import com.alibaba.nacos.api.config.ConfigService;
import com.quanzhi.audit_core.common.config.annotation.DynamicValue;
import lombok.Data;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.boot.context.event.ApplicationReadyEvent;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Service;

import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;

@Service
@Slf4j
public class ConsumeControlService {
    public static final String HANDLER_CONSUME_CONTROL = "handler.consume.control";
    public static final String HANDLER = "handler";
    @DynamicValue(dataId = HANDLER_CONSUME_CONTROL, groupId = HANDLER, typeClz = ConsumeCommand.class)
    @Setter
    private volatile ConsumeCommand consumeCommand;

    @NacosInjected
    @Setter
    private ConfigService configService;

    @EventListener(ApplicationReadyEvent.class)
    public void onEvent(ApplicationReadyEvent event) {
        Executors.newSingleThreadScheduledExecutor().schedule(() -> {
            try {
                String config = configService.getConfig(HANDLER_CONSUME_CONTROL, HANDLER, 5000);
                if (StringUtils.isEmpty(config)) {
                    consumeCommand = null;
                } else {
                    consumeCommand = JSON.parseObject(config, ConsumeCommand.class);
                }
            } catch (Exception e) {
                log.error("get config error", e);
            }
        }, 2, TimeUnit.MINUTES);
    }

    public boolean isStop() {
        if (consumeCommand == null) {
            return false;
        }
        return consumeCommand.isStop();
    }

    @Data
    public static final class ConsumeCommand {
        private long timestamp;
        private boolean stop;
    }
}
