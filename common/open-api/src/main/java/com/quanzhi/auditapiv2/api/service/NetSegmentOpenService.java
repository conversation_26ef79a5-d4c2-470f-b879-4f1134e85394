package com.quanzhi.auditapiv2.api.service;

import com.quanzhi.auditapiv2.common.util.dto.ListOutputDto;
import com.quanzhi.auditapiv2.openapi.sdk.dto.NetSegmentTopNDTO;
import com.quanzhi.auditapiv2.openapi.sdk.dto.TopNDTO;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.aggregation.Aggregation;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

/**
 * <AUTHOR>
 * @class NetSegmentOpenService
 * @created 2023/10/9 15:24
 * @desc
 **/
@Service
public class NetSegmentOpenService {

    private final MongoTemplate mongoTemplate;

    public NetSegmentOpenService(MongoTemplate mongoTemplate) {
        this.mongoTemplate = mongoTemplate;
    }

    public ListOutputDto<NetSegmentTopNDTO> getVisitCntTopN(@RequestBody TopNDTO topN) {
        List<NetSegmentTopNDTO> results = mongoTemplate.aggregate(Aggregation.newAggregation(
                Aggregation.unwind("accessDomainIds"),
                Aggregation.group("accessDomainIds").sum("visitCnt").as("visitCnt"),
                Aggregation.project("visitCnt").and("_id").as("netSegment"),
                Aggregation.sort(Sort.Direction.DESC, "visitCnt").and(Sort.Direction.ASC, "netSegment"),
                Aggregation.limit(topN.getTop())), "ipInfo", NetSegmentTopNDTO.class).getMappedResults();
        ListOutputDto<NetSegmentTopNDTO> dtos = new ListOutputDto<>();
        dtos.setRows(results);
        dtos.setTotalCount(results.size());

        return dtos;
    }


}
