package com.quanzhi.auditapiv2.api.service;

import com.quanzhi.metabase.core.model.http.weakness.ApiWeakness;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/10/27 9:31 上午
 */
public interface WeaknessPushService {

    void push(List<ApiWeakness> weaknesses);

    void onSuccess(String code, List<ApiWeakness> weaknesses);

    void onFail(String code, List<ApiWeakness> weaknesses, Throwable error);

}
