package com.quanzhi.auditapiv2.api.domain.home;

import com.quanzhi.auditapiv2.core.risk.dto.common.CommonSearchDto;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * @author: yangzixian
 * @date: 2023.10.11 10:37
 * @description:
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class HomeSearchDto extends CommonSearchDto {

    @ApiModelProperty(value = "日期列表", name = "dataList")
    private List<String> dateList;

}
