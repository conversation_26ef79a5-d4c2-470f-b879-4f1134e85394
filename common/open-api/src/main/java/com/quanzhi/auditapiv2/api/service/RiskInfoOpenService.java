package com.quanzhi.auditapiv2.api.service;

import com.quanzhi.auditapiv2.api.converter.RiskConverter;
import com.quanzhi.auditapiv2.biz.risk.service.RiskInfoService;
import com.quanzhi.auditapiv2.biz.risk.service.aggRisk.AggRiskService;
import com.quanzhi.auditapiv2.biz.risk.service.aggRisk.RiskV2Service;
import com.quanzhi.auditapiv2.common.dal.dto.aggRisk.AggRiskDto;
import com.quanzhi.auditapiv2.common.dal.dto.aggRisk.AggRiskOperatorDto;
import com.quanzhi.auditapiv2.common.dal.dto.aggRisk.RiskV2OperatorDto;
import com.quanzhi.auditapiv2.common.util.dto.ListOutputDto;
import com.quanzhi.auditapiv2.common.util.utils.ConstantUtil;
import com.quanzhi.auditapiv2.core.risk.dto.RiskInfoDto;
import com.quanzhi.auditapiv2.core.risk.dto.RiskV2Dto;
import com.quanzhi.auditapiv2.core.risk.dto.search.RiskSearchDto;
import com.quanzhi.auditapiv2.openapi.sdk.dto.Pageable;
import com.quanzhi.auditapiv2.openapi.sdk.dto.TimestampPageable;
import com.quanzhi.auditapiv2.openapi.sdk.dto.openapi.AggRiskDTOV5;
import com.quanzhi.auditapiv2.openapi.sdk.dto.openapi.RiskDTOV3;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

@Service
public class RiskInfoOpenService {

    private final RiskV2Service riskV2Service;

    private final AggRiskService aggRiskService;

    public RiskInfoOpenService(RiskV2Service riskV2Service, AggRiskService aggRiskService) {
        this.riskV2Service = riskV2Service;
        this.aggRiskService = aggRiskService;
    }

    public ListOutputDto<RiskDTOV3> getRiskOpenDTOV3(Pageable pageable) throws Exception {
        ListOutputDto<RiskV2Dto> listOutputDto = getRiskInfoList(pageable);
        List<RiskDTOV3> newRows = listOutputDto.getRows().stream().map(e -> RiskConverter.INSTANCE.convertOpenV4(e)).collect(Collectors.toList());
        for (RiskDTOV3 row : newRows) {
            if (row.getRemark() == null) {
                row.setRemark("");
            }
        }
        ListOutputDto<RiskDTOV3> riskOpenDtoListOutputDto = new ListOutputDto<>();
        riskOpenDtoListOutputDto.setRows(newRows);
        riskOpenDtoListOutputDto.setTotalCount(listOutputDto.getTotalCount());
        return riskOpenDtoListOutputDto;
    }

    public ListOutputDto<AggRiskDTOV5> getAggRiskOpenDTOV5(Pageable pageable) throws Exception {
        ListOutputDto<AggRiskDto> listOutputDto = getAggRiskInfoList(pageable);
        List<AggRiskDTOV5> newRows = listOutputDto.getRows().stream().map(e -> AggRiskDTOV5.AggRiskDTOV5Mapper.INSTANCE.convert(e)).collect(Collectors.toList());
        for (AggRiskDTOV5 row : newRows) {
            if (row.getRemark() == null) {
                row.setRemark("");
            }
        }
        ListOutputDto<AggRiskDTOV5> aggRiskOpenDtoListOutputDto = new ListOutputDto<>();
        aggRiskOpenDtoListOutputDto.setRows(newRows);
        aggRiskOpenDtoListOutputDto.setTotalCount(listOutputDto.getTotalCount());
        return aggRiskOpenDtoListOutputDto;
    }

    private ListOutputDto<RiskV2Dto> getRiskInfoList(Pageable pageable) throws Exception {

        RiskV2OperatorDto riskV2OperatorDto = new RiskV2OperatorDto();
        riskV2OperatorDto.setPage(pageable.getPage());
        riskV2OperatorDto.setLimit(pageable.getSize());
        riskV2OperatorDto.setSortField("firstTime");
        riskV2OperatorDto.setSort(ConstantUtil.Sort.DESC);

        if (((TimestampPageable) pageable).getCreateTimestamp() != 0) {
            riskV2OperatorDto.setFirstTimeStart(((TimestampPageable) pageable).getCreateTimestamp());
            riskV2OperatorDto.setFirstTimeEnd(System.currentTimeMillis() + 100);
        }
        if (((TimestampPageable) pageable).getUpdateTimestamp() != 0) {
            riskV2OperatorDto.setLastTimeStart(((TimestampPageable) pageable).getUpdateTimestamp());
            riskV2OperatorDto.setLastTimeEnd(System.currentTimeMillis() + 100);
        }

        ListOutputDto<RiskV2Dto> listOutputDto = riskV2Service.listRiskInfo(riskV2OperatorDto);

        return listOutputDto;

    }

    private ListOutputDto<AggRiskDto> getAggRiskInfoList(Pageable pageable) throws Exception {

        AggRiskOperatorDto aggRiskOperatorDto = new AggRiskOperatorDto();
        aggRiskOperatorDto.setPage(pageable.getPage());
        aggRiskOperatorDto.setLimit(pageable.getSize());
        aggRiskOperatorDto.setSortField("firstTime");
        aggRiskOperatorDto.setSort(ConstantUtil.Sort.DESC);

        if (((TimestampPageable) pageable).getCreateTimestamp() != 0) {
            aggRiskOperatorDto.setFirstTimeStart(((TimestampPageable) pageable).getCreateTimestamp());
            aggRiskOperatorDto.setFirstTimeEnd(System.currentTimeMillis() + 100);
        }
        if (((TimestampPageable) pageable).getUpdateTimestamp() != 0) {
            aggRiskOperatorDto.setLastTimeStart(((TimestampPageable) pageable).getUpdateTimestamp());
            aggRiskOperatorDto.setLastTimeEnd(System.currentTimeMillis() + 100);
        }

        ListOutputDto<AggRiskDto> listOutputDto = aggRiskService.listAggRisk(aggRiskOperatorDto);

        return listOutputDto;

    }

}

