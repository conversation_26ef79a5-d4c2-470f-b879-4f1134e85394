package com.quanzhi.auditapiv2.api.service;

import com.quanzhi.auditapiv2.common.util.dto.ListOutputDto;
import com.quanzhi.auditapiv2.openapi.sdk.dto.IPTopNDTO;
import com.quanzhi.auditapiv2.openapi.sdk.dto.TopNDTO;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @class IPOpenService
 * @created 2023/10/9 15:53
 * @desc
 **/
@Service
public class IPOpenService {

    private final MongoTemplate mongoTemplate;

    public IPOpenService(MongoTemplate mongoTemplate) {
        this.mongoTemplate = mongoTemplate;
    }

    public ListOutputDto<IPTopNDTO> getVisitCntTopN(TopNDTO topN) {
        List<IPTopNDTO> results = mongoTemplate.find(Query.query(new Criteria()).with(Sort.by(Sort.Direction.DESC, "visitCnt")
                                                                                             .and(Sort.by(Sort.Direction.ASC, "_id")))
                                                             .limit(topN.getTop()), IPTopNDTO.class, "ipInfo");
        return new ListOutputDto<>(results.size(), results);
    }
}
