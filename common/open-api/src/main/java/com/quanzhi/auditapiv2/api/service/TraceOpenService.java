package com.quanzhi.auditapiv2.api.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.quanzhi.auditapiv2.core.trace.dto.TaskRecord;
import com.quanzhi.auditapiv2.core.trace.dto.business.TraceTaskConfig;
import com.quanzhi.auditapiv2.core.trace.facade.TaskManagerFacade;
import com.quanzhi.metabase.core.model.ResourceMap;
import com.quanzhi.operate.operateHandler.IOperateHandlerService;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.Map;

@Service
public class TraceOpenService {

    private final OpenApiService openApiService;

    private final TaskManagerFacade taskManagerFacade;

    private final IOperateHandlerService operateHandlerService;

    private final MonitorLogOpenService  monitorLogOpenService;

    private final DataLabelOpenService dataLabelOpenService;

    private final ClickhouseEventDecryptService clickhouseEventDecryptService;

    private final String CREATE_TASK_URL = "/v1/create/clueTraceTaskFullLink";

    private final String CREATE_SUBJECT_TASK_URL = "/v1/create/subjectTraceTask";

    private final String GET_TASK_STATE_URL = "/v1/get/clueTraceTask";

    private final String SUBJECT_TASK_STATE_URL = "/v1/get/subjectTraceTask";

    private final String TRACE_LIST_EVENT = "/v1/query/clueTraceEventsFullLink";

    private final String SUBJECT_LIST_EVENT = "/v2/query/subjectTraceEventsFullLink";

    private final String TRACE_SUBJECT_LIST_EVENT = "/v1/query/subjectTraceEvents";

    public TraceOpenService(OpenApiService openApiService, TaskManagerFacade taskManagerFacade, IOperateHandlerService operateHandlerService, MonitorLogOpenService monitorLogOpenService, DataLabelOpenService dataLabelOpenService, ClickhouseEventDecryptService clickhouseEventDecryptService) {
        this.openApiService = openApiService;
        this.taskManagerFacade = taskManagerFacade;
        this.operateHandlerService = operateHandlerService;
        this.monitorLogOpenService = monitorLogOpenService;
        this.dataLabelOpenService = dataLabelOpenService;
        this.clickhouseEventDecryptService = clickhouseEventDecryptService;
    }

    public Map<String,Object> create(JSONObject reqBody) throws Exception {
        Map<String,Object> result = new HashMap<>();
        JSON json = openApiService.replaceReqArgs(reqBody, CREATE_TASK_URL);
        TraceTaskConfig taskConfig = JSON.toJavaObject(json, TraceTaskConfig.class);
        TaskRecord taskRecord = taskManagerFacade.commitTraceTaskWrapper(taskConfig,null);
        result.put("clueTraceTaskId",taskRecord.getTaskId());
        return result;
    }

    public Map<String,Object> getState(JSONObject reqBody) throws Exception {
        return getState(reqBody,GET_TASK_STATE_URL);
    }

    public Map<String,Object> listEvent(JSONObject reqBody) throws Exception {
        return listEvent(reqBody,TRACE_LIST_EVENT);
    }

    public Map<String,Object> createSubjectTraceTask(JSONObject reqBody) throws Exception{
        return create(reqBody,CREATE_SUBJECT_TASK_URL,"subjectTraceTaskId");
    }

    public Map<String,Object> getSubjectTraceState(JSONObject reqBody) throws Exception{
        return getState(reqBody,SUBJECT_TASK_STATE_URL);
    }

    public Map<String,Object> listSubjectTraceEvent(JSONObject reqBody) throws Exception{
        return listEvent(reqBody,TRACE_SUBJECT_LIST_EVENT);
    }
    public Map<String,Object> listEvent(JSONObject reqBody, String url) throws Exception{
        Map<String,Object> map = new HashMap<>();
        Map<String,Object> result = monitorLogOpenService.getActionFrontResult(reqBody,url);
        if (url.equals(TRACE_LIST_EVENT) || SUBJECT_LIST_EVENT.equals(url)){
            // evidenceInfo解密
            Object object = clickhouseEventDecryptService.decryptEvent(result);
            long totalCount = Long.parseLong(result.get("totalCount").toString());
            map.put("totalCnt",totalCount);
            map.put("rows",object != null ? object : new ArrayList<>());
        }else if (url.equals(TRACE_SUBJECT_LIST_EVENT)){
            Map<String, String> fieldName = new HashMap<>();
            fieldName.put("reqDataLabelIds","reqDataLabels");
            fieldName.put("rspDataLabelIds","rspDataLabels");
            dataLabelOpenService.transfer(result,"map",fieldName);
            long totalCount = Long.parseLong(result.get("totalCount").toString());
            map.put("totalCnt",totalCount);
            map.put("rows",result.get("rows"));
        }
        return map;
    }

    public Map<String,Object> getState(JSONObject reqBody,String url) throws Exception {
        Map<String,Object> result = new HashMap<>();
        Map<String, Object> actionFrontResult = monitorLogOpenService.getActionFrontResult(reqBody, url);
        result.put("taskStatus",((ResourceMap)actionFrontResult.get("taskDetail")).get("taskStatus"));
        return result;
    }

    public Map<String,Object> create(JSONObject reqBody,String url,String resultKey) throws Exception {
        Map<String,Object> result = new HashMap<>();
        JSON json = openApiService.replaceReqArgs(reqBody, url);
        TraceTaskConfig taskConfig = JSON.toJavaObject(json, TraceTaskConfig.class);
        TaskRecord taskRecord = taskManagerFacade.commitTraceTaskWrapper(taskConfig, null);
        result.put(resultKey,taskRecord.getTaskId());
        return result;
    }

    public Map<String, Object> listSubjectEvent(JSONObject reqBody) throws Exception {
        return listEvent(reqBody,SUBJECT_LIST_EVENT);
    }
}
