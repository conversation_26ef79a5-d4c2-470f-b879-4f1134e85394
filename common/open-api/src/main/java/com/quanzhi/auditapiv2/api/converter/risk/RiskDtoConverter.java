package com.quanzhi.auditapiv2.api.converter.risk;

import com.alibaba.fastjson.JSON;
import com.quanzhi.auditapiv2.api.domain.risk.RiskDto;
import com.quanzhi.auditapiv2.api.domain.risk.RiskPolicyDto;
import com.quanzhi.auditapiv2.core.risk.dto.RiskInfoDto;
import com.quanzhi.auditapiv2.core.risk.dto.common.GroupDataDto;
import com.quanzhi.auditapiv2.core.risk.entity.RiskPolicy;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * @author: yangzixian
 * @date: 2023.10.11 09:57
 * @description:
 */
@Mapper
public interface RiskDtoConverter {

    RiskDtoConverter INSTANCE = Mappers.getMapper(RiskDtoConverter.class);

    @Mapping(source = "id", target = "riskId")
    @Mapping(source = "firstTime", target = "findTimeMs")
    @Mapping(source = "firstTime", target = "createTime")
    @Mapping(source = "lastTime", target = "updateTime")
    @Mapping(source = "levelName", target = "level")
    @Mapping(source = "stateName", target = "status")
    RiskDto convert(RiskInfoDto riskInfoDto);

}
