package com.quanzhi.auditapiv2.config;

import com.quanzhi.auditapiv2.core.service.config.KafkaConfig;
import org.apache.kafka.clients.producer.KafkaProducer;
import org.springframework.context.annotation.Bean;
import org.springframework.kafka.annotation.EnableKafka;
import org.springframework.kafka.core.DefaultKafkaProducerFactory;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.kafka.core.ProducerFactory;
import org.springframework.stereotype.Component;

import java.util.Map;
import java.util.Properties;

@Component
public class TyyKafkaConfig {

    private final KafkaConfig kafkaConfig;

    private KafkaProducer kafkaProducer = null;

    public TyyKafkaConfig(KafkaConfig kafkaConfig) {
        this.kafkaConfig = kafkaConfig;

    }

    public KafkaProducer getKafkaProducer() {
        if (kafkaProducer == null) {
            kafkaProducer = new KafkaProducer<String, String>(producerConfigs());
        }
        return kafkaProducer;
    }

//    public KafkaTemplate<Integer, String> tyyKafkaTemplate() {
//        return new KafkaTemplate<>(producerFactory());
//    }
//
//    private ProducerFactory<Integer, String> producerFactory() {
//        return new DefaultKafkaProducerFactory<>(producerConfigs());
//    }

    private Properties producerConfigs() {
        return kafkaConfig.getKafkaProducerConfig();
    }
}
