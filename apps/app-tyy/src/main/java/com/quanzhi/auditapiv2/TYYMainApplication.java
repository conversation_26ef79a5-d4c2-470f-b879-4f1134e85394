package com.quanzhi.auditapiv2;

import com.alibaba.nacos.spring.context.annotation.config.NacosPropertySource;
import com.alibaba.nacos.spring.context.annotation.config.NacosPropertySources;
import com.quanzhi.audit.mix.config.MixConfigService;
import com.quanzhi.audit.mix.config.spring.EnableMixConfig;
import com.quanzhi.audit.mix.schdule.ScheduleConfiguration;
import com.quanzhi.audit.mix.schdule.application.annotation.EnableLockedScheduling;
import com.quanzhi.auditapiv2.core.service.config.BizModuleProperties;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.EnableAspectJAutoProxy;
import org.springframework.context.annotation.FilterType;

/**
 * @Author: ian
 * @Date: 2024/06/18
 * @Description:
 */
//@SpringBootApplication(exclude = {DataSourceAutoConfiguration.class, HibernateJpaAutoConfiguration.class, SecurityAutoConfiguration.class})
@SpringBootApplication //(exclude = JasyptSpringBootAutoConfiguration.class)
@ComponentScan(value = {"com.quanzhi.auditapiv2", "com.quanzhi.audit_core.extract",
        "com.quanzhi.datataskcore", "com.quanzhi.audit_core.common.config",
        "com.quanzhi.plugincore", "com.quanzhi.operate","com.quanzhi.audit",
        "com.quanzhi.auditapiv2.report.data","com.quanzhi.audit_core.resource.fetcher.client","com.quanzhi.login","com.java3y.austin.service"},
        excludeFilters = {
                @ComponentScan.Filter(type = FilterType.ASSIGNABLE_TYPE,
                        value = {
                                ScheduleConfiguration.class,
                                MainApplication.class
                        }
                )
        })
//                    pattern = {
//                    "com.quanzhi.auditapiv2.MainApplication",
////                    "com.quanzhi.datataskcore.core.service.config.AsyncConfig"
//            }
//            )
@NacosPropertySources({
        @NacosPropertySource(
                dataId = MixConfigService.DATA_ID,
                groupId = MixConfigService.GROUP_ID
        ),
        @NacosPropertySource(
                dataId = BizModuleProperties.DATA_ID,
                groupId = BizModuleProperties.GROUP, autoRefreshed = true
        ),
        @NacosPropertySource(dataId = "auditapiv2.bootstrap.properties", groupId = "auditapiv2", autoRefreshed = true, first = true)
})
@EnableMixConfig
@EnableLockedScheduling
@EnableAspectJAutoProxy
public class TYYMainApplication {
    public static void main(String[] args) {
        SpringApplication application = new SpringApplication(TYYMainApplication.class);
        application.setAddCommandLineProperties(true);
        application.run(args);
    }
}
