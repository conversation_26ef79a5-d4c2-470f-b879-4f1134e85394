package com.quanzhi.auditapiv2.dto.web;

import lombok.Data;

@Data
public class TYYApiDetailsDto {
    private int syncType;
    private String apiId;
    private String apiUrl;
    private String host;
    private String appName;
    private String level;
    private String businessDepartment;
    private String apiRiskLevelName;
    private int totalVisits;
    private int accountCount;
    private String flowSources;
    private String maxReqLabelValueCount;
    private String methods;
    private String state;
    private String remark;
    private int weaknessCount;
    private String weaknessNames;
    private int riskCount;
    private String riskNames;
    private String apiLifeFlag;
    private String featureLabels;
    private String apiFormats;
    private String visitDomains;
    private String deployDomains;
    private String terminals;
    private String reqContentTypes;
    private String rspContentTypes;
    private String reqDataLabels;
    private String rspDataLabels;
    private String apiTypeName;
    private long discoverTime;
    private long activeTime;
    private TYYSampleRequest sampleReq;
    private TYYSampleResponse sampleRsp;
    private String notifyIp;
}
