package com.quanzhi.auditapiv2.web.aop;

import com.alibaba.fastjson.JSON;
import com.alibaba.nacos.api.config.annotation.NacosValue;
import com.quanzhi.auditapiv2.common.dal.dao.ISampleEventDao;
import com.quanzhi.auditapiv2.common.dal.dto.ApiWeaknessDto;
import com.quanzhi.auditapiv2.common.dal.enums.WeaknessLevelEnum;
import com.quanzhi.auditapiv2.common.util.utils.DataUtil;
import com.quanzhi.auditapiv2.common.util.utils.DateUtil;
import com.quanzhi.auditapiv2.config.TyyKafkaConfig;
import com.quanzhi.auditapiv2.core.model.kafkaConfiguration.KafkaConfiguration;
import com.quanzhi.auditapiv2.core.model.kafkaConfiguration.ProducerConfig;
import com.quanzhi.auditapiv2.core.risk.entity.RiskInfo;
import com.quanzhi.auditapiv2.core.service.manager.dto.send.AlertEventDto;
import com.quanzhi.auditapiv2.core.service.manager.web.IApiWeaknessService;
import com.quanzhi.auditapiv2.core.service.manager.web.impl.HttpApiService;
import com.quanzhi.auditapiv2.dto.web.*;
import com.quanzhi.auditapiv2.utils.sm4.SM4Coder;
import com.quanzhi.metabase.core.model.enums.ApiRiskLevelEnum;
import com.quanzhi.metabase.core.model.enums.ApiTypeEnum;
import com.quanzhi.metabase.core.model.event.EventType;
import com.quanzhi.metabase.core.model.event.ResourceChangedEvent;
import com.quanzhi.metabase.core.model.http.HttpApiResource;
import com.quanzhi.metabase.core.model.http.HttpApiSample;
import com.quanzhi.metabase.core.model.http.HttpResourceConstant;
import org.apache.kafka.clients.producer.KafkaProducer;
import org.apache.kafka.clients.producer.ProducerRecord;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.UUID;

import static com.quanzhi.auditapiv2.core.model.kafkaConfiguration.ProducerConfig.producerConfigMap;

@Aspect
@Component
public class ResourceChangeEventHandleAop {
    private Logger logger = LoggerFactory.getLogger(ResourceChangeEventHandleAop.class);

    private final TyyKafkaConfig tyyKafkaConfig;

    private final HttpApiService httpApiService;

    private final ISampleEventDao sampleEventDao;

    private final IApiWeaknessService apiWeaknessService;

    public ResourceChangeEventHandleAop(HttpApiService httpApiService, ISampleEventDao sampleEventDao, IApiWeaknessService apiWeaknessService, TyyKafkaConfig tyyKafkaConfig) {
        this.httpApiService = httpApiService;
        this.sampleEventDao = sampleEventDao;
        this.apiWeaknessService = apiWeaknessService;
        this.tyyKafkaConfig = tyyKafkaConfig;
    }

    @NacosValue(value = "${tyyv2.kafka.ip:kafka-server}", autoRefreshed = true)
    private String kafkaIP;

    @NacosValue(value = "${tyyv2.kafka.port:9093}", autoRefreshed = true)
    private String kafkaPort;

    @NacosValue(value = "${tyyv2.sm4.key:d4f8e2b7c6a1d4b3}", autoRefreshed = true)
    private String sm4Key;

    @NacosValue(value = "${tyyv2.kafka.apiTopic:qz-asset-api}", autoRefreshed = true)
    private String kafkaApiTopic;

    @NacosValue(value = "${tyyv2.kafka.apiWeaknessTopic:api-vulnerability}", autoRefreshed = true)
    private String apiWeaknessTopic;

    @NacosValue(value = "${tyyv2.kafka.apiRiskTopic:api-risk}", autoRefreshed = true)
    private String apiRiskTopic;

    @Pointcut("execution(public * com.quanzhi.auditapiv2.core.service.manager.web.impl.ResourceChangeEventHandleServiceImpl.handle(com.quanzhi.metabase.core.model.event.ResourceChangedEvent))")
    public void handleResourceChangeEvent() {
    }

    @Pointcut("execution(public * com.quanzhi.auditapiv2.core.service.manager.web.impl.ResourceChangeEventHandleServiceImpl.handle(com.quanzhi.auditapiv2.core.risk.entity.RiskInfo))")
    public void handleRiskInfo() {
    }

    @Around("handleResourceChangeEvent()")
    public Object toHandleResourceChangeEvent(ProceedingJoinPoint point) {
        Object result = null;
        try {
            Object[] args = point.getArgs();
            ResourceChangedEvent resourceChangedEvent = (ResourceChangedEvent) args[0];

            // report when the api is updated
            if (HttpResourceConstant.API.equals(resourceChangedEvent.getResourceType())) {
                handleApiChange(resourceChangedEvent);
            }

            // report when the api weakness is updated
            if (HttpResourceConstant.API_WEAKNESS.equals(resourceChangedEvent.getResourceType())) {
                handleApiWeaknessChange(resourceChangedEvent);
            }

            result = point.proceed(args);
        } catch (Throwable e) {
            logger.error("资源变更事件前置处理异常：", e);
        } finally {
            try {
                result = point.proceed(point.getArgs());
            } catch (Throwable e) {
                logger.error("资源变更事件前置处理异常: ", e);
            }
        }
        return result;
    }

    // 3.1.15
    @Around("handleRiskInfo()")
    public Object toHandleRiskInfo(ProceedingJoinPoint point) {
        Object result = null;
        try {
            Object[] args = point.getArgs();
            RiskInfo riskInfo = (RiskInfo) args[0];
            HttpApiResource httpApiResource = httpApiService.getHttpApiByUri(riskInfo.getApiUri());

            List<TYYRiskInfoDto> riskInfoDtos = new ArrayList<>();
            TYYRiskInfoDto riskInfoDto = new TYYRiskInfoDto();
            riskInfoDto.setSyncType(EventType.NewResourceEvent.ordinal());
            riskInfoDto.setRiskId(riskInfo.getRiskId());
            riskInfoDto.setRiskRemark(DataUtil.isEmpty(riskInfo.getRiskMark()) ? "" : riskInfo.getRiskMark().toString());
            riskInfoDto.setApiId(httpApiResource.getId());
            riskInfoDto.setApiUrl(httpApiResource.getApiUrl());
            riskInfoDto.setLevel(AlertEventDto.RiskLevel.getLevelName(riskInfo.getLevel()));
            riskInfoDto.setState(RiskInfo.RiskStateEnum.getRiskStateEnum(riskInfo.getState()).getName());
            riskInfoDto.setBusinessDepartment(DataUtil.isEmpty(httpApiResource.getDepartments()) ? "" : httpApiResource.getDepartments().toString());
            riskInfoDto.setName(DataUtil.isEmpty(riskInfo.getPolicySnapshot()) ? "" : riskInfo.getPolicySnapshot().getName());
            riskInfoDto.setType(DataUtil.isEmpty(riskInfo.getPolicySnapshot()) ? "" : riskInfo.getPolicySnapshot().getType());
            riskInfoDto.setRiskSubject(DataUtil.isEmpty(riskInfo.getEntities()) ? "" : riskInfo.getEntities().get(0).getValue());
            riskInfoDto.setAttackSuccessName(DataUtil.isEmpty(riskInfo.getAttackSuccess()) ? "" : riskInfo.getAttackSuccess().toString());
            riskInfoDto.setAttackCount(DataUtil.isEmpty(riskInfo.getAttackCount()) ? 0 : riskInfo.getAttackCount().intValue());
            riskInfoDto.setRspDataDistinctCnt(DataUtil.isEmpty(riskInfo.getRspDataDistinctCnt()) ? 0 : riskInfo.getRspDataDistinctCnt().intValue());
            riskInfoDto.setRspLabelList(DataUtil.isEmpty(riskInfo.getRspLabelList()) ? "" : riskInfo.getRspLabelList().toString());
            riskInfoDto.setRemark(riskInfo.getRemark());
            riskInfoDto.setFirstTime(riskInfo.getFirstTime());
            riskInfoDto.setLastTime(riskInfo.getLastTime());
            riskInfoDto.setNotifyIp(System.getenv("KUBE_HOST_IP"));


            riskInfoDtos.add(riskInfoDto);

            TYYReportCommonDto report = new TYYReportCommonDto();
            report.setBatchId(UUID.randomUUID().toString());
            report.setInterfaceCode(1015);
            report.setSystemCode(10200);
            report.setSendTimestamp(DateUtil.currentTimestamp());
            // encrypt data
            String dataListStr = "\"dataList\":" + JSON.toJSONString(riskInfoDtos);
            SM4Coder sm4Coder = new SM4Coder();
            sm4Coder.setSecretKey(sm4Key);
            String encryptData = sm4Coder.encryptData_ECB(dataListStr);
            report.setEncryptData(encryptData);

            KafkaProducer kafkaProducer = getKafkaProducer();
            ProducerRecord<String, ?> producerRecord = new ProducerRecord(apiRiskTopic,JSON.toJSONString(report));
            kafkaProducer.send(producerRecord);

            result = point.proceed(args);
        } catch (Throwable e) {
            logger.error("风险事件前置处理异常：", e);
        } finally {
            try {
                result = point.proceed(point.getArgs());
            } catch (Throwable e) {
                logger.error("风险事件前置处理异常: ", e);
            }
        }
        return result;
    }

    private KafkaProducer getKafkaProducer() {
//        String kafkaIpPort = kafkaIP + ":" + kafkaPort;
//        if (DataUtil.isEmpty(kafkaTemplate)) {
//            producerConfigMap.put(ProducerConfig.BOOTSTRAP_SERVERS_CONFIG, kafkaIpPort);
//            kafkaTemplate = KafkaConfiguration.getKafkaTemplate();
//        }

        return tyyKafkaConfig.getKafkaProducer();
    }

    private TYYSampleRequest getSampleRequest(HttpApiSample httpApiSample) {
        TYYSampleRequest request = new TYYSampleRequest();
        request.setBody(DataUtil.isEmpty(httpApiSample.getReq().getBody()) ? "" : httpApiSample.getReq().getBody());
        request.setCookies(DataUtil.isEmpty(httpApiSample.getReq().getCookies()) ? "" : httpApiSample.getReq().getCookies().toString());
        request.setHeader(DataUtil.isEmpty(httpApiSample.getReq().getHeader()) ? "" : httpApiSample.getReq().getHeader().toString());
        request.setHttpVersion(DataUtil.isEmpty(httpApiSample.getReq().getHttpVersion()) ? "" : httpApiSample.getReq().getHttpVersion());
        request.setMethod(DataUtil.isEmpty(httpApiSample.getReq().getMethod()) ? "" : httpApiSample.getReq().getMethod());
        request.setPostArgs(DataUtil.isEmpty(httpApiSample.getReq().getPostArgs()) ? "" : httpApiSample.getReq().getPostArgs().toString());
        request.setRemoteAddr(DataUtil.isEmpty(httpApiSample.getReq().getRemoteAddr()) ? "" : httpApiSample.getReq().getRemoteAddr());
        request.setUrl(httpApiSample.getUrl());

        return request;
    }

    private TYYSampleResponse getSampleResponse(HttpApiSample httpApiSample) {
        TYYSampleResponse response = new TYYSampleResponse();
        response.setBody(DataUtil.isEmpty(httpApiSample.getRsp().getBody()) ? "" : httpApiSample.getRsp().getBody());
        response.setHeader(DataUtil.isEmpty(httpApiSample.getRsp().getHeader()) ? "" : httpApiSample.getRsp().getHeader().toString());
        response.setHttpVersion(DataUtil.isEmpty(httpApiSample.getRsp().getHttpVersion()) ? "" : httpApiSample.getRsp().getHttpVersion());
        response.setSetCookies(DataUtil.isEmpty(httpApiSample.getRsp().getSetCookies()) ? "" : httpApiSample.getRsp().getSetCookies().toString());
        response.setStatus(DataUtil.isEmpty(httpApiSample.getRsp().getStatus()) ? "" : httpApiSample.getRsp().getStatus());

        return response;
    }

    // 3.1.13
    private void handleApiChange(ResourceChangedEvent resourceChangedEvent) {
        try {
            HttpApiResource httpApiResource = httpApiService.getHttpApiByUri(resourceChangedEvent.getUri());
            List<HttpApiSample> sampleEventList = sampleEventDao.getSampleEventList(resourceChangedEvent.getUri(), 1, 1);
            HttpApiSample httpApiSample = DataUtil.isNotEmpty(sampleEventList) ? sampleEventList.get(0) : null;

            List<TYYApiDetailsDto> apiDetailsDtos = new ArrayList<>();
            if (DataUtil.isNotEmpty(httpApiResource)) {
                TYYApiDetailsDto apiDetailsDto = new TYYApiDetailsDto();
                apiDetailsDto.setSyncType(resourceChangedEvent.getEventType().ordinal());
                apiDetailsDto.setApiId(httpApiResource.getId());
                apiDetailsDto.setApiUrl(httpApiResource.getApiUrl());
                apiDetailsDto.setHost(httpApiResource.getHost());
                apiDetailsDto.setAppName(httpApiResource.getAppName());
                apiDetailsDto.setLevel(httpApiResource.getLevel());
                apiDetailsDto.setBusinessDepartment(DataUtil.isEmpty(httpApiResource.getDepartments()) ? "" : httpApiResource.getDepartments().toString());
                apiDetailsDto.setApiRiskLevelName(DataUtil.isEmpty(httpApiResource.getApiRiskLevel()) ? "" : ApiRiskLevelEnum.getApiRiskLevelEnum(httpApiResource.getApiRiskLevel()).getName());
                apiDetailsDto.setTotalVisits(DataUtil.isEmpty(httpApiResource.getApiStat().getTotalVisits()) ? 0 : httpApiResource.getApiStat().getTotalVisits().intValue());
                apiDetailsDto.setFlowSources(DataUtil.isEmpty(httpApiResource.getFlowSources()) ? "" : httpApiResource.getFlowSources().toString());
                apiDetailsDto.setMaxReqLabelValueCount(DataUtil.isEmpty(httpApiResource.checkStat().getMaxReqLabelValueCount()) ? "0" : String.valueOf(httpApiResource.checkStat().getMaxReqLabelValueCount()));
                apiDetailsDto.setMethods(DataUtil.isEmpty(httpApiResource.getMethods()) ? "" : httpApiResource.getMethods().toString());
                apiDetailsDto.setState(DataUtil.isEmpty(httpApiResource.getState()) ? "" : httpApiResource.getState().value());
                apiDetailsDto.setRemark(DataUtil.isEmpty(httpApiResource.getRemark()) ? "" : httpApiResource.getRemark());
                apiDetailsDto.setWeaknessCount(DataUtil.isEmpty(httpApiResource.getBriefWeaknesses().getCount()) ? 0 : httpApiResource.getBriefWeaknesses().getCount());
                apiDetailsDto.setWeaknessNames(DataUtil.isEmpty(httpApiResource.getApiStat().getWeaknessNames()) ? "" : httpApiResource.getApiStat().getWeaknessNames().toString());
                apiDetailsDto.setRiskCount(DataUtil.isEmpty(httpApiResource.getBriefRiskInfo().getCount()) ? 0 : httpApiResource.getBriefRiskInfo().getCount());
                apiDetailsDto.setRiskNames(DataUtil.isEmpty(httpApiResource.getApiStat().getRiskNames()) ? "" : httpApiResource.getApiStat().getRiskNames().toString());
                apiDetailsDto.setApiLifeFlag(DataUtil.isEmpty(httpApiResource.getApiLifeFlag()) ? "" : httpApiResource.getApiLifeFlag().toString());
                apiDetailsDto.setFeatureLabels(DataUtil.isEmpty(httpApiResource.getFeatureLabels()) ? "" : httpApiResource.getFeatureLabels().toString());
                apiDetailsDto.setApiFormats(DataUtil.isEmpty(httpApiResource.getApiFormats()) ? "" : httpApiResource.getApiFormats().toString());
                apiDetailsDto.setVisitDomains(DataUtil.isEmpty(httpApiResource.getVisitDomains()) ? "" : httpApiResource.getVisitDomains().toString());
                apiDetailsDto.setDeployDomains(DataUtil.isEmpty(httpApiResource.getDeployDomains()) ? "" : httpApiResource.getDeployDomains().toString());
                apiDetailsDto.setTerminals(DataUtil.isEmpty(httpApiResource.getTerminals()) ? "" : httpApiResource.getTerminals().toString());
                apiDetailsDto.setReqContentTypes(DataUtil.isEmpty(httpApiResource.getReqContentTypes()) ? "" : httpApiResource.getReqContentTypes().toString());
                apiDetailsDto.setRspContentTypes(DataUtil.isEmpty(httpApiResource.getRspContentTypes()) ? "" : httpApiResource.getRspContentTypes().toString());
                apiDetailsDto.setReqDataLabels(DataUtil.isEmpty(httpApiResource.getReqDataLabels()) ? "" : httpApiResource.getReqDataLabels().toString());
                apiDetailsDto.setApiTypeName(DataUtil.isEmpty(httpApiResource.getApiType()) ? "" : ApiTypeEnum.getApiTypeEnum(httpApiResource.getApiType()).getName());
                apiDetailsDto.setDiscoverTime(httpApiResource.getDiscoverTime());
                if (DataUtil.isNotEmpty(httpApiSample)) {
                    apiDetailsDto.setSampleReq(getSampleRequest(httpApiSample));
                    apiDetailsDto.setSampleRsp(getSampleResponse(httpApiSample));
                }
                apiDetailsDto.setNotifyIp(System.getenv("KUBE_HOST_IP"));

                apiDetailsDtos.add(apiDetailsDto);
            }

            TYYReportCommonDto report = new TYYReportCommonDto();
            report.setBatchId(UUID.randomUUID().toString());
            report.setInterfaceCode(1013);
            report.setSystemCode(10200);
            report.setSendTimestamp(DateUtil.currentTimestamp());
            // encrypt data
            String dataListStr = "\"dataList\":" + JSON.toJSONString(apiDetailsDtos);
            SM4Coder sm4Coder = new SM4Coder();
            sm4Coder.setSecretKey(sm4Key);
            String encryptData = sm4Coder.encryptData_ECB(dataListStr);
            report.setEncryptData(encryptData);

            KafkaProducer kafkaProducer = getKafkaProducer();
            ProducerRecord<String, ?> producerRecord = new ProducerRecord(kafkaApiTopic,JSON.toJSONString(report));
            kafkaProducer.send(producerRecord);
        } catch (Exception e) {
            logger.error("资源变更事件前置处理异常(Api)：", e);
        }
    }

    private int getSyncType(int eventType) {
        switch (eventType) {
            case 0:
                return 1;
            case 1:
                return 2;
            case 4:
                return 3;
            default:
                return 1;
        }
    }

    // 3.1.14
    private void handleApiWeaknessChange(ResourceChangedEvent resourceChangedEvent) {
        try {
            Map<String, Object> playLoad = (Map) resourceChangedEvent.getPayload();
            String apiId = (String) playLoad.get("id");
            List<HttpApiSample> sampleEventList = sampleEventDao.getSampleEventList(resourceChangedEvent.getUri(), 1, 1);
            HttpApiSample httpApiSample = DataUtil.isNotEmpty(sampleEventList) ? sampleEventList.get(0) : null;


            ApiWeaknessDto weaknessDto = apiWeaknessService.getApiWeaknessById(apiId);
            List<TYYApiWeaknessDto> apiWeaknessDtos = new ArrayList<>();
            if (DataUtil.isNotEmpty(weaknessDto)) {
                HttpApiResource httpApiResource = httpApiService.getHttpApiByUri(resourceChangedEvent.getUri());
                TYYApiWeaknessDto apiWeaknessDto = new TYYApiWeaknessDto();

                apiWeaknessDto.setWeaknessId(weaknessDto.getWeaknessId());
                apiWeaknessDto.setApiId(apiId);
                apiWeaknessDto.setSyncType(getSyncType(resourceChangedEvent.getEventType().ordinal()));
                apiWeaknessDto.setApiUrl(httpApiResource.getApiUrl());
                apiWeaknessDto.setHost(httpApiResource.getHost());
                apiWeaknessDto.setAppName(httpApiResource.getAppName());
                apiWeaknessDto.setLevel(DataUtil.isEmpty(apiWeaknessDto.getLevel()) ? "" : WeaknessLevelEnum.getName(Integer.valueOf(apiWeaknessDto.getLevel())));
                apiWeaknessDto.setName(weaknessDto.getName());
                apiWeaknessDto.setType(weaknessDto.getType());
                apiWeaknessDto.setState(weaknessDto.getState().value());
                apiWeaknessDto.setBusinessDepartment(DataUtil.isEmpty(httpApiResource.getDepartments()) ? "" : httpApiResource.getDepartments().toString());
                apiWeaknessDto.setSolution(weaknessDto.getSolution());
                apiWeaknessDto.setFeatureLabels(DataUtil.isEmpty(httpApiResource.getFeatureLabels()) ? "" : httpApiResource.getFeatureLabels().toString());
                apiWeaknessDto.setMethods(DataUtil.isEmpty(httpApiResource.getMethods()) ? "" : httpApiResource.getMethods().toString());
                apiWeaknessDto.setMaxRspLabelValueCount(DataUtil.isEmpty(httpApiResource.checkStat().getMaxReqLabelValueCount()) ? 0 : httpApiResource.checkStat().getMaxReqLabelValueCount());
                apiWeaknessDto.setReqDataLabels(DataUtil.isEmpty(httpApiResource.getReqDataLabels()) ? "" : httpApiResource.getReqDataLabels().toString());
                apiWeaknessDto.setRspDataLabels(DataUtil.isEmpty(httpApiResource.getRspDataLabels()) ? "" : httpApiResource.getRspDataLabels().toString());
                apiWeaknessDto.setDeployDomains(DataUtil.isEmpty(httpApiResource.getDeployDomains()) ? "" : httpApiResource.getDeployDomains().toString());
                apiWeaknessDto.setTerminals(DataUtil.isEmpty(httpApiResource.getTerminals()) ? "" : httpApiResource.getTerminals().toString());
                apiWeaknessDto.setOperationId(weaknessDto.getOperationId());
                apiWeaknessDto.setLastTimestamp(weaknessDto.getLastTimestamp());
                apiWeaknessDto.setEarlyTimestamp(weaknessDto.getEarlyTimestamp());
                if (!DataUtil.isEmpty(weaknessDto.getHttpApiSampleDetail())) {
                    apiWeaknessDto.setRspContentTypes(DataUtil.isEmpty(weaknessDto.getHttpApiSampleDetail().getRspContentType()) ? "" : weaknessDto.getHttpApiSampleDetail().getRspContentType());
                }
                if (DataUtil.isNotEmpty(httpApiSample)) {
                    apiWeaknessDto.setSampleReq(getSampleRequest(httpApiSample));
                    apiWeaknessDto.setSampleRsp(getSampleResponse(httpApiSample));
                }
                apiWeaknessDto.setNotifyIp(System.getenv("KUBE_HOST_IP"));

                apiWeaknessDtos.add(apiWeaknessDto);

                TYYReportCommonDto report = new TYYReportCommonDto();
                report.setBatchId(UUID.randomUUID().toString());
                report.setInterfaceCode(1014);
                report.setSystemCode(10200);
                report.setSendTimestamp(DateUtil.currentTimestamp());
                // encrypt data
                String dataListStr = "\"dataList\":" + JSON.toJSONString(apiWeaknessDtos);
                SM4Coder sm4Coder = new SM4Coder();
                sm4Coder.setSecretKey(sm4Key);
                String encryptData = sm4Coder.encryptData_ECB(dataListStr);
                report.setEncryptData(encryptData);

                KafkaProducer kafkaProducer = getKafkaProducer();
                ProducerRecord<String, ?> producerRecord = new ProducerRecord(apiWeaknessTopic,JSON.toJSONString(report));
                kafkaProducer.send(producerRecord);
            }
        } catch (Exception e) {
            logger.error("资源变更事件前置处理异常(Api Weakness)：", e);
        }
    }
}
