package com.quanzhi.auditapiv2.biz.schedule.job;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.nacos.api.config.annotation.NacosValue;
import com.quanzhi.audit.mix.schdule.application.annotation.LockedScheduler;
import com.quanzhi.auditapiv2.common.util.utils.DataUtil;
import com.quanzhi.auditapiv2.common.util.utils.DateUtil;
import com.quanzhi.auditapiv2.config.TyyKafkaConfig;
import com.quanzhi.auditapiv2.core.model.kafkaConfiguration.KafkaConfiguration;
import com.quanzhi.auditapiv2.core.model.kafkaConfiguration.ProducerConfig;
import com.quanzhi.auditapiv2.core.service.manager.web.impl.HttpApiService;
import com.quanzhi.auditapiv2.dto.web.TYYApiDataDto;
import com.quanzhi.auditapiv2.dto.web.TYYReportCommonDto;
import com.quanzhi.auditapiv2.utils.sm4.SM4Coder;
import com.quanzhi.dlp4j.core.util.DataUtils;
import com.quanzhi.metabase.core.model.http.HttpApiResource;
import lombok.extern.slf4j.Slf4j;
import org.apache.kafka.clients.producer.KafkaProducer;
import org.apache.kafka.clients.producer.ProducerRecord;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.data.redis.core.Cursor;
import org.springframework.data.redis.core.HashOperations;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.ScanOptions;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;

import static com.quanzhi.auditapiv2.core.model.kafkaConfiguration.ProducerConfig.producerConfigMap;

/**
 * @Author: ian
 */
@Component
@Slf4j
public class TYYApiVisitsPushJob {
    private Logger logger = LoggerFactory.getLogger(TYYApiVisitsPushJob.class);

    @Autowired
    private HttpApiService httpApiService;

    @NacosValue(value = "${tyyv2.kafka.ip:kafka-server}", autoRefreshed = true)
    private String kafkaIP;

    @NacosValue(value = "${tyyv2.kafka.port:9093}", autoRefreshed = true)
    private String kafkaPort;

    @NacosValue(value = "${tyyv2.kafka.apiTopic:st-visit-api}", autoRefreshed = true)
    private String kafkaApiTopic;

    @NacosValue(value = "${tyyv2.kafka.batchSize:200}", autoRefreshed = true)
    private Integer batchSize;

    @NacosValue(value = "${tyyv2.sm4.key:d4f8e2b7c6a1d4b3}", autoRefreshed = true)
    private String sm4Key;

    @Autowired
    private TyyKafkaConfig tyyKafkaConfig;

    @Autowired
    private RedisTemplate<String, Object> redisTemplate;


    @LockedScheduler(cron = "0 15 * * * ?", name = "天翼云访问量推送", executor = "TYYApiVisitsPushJob", description = "天翼云访问量推送")
    public void execute() throws Exception {
        logger.info("天翼云 Api 访问量推送开始: {}", getPastHourKey());
        doJob();
        logger.info("天翼云 Api 访问量推送开始: {}", getPastHourKey());
    }

    private KafkaProducer getKafkaProducer() {
//        String kafkaIpPort = kafkaIP + ":" + kafkaPort;
//        if (DataUtil.isEmpty(kafkaTemplate)) {
//            producerConfigMap.put(ProducerConfig.BOOTSTRAP_SERVERS_CONFIG, kafkaIpPort);
//            kafkaTemplate = KafkaConfiguration.getKafkaTemplate();
//        }

        return tyyKafkaConfig.getKafkaProducer();
    }

    private String getPastHourKey() {
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyyMMddHH");
        return "api:visits:" + LocalDateTime.now().minusHours(1).format(formatter);
    }

    // 3.1.13
    private void doJob() {
//        try {
//            KafkaProducer kafkaProducer = getKafkaProducer();
//            JSONObject jsonObject = new JSONObject();
//            jsonObject.put("name", "John");
//            ProducerRecord<String, ?> producerRecord = new ProducerRecord("ApiEvents", jsonObject.toJSONString());
//            kafkaProducer.send(producerRecord);
//        }catch (Exception e){
//            logger.error("测试推送",e);
//        }
        String key = getPastHourKey();
        String dataStr = getDateStr();
        HashOperations<String, Object, Object> hashOps = redisTemplate.opsForHash();
        ScanOptions options = ScanOptions.scanOptions().count(batchSize).build();

        try (Cursor<Map.Entry<Object, Object>> cursor = hashOps.scan(key, options)) {
            while (cursor.hasNext()) {
                Map<String, Integer> visitCounts = new HashMap<>();
                Set<String> apiUris = new HashSet<>();
                for (int i = 0; i < batchSize && cursor.hasNext(); i++) {
                    Map.Entry<Object, Object> entry = cursor.next();
                    visitCounts.put((String) entry.getKey(), (Integer) entry.getValue());
                    apiUris.add((String) entry.getKey());
                }

                // process the current batch of uris
                processBatch(apiUris, visitCounts, key, dataStr);
            }
        } catch (Exception e) {
            logger.error("天翼云 Api 访问量推送异常：", e);
            return;
        }

        logger.info("天翼云 Api 访问量推送: processing completed.");
    }

    private String getDateStr() {
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyyMMddHH");
        return LocalDateTime.now().minusHours(1).format(formatter);
    }

    private void processBatch(Set<String> apiUris, Map<String, Integer> visitCounts, String key, String dataStr) {
        if (apiUris.isEmpty()) {
            return;
        }

        List<HttpApiResource> httpApiResources = httpApiService.getHttpApiList(apiUris, null);
        List<TYYApiDataDto> dataList = new ArrayList<>();
        for (HttpApiResource httpApiResource : httpApiResources) {
            TYYApiDataDto apiData = new TYYApiDataDto();
            if (!DataUtils.isEmpty(visitCounts.get(httpApiResource.getUri()))) {
                int visits = visitCounts.get(httpApiResource.getUri());
                apiData.setVisits(visits);
                apiData.setApiId(httpApiResource.getId());
                apiData.setApiUrl(httpApiResource.getApiUrl());
                apiData.setSyncType(1);
                apiData.setDateStr(dataStr);
                dataList.add(apiData);
            }
        }

        if (!dataList.isEmpty()) {
            TYYReportCommonDto report = new TYYReportCommonDto();
            report.setBatchId(UUID.randomUUID().toString());
            report.setInterfaceCode(1008);
            report.setSystemCode(10200);
            report.setSendTimestamp(DateUtil.currentTimestamp());
            // encrypt data
            String dataListStr = "\"dataList\":" + JSON.toJSONString(dataList);
            SM4Coder sm4Coder = new SM4Coder();
            sm4Coder.setSecretKey(sm4Key);
            String encryptData = sm4Coder.encryptData_ECB(dataListStr);
            report.setEncryptData(encryptData);

            KafkaProducer kafkaProducer = getKafkaProducer();
            ProducerRecord<String, ?> producerRecord = new ProducerRecord(kafkaApiTopic,JSON.toJSONString(report));
            kafkaProducer.send(producerRecord);
        }
    }
}
