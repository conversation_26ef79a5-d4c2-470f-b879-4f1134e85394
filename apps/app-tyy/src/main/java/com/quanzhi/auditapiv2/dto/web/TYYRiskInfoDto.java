package com.quanzhi.auditapiv2.dto.web;

import lombok.Data;

@Data
public class TYYRiskInfoDto {
    private int syncType;
    private String riskId;
    private String riskRemark;
    private String apiId;
    private String apiUrl;
    private String level;
    private String state;
    private String businessDepartment;
    private String name;
    private String type;
    private String riskSubject;
    private String subjectType;
    private String attackSuccessName;
    private int attackCount;
    private int rspDataDistinctCnt;
    private String rspLabelList;
    private String remark;
    private long firstTime;
    private long lastTime;
    private TYYSampleRequest sampleReq;
    private TYYSampleResponse sampleRsp;
    private String notifyIp;
}