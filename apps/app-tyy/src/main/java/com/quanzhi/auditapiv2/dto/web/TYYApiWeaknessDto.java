package com.quanzhi.auditapiv2.dto.web;

import lombok.Data;

@Data
public class TYYApiWeaknessDto {
    private int syncType;
    private String weaknessId;
    private String apiId;
    private String apiUrl;
    private String host;
    private String appName;
    private String name;
    private String level;
    private String type;
    private String state;
    private String businessDepartment;
    private String solution;
    private String featureLabels;
    private String methods;
    private int maxRspLabelValueCount;
    private String reqDataLabels;
    private String rspDataLabels;
    private String visitDomains;
    private String deployDomains;
    private String terminals;
    private String operationId;
    private long lastTimestamp;
    private long earlyTimestamp;
    private String rspContentTypes;
    private TYYSampleRequest sampleReq;
    private TYYSampleResponse sampleRsp;
    private String notifyIp;
}
