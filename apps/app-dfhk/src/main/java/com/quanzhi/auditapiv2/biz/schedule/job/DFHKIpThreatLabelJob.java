package com.quanzhi.auditapiv2.biz.schedule.job;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.nacos.api.config.annotation.NacosValue;
import com.mongodb.client.MongoCursor;
import com.quanzhi.audit.mix.schdule.application.annotation.LockedScheduler;
import com.quanzhi.auditapiv2.common.dal.entity.IpDateInfo;
import com.quanzhi.auditapiv2.common.util.utils.DataUtil;
import com.quanzhi.auditapiv2.core.risk.entity.ThreatInfo;
import com.quanzhi.auditapiv2.core.risk.repository.IThreatInfoDao;
import com.quanzhi.auditapiv2.core.service.sso.RequestService;
import lombok.extern.slf4j.Slf4j;
import okhttp3.Request;
import org.bson.Document;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @Author: K, 小康
 * @Date: 2024/04/09/上午11:31
 * @Description:
 */
@Component
@Slf4j
public class DFHKIpThreatLabelJob {

    @NacosValue(value = "${dfhk.ipThreat.url:http://************:8090/tip_api/v4/ip}", autoRefreshed = true)
    private String url;

    @NacosValue(value = "${dfhk.ipThreat.apiKey:ed00a8568b1341999c4da1eee3d69a22}", autoRefreshed = true)
    private String apiKey;

    private final IThreatInfoDao iThreatInfoDao;
    private final MongoTemplate mongoTemplate;

    private final RequestService requestService;

    public DFHKIpThreatLabelJob(IThreatInfoDao iThreatInfoDao, MongoTemplate mongoTemplate, RequestService requestService) {
        this.iThreatInfoDao = iThreatInfoDao;
        this.mongoTemplate = mongoTemplate;
        this.requestService = requestService;
    }

    @LockedScheduler(cron = "0 0 2 * * ?", name = "IP威胁标签同步", executor = "IpThreatLabelJob", description = "IP威胁标签同步")
    public void execute() throws Exception {
        log.info("IP威胁标签同步开始");
        doJob();
        log.info("IP威胁标签同步结束");
    }

    private void doJob() {
        try (MongoCursor<Document> cursor = mongoTemplate.getCollection("threatInfo")
                .find(new Query().getQueryObject(), Document.class)
                .batchSize(1000)
                .noCursorTimeout(true)
                .cursor()) {


            while (cursor.hasNext()) {
                Document document = cursor.next();
                String threatEntity = document.getString("threatEntity");
                Map<String, Object> map = getLabelByUrl(threatEntity);
                if (DataUtil.isNotEmpty(map)) {
                    Update update = new Update();
                    List<String> judgments = (List<String>) map.get("judgments");
                    if (DataUtil.isNotEmpty(judgments)) {
                        update.set("judgments", judgments);
                    }
                    Boolean isMalicious = (Boolean) map.get("is_malicious");
                    if (DataUtil.isNotEmpty(isMalicious)) {
                        update.set("isMalicious", isMalicious);
                    }
                    String severity = (String) map.get("severity");
                    if (DataUtil.isNotEmpty(severity)) {
                        update.set("severity", severity);
                    }
                    // 使用update更新数据
                    Query query = new Query(Criteria.where("_id").is(document.get("_id")));
                    mongoTemplate.updateFirst(query, update, "threatInfo");
                }
            }

        }

    }

    private Map<String, Object> getLabelByUrl(String ip) {
        Map<String, Object> map = new HashMap<>();

        String dfhkUrl =  url + "?apiKey=" + apiKey + "&resource=" + ip;
        Request request = new Request.Builder().get().url(dfhkUrl).build();
        try {
            String str = requestService.sendRequest(request);
            log.info("请求响应信息:{}", str);
            JSONObject jsonObject = JSONObject.parseObject(str);
            JSONArray data = jsonObject.getJSONArray("data");
            if (data != null && data.size() > 0 && data.getJSONObject(0).getJSONArray("intelligence").size() > 0) {
                JSONObject object = data.
                        getJSONObject(0).getJSONArray("intelligence").
                        getJSONObject(0);
                List<String> judgments = object.getJSONArray("judgments").toJavaList(String.class);
                Boolean isMalicious = object.getBoolean("is_malicious");
                String severity = object.getString("severity");
                map.put("judgments", judgments);
                map.put("is_malicious", isMalicious);
                map.put("severity", severity);
            }

        } catch (Exception e) {
            log.error("请求Url:{} ip:{} 请求dfhk获取威胁ip信息报错:",dfhkUrl,ip, e);
        }
        return map;
    }
}
