<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.quanzhi.auditapiv2</groupId>
        <artifactId>auditapiv2</artifactId>
        <version>3.3.0-SNAPSHOT</version>
        <relativePath>../../pom.xml</relativePath>
    </parent>
    <artifactId>app-dfhk</artifactId>
    <name>app-dfhk</name>
    <url>http://maven.apache.org</url>

    <dependencies>
        <dependency>
            <groupId>com.quanzhi.auditapiv2</groupId>
            <artifactId>auditapiv2-web</artifactId>
        </dependency>
        <dependency>
            <groupId>com.quanzhi.audit.mix</groupId>
            <artifactId>audit-mix-schedule</artifactId>
        </dependency>
        <dependency>
            <groupId>com.quanzhi.auditapiv2</groupId>
            <artifactId>auditapiv2-core-service</artifactId>
        </dependency>
    </dependencies>


    <build>
        <finalName>dfhk</finalName>

        <resources>
            <resource>
                <directory>src/main/resources</directory>
                <filtering>true</filtering>
                <includes>
                    <include>application.properties</include>
                    <include>documents/*</include>
                    <include>riskExamples/*</include>
                    <include>application-${env}.properties</include>
                    <include>logback-${env}.xml</include>
                    <include>audit-log*.yml</include>
                    <include>uploadtemplate/*</include>
                    <include>mongo/**/**/**.json</include>
                    <include>report/**.zip</include>
                    <include>tool/**.zip</include>
                    <include>template-bak/*</include>
                    <include>offline-template/*</include>
                    <include>static/*</include>
                    <include>node/*</include>
                    <include>scripts/*</include>
                    <include>trace/*</include>
                </includes>
            </resource>
        </resources>

        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-resources-plugin</artifactId>
                <!--防止文件内容乱码-->
                <configuration>
                    <delimiters>
                        <delimiter>@</delimiter>
                    </delimiters>
                    <useDefaultDelimiters>false</useDefaultDelimiters>
                    <encoding>UTF-8</encoding>
                    <nonFilteredFileExtensions>
                        <nonFilteredFileExtension>xlsx</nonFilteredFileExtension>
                        <nonFilteredFileExtension>xls</nonFilteredFileExtension>
                        <nonFilteredFileExtension>pdf</nonFilteredFileExtension>
                    </nonFilteredFileExtensions>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <configuration>
                    <source>1.8</source>
                    <target>1.8</target>
                    <encoding>UTF-8</encoding>
                </configuration>
            </plugin>
            <!--            <plugin>-->
            <!--                <groupId>org.springframework.boot</groupId>-->
            <!--                <artifactId>spring-boot-maven-plugin</artifactId>-->
            <!--                <configuration>-->
            <!--                    <mainClass>com.quanzhi.auditapiv2.MainApplication</mainClass>-->
            <!--                    <layout>ZIP</layout>-->
            <!--                    &lt;!&ndash;构建完整可执行程序，可以直接运行&ndash;&gt;-->
            <!--                    <executable>true</executable>-->
            <!--                    <skip>true</skip>-->
            <!--                </configuration>-->
            <!--            </plugin>-->
        </plugins>
    </build>
    <profiles>
        <profile>
            <!-- 本地开发环境 -->
            <id>local</id>
            <properties>
                <env>local</env>
            </properties>
        </profile>
        <profile>
            <id>dev</id>
            <properties>
                <env>dev</env>
            </properties>
            <activation>
                <!-- 设置默认激活这个配置 -->
                <activeByDefault>true</activeByDefault>
            </activation>
            <build>
                <plugins>
                    <plugin>
                        <groupId>org.springframework.boot</groupId>
                        <artifactId>spring-boot-maven-plugin</artifactId>
                        <version>${spring.boot.version}</version>
                        <configuration>
                            <mainClass>com.quanzhi.auditapiv2.DFHKMainApplication</mainClass>
                        </configuration>
                        <executions>
                            <execution>
                                <goals>
                                    <goal>repackage</goal>
                                </goals>
                            </execution>
                        </executions>
                    </plugin>
                </plugins>
            </build>
        </profile>
        <profile>
            <id>prod</id>
            <properties>
                <env>prod</env>
            </properties>
            <build>
                <plugins>
                    <plugin>
                        <groupId>org.springframework.boot</groupId>
                        <artifactId>spring-boot-maven-plugin</artifactId>
                        <version>${spring.boot.version}</version>
                        <configuration>
                            <includes>
                                <!-- 这里只包含一个不存在的项nothing,即代表什么都不包含，当然名字可以随便写 -->
                                <include>
                                    <!--                                    <groupId>com.quanzhi.auditapiv2</groupId>-->
                                    <!--                                    <artifactId>auditapiv2-web</artifactId>-->
                                    <groupId>nothing</groupId>
                                    <artifactId>nothing</artifactId>
                                </include>
                            </includes>
                            <layout>ZIP</layout>
                            <mainClass>com.quanzhi.auditapiv2.DFHKMainApplication</mainClass>
                        </configuration>
                        <executions>
                            <execution>
                                <goals>
                                    <goal>repackage</goal>
                                </goals>
                            </execution>
                        </executions>
                    </plugin>
                    <!-- 自研加固插件 -->
                    <plugin>
                        <groupId>com.quanzhi</groupId>
                        <artifactId>jencryptor-springboot-maven-plugin</artifactId>
                        <version>1.0.0</version>
                        <executions>
                            <execution>
                                <goals>
                                    <goal>encryptor-after-springboot-plugin</goal>
                                </goals>
                            </execution>
                        </executions>
                        <configuration>
                            <!-- 注意：这里的路径要根据实际情况来配置，加固之后的.ef文件将会在此处配置的目录后面加一个字母s，例如/home/<USER>/加密输出的.ef文件路径为/home/<USER>/ -->
                            <libPath>${project.build.directory}/lib</libPath>
                        </configuration>
                    </plugin>
                </plugins>
            </build>
        </profile>
        <profile>
            <id>deploy</id>
            <properties>
                <env>prod</env>
            </properties>
        </profile>
    </profiles>
    <pluginRepositories>
        <pluginRepository>
            <id>public</id>
            <url>http://192.168.0.92:8081/nexus/content/groups/public/</url>
        </pluginRepository>
    </pluginRepositories>
</project>
