package com.quanzhi.auditapiv2.core.model.event;

import com.quanzhi.metabase.core.model.http.weakness.ApiWeakness;
import com.quanzhi.metabase.core.model.http.weakness.State;
import lombok.AllArgsConstructor;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/3/9 10:45 上午
 */
@AllArgsConstructor
@Data
public class WeaknessChangedEvent {

    private List<ApiWeakness> apiWeakness;

    private State state;

    private List<String> operations;

    public WeaknessChangedEvent(List<ApiWeakness> apiWeakness, State state) {
        this.apiWeakness = apiWeakness;
        this.state = state;
    }

}
