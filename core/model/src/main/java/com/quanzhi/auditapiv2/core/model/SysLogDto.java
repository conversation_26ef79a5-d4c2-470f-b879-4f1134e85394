package com.quanzhi.auditapiv2.core.model;

import com.quanzhi.auditapiv2.common.dal.dataobject.SysLog;
import com.quanzhi.auditapiv2.common.util.utils.BeanConvertUtils;
import lombok.Data;

/**
 * @Description:
 * @Author: danniel.yu
 * @Date: 2020-05-11 15:24
 */
@Data
public class SysLogDto {


    /**
     * 日志id
     */
    private String id;

    /**
     * 操作用户id
     */
    private String userId;

    /**
     * 操作用户
     */
    private String username;

    /**
     * 操作
     */
    private String operation;

    /**
     * 方法
     */
    private String method;

    /**
     * 参数
     */
    private String params;

    /**
     * 耗时
     */
    private Long time;

    /**
     * 操作ip地址
     */
    private String ip;

    /**
     * 创建时间
     */
    private Long gmtCreate;

    public static SysLogDto readFromDO(SysLog sysLog) {
        return BeanConvertUtils.convertTo(sysLog, SysLogDto.class);
    }

    public SysLog convertToDO() {
        return BeanConvertUtils.convertTo(this, SysLog.class);
    }
}
