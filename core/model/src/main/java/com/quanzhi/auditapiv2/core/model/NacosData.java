package com.quanzhi.auditapiv2.core.model;

import com.quanzhi.audit_core.common.model.AppFeatureLabel;
import com.quanzhi.audit_core.common.model.DataLabel;
import com.quanzhi.audit_core.common.model.FeatureLabel;
import com.quanzhi.audit_core.common.model.WeaknessRule;
import lombok.Data;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * create at 2020/11/21 10:13 下午
 * @description: nacos映射数据
 **/
@Data
public class NacosData {

    /**
     * 数据标签映射集合
     */
    private Map<String, DataLabel> dataLabelMap = new HashMap<>();

    private Map<String, String> dataLabelParentMap = new HashMap<>();

    /**
     * 接口分类映射集合
     */
    private Map<String, String> apiClassifierMap = new HashMap<>();
    /**
     * 接口标签
     */
    private Map<String, FeatureLabel> featureLabelMap = new HashMap<>();
    /**
     * 应用标签
     */
    private Map<String, AppFeatureLabel> appFeatureLabelMap = new HashMap<>();

    /**
     * 弱点规则映射集合 key:弱点ID
     */
    private Map<String, WeaknessRule> weaknessRuleMap = new HashMap<>();

    /**
     * 风险规则id-name映射
     */
    private Map<String, String> riskPolicyMap = new HashMap<>();

    private Map<String, String> firstClass = dataLabelParentMap = new HashMap<>();
}
