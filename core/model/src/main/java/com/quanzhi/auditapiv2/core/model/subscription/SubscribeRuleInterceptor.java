package com.quanzhi.auditapiv2.core.model.subscription;

import com.alibaba.nacos.api.config.annotation.NacosValue;
import com.quanzhi.audit_core.common.model.HttpEvent;
import com.quanzhi.audit_core.common.risk.RiskInfo;
import com.quanzhi.auditapiv2.common.dal.dto.HttpAppDto;
import com.quanzhi.auditapiv2.common.dal.entity.EventNotifyEntity;
import com.quanzhi.auditapiv2.common.dal.entity.SubscribePolicyRule;
import com.quanzhi.auditapiv2.common.dal.entity.aggRisk.AggRiskInfo;
import com.quanzhi.auditapiv2.common.dal.enums.ProductTypeEnum;
import com.quanzhi.auditapiv2.common.util.utils.DataUtil;
import com.quanzhi.metabase.core.model.event.ResourceChangedEvent;
import com.quanzhi.metabase.core.model.http.HttpApiResource;
import com.quanzhi.metabase.core.model.http.HttpResourceConstant;
import com.quanzhi.metabase.core.model.http.Pair;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.script.ScriptEngine;
import javax.script.ScriptEngineManager;
import javax.script.ScriptException;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import static com.quanzhi.auditapiv2.core.model.subscription.SubscribeRuleContext.*;

/**
 * <AUTHOR>
 * @date 2020/8/14 下午4:59
 */
@Slf4j
@Component
public class SubscribeRuleInterceptor implements IResourceChangedEventInterceptor {

    @NacosValue(value = "${product.type:api}", autoRefreshed = true)
    private String productType;

    /**
     * 关于ScriptEngine是否线程安全的讨论
     * https://stackoverflow.com/questions/30140103/should-i-use-a-separate-scriptengine-and-compiledscript-instances-per-each-threa
     */
    private ScriptEngine scriptEngine;

    public SubscribeRuleInterceptor() {
        ScriptEngineManager scriptEngineManager = new ScriptEngineManager();
        scriptEngine = scriptEngineManager.getEngineByName("javascript");
    }

    private boolean containsOne(List<String> scopeList, ResourceChangedEvent resourceChangedEvent, String playLoadKey) {

        boolean conditinue = true;

        List<String> playLoadValues = DataUtil.isNotEmpty(resourceChangedEvent.getPayload())
                && DataUtil.isNotEmpty(((Map) resourceChangedEvent.getPayload()).get(playLoadKey))
                ? (List<String>) ((Map) resourceChangedEvent.getPayload()).get(playLoadKey) : new ArrayList<>();

        if (DataUtil.isEmpty(playLoadValues)) {
            conditinue = false;
        }

        boolean hasOne = false;
        for (String playLoadValue : playLoadValues) {

            if (scopeList.contains(playLoadValue)) {
                hasOne = true;
                break;
            }
        }
        conditinue = hasOne;

        return conditinue;
    }

    private boolean containsOne(List<String> scopeList, List<String> classifications, String playLoadKey) {

        boolean conditinue = true;

        if (DataUtil.isEmpty(classifications)) {
            conditinue = false;
        }

        boolean hasOne = false;
        for (String value : classifications) {

            if (scopeList.contains(value)) {
                hasOne = true;
                break;
            }
        }
        conditinue = hasOne;

        return conditinue;
    }

    @Override
    public boolean matchPolicy(ResourceChangedEvent resourceChangedEvent, HttpApiResource httpApiResource, SubscribePolicyRule subscribePolicyRule) {
        //订阅周期为每日全量更新，直接返回false
        if (SubscribePolicyRule.CycleEnum.FULL_DAILY.name().equals(subscribePolicyRule.getCycle())) {
            return false;
        }
        if (DataUtil.isEmpty(resourceChangedEvent.getResourceType())
                || (!HttpResourceConstant.API.equals(resourceChangedEvent.getResourceType())
                && !HttpResourceConstant.API_WEAKNESS.equals(resourceChangedEvent.getResourceType())
                && !HttpResourceConstant.APP.equals(resourceChangedEvent.getResourceType()))
        ) {
            return false;
        }

        //弱点事件和接口事件是两种resourceType 需要在订阅条件判断之前作下过滤
        //弱点订阅的订阅条件中不能有 新增接口/新增应用
        if (HttpResourceConstant.API_WEAKNESS.equals(resourceChangedEvent.getResourceType())) {

            if (subscribePolicyRule.getConditionList().stream().filter(subscribeCondition -> subscribeCondition.getPolicy().equals(NEW_RESOURCE_EVENT)).count() > 0) {
                return false;
            }
            if (subscribePolicyRule.getConditionList().stream().filter(subscribeCondition -> subscribeCondition.getPolicy().equals(NEW_APP_EVENT)).count() > 0) {
                return false;
            }
        }

        //接口订阅的订阅条件中不能有 新增弱点/新增应用
        if (HttpResourceConstant.API.equals(resourceChangedEvent.getResourceType())) {

            if (subscribePolicyRule.getConditionList().stream().filter(subscribeCondition -> subscribeCondition.getPolicy().equals(NEW_WEAKNESS_EVENT)).count() > 0) {
                return false;
            }
            if (subscribePolicyRule.getConditionList().stream().filter(subscribeCondition -> subscribeCondition.getPolicy().equals(NEW_APP_EVENT)).count() > 0) {
                return false;
            }
        }

        if (productType.equals(ProductTypeEnum.tjdx.name())) {
            return true;
        }

        //订阅范围判断
        if (DataUtil.isNotEmpty(subscribePolicyRule.getScope())) {

            boolean conditinue = true;

            SubscribePolicyRule.ScopeEnum scopeEnum = SubscribePolicyRule.ScopeEnum.valueOf(subscribePolicyRule.getScope());

            if (DataUtil.isNotEmpty(subscribePolicyRule.getScopeList()) | DataUtil.isNotEmpty(subscribePolicyRule.getScopeField())) {

                List<String> scopeList = subscribePolicyRule.getScopeList();

                switch (scopeEnum) {

                    case APP:
                        if (DataUtil.isNotEmpty(httpApiResource) && DataUtil.isNotEmpty(httpApiResource.getAppUri())) {
                            String appUri = httpApiResource.getAppUri();
                            conditinue = appUri != null && scopeList.contains(appUri);
                        } else {
                            conditinue = false;
                        }
                        break;

                    case APICLASSIFICATION:
                        if (DataUtil.isNotEmpty(httpApiResource) && DataUtil.isNotEmpty(httpApiResource.getClassifications())) {
                            List<String> classifications = httpApiResource.getClassifications();
                            conditinue = classifications != null && containsOne(scopeList, classifications, "classifications");
                        } else {
                            conditinue = false;
                        }

                        break;

                    case VISITDOMAIN:
                        if (DataUtil.isNotEmpty(httpApiResource) && DataUtil.isNotEmpty(httpApiResource.getVisitDomains())) {
                            boolean flag = false;
                            List<String> visitDomains = httpApiResource.getVisitDomains();
                            for (String visitDomain : visitDomains) {
                                for (String domain : scopeList) {
                                    if (visitDomain.contains(domain)) {
                                        flag = true;
                                        break;
                                    }
                                }
                                if (flag) {
                                    break;
                                }
                            }
                            conditinue = visitDomains != null && flag;
                        } else {
                            conditinue = false;
                        }

                        break;
                    case CUSTOM:
                        if (DataUtil.isNotEmpty(httpApiResource) && httpApiResource.getDepartments() != null && httpApiResource.getDepartments().size() > 0) {
                            List<Pair<String>> customFieldsProperties = httpApiResource.getDepartments().get(0).getProperties();
                            for (Pair<String> customFieldsProperty : customFieldsProperties) {
                                if (customFieldsProperty.getKey().equals(subscribePolicyRule.getScopeField())) {
                                    conditinue = customFieldsProperty.getValue().equals(subscribePolicyRule.getScopeValue());
                                    break;
                                }
                            }
                        } else {
                            conditinue = false;
                        }

                        break;
                    default:
                }
            }

            if (!conditinue) {
                return false;
            }
        }

        //应用订阅只有新增订阅，手动判断
        if (HttpResourceConstant.APP.equals(resourceChangedEvent.getResourceType())) {

            if (subscribePolicyRule.getConditionList().stream().filter(subscribeCondition -> subscribeCondition.getPolicy().equals(NEW_APP_EVENT)).count() > 0) {
                return true;
            }
        }


        //订阅条件判断
        String expr = subscribePolicyRule.getExpr();
        if (StringUtils.isEmpty(expr)) {
            return false;
        }
        for (SubscribePolicyRule.SubscribeCondition policyIndex : subscribePolicyRule.getConditionList()) {
            String policy = policyIndex.getPolicy();
            String operate = policyIndex.getOperate();
            String content = policyIndex.getContent();

            boolean recommend = SubscribeRuleContext.match(resourceChangedEvent, policy, operate, content);
            expr = expr.replace(policyIndex.getSeq(), String.valueOf(recommend));
        }
        try {
            Object eval = scriptEngine.eval(expr);
            String evalValue = eval.toString();
            if (StringUtils.isNumeric(eval.toString())) {
                return Integer.valueOf(eval.toString()) > 0;
            } else {
                return Boolean.valueOf(evalValue);
            }
        } catch (ScriptException e) {
            log.error("illegal expr: {}", subscribePolicyRule.getExpr(), e);
        } catch (NumberFormatException e) {
            log.error("illegal expr: {}", subscribePolicyRule.getExpr(), e);
        }
        return false;
    }

    @Override
    public boolean matchPolicy(RiskInfo riskInfo, HttpApiResource httpApiResource, HttpAppDto httpApp, SubscribePolicyRule subscribePolicyRule) {
        //订阅周期为每日全量更新，直接返回false
        if (SubscribePolicyRule.CycleEnum.FULL_DAILY.name().equals(subscribePolicyRule.getCycle())) {
            return false;
        }
        //订阅类型不是异常的直接返回false
        if (!subscribePolicyRule.getType().equals("RISK")) {
            return false;
        }
        if (productType.equals(ProductTypeEnum.tjdx.name())) {
            return true;
        }
        //订阅范围判断
        if (DataUtil.isNotEmpty(subscribePolicyRule.getScope())) {

            boolean conditinue = true;

            SubscribePolicyRule.ScopeEnum scopeEnum = SubscribePolicyRule.ScopeEnum.valueOf(subscribePolicyRule.getScope());

            if (DataUtil.isNotEmpty(subscribePolicyRule.getScopeList()) | DataUtil.isNotEmpty(subscribePolicyRule.getScopeField())) {


                List<String> scopeList = subscribePolicyRule.getScopeList();

                switch (scopeEnum) {

                    case APP:
                        if (DataUtil.isNotEmpty(httpApiResource) && DataUtil.isNotEmpty(httpApiResource.getAppUri())) {
                            String appUri = httpApiResource.getAppUri();
                            conditinue = appUri != null && scopeList.contains(appUri);
                        } else if (DataUtil.isNotEmpty(httpApp) && DataUtil.isNotEmpty(httpApp.getUri())) {
                            String appUri = httpApp.getUri();
                            conditinue = appUri != null && scopeList.contains(appUri);
                        } else {
                            conditinue = false;
                        }
                        if (DataUtil.isNotEmpty(httpApp) && DataUtil.isNotEmpty(httpApp.getUri())) {
                            String appUri = httpApp.getUri();
                            conditinue = appUri != null && scopeList.contains(appUri);
                        }

                        break;

                    case APICLASSIFICATION:
                        if (DataUtil.isNotEmpty(httpApiResource) && DataUtil.isNotEmpty(httpApiResource.getClassifications())) {
                            List<String> classifications = httpApiResource.getClassifications();
                            List<String> featureLabels = httpApiResource.getFeatureLabels();
                            classifications.addAll(featureLabels);
                            conditinue = containsOne(scopeList, classifications, "classifications");

                        } else if (DataUtil.isNotEmpty(httpApp) && DataUtil.isNotEmpty(httpApp.getAppClassifications())) {
                            List<String> classifications = httpApp.getAppClassifications();
                            List<String> featureLabels = httpApp.getFeatureLabels();
                            classifications.addAll(featureLabels);
                            conditinue = containsOne(scopeList, classifications, "classifications");

                        } else {
                            conditinue = false;
                        }

                        break;

                    case VISITDOMAIN:
                        List<String> visitDomains = new ArrayList<>();
                        if (DataUtil.isNotEmpty(httpApiResource) && DataUtil.isNotEmpty(httpApiResource.getVisitDomains())) {
                            visitDomains = httpApiResource.getVisitDomains();

                        } else if (DataUtil.isNotEmpty(httpApp) && DataUtil.isNotEmpty(httpApp.getVisitDomains())) {
                            visitDomains = httpApp.getVisitDomains();
                        }
                        boolean flag = false;

                        for (String visitDomain : visitDomains) {
                            for (String domain : scopeList) {
                                if (visitDomain.contains(domain)) {
                                    flag = true;
                                    break;
                                }
                            }
                            if (flag) {
                                break;
                            }
                        }

                        conditinue = visitDomains != null && flag;

                        break;
                    case CUSTOM:
                        if (DataUtil.isNotEmpty(httpApiResource) && httpApiResource.getDepartments() != null && httpApiResource.getDepartments().size() > 0) {
                            List<Pair<String>> customFieldsProperties = httpApiResource.getDepartments().get(0).getProperties();
                            for (Pair<String> customFieldsProperty : customFieldsProperties) {
                                if (customFieldsProperty.getKey().equals(subscribePolicyRule.getScopeField())) {
                                    conditinue = customFieldsProperty.getValue().equals(subscribePolicyRule.getScopeValue());
                                    break;
                                }
                            }
                        } else if (DataUtil.isNotEmpty(httpApp) && httpApp.getDepartments() != null && httpApp.getDepartments().size() > 0) {
                            List<Pair<String>> customFieldsProperties = httpApp.getDepartments().get(0).getProperties();
                            for (Pair<String> customFieldsProperty : customFieldsProperties) {
                                if (customFieldsProperty.getKey().equals(subscribePolicyRule.getScopeField())) {
                                    conditinue = customFieldsProperty.getValue().equals(subscribePolicyRule.getScopeValue());
                                    break;
                                }
                            }
                        } else {
                            conditinue = false;
                        }

                        break;
                    default:
                }
            }

            if (!conditinue) {
                return false;
            }
        }
        //订阅条件判断
        String expr = subscribePolicyRule.getExpr();
        if (StringUtils.isEmpty(expr)) {
            return false;
        }
        for (SubscribePolicyRule.SubscribeCondition policyIndex : subscribePolicyRule.getConditionList()) {
            String policy = policyIndex.getPolicy();
            String operate = policyIndex.getOperate();
            String content = policyIndex.getContent();

            boolean recommend = SubscribeRuleContext.match(riskInfo, policy, operate, content);

            expr = expr.replace(policyIndex.getSeq(), String.valueOf(recommend));

        }
        try {
            Object eval = scriptEngine.eval(expr);
            String evalValue = eval.toString();
            if (StringUtils.isNumeric(eval.toString())) {
                return Integer.valueOf(eval.toString()) > 0;
            } else {
                return Boolean.valueOf(evalValue);
            }
        } catch (ScriptException e) {
            log.error("illegal expr: {}", subscribePolicyRule.getExpr(), e);
        } catch (NumberFormatException e) {
            log.error("illegal expr: {}", subscribePolicyRule.getExpr(), e);
        }
        return false;
    }

    @Override
    public boolean matchPolicy(AggRiskInfo aggRiskInfo, HttpApiResource httpApiResource, HttpAppDto httpApp, SubscribePolicyRule subscribePolicyRule) {
        //订阅周期为每日全量更新，直接返回false
        if (SubscribePolicyRule.CycleEnum.FULL_DAILY.name().equals(subscribePolicyRule.getCycle())) {
            return false;
        }
        //订阅类型不是异常的直接返回false
        if (!subscribePolicyRule.getType().equals("RISK")) {
            return false;
        }
        if (productType.equals(ProductTypeEnum.tjdx.name())) {
            return true;
        }
        //订阅范围判断
        if (DataUtil.isNotEmpty(subscribePolicyRule.getScope())) {

            boolean conditinue = true;

            SubscribePolicyRule.ScopeEnum scopeEnum = SubscribePolicyRule.ScopeEnum.valueOf(subscribePolicyRule.getScope());

            if (DataUtil.isNotEmpty(subscribePolicyRule.getScopeList()) | DataUtil.isNotEmpty(subscribePolicyRule.getScopeField())) {


                List<String> scopeList = subscribePolicyRule.getScopeList();

                switch (scopeEnum) {

                    case APP:
                        if (DataUtil.isNotEmpty(httpApiResource) && DataUtil.isNotEmpty(httpApiResource.getAppUri())) {
                            String appUri = httpApiResource.getAppUri();
                            conditinue = appUri != null && scopeList.contains(appUri);
                        } else if (DataUtil.isNotEmpty(httpApp) && DataUtil.isNotEmpty(httpApp.getUri())) {
                            String appUri = httpApp.getUri();
                            conditinue = appUri != null && scopeList.contains(appUri);
                        } else {
                            conditinue = false;
                        }
                        if (DataUtil.isNotEmpty(httpApp) && DataUtil.isNotEmpty(httpApp.getUri())) {
                            String appUri = httpApp.getUri();
                            conditinue = appUri != null && scopeList.contains(appUri);
                        }

                        break;

                    case APICLASSIFICATION:
                        if (DataUtil.isNotEmpty(httpApiResource) && DataUtil.isNotEmpty(httpApiResource.getClassifications())) {
                            List<String> classifications = httpApiResource.getClassifications();
                            List<String> featureLabels = httpApiResource.getFeatureLabels();
                            classifications.addAll(featureLabels);
                            conditinue = containsOne(scopeList, classifications, "classifications");

                        } else if (DataUtil.isNotEmpty(httpApp) && DataUtil.isNotEmpty(httpApp.getAppClassifications())) {
                            List<String> classifications = httpApp.getAppClassifications();
                            List<String> featureLabels = httpApp.getFeatureLabels();
                            classifications.addAll(featureLabels);
                            conditinue = containsOne(scopeList, classifications, "classifications");

                        } else {
                            conditinue = false;
                        }

                        break;

                    case VISITDOMAIN:
                        List<String> visitDomains = new ArrayList<>();
                        if (DataUtil.isNotEmpty(httpApiResource) && DataUtil.isNotEmpty(httpApiResource.getVisitDomains())) {
                            visitDomains = httpApiResource.getVisitDomains();

                        } else if (DataUtil.isNotEmpty(httpApp) && DataUtil.isNotEmpty(httpApp.getVisitDomains())) {
                            visitDomains = httpApp.getVisitDomains();
                        }
                        boolean flag = false;

                        for (String visitDomain : visitDomains) {
                            for (String domain : scopeList) {
                                if (visitDomain.contains(domain)) {
                                    flag = true;
                                    break;
                                }
                            }
                            if (flag) {
                                break;
                            }
                        }

                        conditinue = visitDomains != null && flag;

                        break;
                    case CUSTOM:
                        if (DataUtil.isNotEmpty(httpApiResource) && httpApiResource.getDepartments() != null && httpApiResource.getDepartments().size() > 0) {
                            List<Pair<String>> customFieldsProperties = httpApiResource.getDepartments().get(0).getProperties();
                            for (Pair<String> customFieldsProperty : customFieldsProperties) {
                                if (customFieldsProperty.getKey().equals(subscribePolicyRule.getScopeField())) {
                                    conditinue = customFieldsProperty.getValue().equals(subscribePolicyRule.getScopeValue());
                                    break;
                                }
                            }
                        } else if (DataUtil.isNotEmpty(httpApp) && httpApp.getDepartments() != null && httpApp.getDepartments().size() > 0) {
                            List<Pair<String>> customFieldsProperties = httpApp.getDepartments().get(0).getProperties();
                            for (Pair<String> customFieldsProperty : customFieldsProperties) {
                                if (customFieldsProperty.getKey().equals(subscribePolicyRule.getScopeField())) {
                                    conditinue = customFieldsProperty.getValue().equals(subscribePolicyRule.getScopeValue());
                                    break;
                                }
                            }
                        } else {
                            conditinue = false;
                        }

                        break;
                    default:
                }
            }

            if (!conditinue) {
                return false;
            }
        }
        //订阅条件判断
        String expr = subscribePolicyRule.getExpr();
        if (StringUtils.isEmpty(expr)) {
            return false;
        }
        for (SubscribePolicyRule.SubscribeCondition policyIndex : subscribePolicyRule.getConditionList()) {
            String policy = policyIndex.getPolicy();
            String operate = policyIndex.getOperate();
            String content = policyIndex.getContent();

            boolean recommend = SubscribeRuleContext.match(aggRiskInfo, policy, operate, content);

            expr = expr.replace(policyIndex.getSeq(), String.valueOf(recommend));

        }
        try {
            Object eval = scriptEngine.eval(expr);
            String evalValue = eval.toString();
            if (StringUtils.isNumeric(eval.toString())) {
                return Integer.valueOf(eval.toString()) > 0;
            } else {
                return Boolean.valueOf(evalValue);
            }
        } catch (ScriptException e) {
            log.error("illegal expr: {}", subscribePolicyRule.getExpr(), e);
        } catch (NumberFormatException e) {
            log.error("illegal expr: {}", subscribePolicyRule.getExpr(), e);
        }
        return false;
    }

    @Override
    public boolean matchPolicy(HttpEvent httpEvent, HttpApiResource httpApiResource, SubscribePolicyRule subscribePolicyRule) {
        //订阅周期为每日全量更新，直接返回false
        if (SubscribePolicyRule.CycleEnum.FULL_DAILY.name().equals(subscribePolicyRule.getCycle())) {
            return false;
        }
        //订阅类型不是事件的直接返回false
        if (!subscribePolicyRule.getType().equals("EVENT")) {
            return false;
        }
        if (productType.equals(ProductTypeEnum.tjdx.name())) {
            return true;
        }
        //订阅范围判断
        if (DataUtil.isNotEmpty(subscribePolicyRule.getScope())) {

            boolean conditinue = true;

            SubscribePolicyRule.ScopeEnum scopeEnum = SubscribePolicyRule.ScopeEnum.valueOf(subscribePolicyRule.getScope());

            if (DataUtil.isNotEmpty(subscribePolicyRule.getScopeList()) | DataUtil.isNotEmpty(subscribePolicyRule.getScopeField())) {


                List<String> scopeList = subscribePolicyRule.getScopeList();

                switch (scopeEnum) {

                    case APP:
                        if (DataUtil.isNotEmpty(httpApiResource) && DataUtil.isNotEmpty(httpApiResource.getAppUri())) {
                            String appUri = httpApiResource.getAppUri();
                            conditinue = appUri != null && scopeList.contains(appUri);
                        } else {
                            conditinue = false;
                        }
                        break;

                    case APICLASSIFICATION:
                        if (DataUtil.isNotEmpty(httpApiResource) && DataUtil.isNotEmpty(httpApiResource.getClassifications())) {
                            List<String> classifications = httpApiResource.getClassifications();
                            conditinue = classifications != null && containsOne(scopeList, classifications, "classifications");
                        } else {
                            conditinue = false;
                        }

                        break;

                    case VISITDOMAIN:
                        if (DataUtil.isNotEmpty(httpApiResource) && DataUtil.isNotEmpty(httpApiResource.getVisitDomains())) {
                            boolean flag = false;
                            List<String> visitDomains = httpApiResource.getVisitDomains();
                            for (String visitDomain : visitDomains) {
                                for (String domain : scopeList) {
                                    if (visitDomain.contains(domain)) {
                                        flag = true;
                                        break;
                                    }
                                }
                                if (flag) {
                                    break;
                                }
                            }
                            conditinue = visitDomains != null && flag;
                        } else {
                            conditinue = false;
                        }

                        break;
                    case CUSTOM:
                        if (DataUtil.isNotEmpty(httpApiResource) && httpApiResource.getDepartments() != null && httpApiResource.getDepartments().size() > 0) {
                            List<Pair<String>> customFieldsProperties = httpApiResource.getDepartments().get(0).getProperties();
                            for (Pair<String> customFieldsProperty : customFieldsProperties) {
                                if (customFieldsProperty.getKey().equals(subscribePolicyRule.getScopeField())) {
                                    conditinue = customFieldsProperty.getValue().equals(subscribePolicyRule.getScopeValue());
                                    break;
                                }
                            }
                        } else {
                            conditinue = false;
                        }

                        break;
                    default:
                }
            }

            if (!conditinue) {
                return false;
            }
        }
        return true;
    }

    @Override
    public boolean matchPolicy(EventNotifyEntity eventNotifyEntity, SubscribePolicyRule subscribePolicyRule) {
        if(!"EVENT".equals(subscribePolicyRule.getType())){
            return false;
        }
        //暂时只有监控事件，就不做复杂判断了
        for (SubscribePolicyRule.SubscribeCondition policyIndex : subscribePolicyRule.getConditionList()) {
            if ("monitorEvent".equals(policyIndex.getPolicy())){
                return true;
            }
        }
        return false;
    }

}
