package com.quanzhi.auditapiv2.core.model;

import com.quanzhi.auditapiv2.common.dal.dataobject.SysUserToken;
import com.quanzhi.auditapiv2.common.util.utils.BeanConvertUtils;
import lombok.Data;

import java.util.Date;

/**
 * @Description:
 * @Author: danniel.yu
 * @Date: 2020-05-11 16:11
 */
@Data
public class SysUserTokenDto {
    /**
     * 用户id
     */
    private String userId;

    /**
     * token
     */
    private String token;

    /**
     * 过期时间
     */
    private Date gmtExpire;

    /**
     * 更新时间
     */
    private Date gmtModified;



    public static SysUserTokenDto readFromDO(SysUserToken sysUserToken) {
        return BeanConvertUtils.convertTo(sysUserToken, SysUserTokenDto.class);
    }

    public SysUserToken convertToDO() {
        return BeanConvertUtils.convertTo(this, SysUserToken.class);
    }
}
