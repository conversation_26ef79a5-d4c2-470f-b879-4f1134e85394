package com.quanzhi.auditapiv2.core.model.bigScreen;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * create at 2021/3/26 1:52 下午
 * @description: 节点关系
 **/
@Data
@NoArgsConstructor
@AllArgsConstructor
public class NodeLinks {

    /**
     * 起始节点坐标
     */
    private int source;
    /**
     * 目的节点坐标
     */
    private int target;
    /**
     * 关系值
     */
    private String value;

}