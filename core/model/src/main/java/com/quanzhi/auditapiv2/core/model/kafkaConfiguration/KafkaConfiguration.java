package com.quanzhi.auditapiv2.core.model.kafkaConfiguration;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.context.event.ApplicationReadyEvent;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.event.EventListener;
import org.springframework.kafka.annotation.EnableKafka;
import org.springframework.kafka.core.DefaultKafkaProducerFactory;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.kafka.core.ProducerFactory;

import java.util.HashMap;
import java.util.Map;

import static com.quanzhi.auditapiv2.core.model.kafkaConfiguration.ProducerConfig.QZ_PRODUCER_CONFIG_MAP;
import static com.quanzhi.auditapiv2.core.model.kafkaConfiguration.ProducerConfig.producerConfigMap;

/**
 * <AUTHOR>
 * @date 2020/8/18 上午10:03
 */
@EnableKafka
@Configuration
@Slf4j
public class KafkaConfiguration {

    @Value("${qz.kafka.security.protocol:}")
    private String securityProtocol;

    @Value("${qz.kafka.sasl.mechanism:}")
    private String saslMechanism;

    @Value("${qz.kafka.sasl.jaas.config:}")
    private String jaasConfig;

    private static ProducerFactory<Integer, String> producerFactory() {
        return new DefaultKafkaProducerFactory<>(senderProps(producerConfigMap));
    }

    // 生产者配置
    private static Map<String, Object> senderProps(Map<String,String> map) {
        Map<String, Object> props = new HashMap<>();
        for(Map.Entry<String, String> entry : map.entrySet()){
            props.put(entry.getKey(), entry.getValue());
        }
        return props;
    }

    public static KafkaTemplate<Integer, String> getKafkaTemplate() {
        KafkaTemplate template = new KafkaTemplate<Integer, String>(producerFactory());
        return template;
    }

    public static KafkaTemplate<Integer, String> getQzKafkaTemplate() {
        KafkaTemplate template = new KafkaTemplate<Integer, String>(new DefaultKafkaProducerFactory<>(senderProps(QZ_PRODUCER_CONFIG_MAP)));
        return template;
    }

    @EventListener(ApplicationReadyEvent.class)
    public void onEvent(){
        try {
            // 设置为 SASL_PLAINTEXT，表示使用 SASL 认证，不加密
            if(!securityProtocol.isEmpty()){
                QZ_PRODUCER_CONFIG_MAP.put("security.protocol", securityProtocol);
            }
            if(!saslMechanism.isEmpty()){
                // 配置 SASL 认证机制
                QZ_PRODUCER_CONFIG_MAP.put("sasl.mechanism", saslMechanism);
            }
            if(!jaasConfig.isEmpty()){
                // 配置 JAAS 登录模块
                QZ_PRODUCER_CONFIG_MAP.put("sasl.jaas.config",jaasConfig);
            }
        }catch (Exception e){
            log.error("kafka init auth error:",e);
        }
    }
}
