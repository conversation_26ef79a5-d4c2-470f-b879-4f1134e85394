package com.quanzhi.auditapiv2.core.model;

import com.alibaba.fastjson.JSONObject;
import com.quanzhi.auditapiv2.common.dal.entity.ScanTask;
import com.quanzhi.auditapiv2.common.dal.entity.ScanTaskResult;
import lombok.Data;

import java.time.Instant;

/**
 * <AUTHOR>
 * @date 2019/12/13
 * @desc 报告执行动作
 */
@Data
public class ScanTaskAction {

    /**
     * 报表立即执行状态
     */
    public static final String TASK_EXECUTE = "execute";
    /**
     * 报表删除
     */
    public static final String TASK_DEL = "delete";

    /**
     * 报表停止运行
     */
    public static final String TASK_STOP = "stop";


    public ScanTaskAction(String taskId, String resultId, String taskCode, JSONObject parasm, JSONObject metaJson,JSONObject scan){
        this.resultId = resultId;
        this.taskId = taskId;
        this.paramsJson = parasm;
        this.metaJson = metaJson;
        this.code = taskCode;
        this.tm = Instant.now().toEpochMilli();
        this.scan = scan;
    }

    public ScanTaskAction(String taskId, String resultId, String taskCode, JSONObject parasm, JSONObject metaJson){
        this.resultId = resultId;
        this.taskId = taskId;
        this.paramsJson = parasm;
        this.metaJson = metaJson;
        this.code = taskCode;
        this.tm = Instant.now().toEpochMilli();
    }

    private String id;

    /**
     * 任务动作
     * TASK_EXECUTE
     * TASK_DEL
     * TASK_STOP
     */
    private String action;

    /**
     * @see ScanTaskResult#getId()
     */
    private String resultId;

    /**
     * @see ScanTask#getId()
     */
    private String taskId;

    /**
     * taskCode
     */
    private String code;

    /**
     * @see ScanTask#getParamsJson() ()
     */
    private JSONObject paramsJson;

    /**
     * 报表元信息
     */
    private JSONObject metaJson;

    private Long tm;

    private JSONObject scan;
}
