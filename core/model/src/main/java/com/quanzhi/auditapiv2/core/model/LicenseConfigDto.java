package com.quanzhi.auditapiv2.core.model;

import lombok.Data;

@Data
public class LicenseConfigDto {
    /**
     * 产品编号
     */
    private String productId;

    /**
     * 产品名称
     */
    private String productName;

    /**
     * 版本号
     */
    private String version;

    /**
     * 版本类型
     */
    private String version_type;

    /**
     * 设备唯一编号
     */
    private String uniqueCode;

    /**
     * 产品序列
     */
    private String product_serial;

    /**
     * 开始时间(时间戳)
     */
    private String start_time;

    /**
     * 结束时间(时间戳)
     */
    private String end_time;

    /**
     * 签发时间(时间戳)
     */
    private String issued;

    /**
     * 当前授权文件状态  0:未授权  1:已授权  2:已过期
     */
    private Integer state;

    /**
     * 产品组件模块
     */
    private String modules;

    private String company;

    /**
     * since 3.2.0 只是每个版本授权里面扩展模块的值
     */
    private String extendModules;

    /**
     * since 3.2.0 合并了授权里面扩展模块的值，还有默认的值
     */
    private String allModules;

    /**
     * 授权的吞吐量，QPS
     */
    private String throughput;
}
