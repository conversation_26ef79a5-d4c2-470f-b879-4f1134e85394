package com.quanzhi.auditapiv2.core.model.subscription;

import com.alibaba.fastjson.JSONObject;
import com.quanzhi.audit_core.common.risk.RiskInfo;
import com.quanzhi.auditapiv2.common.dal.entity.aggRisk.AggRiskInfo;
import com.quanzhi.metabase.core.model.event.ApiWeaknessPayload;
import com.quanzhi.metabase.core.model.event.ResourceChangedEvent;
import com.quanzhi.metabase.core.model.http.HttpResourceConstant;

import java.util.Map;

/**
 * <AUTHOR>
 * @date 2021/10/19 11:11 上午
 */
public class LevelRule implements SubscribeRule {
    @Override
    public boolean match(ResourceChangedEvent resourceChangedEvent, String operator, String content) {
        if (content == null) {
            return false;
        }
        if (!HttpResourceConstant.API_WEAKNESS.equals(resourceChangedEvent.getResourceType())) {
            return false;
        }
        ApiWeaknessPayload apiWeaknessPayload
                = new JSONObject((Map<String, Object>) resourceChangedEvent.getPayload())
                .toJavaObject(ApiWeaknessPayload.class);
        String[] contentArray = content.split(",");
        for (String c : contentArray) {
            if (c.equals(String.valueOf(apiWeaknessPayload.getLevel()))) {
                return true;
            }
        }
        return false;
    }

    @Override
    public boolean match(RiskInfo riskInfo, String operator, String content) {
        if (content == null) {
            return false;
        }
        String[] contentArray = content.split(",");
        for (String c : contentArray) {
            if (c.equals(String.valueOf(riskInfo.getLevel()))) {
                return true;
            }
        }
        return false;
    }

    @Override
    public boolean match(AggRiskInfo aggRiskInfo, String operator, String content) {
        if (content == null) {
            return false;
        }
        String[] contentArray = content.split(",");
        for (String c : contentArray) {
            if (c.equals(String.valueOf(aggRiskInfo.getLevel()))) {
                return true;
            }
        }
        return false;
    }
}
