package com.quanzhi.auditapiv2.core.model;

import com.alibaba.fastjson.JSON;
import com.quanzhi.auditapiv2.common.util.utils.DataUtil;
import com.quanzhi.auditapiv2.common.util.utils.MD5Util;
import lombok.Data;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2021/2/2 下午2:03
 */
@Data
public class UaType {

    private String defaultUaType;

    private List<UaTypeDetail> uaTypeConfigs;

    @Data
    public static class UaTypeDetail {

        private String uaType;
        private String standardUaType;
        private Boolean enable;
        private Integer type;
        private UaTypeConfig uaTypeConfig;

    }

    public static enum TypeEnum {
        SYSTEM(0),
        CUSTOM(1);

        private int val;

        private TypeEnum(int val) {
            this.val = val;
        }

        public Integer val() {
            return this.val;
        }
    }

    @Data
    public static class UaTypeConfig {

        private Boolean ignoreCaseEnable;

        private MatchPolicy matchPolicy;

        private List<String> keywords;
    }

    public enum MatchPolicy {
        KEYWORD
    }

    public static String createId(UaTypeDetail uaTypeDetail) {

        return MD5Util.md5( JSON.toJSONString(uaTypeDetail) );
    }

    public static List<UaTypeDto> getUaTypeDtos( UaType uaType ) {

        if(uaType!=null&&uaType.getUaTypeConfigs() != null ) {
            List<UaTypeDto> uaTypeDtos = uaType.getUaTypeConfigs().stream().map(uaTypeDetail -> convertUaTypeDetail2UaTypeDto(uaTypeDetail)).collect(Collectors.toList());
            return uaTypeDtos;

        } else {

            return new ArrayList<>();
        }
    }

    public static UaTypeDto getUaTypeDtoById(String id,List<UaTypeDto> uaTypeDtos) {

        Optional<UaTypeDto> uaTypeDtoOptional =  uaTypeDtos.stream().filter( uaTypeDto -> uaTypeDto.getId().equals(id) ).findFirst();

        if(uaTypeDtoOptional.isPresent()) {

            return  uaTypeDtoOptional.get();

        } else {

            return null;
        }
    }

    public static UaTypeDto convertUaTypeDetail2UaTypeDto(UaTypeDetail uaTypeDetail) {

        UaTypeDto uaTypeDto = new UaTypeDto();

        uaTypeDto.setId(  createId(uaTypeDetail) );
        uaTypeDto.setUaType( uaTypeDetail.getUaType() );
        uaTypeDto.setStandardUaType(uaTypeDetail.getStandardUaType());
        uaTypeDto.setEnable( uaTypeDetail.getEnable() );
        uaTypeDto.setType(uaTypeDetail.getType()==null?TypeEnum.CUSTOM.val():uaTypeDetail.getType());

        UaTypeConfig uaTypeConfig = new UaTypeConfig();
        uaTypeConfig.setIgnoreCaseEnable( uaTypeDetail.getUaTypeConfig()==null?true:uaTypeDetail.getUaTypeConfig().getIgnoreCaseEnable());
        uaTypeConfig.setKeywords( uaTypeDetail.getUaTypeConfig()==null?new ArrayList<>():uaTypeDetail.getUaTypeConfig().getKeywords());

        uaTypeDto.setUaTypeConfig( uaTypeConfig );
        return uaTypeDto;
    }

    public static UaTypeDetail deleteUaType(List<UaTypeDetail> uaTypeConfigs,String id) {

        Iterator<UaTypeDetail> it = uaTypeConfigs.iterator();
        while (it.hasNext()) {

            UaTypeDetail uaTypeDetail = it.next();
            if ( createId(uaTypeDetail).equals(id)) {
                it.remove();
                return uaTypeDetail;
            }
        }
        return null;
    }

    public static List<Map<String,String>> getUaTypeEnum(List<UaTypeDetail> uaTypeConfigs) {

        return uaTypeConfigs.stream().map(uaTypeDetail -> {

            Map<String,String> map = new HashMap<>();

            map.put("id",uaTypeDetail.getUaType());
            map.put("name",uaTypeDetail.getStandardUaType());

            return map;
        }).collect(Collectors.toList());
    }


    /**
     * 更新终端类型列表
     * @param uaTypeConfigs
     * @param uaTypeDto
     * @return
     */
    public static List<UaTypeDetail> updateFromUaTypeDto (List<UaTypeDetail> uaTypeConfigs,UaTypeDto uaTypeDto) throws Exception{

        if(DataUtil.isEmpty( uaTypeDto.getId() )) {

            UaTypeDetail newUaTypeDetail = createUaTypeDetailFromUaTypeDto(uaTypeDto);

            Optional<UaTypeDetail> uaTypeDetailOptional = uaTypeConfigs.stream().filter(uaTypeDetail -> {

                        return createId(uaTypeDetail).equals( createId(newUaTypeDetail) )
                                || uaTypeDetail.getUaType().equals( newUaTypeDetail.getUaType() );
                    }).findFirst();

            if(uaTypeDetailOptional.isPresent()) {
                throw new Exception("已存在相同的终端配置或存在相同的终端配置名称");
            }

            uaTypeConfigs.add( newUaTypeDetail );

        } else {

            Optional<UaTypeDetail> checkUaTypeDetailOptional = uaTypeConfigs.stream().filter(uaTypeDetail -> {

                return !createId(uaTypeDetail).equals( uaTypeDto.getId() ) && uaTypeDetail.getUaType().equals( uaTypeDto.getUaType() );
            }).findFirst();

            if(checkUaTypeDetailOptional.isPresent()) {
                throw new Exception("存在相同的终端配置名称");
            }

            Optional<UaTypeDetail> uaTypeDetailOptional = uaTypeConfigs.stream().filter(uaTypeDetail -> createId(uaTypeDetail).equals( uaTypeDto.getId() )).findFirst();

            if(uaTypeDetailOptional.isPresent()) {

                UaTypeDetail uaTypeDetail = uaTypeDetailOptional.get();
                updateUaTypeDetailFromUaTypeDto(uaTypeDetail,uaTypeDto);
            } else {

                UaTypeDetail uaTypeDetail = createUaTypeDetailFromUaTypeDto(uaTypeDto);
                uaTypeConfigs.add( uaTypeDetail );
            }
        }

        return uaTypeConfigs;
    }

    public static UaTypeDetail createUaTypeDetailFromUaTypeDto (UaTypeDto uaTypeDto) {

        UaTypeDetail uaTypeDetail = new UaTypeDetail();

        uaTypeDetail.setUaType( uaTypeDto.getUaType() );
        uaTypeDetail.setEnable( uaTypeDto.getEnable() );
        uaTypeDetail.setType(uaTypeDto.getType());
        uaTypeDetail.setStandardUaType(uaTypeDto.getStandardUaType()==null?uaTypeDto.getUaType():uaTypeDto.getStandardUaType());

        UaTypeConfig uaTypeConfig = new UaTypeConfig();
        uaTypeConfig.setIgnoreCaseEnable( true );
        uaTypeConfig.setMatchPolicy( MatchPolicy.KEYWORD );

        if( uaTypeDto.getUaTypeConfig() != null) {

            uaTypeConfig.setKeywords( uaTypeDto.getUaTypeConfig().getKeywords()  );

            if( uaTypeDto.getUaTypeConfig().getIgnoreCaseEnable() != null ) {

                uaTypeConfig.setIgnoreCaseEnable( uaTypeDto.getUaTypeConfig().getIgnoreCaseEnable() );
            }
        }

        uaTypeDetail.setUaTypeConfig( uaTypeConfig );

        return uaTypeDetail;
    };

    public static void updateUaTypeDetailFromUaTypeDto (UaTypeDetail uaTypeDetail,UaTypeDto uaTypeDto) {

        uaTypeDetail.setUaType( uaTypeDto.getUaType() );
        uaTypeDetail.setStandardUaType(uaTypeDto.getStandardUaType());
        uaTypeDetail.setEnable( uaTypeDto.getEnable() );

        UaTypeConfig uaTypeConfig = uaTypeDetail.getUaTypeConfig();
        uaTypeConfig.setIgnoreCaseEnable( true );
        uaTypeConfig.setMatchPolicy( MatchPolicy.KEYWORD );

        if( uaTypeDto.getUaTypeConfig() != null) {

            uaTypeConfig.setKeywords( uaTypeDto.getUaTypeConfig().getKeywords()  );

            if( uaTypeDto.getUaTypeConfig().getIgnoreCaseEnable() != null ) {

                uaTypeConfig.setIgnoreCaseEnable( uaTypeDto.getUaTypeConfig().getIgnoreCaseEnable() );
            }
        }
    };


}
