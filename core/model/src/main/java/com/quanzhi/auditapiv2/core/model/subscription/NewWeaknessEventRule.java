package com.quanzhi.auditapiv2.core.model.subscription;

import com.alibaba.fastjson.JSONObject;
import com.quanzhi.audit_core.common.risk.RiskInfo;
import com.quanzhi.auditapiv2.common.dal.entity.aggRisk.AggRiskInfo;
import com.quanzhi.metabase.core.model.event.ApiWeaknessPayload;
import com.quanzhi.metabase.core.model.event.EventType;
import com.quanzhi.metabase.core.model.event.ResourceChangedEvent;
import com.quanzhi.metabase.core.model.http.HttpResourceConstant;

import java.util.Arrays;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2020/8/14 下午3:11
 */
public class NewWeaknessEventRule implements SubscribeRule{

    /**
     *
     * @param resourceChangedEvent
     * @param operator
     * @param content
     * @return
     */
    @Override
    public boolean match (ResourceChangedEvent resourceChangedEvent, String operator, String content) {

        if(HttpResourceConstant.API_WEAKNESS.equals(resourceChangedEvent.getResourceType())
                && EventType.NewResourceEvent.equals( resourceChangedEvent.getEventType() )) {

            ApiWeaknessPayload apiWeaknessPayload = new JSONObject((Map<String, Object>) resourceChangedEvent.getPayload()).toJavaObject(ApiWeaknessPayload.class);

            SubscribeRuleOperateEnum operateEnum = SubscribeRuleOperateEnum.valueOf( operator );

            boolean match = false;

            switch (operateEnum) {

                case CONTAIN:

                    List<String> thresholdLabels = Arrays.asList(content.split(ARRAY_SEPARATOR));

                    if (thresholdLabels == null || thresholdLabels.size() <= 0) {
                        match = false;
                        break;
                    }

                    match = thresholdLabels.contains(apiWeaknessPayload.getWeaknessId());

                    break;

                case EQUAL:

                    match = apiWeaknessPayload.getWeaknessId().equals( content );
                    break;

                    default: break;
            }

            return match;

        } else {

            return false;
        }
    }

    @Override
    public boolean match(RiskInfo riskInfo, String operator, String content) {
        return false;
    }

    @Override
    public boolean match(AggRiskInfo aggRiskInfo, String operator, String content) {
        return false;
    }
}
