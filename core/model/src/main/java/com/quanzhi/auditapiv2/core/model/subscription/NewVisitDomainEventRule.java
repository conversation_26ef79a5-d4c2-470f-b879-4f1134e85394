package com.quanzhi.auditapiv2.core.model.subscription;

import com.quanzhi.audit_core.common.risk.RiskInfo;
import com.quanzhi.auditapiv2.common.dal.entity.aggRisk.AggRiskInfo;
import com.quanzhi.metabase.core.model.event.ResourceChangedEvent;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/8/14 下午4:42
 */
public class NewVisitDomainEventRule extends  NewPolicyListEventRule  {

    private String playLoadKey = "newVisitDomains";

    @Override
    public List<String> getNewPolicyList(ResourceChangedEvent resourceChangedEvent){

        return super.getNewList(resourceChangedEvent,playLoadKey);
    }

    @Override
    public boolean match(RiskInfo riskInfo, String operator, String content) {
        return false;
    }

    @Override
    public boolean match(AggRiskInfo aggRiskInfo, String operator, String content) {
        return false;
    }
}
