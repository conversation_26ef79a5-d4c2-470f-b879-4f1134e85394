package com.quanzhi.auditapiv2.core.model;

import com.quanzhi.operate.atomDefinition.ActionConfigFrontTransform;
import lombok.Data;

import java.util.List;

@Data
public class FeatureEnumDTO {

    private String id;

    private String name;

    private String dataType;

    private String fillType;

    private Object value;

    private String dataSource;

    private List<NacosConfig> nacosConfigs;

    @Data
    public static class  NacosConfig {

        private String dataId;

        private String groupId;

        private String udf;
    }

    private ActionConfigFrontTransform actionConfigFrontTransform;

}
