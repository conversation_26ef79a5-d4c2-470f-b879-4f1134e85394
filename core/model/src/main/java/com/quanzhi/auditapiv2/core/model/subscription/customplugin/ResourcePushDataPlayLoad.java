package com.quanzhi.auditapiv2.core.model.subscription.customplugin;

import com.quanzhi.auditapiv2.core.model.subscribeOutput.SubscribeMsg2OtherModel;
import com.quanzhi.metabase.core.model.event.ResourceChangedEvent;
import lombok.Data;

/**
 * <AUTHOR>
 * create at 2022/3/31 4:43 下午
 * @description: 资产、弱点 数据负载
 **/
@Data
public class ResourcePushDataPlayLoad {


    /**
     * 原始的数据变更事件
     */
    private ResourceChangedEvent resourceChangedEvent;

    /**
     *  相关字段转换后的数据
     */
    private SubscribeMsg2OtherModel subscribeMsg2OtherModel;


}