package com.quanzhi.auditapiv2.core.model;

import com.quanzhi.auditapiv2.common.dal.dataobject.SysRole;
import com.quanzhi.auditapiv2.common.util.utils.BeanConvertUtils;
import lombok.Data;

import java.util.List;

/**
 * @Description: 用户角色实体类
 * @Author: danniel.yu
 * @Date: 2020-05-11 15:24
 */
@Data
public class SysRoleDto {
    /**
     * 角色id
     */
    private String id;

    /**
     * 角色名称
     */
    private String roleName;

    /**
     * 角色标识
     */
    private String roleSign;

    /**
     * 备注
     */
    private String remark;

    private List<Long> menuIdList;

    private List<Long> orgIdList;

    private List<String> AssetsIdList;

    private List<String> FrailtyIdList;

    private List<String> ReportIdList;

    private List<String> DataIdList;
    
    /**
     * 角色类型（1-系统内置角色，2-自定义角色）
     */
    private Integer type;

    /**
     * 状态（0-禁用，1-正常）
     */
    private Integer status;

    /**
     * 创建者id
     */
    private Long userIdCreate;

    /**
     * 创建者name
     */
    private String userNameCreate;

    /**
     * 创建时间
     */
    private Long gmtCreate;

    /**
     * 修改时间
     */
    private Long gmtModified;

//    private Set<Menu> menus;//该角色对应的菜单
//
//    private Set<Auth> auths;//该角色对应的权限

    public static SysRoleDto readFromDO(SysRole sysRole) {
        return BeanConvertUtils.convertTo(sysRole, SysRoleDto.class);
    }

    public SysRole convertToDO() {
        return BeanConvertUtils.convertTo(this, SysRole.class);
    }

}
