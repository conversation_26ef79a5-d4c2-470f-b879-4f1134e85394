package com.quanzhi.auditapiv2.core.model.subscription;

import com.quanzhi.audit_core.common.risk.RiskInfo;
import com.quanzhi.auditapiv2.common.dal.entity.aggRisk.AggRiskInfo;
import com.quanzhi.auditapiv2.common.util.utils.DataUtil;
import com.quanzhi.metabase.core.model.event.EventType;
import com.quanzhi.metabase.core.model.event.ResourceChangedEvent;

/**
 * <AUTHOR>
 * @date 2020/8/14 下午3:11
 */
public class NewResourceEventRule implements SubscribeRule{

    /**
     *
     * @param resourceChangedEvent
     * @param operator
     * @param content
     * @return
     */
    @Override
    public boolean match (ResourceChangedEvent resourceChangedEvent, String operator, String content) {

        if(DataUtil.isNotEmpty( resourceChangedEvent.getEventType() )
                && EventType.NewResourceEvent.equals( resourceChangedEvent.getEventType() )) {

            return true;
        } else {
            return false;
        }
    }

    @Override
    public boolean match(RiskInfo riskInfo, String operator, String content) {
        return false;
    }

    @Override
    public boolean match(AggRiskInfo aggRiskInfo, String operator, String content) {
        return false;
    }
}
