package com.quanzhi.auditapiv2.core.model;

/**
 * @Author: Ha<PERSON><PERSON><PERSON>
 * @Date: 2019/12/8 11:50 上午
 * <p>
 * 所有的预置topic需在此声明，以枚举名作为topicId，通过topicId统一获取真实的topic名
 * 如ApiEvents作为全流量事件的topicId，其默认topic名为ApiEvents，在客户现场可能会改名为user.ApiEvents
 * 这种情况下，可以修改配置文件config/topic.conf中ApiEvents对应的topic为user.ApiEvents做到，无需变更代码
 * </p>
 *
 */
public enum TopicIdEnum {


    /**
     * 报告模块操作事件
     */
    ReportAction("报告模块操作事件", "ReportAction");

    private String topicName;
    private String defaultTopic;

    TopicIdEnum(String topicName, String defaultTopic) {
        this.topicName = topicName;
        this.defaultTopic = defaultTopic;
    }

    /**
     * 返回当前topic的id，即Enum变量的name
     * @return
     */
    public final String getTopicId() {
        return name();
    }

    /**
     * 获取当前topic的展示名
     * @return
     */
    public final String getTopicName() {
        return topicName;
    }

    /**
     * 获取当前topic的默认值
     * @return
     */
    public final String getDefaultTopic() {
        return defaultTopic;
    }

}
