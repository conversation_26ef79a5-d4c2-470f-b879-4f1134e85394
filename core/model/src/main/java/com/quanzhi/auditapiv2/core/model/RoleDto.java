package com.quanzhi.auditapiv2.core.model;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * @Description: 用户角色实体类
 * @Author: danniel.yu
 * @Date: 2020-05-11 15:24
 */
@Data
public class RoleDto {
    /**
     * 角色id
     */
    private String id;

    /**
     * 角色名称
     */
    private String roleName;

    /**
     * 备注
     */
    private String remark;

    /**
     * 创建者id
     */
    private String userIdCreate;

    /**
     * 创建者name
     */
    private String userNameCreate;


    //该角色对应的资产权限Id列表
    @ApiModelProperty("该角色对应的资产权限Id列表")
    private List<String> AssetsIdList;

    //该角色对应的脆弱性权限Id列表
    @ApiModelProperty("该角色对应的脆弱性权限Id列表")
    private List<String> FrailtyIdList;

    //该角色对应的报告权限Id列表
    @ApiModelProperty("该角色对应的报告权限Id列表")
    private List<String> ReportIdList;

    //该角色对应的数据权限Id列表
    @ApiModelProperty("该角色对应的数据权限Id列表")
    private List<String> DataIdList;

    /**
     * 创建时间
     */
    private Date gmtCreate;

    /**
     * 修改时间
     */
    private Date gmtModified;

}
