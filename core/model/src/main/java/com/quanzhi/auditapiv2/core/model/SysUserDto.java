package com.quanzhi.auditapiv2.core.model;

import com.quanzhi.auditapiv2.common.dal.dataobject.DataGroup;
import com.quanzhi.auditapiv2.common.dal.dataobject.SysUser;
import com.quanzhi.auditapiv2.common.util.utils.BeanConvertUtils;
import com.quanzhi.auditapiv2.common.util.utils.DataUtil;
import lombok.Data;

import java.util.List;

/**
 * @Description:
 * @Author: danniel.yu
 * @Date: 2020-05-11 15:24
 */
@Data
public class SysUserDto {

    /**
     * 用户id
     */
    private String id;

    /**
     * 用户名
     */
    private String username;

    /**
     * 密码
     */
    private String password;

    /**
     * google 身份认证密钥
     */
    private String googleAuthSecret;

    /**
     * 密钥二维码字符串
     */
    private String googleSecretQRCode;

    /**
     * 邮箱
     */
    private String email;

    /**
     * 手机号
     */
    private String mobile;

    /**
     * 用户类型（1-系统管理员，2-审计员，3-普通用户）
     */
    private Integer type;

    /**
     * 状态(0：禁用   1：正常)
     */
    private Integer status;

    /**
     * 创建用户id
     */
    private String userIdCreate;

    /**
     * 备注
     */
    private String remark;

    /**
     * 创建时间
     */
    private Long gmtCreate;

    /**
     * 修改时间
     */
    private Long gmtModified;

    /**
     * 角色id列表
     */
    private String roleId;
    /**
     * 角色名称
     */
    private String roleName;


    /**
     * 解锁时间
     */
    private Long lockTime;

    /**
     * 有效时间
     */
    private Long validTime;

    /**
     * 错误次数
     */
    private Integer errorCount;

    /**
     * 是否锁定
     */
    private Boolean lock;

    private String resource;
    /**
     * 是否内置 true:内置
     */
    private Boolean defaultFlag;
    /**
     * 数据分组ids
     */
    private List<String> dataGroupIds;
    /**
     * 数据分组names
     */
    private List<String> dataGroupNames;
    /**
     * 数据分组详细信息
     */
    private DataGroup dataGroup;


    public static SysUserDto readFromDO(SysUser sysUser) {
        SysUserDto sysUserDto = BeanConvertUtils.convertTo(sysUser, SysUserDto.class);

        if(DataUtil.isNotEmpty(sysUser.getLockTime()) && sysUser.getLockTime() > System.currentTimeMillis()) {

            sysUserDto.setLock( true );
        } else {
            sysUserDto.setLock( false );
        }
        return sysUserDto;
    }

    public SysUser convertToDO() {
        return BeanConvertUtils.convertTo(this, SysUser.class);
    }

//    public SysUserDto convertToDTO() {
//        ResourceDTO resourceDTO = BeanConvertUtils.convertTo(this, ResourceDTO.class);
//        resourceDTO.setType(this.type == null ? null : this.type.getCode())
//                .setMethod(this.method == null ? null : this.method.getCode())
//                .setStyle(this.style == null ? null : this.style.getCode())
//                .setMatchKind(this.matchKind == null ? null : this.matchKind.getCode())
//                .setStatus(this.status == null ? null : this.status.getCode())
//                .setOpType(this.opType == null ? null : this.opType.getCode());
//        return resourceDTO;
//    }
//
//    public ResourceDTO convertToDTORecursive() {
//        ResourceDTO resourceDTO = this.convertToDTO();
//        if (CollectionUtils.isEmpty(this.getChildren())) {
//            return resourceDTO;
//        }
//
//        List<ResourceDTO> resourceDTOList = new ArrayList<>();
//        for (Resource child : this.getChildren()) {
//            resourceDTOList.add(child.convertToDTORecursive());
//        }
//        resourceDTO.setChildren(resourceDTOList);
//
//        return resourceDTO;
//    }


}
