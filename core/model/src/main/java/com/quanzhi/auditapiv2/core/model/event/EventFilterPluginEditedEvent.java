package com.quanzhi.auditapiv2.core.model.event;

import com.quanzhi.audit_core.common.model.EventFilterPlugin;
import lombok.AllArgsConstructor;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2022/3/11 10:56 上午
 */
@AllArgsConstructor
@Data
public class EventFilterPluginEditedEvent {
    private EventFilterPlugin eventFilterPlugin;
    private String action;

    public static final String DISABLE = "disable";
    public static final String ENABLE = "enable";

}
