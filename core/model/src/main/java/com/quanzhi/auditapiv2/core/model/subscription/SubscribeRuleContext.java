package com.quanzhi.auditapiv2.core.model.subscription;

import com.quanzhi.audit_core.common.risk.RiskInfo;
import com.quanzhi.auditapiv2.common.dal.entity.aggRisk.AggRiskInfo;
import com.quanzhi.metabase.core.model.event.ResourceChangedEvent;
import lombok.extern.slf4j.Slf4j;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2020/8/14 下午2:13
 */
@Slf4j
public class SubscribeRuleContext {


    private static final Map<String,SubscribeRule> subscribeRuleMap;

    /**
     * 新增接口
     */
    public static final String NEW_RESOURCE_EVENT = "newResourceEvent";

    /**
     * 新增推荐接口
     */
    public static final String NEW_RECOMMEND_RESOURCE_EVENT = "newRecommendResourceEvent";

    /**
     * 新增请求数据标签
     */
    public static final String NEW_REQ_DATA_LABEL_EVENT = "newReqDataLabelEvent";

    /**
     * 新增返回数据标签
     */
    public static final String NEW_RSP_DATA_LABEL_EVENT = "newRspDataLabelEvent";

    /**
     * 新增接口标识
     */
    public static final String NEW_API_FEATURE_LABEL_EVENT = "newApiFeatureLabelEvent";

    /**
     * 新增接口类型
     */
    public static final String NEW_API_CLASSIFICATION_EVENT = "newApiClassificationEvent";

    /**
     * 新增请求类型
     */
    public static final String NEW_REQ_CONTENT_TYPE_EVENT = "newReqContentTypeEvent";

    /**
     * 新增返回类型
     */
    public static final String NEW_RSP_CONTENT_TYPE_EVENT = "newRspContentTypeEvent";

    /**
     * 新增访问域
     */
    public static final String NEW_VISIT_DOMAIN_EVENT = "newVisitDomainEvent";

    /**
     * 新增部署域
     */
    public static final String NEW_DEPLOY_DOMAIN_EVENT = "newDeployDomainEvent";

    /**
     * 新增弱点
     */
    public static final String NEW_WEAKNESS_EVENT="newWeaknessEvent";

    /**
     * 异常
     */
    public static final String NEW_RISK_EVENT="newRiskEvent";

    /**
     * 新增应用
     */
    public static final String NEW_APP_EVENT="newAppEvent";

    public static final String LEVEL = "level";

    static {
        subscribeRuleMap = new HashMap<>();
        subscribeRuleMap.put(NEW_RESOURCE_EVENT,new NewResourceEventRule());
        subscribeRuleMap.put(NEW_RECOMMEND_RESOURCE_EVENT,new NewRecommendResourceEventRule());
        subscribeRuleMap.put(NEW_REQ_DATA_LABEL_EVENT,new NewReqDataLabelEventRule());
        subscribeRuleMap.put(NEW_RSP_DATA_LABEL_EVENT,new NewRspDataLabelEventRule());
        subscribeRuleMap.put(NEW_API_FEATURE_LABEL_EVENT,new NewApiFeatureLabelEventRule());
        subscribeRuleMap.put(NEW_API_CLASSIFICATION_EVENT,new NewApiClassificationEventRule());
        subscribeRuleMap.put(NEW_REQ_CONTENT_TYPE_EVENT,new NewReqContentTypeEventRule());
        subscribeRuleMap.put(NEW_RSP_CONTENT_TYPE_EVENT,new NewRspContentTypeEventRule());
        subscribeRuleMap.put(NEW_VISIT_DOMAIN_EVENT,new NewVisitDomainEventRule());
        subscribeRuleMap.put(NEW_DEPLOY_DOMAIN_EVENT,new NewDeployDomainEventRule());
        subscribeRuleMap.put(NEW_WEAKNESS_EVENT,new NewWeaknessEventRule());
        subscribeRuleMap.put(NEW_RISK_EVENT,new NewRiskEventRule());
        subscribeRuleMap.put(LEVEL, new LevelRule());
    }

    public static final boolean match(ResourceChangedEvent resourceChangedEvent,String policy,String operate,String content) {

        SubscribeRule subscribeRule = subscribeRuleMap.get(policy);
        if (subscribeRule == null) {
            log.warn("miss this subscribeRule policy: {}",policy);
            return false;
        } else {
            return subscribeRule.match(resourceChangedEvent, operate,content);
        }
    }

    public static final boolean match(RiskInfo riskInfo, String policy, String operate, String content) {

        SubscribeRule subscribeRule = subscribeRuleMap.get(policy);
        if (subscribeRule == null) {
            log.warn("miss this subscribeRule policy: {}",policy);
            return false;
        } else {
            return subscribeRule.match(riskInfo, operate,content);
        }
    }

    public static final boolean match(AggRiskInfo aggRiskInfo, String policy, String operate, String content) {

        SubscribeRule subscribeRule = subscribeRuleMap.get(policy);
        if (subscribeRule == null) {
            log.warn("miss this subscribeRule policy: {}",policy);
            return false;
        } else {
            return subscribeRule.match(aggRiskInfo, operate,content);
        }
    }

}
