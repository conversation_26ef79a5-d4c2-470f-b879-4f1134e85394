package com.quanzhi.auditapiv2.core.model.event;

import com.quanzhi.auditapiv2.common.dal.entity.aggRisk.AggRiskInfo;
import com.quanzhi.auditapiv2.core.risk.entity.RiskInfo;
import lombok.AllArgsConstructor;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2022/3/9 2:19 下午
 */
@AllArgsConstructor
@Data
public class RiskChangedEvent {

    private String id;

    private String target;

    private String risk;

    private int state;

    private String stateName;

    private String option;

    private AggRiskInfo riskInfo;

    public RiskChangedEvent(AggRiskInfo riskInfo) {
        this.riskInfo = riskInfo;
        this.id = riskInfo.getId();
        this.target = riskInfo.getEntities().get(0).getValue();
        this.risk = riskInfo.getName();
        this.state = riskInfo.getState();
        this.stateName = riskInfo.getStateName();
    }

    public RiskChangedEvent(AggRiskInfo riskInfo, String option) {
        this.riskInfo = riskInfo;
        this.id = riskInfo.getId();
        this.target = riskInfo.getEntities().get(0).getValue();
        this.risk = riskInfo.getName();
        this.state = riskInfo.getState();
        this.stateName = riskInfo.getStateName();
        this.option = option;
    }

}
