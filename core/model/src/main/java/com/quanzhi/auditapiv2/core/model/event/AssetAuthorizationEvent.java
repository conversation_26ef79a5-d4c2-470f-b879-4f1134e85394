package com.quanzhi.auditapiv2.core.model.event;

import lombok.AllArgsConstructor;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/3/11 10:12 上午
 */
@AllArgsConstructor
@Data
public class AssetAuthorizationEvent {

    private Integer type;

    private String oldRuleName;

    private String newRuleName;

    private List<String> addUsernameList;

    private List<String> delUsernameList;

    private List<String> addDepartmentList;

    private List<String> delDepartmentList;
    
    private List<String> addAppUriList;

    private List<String> delAppUriList;

    /**
     * 方法类型枚举类
     */
    public static enum TypeEnum {

        ADD("新增", 1),

        EDIT("编辑", 2),

        DEL("删除", 3);

        private String name;

        private Integer code;

        TypeEnum(String name, Integer code) {

            this.name = name;
            this.code = code;
        }

        public String getName() {
            return this.name;
        }

        public Integer getCode() {
            return this.code;
        }

        public static AssetAuthorizationEvent.TypeEnum getTypeEnum(Integer code) {

            for (AssetAuthorizationEvent.TypeEnum methodEnum : AssetAuthorizationEvent.TypeEnum.values()) {
                if(methodEnum.getCode() == code.intValue()) {
                    return methodEnum;
                }
            }
            return null;
        }
    }
}
