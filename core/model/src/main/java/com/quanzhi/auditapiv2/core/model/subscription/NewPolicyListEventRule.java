package com.quanzhi.auditapiv2.core.model.subscription;

import com.quanzhi.auditapiv2.common.util.utils.DataUtil;
import com.quanzhi.metabase.core.model.event.ResourceChangedEvent;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2020/8/14 下午3:42
 */
@Slf4j
public abstract class NewPolicyListEventRule implements SubscribeRule {

    protected abstract List<String> getNewPolicyList(ResourceChangedEvent resourceChangedEvent);

    protected List<String> getNewList(ResourceChangedEvent resourceChangedEvent, String playLoadKey) {

        List<String> policyList;

        policyList = DataUtil.isNotEmpty(resourceChangedEvent.getPayload()) && ((Map) resourceChangedEvent.getPayload()).containsKey(playLoadKey)
                ? (List<String>) ((Map) resourceChangedEvent.getPayload()).get(playLoadKey) : new ArrayList<>();

        return policyList;
    }

    protected List<String> getNewList(ResourceChangedEvent resourceChangedEvent, List<String> playLoadKeys) {

        List<String> policyList = new ArrayList<>();

        for (String playLoadKey : playLoadKeys) {
            policyList.addAll(DataUtil.isNotEmpty(resourceChangedEvent.getPayload()) && ((Map) resourceChangedEvent.getPayload()).containsKey(playLoadKey)
                    ? (List<String>) ((Map) resourceChangedEvent.getPayload()).get(playLoadKey) : new ArrayList<>());
        }

        return policyList;
    }

    @Override
    public boolean match(ResourceChangedEvent resourceChangedEvent, String operator, String content) {

        List<String> newPolicyList = getNewPolicyList(resourceChangedEvent);

        if (DataUtil.isNotEmpty(newPolicyList)) {

            try {

                String[] thresholdLabels = content.split(ARRAY_SEPARATOR);

                SubscribeRuleOperateEnum operateEnum = SubscribeRuleOperateEnum.valueOf(operator);

                boolean match = false;

                switch (operateEnum) {

                    case CONTAIN:

                        if (thresholdLabels == null || thresholdLabels.length <= 0) {
                            match = false;
                        }

                        for (int i = 0; i < thresholdLabels.length; i++) {

                            if (newPolicyList.contains(thresholdLabels[i])) {
                                match = true;
                                break;
                            }
                        }
                        break;

                    case EQUAL:

                        match = (newPolicyList.size() == thresholdLabels.length && newPolicyList.containsAll(Arrays.asList(thresholdLabels)))
                                ? true : false;
                }
                return match;

            } catch (Exception e) {

                log.warn("resourceChangedEvent match failed: {}", e);
                return false;
            }

        } else {

            return false;
        }

    }
}
