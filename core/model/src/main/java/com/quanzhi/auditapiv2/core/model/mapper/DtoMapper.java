package com.quanzhi.auditapiv2.core.model.mapper;

import com.quanzhi.auditapiv2.common.dal.dataobject.SysLog;
import com.quanzhi.auditapiv2.common.dal.dataobject.SysRole;
import com.quanzhi.auditapiv2.common.dal.dataobject.SysUser;
import com.quanzhi.auditapiv2.core.model.SysLogDto;
import com.quanzhi.auditapiv2.core.model.SysRoleDto;
import com.quanzhi.auditapiv2.core.model.SysUserDto;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 描述:
 *
 * @Author: danniel.yu
 * @Date: 2020-05-17 09:42
 */
@Mapper(componentModel = "spring")
public interface DtoMapper {
    DtoMapper INSTANCE = Mappers.getMapper( DtoMapper.class );

    SysUserDto ToUserDto(SysUser sysUser);
    SysUser ToUserDO(SysUserDto sysUserDto);
    List<SysUserDto> ToUserDtoList(List<SysUser> sysUserList);
    List<SysUser> ToUserDOList(List<SysUserDto> sysUserDtoList);

    SysRoleDto ToRoleDto(SysRole sysRole);
    SysRole ToRoleDO(SysRoleDto sysUserDto);
    List<SysRoleDto> ToRoleDtoList(List<SysRole> sysRoleList);
    List<SysRole> ToRoleDOList(List<SysRoleDto> sysRoleDtoList);

    SysLogDto ToLogDto(SysLog sysLog);
    SysLog ToLogDO(SysLogDto sysLogDto);
    List<SysLogDto> ToLogDtoList(List<SysLog> sysLogList);
    List<SysLog> ToLogDOList(List<SysLogDto> sysLogDtoList);

}
