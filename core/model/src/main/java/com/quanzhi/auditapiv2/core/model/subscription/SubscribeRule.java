package com.quanzhi.auditapiv2.core.model.subscription;

import com.quanzhi.audit_core.common.risk.RiskInfo;
import com.quanzhi.auditapiv2.common.dal.entity.aggRisk.AggRiskInfo;
import com.quanzhi.metabase.core.model.event.ResourceChangedEvent;

/**
 * <AUTHOR>
 * @date 2020/8/14 下午2:25
 */
public interface SubscribeRule {

    /**
     * 分隔符
     */
    String ARRAY_SEPARATOR = ",";

    boolean match(ResourceChangedEvent resourceChangedEvent, String operator, String content);

    boolean match(RiskInfo riskInfo, String operator, String content);

    boolean match(AggRiskInfo aggRiskInfo, String operator, String content);

}
