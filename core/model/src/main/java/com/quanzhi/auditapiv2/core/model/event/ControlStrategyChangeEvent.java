package com.quanzhi.auditapiv2.core.model.event;

import com.quanzhi.auditapiv2.common.dal.entity.ControlStrategy;
import com.quanzhi.auditapiv2.core.risk.entity.RiskInfo;
import lombok.AllArgsConstructor;
import lombok.Data;

/**
 * <AUTHOR>
 * create at 2022/12/19 2:22 下午
 * @description:
 **/
@Data
@AllArgsConstructor
public class ControlStrategyChangeEvent {

    private RiskInfo riskInfo;

    private ControlStrategy controlStrategy;

    private ControlContentChange controlContentChange;

    public enum ControlContentChange {
        ADD, DEL
    }

    public ControlStrategyChangeEvent(ControlStrategy controlStrategy, ControlContentChange controlContentChange) {
        this.controlStrategy = controlStrategy;
        this.controlContentChange = controlContentChange;
    }


}