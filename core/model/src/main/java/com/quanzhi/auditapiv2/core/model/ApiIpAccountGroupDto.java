package com.quanzhi.auditapiv2.core.model;

import com.quanzhi.audit_core.common.adaptive.AdaptiveCounting;
import com.quanzhi.audit_core.common.utils.DataUtil;
import lombok.Data;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2020/11/10 下午2:58
 */
@Data
public class ApiIpAccountGroupDto {

    private String ip;

    private String date;

    private String account;

    /**
     * 去重应用数
     */
    private Integer distinctAppCnt;

    /**
     * 去重接口数
     */
    private Integer distinctApiCnt;

    private Long visitCnt;

    private Long loginCnt;

    /**
     * 聚合后的事件标签
     */
    private List<List<String>> eventDefineIdGroupList;

    private List<String> eventDefineIdList;

    private List<String> rspDataLabelList;

    private List<String> appUriList;

    private List<String> apiUriList;

    private AdaptiveCounting rspDataBucket;

    private AdaptiveCounting downloadFileBucket;

    private AdaptiveCounting uploadFileBucket;

    /**
     * 聚合后的应用列表
     */
    private List<List<String>> appUriGroupList;

    /**
     * 聚合后的接口列表
     */
    private List<List<String>> apiUriGroupList;

    /**
     * 聚合后的返回数据标签
     */
    private List<List<String>> rspDataLabelGroupList;


    private Long rspDataLabelDistinctCnt;

    private Long rspDataDistinctCnt;

    /**
     * 聚合后数据标签
     */
    private List<Map<String,AdaptiveCounting>> rspDataBucketByLabelList;

    /**
     * 聚合后的数据标签数量统计
     */
    private List<AdaptiveCounting> rspDataBucketList;

    /**
     * 下载数量去重数
     */
    private Long downloadFileDistinctCnt;

    /**
     * 聚合后下载文件sha256的去重桶
     */
    private List<AdaptiveCounting> downloadFileBucketList;

    /**
     * 上传文件去重数
     */
    private Long uploadFileDistinctCnt;

    /**
     * 聚合后上传文件sha256的去重桶
     */
    private List<AdaptiveCounting> uploadFileBucketList;

    /**
     * 聚合后的小时纬度
     */
    private List<List<Long>> cntHrlyList;

    /**
     * 聚合后的分钟纬度
     */
    private List<List<Long>> cntMinuteList;

    public long getRspDataBucketDistinctCnt () {

        if(DataUtil.isNotEmpty( this.getRspDataBucketByLabelList() )) {

            Map<String,List<AdaptiveCounting>> rspDataBucketByLabelMap = new HashMap<>();

            for(Map<String,AdaptiveCounting> rspDataBucketByLabel : this.getRspDataBucketByLabelList()) {

                for(  Map.Entry<String,AdaptiveCounting> entry : rspDataBucketByLabel.entrySet() ) {

                    if(rspDataBucketByLabelMap.containsKey( entry.getKey() ) ) {

                        rspDataBucketByLabelMap.get( entry.getKey() ).add( entry.getValue() );
                    } else {
                        rspDataBucketByLabelMap.put(entry.getKey(), new ArrayList<>(Arrays.asList( entry.getValue() )));
                    }
                }
            }

            long distinctLabelCnt = 0L;

            for( List<AdaptiveCounting> adaptiveCountingList : rspDataBucketByLabelMap.values() ) {
                distinctLabelCnt += AdaptiveCounting.mergeAll(adaptiveCountingList).cardinality();
            }

            return distinctLabelCnt;

        } else {
            return 0L;
        }
    }

    /**
     * 获取去重敏感数量
     * @return
     */
    public long getRspDataAcResultCnt () {

        if(DataUtil.isNotEmpty( this.getRspDataBucketList() )) {

            long distinctLabelCnt = 0L;

            this.rspDataBucketList = this.getRspDataBucketList().stream().filter(i -> i != null).collect(Collectors.toList());

            if(this.rspDataBucketList.size() == 0) return 0L;

            distinctLabelCnt = AdaptiveCounting.mergeAll(this.getRspDataBucketList()).cardinality();

            return distinctLabelCnt;

        } else {
            return 0L;
        }
    }

    public long getUploadFileBucketDistinctCnt () {

        if(DataUtil.isNotEmpty(this.getUploadFileBucketList())){

            this.uploadFileBucketList = this.getUploadFileBucketList().stream().filter(i -> i != null).collect(Collectors.toList());

            if(this.uploadFileBucketList.size() == 0) return 0L;

            long distinctCnt = AdaptiveCounting.mergeAll( this.getUploadFileBucketList() ).cardinality();

            return distinctCnt;

        } else {

            return 0L;
        }
    }

    public long getDownloadFileBucketDistinctCnt () {

        if(DataUtil.isNotEmpty(this.getDownloadFileBucketList())){

            this.downloadFileBucketList = this.getDownloadFileBucketList().stream().filter(i -> i != null).collect(Collectors.toList());

            if(this.downloadFileBucketList.size() == 0) return 0L;

            long distinctCnt = AdaptiveCounting.mergeAll( this.getDownloadFileBucketList() ).cardinality();

            return distinctCnt;

        } else {

            return 0L;
        }
    }

    public int[] getHourAmountSum () {

        int[] hourAmountSum = new int[24];

        if(DataUtil.isNotEmpty( this.getCntHrlyList()  )) {

            for(List<Long> item : this.getCntHrlyList()) {

                for(int i = 0; i < hourAmountSum.length; i++) {
                    hourAmountSum[i] += item.get(i);
                }
            }
        }

        return hourAmountSum;
    }

    public int[] getHourAmountSumFromMinute () {

        int[] hourAmountSum = new int[24];

        if(DataUtil.isNotEmpty( this.getCntMinuteList()  )) {

            for(List<Long> item : this.getCntMinuteList()) {

                for(int i = 0; i < hourAmountSum.length; i++) {

                    int start = i * 60;
                    int end = (i + 1) * 60;

                    for (int j = start; j < end; j++) {

                        hourAmountSum[i] += item.get(j);
                    }
                }
            }
        }

        return hourAmountSum;
    }

    public List<String> getEventDefineIdListFromGroup () {

        if(DataUtil.isNotEmpty(this.getEventDefineIdGroupList())) {

            return this.getEventDefineIdGroupList().stream().flatMap(Collection::stream).distinct().collect(Collectors.toList());

        } else {
            return new ArrayList<>();
        }
    }

    public List<String> geRspDataLabelListFromGroup () {

        if(DataUtil.isNotEmpty(this.getRspDataLabelGroupList())) {

            return this.getRspDataLabelGroupList().stream().flatMap(Collection::stream).distinct().collect(Collectors.toList());

        } else {
            return new ArrayList<>();
        }
    }

    public Integer getDistinctAppCntFromGroup () {

        if(DataUtil.isNotEmpty(this.getAppUriGroupList())) {

            List<String> list = this.getAppUriGroupList().stream().flatMap(Collection::stream).distinct().collect(Collectors.toList());

            return list.size();

        } else {
            return 0;
        }
    }

    public Integer getDistinctApiCntFromGroup () {

        if(DataUtil.isNotEmpty(this.getApiUriGroupList())) {

            List<String> list = this.getApiUriGroupList().stream().flatMap(Collection::stream).distinct().collect(Collectors.toList());

            return list.size();

        } else {
            return 0;
        }
    }

    public long getRspDataLabelDistinctCntFromBucket () {

        if(DataUtil.isNotEmpty( this.getRspDataBucket() )) {

            return AdaptiveCounting.compress(this.getRspDataBucket()).cardinality();

        } else {

            return 0;
        }
    }

    public long geUploadFileDistinctCntFromBucket () {

        if(DataUtil.isNotEmpty( this.getUploadFileBucket() )) {

            return AdaptiveCounting.compress(this.getUploadFileBucket()).cardinality();

        } else {

            return 0;
        }
    }

    public long getDownloadFileDistinctCntFromBucket () {

        if(DataUtil.isNotEmpty( this.getDownloadFileBucket() )) {

            return AdaptiveCounting.compress(this.getDownloadFileBucket()).cardinality();

        } else {

            return 0;
        }
    }

}
