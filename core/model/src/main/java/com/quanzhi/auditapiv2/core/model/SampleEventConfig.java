package com.quanzhi.auditapiv2.core.model;

import com.quanzhi.metabase.core.model.http.HttpLocationEnum;

public class SampleEventConfig {

    /**
     * 路径类型枚举类
     */
    public enum PathTypeEnum {
        JSON(1),

        XML(2),

        HTML(3),

        REGEX(4),

        JSONP(5);

        private int val;

        PathTypeEnum(int val) {
            this.val = val;
        }

        public int value() {
            return this.val;
        }
    }

    /**
     * 关键信息存放位置枚举类
     *
     * @see com.quanzhi.metabase.core.model.http.HttpLocationEnum
     * 名称保持一致
     */
    public enum LocationEnum {

        /**
         * body
         */
        BODY(1, HttpLocationEnum.BODY),
        /**
         * cookie
         */
        COOKIE(2, HttpLocationEnum.COOKIE),

        /**
         * set-cookie
         */
        SETCOOKIE(3, HttpLocationEnum.SET_COOKIE),

        /**
         * get
         */
        GET(4, HttpLocationEnum.GET),

        /**
         * post
         */
        POST(5, HttpLocationEnum.POST),

        /**
         * 请求header
         */
        REQ_HEADER(6, HttpLocationEnum.REQ_HEADER),

        /**
         * 返回header
         */
        RSP_HEADER(7, HttpLocationEnum.RSP_HEADER),

        /**
         * 原始请求
         */
        REQ_RAW(8, HttpLocationEnum.REQ_RAW),

        /**
         * 原始返回
         */
        RSP_RAW(9, HttpLocationEnum.RSP_RAW);

        private int val;

        private HttpLocationEnum location;

        LocationEnum(int val, HttpLocationEnum location) {
            this.val = val;
            this.location = location;
        }

        public int value() {
            return this.val;
        }

        public int getVal(){
            return val;
        }

        public HttpLocationEnum getLocation() {
            return location;
        }

        public static final LocationEnum valueOf(int l){
            for (LocationEnum locationEnum : LocationEnum.values()) {
                if(locationEnum.val == l){
                    return locationEnum;
                }
            }
            return null;
        }

        public static Integer getVal(String str){

            for (LocationEnum locationEnum : LocationEnum.values()) {
                if(locationEnum.toString().equals(str)){
                    return locationEnum.getVal();
                }
            }
            return null;
        }
    }
}
