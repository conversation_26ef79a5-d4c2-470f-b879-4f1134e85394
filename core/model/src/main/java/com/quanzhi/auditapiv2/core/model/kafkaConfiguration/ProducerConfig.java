package com.quanzhi.auditapiv2.core.model.kafkaConfiguration;

import lombok.Data;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2020/8/18 上午10:00
 */
@Data
public class ProducerConfig {

    public static final String BOOTSTRAP_SERVERS_CONFIG = "bootstrap.servers";

    public static final String RETRIES_CONFIG = "producer.retries";

    public static final String PRODUCER_TIMEOUT_MS = "producer.timeout.ms";

    public static final String RETRY_BACKOFF_MS = "retry.backoff.ms";

    public static final String RETRIES = "retries";

    public static final String BATCH_SIZE_CONFIG = "producer.batch-size";

    public static final String BUFFER_MEMORY_CONFIG = "producer.buffer-memory";

    public static final String KEY_SERIALIZER_CLASS_CONFIG = "key.serializer";

    public static final String VALUE_SERIALIZER_CLASS_CONFIG = "value.serializer";

    // 这个生产者的配置主要是对接客户的，不要动
    public static final Map<String, String> producerConfigMap;

    // 内部的kafka,有认证的
    public static final Map<String, String> QZ_PRODUCER_CONFIG_MAP = new HashMap<>();

    static {
        producerConfigMap = new HashMap<>();
        producerConfigMap.put(BOOTSTRAP_SERVERS_CONFIG, "");
        producerConfigMap.put(RETRIES_CONFIG, "1");
        producerConfigMap.put(BATCH_SIZE_CONFIG, "16384");
        producerConfigMap.put(BUFFER_MEMORY_CONFIG, "1024000");
        producerConfigMap.put(KEY_SERIALIZER_CLASS_CONFIG, "org.apache.kafka.common.serialization.StringSerializer");
        producerConfigMap.put(VALUE_SERIALIZER_CLASS_CONFIG, "org.apache.kafka.common.serialization.StringSerializer");
        //超时时间
        producerConfigMap.put(PRODUCER_TIMEOUT_MS, "10000");
        QZ_PRODUCER_CONFIG_MAP.putAll(producerConfigMap);
    }

}
