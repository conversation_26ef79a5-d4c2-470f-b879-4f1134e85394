package com.quanzhi.auditapiv2.core.model.bigScreen;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * create at 2021/3/26 1:49 下午
 * @description: 关系拓扑节点数据
 **/
@Data
@NoArgsConstructor
@AllArgsConstructor
public class NodeData {

    /**
     * 节点名
     */
    private String name;
    /**
     * 节点层级从1开始
     */
    private int category;
    /**
     * 是否可拖拽
     */
    private boolean draggable;

}