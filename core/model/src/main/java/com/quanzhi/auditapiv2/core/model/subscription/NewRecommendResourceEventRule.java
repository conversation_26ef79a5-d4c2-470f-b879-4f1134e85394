package com.quanzhi.auditapiv2.core.model.subscription;

import com.quanzhi.audit_core.common.risk.RiskInfo;
import com.quanzhi.auditapiv2.common.dal.entity.aggRisk.AggRiskInfo;
import com.quanzhi.auditapiv2.common.util.utils.DataUtil;
import com.quanzhi.metabase.core.model.event.ResourceChangedEvent;

import java.util.Map;

/**
 * <AUTHOR>
 * @date 2020/8/14 下午3:25
 */
public class NewRecommendResourceEventRule implements SubscribeRule {

    private String playLoadKey = "recommend";

    @Override
    public boolean match (ResourceChangedEvent resourceChangedEvent, String operator, String content) {

        if(DataUtil.isNotEmpty( resourceChangedEvent.getEventType() ) && DataUtil.isNotEmpty( resourceChangedEvent.getPayload() ) ) {

            boolean recommend = ((Map)resourceChangedEvent.getPayload()).containsKey(playLoadKey) ?
                    (Boolean) ((Map)resourceChangedEvent.getPayload()).get(playLoadKey)   : false;

            return recommend;
        } else {
            return false;
        }
    }

    @Override
    public boolean match(RiskInfo riskInfo, String operator, String content) {
        return false;
    }

    @Override
    public boolean match(AggRiskInfo aggRiskInfo, String operator, String content) {
        return false;
    }
}
