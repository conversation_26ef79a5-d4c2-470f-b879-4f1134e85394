package com.quanzhi.auditapiv2.core.model.constant;

/**
 * <AUTHOR>
 * @date 2020/8/20 下午4:09
 */
public class NacosConfigConstant {


    public static final class GROUP_ID {

        public static final String AUDIT_APIV2 = "auditapiv2";

        public static final String COMMON = "common";

        public static final String DISCOVER = "discover";
    }


    public static final class DATA_ID {

        /**
         * 订阅规则dataid
         */
        public static final String SUBSCRIBE_POLICY_RULE = "auditapiv2.subscribePolicyRule.json";

        public static final String DATA_LABEL = "common.datalabel.json";

        public static final String FEATURE_LABEL = "common.featurelabel.json";

        public static final String APP_FEATURE_LABEL = "common.appFeaturelabel.json";

        public static final String HTTP_CLASSIFICATION = "discover.http.classification.json";

        public static final String RISK_LEVEL = "common.risk.level.config.json";

        public static final String WEAKNESS_RULE = "discover.weakness.rules.json";

        public static final String DATA_LABEL_PARENT = "common.dataLabelClassify.json";
    }


}
