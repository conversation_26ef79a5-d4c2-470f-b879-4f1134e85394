package com.quanzhi.auditapiv2.core.model.subscription;

import com.quanzhi.audit_core.common.risk.RiskInfo;
import com.quanzhi.auditapiv2.common.dal.entity.aggRisk.AggRiskInfo;
import com.quanzhi.metabase.core.model.event.ResourceChangedEvent;
import lombok.extern.slf4j.Slf4j;

import java.util.Arrays;
import java.util.List;

/**
 * @ClassName NewRiskEventRule
 * @Description TODO
 * <AUTHOR>
 * @Date 2020/12/23 16:15
 **/
@Slf4j
public class NewRiskEventRule implements SubscribeRule {
    @Override
    public boolean match(ResourceChangedEvent resourceChangedEvent, String operator, String content) {
        return false;
    }

    @Override
    public boolean match(RiskInfo riskInfo, String operator, String content) {

        SubscribeRuleOperateEnum operateEnum = SubscribeRuleOperateEnum.valueOf(operator);

        boolean match = false;

        switch (operateEnum) {

            case CONTAIN:

                List<String> thresholdLabels = Arrays.asList(content.split(ARRAY_SEPARATOR));

                if (thresholdLabels == null || thresholdLabels.size() <= 0) {
                    match = false;
                    break;
                }

                match = thresholdLabels.contains(riskInfo.getPolicySnapshot().getId());
                log.info("包含的风险id集合为：" + thresholdLabels.toString());
                log.info("获取到的风险id为：" + riskInfo.getPolicySnapshot().getId());
                break;

            case EQUAL:

                match = riskInfo.getPolicySnapshot().getId().equals(content);
                break;

            default:
                break;
        }

        return match;
    }

    @Override
    public boolean match(AggRiskInfo aggRiskInfo, String operator, String content) {
        SubscribeRuleOperateEnum operateEnum = SubscribeRuleOperateEnum.valueOf(operator);

        boolean match = false;

        switch (operateEnum) {

            case CONTAIN:

                List<String> thresholdLabels = Arrays.asList(content.split(ARRAY_SEPARATOR));

                if (thresholdLabels == null || thresholdLabels.size() <= 0) {
                    match = false;
                    break;
                }

                match = thresholdLabels.contains(aggRiskInfo.getPolicySnapshot().getId());
                log.info("包含的风险id集合为：" + thresholdLabels.toString());
                log.info("获取到的风险id为：" + aggRiskInfo.getPolicySnapshot().getId());
                break;

            case EQUAL:

                match = aggRiskInfo.getPolicySnapshot().getId().equals(content);
                break;

            default:
                break;
        }

        return match;
    }

}
