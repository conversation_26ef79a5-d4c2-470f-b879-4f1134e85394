package com.quanzhi.auditapiv2.core.model.event;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @Author: Linlm
 * @Description:
 * @Date: Created in 2022/3/14 4:20 PM
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class LoginEvent {
    // 登录状态码
    private int loginCode;
    private String msg;

    // 登录用户名
    private String userName;

    // 登录用户角色
    private String roleName;

    // 登录用户权限描述
    private String remark;
}
