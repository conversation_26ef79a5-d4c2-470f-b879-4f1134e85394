package com.quanzhi.auditapiv2.core.model.subscription.customplugin;

import lombok.Data;

/**
 * <AUTHOR>
 * create at 2022/3/30 3:12 下午
 * @description: 数据推送插件tag
 **/
@Data
public class DataPushPluginTag {

    /**
     * 转换加推送
     */
    private boolean convertAndPush;

    /**
     * 只转换数据
     */
    private boolean onlyConvert;

    /**
     * 只推送数据
     */
    private boolean onlyPush;

    /**
     * 转换后数据
     */
    private Object convertData;


}