package com.quanzhi.auditapiv2.core.model.subscribeOutput;

import com.quanzhi.metabase.core.model.http.HttpApiSample;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/8/20 下午2:00
 */
@Data
public class SubscribeMsg2OtherModel {

    /**
     * 事件时间
     */
    private long timestamp;

    /**
     * 接口
     */
    private String uri;

    /**
     * 订阅名称
     */
    private String subscribeName;

    /**
     * 接口类型详情
     */
    private List<String> classificationDetails;

    /**
     * 订阅特征name集合
     */
    private List<String> subscribeFeatureDetail;

    /**
     * 接口详情
     */
    private HttpApiDetail apiDetails;

    /**
     * 事件详情
     */
    private HttpApiSampleDetail httpApiSampleDetail;

    @Data
    @NoArgsConstructor
    public static class HttpApiDetail {

        /**
         * 部署域name列表
         */
        private List<String> deployDomains;
        /**
         * 访问域name
         */
        private List<String> visitDomains;

        private List<String> featureLabels;

        private List<String> reqContentTypes;

        private List<String> rspContentTypes;

        private List<String> reqDataLabels;

        private List<String> rspDataLabels;
    }

    @Data
    public static class HttpApiSampleDetail {

        /**
         * 请求
         */
        private HttpApiSample.HttpRequest req;


        /**
         * 网络信息
         */
        private HttpApiSample.Net net;

        /**
         * 响应
         */
        private HttpApiSample.HttpResponse rsp;

        private List<String> reqDataLabels;

        private List<String> rspDataLabels;
    }

}
