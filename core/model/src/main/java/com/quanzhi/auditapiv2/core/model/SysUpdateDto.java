package com.quanzhi.auditapiv2.core.model;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @Author: Linlm
 * @Description:
 * @Date: Created in 2020/7/21 下午2:49
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class SysUpdateDto {
    // 升级包唯一id，可用升级包文件 MD5 值
    @ApiModelProperty(value = "升级包唯一id", name = "id")
    private String id;

    // 升级包包名
    @ApiModelProperty(value = "升级包名", name = "name")
    private String name;

    // 升级包解压目录
    @ApiModelProperty(value = "升级包解压目录", name = "pkg")
    private String pkg;

    // 升级包版本
    @ApiModelProperty(value = "升级包版本", name = "version")
    private String version;
}
