package com.quanzhi.auditapiv2.core.model;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @Author: Linlm
 * @Description:
 * @Date: Created in 2020/7/22 上午11:57
 */
@Data
public class UpdateStatusDto {
    /**
     * 状态
     * com.quanzhi.auditapiv2.core.service.manager.web.impl.ProcessStatus
     */
    @ApiModelProperty(value = "升级状态", name = "status")
    private int status;

    @ApiModelProperty(value = "升级状态描述", name = "desc")
    private String desc;

    /**
     * 日志类型：upgrade - 升级，rollback - 回滚
     */
    @ApiModelProperty(value = "日志类型：upgrade - 升级，rollback - 回滚", name = "logType")
    String logType;

    /**
     * 日志内容
     */
    @ApiModelProperty(value = "日志内容", name = "log")
    String log;

    public static class LogType{
        public static String upgrade = "upgrade";
        public static String rollback = "rollback";
    }
}
