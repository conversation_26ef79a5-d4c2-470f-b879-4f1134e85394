package com.quanzhi.auditapiv2.core.model.subscription;

import com.quanzhi.audit_core.common.model.HttpEvent;
import com.quanzhi.audit_core.common.risk.RiskInfo;
import com.quanzhi.auditapiv2.common.dal.dto.HttpAppDto;
import com.quanzhi.auditapiv2.common.dal.entity.EventNotifyEntity;
import com.quanzhi.auditapiv2.common.dal.entity.SubscribePolicyRule;
import com.quanzhi.auditapiv2.common.dal.entity.aggRisk.AggRiskInfo;
import com.quanzhi.metabase.core.model.event.ResourceChangedEvent;
import com.quanzhi.metabase.core.model.http.HttpApiResource;

/**
 * <AUTHOR>
 * @date 2020/8/17 下午4:27
 */
public interface IResourceChangedEventInterceptor {

    boolean matchPolicy(ResourceChangedEvent resourceChangedEvent, HttpApiResource httpApiResource, SubscribePolicyRule subscribePolicyRule);

    boolean matchPolicy(RiskInfo riskInfo, HttpApiResource httpApiResource, HttpAppDto httpApp, SubscribePolicyRule subscribePolicyRule);

    boolean matchPolicy(AggRiskInfo aggRiskInfo, HttpApiResource httpApiResource, HttpAppDto httpApp, SubscribePolicyRule subscribePolicyRule);

    boolean matchPolicy(HttpEvent httpEvent, HttpApiResource httpApiResource, SubscribePolicyRule subscribePolicyRule);

    boolean matchPolicy(EventNotifyEntity eventNotifyEntity, SubscribePolicyRule subscribePolicyRule);

}
