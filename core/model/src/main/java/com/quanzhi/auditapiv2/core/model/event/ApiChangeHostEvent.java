package com.quanzhi.auditapiv2.core.model.event;

import com.quanzhi.auditapiv2.common.dal.dto.HttpApiDto;
import lombok.AllArgsConstructor;
import lombok.Data;

/**
 * <AUTHOR>
 * create at 2022/9/7 2:39 下午
 * @description:
 **/
@Data
@AllArgsConstructor
public class ApiChangeHostEvent {

    private String apiUrl;

    private String oldHost;

    private String newHost;

    private HttpApiDto httpApiDto;
}