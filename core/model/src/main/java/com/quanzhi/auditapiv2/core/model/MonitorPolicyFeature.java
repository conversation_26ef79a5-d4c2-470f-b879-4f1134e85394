package com.quanzhi.auditapiv2.core.model;

import com.quanzhi.operate.atomDefinition.ActionConfigFrontTransform;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/2/15 10:42 上午
 */
@Data
public class MonitorPolicyFeature {

    private String id;

    private String name;

    private String dataType;

    /**
     * 数据提供
     * user_input 用户输入
     * sys_support 系统提供
     */
    private String fillType;

    private Object value;

    /**
     * 有一些指标有前置的指标描述
     */
    private Object featureConfig;

    private String dataSource;

    private NacosConfig nacosConfig;

    private List<Operator> operatorList;

    @Data
    public static class Operator {

        private String label;

        private String name;
    }

    @Data
    public static class  NacosConfig {

        private String dataId;

        private String groupId;

        private String udf;
    }

    private ActionConfigFrontTransform actionConfigFrontTransform;
}
