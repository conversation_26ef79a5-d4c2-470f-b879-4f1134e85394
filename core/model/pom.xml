<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
    <parent>
        <groupId>com.quanzhi.auditapiv2</groupId>
        <artifactId>auditapiv2</artifactId>
        <version>3.3.0-SNAPSHOT</version>
        <relativePath>../../pom.xml</relativePath>
    </parent>
    <modelVersion>4.0.0</modelVersion>


    <artifactId>auditapiv2-core-model</artifactId>

    <name>auditapiv2-core-model</name>


    <packaging>jar</packaging>

    <dependencies>
        <!-- sub projects -->
        <dependency>
            <groupId>com.quanzhi.auditapiv2</groupId>
            <artifactId>auditapiv2-common-service-integration</artifactId>
        </dependency>
        <dependency>
            <groupId>com.quanzhi.auditapiv2</groupId>
            <artifactId>auditapiv2-common-dal</artifactId>
        </dependency>
        <dependency>
            <groupId>com.quanzhi.auditapiv2</groupId>
            <artifactId>auditapiv2-common-util</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework.kafka</groupId>
            <artifactId>spring-kafka</artifactId>
        </dependency>

        <dependency>
            <groupId>com.quanzhi.auditapiv2</groupId>
            <artifactId>auditapiv2-core-risk</artifactId>
        </dependency>

        <!-- lib depends -->

    </dependencies>

</project>
