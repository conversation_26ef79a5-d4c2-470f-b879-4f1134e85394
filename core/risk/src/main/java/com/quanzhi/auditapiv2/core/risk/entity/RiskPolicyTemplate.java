package com.quanzhi.auditapiv2.core.risk.entity;

import com.alibaba.fastjson.JSONObject;
import lombok.Data;

import java.util.List;

@Data
public class RiskPolicyTemplate {

    private String cycle;

    private List<String> entities;

    private List<feature> features;

    private String topic;

    @Data
    public static class feature {

        private JSONObject baseline;

        private String dataType;

        private List<String> descriptions;

        private boolean enable;

        private String name;

        private List<String> operators;

        private String path;

        private String type;

        private String udf;

        private String var;

    }

}
