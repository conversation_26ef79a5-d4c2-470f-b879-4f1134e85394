package com.quanzhi.auditapiv2.core.risk.entity;

import lombok.Data;

import java.util.List;

/**
 * @Author: HaoJun
 * @Date: 2021/8/9 9:47 上午
 */
@Data
public class DefinedEvent extends BaseEntity {
    /**
     * 事件ID
     */
    private String id;
    /**
     * 事件时间
     */
    private Long timestamp;
    /**
     * 日期，如********
     */
    private String date;
    /**
     * 去掉参数的接口URL
     */
    private String apiUrl;
    /**
     * ip
     */
    private String ip;
    /**
     * 账号
     */
    private String account;
    /**
     * 账号类型
     */
    private String accountType;
    /**
     * 密码
     */
    private String password;
    /**
     * 请求方法
     */
    private String method;
    /**
     * 请求类型
     */
    private String reqContentType;
    /**
     * User-Agent
     */
    private String ua;
    /**
     * User-Agent类型
     */
    private String uaType;
    /**
     * REFERER
     */
    private String referer;
    /**
     * COOKIE 数据
     */
    private String cookie;
    /**
     * 返回状态码
     */
    private String rspStatus;
    /**
     * 返回格式
     */
    private String rspContentType;
    /**
     * 返回长度
     */
    private Integer rspContentLength;
    /**
     * 返回数据标签
     */
    private List<String> rspDataLabelIds;
    /**
     * 请求数据标签
     */
    private List<String> reqDataLabelIds;
    /**
     * 数据标签
     */
    private List<String> dataLabelIds;
    /**
     * 去重标签数量
     */
    private Long rspLabelContentDistinctCount;

    /**
     * 标签内容
     */
    private List<LabelValue> labelValues;

    /**
     * 登陆结果
     */
    private String loginResult;
    /**
     * 是否攻击
     */
    private Boolean attack;
    /**
     * 攻击方式
     */
    private String attackType;
    /**
     * 攻击结果
     */
    private Integer attackSuccess;
    /**
     * 事件定义
     */
    private List<String> eventDefineIds;
    /**
     * 部署域
     */
    private List<String> deployDomains;
    /**
     * 访问域
     */
    private List<String> accessDomains;
    /**
     * 应用域
     */
    private String host;
    /**
     * 风险类型
     */
    private List<String> riskTypes;
    /**
     * 接口uri
     */
    private String apiUri;
    /**
     * 应用uri
     */
    private String appUri;
    /**
     * 省
     */
    private String province;
    /**
     * 市
     */
    private String city;
    /**
     * 国家
     */
    private String country;
    /**
     * 地域
     */
    private String location;
    /**
     * 提取到的数据
     */
    private List<String> enumerateParaSigns;
    /**
     * 分钟级别
     */
    private Long minute;

    private List<String> statMarkingTags;
    /**
     * 保留标记
     */
    private Boolean keepFlag = false;

    /**
     * get请求参数
     */
    private String getParam;
    /**
     * post请求参数
     */
    private String postParam;
    /**
     * 攻击关键字
     */
    private List<String> attackKeywords;

    @Data
    public static class LabelValue {

        /**
         * 标签
         */
        private String label;

        /**
         * 内容
         */
        private List<String> values;
    }

    public enum PolicyEnum {

        /**
         * API单次返回大量敏感数据
         */
        POLICY_1("1", "API单次返回大量敏感数据"),

        /**
         * 登录API存在弱密码登录账号
         */
        POLICY_2("2", "登录API存在弱密码登录账号"),

        /**
         * IP执行参数遍历
         */
        POLICY_3("3", "IP执行参数遍历"),

        /**
         * IP执行翻页遍历
         */
        POLICY_4("4", "IP执行翻页遍历"),

        /**
         * IP使用异常请求查询敏感数据
         */
        POLICY_5("5", "IP使用异常请求查询敏感数据"),

        /**
         * IP访问次数异常
         */
        POLICY_6("6", "IP访问次数异常"),

        /**
         * IP高频撞库
         */
        POLICY_7("7", "IP高频撞库"),

        /**
         * 账号暴力破解
         */
        POLICY_8("8", "账号暴力破解"),

        /**
         * SSRF攻击
         */
        POLICY_9("9", "SSRF攻击"),

        /**
         * SQL注入
         */
        POLICY_10("10", "SQL注入"),

        /**
         * 路径试探
         */
        POLICY_11("11", "路径试探"),

        /**
         * 境外IP拉取大量数据
         */
        POLICY_12("12", "境外IP拉取大量数据"),

        /**
         * WEB攻击
         */
        POLICY_13("13", "WEB攻击"),

        /**
         * 验证码爆破
         */
        POLICY_14("14", "验证码爆破"),

        /**
         * 异常参数获取敏感数据
         */
        POLICY_15("15", "异常参数获取敏感数据"),

        /**
         * session多终端访问
         */
        POLICY_16("16", "session多终端访问"),

        /**
         * session异地访问
         */
        POLICY_17("17", "session异地访问"),

        /**
         * IP使用多账号
         */
        POLICY_18("18", "IP使用多账号"),

        /**
         * 账号异地登录
         */
        POLICY_19("19", "账号异地登录"),

        /**
         * 短信炸弹
         */
        POLICY_20("20", "短信炸弹"),

        /**
         * 渗透测试尝试
         */
        POLICY_21("21", "渗透测试尝试");

        String policyId;

        String name;

        PolicyEnum(String policyId, String name) {

            this.policyId = policyId;
            this.name = name;
        }

        public String getPolicyId() {
            return policyId;
        }

        public String getName() {
            return name;
        }

        public static PolicyEnum getPolicyEnum(Integer policyId) {

            for (PolicyEnum policyEnum : PolicyEnum.values()) {
                if (policyEnum.getPolicyId().equals(policyId)) {
                    return policyEnum;
                }
            }
            return null;
        }
    }
}
