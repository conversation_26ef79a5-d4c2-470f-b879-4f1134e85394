package com.quanzhi.auditapiv2.core.risk.service.bridge;

import com.quanzhi.auditapiv2.core.risk.dto.RiskInfoDto;
import com.quanzhi.auditapiv2.core.risk.entity.RiskInfo;

import java.util.Map;

/**
 * 解决biz-risk 和 core-service 循环引用的中间接口层
 */
public interface IRiskBridgeService {


    RiskInfoDto getRiskInfoDto(RiskInfo riskInfo, Map<String, String> nodeMap);

    RiskInfoDto getRiskInfoDtoById(String id);

    Object getRiskOneSampleByRiskId(String id);

    Object getRiskOneSampleBySampleId(String sampleId);

    String getRiskDesc(String id) throws Exception;

}
