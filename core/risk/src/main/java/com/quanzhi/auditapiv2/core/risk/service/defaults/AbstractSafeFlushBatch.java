package com.quanzhi.auditapiv2.core.risk.service.defaults;

import com.quanzhi.auditapiv2.core.risk.service.AutoFlushBatch;

import java.util.List;
import java.util.concurrent.locks.Lock;
import java.util.concurrent.locks.ReentrantLock;

/**
 * @Author: HaoJun
 * @Date: 2021/7/1 6:47 下午
 */
public abstract class AbstractSafeFlushBatch<T> implements AutoFlushBatch<T> {

    private Lock flushLock = new ReentrantLock();

    @Override
    public final void flush() {
        if (!flushLock.tryLock()) {
            return;
        }
        List<T> items;
        try {
            items = poll();
        } finally {
            flushLock.unlock();
        }
        if (items == null || items.size() <= 0) {
            return;
        }
        onFlush(items);
    }

    protected abstract void onFlush(List<T> items);

    protected abstract List<T> poll();
}
