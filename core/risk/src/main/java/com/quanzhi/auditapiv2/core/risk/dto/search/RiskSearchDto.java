package com.quanzhi.auditapiv2.core.risk.dto.search;

import com.quanzhi.auditapiv2.common.dal.dto.app.CustomPropertyDto;
import com.quanzhi.auditapiv2.core.risk.dto.common.CommonSearchDto;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * <AUTHOR>
 * create at 2021/8/10 2:53 下午
 * @description: 异常查询条件
 **/
@Data
@ApiModel
public class RiskSearchDto extends CommonSearchDto {

    /**
     * /**
     * IP
     */
    @ApiModelProperty(value = "IP", name = "ip")
    private String ip;

    @ApiModelProperty(value = "异常运营ID", name = "operationId")
    private String operationId;

    /**
     * apiUrl
     */
    @ApiModelProperty(value = "apiUrl", name = "apiUrl")
    private String apiUrl;

    /**
     * host
     */
    @ApiModelProperty(value = "host", name = "host")
    private String host;

    @ApiModelProperty(value = "应用名称", name = "appName")
    private String appName;

    @ApiModelProperty(value = "是否忽略大小写", name = "hostIgnoreCase")
    private Boolean hostIgnoreCase;

    /**
     * 事件id
     */
    @ApiModelProperty(value = "事件id", name = "id")
    private String id;

    /**
     * 事件id
     */
    @ApiModelProperty(value = "策略id", name = "policyId")
    private String policyId;

    /**
     * 异常类别
     */
    @ApiModelProperty(value = "异常类别", name = "riskType")
    private List<String> riskType;

    /**
     * 异常名称
     */
    @ApiModelProperty(value = "异常名称列表", name = "riskName")
    private List<String> riskName;

    /**
     * 异常等级
     */
    @ApiModelProperty(value = "异常等级(1-低异常，2-中异常，3-高异常)", name = "level")
    private List<Integer> level;

    /**
     * 异常状态
     */
    @ApiModelProperty(value = "异常状态(0-待确认，1-已忽略，2-已确认)", name = "state")
    private List<Integer> state;

    /**
     * 是否按状态分组
     */
    private boolean stateGroup;

    private String nodeId;

    /**
     * 异常标识
     */
    @ApiModelProperty(value = "异常标识(true-已标识, false-未标识)", name = "riskMark")
    private Boolean riskMark;

    /**
     * 异常主体
     */
    @ApiModelProperty(value = "异常主体-用于异常清单)", name = "riskSubject")
    private String riskSubject;

    /**
     * 攻击结果
     */
    @ApiModelProperty(value = "攻击结果", name = "attackSuccess")
    private Integer attackSuccess;

    /**
     * 返回数据标签
     */
    @ApiModelProperty(value = "返回数据标签", name = "rspLabelList")
    private List<String> rspLabelList;

    /**
     * @see com.quanzhi.audit_core.common.risk.Policy.EntityEnum
     */
    @ApiModelProperty(value = "主体类型(API|IP|ACCOUNT|APP))", name = "entitiesType")
    private String entitiesType;

    @ApiModelProperty(value = "主体内容)", name = "entitiesValue")
    private String entitiesValue;

    @ApiModelProperty(value = "api或app)", name = "apiOrApp")
    private String apiOrApp;

    /**
     * @see com.quanzhi.audit_core.common.risk.Policy.EntityEnum
     */
    @ApiModelProperty(value = "关联类型(API|IP|ACCOUNT|APP))", name = "relationType")
    private String relationType;

    @ApiModelProperty(value = "关联内容)", name = "relationValue")
    private String relationValue;

    /**
     * 异常首次发现时间-start
     */
    @ApiModelProperty(value = "异常首次发现时间-start", name = "firstTimeStart")
    private Long firstTimeStart;

    /**
     * 异常首次发现时间-end
     */
    @ApiModelProperty(value = "异常首次发现时间-end", name = "firstTimeEnd")
    private Long firstTimeEnd;

    /**
     * 异常更新时间-start
     */
    @ApiModelProperty(value = "异常更新时间-start", name = "updateTimeStart")
    private Long updateTimeStart;

    /**
     * 异常更新时间-end
     */
    @ApiModelProperty(value = "异常更新时间-end", name = "updateTimeEnd")
    private Long updateTimeEnd;

    @ApiModelProperty(value = "异常日期-start", name = "dateStart")
    private String dateStart;

    @ApiModelProperty(value = "异常日期-end", name = "dateEnd")
    private String dateEnd;

    @ApiModelProperty(value = "建单状态", name = "orderFlag")
    private Integer orderFlag;

    /**
     * 是否旧异常
     */
    private Boolean isOldRisk;

    /**
     * appUri集合
     */
    private Set<String> appUriSet;

    /**
     * apiUri集合
     */
    private Set<String> apiUris;

    /**
     * 部门集合
     */
    private Set<String> departmentSet;

    /**
     * 是否执行资产权限控制
     */
    private Boolean isAssetAuthorization;

    private List<CustomPropertyDto> customProperties;

    /**
     * 返回数据标签查询操作符
     *
     * @see com.quanzhi.auditapiv2.common.dal.enums.DBOperatorEnum
     */
    private String rspDataLabelsOperator;

    private Map<String, String> fieldOperateMap;

    private Map<String,Object> dataPermissionMap;

    /**
     * 分组字段枚举
     */
    public enum GroupFieldEnum {

        RISK_NAME("policySnapshot.name"),

        RISK_TYPE("policySnapshot.group"),

        RISK_MARK("riskMark"),

        RISK_STATE("state"),

        RISK_LEVEL("level"),

        ATTACK_SUCCESS("attackSuccess"),

        ORDER_FLAG("orderFlag");

        String name;

        GroupFieldEnum(String name) {

            this.name = name;
        }

        public String getName() {
            return name;
        }

    }


}