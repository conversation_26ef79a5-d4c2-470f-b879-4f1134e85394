package com.quanzhi.auditapiv2.core.risk.repository;

import com.quanzhi.auditapiv2.common.dal.dao.base.IBaseDao;
import com.quanzhi.auditapiv2.common.dal.dto.common.CommonGroupDto;
import com.quanzhi.auditapiv2.core.risk.dto.search.ThreatInfoSearchDto;
import com.quanzhi.auditapiv2.core.risk.entity.ThreatInfo;

import java.util.List;

/**
 * @author: yangzixian
 * @date: 1/3/2023 17:39
 * @description:
 */
public interface IThreatInfoDao extends IBaseDao<ThreatInfo> {

    /**
     * 获取威胁信息清单
     *
     * @param threatInfoSearchDto
     * @return
     */
    List<ThreatInfo> listThreatInfos(ThreatInfoSearchDto threatInfoSearchDto);

    List<ThreatInfo> listThreatInfos(String entity);

    List<ThreatInfo> listThreatInfos(List<String> ids, ThreatInfoSearchDto threatInfoSearchDto);

    List<ThreatInfo> listThreatInfos();

    List<ThreatInfo> getThreatIpTop10();

    List<ThreatInfo> listThreatIpInfos();

    /**
     * 统计总数
     *
     * @param threatInfoSearchDto
     * @return
     */
    Long countThreatInfos(ThreatInfoSearchDto threatInfoSearchDto);

    /**
     * 分组计数威胁信息
     *
     * @param threatInfoSearchDto
     * @return
     */
    List<CommonGroupDto> groupThreatInfo(ThreatInfoSearchDto threatInfoSearchDto);

    void upsertThreatInfo(String threatEntity, ThreatInfo threatInfo);

}
