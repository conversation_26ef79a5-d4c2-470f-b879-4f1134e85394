package com.quanzhi.auditapiv2.core.risk.dto.common;

import com.quanzhi.auditapiv2.core.risk.dto.search.DefinedEventSearchDto;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * create at 2021/8/23 5:29 下午
 * @description:
 **/
@Data
@ApiModel
public class CommonSearchDto {

    /**
     * 排序字段
     */
    @ApiModelProperty(value = "排序字段", name = "sortField")
    private String sortField;

    /**
     * 排序方式
     */
    @ApiModelProperty(value = "排序方式（1-正序，2-倒序）", name = "sort")
    private Integer sort;

    /**
     * 当前页
     */
    @ApiModelProperty(value = "当前页", name = "page")
    private Integer page = 1;

    /** 
     * 每页显示条数
     */
    @ApiModelProperty(value = "每页显示条数", name = "limit")
    private Integer limit = 10;

    /**
     * 分组字段
     * @see DefinedEventSearchDto.GroupFieldEnum
     */
    @ApiModelProperty(value = "分组字段", name = "groupField")
    private String groupField;
}