package com.quanzhi.auditapiv2.core.risk.dto;

import com.quanzhi.audit_core.common.risk.PolicySnapshot;
import com.quanzhi.auditapiv2.core.risk.entity.RiskInfo;
import com.quanzhi.metabase.core.model.ResourceEntity;
import com.quanzhi.metabase.core.model.http.HttpAppResource;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * @Author: yangzx
 * @Date: 2024/10/9 11:25
 */
@Data
public class RiskV2Dto {

    @ApiModelProperty("异常数据库ID")
    private String id;

    @ApiModelProperty("异常操作ID")
    private String operationId;

    @ApiModelProperty("异常名称")
    private String name;

    @ApiModelProperty("异常描述")
    private String desc;

    @ApiModelProperty("等级")
    private Integer level;

    @ApiModelProperty("异常等级名称")
    private String levelName;

    @ApiModelProperty("状态")
    private Integer state;

    @ApiModelProperty("异常状态名称")
    private String stateName;

    @ApiModelProperty("异常类型")
    private String type;

    @ApiModelProperty("异常主体")
    private List<RiskInfo.Entity> entities;

    @ApiModelProperty("异常主体类型")
    private String entityType;

    @ApiModelProperty("host")
    private String host;

    @ApiModelProperty("url")
    private String apiUrl;

    @ApiModelProperty("账号")
    private String account;

    @ApiModelProperty("IP")
    private String ip;

    @ApiModelProperty("应用部署域")
    private Set<String> deployDomains;

    @ApiModelProperty("appUri")
    private String appUri;

    @ApiModelProperty("备注")
    private String remark;

    @ApiModelProperty("整改建议")
    private String suggest;

    @ApiModelProperty("应用名称")
    private String appName;

    @ApiModelProperty("加白结果")
    private Map<String, String> whiteMap;

    @ApiModelProperty(value = "异常快照")
    private com.quanzhi.audit_core.common.risk.RiskInfo.PolicySnapshotV2 policySnapshotV2;

    @ApiModelProperty(value = "330之前异常快照")
    private PolicySnapshot policySnapshot;

    @ApiModelProperty("首次发现时间")
    private Long firstTime;

    @ApiModelProperty("最近活跃时间")
    private Long lastTime;

    @ApiModelProperty("置顶状态")
    private Boolean riskMark;

    @ApiModelProperty("部门")
    private List<HttpAppResource.Department> departments;

    @ApiModelProperty("操作人")
    private String operateName;

    @ApiModelProperty("操作时间")
    private Long operateTime;

    @ApiModelProperty("节点信息")
    private List<ResourceEntity.Node> nodes = new ArrayList<>();

    @Mapper
    public interface RiskV2DtoMapper {

        RiskV2Dto.RiskV2DtoMapper INSTANCE = Mappers.getMapper(RiskV2Dto.RiskV2DtoMapper.class);

        RiskV2Dto convert(RiskInfo riskInfo);

    }

}
