package com.quanzhi.auditapiv2.core.risk.entity;

import com.quanzhi.re.core.domain.entity.po.MatchRule;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * @Author: yangzx
 * @Date: 2024/10/31 10:15
 */
@Data
public class RiskPolicyV2 {

    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    private String id;

    private String desc;

    /**
     * 是否为默认风险策略
     */
    private Boolean isDefaultRisk;

    private String type;

    /**
     * 名称
     */
    private String name;

    /**
     * 分类
     */
    private String group;

    /**
     * 事件规则
     *
     * @desc 过滤出参与风险检测的事件
     */
    private MatchRule eventRule;

    /**
     * 指标匹配规则
     */
    private MatchRule quotaRule;

    /**
     * 风险等级
     * default
     */
    private Integer level;

    /**
     * 威胁标签
     */
    private String threatLabel;

    /**
     * 启停开关
     */
    private Boolean enable;

    /**
     * 支持的白名单类型
     */
    private List<String> allowListTypes;

    /**
     * 删除标记
     */
    private Boolean delFlag;

    /**
     * 采样配置
     */
    private SampleConfig sampleConfig;

    /**
     * 周期
     * 新的表达方式，和决策引擎保持一致
     *
     * @since 3.3
     */
    private Period period;

    private Long updateTime;

    private Long createTime;

    /**
     * 主体的有序组合
     * 生成唯一 ID 时，必须按照顺序保证唯一性
     *
     * @desc 主体的配置记在模板中，这里便只需要记录主体类型的组合，而无需主体的取值方式
     * @since 3.3
     */
    private List<Entity> entities;

    /**
     * @see com.quanzhi.re.core.domain.entity.variable.Period
     */
    @Data
    public static class Period {

        private Integer time;

        private String unit;

    }

    @Data
    public static class EntityRelated {

        private List<String> related;

        private String baseline;

        private String type;

        private String name;

    }

    /**
     * 生成策略快照
     */
    public PolicySnapshotV2 snapshot() {
        PolicySnapshotV2 snapshot = new PolicySnapshotV2();
        snapshot.setId(this.id);
        snapshot.setName(this.name);
        snapshot.setGroup(this.group);
        snapshot.setQuotaRule(this.quotaRule);
        snapshot.setEventRule(this.eventRule);
        snapshot.setLevel(this.level);
        snapshot.setThreatLabel(this.threatLabel);
        snapshot.setType(this.type);
        snapshot.setPeriod(this.period);
        return snapshot;
    }

    @Data
    public static class SampleConfig {

        /**
         * counter name
         */
        private String name;

        /**
         * sample limit
         */
        private int limit;

        /**
         * count phase
         * 风险前、风险后、聚合
         */
        private String phase;

        /**
         * 计数器配置
         */
        private MatchRule properties;

        public enum PhaseEnum {
            /**
             * 风险前
             */
            BEFORE,
            /**
             * 风险后
             */
            AFTER,
            /**
             * 聚合
             */
            AGG
        }

    }

    @Data
    public class PolicySnapshotV2 implements Serializable {

        private static final long serialVersionUID = 1L;

        private String id;

        private String name;

        private String group;

        private MatchRule eventRule;

        private String type;

        /**
         * 指标匹配规则
         */
        private MatchRule quotaRule;

        /**
         * 风险等级
         */
        private Integer level;

        private String threatLabel;

        private Period period;

    }

}
