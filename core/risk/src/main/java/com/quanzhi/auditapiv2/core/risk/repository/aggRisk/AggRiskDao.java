package com.quanzhi.auditapiv2.core.risk.repository.aggRisk;

import com.quanzhi.auditapiv2.common.dal.dao.base.IBaseDao;
import com.quanzhi.auditapiv2.common.dal.dto.aggRisk.AggRiskOperatorDto;
import com.quanzhi.auditapiv2.common.dal.dto.aggRisk.BatchAggRiskOperatorDto;
import com.quanzhi.auditapiv2.common.dal.entity.IpCountEntity;
import com.quanzhi.auditapiv2.common.dal.entity.aggRisk.AggRiskInfo;
import com.quanzhi.auditapiv2.common.util.dto.AggregationDto;
import com.quanzhi.auditapiv2.common.util.dto.GroupDto;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;

import java.util.List;

/**
 * @Author: yangzx
 * @Date: 2024/10/9 14:37
 */
public interface AggRiskDao extends IBaseDao<AggRiskInfo> {

    List<AggRiskInfo> listAggRisk(AggRiskOperatorDto aggRiskOperatorDto) throws Exception;

    List<AggregationDto> groupAggRisk(AggRiskOperatorDto aggRiskOperatorDto, GroupDto groupDto) throws Exception;

    String markAggRisk(AggRiskOperatorDto aggRiskOperatorDto);

    String batchMarkAggRisk(BatchAggRiskOperatorDto batchAggRiskOperatorDto);

    Long totalCount(AggRiskOperatorDto aggRiskOperatorDto);

    Criteria getCriteria(AggRiskOperatorDto aggRiskOperatorDto);

    String dealAggRisk(AggRiskOperatorDto aggRiskOperatorDto);

    String batchDealAggRisk(BatchAggRiskOperatorDto batchAggRiskOperatorDto);

    String ignoreByEntity(String value, String remark, String policyId);

    List<AggRiskInfo> findByEntityValue(String value, String policyId);

    AggRiskInfo findById(String id);

    AggRiskInfo findByOperationId(String id);

    List<String> findRiskIpsByPolicyId(List<String> id);

    List<String> findRiskIpsByLevel(List<Integer> level);

    List<AggRiskInfo> findByPolicyIds(List<String> id);

    List<AggRiskInfo> findByLevel(List<Integer> level);

    List<AggRiskInfo> findByEntityAndApiOrApp(String entity, String uri, String type);

    List<AggRiskInfo> findByIds(List<String> ids);

    void upsertAggRiskInfo(AggRiskInfo aggRiskInfo);

    AggRiskInfo getByAggRiskKey(String aggRiskKey);

    List<AggRiskInfo> getAllAggRisk();

    List<AggRiskInfo> getByDataId(String dataId);

    List<Long> getStatistics();

    List<AggRiskInfo> listAggRiskInfoByDate(long startTime, long endTime, List<String> domains);

    List<IpCountEntity> getRiskDistributeByIp(List<Integer> state, List<String> appUris);

    List<IpCountEntity> getRiskCountryDistributeByDataReveal(List<String> riskNames, List<String> appUris);

    List<AggRiskInfo> selectIpRisk(Integer count, Integer limit);

    List<AggRiskInfo> selectAccountRisk(Integer count, Integer limit);

    List<AggRiskInfo> selectApiRisk(Integer count, Integer limit);

    List<AggRiskInfo> selectAppRisk(Integer count, Integer limit);

    <T> List<T> findDistinct(Query query, String filed, String collection, Class<T> resultClass);

    String ignoreByEntityValue(String policyId, String value, String remark, String operateName);

}
