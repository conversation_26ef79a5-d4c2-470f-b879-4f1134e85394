package com.quanzhi.auditapiv2.core.risk.entity.page;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 * @Author: HaoJun
 * @Date: 2020/12/31 2:04 下午
 */
@Data
public class Sort {

    private List<Order> orders;

    public Sort() {
        orders = new ArrayList<>();
    }

    public Sort(Order order) {
        orders = new ArrayList<>();
        orders.add(order);
    }

    public static final Sort asc(String property) {
        Order order = new Order();
        order.setDirection(Direction.ASC);
        order.setProperty(property);
        return new Sort(order);
    }

    public void addOrder(Order order) {
        orders.add(order);
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Order implements Serializable {
        private Direction direction;
        private String property;
    }

    public enum Direction {
        ASC, DESC;
    }
}
