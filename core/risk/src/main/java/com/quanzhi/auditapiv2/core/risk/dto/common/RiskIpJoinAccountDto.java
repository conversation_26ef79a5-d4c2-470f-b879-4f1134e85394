package com.quanzhi.auditapiv2.core.risk.dto.common;

import com.quanzhi.auditapiv2.core.risk.entity.RiskSample;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

import java.io.Serializable;

/**
 * 《风险IP关联账号DTO》
 *
 * <AUTHOR> [<EMAIL>]
 * @Project:
 * @Module ID:
 * @Comments:
 * @JDK version used:      <JDK1.8>
 * @since 2021-08-10-上午9:56:10
 */
@Data
@ApiModel
public class RiskIpJoinAccountDto implements Serializable {

    /**
     * 账号
     */
    @ApiModelProperty(value = "登录账号", name = "account")
    private String account;

    /**
     * 登录api
     */
    @ApiModelProperty(value = "登录api", name = "apiUrl")
    private String apiUrl;

    /**
     * 登录次数
     */
    @ApiModelProperty(value = "登录次数", name = "loginCount")
    private Long loginCount;

    /**
     * 登录成功次数
     */
    @ApiModelProperty(value = "登录成功次数", name = "loginSuccessCount")
    private Long loginSuccessCount;

    /**
     * 登录失败次数
     */
    @ApiModelProperty(value = "登录失败次数", name = "loginFailCount")
    private Long loginFailCount;

    /**
     * 登录未知次数
     */
    @ApiModelProperty(value = "登录未知次数", name = "loginUnknownCount")
    private Long loginUnknownCount;

    /**
     * 发现时间
     */
    @ApiModelProperty(value = "发现时间", name = "discoverTime")
    private Long discoverTime;

    /**
     * 最近活跃时间
     */
    @ApiModelProperty(value = "最近活跃时间", name = "activeTime")
    private Long activeTime;

    @Mapper
    public interface RiskIpJoinAccountDtoMapper {

        RiskIpJoinAccountDto.RiskIpJoinAccountDtoMapper INSTANCE = Mappers.getMapper(RiskIpJoinAccountDto.RiskIpJoinAccountDtoMapper.class);

        @Mapping(target = "account", source = "httpEvent.account")
        @Mapping(target = "apiUrl", source = "httpEvent.apiUri")
        @Mapping(target = "discoverTime", source = "createTime")
        @Mapping(target = "activeTime", source = "httpEvent.timestamp")
        RiskIpJoinAccountDto convert(RiskSample riskSample);
    }

}
