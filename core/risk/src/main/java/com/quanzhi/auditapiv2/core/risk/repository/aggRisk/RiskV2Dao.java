package com.quanzhi.auditapiv2.core.risk.repository.aggRisk;

import com.quanzhi.auditapiv2.common.dal.dto.aggRisk.BatchRiskV2OperatorDto;
import com.quanzhi.auditapiv2.common.dal.dto.aggRisk.RiskV2OperatorDto;
import com.quanzhi.auditapiv2.common.util.dto.AggregationDto;
import com.quanzhi.auditapiv2.common.util.dto.GroupDto;
import com.quanzhi.auditapiv2.core.risk.entity.RiskInfo;
import org.springframework.data.mongodb.core.query.Criteria;

import java.util.List;

/**
 * @Author: yangzx
 * @Date: 2024/10/9 14:37
 */
public interface RiskV2Dao {

    List<RiskInfo> listRiskV2(RiskV2OperatorDto riskV2OperatorDto) throws Exception;

    List<AggregationDto> groupRiskV2(RiskV2OperatorDto riskV2OperatorDto, GroupDto groupDto) throws Exception;

    String markRiskV2(RiskV2OperatorDto riskV2OperatorDto);

    String batchMarkRiskV2(BatchRiskV2OperatorDto batchRiskV2OperatorDto);

    Long totalCount(RiskV2OperatorDto riskV2OperatorDto);

    Long totalCount(String aggRiskId);

    Criteria getCriteria(RiskV2OperatorDto riskV2OperatorDto);

    String dealRiskV2(RiskV2OperatorDto riskV2OperatorDto);

    String batchDealRiskV2(BatchRiskV2OperatorDto batchRiskV2OperatorDto);

    String ignoreByEntityValue(String value, String remark, String operateName);

    List<RiskInfo> findByEntityValues(List<String> values);

    RiskInfo findById(String id);

    List<RiskInfo> findByIds(List<String> ids);

    List<RiskInfo> selectIpRisk(Integer count, Integer limit);

    List<RiskInfo> selectAccountRisk(Integer count, Integer limit);

    List<RiskInfo> selectApiRisk(Integer count, Integer limit);

    List<RiskInfo> selectAppRisk(Integer count, Integer limit);

    void updateAggRiskId(String id, String aggRiskId);

}
