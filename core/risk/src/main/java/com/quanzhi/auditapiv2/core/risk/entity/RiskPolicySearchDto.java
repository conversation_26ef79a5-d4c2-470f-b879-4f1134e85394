package com.quanzhi.auditapiv2.core.risk.entity;

import com.quanzhi.auditapiv2.core.risk.dto.common.CommonSearchDto;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * @author: yangzixian
 * @date: 9/3/2023 14:17
 * @description: 风险策略筛选
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class RiskPolicySearchDto extends CommonSearchDto {

    @ApiModelProperty(value = "id", name = "id")
    private String id;

    @ApiModelProperty(value = "文件名称", name = "fileName")
    private String fileName;

    @ApiModelProperty(value = "风险名称", name = "riskName")
    private String riskName;

    @ApiModelProperty(value = "风险类型", name = "riskGroup")
    private List<String> riskGroup;

    @ApiModelProperty(value = "风险属性（是否内置（true/false））", name = "isDefault")
    private Boolean isDefaultRisk;

    @ApiModelProperty(value = "逻辑删除", name = "delFlag")
    private Boolean delFlag;

    @ApiModelProperty(value = "风险等级", name = "riskLevel")
    private List<Integer> riskLevel;

    @ApiModelProperty(value = "风险主体内容", name = "riskEntity")
    private String riskEntity;

    @ApiModelProperty(value = "风险主体", name = "type")
    private String type;

    @ApiModelProperty(value = "不包含的风险主体", name = "unType")
    private List<String> unType;

    @ApiModelProperty(value = "策略状态（true/false）", name = "enable")
    private Boolean enable;

    @ApiModelProperty(value = "最近修改时间开始", name = "startUpdateTime")
    private Long startUpdateTime;

    @ApiModelProperty(value = "最近修改时间结束", name = "endUpdateTime")
    private Long endUpdateTime;

}
