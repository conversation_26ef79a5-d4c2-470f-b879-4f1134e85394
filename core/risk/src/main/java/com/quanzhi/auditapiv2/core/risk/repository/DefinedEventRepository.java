package com.quanzhi.auditapiv2.core.risk.repository;

import com.quanzhi.auditapiv2.common.util.dto.ListOutputDto;
import com.quanzhi.auditapiv2.core.risk.dto.common.GroupDataDto;
import com.quanzhi.auditapiv2.core.risk.dto.common.RiskDescribeDto;
import com.quanzhi.auditapiv2.core.risk.dto.common.RiskIpJoinAccountDto;
import com.quanzhi.auditapiv2.core.risk.dto.search.DefinedEventSearchDto;
import com.quanzhi.auditapiv2.core.risk.entity.DefinedEvent;
import com.quanzhi.auditapiv2.core.risk.entity.page.Page;
import com.quanzhi.auditapiv2.core.risk.entity.page.Pageable;

import java.util.List;


/**
 * @Author: HaoJun
 * @Date: 2021/8/9 9:47 上午
 */
public interface DefinedEventRepository extends CrudRepository<DefinedEvent, String> {

    Page<DefinedEvent> findByDate(String sqlSegment, String startDate, String endDate, Pageable pageable);

    Page<DefinedEvent> findByDate(String sqlSegment, String date, Pageable pageable);


    /**
     * 风险描述
     * @Comments:  <对此方法的描述，可以引用系统设计中的描述>
     * <AUTHOR> [<EMAIL>]
     * @since 2021-08-10 9:56
     * @param
     * @return
     */
    RiskDescribeDto riskDescribe(String policyId, String sql) throws Exception;

    /**
     * IPTop10
     * @Comments:  <对此方法的描述，可以引用系统设计中的描述>
     * <AUTHOR> [<EMAIL>]
     * @since 2021-08-10 9:56
     * @param
     * @return
     */
    List<GroupDataDto> ipTop10(String sql) throws Exception;

    /**
     * APITop10
     * @Comments:  <对此方法的描述，可以引用系统设计中的描述>
     * <AUTHOR> [<EMAIL>]
     * @since 2021-08-10 9:56
     * @param
     * @return
     */
    List<GroupDataDto> apiTop10(String sql) throws Exception;

    /**
     * UATop10
     * @Comments:  <对此方法的描述，可以引用系统设计中的描述>
     * <AUTHOR> [<EMAIL>]
     * @since 2021-08-10 9:56
     * @param
     * @return
     */
    List<GroupDataDto> uaTop10(String sql) throws Exception;

    /**
     * 终端类型Top10
     * @Comments:  <对此方法的描述，可以引用系统设计中的描述>
     * <AUTHOR> [<EMAIL>]
     * @since 2021-08-10 9:56
     * @param
     * @return
     */
    List<GroupDataDto> uaTypeTop10(String sql) throws Exception;

    /**
     * 数据分布情况环形图
     * @Comments:  <对此方法的描述，可以引用系统设计中的描述>
     * <AUTHOR> [<EMAIL>]
     * @since 2021-08-10 9:56
     * @param
     * @return
     */
    List<GroupDataDto> dataLabelDonutChart(String sql) throws Exception;

    /**
     * 事件返回情况环形图
     * @Comments:  <对此方法的描述，可以引用系统设计中的描述>
     * <AUTHOR> [<EMAIL>]
     * @since 2021-08-10 9:56
     * @param
     * @return
     */
    Long rspDonutChart(String sql, Boolean rsp) throws Exception;

    /**
     * 登录结果分布环形图
     * @Comments:  <对此方法的描述，可以引用系统设计中的描述>
     * <AUTHOR> [<EMAIL>]
     * @since 2021-08-10 9:56
     * @param
     * @return
     */
    List<GroupDataDto> loginResultDonutChart(String sql) throws Exception;

    /**
     * 返回状态码环形图
     * @Comments:  <对此方法的描述，可以引用系统设计中的描述>
     * <AUTHOR> [<EMAIL>]
     * @since 2021-08-10 9:56
     * @param
     * @return
     */
    List<GroupDataDto> rspStatusDonutChart(String sql) throws Exception;

    /**
     * 攻击结果分布环形图
     * @Comments:  <对此方法的描述，可以引用系统设计中的描述>
     * <AUTHOR> [<EMAIL>]
     * @since 2021-08-10 9:56
     * @param
     * @return
     */
    List<GroupDataDto> attackSuccessDonutChart(String sql) throws Exception;

    /**
     * 数据量分布条形图
     * @Comments:  <对此方法的描述，可以引用系统设计中的描述>
     * <AUTHOR> [<EMAIL>]
     * @since 2021-08-10 9:56
     * @param
     * @return
     */
    List<GroupDataDto> dataAmountBarChart(String sql) throws Exception;

    /**
     * 事件摘要列表
     * @Comments:  <对此方法的描述，可以引用系统设计中的描述>
     * <AUTHOR> [<EMAIL>]
     * @since 2021-08-10 9:56
     * @param
     * @return
     */
    ListOutputDto<DefinedEvent> selectDefinedEventList(DefinedEventSearchDto definedEventSearchDto, String sql) throws Exception;

    /**
     * 事件摘要分组
     * @Comments:  <对此方法的描述，可以引用系统设计中的描述>
     * <AUTHOR> [<EMAIL>]
     * @since 2021-08-10 9:56
     * @param
     * @return
     */
    List<GroupDataDto> selectDefinedEventGroup(DefinedEventSearchDto definedEventSearchDto, String sql) throws Exception;

    /**
     * 事件去重IP列表
     * @Comments:  <对此方法的描述，可以引用系统设计中的描述>
     * <AUTHOR> [<EMAIL>]
     * @since 2021-08-10 9:56
     * @param
     * @return
     */
    List<String> selectIpList(String sql) throws Exception;

    /**
     * 事件去重账号列表
     * @Comments:  <对此方法的描述，可以引用系统设计中的描述>
     * <AUTHOR> [<EMAIL>]
     * @since 2021-08-10 9:56
     * @param
     * @return
     */
    List<String> selectAccountList(String sql) throws Exception;

    /**
     * 事件去重账号列表
     * @Comments:  <对此方法的描述，可以引用系统设计中的描述>
     * <AUTHOR> [<EMAIL>]
     * @since 2021-08-10 9:56
     * @param
     * @return
     */
    List<GroupDataDto> selectPasswordList(String sql) throws Exception;

    /**
     * 事件去重账号密码组合列表
     * @Comments:  <对此方法的描述，可以引用系统设计中的描述>
     * <AUTHOR> [<EMAIL>]
     * @since 2021-08-10 9:56
     * @param
     * @return
     */
    List<GroupDataDto> selectAccountAndPasswordList(String sql) throws Exception;

    /**
     * IP关联账号列表
     * @Comments:  <对此方法的描述，可以引用系统设计中的描述>
     * <AUTHOR> [<EMAIL>]
     * @since 2021-08-10 9:56
     * @param
     * @return
     */
    ListOutputDto<RiskIpJoinAccountDto> selectIpJoinAccountList(DefinedEventSearchDto definedEventSearchDto) throws Exception;

    List<DefinedEvent.LabelValue> getDataLabelInfo(String sql)throws Exception;

    void delete(String date);

    void updateBatch(String updateSql, String whereSql);
}
