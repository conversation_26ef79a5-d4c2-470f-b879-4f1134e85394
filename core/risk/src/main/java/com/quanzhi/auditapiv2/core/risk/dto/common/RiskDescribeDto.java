package com.quanzhi.auditapiv2.core.risk.dto.common;

import com.quanzhi.audit_core.common.risk.PolicySnapshot;
import com.quanzhi.auditapiv2.core.risk.dto.RiskInfoDto;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 《风险描述DTO》
 *
 * <AUTHOR> [<EMAIL>]
 * @Project:
 * @Module ID:
 * @Comments:
 * @JDK version used:      <JDK1.8>
 * @since 2021-08-10-上午9:56:10
 */
@Data
@ApiModel
public class RiskDescribeDto implements Serializable {

    @ApiModelProperty(value = "风险ID", name = "operationId")
    private String operationId;

    @ApiModelProperty(value = "操作人", name = "operateName")
    private String operateName;

    @ApiModelProperty(value = "业务主体", name = "bizFields")
    private Map<String, List<String>> bizFields;

    @ApiModelProperty(value = "风险快照", name = "policySnapshot")
    private PolicySnapshot policySnapshot;

    @ApiModelProperty(value = "操作时间", name = "operateTime")
    private long operateTime;

    @ApiModelProperty(value = "api或app信息", name = "apiOrAppInfo")
    private Map<String, String> apiOrAppInfo = new HashMap<>();

    @ApiModelProperty(value = "攻击信息", name = "attackInfo")
    private Map<String, String> attackInfo = new HashMap<>();

    @ApiModelProperty(value = "分析总结", name = "conclusion")
    private String conclusion;

    @ApiModelProperty(value = "修复建议", name = "suggestion")
    private String suggestion;

    @ApiModelProperty(value = "涉及数据量", name = "dataCnt")
    private Integer dataCnt;

    @ApiModelProperty(value = "涉及数据标签种类", name = "labelCnt")
    private Integer labelCnt;

    @ApiModelProperty(value = "访问量", name = "visitCnt")
    private Integer visitCnt;

    @ApiModelProperty(value = "基线值", name = "baseline")
    private Long baseline;

    @ApiModelProperty(value = "涉及省份", name = "relatedProvince")
    private List<String> relatedProvince;

    @ApiModelProperty(value = "应用名称", name = "appName")
    private String appName;

    @ApiModelProperty(value = "应用", name = "host")
    private String host;

    @ApiModelProperty(value = "apiUri", name = "apiUri")
    private String apiUri;

    @ApiModelProperty(value = "apiUrl", name = "apiUrl")
    private String apiUrl;

    @ApiModelProperty(value = "appUri", name = "appUri")
    private String appUri;

    @ApiModelProperty(value = "ip", name = "ip")
    private String ip;

    /**
     * 使用终端
     */
    @ApiModelProperty(value = "使用终端", name = "useTerminalCnt")
    private Long useTerminalCnt;

    /**
     * 登录省份个数
     */
    @ApiModelProperty(value = "登录省份个数", name = "distinctProvinceCnt")
    private Long distinctProvinceCnt;

    /**
     * 涉及省份
     */
    @ApiModelProperty(value = "涉及省份", name = "distinctCntByLocation")
    private List<String> distinctCntByLocation;

    /**
     * 数量
     */
    @ApiModelProperty(value = "数量", name = "count")
    private Long count;

    /**
     * api数量
     */
    @ApiModelProperty(value = "api数量", name = "apiCount")
    private Long apiCount;

    /**
     * ip数量
     */
    @ApiModelProperty(value = "ip数量", name = "ipCount")
    private Long ipCount;

    /**
     * 账号数量
     */
    @ApiModelProperty(value = "账号数量", name = "accountCount")
    private Long accountCount;

    /**
     * 请求参数数量
     */
    @ApiModelProperty(value = "请求参数数量", name = "reqParamCount")
    private Long reqParamCount;

    /**
     * 分页页码数量
     */
    @ApiModelProperty(value = "分页页码数量", name = "pageCount")
    private Long pageCount;

    /**
     * 登录成功数量
     */
    @ApiModelProperty(value = "登录成功数量", name = "loginSuccessCount")
    private Long loginSuccessCount;

    /**
     * 攻击成功数量
     */
    @ApiModelProperty(value = "攻击成功数量", name = "attackSuccessCount")
    private Long attackSuccessCount;

    /**
     * ip去重集合
     */
    @ApiModelProperty(value = "ip去重集合", name = "ipList")
    private List<String> ipList;

    /**
     * 账号去重集合
     */
    @ApiModelProperty(value = "账号去重集合", name = "accountList")
    private List<String> accountList;

    /**
     * 密码去重集合
     */
    @ApiModelProperty(value = "密码去重集合", name = "passwordList")
    private List<GroupDataDto> passwordList;

    /**
     * 验证码去重集合
     */
    @ApiModelProperty(value = "验证码去重集合", name = "vocdeList")
    private List<String> vcodeList;

    /**
     * 数据标签分组数据
     */
    @ApiModelProperty(value = "数据标签分组数据", name = "dataLabelList")
    private List<GroupDataDto> dataLabelList;

    @ApiModelProperty(value = "数据标签", name = "dataLabelListValue")
    private List<String> dataLabelListValue;

    /**
     * 账号密码分组数据
     */
    @ApiModelProperty(value = "账号密码分组数据", name = "accountPasswordList")
    private List<GroupDataDto> accountPasswordList;

    /**
     * 风险事件id
     */
    @ApiModelProperty(value = "风险事件id", name = "riskInfoId")
    private String riskInfoId;

    /**
     * 风险日期
     */
    @ApiModelProperty(value = "风险日期", name = "date")
    private String date;

    /**
     * 风险首次发现时间
     */
    @ApiModelProperty(value = "风险首次发现时间", name = "firstTime")
    private Long firstTime;

    /**
     * 风险规则id
     */
    @ApiModelProperty(value = "风险规则id", name = "policyId")
    private String policyId;

    /**
     * 风险规则名称
     */
    @ApiModelProperty(value = "风险规则名称", name = "policyName")
    private String policyName;

    /**
     * 风险主体
     */
    @ApiModelProperty(value = "风险主体", name = "entitie")
    private RiskInfoDto.Entity entitie;

    /**
     * 影响面列表
     */
    @ApiModelProperty(value = "影响面列表", name = "channels")
    private List<RiskInfoDto.Entity> channels;

    /**
     * 风险等级
     *
     * @see com.quanzhi.auditapiv2.core.risk.entity.RiskInfo.RiskLevelEnum
     */
    @ApiModelProperty(value = "风险等级（1-低风险，2-中风险，3-高风险）", name = "level")
    private Integer level;

    /**
     * 风险等级名称
     */
    @ApiModelProperty(value = "风险等级名称", name = "level")
    private String levelName;

    /**
     * 风险状态
     *
     * @see com.quanzhi.auditapiv2.core.risk.entity.RiskInfo.RiskStateEnum
     */
    @ApiModelProperty(value = "风险状态（0-待确认，1-已忽略，2-已确认）", name = "state")
    private Integer state;

    /**
     * 风险状态名称
     */
    @ApiModelProperty(value = "风险状态名称", name = "stateName")
    private String stateName;

    /**
     * 风险备注
     */
    @ApiModelProperty(value = "风险备注", name = "remark")
    private String remark;

    /**
     * 整改建议
     */
    @ApiModelProperty(value = "整改建议", name = "suggest")
    private String suggest;

    /**
     * 威胁状态
     */
    @ApiModelProperty(value = "威胁状态", name = "threatState")
    private String threatState;

    /**
     * 威胁标签
     */
    @ApiModelProperty(value = "威胁标签", name = "threatLabels")
    private List<String> threatLabels;

    /**
     * 风险描述
     */
    @ApiModelProperty(value = "风险描述", name = "describeArray")
    private String[] describeArray;

    @ApiModelProperty(value = "手机号", name = "mobile")
    private String mobile;

    @ApiModelProperty(value = "风险描述", name = "riskDesc")
    private String riskDesc;

    /**
     * 风险管控策略ID
     */
    @ApiModelProperty(value = "风险管控策略ID", name = "strategyId")
    private String strategyId;
    /**
     * 风险策略类型 阻断限流
     */
    @ApiModelProperty(value = "风险策略类型 阻断/限流", name = "strategyType")
    private String strategyType;

    /**
     * 风险管控状态 0未管控 1已管控
     */
    @ApiModelProperty(value = "风险管控状态 0未管控 1已管控", name = "strategyStatus")
    private String strategyStatus;

    /**
     * 风险管控状态 0未管控 1已管控
     */
    @ApiModelProperty(value = "风险管控状态 0未管控 1已管控", name = "strategyStatus")
    private long strategyCount;
}
