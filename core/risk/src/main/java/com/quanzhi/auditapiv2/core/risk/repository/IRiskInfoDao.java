package com.quanzhi.auditapiv2.core.risk.repository;

import com.quanzhi.auditapiv2.common.dal.dao.base.IBaseDao;
import com.quanzhi.auditapiv2.common.dal.dto.common.CommonGroupDto;
import com.quanzhi.auditapiv2.common.dal.entity.CountEntity;
import com.quanzhi.auditapiv2.common.dal.entity.IpCountEntity;
import com.quanzhi.auditapiv2.common.util.dto.AggregationDto;
import com.quanzhi.auditapiv2.common.util.dto.GroupDto;
import com.quanzhi.auditapiv2.core.risk.dto.search.RiskSearchDto;
import com.quanzhi.auditapiv2.core.risk.entity.RiskInfo;
import com.quanzhi.auditapiv2.core.risk.entity.RiskTrend;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;

import java.util.List;
import java.util.Map;

/**
 * 《风险事件持久层接口》
 *
 * <AUTHOR> [<EMAIL>]
 * @Project:
 * @Module ID:
 * @Comments:
 * @JDK version used:      <JDK1.8>
 * @since 2021-08-10-上午9:56:10
 */
public interface IRiskInfoDao extends IBaseDao<RiskInfo> {

    /**
     * 获取过去一周新增的risk信息
     *
     * @return
     */
    List<RiskInfo> getWeekRiskInfos();

    /**
     * 标记一周前的风险
     *
     * @param riskSearchDto
     * @return
     */
    void markCleanSamplesRisk();

    Criteria getCriteria(RiskSearchDto riskSearchDto);

    List<RiskInfo> getRiskInfoByEntity(String entityValue);

    void batchIgored(List<String> riskIds, Integer state, String operateName, String remark);

    List<CommonGroupDto> groupByMap(Map<String, Object> map);

    /**
     * 查询风险事件列表(分页)
     *
     * @param
     * @return
     * @Comments: <对此方法的描述，可以引用系统设计中的描述>
     * <AUTHOR> [<EMAIL>]
     * @since 2021-08-10 9:56
     */
    List<RiskInfo> selectRiskInfoList(RiskSearchDto riskSearchDto, String field, Integer sort, Integer page, Integer limit) throws Exception;

    List<RiskInfo> selectRiskInfoListNoSort(RiskSearchDto riskSearchDto, Integer page, Integer limit) throws Exception;

    void ignoreRiskInfos(RiskSearchDto riskSearchDto, Integer state, String remark, String operateName) throws Exception;

    List<RiskInfo> selectRiskInfoListByChannels(RiskSearchDto riskSearchDto, String field, Integer sort, Integer page, Integer limit) throws Exception;

    /**
     * 查询风险事件数量
     *
     * @param
     * @return
     * @Comments: <对此方法的描述，可以引用系统设计中的描述>
     * <AUTHOR> [<EMAIL>]
     * @since 2021-08-10 9:56
     */
    Long totalCount(RiskSearchDto riskSearchDto) throws Exception;

    Long totalCountByChannels(RiskSearchDto riskSearchDto) throws Exception;

    /**
     * 查询风险事件列表
     *
     * @param
     * @return
     * @Comments: <对此方法的描述，可以引用系统设计中的描述>
     * <AUTHOR> [<EMAIL>]
     * @since 2021-08-10 9:56
     */
    List<RiskInfo> selectRiskInfoList(RiskSearchDto riskSearchDto, String field, Integer sort) throws Exception;

    List<RiskInfo> selectRiskByIp(String ip, String api);

    /**
     * 通过风险Id查找风险
     */
    List<String> selectIpsByPolicySnapshotId(List<String> id);

    List<RiskInfo> selectRisksByPolicySnapshotId(List<String> id);

    /**
     * 通过风险等级查找数据
     *
     * @param level
     * @return
     */
    List<String> selectIpsByLevel(List<Integer> level);

    List<RiskInfo> selectRisksByLevel(List<Integer> level);

    /**
     * id查询风险事件详情
     *
     * @param
     * @return
     * @Comments: <对此方法的描述，可以引用系统设计中的描述>
     * <AUTHOR> [<EMAIL>]
     * @since 2021-08-10 9:56
     */
    RiskInfo selectRiskInfoById(String id) throws Exception;

    RiskInfo selectRiskInfoByOperationId(String operationId) throws Exception;

    List<RiskInfo> selectRiskInfoByIds(List<String> ids, Integer sort, String field) throws Exception;

    /**
     * 编辑风险事件
     *
     * @param
     * @return
     * @Comments: <对此方法的描述，可以引用系统设计中的描述>
     * <AUTHOR> [<EMAIL>]
     * @since 2021-08-10 9:56
     */
    RiskInfo updateRiskInfo(RiskInfo riskInfo) throws Exception;

    /**
     * 批量忽略风险事件
     *
     * @param
     * @return
     * @Comments: <对此方法的描述，可以引用系统设计中的描述>
     * <AUTHOR> [<EMAIL>]
     * @since 2021-08-10 9:56
     */
    void ingoreRiskInfo(String policyId, String type, String entitiesValue, String channelsValue) throws Exception;

    /**
     * 风险事件分组
     *
     * @param
     * @return
     * @Comments: <对此方法的描述，可以引用系统设计中的描述>
     * <AUTHOR> [<EMAIL>]
     * @since 2021-08-10 9:56
     */
    List<AggregationDto> selectRiskInfoGroup(RiskSearchDto riskSearchDto, GroupDto groupDto, String field, Integer sort, Integer page, Integer limit) throws Exception;

    /**
     * 获取风险名称、分类的分组统计信息
     *
     * @return
     * @throws Exception
     */
    List<CommonGroupDto> selectRiskIdGroup() throws Exception;

    /**
     * 按日期查询风险事件数量
     *
     * @param startTime
     * @param EndTime
     * @return
     */
    List<RiskTrend> getSystemRiskTrend(Long startTime, Long endTime, boolean state);

    /**
     * 按ip分组查询风险事件
     *
     * @return
     */
    List<IpCountEntity> getRiskDistributeByIp(List<Integer> state);

    /**
     * 查询ip是否存在确认风险
     *
     * @return
     */
    boolean checkIpState(String ip);

    /**
     * 风险类型环形图
     *
     * @param
     * @return
     * @Comments: <对此方法的描述，可以引用系统设计中的描述>
     * <AUTHOR> [<EMAIL>]
     * @since 2021-08-31 16:52
     */
    List<AggregationDto> riskTypeDonutChartByAppUri(String appUri, GroupDto groupDto) throws Exception;

    List<IpCountEntity> getRiskCountryDistributeByDataReveal(List<String> riskNames);

    /**
     * 获取大屏概览信息 - 总风险事件、高风险风险事件
     *
     * @return
     */
    List<Long> getStatistics();

    /**
     * 根据日期获取风险信息
     *
     * @param startTime
     * @param endTime
     * @return
     */
    List<RiskInfo> listRiskInfoByDate(long startTime, long endTime, List<String> domains);

    List<RiskInfo> listRiskInfoByDate(long startTime, long endTime);

    List<RiskInfo> listRiskInfoByDate(long startTime);

    /**
     * 筛选一组app中风险数量前五的
     *
     * @param appUris
     * @return
     */
    List<CountEntity> selectRiskAppTop5ByAppUri(List<String> hosts);

    List<RiskInfo> selectRiskInfoByAppUri(String appUri, List<String> fields) throws Exception;

    List<Map> getRiskInfoLevelsBuApiUri(String apiUri);

    <T> List<T> findDistinct(Query query, String filed, String collection, Class<T> resultClass);

    List<RiskInfo> selectRiskInfo(String account, String date);

    boolean exist(String account, String date);

    List<RiskInfo> getByDataId(String dataId);

}
