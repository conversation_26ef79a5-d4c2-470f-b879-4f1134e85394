package com.quanzhi.auditapiv2.core.risk.repository;

import com.quanzhi.auditapiv2.common.dal.dao.base.IBaseDao;
import com.quanzhi.auditapiv2.core.risk.entity.DataRevealLabelCount;
import com.quanzhi.auditapiv2.core.risk.entity.DataRevealLabelCountVo;

import java.util.List;

public interface IDataRevealLabelCountDao extends IBaseDao<DataRevealLabelCount> {


    void saveDataRevealLabelCountVo(List<DataRevealLabelCountVo> insertList, String dataRevealLabelCountTable);

    List<DataRevealLabelCountVo> findDataRevealLabelCountVo(String dataRevealLabelCountTable);
}
