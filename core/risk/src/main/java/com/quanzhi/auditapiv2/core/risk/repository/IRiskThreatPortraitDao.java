package com.quanzhi.auditapiv2.core.risk.repository;

import com.quanzhi.auditapiv2.common.dal.entity.aggRisk.AggRiskInfo;
import com.quanzhi.auditapiv2.core.risk.entity.RiskInfo;
import com.quanzhi.auditapiv2.core.risk.entity.RiskTrend;
import com.quanzhi.auditapiv2.core.risk.entity.RiskType;
import com.quanzhi.auditapiv2.core.risk.entity.ThreatIp;

import java.util.List;

/**
 * @Auther: yangzixian
 * @Date: 2021/8/10 09:42
 * @Description:
 */
public interface IRiskThreatPortraitDao {

    /**
     * 查询威胁ip数据
     *
     * @param ip
     * @return
     */
    ThreatIp selectThreatIp(String ip);

    /**
     * 条件查询RiskInfo信息
     *
     * @param ip IP
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param state 是否限制状态（true ：查询排除忽略风险，false ：查询全部风险）
     * @return
     */
    List<AggRiskInfo> selectRiskInfo(String ip, String account, Long startTime, Long endTime, boolean state);

    /**
     * 获取风险类型分布信息
     * @param ip
     * @return
     */
    List<RiskType> selectRiskTypeDistributed(String ip);

    /**
     * 统计ip关联威胁总数
     * @param ip
     * @return
     */
    Long countRiskInfoByIp(String ip);

    /**
     * 统计ip一月触发风险趋势
     * @param ip
     * @param startDate
     * @param endDate
     * @return
     */
    List<RiskTrend> selectRiskTrend(String ip, long startTime, long endTime);

}
