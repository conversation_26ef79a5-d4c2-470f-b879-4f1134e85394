package com.quanzhi.auditapiv2.core.risk.repository;

import java.util.Collection;
import java.util.List;

/**
 * @Author: HaoJun
 * @Date: 2021/8/6 10:35 上午
 */
public interface CrudRepository<T, ID> extends Repository<T, ID> {

    boolean save(T entity);

    boolean insert(T entity);

    boolean insertBatch(Collection<T> entities);

    boolean saveBatch(Collection<T> entities);

    boolean update(T entity);

    T findById(ID id);

    List<T> findAll();

    List<T> findAllById(Collection<ID> ids);

    int count();

    void deleteById(ID id);
}

