package com.quanzhi.auditapiv2.core.risk.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * create at 2021/9/27 5:47 下午
 * @description: 数据泄漏类标签统计页面展示
 **/
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class DataRevealLabelCountVo {

    private String id;

    /**
     * 数据标签
     */
    private String dataLabel;

    /**
     * 标签数据量
     */
    private Long dataLabelValueCnt;

}