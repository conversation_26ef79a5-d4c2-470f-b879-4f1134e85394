package com.quanzhi.auditapiv2.core.risk.dto.search;

import com.quanzhi.auditapiv2.common.dal.dto.HttpAppDto;
import com.quanzhi.metabase.core.model.http.HttpApiResource;
import com.quanzhi.metabase.core.model.http.weakness.BriefWeaknesses;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * @author: yangzixian
 * @date: 27/3/2023 16:31
 * @description:
 */
@Data
@ApiModel
public class RiskApiInfoDto {

    private String host;

    private Long accountNum;

    private List<String> visitDomains;

    private List<String> deployDomains;

    private List<String> rspDataLabels;

    private List<String> featureLabels;

    private String deployIp;

    private BriefWeaknesses briefWeaknesses;

    @Mapper
    public interface RiskApiInfoDtoMapper {

        RiskApiInfoDto.RiskApiInfoDtoMapper INSTANCE = Mappers.getMapper(RiskApiInfoDto.RiskApiInfoDtoMapper.class);

        RiskApiInfoDto convert(HttpApiResource httpApiResource);

        RiskApiInfoDto convert(HttpAppDto httpAppDto);

    }

}
