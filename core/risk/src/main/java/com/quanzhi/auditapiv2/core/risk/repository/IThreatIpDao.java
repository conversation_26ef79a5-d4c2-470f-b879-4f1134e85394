package com.quanzhi.auditapiv2.core.risk.repository;

import com.quanzhi.auditapiv2.common.dal.dao.base.IBaseDao;
import com.quanzhi.auditapiv2.common.util.dto.AggregationDto;
import com.quanzhi.auditapiv2.common.util.dto.GroupDto;
import com.quanzhi.auditapiv2.core.risk.entity.ThreatIp;
import com.quanzhi.metabase.core.model.query.MetabaseQuery;

import java.util.List;

public interface IThreatIpDao extends IBaseDao<ThreatIp> {


    void threatIpTemp2ThreatTemp(String tempTable);

    List<AggregationDto> getThreatIpGroup(MetabaseQuery query, GroupDto groupDto, String field, Integer sort, Integer page, Integer limit) throws Exception;

    void createThreatIpTable();

    void updateThreatIpLabels(String ip, List<String> ipLabels);

    /**
     * 更新IP标签
     *
     * @param ipLabel
     * @return
     */
    void updateIpLabel(List<ThreatIp> threatIps);

    /**
     * 获取存在自定义标签的威胁IP
     *
     * @return
     */
    List<ThreatIp> getManualIpLabelThreatIp();

    List<ThreatIp> getThreatTop10();

    void updateByIp(ThreatIp threatIp);

}
