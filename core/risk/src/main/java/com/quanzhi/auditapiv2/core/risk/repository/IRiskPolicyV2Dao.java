package com.quanzhi.auditapiv2.core.risk.repository;

import com.alibaba.fastjson.JSONObject;
import com.quanzhi.auditapiv2.common.dal.dao.base.IBaseDao;
import com.quanzhi.auditapiv2.common.dal.dto.common.CommonGroupDto;
import com.quanzhi.auditapiv2.core.risk.entity.RiskPolicySearchDto;
import com.quanzhi.auditapiv2.core.risk.entity.RiskPolicyV2;

import java.util.List;
import java.util.Map;

/**
 * 《风险规则持久层接口》
 *
 * <AUTHOR> [<EMAIL>]
 * @Project:
 * @Module ID:
 * @Comments:
 * @JDK version used:      <JDK1.8>
 * @since 2021-08-10-上午9:56:10
 */
public interface IRiskPolicyV2Dao extends IBaseDao<RiskPolicyV2> {

    void saveRiskPolicy(JSONObject riskPolicy);

    RiskPolicyV2 selectRiskPolicyById(String policyId) throws Exception;

    Long count(RiskPolicySearchDto riskPolicySearchDto);

    Long totalCount();

    List<RiskPolicyV2> selectRiskPolices(RiskPolicySearchDto riskPolicySearchDto);

    List<CommonGroupDto> groupRiskPolices(RiskPolicySearchDto riskPolicySearchDto);

    void updateRiskPolicyState(String policyId, Boolean enable);

    void deleteRiskPolicy(String policyId);

    Map<String, String> getRiskPolicyIdMap();

    List<RiskPolicyV2> listAllPolicies();

    List<RiskPolicyV2> selectRiskPolicyByIds(List<String> ids);

    void upsertRiskPolicy(RiskPolicyV2 riskPolicy);

}
