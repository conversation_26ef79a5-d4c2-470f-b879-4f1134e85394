package com.quanzhi.auditapiv2.core.risk.repository;

import com.quanzhi.auditapiv2.core.risk.entity.RiskInfo;
import com.quanzhi.auditapiv2.core.risk.entity.page.Page;
import com.quanzhi.auditapiv2.core.risk.entity.page.Pageable;


/**
 * @Author: HaoJun
 * @Date: 2021/8/11 7:51 下午
 */
public interface RiskRepository {

    Page<RiskInfo> findByDate(String startDate, String endDate, Pageable pageable);

    Page<RiskInfo> findByDate(String date, Pageable pageable);

}
