package com.quanzhi.auditapiv2.core.risk.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 *
 * 《风险白名单DTO》
 *
 *
 * @Project:
 * @Module ID:
 * @Comments:
 * @JDK version used:      <JDK1.8>
 * <AUTHOR> [<EMAIL>]
 * @since 2022-04-21-上午9:56:10
 */
@Data
@ApiModel
public class RiskPolicyAllowListDto implements Serializable {

    @ApiModelProperty(value = "id", name = "id")
    private String id;

    /**
     * 风险规则id
     */
    @ApiModelProperty(value = "风险规则id", name = "policyId")
    private String policyId;

    /**
     * 风险主体类型
     * @see com.quanzhi.audit_core.common.risk.PolicyAllowList.PolicyAllowListTypeEnum
     */
    @ApiModelProperty(value = "风险主体类型", name = "type")
    private String type;

    /**
     * 风险主体内容
     */
    @ApiModelProperty(value = "风险主体类型", name = "value")
    private String value;
}
