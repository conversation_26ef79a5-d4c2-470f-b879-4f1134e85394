package com.quanzhi.auditapiv2.core.risk.repository;

import com.quanzhi.auditapiv2.common.dal.dao.base.IBaseDao;
import com.quanzhi.auditapiv2.core.risk.dto.common.RiskIpJoinAccountDto;
import com.quanzhi.auditapiv2.core.risk.dto.search.DefinedEventSearchDto;
import com.quanzhi.auditapiv2.core.risk.entity.RiskIpAccountInfo;
import com.quanzhi.auditapiv2.core.risk.entity.RiskIpDate;

import java.util.List;

public interface IRiskIpAccountInfoDao extends IBaseDao<RiskIpAccountInfo> {


    List<String> findAllAccountByIp(String ip);

    List<RiskIpJoinAccountDto> selectIpJoinAccountList(DefinedEventSearchDto definedEventSearchDto);

    Long countIpJoinAccount(DefinedEventSearchDto definedEventSearchDto);

    void addTodayRiskIp(RiskIpDate riskIpDate);

    List<String> findTodayRiskIp();
}
