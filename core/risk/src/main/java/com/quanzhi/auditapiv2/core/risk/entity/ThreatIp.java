package com.quanzhi.auditapiv2.core.risk.entity;

import lombok.Data;

import java.util.List;


/**
 * <AUTHOR>
 * @Date: 2021/8/11 16:04
 * @Description: 威胁ip表
 */
@Data
public class ThreatIp {

    private String id;
    /**
     * 威胁ip
     */
    private String ip;
    /**
     * 省
     */
    private String province;
    /**
     * 市
     */
    private String city;
    /**
     * 国家
     */
    private String country;
    /**
     * 地域
     */
    private String location;
    /**
     * 风险事件数量
     */
    private Long riskNum;
    /**
     * 确认风险数量
     */
    private Long confirmRiskNum;
    /**
     * ip标签
     */
    private List<String> ipLabels;

    /**
     * 是否手动编辑的数据标签，true的话，定时任务统计的话不更新ip标签字段
     */
    private Boolean manualIpLabel;

    /**
     * 网段
     */
    private List<String> networkSegment;

    /**
     * 威胁状态
     */
    private String threatState;

    /**
     * 威胁标签
     */
    private List<String> threatLabels;

    /**
     * 首次发现时间
     */
    private Long firstTime;
    /**
     * 最近发现时间
     */
    private Long lastTime;
    /**
     * 更新时间
     */
    private String updateTime;
    /**
     * 关联账号
     */
    private List<String> accounts;

}
