package com.quanzhi.auditapiv2.core.risk.entity;

import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * create at 2022/9/1 10:58 上午
 * @description:
 **/
@Data
public class StrippedEvent extends BaseEntity {

    /**
     * 事件ID
     */
    private String id;
    /**
     * 事件时间
     */
    private Long timestamp;
    /**
     * 日期，如2021-02-01
     */
    private String date;
    /**
     * 去掉参数的接口URL
     */
    private String apiUrl;
    /**
     * ip
     */
    private String ip;
    /**
     * 账号
     */
    private String account;
    /**
     * 账号类型
     */
    private String accountType;
    /**
     * 密码
     */
    private String password;
    /**
     * 请求方法
     */
    private String method;
    /**
     * 请求类型
     */
    private String reqContentType;
    /**
     * User-Agent
     */
    private String ua;
    /**
     * User-Agent类型
     */
    private String uaType;
    /**
     * REFERER
     */
    private String referer;
    /**
     * COOKIE 数据
     */
    private String cookie;
    /**
     * 返回状态码
     */
    private String rspStatus;
    /**
     * 返回格式
     */
    private String rspContentType;
    /**
     * 返回长度
     */
    private Integer rspContentLength;
    /**
     * 返回数据标签
     */
    private List<String> rspDataLabelIds;
    /**
     * 请求数据标签
     */
    private List<String> reqDataLabelIds;
    /**
     * 数据标签
     */
    private List<String> dataLabelIds;
    /**
     * 去重标签数量
     */
    private Long rspLabelContentDistinctCount;

    /**
     * 标签内容
     */
    private List<DefinedEvent.LabelValue> labelValues;

    /**
     * 登录信息
     */
    private String loginInfo;
    /**
     * 登陆结果
     */
    private String loginResult;
    /**
     * 是否攻击
     */
    private Boolean attack;
    /**
     * 攻击方式
     */
    private String attackType;
    /**
     * 攻击结果
     */
    private Integer attackSuccess;
    /**
     * 事件定义
     */
    private List<String> eventDefineIds;
    /**
     * 部署域
     */
    private List<String> deployDomains;
    /**
     * 访问域
     */
    private List<String> accessDomains;
    /**
     * 应用域
     */
    private String host;
    /**
     * 风险类型
     */
    private List<String> riskTypes;
    /**
     * 资产唯一标识
     */
    private String uri;

    /**
     * 应用uri
     */
    private String appUri;
    /**
     * 省
     */
    private String province;
    /**
     * 市
     */
    private String city;
    /**
     * 国家
     */
    private String country;
    /**
     * 提取到的数据
     */
    private List<String> enumerateParaSigns;
    /**
     * 分钟级别
     */
    private Long minute;

    private List<String> statMarkingTags;
    /**
     * 保留标记
     */
    private Boolean keepFlag = false;

    /**
     * get请求参数
     */
    private String getParam;
    /**
     * post请求参数
     */
    private String postParam;
    /**
     * 攻击关键字
     */
    private List<String> attackKeywords;



}