package com.quanzhi.auditapiv2.core.risk.entity;

import com.alibaba.fastjson.JSONObject;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * @author: yang<PERSON>xian
 * @date: 9/3/2023 15:52
 * @description:
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class RiskTemplate {

    private List<JSONObject> templates;
    private List<JSONObject> operators;
    private List<JSONObject> featureDescriptions;
    private List<JSONObject> entities;

}
