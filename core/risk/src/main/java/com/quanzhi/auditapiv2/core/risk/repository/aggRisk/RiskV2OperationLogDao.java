package com.quanzhi.auditapiv2.core.risk.repository.aggRisk;

import com.quanzhi.auditapiv2.common.dal.entity.aggRisk.AggRiskOperationLog;
import com.quanzhi.auditapiv2.common.dal.entity.aggRisk.RiskV2OperationLog;

import java.util.List;

/**
 * @Author: yangzx
 * @Date: 2024/10/9 14:37
 */
public interface RiskV2OperationLogDao {

    void insertLog(RiskV2OperationLog riskV2OperationLog);

    void batchInsertLog(List<RiskV2OperationLog> riskV2OperationLogs);

    List<RiskV2OperationLog> listLog(String riskId);

}
