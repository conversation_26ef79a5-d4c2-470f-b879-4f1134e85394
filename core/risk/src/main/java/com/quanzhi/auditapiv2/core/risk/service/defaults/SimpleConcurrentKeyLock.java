package com.quanzhi.auditapiv2.core.risk.service.defaults;

import com.quanzhi.auditapiv2.core.risk.service.KeyLock;

import java.util.concurrent.ConcurrentHashMap;

/**
 * @Author: HaoJun
 * @Date: 2021/1/11 9:46 上午
 */
public class SimpleConcurrentKeyLock implements KeyLock {

    private ConcurrentHashMap<String, Object> keyMap = new ConcurrentHashMap<>();

    private static final Object OBJECT_VALUE = new Object();

    @Override
    public boolean tryLock(String key) {
        return keyMap.putIfAbsent(key, OBJECT_VALUE) == null;
    }

    @Override
    public void unlock(String key) {
        keyMap.remove(key);
    }
}
