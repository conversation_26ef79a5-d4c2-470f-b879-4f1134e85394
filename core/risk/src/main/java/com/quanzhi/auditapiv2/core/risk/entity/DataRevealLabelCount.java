package com.quanzhi.auditapiv2.core.risk.entity;

import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * create at 2021/8/13 5:01 下午
 * @description: 数据泄漏类标签统计表
 **/
@Data
public class DataRevealLabelCount {

    private String id;

    /**
     * 风险事件ID
     */
    private String riskId;

    private List<DataLabelCount> dataLabelCountList;

    private Long createTime;

    private Boolean delFlag = false;


    @Data
    public static class DataLabelCount {
        /**
         * 数据标签
         */
        private String dataLabel;

        /**
         * 去重的标签值
         */
        private List<String> distinctDataLabelValList;

    }

}