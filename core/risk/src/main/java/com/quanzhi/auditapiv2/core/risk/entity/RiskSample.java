package com.quanzhi.auditapiv2.core.risk.entity;

import com.quanzhi.audit_core.common.model.HttpEvent;
import lombok.Data;
import org.springframework.data.mongodb.core.mapping.Document;

/**
 * @Author: HaoJun
 * @Date: 2021/8/17 3:26 下午
 */
@Data
@Document("riskSample")
public class RiskSample {

    private String id;

    private String sample;

    private String riskId;

    private Long createTime;

    private HttpEvent httpEvent;

    private Boolean isLive;

}
