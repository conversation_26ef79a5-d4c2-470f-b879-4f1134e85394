package com.quanzhi.auditapiv2.core.risk.entity;

import lombok.Data;

/**
 * @Author: Hao<PERSON>un
 * @Date: 2021/8/11 7:59 下午
 * <p>
 * 清理definedEvent事件日志表
 */
@Data
public class DefinedEventCleanLog {

    private String startDate;

    private String endDate;

    private DefinedEventCleanLog.CleanState state;

    private String msg;

    private Long duration;

    private Long createTime;

    private Long updateTime;

    private String id;

    public enum CleanState {
        SUCCESS, FAIL, RUNNING
    }

    public DefinedEventCleanLog() {
        this.createTime = System.currentTimeMillis();
        this.updateTime = System.currentTimeMillis();
    }
}
