package com.quanzhi.auditapiv2.core.risk.entity;

import com.quanzhi.audit_core.common.risk.Policy;
import com.quanzhi.audit_core.common.risk.Rule;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 《风险规则》
 *
 * <AUTHOR> [<EMAIL>]
 * @Project:
 * @Module ID:
 * @Comments:
 * @JDK version used:      <JDK1.8>
 * @since 2021-08-10-上午9:56:10
 */
@Data
public class AuditRiskPolicy implements Serializable {

    private String id;

    private String name;

    private String granularity;

    private boolean delFlag;

    private Rule entityRule;

    private Rule quotaRule;

    private String level;

    private List<Policy.EntityRelation> entityConfig;

    private String entityRuleHash;

    private Policy.RiskTimeConfig riskTimeConfig;

    private boolean enable;

}
