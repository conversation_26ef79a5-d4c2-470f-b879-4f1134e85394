package com.quanzhi.auditapiv2.core.risk.service;

import java.io.Closeable;
import java.util.List;

/**
 * @Author: HaoJun
 * @Date: 2021/1/6 12:06 下午
 */
public interface AutoFlushBatch<T> extends Closeable {
    /**
     * 添加成员
     *
     * @param item
     * @return
     */
    void add(T item);

    /**
     * 执行手动flush
     */
    void flush();

    /**
     * 设置单次flush批量上限
     *
     * @param size
     */
    void setBatchSize(int size);

    /**
     * 检测是否需要flush
     *
     * @return
     */
    boolean checkFlush();

    /**
     * 刷新执行器
     *
     * @param executor
     */
    void setFlushExecutor(FlushExecutor<T> executor);

    int size();

    boolean isEmpty();

    /**
     * 记录数
     * @return
     */
    int records();

    /**
     *  有无加过数据
     */
    boolean hasAdd();

    /**
     * 关闭
     */
    @Override
    void close();

    @FunctionalInterface
    interface FlushExecutor<T> {

        void doFlush(List<T> list);

        default void beforeAdd(T item) {
        }

        default void onClosed(){
        }
    }
}
