package com.quanzhi.auditapiv2.core.risk.entity;

import com.quanzhi.audit_core.common.risk.Policy;
import com.quanzhi.auditapiv2.common.util.utils.DateUtils;
import com.quanzhi.auditapiv2.core.risk.dto.RiskInfoDto;
import com.quanzhi.auditapiv2.core.risk.dto.common.RiskDescribeDto;
import com.quanzhi.metabase.core.model.ResourceEntity;
import com.quanzhi.metabase.core.model.http.HttpAppResource;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.io.Serializable;
import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * 《风险事件》
 *
 * <AUTHOR> [<EMAIL>]
 * @Project:
 * @Module ID:
 * @Comments:
 * @JDK version used:      <JDK1.8>
 * @since 2021-08-10-上午9:56:10
 */
@Data
public class RiskInfo extends com.quanzhi.audit_core.common.risk.RiskInfo implements Serializable {

    /**
     * 忽略类型
     */
    private String ignoreType;

    private String remark;

    private String aggRiskId;

    private String operateName;

    private Long operateTime;

    private List<ResourceEntity.Node> nodes;

    /**
     * 业务主体
     */
    private Map<String, List<String>> bizFields;

    /**
     * 所属部门
     */
    private List<HttpAppResource.Department> departments;

    private boolean cleanSample = false;

    /**
     * 整改建议
     */
    private String suggest;

    /**
     * 风险分析
     */
    private String analysis;

    /**
     * 工单状态
     */
    private Integer orderFlag;

    /**
     * 风险相关威胁IP信息
     */
    private List<String> threatIps;

    /**
     * 风险相关威胁账号信息
     */
    private List<String> threatAccounts;

    /**
     * 风险描述
     */
    private RiskDescribeDto riskDescribe;

    private String riskDesc;
    /**
     * 是否阻断
     */
    private Boolean blockFlag;

    private String conclusion;
    private String suggestion;

    private Long handleTime;

    @Mapper
    public interface RiskInfoMapper {

        RiskInfo.RiskInfoMapper INSTANCE = Mappers.getMapper(RiskInfo.RiskInfoMapper.class);

        RiskInfo convert(RiskInfoDto riskInfoDto);
    }


    /**
     * 获取event事件的查询SQL
     * <p>
     * eventId in [123] and xxx in [xx] and xx
     *
     * @return
     */
    public String createEventQuerySqlSegment() {
        StringBuilder sb = new StringBuilder();
        if (Policy.GranularityEnum.EVENT.name().equals(getPolicySnapshot().getGranularity())) {
            if (getClue() == null
                    || getClue().getEvent() == null
                    || getClue().getEvent().getEventIds() == null
                    || getClue().getEvent().getEventIds().size() == 0) {
                throw new IllegalStateException("风险Clue数据异常");
            }

            sb.append(" id IN " + joining(getClue().getEvent().getEventIds()));
        } else {
            if (getEntities() != null && getEntities().size() > 0) {
                for (com.quanzhi.audit_core.common.risk.RiskInfo.Entity entity : getEntities()) {
                    tryAppendAnd(sb);
                    appendEntitySegment(sb, entity);
                }
            }
            if (getChannels() != null && getChannels().size() > 0) {
                for (com.quanzhi.audit_core.common.risk.RiskInfo.Entity entity : getChannels()) {
                    tryAppendAnd(sb);
                    appendEntitySegment(sb, entity);
                }
            }
            if (getClue() != null
                    && getClue().getStat() != null
                    && getClue().getStat().getTsSet() != null
                    && getClue().getStat().getTsSet().size() > 0) {
                tryAppendAnd(sb);

                sb.append(" minute IN " + joining(getClue().getStat().getTsSet()));
            }
            if (getClue() != null
                    && getClue().getEvent() != null
                    && StringUtils.isNotEmpty(getClue().getEvent().getEventRuleTag())) {
                tryAppendAnd(sb);
                sb.append(String.format(" has(statMarkingTags, %s) ", "'" + getClue().getEvent().getEventRuleTag() + "'"));
            }
            if (getDate() != null) {
                tryAppendAnd(sb);
                sb.append(String.format(" date='%s' ", DateUtils.format(getDate(), DateUtils.DATE_PATTERN)));
            }
        }
        return sb.toString();
    }

    private void tryAppendAnd(StringBuilder sb) {
        if (sb.length() > 0) {
            sb.append(" AND ");
        }
    }

    private <T> String joining(Collection<T> collection) {
        StringBuilder stringBuilder = new StringBuilder();
        stringBuilder.append(" [");
        int len = collection.size();
        int index = 0;
        for (T c : collection) {
            stringBuilder.append("'" + c + "'");
            if (index < len - 1) {
                stringBuilder.append(",");
            }
            index++;
        }
        stringBuilder.append("] ");
        return stringBuilder.toString();
    }

    private void appendEntitySegment(StringBuilder sb, Entity entity) {
        if (Policy.EntityEnum.IP.name().equals(entity.getType())) {
            sb.append("ip = " + "'" + entity.getValue() + "'");
        } else if (Policy.EntityEnum.ACCOUNT.name().equals(entity.getType())) {
            sb.append("account = " + "'" + entity.getValue() + "'");
        } else if (Policy.EntityEnum.API.name().equals(entity.getType())) {
            sb.append("apiUri = " + "'" + entity.getValue() + "'");
        } else if (Policy.EntityEnum.APP.name().equals(entity.getType())) {
            sb.append("appUri = " + "'" + entity.getValue() + "'");
        }
    }

    public enum RiskStateEnum {

        /**
         * 待确认
         */
        NOT_HANDLE(0, "待确认"),

        /**
         * 已忽略
         */
        HAS_IGNORE(1, "已忽略"),

        /**
         * 已确认
         */
        HAS_HANDLE(2, "已确认");

        private Integer state;

        private String name;

        RiskStateEnum(Integer state, String name) {

            this.state = state;
            this.name = name;
        }

        public Integer getState() {
            return this.state;
        }

        public String getName() {
            return this.name;
        }

        public static RiskStateEnum getRiskStateEnum(Integer state) {

            for (RiskStateEnum riskStateEnum : RiskStateEnum.values()) {
                if (riskStateEnum.getState().equals(state)) {
                    return riskStateEnum;
                }
            }
            return null;
        }
    }

    public enum RiskLevelEnum {

        /**
         * 低风险
         */
        LOW(1, "低危"),

        /**
         * 中风险
         */
        MEDIUM(2, "中危"),

        /**
         * 高风险
         */
        HIGH(3, "高危");

        private Integer level;

        private String name;

        RiskLevelEnum(Integer level, String name) {

            this.level = level;
            this.name = name;
        }

        public Integer getLevel() {
            return this.level;
        }

        public String getName() {
            return this.name;
        }

        public static RiskLevelEnum getRiskLevelEnum(Integer level) {

            for (RiskLevelEnum riskLevelEnum : RiskLevelEnum.values()) {
                if (riskLevelEnum.getLevel().equals(level)) {
                    return riskLevelEnum;
                }
            }
            return null;
        }
    }

    public enum IgnoreTypeEnum {

        /**
         * IP忽略
         */
        IP,

        /**
         * 账号忽略
         */
        ACCOUNT,

        /**
         * API忽略
         */
        API,

        /**
         * IP+HOST忽略
         */
        IP_HOST,

        /**
         * IP+API忽略
         */
        IP_API,

        /**
         * 账号+HOST忽略
         */
        ACCOUNT_HOST,

        /**
         * 账号+API忽略
         */
        ACCOUNT_API;

        public static IgnoreTypeEnum getIgnoreTypeEnum(String name) {

            for (IgnoreTypeEnum ignoreTypeEnum : IgnoreTypeEnum.values()) {
                if (ignoreTypeEnum.name().equals(name)) {
                    return ignoreTypeEnum;
                }
            }
            return null;
        }
    }
}
