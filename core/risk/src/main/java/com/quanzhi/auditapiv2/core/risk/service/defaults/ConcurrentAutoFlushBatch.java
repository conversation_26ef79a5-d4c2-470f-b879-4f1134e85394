package com.quanzhi.auditapiv2.core.risk.service.defaults;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.ConcurrentLinkedQueue;

/**
 * @Author: HaoJun
 * @Date: 2021/1/6 12:22 下午
 */
public class ConcurrentAutoFlushBatch<T> extends AbstractSafeFlushBatch<T> {

    private int batchSie;

    private ConcurrentLinkedQueue<T> queue;

    private FlushExecutor flushExecutor;

    public static final int DEFAULT_BATCH_SIE = 4096;

    private boolean hasAdd=false;

    public ConcurrentAutoFlushBatch(FlushExecutor<T> flushExecutor) {
        this(flushExecutor, DEFAULT_BATCH_SIE);
    }

    public ConcurrentAutoFlushBatch(FlushExecutor<T> flushExecutor, int batchSie) {
        this.batchSie = batchSie;
        init();
        this.flushExecutor = flushExecutor;
    }

    @Override
    public void add(T item) {
        if (flushExecutor != null) {
            flushExecutor.beforeAdd(item);
        }
        queue.add(item);
        if(!hasAdd){
            hasAdd=true;
        }
        if (checkFlush()) {
            flush();
        }
    }

    @Override
    public void onFlush(List<T> items) {
        if (flushExecutor != null) {
            flushExecutor.doFlush(items);
        }
    }

    @Override
    protected List<T> poll() {
        List<T> list = new ArrayList<>(batchSie * 2);
        for (; ; ) {
            T item = queue.poll();
            if (item == null) {
                break;
            }
            list.add(item);
            if (list.size() >= batchSie) {
                break;
            }
        }
        return list;
    }

    @Override
    public void setBatchSize(int size) {
        this.batchSie = size;
        init();
    }

    @Override
    public boolean checkFlush() {
        return queue.size() >= batchSie;
    }

    @Override
    public void setFlushExecutor(FlushExecutor<T> flushExecutor) {
        this.flushExecutor = flushExecutor;
    }

    @Override
    public int size() {
        return queue.size();
    }

    @Override
    public boolean isEmpty() {
        return queue.isEmpty();
    }

    @Override
    public int records() {
        return 0;
    }

    @Override
    public boolean hasAdd() {
        return hasAdd;
    }

    private void init() {
        initBatch();
    }

    private void initBatch() {
        queue = new ConcurrentLinkedQueue<>();
    }

    @Override
    public void close() {
        flush();
        if (flushExecutor != null) {
            flushExecutor.onClosed();
        }
    }
}
