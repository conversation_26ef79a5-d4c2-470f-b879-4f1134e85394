package com.quanzhi.auditapiv2.core.risk.dto.search;

import com.quanzhi.auditapiv2.core.risk.dto.common.CommonSearchDto;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 *
 * 《风险事件CK查询条件DTO》
 *
 *
 * @Project:
 * @Module ID:
 * @Comments:
 * @JDK version used:      <JDK1.8> 
 * <AUTHOR> [<EMAIL>]
 * @since 2021-08-10-上午9:56:10
 */
@Data
@ApiModel
public class DefinedEventSearchDto extends CommonSearchDto {

    /**
     * id
     */
    @ApiModelProperty(value = "风险事件id", name = "id")
    private String id;

    /**
     * api统一标识
     */
    @ApiModelProperty(value = "api统一标识", name = "apiUri")
    private String apiUri;

    /**
     * 去掉参数的接口URL
     */
    @ApiModelProperty(value = "去掉参数的接口URL", name = "apiUrl")
    private String apiUrl;

    /**
     * ip
     */
    @ApiModelProperty(value = "ip", name = "ip")
    private String ip;

    /**
     * 账号
     */
    @ApiModelProperty(value = "账号", name = "account")
    private String account;

    /**
     * 账号类型
     */
    @ApiModelProperty(value = "账号类型", name = "accountType")
    private String accountType;

    /**
     * User-Agent
     */
    @ApiModelProperty(value = "User-Agent", name = "ua")
    private String ua;

    /**
     * User-Agent类型
     */
    @ApiModelProperty(value = "User-Agent类型", name = "uaType")
    private String uaType;

    /**
     * REFERER
     */
    @ApiModelProperty(value = "referer", name = "referer")
    private String referer;

    /**
     * COOKIE 数据
     */
    @ApiModelProperty(value = "cookie", name = "cookie")
    private String cookie;

    /**
     * 返回状态码
     */
    @ApiModelProperty(value = "返回状态码", name = "rspStatus")
    private String rspStatus;
    
    /**
     * 返回数据标签
     */
    @ApiModelProperty(value = "返回数据标签", name = "rspDataLabelIds")
    private List<String> rspDataLabelIds;

    /**
     * 国家
     */
    @ApiModelProperty(value = "国家", name = "country")
    private String country;

    /**
     * 省
     */
    @ApiModelProperty(value = "省", name = "province")
    private String province;

    /**
     * 市
     */
    @ApiModelProperty(value = "市", name = "city")
    private String city;

    /**
     * 登陆结果
     */
    @ApiModelProperty(value = "登陆结果", name = "loginResult")
    private String loginResult;

    /**
     * 攻击结果
     *
     * @see com.quanzhi.auditapiv2.core.risk.dto.common.GroupDataDto.AttackSuccessEnum
     */
    @ApiModelProperty(value = "攻击结果（0-登录失败，1-登录成功，2-未知）", name = "attackSuccess")
    private Integer attackSuccess;

    /**
     * get与post合并or搜索
     */
    @ApiModelProperty(value = "get与post合并or搜索", name = "reqParam")
    private String reqParam;

    /**
     * get请求参数
     */
    @ApiModelProperty(value = "get请求参数", name = "getParam")
    private String getParam;

    /**
     * post请求参数
     */
    @ApiModelProperty(value = "post请求参数", name = "postParam")
    private String postParam;

    /**
     * 分页页码
     */
    @ApiModelProperty(value = "分页页码", name = "enumerateParaSigns")
    private String enumerateParaSigns;

    /**
     * 事件时间-start
     */
    @ApiModelProperty(value = "事件时间-start", name = "timestampStart")
    private Long timestampStart;

    /**
     * 事件时间-end
     */
    @ApiModelProperty(value = "事件时间-end", name = "timestampEnd")
    private Long timestampEnd;

    public enum GroupFieldEnum {

        /**
         * IP
         */
        IP("ip"),

        /**
         * 账号
         */
        ACCOUNT("account"),

        /**
         * 账号类型
         */
        ACCOUNT_TYPE("accountType"),

        /**
         * ua
         */
        UA("ua"),

        /**
         * 终端类型
         */
        UA_TYPE("uaType"),

        /**
         * 登录结果
         */
        LOGIN_RESULT("loginResult"),

        /**
         * 攻击结果
         */
        ATTACK_SUCCESS("attackSuccess"),

        /**
         * 地域
         */
        COUNTRY_PROVINCE_CITY("country,province,city");

        String name;

        GroupFieldEnum(String name){

            this.name = name;
        }

        public String getName(){
            return name;
        }

        public static GroupFieldEnum getGroupFieldEnum(String name) {

            for (GroupFieldEnum groupFieldEnum : GroupFieldEnum.values()) {
                if(groupFieldEnum.getName().equals(name)) {
                    return groupFieldEnum;
                }
            }
            return null;
        }
    }
}