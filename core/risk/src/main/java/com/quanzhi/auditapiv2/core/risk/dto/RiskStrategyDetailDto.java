package com.quanzhi.auditapiv2.core.risk.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class RiskStrategyDetailDto {

    /**
     * 风险管控策略ID
     */
    @ApiModelProperty(value = "风险管控策略ID", name = "strategyId")
    private String strategyId;
    /**
     * 风险策略类型 阻断限流
     */
    @ApiModelProperty(value = "风险策略类型 阻断/限流", name = "strategyType")
    private String strategyType;

    /**
     * 风险管控状态 0未管控 1已管控
     */
    @ApiModelProperty(value = "风险管控状态 0未管控 1已管控", name = "strategyStatus")
    private String strategyStatus;
}
