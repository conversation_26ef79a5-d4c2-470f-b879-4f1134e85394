package com.quanzhi.auditapiv2.core.risk.repository;

import com.quanzhi.auditapiv2.common.dal.dao.base.IBaseDao;
import com.quanzhi.auditapiv2.core.risk.entity.RiskPolicyAllowList;

import java.util.List;

/**
 *
 * 《风险白名单持久层接口》
 *
 *
 * @Project:
 * @Module ID:
 * @Comments:
 * @JDK version used:      <JDK1.8> 
 * <AUTHOR> [<EMAIL>]
 * @since 2021-08-10-上午9:56:10
 */
public interface IRiskPolicyAllowListDao extends IBaseDao<RiskPolicyAllowList> {

	/**
	 * id查询风险白名单详情
	 * @Comments:  <对此方法的描述，可以引用系统设计中的描述>
	 * <AUTHOR> [<EMAIL>]
	 * @since 2021-08-10 9:56
	 * @param
	 * @return
	 */
	RiskPolicyAllowList selectRiskPolicyAllowListById(String id) throws Exception;

	void updatePolicyId(String policyId);

	/**
	 * 风险规则id、类型、内容查询风险白名单
	 * @Comments:  <对此方法的描述，可以引用系统设计中的描述>
	 * <AUTHOR> [<EMAIL>]
	 * @since 2021-08-10 9:56
	 * @param
	 * @return
	 */
	RiskPolicyAllowList selectRiskPolicyAllowList(String policyId, String type, String value) throws Exception;

	/**
	 * 新增风险白名单
	 * @Comments:  <对此方法的描述，可以引用系统设计中的描述>
	 * <AUTHOR> [<EMAIL>]
	 * @since 2021-08-10 9:56
	 * @param
	 * @return
	 */
	RiskPolicyAllowList insertRiskPolicyAllowList(RiskPolicyAllowList riskPolicyAllowList) throws Exception;

	/**
	 * 编辑风险白名单
	 * @Comments:  <对此方法的描述，可以引用系统设计中的描述>
	 * <AUTHOR> [<EMAIL>]
	 * @since 2021-08-10 9:56
	 * @param
	 * @return
	 */
	RiskPolicyAllowList updateRiskPolicyAllowList(RiskPolicyAllowList riskPolicyAllowList) throws Exception;

	/**
	 * 删除风险白名单
	 * @Comments:  <对此方法的描述，可以引用系统设计中的描述>
	 * <AUTHOR> [<EMAIL>]
	 * @since 2021-08-10 9:56
	 * @param
	 * @return
	 */
	void deleteRiskPolicyAllowList(List<String> ids) throws Exception;

	void deleteByRiskPolicyId(String id);

	List<RiskPolicyAllowList> getByRiskPolicyId(String id);

    List<RiskPolicyAllowList> getByRiskPolicyIds(List<String> ids);
}
