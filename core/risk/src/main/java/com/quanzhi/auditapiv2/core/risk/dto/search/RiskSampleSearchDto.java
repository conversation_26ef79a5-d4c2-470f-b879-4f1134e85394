package com.quanzhi.auditapiv2.core.risk.dto.search;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * create at 2021/9/14 3:08 下午
 * @description:
 **/
@Data
@ApiModel
public class RiskSampleSearchDto {

    private String id;

    @ApiModelProperty(value = "异常事件ID")
    private String riskId;

    @ApiModelProperty(value = "风险事件ID")
    private String aggRiskId;


    @ApiModelProperty(value = "样例ID", name = "sampleId")
    private String sampleId;

    @ApiModelProperty(value = "是否启用模糊查询", name = "useRegex")
    private Boolean useRegex = true;

    @ApiModelProperty(value = "是否脱敏", name = "desensitize")
    private boolean desensitize;

    /**
     * 事件ID
     */
    @ApiModelProperty(value = "事件ID", name = "eventId")
    private String eventId;

    /**
     * 排序规则
     */
    @ApiModelProperty(value = "排序规则", name = "sort")
    private Integer sort;

    /**
     * 排序字段
     */
    @ApiModelProperty(value = "排序字段", name = "sortField")
    private String sortField;

    /**
     * 分组字段
     */
    @ApiModelProperty(value = "分组字段", name = "groupField")
    private String groupField;

    /**
     * 登陆结果
     */
    @ApiModelProperty(value = "登陆结果", name = "loginResult")
    private String loginResult;

    /**
     * 终端类型
     */
    @ApiModelProperty(value = "终端类型", name = "uaType")
    private String uaType;

    /**
     * api统一标识
     */
    @ApiModelProperty(value = "api统一标识", name = "apiUri")
    private String apiUri;

    /**
     * 去掉参数的接口URL
     */
    @ApiModelProperty(value = "去掉参数的接口URL", name = "apiUrl")
    private String apiUrl;

    /**
     * ip
     */
    @ApiModelProperty(value = "ip", name = "ip")
    private String ip;

    /**
     * 账号
     */
    @ApiModelProperty(value = "账号", name = "account")
    private String account;

    /**
     * 账号类型
     */
    @ApiModelProperty(value = "账号类型", name = "accountType")
    private String accountType;

    /**
     * User-Agent
     */
    @ApiModelProperty(value = "User-Agent", name = "ua")
    private String ua;

    /**
     * REFERER
     */
    @ApiModelProperty(value = "referer", name = "referer")
    private String referer;

    /**
     * COOKIE 数据
     */
    @ApiModelProperty(value = "cookie", name = "cookie")
    private String cookie;

    /**
     * 返回状态码
     */
    @ApiModelProperty(value = "返回状态码", name = "rspStatus")
    private String rspStatus;

    /**
     * 返回数据标签
     */
    @ApiModelProperty(value = "返回数据标签", name = "rspDataLabelIds")
    private List<String> rspDataLabelIds;

    /**
     * 国家
     */
    @ApiModelProperty(value = "国家", name = "country")
    private String country;

    /**
     * 省
     */
    @ApiModelProperty(value = "省", name = "province")
    private String province;

    /**
     * 市
     */
    @ApiModelProperty(value = "市", name = "city")
    private String city;

    /**
     * 攻击结果
     */
    @ApiModelProperty(value = "攻击结果（0-登录失败，1-登录成功，2-未知）", name = "attackSuccess")
    private Integer attackSuccess;

    /**
     * 攻击方式
     */
    @ApiModelProperty(value = "攻击方式", name = "attackType")
    private String attackType;

    /**
     * 异常参数
     */
    @ApiModelProperty(value = "异常参数", name = "attackKeywords")
    private String attackKeywords;

    /**
     * get与post合并or搜索
     */
    @ApiModelProperty(value = "get与post合并or搜索", name = "reqParam")
    private String reqParam;

    /**
     * get与post合并or搜索
     */
    @ApiModelProperty(value = "get与post合并or搜索", name = "enumerateParaSigns")
    private String enumerateParaSigns;

    /**
     * get请求参数
     */
    @ApiModelProperty(value = "get请求参数", name = "getParam")
    private String getParam;

    /**
     * post请求参数
     */
    @ApiModelProperty(value = "post请求参数", name = "postParam")
    private String postParam;

    /**
     * 事件时间-start
     */
    @ApiModelProperty(value = "事件时间-start", name = "timestampStart")
    private Long timestampStart;

    /**
     * 事件时间-end
     */
    @ApiModelProperty(value = "事件时间-end", name = "timestampEnd")
    private Long timestampEnd;

    /**
     * 地域
     */
    @ApiModelProperty(value = "地域", name = "timestampEnd")
    private String location;

    /**
     * 页码
     */
    @ApiModelProperty(value = "页码", name = "page")
    private Integer page = 1;

    /**
     * 每页显示数量
     */
    @ApiModelProperty(value = "每页显示数量", name = "limit")
    private Integer limit = 10;

}