package com.quanzhi.auditapiv2.core.risk.entity.page;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @Author: HaoJun
 * @Date: 2020/12/31 2:16 下午
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class Page<T> {

    private long totalCount;

    private List<T> rows = Collections.emptyList();

    public static final <T, V> Page<T> convert(long total, List<V> items, Converter<T, V> converter) {
        List<T> contents = items.stream().map(converter::convert).collect(Collectors.toList());
        Page<T> page = new Page<>(total, contents);
        return page;
    }

    public final <V> Page<V> map(Converter<V, T> converter) {
        Page<V> page = new Page<>();
        page.setTotalCount(totalCount);
        if (rows == null) {
            page.setRows(Collections.emptyList());
        } else {
            page.setRows(rows.stream().map(converter::convert).collect(Collectors.toList()));
        }
        return page;
    }

    @FunctionalInterface
    public interface Converter<T, V> {
        T convert(V v);
    }

}
