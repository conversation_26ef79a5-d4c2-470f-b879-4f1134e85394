package com.quanzhi.auditapiv2.core.risk.entity;

import com.quanzhi.audit_core.common.model.ScopeMatchInfo;
import lombok.Data;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

/**
 * 《风险规则》
 *
 * <AUTHOR> [<EMAIL>]
 * @Project:
 * @Module ID:
 * @Comments:
 * @JDK version used:      <JDK1.8>
 * @since 2021-08-10-上午9:56:10
 */
@Data
public class RiskPolicy extends RiskPolicyV2 implements Serializable {

    private String eventRuleHash;

    private Map<String, List<Object>> autoUpdateRuleInfo;

    private ScopeMatchInfo scopeMatchInfo;

    private List<RiskPolicyAllowList> riskPolicyAllowLists;

}
