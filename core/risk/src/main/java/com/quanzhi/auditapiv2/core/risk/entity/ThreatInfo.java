package com.quanzhi.auditapiv2.core.risk.entity;

import com.quanzhi.audit_core.common.model.AccountInfo;
import com.quanzhi.metabase.core.model.ResourceEntity;
import lombok.Data;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

import java.util.List;
import java.util.Set;


/**
 * <AUTHOR>
 * @Date: 2021/8/11 16:04
 * @Description: 威胁信息
 */
@Data
public class ThreatInfo {

    private String id;
    /**
     * 威胁主体
     */
    private String threatEntity;

    private List<ResourceEntity.Node> nodes;
    /**
     * 逻辑删除标记
     */
    private Boolean delFlag = false;
    /**
     * 威胁类型
     */
    private String threatType;
    /**
     * 地域
     */
    private String location;
    /**
     * 所属网段
     */
    private List<String> networkSegment;
    /**
     * 风险数量
     */
    private Long riskNum;
    /**
     * 确认风险数量
     */
    private Long confirmRiskNum;
    /**
     * 风险名称
     */
    private List<String> riskNames;
    /**
     * 威胁标签
     */
    private List<String> threatLabels;
    /**
     * 首次发现时间
     */
    private Long firstTime;
    /**
     * 最近发现时间
     */
    private Long lastTime;
    /**
     * 更新时间
     */
    private Long updateTime;

    /**
     * 阻断状态
     */
    private Boolean blockFlag;
    /**
     * 威胁标签
     */
    private List<String> judgments;
    /**
     * 威胁状态
     */
    private Boolean isMalicious;
    /**
     * 威胁等级
     */
    private String severity;
    /**
     * 关联应用
     */
    private Set<String> appUriList;

    @Mapper
    public interface ThreatInfoMapper {

        ThreatInfo.ThreatInfoMapper INSTANCE = Mappers.getMapper(ThreatInfo.ThreatInfoMapper.class);

        @Mapping(target = "threatEntity", source = "ip")
        @Mapping(target = "riskNames", source = "ipLabels")
        ThreatInfo convert(ThreatIp threatIp);

        @Mapping(target = "threatEntity", source = "account")
        ThreatInfo convert(AccountInfo account);

    }

}
