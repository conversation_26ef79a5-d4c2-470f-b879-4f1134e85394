package com.quanzhi.auditapiv2.core.risk.repository;

import com.alibaba.fastjson.JSONObject;
import com.quanzhi.auditapiv2.common.dal.dao.base.IBaseDao;
import com.quanzhi.auditapiv2.common.dal.dto.common.CommonGroupDto;
import com.quanzhi.auditapiv2.core.risk.entity.RiskPolicy;
import com.quanzhi.auditapiv2.core.risk.entity.RiskPolicySearchDto;

import java.util.List;
import java.util.Map;

/**
 * 《风险规则持久层接口》
 *
 * <AUTHOR> [<EMAIL>]
 * @Project:
 * @Module ID:
 * @Comments:
 * @JDK version used:      <JDK1.8>
 * @since 2021-08-10-上午9:56:10
 */
public interface IRiskPolicyDao extends IBaseDao<RiskPolicy> {

    List<CommonGroupDto> groupByMap(Map<String, Object> map);

    void saveJson(JSONObject riskPolicy);

    String savePolicyReturnId(RiskPolicy riskPolicy);

    /**
     * 查询风险规则列表
     *
     * @param
     * @return
     * @Comments: <对此方法的描述，可以引用系统设计中的描述>
     * <AUTHOR> [<EMAIL>]
     * @since 2021-08-10 9:56
     */
    List<RiskPolicy> selectRiskPolicyList() throws Exception;

    List<RiskPolicy> getByGroup(String group);

    /**
     * 查询所有风险规则列表包括已删除的
     *
     * @param
     * @return
     * @Comments: <对此方法的描述，可以引用系统设计中的描述>
     * <AUTHOR> [<EMAIL>]
     * @since 2021-08-10 9:56
     */
    List<RiskPolicy> selectAllRiskPolicyWithDel() throws Exception;

    /**
     * id查询风险规则详情
     *
     * @param
     * @return
     * @Comments: <对此方法的描述，可以引用系统设计中的描述>
     * <AUTHOR> [<EMAIL>]
     * @since 2021-08-10 9:56
     */
    RiskPolicy selectRiskPolicyById(String id) throws Exception;

    /**
     * 插入风险政策
     *
     * @param riskPolicy 政策风险
     * @return {@link RiskPolicy}
     */
    void upsertRiskPolicy(RiskPolicy riskPolicy);

    Long count(RiskPolicySearchDto riskPolicySearchDto);

    Long totalCount();

    Long totalCount(RiskPolicySearchDto riskPolicySearchDto);

    List<RiskPolicy> selectRiskPolices(RiskPolicySearchDto riskPolicySearchDto);

    List<CommonGroupDto> groupRiskPolices(RiskPolicySearchDto riskPolicySearchDto);

    void updateRiskPolicyState(String policyId, Boolean enable);

    void deleteRiskPolicy(String policyId);

    Map<String, String> getRiskPolicyIdMap();

    /**
     * 获取所有风险策略，包含逻辑删除
     */
    List<RiskPolicy> listAllPolicies();
    List<RiskPolicy> selectRiskPolicyByIds(List<String> ids);
}
