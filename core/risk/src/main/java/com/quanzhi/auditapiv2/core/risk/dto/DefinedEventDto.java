package com.quanzhi.auditapiv2.core.risk.dto;

import com.quanzhi.auditapiv2.core.risk.entity.DefinedEvent;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

/**
 * 《风险事件CKDTO》
 *
 * <AUTHOR> [<EMAIL>]
 * @Project:
 * @Module ID:
 * @Comments:
 * @JDK version used:      <JDK1.8>
 * @since 2021-08-10-上午9:56:10
 */
@Data
@ApiModel
public class DefinedEventDto implements Serializable {

    /**
     * id
     */
    @ApiModelProperty(value = "id", name = "id")
    private String id;

    /**
     * 事件时间
     */
    @ApiModelProperty(value = "事件时间", name = "timestamp")
    private Long timestamp;

    /**
     * 日期，如********
     */
    @ApiModelProperty(value = "日期，如********", name = "date")
    private String date;


    @ApiModelProperty(value = "接口唯一标识", name = "uri")
    private String uri;

    /**
     * 去掉参数的接口URL
     */
    @ApiModelProperty(value = "去掉参数的接口URL", name = "apiUrl")
    private String apiUrl;

    /**
     * ip
     */
    @ApiModelProperty(value = "ip", name = "ip")
    private String ip;

    /**
     * 账号
     */
    @ApiModelProperty(value = "账号", name = "account")
    private String account;

    /**
     * 账号类型
     */
    @ApiModelProperty(value = "账号类型", name = "accountType")
    private String accountType;

    /**
     * 密码
     */
    @ApiModelProperty(value = "密码", name = "password")
    private String password;

    /**
     * User-Agent
     */
    @ApiModelProperty(value = "User-Agent", name = "ua")
    private String ua;

    /**
     * User-Agent类型
     */
    @ApiModelProperty(value = "User-Agent类型", name = "uaType")
    private String uaType;

    /**
     * REFERER
     */
    @ApiModelProperty(value = "referer", name = "referer")
    private String referer;

    /**
     * COOKIE 数据
     */
    @ApiModelProperty(value = "cookie", name = "cookie")
    private String cookie;

    /**
     * 去重返回数据量
     */
    @ApiModelProperty(value = "去重返回数据量", name = "rspLabelContentDistinctCount")
    private Long rspLabelContentDistinctCount;

    /**
     * 返回状态码
     */
    @ApiModelProperty(value = "返回状态码", name = "rspStatus")
    private String rspStatus;

    /**
     * 返回格式
     */
    @ApiModelProperty(value = "返回格式", name = "rspContentType")
    private String rspContentType;

    /**
     * 返回长度
     */
    @ApiModelProperty(value = "返回长度", name = "rspContentLength")
    private Integer rspContentLength;

    /**
     * 返回数据标签
     */
    @ApiModelProperty(value = "返回数据标签", name = "rspDataLabelIds")
    private List<String> rspDataLabelIds;

    /**
     * 返回数据标签内容
     */
    @ApiModelProperty(value = "返回数据标签内容", name = "labelValues")
    private List<DefinedEvent.LabelValue> labelValues;

    /**
     * 返回数据标签数量
     */
    @ApiModelProperty(value = "返回数据标签数量", name = "labelValues")
    private Map<String, Long> rspDataLabelCounts;

    /**
     * 登陆结果
     *
     * @see com.quanzhi.auditapiv2.core.risk.dto.common.GroupDataDto.LoginResultEnum
     */
    @ApiModelProperty(value = "登陆结果（LOGIN_SUCCESS-登录失败，LOGIN_FAIL-登录成功，UNKNOWN-未知）", name = "loginResult")
    private String loginResult;

    /**
     * 登陆结果名称
     */
    @ApiModelProperty(value = "登陆结果名称", name = "loginResultName")
    private String loginResultName;

    /**
     * 攻击结果
     *
     * @see com.quanzhi.auditapiv2.core.risk.dto.common.GroupDataDto.AttackSuccessEnum
     */
    @ApiModelProperty(value = "攻击结果（0-攻击失败，1-攻击成功，2-未知）", name = "attackSuccess")
    private Integer attackSuccess;

    @ApiModelProperty(value = "攻击方式", name = "attackType")
    private String attackType;

    /**
     * 攻击结果名称
     */
    @ApiModelProperty(value = "攻击结果名称", name = "attackSuccessName")
    private String attackSuccessName;

    /**
     * get请求参数
     */
    @ApiModelProperty(value = "get请求参数", name = "getParam")
    private String getParam;

    /**
     * post请求参数
     */
    @ApiModelProperty(value = "post请求参数", name = "postParam")
    private String postParam;

    /**
     * 分页页码
     */
    @ApiModelProperty(value = "分页页码", name = "enumerateParaSigns")
    private List<String> enumerateParaSigns;

    /**
     * payload
     */
    @ApiModelProperty(value = "payload", name = "attackKeywords")
    private List<String> attackKeywords;

    /**
     * 国家
     */
    @ApiModelProperty(value = "国家", name = "country")
    private String country;

    /**
     * 省
     */
    @ApiModelProperty(value = "省", name = "province")
    private String province;

    /**
     * 市
     */
    @ApiModelProperty(value = "市", name = "city")
    private String city;

    /**
     * 地域
     */
    @ApiModelProperty(value = "地域", name = "location")
    private String location;

    /**
     * 样例idID
     */
    @ApiModelProperty(value = "样例ID", name = "sampleId")
    private String sampleId;

    @ApiModelProperty(value = "短信个数", name = "messageCount")
    private Integer messageCount = 1;

    /**
     * 脱敏key
     */
    @ApiModelProperty(value = "脱敏key", name = "desensitizationKey")
    private String desensitizationKey;

    @Mapper
    public interface DefinedEventDtoMapper {

        DefinedEventDto.DefinedEventDtoMapper INSTANCE = Mappers.getMapper(DefinedEventDto.DefinedEventDtoMapper.class);

        DefinedEventDto convert(DefinedEvent definedEvent);
    }
}
