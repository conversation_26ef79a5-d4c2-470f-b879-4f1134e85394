package com.quanzhi.auditapiv2.core.risk.entity;

import com.quanzhi.auditapiv2.core.risk.dto.RiskPolicyAllowListDto;
import lombok.Data;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.io.Serializable;

/**
 * 《风险白名单》
 *
 * <AUTHOR> [<EMAIL>]
 * @Project:
 * @Module ID:
 * @Comments:
 * @JDK version used:      <JDK1.8>
 * @since 2021-08-10-上午9:56:10
 */
@Data
public class RiskPolicyAllowList extends com.quanzhi.audit_core.common.risk.PolicyAllowList implements Serializable {

    @Mapper
    public interface RiskPolicyAllowListMapper {

        RiskPolicyAllowList.RiskPolicyAllowListMapper INSTANCE = Mappers.getMapper(RiskPolicyAllowList.RiskPolicyAllowListMapper.class);
        RiskPolicyAllowList convert(RiskPolicyAllowListDto riskPolicyAllowListDto);
    }
}
