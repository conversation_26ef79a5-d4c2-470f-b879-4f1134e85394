package com.quanzhi.auditapiv2.core.risk.repository.aggRisk;

import com.quanzhi.auditapiv2.common.dal.entity.aggRisk.AggRiskOperationLog;

import java.util.List;

/**
 * @Author: yangzx
 * @Date: 2024/10/9 14:37
 */
public interface AggRiskOperationLogDao {

    void insertLog(AggRiskOperationLog aggRiskOperationLog);

    void batchInsertLog(List<AggRiskOperationLog> aggRiskOperationLogs);

    List<AggRiskOperationLog> listLog(String riskId);

}
