package com.quanzhi.auditapiv2.core.risk.dto;

import com.alibaba.fastjson.JSONObject;
import com.quanzhi.audit_core.common.risk.PolicySnapshot;
import com.quanzhi.auditapiv2.core.risk.dto.common.RiskDescribeDto;
import com.quanzhi.auditapiv2.core.risk.entity.RiskInfo;
import com.quanzhi.auditapiv2.core.risk.entity.RiskPolicy;
import com.quanzhi.metabase.core.model.ResourceEntity;
import com.quanzhi.metabase.core.model.http.HttpAppResource;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.io.Serializable;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 《异常事件DTO》
 *
 * <AUTHOR> [<EMAIL>]
 * @Project:
 * @Module ID:
 * @Comments:
 * @JDK version used:      <JDK1.8>
 * @since 2021-08-10-上午9:56:10
 */
@Data
@ApiModel
public class RiskInfoDto implements Serializable {

    @ApiModelProperty(value = "id", name = "id")
    private String id;

    /**
     * 阻断状态
     */
    @ApiModelProperty(value = "阻断状态", name = "blockFlag")
    private Boolean blockFlag;

    @ApiModelProperty(value = "节点信息", name = "nodes")
    private List<ResourceEntity.Node> nodes;

    @ApiModelProperty(value = "是否被清理过样例", name = "cleanSample")
    private boolean cleanSample;

    /**
     * 异常主体列表
     */
    @ApiModelProperty(value = "异常主体列表", name = "entities")
    private List<Entity> entities;

    @ApiModelProperty(value = "异常运营ID", name = "operationId")
    private String operationId;

    @ApiModelProperty(value = "操作人", name = "operator")
    private String operateName;

    /**
     * 影响面列表
     */
    @ApiModelProperty(value = "影响面列表", name = "channels")
    private List<Entity> channels;

    /**
     * 业务主体
     */
    @ApiModelProperty(value = "业务主体", name = "bizFields")
    private Map<String, List<String>> bizFields;

    /**
     * 所属部门
     */
    @ApiModelProperty(value = "所属部门", name = "departments")
    private List<HttpAppResource.Department> departments;

    /**
     * 异常等级
     *
     * @see RiskInfo.RiskLevelEnum
     */
    @ApiModelProperty(value = "异常等级（1-低异常，2-中异常，3-高异常）", name = "level")
    private Integer level;

    /**
     * 异常等级名称
     */
    @ApiModelProperty(value = "异常等级名称", name = "level")
    private String levelName;

    /**
     * 主体关联信息
     */
    private List<com.quanzhi.audit_core.common.risk.RiskInfo.Entity> relatedInfos;

    /**
     * 异常状态
     *
     * @see RiskInfo.RiskStateEnum
     */
    @ApiModelProperty(value = "异常状态（0-待确认，1-已忽略，2-已确认）", name = "state")
    private Integer state;

    /**
     * 异常状态名称
     */
    @ApiModelProperty(value = "异常状态名称", name = "stateName")
    private String stateName;

    /**
     * 异常日期
     */
    @ApiModelProperty(value = "异常日期", name = "date")
    private String date;

    /**
     * 首次发现时间
     */
    @ApiModelProperty(value = "首次发现时间", name = "firstTime")
    private Long firstTime;

    /**
     * 最近发现时间
     */
    @ApiModelProperty(value = "最近发现时间", name = "lastTime")
    private Long lastTime;

    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间", name = "createTime")
    private Long createTime;

    /**
     * 更新时间
     */
    @ApiModelProperty(value = "更新时间", name = "updateTime")
    private Long updateTime;

    /**
     * 处理建议
     */
    @ApiModelProperty(value = "处理建议", name = "remark")
    private String remark;

    private String suggest;

    @ApiModelProperty(value = "总结", name = "summarize")
    private String summarize;

    @ApiModelProperty(value = "修复建议", name = "repair")
    private String repair;

    /**
     * 线索
     */
    @ApiModelProperty(value = "线索", name = "clue")
    private Clue clue;

    /**
     * 异常快照
     */
    @ApiModelProperty(value = "异常快照", name = "policySnapshot")
    private PolicySnapshot policySnapshot;

    /**
     * 异常标识 true:已标识
     */
    @ApiModelProperty(value = "异常标识", name = "riskMark")
    private Boolean riskMark;

    /**
     * 异常描述
     */
    @ApiModelProperty(value = "异常描述", name = "riskDescribe")
    private RiskDescribeDto riskDescribe;

    /**
     * 异常描述-String
     */
    @ApiModelProperty(value = "异常描述-String", name = "riskDesc")
    private String riskDesc;

    /**
     * 忽略类型
     *
     * @see com.quanzhi.auditapiv2.core.risk.entity.RiskInfo.IgnoreTypeEnum
     */
    @ApiModelProperty(value = "忽略类型（IP，API，IP_HOST，IP_API）", name = "ingoreType")
    private String ignoreType;

    /**
     * 白名单类型
     *
     * @see com.quanzhi.audit_core.common.risk.Policy.EntityEnum
     */
    @ApiModelProperty(value = "白名单类型（IP，ACCOUNT，APP，API）", name = "whiteTypeList")
    private List<String> whiteTypeList;

    /**
     * 攻击结果
     */
    @ApiModelProperty(value = "攻击结果", name = "attackSuccess")
    private Integer attackSuccess;

    /**
     * 攻击结果名称
     */
    @ApiModelProperty(value = "攻击结果名称", name = "attackSuccessName")
    private String attackSuccessName;

    /**
     * 攻击次数
     */
    @ApiModelProperty(value = "攻击次数", name = "attackCount")
    private Long attackCount;

    /**
     * 返回数据标签
     */
    @ApiModelProperty(value = "返回数据标签", name = "rspLabelList")
    private List<String> rspLabelList;

    @ApiModelProperty(value = "返回数据标签长度", name = "rspLabelListCount")
    private Long rspLabelListCount;

    /**
     * 返回数据标签内容
     */
    @ApiModelProperty(value = "返回数据标签内容", name = "rspLabelListValue")
    private List<String> rspLabelListValue;

    /**
     * 去重返回数据量
     */
    @ApiModelProperty(value = "去重返回数据量", name = "rspDataDistinctCnt")
    private Long rspDataDistinctCnt;

    /**
     * 涉及数据量
     */
    @ApiModelProperty(value = "去重返回数据量", name = "dataCnt")
    private Integer dataCnt;

    /**
     * 异常描述数据集
     */
    @ApiModelProperty(value = "异常描述数据集", name = "description")
    private JSONObject description;

//    @ApiModelProperty(value = "修复建议", name = "suggestion")
//    private String suggestion;

    @ApiModelProperty(value = "分析总结", name = "conclusion")
    private String conclusion;

    /**
     * 异常版本
     */
    @ApiModelProperty(value = "异常版本", name = "version")
    private Integer version;

    /**
     * 工单状态
     */
    @ApiModelProperty(value = "工单状态", name = "orderFlag")
    private Integer orderFlag;

    /**
     * 工单状态名称
     */
    @ApiModelProperty(value = "工单状态名称", name = "orderFlagName")
    private String orderFlagName;

    /**
     * 威胁状态
     */
    @ApiModelProperty(value = "威胁状态", name = "threatState")
    private String threatState;

    /**
     * 威胁标签
     */
    @ApiModelProperty(value = "威胁标签", name = "threatLabels")
    private List<String> threatLabels;


    //  ================================
    /**
     * 异常发现时间的时间戳
     */
    private Long findTimeMs;

    /**
     * 异常状态
     */
    private String status;

    /**
     * 异常策略id
     */
    private String riskPolicyId;

    /**
     * 异常策略名称
     */
    private String riskPolicyName;

    /**
     * 异常策略类型
     */
    private String riskPolicyGroup;

    /**
     * 主体类型
     */
    private String entityType;

    /**
     * 威胁主体
     */
    private String entityValue;

    /**
     * 主体类型为IP时对应的网段
     */
    private List<String> domainIds;

    /**
     * 主体类型为账号时，对应解析出来账号的部门信息
     */
    private String depart;

    /**
     * 异常id
     */
    private String riskId;

    /**
     * 最大去重数据量
     */
    private Long distinctSensiValueCnt;

    /**
     * 操作时间
     */
    private Long operateTime;

    // --------------------------------------
    private Long eventCnt;

    private Long dataDistinctCnt;

    private Long maxVisitCntPerMin;

    private List<DataLabelDistinct> dataDistinctCntByDataLabel;

    private String appName;

    private String host;

    private String apiUri;

    private String apiUrl;

    private String appUri;

    private String ip;

    /**
     * 样例数量
     */
    @ApiModelProperty(value = "样例数量", name = "sampleCount")
    private Integer sampleCount;

    @Data
    public static class DataLabelDistinct {
        private String dataLabelId;

        private String dataLabelName;

        private Long dataDistinctCnt;
    }
    // =====================================

    public static final DateTimeFormatter FORMATTER = DateTimeFormatter.ofPattern("yyyyMMdd");

    @Data
    public static class Entity {

        /**
         * 主体类型
         *
         * @see RiskPolicy.EntityEnum
         */
        @ApiModelProperty(value = "主体类型", name = "type")
        private String type;

        private String name;

        /**
         * 值
         */
        @ApiModelProperty(value = "值", name = "value")
        private String value;
    }

    /**
     * 线索
     */
    @Data
    public static class Clue {

        /**
         * 单事件类异常线索
         */
        @ApiModelProperty(value = "单事件类异常线索", name = "event")
        private Event event;

        /**
         * 统计类异常线索
         */
        @ApiModelProperty(value = "统计类异常线索", name = "stat")
        private Stat stat;

        private Integer version;

        @Data
        public static class Event {

            /**
             * 事件ID
             */
            @ApiModelProperty(value = "事件ID", name = "eventIds")
            private Set<String> eventIds;
        }

        @Data
        public static class Stat {

            /**
             * 时间戳
             */
            @ApiModelProperty(value = "时间戳", name = "tsSet")
            private Set<Long> tsSet;

        }

    }

    @Mapper
    public interface RiskInfoDtoMapper {

        RiskInfoDto.RiskInfoDtoMapper INSTANCE = Mappers.getMapper(RiskInfoDto.RiskInfoDtoMapper.class);

        RiskInfoDto convert(RiskInfo riskInfo);
    }
}
