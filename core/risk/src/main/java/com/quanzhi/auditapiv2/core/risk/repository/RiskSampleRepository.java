package com.quanzhi.auditapiv2.core.risk.repository;

import com.quanzhi.audit_core.common.model.HttpEvent;
import com.quanzhi.auditapiv2.common.dal.base.Sort;
import com.quanzhi.auditapiv2.common.dal.dao.base.IBaseDao;
import com.quanzhi.auditapiv2.core.risk.dto.common.RiskIpJoinAccountDto;
import com.quanzhi.auditapiv2.core.risk.dto.search.DefinedEventSearchDto;
import com.quanzhi.auditapiv2.core.risk.entity.DefinedEvent;
import com.quanzhi.auditapiv2.core.risk.entity.RiskSample;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;

import java.util.List;

/**
 * @Author: HaoJun
 * @Date: 2021/8/17 3:36 下午
 */
public interface RiskSampleRepository extends IBaseDao<RiskSample> {

    DefinedEvent convert(HttpEvent httpEvent);

    List<RiskSample> findBy(Criteria criteria, int page, int size, Sort sort);

    List<RiskSample> findByQuery(Query query);

    List<RiskSample> getRiskSample(Criteria criteria);

    List<RiskSample> getRiskSampleByLimit(Criteria criteria, Integer limit);

    Long totalCount(Criteria criteria);

    void updateRiskSampleLiveByRiskIds(List<String> riskIds);

    void updateRiskSampleLiveByRiskId(String riskId);

    Long deleteRiskSampleUnLive();

    List<RiskIpJoinAccountDto> selectIpJoinAccountList(DefinedEventSearchDto definedEventSearchDto);

    String findBySampleId(String sampleId);

    Long countIpJoinAccount(DefinedEventSearchDto definedEventSearchDto);

    RiskSample getRiskLastSample(String riskId);

    RiskSample getAggRiskLastSample(String aggRiskId);

    List<RiskSample> getRiskListSample(String riskId, int size);

    Long countDistinctLabel(String riskId);

    Long countDataCnt(String riskId);

    List<RiskSample> listSampleByAggRiskId(String aggRiskId);

}
