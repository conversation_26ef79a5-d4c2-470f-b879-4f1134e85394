package com.quanzhi.auditapiv2.core.risk.entity;

import lombok.Data;

/**
 * <AUTHOR>
 * create at 2022/8/30 10:42 上午
 * @description: 风险ip与账号关联表
 **/
@Data
public class RiskIpAccountInfo {

    private String id;
    /**
     * 事件id
     */
    private String eventId;
    /**
     * 对应ip
     */
    private String ip;
    /**
     * 账号
     */
    private String account;
    /**
     * 接口url
     */
    private String apiUrl;
    /**
     * uri
     */
    private String uri;
    /**
     * 登录信息
     */
    private String loginInfo;
    /**
     * 登录结果
     */
    private String loginResult;
    /**
     * 事件时间戳
     */
    private Long timestamp;
    /**
     * 日期 eg:2022-09-08
     */
    private String date;
    /**
     * 入库时间
     */
    private Long createTime;
}