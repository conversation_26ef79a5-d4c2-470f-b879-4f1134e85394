package com.quanzhi.auditapiv2.core.risk.dto.search;

import com.quanzhi.auditapiv2.core.risk.dto.common.CommonSearchDto;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * @author: yangzixian
 * @date: 1/3/2023 17:55
 * @description:
 */
@Data
@ApiModel
public class ThreatInfoSearchDto extends CommonSearchDto {

    private String id;

    @ApiModelProperty(value = "威胁类型", name = "threatType")
    private String threatType;

    @ApiModelProperty(value = "逻辑删除", name = "delFlag")
    private Boolean delFlag = false;

    @ApiModelProperty(value = "威胁主体", name = "threatEntity")
    private String threatEntity;

    @ApiModelProperty(value = "地域", name = "location")
    private String location;

    @ApiModelProperty(value = "所属网段", name = "networkSegment")
    private List<String> networkSegment;

    @ApiModelProperty(value = "风险数量", name = "riskNum")
    private Long riskNum;

    @ApiModelProperty(value = "确认风险数量", name = "confirmRiskNum")
    private Long confirmRiskNum;

    @ApiModelProperty(value = "风险名称", name = "riskNames")
    private List<String> riskNames;

    @ApiModelProperty(value = "威胁标签", name = "threatLabels")
    private List<String> threatLabels;

    @ApiModelProperty(value = "首次发现时间开始", name = "firstTimeStart")
    private Long firstTimeStart;

    @ApiModelProperty(value = "首次发现时间结束", name = "firstTimeEnd")
    private Long firstTimeEnd;

    @ApiModelProperty(value = "最近发现时间开始", name = "lastTimeStart")
    private Long lastTimeStart;

    @ApiModelProperty(value = "最近发现时间结束", name = "lastTimeEnd")
    private Long lastTimeEnd;

    @ApiModelProperty(value = "字段筛选条件map", name = "fieldOperateMap")
    private Map<String, String> fieldOperateMap;

    @ApiModelProperty(value = "威胁标签", name = "judgments")
    private List<String> judgments;

    @ApiModelProperty(value = "威胁状态", name = "isMalicious")
    private Boolean isMalicious;

    @ApiModelProperty(value = "威胁等级", name = "severity")
    private String severity;

    @ApiModelProperty("权限条件")
    private Map<String,Object> dataPermissionMap;
}
