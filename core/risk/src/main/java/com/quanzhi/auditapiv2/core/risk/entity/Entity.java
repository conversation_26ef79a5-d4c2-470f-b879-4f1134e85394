package com.quanzhi.auditapiv2.core.risk.entity;

import lombok.Data;

/**
 * @Author: yangzx
 * @Date: 2024/10/31 10:16
 */
@Data
public class Entity {

    /**
     * 主体类型
     *
     * @see EntityEnum
     */
    private String type;

    private String name;

    /**
     * 主体取值
     * <p>
     * 1. 主体类型是数据标签时，value 存选择的数据标签 ID
     * 2. 非 1 时，存决策引擎中的 feature（包含事件上的主体，以及未来将自定义变量声明为主体时的情况）
     * </p>
     */
    private String value;

    /**
     * 主体类型
     *
     * @desc 这里只包含内置的主体，动态声明的自定义主体并不在此处
     * @since 3.3
     */
    public enum EntityEnum {
        /**
         * 应用
         */
        APP,
        /**
         * API
         */
        API,
        /**
         * 账号
         */
        ACCOUNT,
        /**
         * session
         */
        SESSION,
        /**
         * IP
         */
        IP,
        /**
         * 请求数据标签
         */
        REQ_DATALABEL,
        /**
         * 响应数据标签
         */
        RSP_DATALABEL;
    }

}
