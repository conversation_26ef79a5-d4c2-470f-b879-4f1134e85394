package com.quanzhi.auditapiv2.core.risk.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * create at 2021/12/10 4:24 下午
 * @description: 风险操作日志
 **/
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class RiskOperationLog {

    /**
     * 风险事件id
     */
    private String riskId;

    private Long createTime;

    /**
     * 具体操作日志
     */
    private String detailLog;

    /**
     * 具体操作人员名称
     */
    private String operatorName;

}