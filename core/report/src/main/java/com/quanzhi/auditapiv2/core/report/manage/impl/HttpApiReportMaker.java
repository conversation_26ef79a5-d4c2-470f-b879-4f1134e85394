package com.quanzhi.auditapiv2.core.report.manage.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.nacos.api.annotation.NacosInjected;
import com.alibaba.nacos.api.config.ConfigService;
import com.alibaba.nacos.api.config.annotation.NacosConfigListener;
import com.alibaba.nacos.api.config.annotation.NacosValue;
import com.mongodb.client.MongoCursor;
import com.quanzhi.audit_core.common.model.AssetLifeStateConfig;
import com.quanzhi.auditapiv2.common.dal.dao.convert.AssetLifeStateConfigConvert;
import com.quanzhi.auditapiv2.common.dal.entity.NacosBaseConfig;
import com.quanzhi.auditapiv2.common.dal.enums.NacosConfigEnum;
import com.quanzhi.auditapiv2.core.report.manage.IHttpApiReportMaker;
import com.quanzhi.auditapiv2.core.report.manage.ISampleEventReportMaker;
import com.quanzhi.auditapiv2.core.report.manage.IWeaknessReportMaker;
import com.quanzhi.auditapiv2.core.report.manage.model.ReportSearchDto;
import com.quanzhi.auditapiv2.core.report.manage.model.ReportTask;
import com.quanzhi.metabase.core.model.http.constant.AssetLifeStateEnum;
import com.quanzhi.metabase.core.model.query.Sort;
import com.quanzhi.metabase.core.model.query.SortOrder;
import com.quanzhi.operate.localMethodOperate.localMethodBuildIn.ILocalMethodTransform;
import com.quanzhi.operate.query.Criteria;
import com.quanzhi.operate.query.MetabaseQuery;
import com.quanzhi.operate.query.Predicate;
import com.quanzhi.operate.repositoryTemplate.mongo.IBaseMongoTemplate;
import com.quanzhi.operate.utils.DataUtil;
import com.quanzhi.operate.utils.MD5Util;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FileUtils;
import org.bson.Document;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.io.File;
import java.io.FileWriter;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2021/2/22 下午4:27
 */
@Slf4j
@Component
public class HttpApiReportMaker implements IHttpApiReportMaker {


    @NacosValue( value = "${auditapiv.report.save.path:/Users/<USER>/Documents/aaa}" ,autoRefreshed = true)
    private String reportPath;

    @NacosValue( value = "${auditapiv.report.template.path:/Users/<USER>/Documents/aaa/template-bak}" ,autoRefreshed = true)
    private String templatePath = "/home/<USER>/audit-api/template-bak";

    @NacosValue( value = "${auditapiv.offlineReport.max.limit:1000}" ,autoRefreshed = true)
    private Integer MAX_PULL_LIMIT;

    private IBaseMongoTemplate baseMongoTemplate;

    private ISampleEventReportMaker sampleEventReportMaker;

    private IWeaknessReportMaker weaknessReportMaker;

    private AssetLifeStateConfigConvert assetLifeStateConfigConvert;

    private ILocalMethodTransform localMethodTransform;

    public HttpApiReportMaker (IBaseMongoTemplate baseMongoTemplate,
                               ISampleEventReportMaker sampleEventReportMaker,
                               IWeaknessReportMaker weaknessReportMaker,
                               AssetLifeStateConfigConvert assetLifeStateConfigConvert,
                               ILocalMethodTransform localMethodTransform) {

        this.baseMongoTemplate = baseMongoTemplate;
        this.sampleEventReportMaker = sampleEventReportMaker;
        this.weaknessReportMaker = weaknessReportMaker;
        this.assetLifeStateConfigConvert = assetLifeStateConfigConvert;
        this.localMethodTransform = localMethodTransform;
    }

    @NacosInjected
    private ConfigService configService;

    private List<Object> dataLabelList = new ArrayList<>();

    private Map<String,String> dataLabelMap = new HashMap<>();

    /**
     * 样例表
     */
    private String sampleCollection = "httpSample";

    @PostConstruct
    public void init() {

        try {
            NacosBaseConfig nacosBaseConfig = NacosConfigEnum.DATA_LABEL.getNacosBaseConfig();
            String dataId = nacosBaseConfig.getDataId();
            String group = nacosBaseConfig.getGroup();
            Class clazz = nacosBaseConfig.getClazz();

            String content = configService.getConfig(dataId, group, 3000);

            dataLabelList = JSONArray.parseArray(content);

            dataLabelList.stream().forEach(item ->{
                dataLabelMap.put(((JSONObject) item).getString("id"),((JSONObject) item).getString("name"));
            });

        } catch (Exception e) {

        }
    }

    @NacosConfigListener(dataId ="common.datalabel.json",groupId = "common", timeout = 30000)
    public void onMessage(String msg) {
        dataLabelList = JSONArray.parseArray(msg);

        dataLabelList.stream().forEach(item ->{
            dataLabelMap.put(((JSONObject) item).getString("id"),((JSONObject) item).getString("name"));
        });
    }

    @Override
    public void getReport(ReportTask reportTask,ReportSearchDto reportSearchDto, String taskId,Map<String,List<Object>> reportExportData) throws Exception{

        log.info("MAX_PULL_LIMIT is {}",MAX_PULL_LIMIT);

        if (!reportSearchDto.getReportDataResource().equals(ReportSearchDto.ReportDataResource.HTTP_API )) return;

        //应用数据
        Map<String,Map<String,Object>> hostMap = new HashMap<>();

        //应用接口数据
        Map<String,List<Object>> hostApiMap = new HashMap<>();

        //所有的接口数据
        List<JSONObject> apiDataList = new ArrayList<>();

        MetabaseQuery metabaseQuery = new MetabaseQuery();

        List<Criteria> frontCriteriaList = new ArrayList<>();

        //特殊字符进行转义
        if(DataUtil.isNotEmpty( reportSearchDto.getFrontCriteriaList() )) {

            for(Criteria criteria : reportSearchDto.getFrontCriteriaList()) {

                if( (criteria.getValue() != null && criteria.getValue() instanceof String) && (criteria.getPredicate() != null && criteria.getPredicate().equals(Predicate.REGEX))  ) {
                    criteria.setValue( DataUtil.regexStrEscape( (String) criteria.getValue() ) );
                }
                if(!"apiLifeFlag".equals(criteria.getProperty())){
                    frontCriteriaList.add(criteria);
                }
            }
        }

        Optional<Criteria> optionalCriteria = reportSearchDto.getFrontCriteriaList().stream().filter(frontCriteria -> "apiLifeFlag".equals(frontCriteria.getProperty())).findAny();
        if (optionalCriteria.isPresent()) {
            Criteria criteria = optionalCriteria.get();
            Integer lifeFlag = null;
            Object value = criteria.getValue();
            if (value instanceof List) {
                lifeFlag = ((List<Integer>) value).get(0);
            }
            AssetLifeStateEnum assetLifeStateEnum = AssetLifeStateEnum.valueOfNum(Short.valueOf(lifeFlag.toString()));
            AssetLifeStateConfigConvert.CriteriaTag criteriaTag = assetLifeStateConfigConvert.getCriteria(AssetLifeStateConfig.AssetTypeEnum.API, assetLifeStateEnum);
            com.quanzhi.metabase.core.model.query.Criteria metabaseCriteria = criteriaTag.getMetabaseCriteria();
            com.quanzhi.metabase.core.model.query.Criteria finalCriteria = metabaseCriteria.getCriteriaChain().get(0);
            Criteria criteriaApiLifeFlag = new Criteria();
            criteriaApiLifeFlag.setProperty(finalCriteria.getProperty());
            com.quanzhi.metabase.core.model.query.Predicate predicate = finalCriteria.getPredicate();
            criteriaApiLifeFlag.setPredicate(Predicate.valueOf(predicate.toString()));
            criteriaApiLifeFlag.setValue(finalCriteria.getValue());
            frontCriteriaList.add(criteriaApiLifeFlag);
        }
        if(DataUtil.isNotEmpty(frontCriteriaList)) {
            metabaseQuery.setCriteria(frontCriteriaList);
        }
        metabaseQuery.where("delFlag",Predicate.IS,false);

        MongoCursor<Document> cursor = baseMongoTemplate.getCursor(metabaseQuery,500,MAX_PULL_LIMIT,reportSearchDto.getReportDataResource().value());

        try {

            while (cursor.hasNext()) {

                JSONObject resourceData = JSON.parseObject(JSON.toJSONString(cursor.next()));

                String host = resourceData.getString("host");

                if(!hostMap.containsKey(host)) {

                    Map<String,Object> hostInfo = new HashMap<>();

                    hostInfo.put("host",host);
                    hostInfo.put("apiCount",1);
                    hostInfo.put("monitorApiCount",
                            Optional.ofNullable(resourceData.getJSONObject("storeMonitor"))
                                    .map(s -> ((JSONObject) s).getInteger("monitorFlag")).orElse(0) == 1 ? 1 :0 );

                    hostInfo.put("riskCount",new HashMap<String,Integer>(){
                        {
                            put("total",0);
                            put("h",0);
                            put("m",0);
                            put("l",0);
                        }
                    });


                    hostInfo.put("monitorText",Optional.ofNullable(resourceData.getJSONObject("storeMonitor"))
                            .map(s -> ((JSONObject) s).getInteger("monitorFlag")).orElse(0) == 1 ? "是" : "否");

                    hostInfo.put("srcIpLabel", new HashSet(  Optional.ofNullable(resourceData.getJSONArray("visitDomains")).orElse(new JSONArray()) ) );
                    hostInfo.put("uaLabel", new HashSet(  Optional.ofNullable(resourceData.getJSONArray("terminals")).orElse(new JSONArray()) ));
                    hostInfo.put("dataLabels", new HashSet(  Optional.ofNullable(resourceData.getJSONArray("dataLabels")).orElse(new JSONArray()) ));

                    hostInfo.put("maxRspLabelValueCount", Optional.ofNullable(resourceData.getJSONObject("apiStat")).map(c -> c.getInteger("maxReqLabelValueCount")).orElse(0) );
                    hostInfo.put("maxRspLabelSubCount",Optional.ofNullable(resourceData.getJSONObject("apiStat")).map(c -> c.getInteger("maxReqDupLabelCount")).orElse(0));


                    hostMap.put(host,hostInfo);

                } else {
                    Map<String,Object> hostInfo = hostMap.get(host);

                    hostInfo.put("apiCount", ((Integer) hostInfo.get("apiCount")) + 1);

                    if(Optional.ofNullable(resourceData.getJSONObject("storeMonitor"))
                            .map(s -> ((JSONObject) s).getInteger("monitorFlag")).orElse(0) == 1) {

                        hostInfo.put("monitorApiCount",((Integer) hostInfo.get("monitorApiCount")) + 1);
                    }

                    if(Optional.ofNullable(resourceData.getJSONObject("storeMonitor"))
                            .map(s -> ((JSONObject) s).getInteger("monitorFlag")).orElse(0) == 1) {
                        hostInfo.put("monitorText","是");
                    }
                    ((HashSet) hostInfo.get("srcIpLabel")).addAll( new HashSet(  Optional.ofNullable(resourceData.getJSONArray("visitDomains")).orElse(new JSONArray()) ));

                    ((HashSet) hostInfo.get("uaLabel")).addAll( new HashSet(  Optional.ofNullable(resourceData.getJSONArray("terminals")).orElse(new JSONArray()) ));

                    ((HashSet) hostInfo.get("dataLabels")).addAll( new HashSet(  Optional.ofNullable(resourceData.getJSONArray("dataLabels")).orElse(new JSONArray()) ));

                    if( Optional.ofNullable(resourceData.getJSONObject("apiStat")).map(c -> c.getInteger("maxReqLabelValueCount")).orElse(0) > ((Integer) hostInfo.get("maxRspLabelValueCount"))) {

                        hostInfo.put("maxRspLabelValueCount", Optional.ofNullable(resourceData.getJSONObject("apiStat")).map(c -> c.getInteger("maxReqLabelValueCount")).orElse(0));
                    }

                    if( Optional.ofNullable(resourceData.getJSONObject("apiStat")).map(c -> c.getInteger("maxReqDupLabelCount")).orElse(0) > ((Integer) hostInfo.get("maxRspLabelSubCount"))) {

                        hostInfo.put("maxRspLabelSubCount",Optional.ofNullable(resourceData.getJSONObject("apiStat")).map(c -> c.getInteger("maxReqDupLabelCount")).orElse(0));
                    }

                }

                if(reportTask.containsHtmlExportType()) {
                    resourceData.put("featureLabelsValue",localMethodTransform.transformFeatureLabelId2name(resourceData.get("featureLabels")) != null ? localMethodTransform.transformFeatureLabelId2name(resourceData.get("featureLabels")) : "");
                    resourceData.put("reqDataLabelsValue",
                            Optional.ofNullable(resourceData.getJSONArray("reqDataLabels")).orElse(new JSONArray())
                                    .stream().map(i -> dataLabelMap.containsKey(i) ? dataLabelMap.get(i) : i).collect(Collectors.toList())
                    );
                    resourceData.put("rspDataLabelsValue",
                            Optional.ofNullable(resourceData.getJSONArray("rspDataLabels")).orElse(new JSONArray())
                                    .stream().map(i -> dataLabelMap.containsKey(i ) ? dataLabelMap.get(i) : i).collect(Collectors.toList())
                    );
                    this.getApiDetailData(resourceData,taskId);
                }

                if(!hostApiMap.containsKey(host)) {

                    hostApiMap.put(host, new ArrayList<Object>(){
                        {
                            add( convertHttpApi(resourceData));
                        }
                    });
                } else {

                    hostApiMap.get(host).add( convertHttpApi(resourceData));
                }

                apiDataList.add( convertHttpApi(resourceData) );

            }

        } catch (Exception e) {

            throw new Exception(e);

        } finally {

            cursor.close();
        }

        List<Map<String,Object>> hostDataList = hostMap.values().stream().collect(Collectors.toList());


        hostDataList = hostDataList.stream().map(h -> {


            if ( h.get("dataLabels") != null) {

                List<String> dataLabels = JSON.parseArray(JSON.toJSONString(h.get("dataLabels") ),String.class) ;

                dataLabels = dataLabels.stream().map(i -> dataLabelMap.containsKey(i ) ? dataLabelMap.get(i) : i).collect(Collectors.toList());

                h.put("dataLabels", dataLabels);
            }

            return h;

        }).collect(Collectors.toList());

        apiDataList.stream().forEach(api->{
            if(api.getString("uri").indexOf("@QZKJMGC@") != -1) {
                api.put("uri",api.getString("uri").substring(0,api.getString("uri").indexOf("@QZKJMGC@")));
            }
        });

        for(Map<String,Object> hostData : hostDataList) {
            try {
                String host = (String) hostData.get("host");
                MetabaseQuery query = new MetabaseQuery();
                query.where("host", Predicate.IS, host);
                List<Map> weaknessList = weaknessReportMaker.getWeaknessList(reportTask, query, taskId);
                Long lowCnt = weaknessList.stream().filter(i -> i.get("level").toString().equals("1")).count();
                Long middleCnt = weaknessList.stream().filter(i -> i.get("level").toString().equals("2")).count();
                Long highCnt = weaknessList.stream().filter(i -> i.get("level").toString().equals("3")).count();
                ((Map) hostData.get("riskCount")).put("total", weaknessList.size());
                ((Map) hostData.get("riskCount")).put("l", lowCnt);
                ((Map) hostData.get("riskCount")).put("m", middleCnt);
                ((Map) hostData.get("riskCount")).put("h", highCnt);
            } catch (Exception e) {
                log.error("获取应用下弱点数量error:{}",e);
            }
        }
        reportExportData.put("hostList",JSON.parseArray(JSON.toJSONString(hostDataList)));
        reportExportData.put("apiList",JSON.parseArray(JSON.toJSONString(apiDataList)));

        //导出有html格式
        if(reportTask.containsHtmlExportType()) {

            this.makeHostWeaknessHtmlReport(reportTask,hostDataList,taskId);

            this.makeHostHtmlReport(hostDataList,taskId);

            this.makeHostApiHtmlReport(hostApiMap,taskId);
        }
    }

    /**
     * 接口数据转换
     * @param resourceData
     * @return
     */
    private JSONObject convertHttpApi(JSONObject resourceData) {

        resourceData.put("monitorText",Optional.ofNullable(resourceData.getJSONObject("storeMonitor"))
                .map(s -> ((JSONObject) s).getInteger("monitorFlag")).orElse(0) == 1 ? "是" : "否");

        resourceData.put("reqDataLabels",
                Optional.ofNullable(resourceData.getJSONArray("reqDataLabels")).orElse(new JSONArray())
                .stream().map(i -> dataLabelMap.containsKey(i ) ? dataLabelMap.get(i) : i).collect(Collectors.toList())
        );
        resourceData.put("rspDataLabels",
                Optional.ofNullable(resourceData.getJSONArray("rspDataLabels")).orElse(new JSONArray())
                        .stream().map(i -> dataLabelMap.containsKey(i ) ? dataLabelMap.get(i) : i).collect(Collectors.toList())
        );

        return resourceData;
    }

    /**
     * 生成接口详情页面
     */
    private void getApiDetailData(JSONObject resourceData,String taskId) {

        List<String> sampleIds = new ArrayList<>();

        String uri = resourceData.getString("uri");

        resourceData.put("uriMd5",MD5Util.md5(resourceData.getString("uri")));

        MetabaseQuery metabaseQuery = new MetabaseQuery();

        metabaseQuery.setSort(new Sort[]{Sort.by("dataLabelCount", SortOrder.DESC)});
        metabaseQuery.where("uri", Predicate.IS, uri);

        JSONObject sampleInfo = JSON.parseObject(JSON.toJSONString(baseMongoTemplate.findOne(metabaseQuery, sampleCollection)));

        if(sampleInfo != null) {

            sampleEventReportMaker.formatSampleEventInfo(sampleInfo);

            resourceData.put("dataLabelValueMax", new HashMap<String,Object>() {{

                put("eventId", sampleInfo.getString("_id"));
                put("get", sampleInfo.getJSONObject("labelValue").getJSONObject("get"));
                put("post", sampleInfo.getJSONObject("labelValue").getJSONObject("post"));
                put("rsp", sampleInfo.getJSONObject("labelValue").getJSONObject("rsp"));

            }});

            sampleIds.add(sampleInfo.getString("_id"));
        }

        MetabaseQuery dataLabelGroupMetabaseQuery = new MetabaseQuery();

        dataLabelGroupMetabaseQuery.where("uri",Predicate.IS,uri);
        dataLabelGroupMetabaseQuery.setSkip(0);
        dataLabelGroupMetabaseQuery.setLimit(100);
        List<Object> sampleList = baseMongoTemplate.find(metabaseQuery, sampleCollection);

        Map<String,Object> dataLabelGroup = new HashMap<>();

        Map<String,Set<TreeSet<String>>> map = new HashMap<>();

        Set<TreeSet<String>> dataLabelSets = new HashSet<>();

        if(sampleList != null && sampleList.size() > 0) {

            for (Object sampleEvent : sampleList){


                JSONObject jsonObject = JSON.parseObject(JSON.toJSONString(sampleEvent)) ;
                sampleEventReportMaker.formatSampleEventInfo(jsonObject);

                TreeSet<String> dataIds = new TreeSet<>();

                if(DataUtil.isNotEmpty( jsonObject.getJSONObject("labelValue").getJSONObject("get") )) {


                    jsonObject.getJSONObject("labelValue").getJSONObject("get").keySet().forEach(i ->{
                        dataIds.add("get_" + i);
                    });
                }

                if(DataUtil.isNotEmpty( jsonObject.getJSONObject("labelValue").getJSONObject("post") )) {

                    jsonObject.getJSONObject("labelValue").getJSONObject("post").keySet().forEach(i ->{
                        dataIds.add("post_" + i);
                    });
                }

                if(DataUtil.isNotEmpty( jsonObject.getJSONObject("labelValue").getJSONObject("rsp") )) {

                    jsonObject.getJSONObject("labelValue").getJSONObject("rsp").keySet().forEach(i ->{
                        dataIds.add("rsp_" + i);
                    });
                }


                if(!dataLabelSets.contains( dataIds )) {

                    dataLabelSets.add(dataIds);

                    dataLabelGroup.put(jsonObject.getString("_id"),new HashMap<String,Object>() {{

                        put("eventId", jsonObject.getString("_id"));
                        put("get", jsonObject.getJSONObject("labelValue").getJSONObject("get"));
                        put("post", jsonObject.getJSONObject("labelValue").getJSONObject("post"));
                        put("rsp", jsonObject.getJSONObject("labelValue").getJSONObject("rsp"));

                    }});

                    sampleIds.add(jsonObject.getString("_id"));
                }
            }
        }

        resourceData.put("dataLabelGroup",dataLabelGroup);

        this.makeApiDetailHtmlReport(resourceData,taskId);

        if(sampleIds != null && sampleIds.size() > 0) {
            sampleEventReportMaker.makeSampleReportByIds(sampleIds,taskId);
        }
    }

    private void makeApiDetailHtmlReport(JSONObject resourceData,String taskId) {

        if(resourceData.getString("uri").indexOf("@QZKJMGC@") != -1){
            resourceData.put("uri",resourceData.getString("uri").substring(0,resourceData.getString("uri").indexOf("@QZKJMGC@")));
        }

        String host = resourceData.getString("host");
        String uriMd5  =  resourceData.getString("uriMd5");

        String reportSavePath = reportPath + "/" + taskId + "/" + host + "/detail";

        File file = new File(reportSavePath);

        if(!file.exists()) {

            try {
                Path path = file.toPath();
                Files.createDirectories( path );

            } catch (IOException e) {

            }
        }

        List<String> scriptList = new ArrayList<>(Arrays.asList("js.js","style.css","jquery.json-viewer.css","jquery.json-viewer.js"));

        for(String path : scriptList) {

            File source = new File(templatePath + "/" + path);
            File target = new File(reportSavePath + "/" + path);

            if(target.exists()) continue;

            try {

                Files.copy(source.toPath(),target.toPath());

            } catch (IOException e) {

            }
        }

        File source = new File(templatePath + "/template-apidetail.html");
        File target = new File(reportSavePath + "/api_"+ uriMd5 + ".html");

        if(target.exists()) return;

        try {

            Files.copy(source.toPath(),target.toPath());

            String content = FileUtils.readFileToString(target, "UTF-8");
            content = content.replace("var data=undefined", "var data=" + JSON.toJSONString( resourceData ) );

            try {
                FileWriter writer = new FileWriter(target);
                writer.write(content);
                writer.flush();
                writer.close();
            } catch (IOException e) {
                e.printStackTrace();
            }

        } catch (IOException e) {

        }
    }

    /**
     * 生成应用下弱点报告
     * @param hostDataList
     * @param taskId
     */
    private void makeHostWeaknessHtmlReport(ReportTask reportTask,List<Map<String,Object>> hostDataList,String taskId) {

        for(Map<String,Object> hostData : hostDataList) {

            try {
                String host = (String) hostData.get("host");

                MetabaseQuery metabaseQuery = new MetabaseQuery();
                metabaseQuery.where("host",Predicate.IS,host);

                List<Map> weaknessList = weaknessReportMaker.getWeaknessList(reportTask,metabaseQuery,taskId);

//                weaknessList.stream().forEach(weakness->{
//                    if(weakness.get("apiUrl").toString().indexOf("@QZKJMGC@") != -1) {
//                        weakness.put("apiUrl",weakness.get("apiUrl").toString().substring(0,weakness.get("apiUrl").toString().indexOf("@QZKJMGC@")));
//                    }
//                });

                Long lowCnt = weaknessList.stream().filter( i -> i.get("level").toString().equals("1") ).count();
                Long middleCnt = weaknessList.stream().filter( i -> i.get("level").toString().equals("2") ).count();
                Long highCnt = weaknessList.stream().filter( i -> i.get("level").toString().equals("3") ).count();

                ((Map) hostData.get("riskCount")).put("total",weaknessList.size());
                ((Map) hostData.get("riskCount")).put("l",lowCnt);
                ((Map) hostData.get("riskCount")).put("m",middleCnt);
                ((Map) hostData.get("riskCount")).put("h",highCnt);

                String reportSavePath = reportPath + "/" + taskId + "/" + host;
                String weaknessSourcePath = templatePath + "/template-hostrisklist.html";

                weaknessReportMaker.makeWeaknessHtmlReport(taskId,weaknessList,reportSavePath,weaknessSourcePath);

            } catch (Exception e) {

                log.error("生成应用下的弱点列表error",e);
            }
        }
    }

     /**
     * 生成应用下接口列表
     * @param hostApiMap
     * @param taskId
     */
    private void makeHostApiHtmlReport(Map<String,List<Object>> hostApiMap,String taskId) {

        for(Map.Entry hostApiInfo: hostApiMap.entrySet()) {

            String reportSavePath = reportPath + "/" + taskId + "/" + hostApiInfo.getKey();
            File file = new File(reportSavePath);

            if(!file.exists()) {

                try {
                    Path path = file.toPath();
                    Files.createDirectories( path );

                } catch (IOException e) {

                }
            }

            List<String> scriptList = new ArrayList<>(Arrays.asList("js.js","style.css"));

            for(String path : scriptList) {

                File source = new File(templatePath + "/" + path);
                File target = new File(reportSavePath + "/" + path);

                if(target.exists()) continue;

                try {

                    Files.copy(source.toPath(), target.toPath());

                } catch (IOException e) {

                }
            }

            File source = new File(templatePath + "/template-hostapilist.html");
            File target = new File(reportSavePath + "/index_api_list.html");

            if(target.exists()) return;

            try {

                Files.copy(source.toPath(),target.toPath());

                String content = FileUtils.readFileToString(target, "UTF-8");

                ((List)hostApiInfo.getValue()).stream().forEach(api->{
                    if(((JSONObject)api).getString("uri").indexOf("@QZKJMGC@")!=-1){
                        ((JSONObject)api).put("uri",((JSONObject)api).getString("uri").substring(0,((JSONObject)api).getString("uri").indexOf("@QZKJMGC@")));
                    }
                });

                content = content.replace("var data=undefined", "var data=" + JSON.toJSONString( hostApiInfo.getValue() ) );

                try {
                    FileWriter writer = new FileWriter(target);
                    writer.write(content);
                    writer.flush();
                    writer.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }

            } catch (IOException e) {

            }


        }
    }

    /**
     * 生成应用资产
     * @param hostDataList
     * @param taskId
     */
    private void makeHostHtmlReport(List<Map<String,Object>> hostDataList,String taskId) {

        try {

            String reportSavePath = reportPath + "/" + taskId;

            File file = new File(reportSavePath);

            if(!file.exists()) {
                Path path = file.toPath();
                Files.createDirectories( path );
            }

            List<String> scriptList = new ArrayList<>(Arrays.asList("js.js","style.css"));

            for(String path : scriptList) {

                File source = new File(templatePath + "/" + path);
                File target = new File(reportSavePath + "/" + path);

                if(target.exists()) continue;

                try {

                    Files.copy(source.toPath(), target.toPath());

                } catch (IOException e) {

                }
            }


            File source = new File(templatePath + "/template-hostlist.html");
            File target = new File(reportSavePath + "/index_host_list.html");

            if(target.exists()) return;

            try {

                Files.copy(source.toPath(),target.toPath());

                String content = FileUtils.readFileToString(target, "UTF-8");
                content = content.replace("var data=undefined", "var data=" + JSON.toJSONString(hostDataList) );

                try {
                    FileWriter writer = new FileWriter(target);
                    writer.write(content);
                    writer.flush();
                    writer.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }

            } catch (IOException e) {

            }


        } catch (IOException e) {

        }
    }
}
