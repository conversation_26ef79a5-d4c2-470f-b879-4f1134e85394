package com.quanzhi.auditapiv2.core.report.manage.impl.word;

import com.alibaba.nacos.api.config.annotation.NacosValue;
import com.quanzhi.auditapiv2.common.util.utils.DataUtil;
import org.springframework.stereotype.Component;

import java.io.File;
import java.io.IOException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2021/4/21 5:09 PM
 */
@Component
public class WordReportMakerImpl implements  IWordReportMaker{

    @NacosValue( value = "${auditapiv.report.save.path:/Users/<USER>/Documents/aaa}" ,autoRefreshed = true)
    private String reportPath;

    @NacosValue( value = "${auditapiv.report.word.template.path:/Users/<USER>/Documents}")
    private String templatePath = "/home/<USER>/audit-api/offline-template";

    private final String exportName = "offlineLineReport";

    @Override
    public void makeWordRepoer(String taskId,Map<String,List<Object>> reportExportData) {

        FreemakerUtil freemakerUtil = new FreemakerUtil();

        //数据准备
        Map<String, Object> dataMap = new HashMap<>();

        dataMap.put("hostList", reportExportData.get("hostList"));
        dataMap.put("apiList", reportExportData.get("apiList"));
        dataMap.put("weaknessList", reportExportData.get("weaknessList"));

        //没有数据就不生成了，空数据生成的文件打开会有问题
        if(DataUtil.isEmpty(dataMap.get("hostList")) && DataUtil.isEmpty(dataMap.get("apiList")) && DataUtil.isEmpty(dataMap.get("weaknessList"))){
            return;
        }

        String reportSavePath = reportPath + "/" + taskId + "/" + exportName + ".doc";

        File file = new File(reportSavePath);

        if(file.exists()) {
            return;
        }

        try {
            freemakerUtil.createFile(dataMap, "offlineLineWordTemplate.ftl", templatePath,reportSavePath);
            System.out.println("文件生成完毕");
        } catch (IOException e) {
            System.out.println("文件生成出错：" + e.getMessage());
        }
    }
}
