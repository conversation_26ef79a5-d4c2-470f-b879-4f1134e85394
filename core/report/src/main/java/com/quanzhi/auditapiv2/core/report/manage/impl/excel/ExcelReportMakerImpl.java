package com.quanzhi.auditapiv2.core.report.manage.impl.excel;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.nacos.api.config.annotation.NacosValue;
import com.quanzhi.auditapiv2.common.util.utils.ExcelUtil;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.springframework.stereotype.Component;

import java.io.File;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2021/4/22 5:07 PM
 */
@Component
public class ExcelReportMakerImpl implements IExcelReportMaker {

    @NacosValue( value = "${auditapiv.report.save.path:/Users/<USER>/Documents/aaa}" ,autoRefreshed = true)
    private String reportPath;

    @Override
    public void makeExcelReport(String taskId,Map<String,List<Object>> reportExportData) {


        HSSFWorkbook wb = new HSSFWorkbook();

        String sheetName1 = "应用列表";

        //没有数据的时候也有标题
        List<String> titles1 = Arrays.asList("应用","API数量","弱点总数","高危弱点数","中危弱点数","低危弱点数","访问域","终端类型","最大响应数据量","最大响应标签个数","数据标签");

        List<List<String>> rows1 = new ArrayList<>();

        if(reportExportData.containsKey("hostList") && reportExportData.get("hostList").size() > 0) {



            for(Object obj : reportExportData.get("hostList")) {

                JSONObject jsonObject = (JSONObject) obj;

                List<String> row = new ArrayList<>();

                row.add( jsonObject.getString( "host"  ) );
                row.add( jsonObject.getString( "apiCount"  ) );
                // API 没有监控概念了
//                row.add( jsonObject.getString( "monitorApiCount"  ) );
                row.add( jsonObject.getJSONObject("riskCount").getString( "total"  ) );
                row.add( jsonObject.getJSONObject("riskCount").getString( "h"  ) );
                row.add( jsonObject.getJSONObject("riskCount").getString( "m"  ) );
                row.add( jsonObject.getJSONObject("riskCount").getString( "l"  ));
                row.add( org.apache.commons.lang3.StringUtils.join(
                        jsonObject.getJSONArray("srcIpLabel"),
                        ","
                ) );
                row.add( org.apache.commons.lang3.StringUtils.join(
                        jsonObject.getJSONArray("uaLabel"),
                        ","
                ) );

                row.add( jsonObject.getString( "maxRspLabelValueCount"  ) );
                row.add( jsonObject.getString( "maxRspLabelSubCount"  ) );

                row.add( org.apache.commons.lang3.StringUtils.join(
                        jsonObject.getJSONArray("dataLabels"),
                        ","
                ) );

                rows1.add(row);
            }


        }
        ExcelUtil.getHSSFWorkbook(wb,sheetName1, titles1,rows1);


        String sheetName2 = "API列表";

        List<String> titles2 = Arrays.asList("API","访问域","终端类型","最大响应数据量","最大响应标签个数","请求数据标签","响应数据标签");

        List<List<String>> rows2 = new ArrayList<>();
        if(reportExportData.containsKey("apiList") && reportExportData.get("apiList").size() > 0 ) {



            for(Object obj : reportExportData.get("apiList")) {

                JSONObject jsonObject = (JSONObject) obj;

                List<String> row = new ArrayList<>();

                row.add( jsonObject.getString( "apiUrl"  ).indexOf("@QZKJMGC@") != -1 ? jsonObject.getString( "apiUrl"  ).substring(0,jsonObject.getString( "apiUrl"  ).indexOf("@QZKJMGC@")) : jsonObject.getString( "apiUrl"  ) );
//                row.add( jsonObject.getString( "monitorText"  ) );

                row.add( org.apache.commons.lang3.StringUtils.join(
                        jsonObject.getJSONArray("visitDomains"),
                        ","
                ) );

                row.add( org.apache.commons.lang3.StringUtils.join(
                        jsonObject.getJSONArray("terminals"),
                        ","
                ) );


                row.add( jsonObject.getJSONObject("apiStat").getString( "maxReqLabelValueCount"  ) );
                row.add( jsonObject.getJSONObject("apiStat").getString( "maxReqDupLabelCount"  ) );


                row.add( org.apache.commons.lang3.StringUtils.join(
                        jsonObject.getJSONArray("reqDataLabels"),
                        ","
                ) );

                row.add( org.apache.commons.lang3.StringUtils.join(
                        jsonObject.getJSONArray("rspDataLabels"),
                        ","
                ) );
                rows2.add(row);
            }


        }
        ExcelUtil.getHSSFWorkbook(wb,sheetName2, titles2,rows2);

        String sheetName3 = "弱点列表";

        List<String> titles3 = Arrays.asList("API","应用","弱点名称","弱点状态","弱点等级");

        List<List<String>> rows3 = new ArrayList<>();

        if(reportExportData.containsKey("weaknessList") && reportExportData.get("weaknessList").size() > 0) {


            for(Object obj : reportExportData.get("weaknessList")) {

                JSONObject jsonObject = (JSONObject) obj;

                List<String> row = new ArrayList<>();

                row.add( jsonObject.getString( "apiUrl"  ).indexOf("@QZKJMGC@") != -1 ? jsonObject.getString( "apiUrl"  ).substring(0,jsonObject.getString( "apiUrl"  ).indexOf("@QZKJMGC@")) : jsonObject.getString( "apiUrl"  )  );
                row.add( jsonObject.getString( "host"  ) );

                row.add( jsonObject.getString( "ruleName"  ) );

                row.add( jsonObject.getString( "statusText"  ) );

                row.add( jsonObject.getString( "levelText"  ) );

                rows3.add(row);
            }


        }
        ExcelUtil.getHSSFWorkbook(wb,sheetName3, titles3,rows3);

        String name = "离线报告";

        String reportSavePath = reportPath + "/" + taskId + "/" + name + ".xls";

        try {
            wb.write(new File(reportSavePath));

        } catch (IOException e) {

        }


    }
}
