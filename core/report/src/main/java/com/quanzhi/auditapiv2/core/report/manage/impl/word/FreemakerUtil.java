package com.quanzhi.auditapiv2.core.report.manage.impl.word;

import freemarker.template.Configuration;
import freemarker.template.Template;
import freemarker.template.TemplateException;

import java.io.*;
import java.util.Map;

public class FreemakerUtil {


    /**
     * @param dataMap 封装的数据
     * @param ftlName ftl模板名
     * @param outPath 输出路径
     * <AUTHOR>
     * @description: 根据ftl模板生成文件
     * @date: 2020/12/27
     * @Return void
     */
    public void createFile(Map<String, Object> dataMap, String ftlName,String templatePath,String outPath) throws IOException {
        Configuration configuration = new Configuration();
        configuration.setDefaultEncoding("utf-8");
        configuration.setDirectoryForTemplateLoading(new File(templatePath));
        Template template = configuration.getTemplate(ftlName);
        //写入
        File outFile = new File(outPath);
        Writer out = new BufferedWriter(new OutputStreamWriter(new FileOutputStream(outFile), "UTF-8"));
        try {
            template.process(dataMap, out);
            out.flush();
            out.close();
        } catch (TemplateException e) {
            e.printStackTrace();
        }
    }


    public void print(String ftlName, Map<String, Object> root) throws IOException {
        //通过Template可以将模版文件输出到相应的文件流
        Configuration configuration = new Configuration();
        configuration.setDefaultEncoding("utf-8");
        configuration.setClassForTemplateLoading(this.getClass(), "/template/freemarker");
        Template template = configuration.getTemplate(ftlName);
        try {
            template.process(root, new PrintWriter(System.out));//在控制台输出内容
        } catch (TemplateException e) {
            e.printStackTrace();
        } catch (IOException e) {
            e.printStackTrace();
        }

    }

}
