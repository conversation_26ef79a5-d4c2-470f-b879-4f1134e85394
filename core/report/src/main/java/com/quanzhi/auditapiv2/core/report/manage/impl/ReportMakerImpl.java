package com.quanzhi.auditapiv2.core.report.manage.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.nacos.api.config.annotation.NacosValue;
import com.quanzhi.audit_core.common.model.AssetLifeStateConfig;
import com.quanzhi.auditapiv2.common.dal.dao.convert.AssetLifeStateConfigConvert;
import com.quanzhi.auditapiv2.common.dal.dao.impl.common.DataPermissionUtil;
import com.quanzhi.auditapiv2.common.dal.dao.impl.common.Module;
import com.quanzhi.auditapiv2.common.dal.dto.group.DataPermissionTypeEnum;
import com.quanzhi.auditapiv2.common.util.utils.HttpResponseUtil;
import com.quanzhi.auditapiv2.common.util.utils.ZipUtils;
import com.quanzhi.auditapiv2.core.report.manage.IHttpApiReportMaker;
import com.quanzhi.auditapiv2.core.report.manage.IReportMaker;
import com.quanzhi.auditapiv2.core.report.manage.IWeaknessReportMaker;
import com.quanzhi.auditapiv2.core.report.manage.impl.excel.IExcelReportMaker;
import com.quanzhi.auditapiv2.core.report.manage.impl.pdf.IPdfReportMaker;
import com.quanzhi.auditapiv2.core.report.manage.impl.word.IWordReportMaker;
import com.quanzhi.auditapiv2.core.report.manage.model.ReportSearchDto;
import com.quanzhi.auditapiv2.core.report.manage.model.ReportTask;
import com.quanzhi.auditapiv2.core.service.SysLogService;
import com.quanzhi.metabase.core.model.http.constant.AssetLifeStateEnum;
import com.quanzhi.metabase.core.model.query.ResourceUpdates;
import com.quanzhi.operate.atomDefinition.*;
import com.quanzhi.operate.operateHandler.IOperateHandlerService;
import com.quanzhi.operate.query.Criteria;
import com.quanzhi.operate.repositoryTemplate.mongo.IBaseMongoTemplate;
import com.quanzhi.operate.utils.DataUtil;
import com.quanzhi.operate.utils.DateUtil;
import com.quanzhi.operate.utils.MD5Util;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FileUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.net.URLEncoder;
import java.nio.file.Files;
import java.nio.file.Path;
import java.util.*;
import java.util.concurrent.ThreadLocalRandom;

/**
 * <AUTHOR>
 * @date 2021/2/19 下午2:57
 */
@Slf4j
@Service
public class ReportMakerImpl implements IReportMaker {

    @Autowired
    private IOperateHandlerService operateHandlerService;

    @NacosValue(value = "${auditapiv.report.save.path:/Users/<USER>/Documents/aaa}", autoRefreshed = true)
    private String reportPath;

    @NacosValue(value = "${auditapiv.report.template.path:/Users/<USER>/Documents/aaa/template-bak}", autoRefreshed = true)
    private String templatePath = "/home/<USER>/audit-api/template-bak";

    @NacosValue(value = "${audit.trace.module:溯源}", autoRefreshed = true)
    private String auditLogModule;

    @Resource
    private SysLogService sysLogService;

    @Autowired
    private AssetLifeStateConfigConvert assetLifeStateConfigConvert;

    @Autowired
    private MongoTemplate mongoTemplate;

    @Autowired
    private DataPermissionUtil dataPermissionUtil;


    /**
     * 离线任务表
     */
    private String reportTaskCollection = "reportTask";

    private IBaseMongoTemplate baseMongoTemplate;

    private IWeaknessReportMaker weaknessReportMaker;

    private IHttpApiReportMaker httpApiReportMaker;

    private IWordReportMaker wordReportMaker;

    private IPdfReportMaker pdfReportMaker;

    private IExcelReportMaker excelReportMaker;

    private final String exportWordName = "offlineLineReport";

    public ReportMakerImpl(IBaseMongoTemplate baseMongoTemplate,
                           IWeaknessReportMaker weaknessReportMaker,
                           IHttpApiReportMaker httpApiReportMaker,
                           IWordReportMaker wordReportMaker, IPdfReportMaker pdfReportMaker, IExcelReportMaker excelReportMaker) {
        this.baseMongoTemplate = baseMongoTemplate;
        this.weaknessReportMaker = weaknessReportMaker;
        this.httpApiReportMaker = httpApiReportMaker;
        this.wordReportMaker = wordReportMaker;
        this.pdfReportMaker = pdfReportMaker;
        this.excelReportMaker = excelReportMaker;
    }

    /**
     * 生成报告id
     */
    private String taskId;

    @Override
    public void makeReport(ReportTask reportTask) {
        this.saveReportTask(reportTask);
        fillDataPermission(reportTask);
        new Thread(() -> {
            Map<String, List<Object>> reportExportData = new HashMap<>();
            this.getReportData(reportTask, reportExportData);
            if (reportTask.containsWordExportType()) {
                wordReportMaker.makeWordRepoer(taskId, reportExportData);
            }
            int randomNumber20 = ThreadLocalRandom.current().nextInt(15, 20 + 1);
            this.savePercentTask(reportTask, randomNumber20);
            if (reportTask.containsPdfExportType()) {
                pdfReportMaker.makePdfRepoer(taskId, reportExportData);
            }
            int randomNumber40 = ThreadLocalRandom.current().nextInt(35, 40 + 1);
            this.savePercentTask(reportTask, randomNumber40);
            if (reportTask.containsExcelExportType()) {
                excelReportMaker.makeExcelReport(taskId, reportExportData);
            }
            int randomNumber60 = ThreadLocalRandom.current().nextInt(55, 60 + 1);
            this.savePercentTask(reportTask, randomNumber60);
            // pdf 是用 word转化的， 如果只选了pdf但是没有选word，就需要把word的文件删掉
            if (!reportTask.containsWordExportType() && reportTask.containsPdfExportType()) {
                String reportSavePath = reportPath + "/" + taskId + "/" + exportWordName + ".docx";
                File file = new File(reportSavePath);
                if (file.exists()) {
                    FileUtils.deleteQuietly(file);
                }
            }
            int randomNumber80 = ThreadLocalRandom.current().nextInt(75, 80 + 1);
            this.savePercentTask(reportTask, randomNumber80);
            this.finishReportTask(reportTask.get_id());
        }).start();
    }

    /**
     * 填充数据权限条件
     */
    private void fillDataPermission(ReportTask reportTask) {
        Map<String, Object> dataPermissionMap = reportTask.getDataPermissionMap();
        if (DataUtil.isNotEmpty(dataPermissionMap)) {
            List<ReportSearchDto> reportSearchDtoList = reportTask.getReportSearchDtoList();
            for (ReportSearchDto reportSearchDto : reportSearchDtoList) {
                 ReportSearchDto.ReportDataResource reportDataResource = reportSearchDto.getReportDataResource();
                Criteria criteria = convertDataPermissionCriteria(dataPermissionMap,reportDataResource);
                reportSearchDto.getFrontCriteriaList().add(criteria);
            }
        }
    }

    private Criteria convertDataPermissionCriteria(Map<String, Object> dataPermissionMap,ReportSearchDto.ReportDataResource reportDataResource) {
        for (Map.Entry<String, Object> entry : dataPermissionMap.entrySet()) {
            DataPermissionTypeEnum dataPermissionTypeEnum = DataPermissionTypeEnum.valueOf(entry.getKey());
            String dbKey = dataPermissionUtil.convertDbKey(dataPermissionTypeEnum,reportDataResource==ReportSearchDto.ReportDataResource.HTTP_API? Module.API:Module.WEAKNESS);
            return Criteria.where(dbKey).in(entry.getValue());
        }
        return null;
    }

    private String AppCriteriaKey(DataPermissionTypeEnum dataPermissionTypeEnum) {
        switch (dataPermissionTypeEnum) {
            case NODE_ID:
                return "nodes.nid";
            case DEPLOY_DOMAIN:
                return "deployDomains";
            case APP_SPECIFY:
                return "appUri";
            default:
                return dataPermissionTypeEnum.name();
        }

    }

    private void savePercentTask(ReportTask reportTask, Integer percentTask) {
        ResourceUpdates updates = new ResourceUpdates();
        updates.set("percentage", percentTask);
        log.info("任务id" + reportTask.get_id() + "完成百分比" + percentTask);
        baseMongoTemplate.update(reportTask.get_id(), updates, reportTaskCollection);
    }

    /**
     * 保存报告任务
     *
     * @param reportTask
     */
    private void saveReportTask(ReportTask reportTask) {
        taskId = MD5Util.md5(DataUtil.getRandomStr(32));
        reportTask.setCreateTime(System.currentTimeMillis());
        reportTask.set_id(taskId);
        reportTask.setPercentage(0);
        /**
         * 审计的离线报告
         */
        reportTask.setReportType("OFFLINE_REPORT");
        baseMongoTemplate.save(reportTask, reportTaskCollection);
    }

    private void getReportData(ReportTask reportTask, Map<String, List<Object>> reportExportData) {
        String reportSavePath = reportPath + "/" + taskId;
        File file = new File(reportSavePath);
        if (!file.exists()) {
            Path path = file.toPath();
            try {
                Files.createDirectories(path);
            } catch (IOException e) {
            }
        }
        if (reportTask.getReportSearchDtoList() != null) {
            reportTask.getReportSearchDtoList().forEach(reportSearchDto -> {
                switch (reportSearchDto.getReportDataResource()) {
                    case HTTP_API_WEAKNESS:
                        try {
                            weaknessReportMaker.getWeaknessReport(reportTask, reportSearchDto, taskId, reportExportData);
                        } catch (Exception e) {
                            reportTask.setReportType("OFFLINE_REPORT");
                            reportTask.setStatus(ReportTask.ReportStatus.FAIL);
                            baseMongoTemplate.save(reportTask, reportTaskCollection);
                            return;
                        }
                        break;
                    case HTTP_API:
                        try {
                            httpApiReportMaker.getReport(reportTask, reportSearchDto, taskId, reportExportData);
                        } catch (Exception e) {
                            reportTask.setReportType("OFFLINE_REPORT");
                            reportTask.setStatus(ReportTask.ReportStatus.FAIL);
                            baseMongoTemplate.save(reportTask, reportTaskCollection);
                            return;
                        }
                        break;
                }
            });
        }
    }

    //对生成的报告进行压缩
    private void finishReportTask(String taskId) {
        ReportTask reportTask = JSON.parseObject(JSON.toJSONString(baseMongoTemplate.findOne(taskId, reportTaskCollection)), ReportTask.class);
        String name = reportTask.getName();
        String targetPath = reportPath + "/" + "_" + taskId + ".zip";
        File source = new File(reportPath + "/" + taskId);
        File target = new File(targetPath);
        try {
            ZipUtils.doCompress(source, target);
            if (target.exists()) {
                reportTask.setReportType("OFFLINE_REPORT");
                reportTask.setReportSize(target.length());
                reportTask.setStatus(ReportTask.ReportStatus.SUCCESS);
                reportTask.setPercentage(100);
            } else {
                reportTask.setReportType("OFFLINE_REPORT");
                reportTask.setStatus(ReportTask.ReportStatus.FAIL);
            }
        } catch (IOException e) {
            reportTask.setReportType("OFFLINE_REPORT");
            reportTask.setStatus(ReportTask.ReportStatus.FAIL);
            log.error("finishReportTask error", e);
        } catch (Exception e) {
            reportTask.setReportType("OFFLINE_REPORT");
            reportTask.setStatus(ReportTask.ReportStatus.FAIL);
            log.error("finishReportTask error", e);
        } finally {
            reportTask.setReportType("OFFLINE_REPORT");
            reportTask.setEndTime(System.currentTimeMillis());
            reportTask.setDuration(System.currentTimeMillis() - reportTask.getCreateTime());
            baseMongoTemplate.save(reportTask, reportTaskCollection);
        }
    }

    @Override
    public String downloadReport(String taskId, HttpServletResponse response) {

        try {
            ReportTask reportTask = JSON.parseObject(JSON.toJSONString(baseMongoTemplate.findOne(taskId, reportTaskCollection)), ReportTask.class);
            String name = URLEncoder.encode(Optional.ofNullable(reportTask.getName()).orElse("离线报告"));
            String targetPath = reportPath + "/" + "_" + taskId + ".zip";

            String fileName = reportTask.getName() + "-" + DateUtil.format(System.currentTimeMillis()) + ".zip";

            File file = new File(targetPath);
            response.reset();
            response.setContentType("application/octet-stream;charset=UTF-8");

            fileName = URLEncoder.encode(fileName, "UTF-8");
            response.setHeader("Content-Disposition", "attachment;filename*=utf-8''" + fileName);
            response.addHeader("Pargam", "no-cache");
            response.addHeader("Cache-Control", "no-cache");

            OutputStream outputStream = new BufferedOutputStream(response.getOutputStream());
            InputStream inputStream = new BufferedInputStream(new FileInputStream(file));
            byte[] buffer = new byte[1024];

            int len;
            // 不能一次性读完，大文件会内存溢出（inputStream.read(buffer);）
            while ((len = inputStream.read(buffer)) != -1) {
                outputStream.write(buffer, 0, len);
            }

            outputStream.flush();
            outputStream.close();

            return HttpResponseUtil.success();

        } catch (Exception e) {

            return HttpResponseUtil.error("未知异常");
        }

    }


    @Override
    public void deleteReport(String taskId) {

        ReportTask reportTask = JSON.parseObject(JSON.toJSONString(baseMongoTemplate.findOne(taskId, reportTaskCollection)), ReportTask.class);
        String name = reportTask.getName();
        String targetPath = reportPath + "/" + name + "_" + taskId + ".zip";

        File dirPath = new File(reportPath + "/" + taskId);

        if (dirPath.exists() && dirPath.isDirectory()) {

            try {
                FileUtils.deleteDirectory(dirPath);

            } catch (IOException e) {

            }

        }

        File zipPath = new File(targetPath);
        if (zipPath.exists()) {

            FileUtils.deleteQuietly(zipPath);
        }

//        if(DataUtil.isNotEmpty(name)){
//            sysLogService.insertLog(auditLogModule,"删除" + name + "任务","删除");
//        }

        baseMongoTemplate.delete(taskId, reportTaskCollection);
    }

    /**
     * 前置校验报告所需数据内容是否为空
     *
     * @param reportSearchDtoList
     * @return
     */
    @Override
    public boolean checkData(List<ReportSearchDto> reportSearchDtoList) {
        try {
            ActionConfig actionConfig = new ActionConfig();
            actionConfig.setId("AUDIT_V2_2.6.0_REPORT_NEED_DATA");
            List<RepositoryOperate> repositoryOperateList = new ArrayList<>();
            Map<String, String> rspKeyMap = new HashMap<>();
            for (ReportSearchDto reportSearchDto : reportSearchDtoList) {
                RepositoryOperate repositoryOperate = new RepositoryOperate();
                OperateRsp operateRsp = new OperateRsp();
                switch (reportSearchDto.getReportDataResource()) {
                    case HTTP_API:
                        repositoryOperate.setCollectionName("httpApi");
                        operateRsp.setKey("httpApiCount");
                        rspKeyMap.put("httpApiCount", "httpApiCount");
                        break;
                    case HTTP_API_WEAKNESS:
                        repositoryOperate.setCollectionName("httpApiWeakness");
                        operateRsp.setKey("httpApiWeaknessCount");
                        rspKeyMap.put("httpApiWeaknessCount", "httpApiWeaknessCount");
                        break;
                    default:
                        ;
                }
                repositoryOperate.setFrontCriteriaList(reportSearchDto.getFrontCriteriaList());
                repositoryOperate.setRepository(RepositoryEnum.MONGO);
                repositoryOperate.setOperateActionEnum(OperateActionEnum.COUNT);
                repositoryOperate.setOperateRsp(operateRsp);
                repositoryOperateList.add(repositoryOperate);
            }
            actionConfig.setRepositoryOperateList(repositoryOperateList);
            actionConfig.setRspKeyMap(rspKeyMap);
            Map<String, Object> result = operateHandlerService.handleActions(actionConfig, null);
            for (String key : result.keySet()) {
                if (0L != (Long) result.get(key)) {
                    return true;
                }
            }
            // apiLifeFlag和别的不一样
            Optional<ReportSearchDto> searchDto = reportSearchDtoList.stream().filter(reportSearchDto -> ReportSearchDto.ReportDataResource.HTTP_API == reportSearchDto.getReportDataResource()).findAny();
            if (searchDto.isPresent()) {
                ReportSearchDto reportSearchDto = searchDto.get();
                Optional<Criteria> optionalCriteria = reportSearchDto.getFrontCriteriaList().stream().filter(frontCriteriaList -> "apiLifeFlag".equals(frontCriteriaList.getProperty())).findAny();
                if (optionalCriteria.isPresent()) {
                    Criteria criteria = optionalCriteria.get();
                    Integer lifeFlag = null;
                    Object value = criteria.getValue();
                    if (value instanceof List) {
                        lifeFlag = ((List<Integer>) value).get(0);
                    }
                    AssetLifeStateEnum assetLifeStateEnum = AssetLifeStateEnum.valueOfNum(Short.valueOf(lifeFlag.toString()));
                    AssetLifeStateConfigConvert.CriteriaTag criteriaTag = assetLifeStateConfigConvert.getCriteria(AssetLifeStateConfig.AssetTypeEnum.API, assetLifeStateEnum);
                    org.springframework.data.mongodb.core.query.Criteria springCriteria = criteriaTag.getSpringCriteria();
                    long apiCount = mongoTemplate.count(new Query().addCriteria(springCriteria).addCriteria(org.springframework.data.mongodb.core.query.Criteria.where("delFlag").is(false)), "httpApi");
                    if (apiCount != 0L) {
                        return true;
                    }
                }
            }
            return false;
        } catch (Exception e) {
            log.error("get AUDIT_V2_2.6.0_REPORT_NEED_DATA", e);
            return false;
        }
    }

}
