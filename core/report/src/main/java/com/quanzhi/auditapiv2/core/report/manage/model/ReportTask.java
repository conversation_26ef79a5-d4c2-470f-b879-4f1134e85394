package com.quanzhi.auditapiv2.core.report.manage.model;

import com.quanzhi.auditapiv2.common.util.utils.DataUtil;
import lombok.Data;

import java.util.Arrays;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2021/2/20 上午10:28
 */
@Data
public class ReportTask {

    private String _id;

    private String name;

    /**
     * API 的报告和审计的离线报告都用了这个，区分一下，OFFLINE_REPORT-审计离线报告；CRON_TASK_REPORT-API的定时任务报告
     */
    private String reportType;

    /**
     * 报告任务完成进度百分比数值
     */
    private Integer percentage;

    private ReportStatus status = ReportStatus.RUNNING;

    private List<ReportExportType> exportTypes = Arrays.asList(ReportExportType.HTML,ReportExportType.EXCEL,ReportExportType.WORD,ReportExportType.PDF);

    /**
     * 数据源查询
     */
    private List<ReportSearchDto> reportSearchDtoList;

    /**
     * 报告大小
     */
    private Long reportSize;

    private Long createTime;

    private Long endTime;

    private Long duration;

    private String errorMsg;

    private Map<String,Object> dataPermissionMap;

    /**
     * 报告生成的形式
     */
    public enum ReportExportType {

        HTML,EXCEL,WORD,PDF
    }


    public enum ReportStatus{

        /**
         * 运行中
         */
        RUNNING(1),

        /**
         * 已成功
         */
        SUCCESS(2),

        /**
         * 失败
         */
        FAIL(3);

        private int status;

        ReportStatus(int status) {this.status = status;}

        public int value() {return this.status;}
    }

    public void check(ReportTask reportTask) throws Exception {

        if(DataUtil.isEmpty( reportTask.getName() )) {
            throw  new Exception("任务名称是必须的！");
        }

        if(DataUtil.isEmpty( reportTask.getReportSearchDtoList() )) {
            throw  new Exception("必须选择资产或者弱点！");
        }
    }

    public boolean containsHtmlExportType() {

        return this.exportTypes.contains( ReportExportType.HTML );
    }

    public boolean containsExcelExportType() {

        return this.exportTypes.contains( ReportExportType.EXCEL );
    }

    public boolean containsWordExportType() {

        return this.exportTypes.contains( ReportExportType.WORD );
    }

    public boolean containsPdfExportType() {

        return this.exportTypes.contains( ReportExportType.PDF );
    }
}
