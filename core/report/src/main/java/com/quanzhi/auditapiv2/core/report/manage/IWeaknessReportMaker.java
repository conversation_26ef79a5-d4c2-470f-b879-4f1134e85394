package com.quanzhi.auditapiv2.core.report.manage;

import com.quanzhi.auditapiv2.core.report.manage.model.ReportSearchDto;
import com.quanzhi.auditapiv2.core.report.manage.model.ReportTask;
import com.quanzhi.operate.query.MetabaseQuery;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2021/2/22 下午3:05
 */
public interface IWeaknessReportMaker {

    List<Map> getWeaknessList(ReportTask reportTask,MetabaseQuery metabaseQuery, String taskId) throws Exception;

    void makeWeaknessHtmlReport(String taskId,List<Map> weaknessList,String reportSavePath,String weaknessSourcePath);

    List<Map> getWeaknessReport(ReportTask reportTask, ReportSearchDto reportSearchDto, String taskId,Map<String,List<Object>> reportExportData) throws Exception;
}
