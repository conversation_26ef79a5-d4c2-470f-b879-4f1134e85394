package com.quanzhi.auditapiv2.core.report.manage.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.nacos.api.config.annotation.NacosValue;
import com.mongodb.client.MongoCursor;
import com.quanzhi.auditapiv2.core.report.manage.ISampleEventReportMaker;
import com.quanzhi.auditapiv2.core.report.manage.IWeaknessReportMaker;
import com.quanzhi.auditapiv2.core.report.manage.model.ReportSearchDto;
import com.quanzhi.auditapiv2.core.report.manage.model.ReportTask;
import com.quanzhi.operate.query.Criteria;
import com.quanzhi.operate.query.MetabaseQuery;
import com.quanzhi.operate.query.Predicate;
import com.quanzhi.operate.repositoryTemplate.mongo.IBaseMongoTemplate;
import com.quanzhi.operate.utils.DataUtil;
import com.quanzhi.operate.utils.DateUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang3.StringUtils;
import org.bson.Document;
import org.springframework.stereotype.Component;

import java.io.File;
import java.io.FileWriter;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2021/2/22 下午3:05
 */
@Slf4j
@Component
public class WeaknessReportMakerImpl implements IWeaknessReportMaker{

    @NacosValue( value = "${auditapiv.report.save.path:/Users/<USER>/Documents/aaa}" ,autoRefreshed = true)
    private String reportPath;

    @NacosValue( value = "${auditapiv.report.template.path:/Users/<USER>/Documents/aaa/template-bak}" ,autoRefreshed = true)
    private String templatePath = "/home/<USER>/audit-api/template-bak";

    @NacosValue( value = "${auditapiv.offlineReport.max.limit:1000}" ,autoRefreshed = true)
    private Integer MAX_PULL_LIMIT;

    private IBaseMongoTemplate baseMongoTemplate;

    private ISampleEventReportMaker sampleEventReportMaker;

    public WeaknessReportMakerImpl (IBaseMongoTemplate baseMongoTemplate,
                                    ISampleEventReportMaker sampleEventReportMaker) {

        this.baseMongoTemplate = baseMongoTemplate;
        this.sampleEventReportMaker = sampleEventReportMaker;
    }

    @Override
    public List<Map> getWeaknessReport(ReportTask reportTask, ReportSearchDto reportSearchDto, String taskId,Map<String,List<Object>> reportExportData) throws Exception{

        List<Map> weaknessList = new ArrayList<>();

        if (!reportSearchDto.getReportDataResource().equals(ReportSearchDto.ReportDataResource.HTTP_API_WEAKNESS )) return weaknessList;

        log.info("MAX_PULL_LIMIT is {}",MAX_PULL_LIMIT);

        try {

            MetabaseQuery metabaseQuery = new MetabaseQuery();

            //特殊字符进行转义
            if(DataUtil.isNotEmpty( reportSearchDto.getFrontCriteriaList() )) {

                for(Criteria criteria : reportSearchDto.getFrontCriteriaList()) {

                    if( (criteria.getValue() != null && criteria.getValue() instanceof String) && (criteria.getPredicate() != null && criteria.getPredicate().equals(Predicate.REGEX))  ) {
                        criteria.setValue( DataUtil.regexStrEscape( (String) criteria.getValue() ) );
                    }
                }
            }

            if(DataUtil.isNotEmpty( reportSearchDto.getFrontCriteriaList() )) {
                metabaseQuery.setCriteria(  reportSearchDto.getFrontCriteriaList() );
            }

            weaknessList = this.getWeaknessList(reportTask,metabaseQuery,taskId);

            if(reportTask.containsHtmlExportType()) {
                //打包弱点页面

                String reportSavePath = reportPath + "/" + taskId;
                String weaknessSourcePath = templatePath + "/template-risklist.html";

                this.makeWeaknessHtmlReport(taskId,weaknessList,reportSavePath,weaknessSourcePath);
            }

        } catch (Exception e) {

            throw new Exception(e);
        }

        reportExportData.put("weaknessList", JSON.parseArray(JSON.toJSONString(weaknessList)));

        return weaknessList;
    }

    @Override
    public List<Map> getWeaknessList(ReportTask reportTask,MetabaseQuery metabaseQuery,String taskId) throws Exception{

        List<Map> weaknessList = new ArrayList<>();

        metabaseQuery.where("delFlag",Predicate.IS,false);

        MongoCursor<Document> cursor = baseMongoTemplate.getCursor(metabaseQuery,500,MAX_PULL_LIMIT,ReportSearchDto.ReportDataResource.HTTP_API_WEAKNESS.value());

        // 弱点状态
        Map<String,String> weaknessStateMap = new HashMap<>();
        weaknessStateMap.put("NEW","待确认");
        weaknessStateMap.put("SUSPECT","可疑");
        weaknessStateMap.put("IGNORED","已忽略");
        weaknessStateMap.put("REPAIRING","待修复");
        weaknessStateMap.put("FIXED","已修复");
        weaknessStateMap.put("REOPEN","再复现");

        // 弱点等级
        Map<Integer,String> weaknessLevelMap = new HashMap<>();
        weaknessLevelMap.put(1,"低");
        weaknessLevelMap.put(2,"中");
        weaknessLevelMap.put(3,"高");
        try {
            while (cursor.hasNext()) {

                try {
                    Document obj = cursor.next();
                    Map<String,Object> weaknessMap = new HashMap<>();
//                    if (!(obj.get("_id") instanceof String)) {
//                        JSONObject id = (JSONObject) obj.get("_id");
//                        ObjectId objectId = new ObjectId(id.getInteger("timestamp"),
//                                id.getInteger("machineIdentifier"),
//                                id.getShort("processIdentifier"),
//                                id.getInteger("counter"));
//                        obj.put("_id", objectId.toHexString());
//                    }
                    weaknessMap.put("_id",obj.get("_id"));
                    weaknessMap.put("status",obj.get("state"));
                    // 排除掉可疑状态弱点
                    if(obj.get("state") == null || "SUSPECT".equals(obj.get("state"))){
                        continue;
                    }
                    weaknessMap.put("statusText",obj.get("state") != null ? weaknessStateMap.get(obj.get("state").toString()) : "");

                    weaknessMap.put("apiUrl",obj.get("uri").toString().indexOf("@QZKJMGC@") != -1 ? obj.get("uri").toString().substring(0,obj.get("uri").toString().indexOf("@QZKJMGC@")) : obj.get("uri").toString());
                    weaknessMap.put("host",obj.get("host"));
                    weaknessMap.put("ruleName",obj.get("name"));

                    weaknessMap.put("updateTime",obj.get("updateTime"));

                    weaknessMap.put("updateTimeFormat", DateUtil.format( (Long) obj.get("updateTime") ));

                    if(obj.get("samples") != null) {

                        JSONArray jsonArray = JSONArray.parseArray(JSON.toJSONString(obj.get("samples")));

                        List<String> eventIdList = jsonArray.stream().map(item -> (JSONObject.parseObject(JSON.toJSONString(item))).getString("sampleId")).collect(Collectors.toList());

                        weaknessMap.put("eventIdList",eventIdList);

                        Set<String> proofDescs = new HashSet<>();

                        for(Object item : jsonArray) {

                            if ( ((JSONObject) item).getJSONArray("proof")  != null ) {

                                ((JSONObject) item).getJSONArray("proof").stream().forEach(p -> {

                                    String desc = "";
                                    if( ((JSONObject) p).getString("name") != null) {

                                        desc = ((JSONObject) p).getString("name") + ":" + ((JSONObject) p).getString("value");
                                    } else {
                                        desc = ((JSONObject) p).getString("value");
                                    }

                                    proofDescs.add(desc);
                                });
                            }
                        }

                        weaknessMap.put("desc", StringUtils.join(proofDescs,","));

                        if(reportTask.containsHtmlExportType()) {
                            sampleEventReportMaker.makeSampleReportByIds(eventIdList,taskId);
                        }

                    }

                    weaknessMap.put("level",obj.get("level"));
                    weaknessMap.put("levelText",weaknessLevelMap.get(obj.get("level")));

                    weaknessMap.put("ruleCode",obj.get("weaknessId"));
                    weaknessList.add(weaknessMap);

                } catch (Exception e) {
                    log.error("handler weakness error",e);
                }
            }
        } catch (Exception e) {

            log.error("handler weakness error",e);

        }  finally {

            cursor.close();
        }

        return weaknessList;
    }

    /**
     * 打包弱点页面
     */
    @Override
    public void makeWeaknessHtmlReport(String taskId,List<Map> weaknessList,String reportSavePath,String weaknessSourcePath) {

        try {

            File file = new File(reportSavePath);

            if(!file.exists()) {
                Path path = file.toPath();
                Files.createDirectories( path );
            }

            List<String> scriptList = new ArrayList<>(Arrays.asList("js.js","style.css"));

            for(String path : scriptList) {

                File source = new File(templatePath + "/" + path);
                File target = new File(reportSavePath + "/" + path);

                if(target.exists()) continue;

                try {

                    Files.copy(source.toPath(), target.toPath());

                } catch (IOException e) {

                }
            }


            File weaknessSource = new File(weaknessSourcePath);
            File weaknessTarget = new File(reportSavePath + "/index_risk_list.html");

            if(weaknessTarget.exists()) return;

            try {

                Files.copy(weaknessSource.toPath(),weaknessTarget.toPath());

                String content = FileUtils.readFileToString(weaknessTarget, "UTF-8");
                content = content.replace("var data=undefined", "var data=" + JSON.toJSONString(weaknessList) );

                try {
                    FileWriter writer = new FileWriter(weaknessTarget);
                    writer.write(content);
                    writer.flush();
                    writer.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }

            } catch (IOException e) {

            }


        } catch (IOException e) {

        }

    }
}
