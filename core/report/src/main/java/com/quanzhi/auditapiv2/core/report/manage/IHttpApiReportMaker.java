package com.quanzhi.auditapiv2.core.report.manage;

import com.quanzhi.auditapiv2.core.report.manage.model.ReportSearchDto;
import com.quanzhi.auditapiv2.core.report.manage.model.ReportTask;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2021/2/22 下午4:27
 */
public interface IHttpApiReportMaker {

    void getReport(ReportTask reportTask,ReportSearchDto reportSearchDto, String taskId,Map<String,List<Object>> reportExportData) throws Exception;
}
