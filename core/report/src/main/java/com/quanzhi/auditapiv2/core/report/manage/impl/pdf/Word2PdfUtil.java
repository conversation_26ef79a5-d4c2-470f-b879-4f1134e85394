package com.quanzhi.auditapiv2.core.report.manage.impl.pdf;

import lombok.extern.slf4j.Slf4j;

import java.io.ByteArrayInputStream;
import java.io.File;
import java.io.FileOutputStream;
import java.io.InputStream;
/**
 * <AUTHOR>
 * @date 2021/4/25 11:50 AM
 */
@Slf4j
public class Word2PdfUtil {

    /**
     * word转pdf方法
     *
     * @param wordPath   word文件路径，如：/template/我的测试.docx
     * @param pdfOutPath 转化后的文件路径，如：/template/out/我的测试.pdf
     */
    public static void word2pdf(String wordPath, String pdfOutPath) {
        return ;
    }

    /**
     * 获取凭证并验证
     *
     * @return 返回验证结果
     */
    private static boolean getLicense() {
       return false;
    }
}
