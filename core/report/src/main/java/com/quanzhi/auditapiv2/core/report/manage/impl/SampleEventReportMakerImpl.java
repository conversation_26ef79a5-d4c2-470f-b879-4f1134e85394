package com.quanzhi.auditapiv2.core.report.manage.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.nacos.api.annotation.NacosInjected;
import com.alibaba.nacos.api.config.ConfigService;
import com.alibaba.nacos.api.config.annotation.NacosValue;
import com.quanzhi.auditapiv2.common.dal.entity.NacosBaseConfig;
import com.quanzhi.auditapiv2.common.dal.enums.NacosConfigEnum;
import com.quanzhi.auditapiv2.core.report.manage.ISampleEventReportMaker;
import com.quanzhi.auditapiv2.core.service.manager.dto.ExtractValueDto;
import com.quanzhi.auditapiv2.core.service.manager.web.ISampleEventService;
import com.quanzhi.operate.repositoryTemplate.mongo.IBaseMongoTemplate;
import com.quanzhi.operate.utils.DataUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FileUtils;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.io.File;
import java.io.FileWriter;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.util.*;

/**
 * <AUTHOR>
 * @date 2021/2/22 下午3:09
 */
@Slf4j
@Component
public class SampleEventReportMakerImpl implements ISampleEventReportMaker{


    @NacosValue( value = "${auditapiv.report.save.path:/Users/<USER>/Documents/aaa}" ,autoRefreshed = true)
    private String reportPath;

    @NacosValue( value = "${auditapiv.report.template.path:/Users/<USER>/Documents/aaa/template-bak}" ,autoRefreshed = true)
    private String templatePath = "/home/<USER>/audit-api/template-bak";

    private IBaseMongoTemplate baseMongoTemplate;

    private ISampleEventService sampleEventService;

    public SampleEventReportMakerImpl (IBaseMongoTemplate baseMongoTemplate,
                                       ISampleEventService sampleEventService) {

        this.baseMongoTemplate = baseMongoTemplate;
        this.sampleEventService = sampleEventService;
    }

    /**
     * 样例表
     */
    private String sampleCollection = "httpSample";

    @NacosInjected
    private ConfigService configService;

    private List<Object> dataLabelList = new ArrayList<>();

    @PostConstruct
    public void init() {

        try {
            NacosBaseConfig nacosBaseConfig = NacosConfigEnum.DATA_LABEL.getNacosBaseConfig();
            String dataId = nacosBaseConfig.getDataId();
            String group = nacosBaseConfig.getGroup();
            Class clazz = nacosBaseConfig.getClazz();

            String content = configService.getConfig(dataId, group, 3000);

            dataLabelList = JSONArray.parseArray(content);

        } catch (Exception e) {

        }
    }

    @Override
    public void makeSampleReportByIds(List<String> sampleIds,String taskId) {

        try {
            if(sampleIds != null && sampleIds.size() > 0) {

                sampleIds.stream().forEach(sampleId -> {

                    JSONObject jsonObject = JSON.parseObject(JSON.toJSONString(baseMongoTemplate.findOne(sampleId,sampleCollection))) ;

                    if(jsonObject != null) {
                        this.makeSampleHtmpReport(jsonObject,taskId);
                    }
                });
            }
        } catch (Exception e) {
            log.error("makeSampleReportByIds error",e);
        }
    }

    /**
     * 提取样例中的敏感数据
     * @param sampleInfo
     */
    @Override
    public void formatSampleEventInfo(JSONObject sampleInfo) {

        if(sampleInfo == null) return;

        try {
            //提取get,post,以及rspBody的敏感信息
            Map<String,Object> labelValue = new HashMap<>();

            Map<String,String> dataLabelMap = new HashMap<>();
            dataLabelList.stream().forEach(item ->{
                dataLabelMap.put(((JSONObject) item).getString("id"),((JSONObject) item).getString("name"));
            });

            String getPathStr = Optional.ofNullable(sampleInfo).map(jsonObject -> jsonObject.getJSONObject("req"))
                    .map(jsonObject -> jsonObject.getJSONObject("args"))
                    .map(jsonObject -> JSON.toJSONString(jsonObject) )
                    .orElse("");

            List<String> getDataLabelIds =  (Optional.ofNullable(sampleInfo.getJSONArray("reqDataLabels")).orElse(new JSONArray())).toJavaList(String.class);

            List<ExtractValueDto> getExtractValueDtos = sampleEventService.extractContentByLabels(getPathStr,getDataLabelIds,"HIGH",false,"");

            if(DataUtil.isNotEmpty( getExtractValueDtos )) {

                Map extraValueMap = new HashMap<String,List<String>>();
                getExtractValueDtos.forEach(extractValueDto -> {

                    if(extractValueDto.getDataLabelIds() != null && DataUtil.isNotEmpty( extractValueDto.getValues() )) {

                        extraValueMap.put( dataLabelMap.get( extractValueDto.getDataLabelIds().get(0)), extractValueDto.getValues());
                    }
                });
                labelValue.put("get",extraValueMap);
            }

            String postPathStr = Optional.ofNullable(sampleInfo).map(jsonObject -> jsonObject.getJSONObject("req"))
                    .map(jsonObject -> jsonObject.getJSONObject("args2"))
                    .map(jsonObject -> JSON.toJSONString(jsonObject) )
                    .orElse("");

            List<String> postDataLabelIds =  (Optional.ofNullable(sampleInfo.getJSONArray("rspDataLabels")).orElse(new JSONArray())).toJavaList(String.class);

            List<ExtractValueDto> postExtractValueDtos = sampleEventService.extractContentByLabels(postPathStr,postDataLabelIds,"HIGH",false,"");

            if(DataUtil.isNotEmpty( postExtractValueDtos )) {

                Map extraValueMap = new HashMap<String,List<String>>();
                postExtractValueDtos.forEach(extractValueDto -> {

                    if(extractValueDto.getDataLabelIds() != null && DataUtil.isNotEmpty( extractValueDto.getValues() )) {

                        extraValueMap.put( dataLabelMap.get( extractValueDto.getDataLabelIds().get(0)), extractValueDto.getValues());
                    }
                });
                labelValue.put("post",extraValueMap);
            }

            List<String> bodyDataLabelIds =  (Optional.ofNullable(sampleInfo.getJSONArray("rspDataLabels")).orElse(new JSONArray())).toJavaList(String.class);

            String rspPathStr = Optional.ofNullable(sampleInfo).map(jsonObject -> jsonObject.getJSONObject("rsp"))
                    .map(jsonObject -> jsonObject.getString("body"))
                    .orElse("");

            List<ExtractValueDto> rspExtractValueDtos = sampleEventService.extractContentByLabels(rspPathStr,bodyDataLabelIds,"HIGH",false,"");

            if(DataUtil.isNotEmpty( rspExtractValueDtos )) {

                Map extraValueMap = new HashMap<String,List<String>>();
                rspExtractValueDtos.forEach(extractValueDto -> {

                    if(extractValueDto.getDataLabelIds() != null && DataUtil.isNotEmpty( extractValueDto.getValues() )) {

                        extraValueMap.put( dataLabelMap.get( extractValueDto.getDataLabelIds().get(0)), extractValueDto.getValues());
                    }
                });
                labelValue.put("rsp",extraValueMap);
            }

            sampleInfo.put("labelValue",labelValue);

        } catch (Exception e) {

            log.error("formatSampleEventInfo error is ${} ,sample id is ${}",e,sampleInfo.getString("id"));
        }

    }

    private void makeSampleHtmpReport(JSONObject sampleInfo,String taskId) {

        String host = sampleInfo.getString("host");

        String sampleId = sampleInfo.getString("_id");

        if(host != null && sampleId != null) {

            sampleInfo.getJSONObject("req").put("args",sampleInfo.getJSONObject("req").getJSONObject("getArgs"));
            sampleInfo.getJSONObject("req").put("args2",sampleInfo.getJSONObject("req").getJSONObject("postArgs"));

            this.formatSampleEventInfo(sampleInfo);

            String reportSavePath = reportPath + "/" + taskId + "/" + host + "/detail";

            File file = new File(reportSavePath);

            if(!file.exists()) {

                try {
                    Path path = file.toPath();
                    Files.createDirectories( path );

                } catch (IOException e) {

                }
            }

            List<String> scriptList = new ArrayList<>(Arrays.asList("js.js","style.css","jquery.json-viewer.css","jquery.json-viewer.js"));

            for(String path : scriptList) {

                File source = new File(templatePath + "/" + path);
                File target = new File(reportSavePath + "/" + path);

                if(target.exists()) continue;

                try {

                    Files.copy(source.toPath(),target.toPath());

                } catch (IOException e) {

                }
            }

            File source = new File(templatePath + "/template-eventdetail.html");
            File target = new File(reportSavePath + "/event_"+ sampleId + ".html");

            if(target.exists()) return;

            try {

                Files.copy(source.toPath(),target.toPath());

                //需要将html格式的body中的script标签替换掉，不然在模版中对象字面量模式会有问题
                if(sampleInfo.getJSONObject("rsp") != null && sampleInfo.getJSONObject("rsp").getString("body") != null) {

                    String body = sampleInfo.getJSONObject("rsp").getString("body").replaceAll("<script","<div").replaceAll("script>","div>");
                    sampleInfo.getJSONObject("rsp").put("body",body);
                }

                String content = FileUtils.readFileToString(target, "UTF-8");
                content = content.replace("var data=undefined", "var data=" + JSON.toJSONString( sampleInfo ) );

                try {
                    FileWriter writer = new FileWriter(target);
                    writer.write(content);
                    writer.flush();
                    writer.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }

            } catch (IOException e) {

            }

        }

    }
}
