package com.quanzhi.auditapiv2.core.report.manage.model;

import com.quanzhi.operate.query.Criteria;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/2/20 上午10:41
 */
@Data
public class ReportSearchDto {

    /**
     * 报告的数据来源
     */
    private ReportDataResource reportDataResource;

    /**
     * 查询
     */
    private List<Criteria> frontCriteriaList;

    public enum ReportDataResource {

        HTTP_API("httpApi"),

        HTTP_API_WEAKNESS("httpApiWeakness");

        private String resourceCollection;

        ReportDataResource(String resourceCollection) {this.resourceCollection = resourceCollection;}

        public String value() {return this.resourceCollection;}
    }
}
