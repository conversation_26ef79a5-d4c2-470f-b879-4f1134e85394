package com.quanzhi.auditapiv2.core.report.manage;

import com.quanzhi.auditapiv2.core.report.manage.model.ReportSearchDto;
import com.quanzhi.auditapiv2.core.report.manage.model.ReportTask;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/2/19 下午2:56
 */
public interface IReportMaker {

    void makeReport(ReportTask reportTask);

    String downloadReport(String taskId,HttpServletResponse response);

    void deleteReport(String taskId);

    boolean checkData(List<ReportSearchDto> reportSearchDtoList);

}
