package com.quanzhi.auditapiv2.core.report.manage.impl.pdf;

import com.alibaba.nacos.api.config.annotation.NacosValue;
import com.quanzhi.auditapiv2.core.report.manage.impl.word.IWordReportMaker;
import org.springframework.stereotype.Component;

import java.io.File;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2021/4/25 2:21 PM
 */
@Component
public class PdfReportMakerImpl implements IPdfReportMaker{


    @NacosValue( value = "${auditapiv.report.save.path:/Users/<USER>/Documents/aaa}" ,autoRefreshed = true)
    private String reportPath;

    @NacosValue( value = "${auditapiv.report.word.template.path:/Users/<USER>/Documents}")
    private String templatePath;

    private final String exportName = "offlineLineReport";

    private IWordReportMaker wordReportMaker;

    public PdfReportMakerImpl (IWordReportMaker wordReportMaker) {

        this.wordReportMaker = wordReportMaker;
    }

    @Override
    public void makePdfRepoer(String taskId,Map<String,List<Object>> reportExportData) {

        wordReportMaker.makeWordRepoer(taskId,reportExportData);

        String wordReportSavePath = reportPath + "/" + taskId + "/" + exportName + ".doc";

        String pdfReportSavePath = reportPath + "/" + taskId + "/" + exportName + ".pdf";

        File file = new File(wordReportSavePath);

        if(file.exists()) {

            Word2PdfUtil.word2pdf(wordReportSavePath, pdfReportSavePath);


        }

    }
}
