<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">

    <parent>
        <groupId>com.quanzhi.auditapiv2</groupId>
        <artifactId>auditapiv2</artifactId>
        <version>3.3.0-SNAPSHOT</version>
        <relativePath>../../pom.xml</relativePath>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>auditapiv2-report-manage</artifactId>
    <packaging>jar</packaging>


    <dependencies>
    <!-- sub projects -->
        <dependency>
            <groupId>com.quanzhi.auditapiv2</groupId>
            <artifactId>auditapiv2-core-service</artifactId>
        </dependency>

        <dependency>
            <groupId>org.freemarker</groupId>
            <artifactId>freemarker</artifactId>
            <version>2.3.23</version>
        </dependency>

        <dependency>
            <groupId>com.aspose</groupId>
            <artifactId>aspose.words</artifactId>
            <version>15.8.0</version>
        </dependency>

    </dependencies>


</project>
