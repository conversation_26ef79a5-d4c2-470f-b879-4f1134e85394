<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>


    <parent>
        <groupId>com.quanzhi.auditapiv2</groupId>
        <artifactId>auditapiv2</artifactId>
        <version>3.3.0-SNAPSHOT</version>
        <relativePath>../../pom.xml</relativePath>
    </parent>

    <artifactId>auditapiv2-encrypt-decrypt</artifactId>
    <packaging>jar</packaging>

    <dependencies>

        <dependency>
            <groupId>com.quanzhi</groupId>
            <artifactId>encrypt-sdk</artifactId>
            <version>2.0.1-SNAPSHOT</version>
            <exclusions>
                <exclusion>
                    <artifactId>slf4j-log4j12</artifactId>
                    <groupId>org.slf4j</groupId>
                </exclusion>
            </exclusions>
        </dependency>

    </dependencies>
</project>
