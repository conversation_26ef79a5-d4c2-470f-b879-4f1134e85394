package com.quanzhi.auditapiv2.core.encrypt.decrypt.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.nacos.api.config.annotation.NacosValue;
import com.quanzhi.audit.crypt.sdk.QzCrypt;
import com.quanzhi.audit.crypt.sdk.enums.EncryModelEnum;
import com.quanzhi.auditapiv2.core.encrypt.decrypt.IQzcrypt;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.*;

/**
 * <AUTHOR>
 * @date 2021/2/24 上午11:02
 */
@Component
public class QzcryptImpl implements IQzcrypt {

    @NacosValue( value = "${crypto.server:nacos-server:8848}" ,autoRefreshed = true)
    private String cryptoServer;

    @NacosValue( value = "${crypto.enum:CLIENT}" )
    private String encryEnum;

    private boolean hasInit = false;

    private void initQzCrypt() {

        if(!hasInit) {

            try {
                QzCrypt.init(cryptoServer);

                hasInit = true;
            } catch (Exception e) {

            }
        }
    }

    @Override
    public String encrypt(Object source) {

        initQzCrypt();

        String sourceString = source.toString();

        if (QzCrypt.isNeedCrypt()) {

            List<String> sourceList = Arrays.asList(sourceString.split(","));

            List<String> content = QzCrypt.encrypt(EncryModelEnum.valueOf(encryEnum),sourceList);

            return StringUtils.join(content,",") ;

        } else {
            return sourceString;
        }
    }

    @Override
    public String encrypt(String sourceString) {

        initQzCrypt();

        if (QzCrypt.isNeedCrypt()) {

            String content = QzCrypt.encrypt(EncryModelEnum.valueOf(encryEnum),sourceString);

            return content;

        } else {
            return sourceString;
        }
    }

    @Override
    public String decrypt(String sourceString) {

        initQzCrypt();

        if (QzCrypt.isNeedCrypt()) {

            String content = QzCrypt.decrypt(EncryModelEnum.valueOf(encryEnum),sourceString);

            return content;

        } else {
            return sourceString;
        }
    }

    /**
     * 批量加密
     * @param sourceList
     * @return
     */
    @Override
    public List<String> encrypt(List<String> sourceList) {

        initQzCrypt();

        if (QzCrypt.isNeedCrypt()) {

            List<String> content = QzCrypt.encrypt(EncryModelEnum.valueOf(encryEnum),sourceList);

            return content;

        } else {
            return sourceList;
        }
    }

    /**
     * 批量解密
     * @param sourceList
     * @return
     */
    @Override
    public List<String> decrypt(List<String> sourceList) {

        initQzCrypt();

        if (QzCrypt.isNeedCrypt()) {

            List<String> content = QzCrypt.decrypt(EncryModelEnum.valueOf(encryEnum),sourceList);
            return content;
        } else {
            return sourceList;
        }
    }

    @Override
    public Map<String,Map<String,Set<String>>> decryptExtractValues(List<Object> extractValues) {

        initQzCrypt();

        Map<String,Map<String,Set<String>>> result = new HashMap<>();
        // new时直接初始化后面gson.toJson()一下就没了，无语
        result.put("rsp",new HashMap<>());
        result.put("req",new HashMap<>());

        if( extractValues != null ) {

            extractValues.forEach(i ->{

                JSONObject extractValue = JSON.parseObject(JSON.toJSONString(i));

                String source = extractValue.getString("source").toLowerCase();

                if(extractValue.getJSONObject("labelValues") != null) {

                    extractValue.getJSONObject("labelValues").keySet().stream().forEach(key ->{

                        Map<String,Set<String>> sourceMap = result.get(source);

                        Set<String> targetSet = new HashSet<>( extractValue.getJSONObject("labelValues").getJSONArray(key).toJavaList(String.class) );

                        if(!sourceMap.containsKey(key)) {

                            sourceMap.put(key, targetSet );

                        } else {

                            sourceMap.get(key).addAll( targetSet );
                        }

                    });
                }
            });


            for(Map<String,Set<String>> value : result.values()) {

                for(Map.Entry<String,Set<String>> entry : value.entrySet()) {

                    value.put(entry.getKey(), new HashSet<>(  this.decrypt(new ArrayList<String>( entry.getValue() )) ));
                }
            }
        }

        return result;
    }

    /**
     * 事件敏感数据按Location分组给 前端做样例的高亮使用
     * @param extractValues
     * @return
     */
    @Override
    public Map<String,Map<String,Set<String>>> decryptExtractValuesByLocation(List<Object> extractValues) {

        initQzCrypt();

        Map<String,Map<String,Set<String>>> result = new HashMap<String,Map<String,Set<String>>>();

        if( extractValues != null ) {

            extractValues.forEach(i ->{

                JSONObject extractValue = JSON.parseObject(JSON.toJSONString(i));

                List<String> locations =  extractValue.getJSONArray("locations").toJavaList(String.class);

                if(extractValue.getJSONObject("labelValues") != null) {

                    for(String location : locations ) {

                        extractValue.getJSONObject("labelValues").keySet().stream().forEach(key ->{

                            if( !result.containsKey( location ) ) {
                                result.put( location, new HashMap<>() );
                            }
                            Map<String,Set<String>> sourceMap = result.get(location);

                            Set<String> targetSet = new HashSet<>( extractValue.getJSONObject("labelValues").getJSONArray(key).toJavaList(String.class) );

                            if(!sourceMap.containsKey(key)) {

                                sourceMap.put(key, targetSet );

                            } else {

                                sourceMap.get(key).addAll( targetSet );
                            }

                        });
                    }
                }
            });


            for(Map<String,Set<String>> value : result.values()) {

                for(Map.Entry<String,Set<String>> entry : value.entrySet()) {

                    value.put(entry.getKey(), new HashSet<>(  this.decrypt(new ArrayList<String>( entry.getValue() )) ));
                }
            }
        }

        return result;
    }


}
