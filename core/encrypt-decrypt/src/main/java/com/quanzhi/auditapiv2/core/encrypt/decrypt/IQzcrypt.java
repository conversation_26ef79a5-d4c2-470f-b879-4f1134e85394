package com.quanzhi.auditapiv2.core.encrypt.decrypt;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * <AUTHOR>
 * @date 2021/2/24 上午11:01
 */
public interface IQzcrypt {

    String encrypt(Object sourceString);

    /**
     * 批量加密
     * @param sourceList
     * @return
     */
    List<String> encrypt(List<String> sourceList);

    String encrypt(String sourceString);

    String decrypt(String sourceString);

    /**
     * 批量解密
     * @param sourceList
     * @return
     */
    List<String> decrypt(List<String> sourceList);

    /**
     * 对敏感提取内容进行解密
     *
     *      "extractValues": [
                 {
                    "dataLabelIds": [
                        "company"
                    ],
                 "labelValues": {
                    "company": [
                        "中国铁道科学研究院"
                    ]
                 },
                 "locations": [
                    "BODY"
                 ],
                 "source": "RSP",
                 "values": [
                    "中国铁道科学研究院"
                    ]
                 }
            ]
     * @param extractValues
     * @return
     */
    Map<String,Map<String,Set<String>>> decryptExtractValues(List<Object> extractValues);


    Map<String,Map<String,Set<String>>> decryptExtractValuesByLocation(List<Object> extractValues);
}
