# audit-apiv2
### 多节点
#### 架构
数据传输主要通过 Source、Transform、Sink 三个步骤组成。

#### 配置
配置文件在 web 项目下的 src/main/resources/node下，主要支持四种配置：
1. tunnel-master-data.json 用于主节点从开放接口中接收数据，实现一些转换然后入库。
2. tunnel-slave-data-1d.json 用于子节点定时1天拉取数据，然后入库。
3. tunnel-slave-data-1h.json 用于子节点定时每1小时拉取数据，然后入库。
4. tunnel-slave-data-10m.json 用于子节点定时每10m拉取数据，然后入库。
5. tunnel-slave-data-6h.json 用于子节点定时每6小时拉取数据，然后入库。

下面罗列一下每个配置的参数：
###### Source
collection 结构如下：

| 名称                 | 类型   | 描述       | 是否必须 |
| -------------------- | ------ | ---------- | -------- |
| name                 | string | 表名称     | 是       |
| excludeFields        | array  | 排除字段   | 否       |
| includeFields        | array  | 包含字段   | 否       |
| updateTimestampField | string | 更新时间戳 | 否       |

###### Transform

transform示例如下：

```
{
  "sources": [
    {
      "type": "QZOpenAPI"
    }
  ],
  "transforms": [
    {
      "type": "MongoCollectionMergeMapper",
      "params": {
        "collections": {
          "accountInfo": {
            "primaryFields": [
              "account"
            ],
            "mappings": [
              {
                "key": "updateTime",
                "func": "CURRENT_TIMESTAMP"
              }
            ]
          },
          "accountMonthInfo": {
            "primaryFields": [
              "account",
              "month"
            ],
            "mappings": [
              {
                "key": "updateTime",
                "func": "CURRENT_TIMESTAMP"
              },
              {
                "keyFunc": "MONTH_DAY_KEY(dateStats., .amount)",
                "func": "SUM"
              }
            ]
          }
        }
      }
    }
  ],
  "sinks": [
    {
      "type": "MongoDB"
    }
  ]
}
```

MongoCollectionMergeMapper 默认会合并集合、对null赋值，支持函数：

- SUM 数值累加
- CURRENT_TIMESTAMP 当前时间戳
- MONTH_DAY_KEY 月度表的当前天数的统计键（和 MonthStat 表相关）
- MAX 获取最大值
- MIN 获取最小值

针对一些字段说明：

- primaryFields 表的唯一主键
- mappings 合并模式
  - key 键，针对此键作用的规则，如果不确定键的名称，可以使用 keyFunc
  - keyFunc 键函数
  - func 值合并函数

所有合并规则见 MongoCollectionMergeMapper，如需自定义函数见 SimpleFunction，目前只支持简单的字符串匹配并执行对应逻辑。


#### 自定义
目前主要通过 TunnelTransform 接口来实现自定义的功能，详细请参考：
```java
@RequiredArgsConstructor
@Component
// 注意这里 scope 必须为原型，否则可能有线程安全问题
@Scope(value = ConfigurableBeanFactory.SCOPE_PROTOTYPE)
public class FakeTestAddValuesTransform implements TunnelTransform<TunnelMapRecord> {
    private final Map<String, Object> addValues;
    @Override
    public TunnelMapRecord map(TunnelMapRecord r) {
        r.getValues().putAll(addValues);
        return r;
    }
    @Override
    public void setConfig(ReadOnlyConfig config) {
    }
}
```
setConfig 会将所有参数传入。

### magic-api 自定义扩展 API 和定时任务
本项目已集成 audit-magic-api (该项目位于audit-mix)。可以在产品页面搜索“插件”的方式自定义 API 和 定时任务的插件，注意 Controller 定义与通用的方式略有出入，如下：
- 请使用 @ResponseBody 标注 Controller。
- RequestParam 必须写 value。
- 由于后端有多个 MongoTemplate，请添加注解 @Qualifier("mongoTemplate")
- 注意不要使用编译器的一些注解（如 lombok），脚本无法解析。

一个参考的插件如下：
```java
package org.example;

import com.quanzhi.audit.mix.plugin.domain.Plugin;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@RequestMapping("/api/test2")
@ResponseBody
public class MyTestControllerTest {
    private final MongoTemplate mongoTemplate;
    public MyTestControllerTest(@Qualifier("mongoTemplate") MongoTemplate mongoTemplate){
        this.mongoTemplate = mongoTemplate;
    }
    @GetMapping("order")
    public Map<String, Object> postTest23(@RequestParam("id") String id) {
        List<Plugin> plugins = mongoTemplate.find(new Query(), Plugin.class, "commonPlugin");
        Map<String, Object> map = new HashMap<>(4);
        map.put("code", "0");
        map.put("msg", "POST请求测试成功, 传递参数params:" + id);
        map.put("data", plugins);
        return map;
    }
}
```

schedule定义如下：
```java
package org.example;

import com.quanzhi.audit.mix.schdule.application.annotation.LockedScheduler;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

@Component
public class MagicSchedule3 {
  private final MongoTemplate mongoTemplate;
  public MagicSchedule3(@Qualifier("mongoTemplate") MongoTemplate mongoTemplate){
    this.mongoTemplate = mongoTemplate;
  }
  @LockedScheduler(cron = "0/1 * * * * ?", executor = "testMagicSchedule3", name = "测试magic任务3", description = "测试不要管，我用一下这台机器的任务oo")
  public void job(){
    System.out.println("测试任务执行成功2");
    Map<String, Object> map = new HashMap<>();
    map.put("time33", System.currentTimeMillis());
    mongoTemplate.save(map, "testCollection");
  }
}
```
上传插件后可以在产品 “任务” 中看到此任务。